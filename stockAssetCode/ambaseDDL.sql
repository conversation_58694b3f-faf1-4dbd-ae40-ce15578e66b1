use ambase;
CREATE TABLE `qr` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `biz_no` varchar(64) NOT NULL COMMENT '业务单号',
  `biz_type` int(2) NOT NULL DEFAULT '0' COMMENT '类型： 0资产编码',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类型：1二维码，2条形码',
  `qr_content` varchar(255) NOT NULL DEFAULT '' COMMENT '二维码内容',
  `qr_img_url` varchar(300) NOT NULL DEFAULT '' COMMENT '二维码/条形码路径',
  `batch_no` varchar(255) NOT NULL COMMENT '批次号',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '0,有效， 1无效',
  `del_flag` int(2) NOT NULL DEFAULT '0' COMMENT '是否删除0否，1是',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_qr_1` (`biz_no`) USING BTREE,
  KEY `idx_qr_2` (`batch_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码表';
