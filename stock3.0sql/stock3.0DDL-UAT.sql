-- 2019/12/04
use stock;

-- 礼品调拨导入头表
CREATE TABLE `stock_allocate_import_heads` (
  `head_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计划出库单导入头id',
  `active_name` varchar(64) DEFAULT NULL COMMENT '计划出库单导入头名称',
  `billing_user` varchar(32) NOT NULL DEFAULT '' COMMENT '制单人工号',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态',
  `CREATED_BY` varchar(32) NOT NULL DEFAULT '0' COMMENT '创建人',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_BY` varchar(32) NOT NULL DEFAULT '0' COMMENT '更新人',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识0，未删除；1，已删除',
  `head_code` varchar(64) NOT NULL DEFAULT '0' COMMENT '计划出库单导入code',
  PRIMARY KEY (`head_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='礼品调拨导入头表';

-- 礼品调拨导入行表
CREATE TABLE `stock_allocate_import_lines` (
  `line_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '新车礼品调拨行id',
  `head_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '礼品调拨导入头id',
  `out_import_warehouse_name` varchar(64) DEFAULT NULL COMMENT '导入调出门店名称',
  `out_warehouse_code` varchar(64) DEFAULT NULL COMMENT '调出匹配仓库编码',
  `in_import_warehouse_name` varchar(64) DEFAULT NULL COMMENT '导入调入门店名称',
  `in_warehouse_code` varchar(32) DEFAULT NULL COMMENT '调入匹配仓库编码',
  `supplies_code` varchar(64) DEFAULT NULL COMMENT '物料编码',
  `number` int(11) NOT NULL DEFAULT '0' COMMENT '计划调拨数量',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态',
  `error_message` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `CREATED_BY` varchar(32) NOT NULL DEFAULT '0' COMMENT '创建人',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_BY` varchar(32) NOT NULL DEFAULT '0' COMMENT '更新人',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识0，未删除；1，已删除',
  PRIMARY KEY (`line_id`),
  KEY `idx_allocate_import_line_1` (`head_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='礼品调拨导入行表';


-- 库存计划出库单头表
CREATE TABLE stock_delivery_plan_heads(
    delivery_plan_head_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '计划出库单id' ,
    delivery_plan_no VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '计划出库单编号' ,
    from_system VARCHAR(32)    COMMENT '来源系统' ,
    out_warehouse_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '调出仓库编码' ,
    in_warehouse_code VARCHAR(64) COMMENT '调入仓库编码' ,
    out_stock_type INT not null  default 0 COMMENT '出库类型' ,
    is_send INT NOT NULL  DEFAULT 0 COMMENT '是否发货(0: 否，1：是)' ,
    biz_no VARCHAR(32)    COMMENT '业务单号' ,
    billing_user VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '制单人' ,
    billing_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '制单日期' ,
    plan_out_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '计划出库日期' ,
    reason_code INT    COMMENT '出库原因code' ,
    duty_user VARCHAR(32)    COMMENT '责任人' ,
    use_user  VARCHAR(32) COMMENT '实际使用人',
    status INT NOT NULL  DEFAULT 0 COMMENT '状态' ,
    remark VARCHAR(128)    COMMENT '备注' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (delivery_plan_head_id)
) COMMENT = '库存计划出库单头表';

-- 库存计划出库单行表
CREATE TABLE stock_delivery_plan_lines(
    delivery_plan_line_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '计划出库单行id' ,
    delivery_plan_head_id BIGINT NOT NULL  DEFAULT 0 COMMENT '计划出库单头id' ,
    supplies_code VARCHAR(255) NOT NULL  DEFAULT '' COMMENT '物料编码' ,
    number INT NOT NULL  DEFAULT 0 COMMENT '计划出库数量' ,
    real_number INT NOT NULL  DEFAULT 0 COMMENT '实际出库数量' ,
    plan_out_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '计划出库时间' ,
    real_warehouse_code VARCHAR(64)    COMMENT '实际仓库编码' ,
    status INT NOT NULL  DEFAULT 0 COMMENT '行状态' ,
    assets_code VARCHAR(64)    COMMENT '资产编码' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (delivery_plan_line_id)
) COMMENT = '库存计划出库单行表';

ALTER TABLE stock_delivery_plan_lines ADD INDEX idx_delivery_plan_lines_1(delivery_plan_head_id) USING BTREE;



-- 出库单行资产关联表
CREATE TABLE stock_delivery_detail_assets(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    delivery_detail_id BIGINT NOT NULL  DEFAULT 0 COMMENT '出库单行' ,
    assets_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '资产编码' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '出库单行资产关联表';

ALTER TABLE stock_delivery_detail_assets ADD INDEX idx_delivery_detail_assets_1(delivery_detail_id) USING BTREE;
ALTER TABLE stock_delivery_detail_assets ADD INDEX idx_delivery_detail_assets_2(assets_code) USING BTREE;

-- 出库单行SN关联表
CREATE TABLE stock_delivery_detail_sns(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    delivery_detail_id BIGINT NOT NULL  DEFAULT 0 COMMENT '出库单行id' ,
    SERIAL_NUMBER VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '序列号' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '出库单行SN关联表';

ALTER TABLE stock_delivery_detail_sns ADD INDEX idx_delivery_detail_sns_1(delivery_detail_id) USING BTREE;
ALTER TABLE stock_delivery_detail_sns ADD INDEX idx_delivery_detail_sns_2(SERIAL_NUMBER) USING BTREE;

-- 出库单行sn关联表历史表
CREATE TABLE stock_delivery_detail_sns_history(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    delivery_detail_id BIGINT NOT NULL  DEFAULT 0 COMMENT '出库单行id' ,
    SERIAL_NUMBER VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '序列号' ,
    CREATED_BY1 VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT1 timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY1 VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '出库单行sn关联表历史表';

ALTER TABLE stock_delivery_detail_sns_history ADD INDEX idx_delivery_detail_sns_his_1(delivery_detail_id) USING BTREE;
ALTER TABLE stock_delivery_detail_sns_history ADD INDEX idx_delivery_detail_sns_his_2(SERIAL_NUMBER) USING BTREE;

-- 出库单行资产关联表历史表
CREATE TABLE stock_delivery_detail_assets_history(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    delivery_detail_id BIGINT NOT NULL  DEFAULT 0 COMMENT '出库单行' ,
    assets_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '资产编码' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '出库单行资产关联表历史表';

ALTER TABLE stock_delivery_detail_assets_history ADD INDEX idx_delivery_detail_assets_his_1(assets_code) USING BTREE;
ALTER TABLE stock_delivery_detail_assets_history ADD INDEX idx_delivery_detail_assets_his_2(delivery_detail_id) USING BTREE;

-- 计划入库单头表
CREATE TABLE stock_inventory_in_plan_heads(
    inventory_in_plan_head_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '计划入库单头id' ,
    biz_no VARCHAR(64)    COMMENT '业务单号' ,
    inventory_in_plan_no VARCHAR(32)    COMMENT '计划入库单编号' ,
    in_warehouse_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '调入仓库编码' ,
    out_warehouse_code VARCHAR(64)    COMMENT '调出仓库编码' ,
    inventory_in_plan_type INT NOT NULL  DEFAULT 0 COMMENT '计划入库单类型' ,
    billing_user VARCHAR(32) NOT NULL  DEFAULT '' COMMENT '制单人工号' ,
    billing_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '制单日期' ,
    reason_code INT    COMMENT '调入仓库原因编码' ,
    vendor_code VARCHAR(32)    COMMENT '供应商编码' ,
    vendor_name VARCHAR(32)    COMMENT '供应商名称' ,
    receive_user VARCHAR(32)    COMMENT '收货人工号' ,
    plan_in_time DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '计划入库日期' ,
    delivery_no VARCHAR(32)    COMMENT '送货单号' ,
    status INT NOT NULL  DEFAULT 0 COMMENT '状态' ,
    remark VARCHAR(255)    COMMENT '备注' ,
    duty_user VARCHAR(32)    COMMENT '责任人工号' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (inventory_in_plan_head_id)
) COMMENT = '计划入库单头表';

-- 计划入库单行表
CREATE TABLE stock_inventory_in_plan_lines(
    inventory_in_plan_line_id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '计划入库单行id' ,
    inventory_in_plan_head_id BIGINT NOT NULL  DEFAULT 0 COMMENT '计划入库单头id' ,
    supplies_code VARCHAR(255) NOT NULL  DEFAULT '' COMMENT '物料编码' ,
    number INT NOT NULL  DEFAULT 0 COMMENT '计划入库数量' ,
    real_number VARCHAR(32)    COMMENT '实际入库数量' ,
    assets_code VARCHAR(64)    COMMENT '资产编码' ,
    status INT NOT NULL  DEFAULT 0 COMMENT '状态' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (inventory_in_plan_line_id)
) COMMENT = '计划入库单行表';

ALTER TABLE stock_inventory_in_plan_lines ADD INDEX idx_inventory_in_plan_lines_1(inventory_in_plan_head_id) USING BTREE;



-- 入库单行sn关联表
CREATE TABLE stock_inventory_in_supplies_sns(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    in_supplies_id BIGINT NOT NULL  DEFAULT 0 COMMENT '入库单行id' ,
    SERIAL_NUMBER VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '序列号' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '入库单行sn关联表';

ALTER TABLE stock_inventory_in_supplies_sns ADD INDEX idx_inventory_in_supplies_sns_1(in_supplies_id) USING BTREE;
ALTER TABLE stock_inventory_in_supplies_sns ADD INDEX idx_inventory_in_supplies_sns_2(SERIAL_NUMBER) USING BTREE;

-- 资产库存导入表
CREATE TABLE stock_inventory_asset_import(
    inventory_asset_imp_id INT NOT NULL AUTO_INCREMENT  COMMENT '主键id' ,
    inventory_asset_batch_code VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '批code' ,
    asset_code VARCHAR(64)    COMMENT '资产编码' ,
    sn_no VARCHAR(32)    COMMENT '序列号' ,
    status INT NOT NULL  DEFAULT 0 COMMENT '状态' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (inventory_asset_imp_id)
) COMMENT = '资产库存导入表';

ALTER TABLE stock_inventory_asset_import ADD INDEX idx_inventory_asset_imp_1(inventory_asset_batch_code) USING BTREE;

-- 资产sn关联表
CREATE TABLE stock_assets_sns(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    assets_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '资产编码' ,
    SERIAL_NUMBER VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '序列号' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '资产sn关联表';

ALTER TABLE stock_assets_sns ADD INDEX idx_assets_sns_1(assets_code) USING BTREE;
ALTER TABLE stock_assets_sns ADD INDEX idx_assets_sns_2(SERIAL_NUMBER) USING BTREE;

-- 序列号基本信息表
CREATE TABLE stock_sns(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    SERIAL_NUMBER VARCHAR(32)    COMMENT '序列号' ,
    remark VARCHAR(255)    COMMENT '' ,
    warehouse_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '仓库编码' ,
    vendor_code VARCHAR(64)    COMMENT '供应商编码' ,
    vendor_name VARCHAR(64)    COMMENT '供应商名称' ,
    status INT NOT NULL  DEFAULT 0 COMMENT '状态' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '序列号基本信息表 ';

ALTER TABLE stock_sns ADD INDEX idx_sns_1(SERIAL_NUMBER) USING BTREE;

-- sn资产关联表
CREATE TABLE stock_sns_assets(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    SERIAL_NUMBER VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '序列号' ,
    assets_code VARCHAR(64) NOT NULL  DEFAULT '' COMMENT '资产编码' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = 'sn资产关联表';

ALTER TABLE stock_sns_assets ADD INDEX idx_sns_assets_1(SERIAL_NUMBER) USING BTREE;
ALTER TABLE stock_sns_assets ADD INDEX idx_sns_assets_2(assets_code) USING BTREE;


-- 出库单表
ALTER table stock_delivery add COLUMN  delivery_plan_head_id BIGINT  COMMENT '计划出库单id';
ALTER TABLE stock_delivery ADD INDEX idx_stock_delivery_1(delivery_plan_head_id) USING BTREE;

-- 出库单详情表
ALTER table stock_delivery_detail add COLUMN  delivery_plan_line_id BIGINT  COMMENT '计划出库单行id';
ALTER TABLE stock_delivery_detail ADD INDEX idx_stock_delivery_detail_2(delivery_plan_line_id) USING BTREE;

-- 入库单表
ALTER table stock_inventory_in add COLUMN  inventory_in_plan_head_id BIGINT  COMMENT '计划入库单id';
ALTER TABLE stock_inventory_in ADD INDEX idx_stock_inventory_in_1(inventory_in_plan_head_id) USING BTREE;

-- 入库单详情表
ALTER table stock_inventory_in_supplies add COLUMN  inventory_in_plan_line_id BIGINT  COMMENT '计划入库单行id';
ALTER TABLE stock_inventory_in_supplies ADD INDEX idx_stock_inventory_in_supplies_2(inventory_in_plan_line_id) USING BTREE;

-- 出库单详情历史表
ALTER table stock_delivery_detail_history add COLUMN  delivery_plan_line_id BIGINT  COMMENT '计划出库单行id';
ALTER TABLE stock_delivery_detail_history ADD INDEX idx_stock_delivery_detail_his_1(delivery_id) USING BTREE;
ALTER TABLE stock_delivery_detail_history ADD INDEX idx_stock_delivery_detail_his_2(delivery_plan_line_id) USING BTREE;

-- 出库单历史表
ALTER table stock_delivery_history add COLUMN  delivery_plan_line_id BIGINT  COMMENT '计划出库单行id';
ALTER TABLE stock_delivery_history ADD INDEX idx_stock_delivery_detail_his_1(delivery_plan_line_id) USING BTREE;

-- 仓库物料分类关联表
CREATE TABLE stock_warehouse_supplies_category(
    id BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键' ,
    warehouse_code VARCHAR(64) NOT NULL   COMMENT '仓库编码' ,
    supplies_cat_code VARCHAR(255) NOT NULL   COMMENT '物料分类编码' ,
    status INT NOT NULL  DEFAULT 0 COMMENT '状态：0无效, 1有效' ,
    CREATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '创建人' ,
    CREATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
    UPDATED_BY VARCHAR(32) NOT NULL  DEFAULT 0 COMMENT '更新人' ,
    UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' ,
    PRIMARY KEY (id)
) COMMENT = '仓库物料分类关联表';

ALTER TABLE stock_warehouse_supplies_category ADD INDEX idx_stock_warehouse_supplies_category_1(warehouse_code) USING BTREE;

ALTER TABLE stock_warehouse_supplies_category ADD INDEX idx_stock_warehouse_supplies_category_2(supplies_cat_code) USING BTREE;

ALTER TABLE `stock`.`stock_delivery`
CHANGE COLUMN `status` `status` INT(4) NOT NULL COMMENT '(-1，新建；0，待推送;1，推送成功;2 , 推送失败;3, 推送中;4,签收;5 取消;)' ,
CHANGE COLUMN `out_stock_type` `out_stock_type` INT(4) NULL DEFAULT NULL COMMENT '出库类型' ,
CHANGE COLUMN `is_send` `is_send` INT(4) NULL DEFAULT NULL COMMENT '是否需要发货 0， 无需发货，1，需要发货' ;

ALTER TABLE `stock`.`stock_delivery_detail`
CHANGE COLUMN `quality` `quality` INT(4) NULL DEFAULT NULL ;

ALTER TABLE `stock`.`stock_delivery_detail_history`
CHANGE COLUMN `quality` `quality` INT(4) NULL DEFAULT NULL ;

ALTER TABLE `stock`.`stock_delivery_history`
CHANGE COLUMN `is_send` `is_send` INT(4) NULL DEFAULT NULL COMMENT '是否需要发货 0， 无需发货，1，需要发货' ,
CHANGE COLUMN `approval_status` `approval_status` INT(4) NULL DEFAULT NULL COMMENT '审批状态 0 创建未发起审批，1 待审批， 2 审批成功，3 审批失败' ;

ALTER TABLE `stock`.`stock_inventory_in`
CHANGE COLUMN `inventory_in_type` `inventory_in_type` INT(5) NOT NULL COMMENT '入库类型' ,
CHANGE COLUMN `status` `status` INT(5) NOT NULL COMMENT '状态 0 待入库，1 已入库' ,
CHANGE COLUMN `approval_status` `approval_status` INT(5) NULL DEFAULT NULL COMMENT '审批状态 0 创建未发起审批，1 待审批， 2 审批成功，3 审批失败' ;

ALTER TABLE `stock`.`stock_inventory_in_supplies`
CHANGE COLUMN `status` `status` INT(5) NOT NULL COMMENT '状态 0 冻结 1 正常' ,
CHANGE COLUMN `quality` `quality` INT(5) NOT NULL COMMENT '品质 1：良品 2：残品 3 ：待检品' ,
CHANGE COLUMN `print_label_type` `print_label_type` INT(5) NOT NULL COMMENT '生成标签类型 1. 不打印   2. 创建入库单时打印    3. 入库时打印' ;

ALTER TABLE `stock`.`stock_supplies`
CHANGE COLUMN `purchase_price` `purchase_price` DECIMAL(11,2) NULL DEFAULT NULL COMMENT '采购价格' ;

ALTER TABLE `stock`.`stock_supplies`
CHANGE COLUMN `type` `type` INT(5) NOT NULL COMMENT '物料类型  1：普通物料 、2 ：固定资产、3：低值资产、4：低值易耗、5：父物料 、6：虚拟物料' ,
CHANGE COLUMN `status` `status` INT(5) NOT NULL DEFAULT '1' COMMENT '使用状态（0，禁用；1，启用；2，不可修改）' ,
CHANGE COLUMN `del_flag` `del_flag` INT(5) NOT NULL DEFAULT '0' COMMENT '删除标识(0，未删除；1，已删除)' ,
CHANGE COLUMN `manage_type` `manage_type` INT(5) NOT NULL COMMENT '是否需要特殊管理 0，无需管理，1，需要生产批次管理；2，需要sn管理' ,
CHANGE COLUMN `can_split` `can_split` INT(5) NOT NULL DEFAULT '0' COMMENT '是否允许拆分 0，不允许；1，允许' ,
CHANGE COLUMN `first_full` `first_full` INT(5) NOT NULL DEFAULT '1' COMMENT '是否优先整单发货(1，优先整单发货；0，不优先整单发货)' ,
CHANGE COLUMN `with_package` `with_package` INT(5) NULL DEFAULT NULL COMMENT '是否带包装 0 不带包装 , 1带包装' ;

DROP TABLE IF EXISTS stock_supplies_brand ;
CREATE TABLE `stock_supplies_brand` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '品牌id',
  `brand_code` varchar(255) NOT NULL COMMENT '品牌编码',
  `brand_name` varchar(255) NOT NULL COMMENT '品牌名称',
  `remark` varchar(255) NOT NULL COMMENT '品牌描述',
  `status` int(4) NOT NULL DEFAULT '1' COMMENT '使用状态（0，禁用；1，启用）',
  `del_flag` int(4) NOT NULL DEFAULT '0' COMMENT '删除标识(0，未删除；1，已删除)',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_brand_code` (`brand_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='品牌表';


DROP TABLE IF EXISTS stock_supplies_model ;
CREATE TABLE `stock_supplies_model` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '型号id',
  `model_code` varchar(255) NOT NULL COMMENT '物料型号编码',
  `brand_code` varchar(255) NOT NULL COMMENT '品牌编码',
  `model_name` varchar(255) NOT NULL COMMENT '型号名称',
  `status` int(4) NOT NULL DEFAULT '1' COMMENT '使用状态（0，禁用；1，启用）',
  `del_flag` int(4) NOT NULL DEFAULT '0' COMMENT '删除标识(0，未删除；1，已删除)',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_model_code` (`model_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='物料型号表';


DROP TABLE IF EXISTS stock_assets_category ;
CREATE TABLE `stock_assets_category` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '资产分类id',
  `category_code` varchar(255) NOT NULL COMMENT '分类编码',
  `category_name` varchar(255) NOT NULL COMMENT '分类名称',
  `status` int(4) NOT NULL DEFAULT '1' COMMENT '使用状态（0，禁用；1，启用）',
  `del_flag` int(4) NOT NULL DEFAULT '0' COMMENT '删除标识(0，未删除；1，已删除)',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_category_code` (`category_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='资产分类表';

DROP TABLE IF EXISTS stock_assets_operation_log;
CREATE TABLE `stock_assets_operation_log` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '记录主键',
  `operation_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '修改类型 1 手动更新，2 业务更新， 3 初始化导入',
  `assets_id` bigint(18) NOT NULL  COMMENT '资产主键',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `assets_name` varchar(255) NOT NULL COMMENT '资产名称',
  `supplies_code` varchar(64) NOT NULL COMMENT '物料编码',
  `warehouse_code` varchar(64) NOT NULL COMMENT '仓库编码',
  `sn_code` varchar(64) DEFAULT NULL COMMENT '序列号',
  `device_code` varchar(64) DEFAULT NULL COMMENT '资产设备码',
  `brand` varchar(255) DEFAULT NULL COMMENT '品牌',
  `model` varchar(255) DEFAULT NULL COMMENT '型号',
  `unit` varchar(32) DEFAULT NULL,
  `category` varchar(255) DEFAULT NULL COMMENT '资产分类',
  `assets_deploy` varchar(255) DEFAULT NULL COMMENT '资产配置',
  `has_sub` tinyint(4) NOT NULL COMMENT '使用有子资产 0 没有自资产, 1 拥有自资产',
  `company_code` varchar(64) NOT NULL COMMENT '所属公司编码',
  `company_name` varchar(255) DEFAULT NULL COMMENT '所属公司名称',
  `status` tinyint(2) NOT NULL COMMENT '资产状态',
  `net_value` decimal(10,2) DEFAULT NULL COMMENT '资产净值',
  `initial_value` decimal(10,2) DEFAULT NULL COMMENT '资产原值',
  `scrap_value` decimal(10,2) DEFAULT NULL COMMENT '资产残值 : 资产报废后的价值',
  `purchase_type` tinyint(4) NOT NULL COMMENT '购置方式',
  `depreciation_way` tinyint(4) DEFAULT NULL COMMENT '折旧方式',
  `cost_dept` varchar(64) DEFAULT NULL COMMENT '费用部门',
  `purchase_time` datetime DEFAULT NULL COMMENT '购置日期',
  `storage_time` datetime DEFAULT NULL COMMENT '入库日期',
  `plan_handle_time` datetime DEFAULT NULL COMMENT '预计处置日期',
  `use_year_limit` int(5) DEFAULT NULL COMMENT '使用年限',
  `regular_maintain` tinyint(4) NOT NULL COMMENT '是否定期维护 0 无需维护 , 1 定期维护',
  `maintain_cycle` varchar(32) DEFAULT NULL COMMENT '维护周期',
  `assets_keeper` varchar(32) DEFAULT NULL COMMENT '资产管理员',
  `assets_pic` varchar(255) DEFAULT NULL COMMENT '资产图片',
  `saas_assets_code` varchar(64) DEFAULT NULL,
  `saas_address` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='资产卡片修改日志表';

DROP TABLE IF EXISTS stock_supplies_operation_log ;
CREATE TABLE `stock_supplies_operation_log` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `supplies_id` bigint(18) NOT NULL COMMENT '物料id',
  `parent_id` bigint(18) NOT NULL COMMENT '父物料主键',
  `code` varchar(255) NOT NULL COMMENT '物料编码',
  `name` varchar(255) NOT NULL COMMENT '物料名称',
  `cat_code` varchar(64) NOT NULL COMMENT '物料分类编码',
  `cat_full_code` varchar(255) NOT NULL COMMENT '物料分类全编码',
  `unit_code` varchar(64) DEFAULT NULL COMMENT '单位主键',
  `purchase_unit_code` varchar(64) DEFAULT NULL COMMENT '采购单位主键',
  `brand_code` varchar(255) DEFAULT NULL COMMENT '品牌',
  `model_code` varchar(255) DEFAULT NULL COMMENT '物料型号编码',
  `specs` varchar(255) DEFAULT NULL COMMENT '规格',
  `purchase_price` decimal(11,2) DEFAULT NULL COMMENT '采购价格',
  `supplies_source` varchar(32) NOT NULL COMMENT '物料来源：1. 手工   2. 新车系统   3. 二手车系统   4. 后市场系统   5. 租车系统',
  `type` int(5) NOT NULL COMMENT '物料类型  1：普通物料 、2 ：固定资产、3：低值资产、4：低值易耗、5：父物料 、6：虚拟物料',
  `status` int(5) NOT NULL DEFAULT '1' COMMENT '使用状态（0，禁用；1，启用；2，不可修改）',
  `apply_by` varchar(32) DEFAULT NULL COMMENT '申请人',
  `del_flag` int(5) NOT NULL DEFAULT '0' COMMENT '删除标识(0，未删除；1，已删除)',
  `remark` varchar(255) DEFAULT NULL COMMENT '描述',
  `search_label` varchar(255) DEFAULT NULL COMMENT '查询标签',
  `manage_type` int(5) NOT NULL COMMENT '是否需要特殊管理 0，无需管理，1，需要生产批次管理；2，需要sn管理',
  `produce_factory` varchar(255) DEFAULT NULL COMMENT '生产厂家',
  `service_factory` varchar(255) DEFAULT NULL COMMENT '服务厂家',
  `aftersale_factory` varchar(255) DEFAULT NULL COMMENT '售后厂家',
  `min_delivery_num` int(5) DEFAULT NULL,
  `pop_code` varchar(255) DEFAULT NULL COMMENT '物料pop码',
  `bar_code` varchar(255) DEFAULT NULL COMMENT '物料69码',
  `can_split` int(5) NOT NULL DEFAULT '0' COMMENT '是否允许拆分 0，不允许；1，允许',
  `first_full` int(5) NOT NULL DEFAULT '1' COMMENT '是否优先整单发货(1，优先整单发货；0，不优先整单发货)',
  `with_package` int(5) DEFAULT NULL COMMENT '是否带包装 0 不带包装 , 1带包装',
  `image` varchar(2000) DEFAULT NULL COMMENT '物料图片',
  `material_quality` varchar(32) DEFAULT NULL COMMENT '材质',
  `color` varchar(32) DEFAULT NULL COMMENT '颜色',
  `supplies_long` varchar(32) DEFAULT NULL COMMENT '长',
  `supplies_width` varchar(32) DEFAULT NULL COMMENT '宽',
  `supplies_high` varchar(32) DEFAULT NULL COMMENT '高',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_supplies_id` (`supplies_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='物料修改日志表';



-- 删除原主键
ALTER TABLE stock_supplies_unit DROP PRIMARY KEY ;
-- 恢复原主键
-- alter table stock_supplies_unit add primary key(id) USING BTREE;

-- 添加新列单位id
ALTER TABLE stock_supplies_unit
ADD COLUMN `id` bigint(18) NULL COMMENT '单位id' FIRST;

-- 设置为主键并自增
ALTER TABLE stock_supplies_unit  MODIFY id BIGINT(18) AUTO_INCREMENT PRIMARY KEY COMMENT '单位id';

ALTER TABLE stock_supplies_unit COMMENT '物料单位表';

-- 添加唯一索引
ALTER TABLE `stock_supplies_unit` ADD CONSTRAINT uniq_unit_code UNIQUE(`unit_code`) USING BTREE;

-- 资产表添加分类字段
ALTER TABLE stock_assets
ADD COLUMN `category` varchar(255) DEFAULT NULL COMMENT '资产分类' AFTER unit;

ALTER TABLE stock_supplies
ADD COLUMN `produce_factory` varchar(255) DEFAULT NULL COMMENT '生产厂家' AFTER manage_type,
ADD COLUMN `service_factory` varchar(255) DEFAULT NULL COMMENT '服务厂家' AFTER produce_factory,
ADD COLUMN `aftersale_factory` varchar(255) DEFAULT NULL COMMENT '售后厂家' AFTER service_factory;

alter table stock_inventory_in_plan_heads add COLUMN demand_dept_code varchar(32) COMMENT '需求部门id';

ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
CHANGE COLUMN `real_number` `real_number` INT(11) NULL DEFAULT NULL COMMENT '实际入库数量';
-- 2019.12.11 after
-- 修改为非必填
ALTER TABLE stock_supplies
modify COLUMN `type` int(4) NULL DEFAULT 1 COMMENT '物料类型  1：普通物料 、2 ：固定资产、3：低值资产、4：低值易耗、5：父物料 、6：虚拟物料' AFTER supplies_source ;
ALTER TABLE stock_supplies MODIFY COLUMN `parent_id` bigint(18) NULL COMMENT '父物料主键';
ALTER TABLE `stock_supplies` ADD CONSTRAINT uniq_code UNIQUE(`code`) USING BTREE;

ALTER TABLE stock_supplies MODIFY COLUMN `parent_id` bigint(18) NULL COMMENT '父物料主键';

-- 修改为非必填
ALTER TABLE stock_supplies_operation_log MODIFY COLUMN `parent_id` bigint(18) NULL COMMENT '父物料主键';
ALTER TABLE stock_supplies_operation_log
modify COLUMN `type` int(4) NULL DEFAULT 1 COMMENT '物料类型  1：普通物料 、2 ：固定资产、3：低值资产、4：低值易耗、5：父物料 、6：虚拟物料' AFTER supplies_source ;

ALTER TABLE stock_inventory_in_plan_heads add COLUMN demand_dept_id varchar(32) COMMENT '需求部门id';

ALTER TABLE stock_assets
ADD COLUMN `need_dept` varchar(64) DEFAULT NULL COMMENT '需求部门' AFTER cost_dept,
ADD COLUMN `holder`varchar(32) DEFAULT NULL COMMENT '资产持有人' AFTER need_dept;

ALTER TABLE stock_assets_operation_log
ADD COLUMN `need_dept` varchar(64) DEFAULT NULL COMMENT '需求部门' AFTER cost_dept,
ADD COLUMN `holder`varchar(32) DEFAULT NULL COMMENT '资产持有人' AFTER need_dept;

-- 修改为非必填
ALTER TABLE stock_assets MODIFY COLUMN has_sub tinyint(4) NULL COMMENT '使用有子资产 0 没有自资产, 1 拥有自资产';
ALTER TABLE stock_assets_operation_log MODIFY COLUMN has_sub tinyint(4) NULL COMMENT '使用有子资产 0 没有自资产, 1 拥有自资产';

alter table stock_inventory_asset_import add column asset_type_name varchar(255) comment '资产类别名称';

ALTER TABLE `stock`.`stock_delivery_plan_lines`
ADD COLUMN `is_send` INT(11) NOT NULL DEFAULT 0 COMMENT '是否发运： 0 否 1 是' AFTER `status`;

ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `plan_in_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `status`;

-- 添加唯一索引
ALTER TABLE `stock_assets` ADD CONSTRAINT uniq_assets_code UNIQUE(`assets_code`) USING BTREE;

ALTER TABLE stock_assets
ADD COLUMN `purchase_no`varchar(32) DEFAULT NULL COMMENT '采购入库单号' AFTER purchase_type;

ALTER TABLE stock_assets_operation_log
ADD COLUMN `purchase_no`varchar(32) DEFAULT NULL COMMENT '采购入库单号' AFTER purchase_type;

ALTER TABLE stock_warehouse_base
ADD COLUMN `bus_large_region`varchar(32) DEFAULT NULL COMMENT '所在大区' AFTER contact_way,
ADD COLUMN `bus_city` varchar(32) DEFAULT NULL COMMENT '所在城市' AFTER bus_large_region;

ALTER TABLE `stock`.`stock_delivery_detail`
DROP COLUMN `assets_code`;

ALTER TABLE `stock`.`stock_inventory_in`
CHANGE COLUMN `dept_code` `dept_code` VARCHAR(32) NULL COMMENT '部门编码' ;

ALTER TABLE `stock`.`stock_delivery_plan_heads`
CHANGE COLUMN `is_send` `is_send` INT(11) NULL DEFAULT '0' COMMENT '是否发货(0: 否，1：是)' ;


ALTER TABLE `stock`.`stock_warehouse_supplies_category` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

 ALTER TABLE `stock`.`stock_sns_assets` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

 ALTER TABLE `stock`.`stock_sns` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

 ALTER TABLE `stock`.`stock_assets_sns` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

 ALTER TABLE `stock`.`stock_inventory_asset_import` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

 ALTER TABLE `stock`.`stock_inventory_in_supplies_sns` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

 ALTER TABLE `stock`.`stock_inventory_in_plan_lines` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

  ALTER TABLE `stock`.`stock_inventory_in_plan_heads` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

  ALTER TABLE `stock`.`stock_delivery_detail_assets_history` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

  ALTER TABLE `stock`.`stock_delivery_detail_sns_history` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

  ALTER TABLE `stock`.`stock_delivery_detail_sns` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

  ALTER TABLE `stock`.`stock_delivery_detail_assets` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

   ALTER TABLE `stock`.`stock_delivery_plan_lines` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

   ALTER TABLE `stock`.`stock_delivery_plan_heads` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

    ALTER TABLE `stock`.`stock_allocate_import_lines` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

    ALTER TABLE `stock`.`stock_allocate_import_heads` CHANGE COLUMN
 UPDATED_AT UPDATED_AT timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP  ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';


 use stock;
 ALTER TABLE stock_delivery_detail_assets
 add column `delivery_id` bigint(18) NOT NULL COMMENT '发货单id' AFTER id ;

 CREATE TABLE `stock_inventory_in_supplies_assets` (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
   `in_supplies_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '入库单行id',
   `assets_code` varchar(64) NOT NULL DEFAULT '' COMMENT '资产编码',
   `CREATED_BY` varchar(32) NOT NULL DEFAULT '0' COMMENT '创建人',
   `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `UPDATED_BY` varchar(32) NOT NULL DEFAULT '0' COMMENT '更新人',
   `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`),
   KEY `idx_inventory_in_supplies_assets_1` (`in_supplies_id`) USING BTREE,
   KEY `idx_inventry_in_supplies_assets_2` (`assets_code`) USING BTREE
 ) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COMMENT='入库单行资产关联表';

 ALTER TABLE `stock`.`stock_inventory_in_plan_heads`
 CHANGE COLUMN `plan_in_time` `plan_in_time` DATETIME NULL COMMENT '计划入库日期' ;

 ALTER TABLE `stock`.`stock_delivery_detail_sns_history`
 CHANGE COLUMN `CREATED_BY1` `CREATED_BY` VARCHAR(32) NOT NULL COMMENT '创建人' ,
 CHANGE COLUMN `CREATED_AT1` `CREATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
 CHANGE COLUMN `UPDATED_BY1` `UPDATED_BY` VARCHAR(32) NOT NULL COMMENT '更新人' ,
 CHANGE COLUMN `UPDATED_AT` `UPDATED_AT` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP  COMMENT '更新时间' ;

 ALTER TABLE `stock`.`stock_inventory_in_plan_heads`
 CHANGE COLUMN `plan_in_time` `plan_in_time` DATETIME NULL COMMENT '计划入库日期' ;

 ALTER TABLE `stock`.`stock_delivery_detail`
 CHANGE COLUMN `is_send` `is_send` INT DEFAULT 0 COMMENT '是否发运：1 是 0 否';

 ALTER TABLE `stock`.`stock_delivery_detail_history`
 CHANGE COLUMN `is_send` `is_send` INT DEFAULT 0 COMMENT '是否发运：1 是 0 否';

 CREATE TABLE `stock_assets_initialization` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `assets_code` varchar(64) DEFAULT NULL COMMENT '资产编码',
   `assets_name` varchar(255) DEFAULT NULL COMMENT '资产名称',
   `supplies_code` varchar(64) DEFAULT '' COMMENT '物料编码',
   `warehouse_code` varchar(64) NOT NULL DEFAULT 'GDZC001' COMMENT '仓库编码',
   `brand` varchar(255) DEFAULT NULL COMMENT '品牌',
   `model` varchar(255) DEFAULT NULL COMMENT '规格',
   `use_year_limit` int(5) DEFAULT NULL COMMENT '使用年限',
   `purchase_type` int(5) DEFAULT '1' COMMENT '购置方式',
   `assets_keeper` varchar(32) DEFAULT '' COMMENT '资产管理员',
   `company_name` varchar(255) DEFAULT '' COMMENT '所属公司',
   `category` varchar(255) DEFAULT '' COMMENT '资产分类',
   `remark` varchar(255) DEFAULT NULL COMMENT '描述',
   `holder` varchar(32) DEFAULT NULL COMMENT '当前持有人',
   `type` int(5) NOT NULL DEFAULT '1' COMMENT '初始化类型：1采购  2领取',
   `sn_code` varchar(255) DEFAULT NULL COMMENT '序列号',
   `batch_id` int(11) DEFAULT '1' COMMENT '批次id',
   `item_remark` varchar(255) DEFAULT NULL,
   `delete_flag` int(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
   PRIMARY KEY (`id`),
   KEY `idx_stock_assets_initialization_1` (`assets_code`) USING BTREE,
   KEY `idx_stock_assets_initialization_2` (`holder`) USING BTREE,
   KEY `idx_stock_assets_initialization_3` (`batch_id`) USING BTREE
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


 use stock;

 ALTER TABLE stock_delivery_plan_heads
 add column `use_address` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '领用人位置' AFTER use_user ;

 ALTER TABLE stock_assets
 add column `holder_address` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '资产持有人位置' AFTER holder ;

 ALTER TABLE stock_assets_operation_log
 add column `holder_address` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '资产持有人位置' AFTER holder ;


 ALTER TABLE stock_assets
 add column `holder_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间' AFTER holder_address ;

 ALTER TABLE stock_assets_operation_log
 add column `holder_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间' AFTER holder_address ;
