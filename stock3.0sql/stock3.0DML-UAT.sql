SET SQL_SAFE_UPDATES = 0;
update stock_supplies ss set ss.purchase_price = (ss.purchase_price / 100) where 1=1;

insert into stock_delivery_detail_sns (`delivery_detail_id`, `SERIAL_NUMBER`, `CREATED_BY`, `UPDATED_BY`) select  `delivery_detail_id`, `sn_no`, 1, 1 from stock_delivery_detail where sn_no is not null and sn_no != '';
insert into stock_delivery_detail_sns_history (`delivery_detail_id`, `SERIAL_NUMBER`, `CREATED_BY`, `UPDATED_BY`) select  `delivery_detail_id`, `sn_no`, 1, 1 from stock.stock_delivery_detail_history where sn_no is not null and sn_no != '';
insert into stock_inventory_in_supplies_sns (`in_supplies_id`, `SERIAL_NUMBER`, `CREATED_BY`, `UPDATED_BY`) select  `in_supplies_id`, `sn_no`, 1, 1 from stock_inventory_in_supplies where sn_no is not null and sn_no != '';

update stock_supplies set cat_code = 'H020101', cat_full_code = '/H02/H0201/H020101' where cat_code = 'HC0101' and cat_full_code = '/HC/HC01/HC0101';
update stock_supplies set cat_code = 'H020202', cat_full_code = '/H02/H0202/H020202' where cat_code = 'HC0201' and cat_full_code = '/HC/HC02/HC0201';
update stock_supplies set cat_code = 'S010302', cat_full_code = '/S01/S0103/S010302' where cat_code = 'SB0101' and cat_full_code = '/SB/SB01/SB0101';
update stock_supplies set cat_code = 'S010102', cat_full_code = '/S01/S0101/S010102' where supplies_id in (15,16,17,18);
update stock_supplies set code = 'S01030100006' where supplies_id = 13;
update stock_supplies_config set supplies_code = 'S01030100006' where supplies_code = 'S01030200006';
update stock_inventory_in_supplies set supplies_code = 'S01030100006' where supplies_code = 'S01030200006';
update stock_supplies set cat_code = 'S010301', cat_full_code = '/S01/S0103/S010301' where cat_code = 'SB0102' and cat_full_code = '/SB/SB01/SB0102';
update stock_supplies_category set del_flag = 1 where supplies_category_id <= 9;
