use stock;

DELETE FROM stock_supplies_category;
-- 一级
-- ALTER TABLE `stock_supplies_category` ADD CONSTRAINT uniq_category_code UNIQUE(`code`) USING BTREE ;

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'S01', '设备类', '1', '2', '/S01', '/设备类', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'H02', '耗材类', '1', '2', '/H02', '/耗材类', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'J03', '家具装饰', '1', '2', '/J03', '/家具装饰', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'K04', '卡', '1', '2', '/K04', '/卡', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'F05', '服务', '1', '2', '/F05', '/服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'J06', '基建', '1', '2', '/J06', '/基建', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ('0', 'C07', '车', '1', '2', '/C07', '/车', '10055052', '10055052');

-- 二级
INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S01'), 'S0101', '专用设备', '2', '2', '/S01/S0101', '/设备类/专用设备', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S01'), 'S0102', '网络设备', '2', '2', '/S01/S0102', '/设备类/网络设备', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S01'), 'S0103', '电子产品', '2', '2', '/S01/S0103', '/设备类/电子产品', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S01'), 'S0104', '智能设备', '2', '2', '/S01/S0104', '/设备类/智能设备', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S01'), 'S0105', '移动通讯设备', '2', '2', '/S01/S0105', '/设备类/移动通讯设备', '10055052', '10055052');


-- 三级
INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0101'), 'S010101', '加工设备', '3', '1', '/S01/S0101/S010101', '/设备类/专用设备/加工设备', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0101'), 'S010102', '检查仪器', '3', '1', '/S01/S0101/S010102', '/设备类/专用设备/检查仪器', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0101'), 'S010104', '金融GPS', '3', '1', '/S01/S0101/S010104', '/设备类/专用设备/金融GPS', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0101'), 'S010103', '工具', '3', '1', '/S01/S0101/S010103', '/设备类/专用设备/工具', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0102'), 'S010201', '网络设备', '3', '1', '/S01/S0102/S010201', '/设备类/网络设备/网络设备', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0103'), 'S010301', '办公设备', '3', '1', '/S01/S0103/S010301', '/设备类/电子产品/办公设备', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0103'), 'S010302', '电器', '3', '1', '/S01/S0103/S010302', '/设备类/电子产品/电器', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0104'), 'S010401', '智能设备', '3', '1', '/S01/S0104/S010401', '/设备类/智能设备/智能设备', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0105'), 'S010501', '手机', '3', '1', '/S01/S0105/S010501', '/设备类/移动通讯设备/手机', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'S0105'), 'S010502', '对讲机', '3', '1', '/S01/S0105/S010502', '/设备类/移动通讯设备/对讲机', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H02'), 'H0201', '配件', '2', '2', '/H02/H0201', '/耗材类/配件', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0201'), 'H020101', '专用配件', '3', '1', '/H02/H0201/H020101', '/耗材类/配件/专用配件', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0201'), 'H020102', '网络配件', '3', '1', '/H02/H0201/H020102', '/耗材类/配件/网络配件', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0201'), 'H020103', '电子配件', '3', '1', '/H02/H0201/H020103', '/耗材类/配件/电子配件', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0201'), 'H020104', '智能配件', '3', '1', '/H02/H0201/H020104', '/耗材类/配件/智能配件', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H02'), 'H0202', '办公日用', '2', '2', '/H02/H0202', '/耗材类/办公日用', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0202'), 'H020201', '办公文具', '3', '1', '/H02/H0202/H020201', '/耗材类/办公日用/办公文具', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0202'), 'H020202', '活动用品', '3', '1', '/H02/H0202/H020202', '/耗材类/办公日用/活动用品', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H02'), 'H0203', '定制品', '2', '2', '/H02/H0203', '/耗材类/定制品', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0203'), 'H020301', '专属定制', '3', '1', '/H02/H0203/H020301', '/耗材类/定制品/专属定制', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0203'), 'H020302', '印刷品', '3', '1', '/H02/H0203/H020302', '/耗材类/定制品/印刷品', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H02'), 'H0204', '劳保用品', '2', '2', '/H02/H0204', '/耗材类/劳保用品', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0204'), 'H020401', '工服', '3', '1', '/H02/H0204/H020401', '/耗材类/劳保用品/工服', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'H0204'), 'H020402', '保洁用品', '3', '1', '/H02/H0204/H020402', '/耗材类/劳保用品/保洁用品', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J03'), 'J0301', '家具', '2', '2', '/J03/J0301', '/家具装饰/家具', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J0301'), 'J030101', '家具', '3', '1', '/J03/J0301/J030101', '/家具装饰/家具/家具', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J03'), 'J0302', '装饰', '2', '2', '/J03/J0302', '/家具装饰/装饰', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J0302'), 'J030201', '装饰品', '3', '1', '/J03/J0302/J030201', '/家具装饰/装饰/装饰品', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'K04'), 'K0401', '电子卡', '2', '2', '/K04/K0401', '/卡/电子卡', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'K0401'), 'K040101', '电子卡', '3', '1', '/K04/K0401/K040101', '/卡/电子卡/电子卡', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'K04'), 'K0402', '实体卡', '2', '2', '/K04/K0402', '/卡/实体卡', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'K0402'), 'K040201', '实体卡', '3', '1', '/K04/K0402/K040201', '/卡/实体卡/实体卡', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0501', '软件服务', '2', '2', '/F05/F0501', '/服务/软件服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0501'), 'F050101', '软件服务', '3', '1', '/F05/F0501/F050101', '/服务/软件服务/软件服务', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0502', '专业服务', '2', '2', '/F05/F0502', '/服务/专业服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0502'), 'F050201', '专业服务', '3', '1', '/F05/F0502/F050201', '/服务/专业服务/软件服务', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0503', '技术服务', '2', '2', '/F05/F0503', '/服务/技术服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0503'), 'F050301', '技术服务', '3', '1', '/F05/F0503/F050301', '/服务/技术服务/技术服务', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0504', '咨询服务', '2', '2', '/F05/F0504', '/服务/咨询服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0504'), 'F050401', '咨询服务', '3', '1', '/F05/F0504/F050401', '/服务/咨询服务/咨询服务', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0505', '维修服务', '2', '2', '/F05/F0505', '/服务/维修服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0505'), 'F050501', '维修服务', '3', '1', '/F05/F0505/F050501', '/服务/维修服务/维修服务', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0506', '工程服务', '2', '2', '/F05/F0506', '/服务/工程服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0506'), 'F050601', '工程服务', '3', '1', '/F05/F0506/F050601', '/服务/工程服务/工程服务', '10055052', '10055052');



INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F05'), 'F0507', '行政服务', '2', '2', '/F05/F0507', '/服务/行政服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'F0507'), 'F050701', '行政服务', '3', '1', '/F05/F0507/F050701', '/服务/行政服务/行政服务', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J06'), 'J0601', '办公', '2', '2', '/J06/J0601', '/基建/办公', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J06'), 'J0602', '门店', '2', '2', '/J06/J0602', '/基建/门店', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J0601'), 'J060101', '办公', '3', '1', '/J06/J0601/J060101', '/基建/办公/办公', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'J0602'), 'J060201', '门店', '3', '1', '/J06/J0602/J060201', '/基建/门店/门店', '10055052', '10055052');


INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'C07'), 'C0701', '二手车', '2', '2', '/C07/C0701', '/车/二手车', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'C07'), 'C0702', '新车', '2', '2', '/C07/C0702', '/车/新车', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'C0701'), 'C070101', '二手车', '3', '1', '/C07/C0701/C070101', '/车/二手车/二手车', '10055052', '10055052');

INSERT INTO `stock_supplies_category` (parent_id,`code`,`name`,`level`,`type`,`all_code`,`all_name`,created_by,updated_by)
VALUES ((select supplies_category_id from stock_supplies_category c where c.code = 'C0702'), 'C070201', '新车', '3', '1', '/C07/C0702/C070201', '/车/新车/新车', '10055052', '10055052');



UPDATE stock_supplies set `cat_code`= 'H020101' , cat_full_code = '/H02/H0201/H020101', code = CONCAT(cat_code,right(code,5)) where cat_code = 'HC0101';
UPDATE stock_supplies set `cat_code`= 'H020202' , cat_full_code = '/H02/H0202/H020202', code = CONCAT(cat_code,right(code,5)) where cat_code = 'HC0201';
UPDATE stock_supplies set `cat_code`= 'S010302' , cat_full_code = '/S01/S0103/S010302', code = CONCAT(cat_code,right(code,5)) where cat_code = 'SB0101';
UPDATE stock_supplies set `cat_code`= 'S010301' , cat_full_code = '/S01/S0103/S010301', code = CONCAT(cat_code,right(code,5)) where cat_code = 'SB0102';

-- select cat_code,cat_full_code from  stock_supplies GROUP BY cat_code;


INSERT INTO `stock_supplies_unit`(unit_code,unit_name,status,del_flag,created_by,updated_by) VALUES
('ZHI', '只', '1', '0', '10055052', '10055052'),
('TAO', '套', '1', '0', '10055052', '10055052'),
('BRANCH', '支', '1', '0', '10055052', '10055052'),
('BOX', '箱', '1', '0', '10055052', '10055052'),
('JIAN', '件', '1', '0', '10055052', '10055052'),
('ZU', '组', '1', '0', '10055052', '10055052');

-- 已初始化
INSERT INTO `stock_supplies` VALUES
(null, null, 'S01030100008', '笔记本电脑', 'S010301', '/S01/S0103/S010301', 'TAI', 'TAI', '小米', '新车礼品专用', null, '0.00', '1', '1', '1', null, '0', '笔记本电脑；小米；新车礼品专用', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:51:11', '10055052', '2019-12-27 15:28:40'),
(null, null, 'S01050100002', '手机', 'S010501', '/S01/S0105/S010501', 'EA', 'EA', '华为', 'Mate30 新车礼品专用', null, '3999.00', '1', '1', '1', null,
'0', '手机；华为；Mate30', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:52:13', '10055052', '2019-12-27 10:52:13'),
 (null, null, 'S01050100003', '手机', 'S010501', '/S01/S0105/S010501', 'TAO', 'TAO', '华为', '20I  新车礼品套装', null, '1300.00', '1', '1', '1', null, '0', '手机；华为；20I  新车礼品套装', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:53:26', '10055052', '2019-12-27 10:53:26'),
(null, null, 'H02020200009', '电暖气', 'H020202', '/H02/H0202/H020202', 'EA', 'EA', '无', '新车活动款', null, '310.00', '1', '1', '1', null, '0', '电暖气；无；新车活动款', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:54:28', '10055052', '2019-12-27 10:54:28'),
(null, null, 'H02020200010', '水暖毯', 'H020202', '/H02/H0202/H020202', 'EA', 'EA', '无', '新车活动款', null, '105.00', '1', '1', '1', null, '0', '水暖毯；无；新车活动款', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:55:20', '10055052', '2019-12-27 10:55:20'),
(null, null, 'K04020100001', '优酷季卡', 'K040201', '/K04/K0402/K040201', 'PIC', 'PIC', '优酷', '季度卡', null, '40.30', '1', '1', '1', null, '0', '优酷季卡；优酷；季度卡', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:56:11', '10055052', '2019-12-27 10:56:11'),
 (null, null, 'K04020100002', '净化器兑换卡', 'K040201', '/K04/K0402/K040201', 'PIC', 'PIC', '无', '新车活动卡', null, '0.00', '1', '1', '1', null, '0', '净化器兑换卡；无；新车活动卡', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:57:42', '10055052', '2019-12-27 10:57:42'),
(null, null, 'H02020200011', '新年福袋', 'H020202', '/H02/H0202/H020202', 'TAO', 'TAO', '无', '新车礼品-福袋', null, '3.00', '1', '1', '1', null, '0', '新年福袋；无；新车礼品-福袋', null, '0', '', '', '', null, null, null, '0', '1', null, null, null, null, '', '', '', '10055052', '2019-12-27 10:58:33', '10055052', '2019-12-27 10:58:33');

-- update stock_supplies set apply_by = '10055052' where apply_by is null;
-- update stock_supplies set model_code = 'Mate30 新车礼品专用' where code = 'S01050100002';
