-- 更新资产管理员
update stock_assets as q
 inner join (select t.assets_code,t.created_by,t.id from stock_inventory_in_supplies_assets t where t.id in
(select max(id) from stock_inventory_in_supplies_assets a where exists (select 1 from stock_inventory_in b,
 stock_inventory_in_supplies c where b.inventory_in_type in (6,7) and b.status=5
 and b.inventory_in_id = c.inventory_in_id and c.in_supplies_id = a.in_supplies_id)
  and a.created_at>'2020-08-01 00:00:00' group by a.assets_code) ) as dd on q.assets_code=dd.assets_code
 set q.assets_keeper=dd.created_by;

 -- 插入资产日志表记录
 insert into stock_assets_operation_log (operation_type, assets_id,
       assets_code, assets_name, supplies_code,
       warehouse_code, sn_code, device_code,
       brand, model, unit,
       category, assets_deploy, has_sub,
       company_code, company_name, status,
       conditions, net_value, initial_value,
       scrap_value, purchase_type, purchase_no,
       depreciation_way, cost_dept, need_dept,
       holder, holder_address, holder_time,
       purchase_time, storage_time, plan_handle_time,
       use_year_limit, regular_maintain, maintain_cycle,
       assets_keeper, assets_pic, saas_assets_code,
       saas_address, remark, label_url,
       created_by, created_at, updated_by,
       updated_at)
      select 1,st.assets_id,
       st.assets_code, st.assets_name, st.supplies_code,
       st.warehouse_code, st.sn_code, st.device_code,
       st.brand, st.model, st.unit,
       st.category, st.assets_deploy, st.has_sub,
       st.company_code, st.company_name, st.status,
       st.conditions, st.net_value, st.initial_value,
       st.scrap_value, st.purchase_type, st.purchase_no,
       st.depreciation_way, st.cost_dept, st.need_dept,
       st.holder, st.holder_address, st.holder_time,
       st.purchase_time, st.storage_time, st.plan_handle_time,
       st.use_year_limit, st.regular_maintain, st.maintain_cycle,
       st.assets_keeper, st.assets_pic, st.saas_assets_code,
       st.saas_address, st.remark, st.label_url,
       '10122730', '2020-08-20 20:00:00', '10122730',
       '2020-08-20 20:00:00' from stock_assets st where exists (select dd.created_by from
 (select assets_code,max(id),created_by from stock_inventory_in_supplies_assets a where exists (select 1 from stock_inventory_in b,
  stock_inventory_in_supplies c where b.inventory_in_type in (6,7) and b.status=5
  and b.inventory_in_id = c.inventory_in_id and c.in_supplies_id = a.in_supplies_id)
   and a.created_at>'2020-08-01 00:00:00' group by assets_code) dd where dd.assets_code=st.assets_code)；

-- 物料H02030200002添加费用项
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`,
 `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`,
 `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-20 14:37:32', '0', '2020-08-20 14:37:32', 0, 'H02030200002', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '宣传印刷页', 0)；

INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
`warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`,
`default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`,
`is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`,
`service_factory`, `after_sale_factory`, `base_id`)
VALUES ('0', '2020-08-20 14:37:32', '0', '2020-08-20 14:37:32', 0, 'H02030200002', 6, 1, -1, -1, 1, -1, '', 'EXTP1150', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
VALUES ('0', '2020-08-20 14:37:32', '0', '2020-08-20 14:37:32', 0, 'H02030200002', 6)；

--修改物料H02020200012描述字段
update stock_supplies set name='项链',remark='项链；施华洛世奇' where code='H02020200012'；

--添加物料H02020200012尾表属性
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`,
`is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`,
`default_asset_category`, `base_id`)
VALUES ('0', '2020-08-19 14:37:32', '0', '2020-08-19 14:37:32', 0, 'H02020200012', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '项链', 0)；

INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`,
 `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-19 14:37:32', '0', '2020-08-19 14:37:32', 0, 'H02020200012', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
VALUES ('0', '2020-08-19 14:37:32', '0', '2020-08-19 14:37:32', 0, 'H02020200012', 11)；

-- 工单DCDA20200817000011处理
update stock_inventory_in_plan_lines_sns set sn_no='987696023790',updated_by='10122730',updated_at='2020-08-18 17:02:51' where sn_no='***********'；
update stock_inventory_in_supplies_sns set  serial_number='987696023790',updated_by='10122730',updated_at='2020-08-18 17:02:51' where serial_number='***********'；
update stock_delivery_detail_sns set  serial_number='987696023790',updated_by='10122730',updated_at='2020-08-18 17:02:51' where serial_number='***********'；
update stock_delivery_detail_sns_history set  serial_number='987696023790',updated_by='10122730',updated_at='2020-08-18 17:02:51' where serial_number='***********'；
update stock_supplies_config_detail set sn_no='987696023790',updated_by='10122730',updated_at='2020-08-18 17:02:51' where sn_no='***********'；
update stock_assets set sn_code='987696023790',updated_by='10122730',updated_at='2020-08-18 17:02:51' where assets_code='1002032402481'；
update stock_assets_operation_log set sn_code='987696023790',updated_by='10122730' where assets_code='1002032402481'；
update stock_assets set sn_code='861838046542859',assets_name='手机;小米;小米Play',model='小米Play',updated_at='2020-08-18 17:02:51' where assets_code='1002011822302'；
INSERT INTO `stock`.`stock_assets_operation_log` (
	`operation_type`,
	`assets_id`,
	`assets_code`,
	`assets_name`,
	`supplies_code`,
	`warehouse_code`,
	`sn_code`,
	`device_code`,
	`brand`,
	`model`,
	`unit`,
	`category`,
	`assets_deploy`,
	`has_sub`,
	`company_code`,
	`company_name`,
	`status`,
	`conditions`,
	`net_value`,
	`initial_value`,
	`scrap_value`,
	`purchase_type`,
	`purchase_no`,
	`depreciation_way`,
	`cost_dept`,
	`need_dept`,
	`holder`,
	`holder_address`,
	`holder_time`,
	`purchase_time`,
	`storage_time`,
	`plan_handle_time`,
	`use_year_limit`,
	`regular_maintain`,
	`maintain_cycle`,
	`assets_keeper`,
	`assets_pic`,
	`saas_assets_code`,
	`saas_address`,
	`remark`,
	`label_url`,
	`created_by`,
	`created_at`,
	`updated_by`,
	`updated_at`
)
VALUES
	(
		2,
		177498,
		'1002011822302',
		'手机',
		'S01050100056',
		'',
		'861838046542859',
		NULL,
		'小米',
		'小米Play',
		NULL,
		'手机',
		NULL,
		0,
		'-',
		'',
		2,
		0,
		NULL,
		NULL,
		NULL,
		1,
		'IN2020040101226',
		NULL,
		NULL,
		NULL,
		'10056670',
		'期初仓库-上海/上海新车一区',
		'2020-04-01 18:59:32',
		NULL,
		'2020-04-01 18:59:33',
		NULL,
		NULL,
		0,
		NULL,
		'10044439',
		NULL,
		NULL,
		NULL,
		'手机;小米;小米Play',
	'',
	'10122730',
	'2020-08-18 17:02:51',
	'10122730',
'2020-08-18 17:02:51'
)；

--物料H02010100008、H02010100009、H02010100010、H02010100011添加费用项
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`,
`is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100008', 4, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '斜坡', 0)；
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`,
 `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100009', 4, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '垫块', 0)；
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`,
 `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100010', 4, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '警示牌', 0)；
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`,
`is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100011', 4, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '警戒线', 0)；
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`,
`is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100012', 4, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '锥桶', 0)；
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
`default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
`default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100008', 4, 1, -1, -1, 1, -1, '', 'EXTP1028', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
`is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
 `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100009', 4, 1, -1, -1, 1, -1, '', 'EXTP1028', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；
 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
 `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100010', 4, 1, -1, -1, 1, -1, '', 'EXTP1028', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；
 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
 `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100011', 4, 1, -1, -1, 1, -1, '', 'EXTP1028', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；
 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
 `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100012', 4, 1, -1, -1, 1, -1, '', 'EXTP1028', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；
 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100008', 4)；
 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100009', 4)；
 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100010', 4)；
 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100011', 4)；
 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-18 15:37:32', '0', '2020-08-18 15:37:32', 0, 'H02010100012', 4)；

 --入库申请单据回退“取消”操作
 update stock_inventory_in_plan_heads set status=3 where delivery_no='OUT2020080700120'

 -- 物料H02030200001添加费用项
 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`,
 `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-10 15:37:32', '0', '2020-08-10 15:37:32', 0, 'H02030200001', 4, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '密封条', 0)；
 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
 `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-10 15:37:32', '0', '2020-08-10 15:37:32', 0, 'H02030200001', 4, 1, -1, -1, 1, -1, '', 'EXTP1028', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；
 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-10 15:37:32', '0', '2020-08-10 15:37:32', 0, 'H02030200001', 4)；
 update stock_supplies set name='RFID车辆标签',remark='RFID车辆标签；无；二手车运营' where code='S01040100010'；

-- 仓储5.0计划入库单数据迁移
insert into stock_inventory_in_plan_lines_assets
select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,c.assets_code,
1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT
from stock_inventory_in_plan_heads d
 INNER JOIN stock_inventory_in_plan_lines a on d.inventory_in_plan_head_id=a.inventory_in_plan_head_id
 inner join stock_inventory_in_supplies b on a.inventory_in_plan_line_id=b.inventory_in_plan_line_id
 inner join stock_inventory_in_supplies_assets c on b.in_supplies_id=c.in_supplies_id
 where d.inventory_in_plan_type='8'；

 insert into stock_inventory_in_plan_lines_sns
  select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,c.SERIAL_NUMBER,
  1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT
  from stock_inventory_in_plan_heads d
   INNER JOIN stock_inventory_in_plan_lines a on d.inventory_in_plan_head_id=a.inventory_in_plan_head_id
   inner join stock_inventory_in_supplies b on a.inventory_in_plan_line_id=b.inventory_in_plan_line_id
   inner join stock_inventory_in_supplies_sns c on b.in_supplies_id=c.in_supplies_id
   where d.inventory_in_plan_type='8'；

 insert into stock_inventory_in_plan_lines_assets
 select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
 a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.assets_code,
 0,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
  INNER JOIN stock_delivery b on a.delivery_no=b.delivery_no
   inner join stock_delivery_detail f on f.delivery_id=b.delivery_id
  inner join stock_delivery_detail_assets c on f.delivery_detail_id=c.delivery_detail_id
  where a.inventory_in_plan_type='6'

 insert into stock_inventory_in_plan_lines_assets
 select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
 a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.assets_code,
 0,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
  INNER JOIN stock_delivery_history b on a.delivery_no=b.delivery_no
   inner join stock_delivery_detail_history f on f.delivery_id=b.delivery_id
  inner join stock_delivery_detail_assets c on f.delivery_detail_id=c.delivery_detail_id
  where a.inventory_in_plan_type='6'；

 insert into stock_inventory_in_plan_lines_sns
 select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
 a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.SERIAL_NUMBER,
 1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
  INNER JOIN stock_delivery b on a.delivery_no=b.delivery_no
   inner join stock_delivery_detail f on f.delivery_id=b.delivery_id
  inner join stock_delivery_detail_sns c on f.delivery_detail_id=c.delivery_detail_id
  where a.inventory_in_plan_type='6'；

 insert into stock_inventory_in_plan_lines_sns
 select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
 a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.SERIAL_NUMBER,
 1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
  INNER JOIN stock_delivery_history b on a.delivery_no=b.delivery_no
   inner join stock_delivery_detail_history f on f.delivery_id=b.delivery_id
  inner join stock_delivery_detail_sns c on f.delivery_detail_id=c.delivery_detail_id
  where a.inventory_in_plan_type='6'；

 update stock_inventory_in_plan_lines_assets a set a.in_stock_status=1 where
  exists (select 1 from stock_inventory_in_supplies b
          inner join stock_inventory_in_supplies_assets c
      on b.in_supplies_id=c.in_supplies_id where a.inventory_in_plan_line_id=b.inventory_in_plan_line_id
      and a.assets_code=c.assets_code)
  and exists (select 1 from stock_inventory_in_plan_heads d where  a.inventory_in_plan_head_id=d.inventory_in_plan_head_id
           and d.inventory_in_plan_type='6')；

 insert into stock_inventory_in_plan_lines_assets
 select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,a.assets_code,
 (case a.status when 1 then 0 else 1 end),1,0,a.CREATED_BY,a.CREATED_AT,a.UPDATED_BY,a.UPDATED_AT from stock_inventory_in_plan_heads d
  INNER JOIN stock_inventory_in_plan_lines a on a.inventory_in_plan_head_id=d.inventory_in_plan_head_id
  where d.inventory_in_plan_type='7'；

 insert into stock_inventory_in_plan_lines_sns
 select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,c.sn_code,
 (case a.status when 1 then 0 else 1 end),1,0,a.CREATED_BY,a.CREATED_AT,a.UPDATED_BY,a.UPDATED_AT from stock_inventory_in_plan_heads d
  INNER JOIN stock_inventory_in_plan_lines a on a.inventory_in_plan_head_id=d.inventory_in_plan_head_id
  inner join stock_assets c on a.assets_code=c.assets_code
  where d.inventory_in_plan_type='7'；

 --物料添加费用项
 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`,
 `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`,
 `default_warehouse_code`, `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04010100016', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；

 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`,
 `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`,
 `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04010100017', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；

 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`,
 `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`,
 `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04020100017', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；

 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
  `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`,
  `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`,
  `default_warehouse_code`, `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04020100018', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；

 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`,
 `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`,
 `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`,
 `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04010100016', 6, 1, -1, -1, 1, -1, '', 'EXTP1158', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,
 `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`,
 `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`,
 `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`,
 `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04010100017', 6, 1, -1, -1, 1, -1, '', 'EXTP1158', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`,
 `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`,
 `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`,
 `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04020100017', 6, 1, -1, -1, 1, -1, '', 'EXTP1158', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`,
 `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`,
 `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`,
 `service_factory`, `after_sale_factory`, `base_id`)
  VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04020100018', 6, 1, -1, -1, 1, -1, '', 'EXTP1158', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

  INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
  VALUES ('0', '2020-07-07 15:37:32', '0', '2020-07-07 15:37:32', 0, 'K04010100016', 6)；

  INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
  VALUES ('0', '2020-07-07 15:37:32', '0', '2020-07-07 15:37:32', 0, 'K04010100017', 6)；

  INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
  VALUES ('0', '2020-07-07 15:37:32', '0', '2020-07-07 15:37:32', 0, 'K04020100017', 6)；

  INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
  VALUES ('0', '2020-07-07 15:37:32', '0', '2020-07-07 15:37:32', 0, 'K04020100018', 6)；

 -- 金融GPS类型仓库添加物料类别配置
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK015', 'S010102', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK016', 'S010102', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK017', 'S010102', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK001', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK002', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK003', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK004', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK005', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK006', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK007', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK008', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK009', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK010', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK011', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK012', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK013', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；
 INSERT INTO `stock`.`stock_warehouse_supplies_category`(`warehouse_code`, `supplies_cat_code`, `status`, `CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`)
 VALUES ('MDCK014', 'S010104', 1, '10122730', '2020-08-07 13:18:35', '10122730', '2020-08-07 13:18:35')；

 -- 资产处置类型前后端枚举配置不一致导致的问题数据
 update stock_delivery_plan_heads set business_type=10 where delivery_plan_head_id='86196'；

 --物料S01050100142添加费用项
 INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`,
 `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
  `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`,
  `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
 VALUES ('0', '2020-08-05 15:37:32', '0', '2020-08-05 15:37:32', 0, 'S01050100142', 2, 1, -1, -1, 1, -1, '', 'EXTP1155', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

 INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-08-05 15:37:32', '0', '2020-08-05 15:37:32', 0, 'S01050100142', 2)；

-- 库存问题修正
delete from stock_supplies_config_detail where config_detail_id='763504'；
update stock_supplies_config set all_number=all_number-1 where supplies_config_id='53698'；
delete from stock_supplies_config_detail where config_detail_id='763430'；
update stock_supplies_config set all_number=all_number-1 where supplies_config_id='65076'；
delete from stock_supplies_config_detail where config_detail_id='763073'；
update stock_supplies_config set all_number=all_number-1 where supplies_config_id='65058'；
delete from stock_supplies_config_detail where config_detail_id='763104'；
update stock_supplies_config set all_number=all_number-1 where supplies_config_id='65087'；

--物料K04010100015添加费用项
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`,
`IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`,
`is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`,
 `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`,
 `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`)
VALUES ('0', '2020-07-23 16:50:32', '0', '2020-07-23 16:50:32', 0, 'K04010100015', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0)；

INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`)
 VALUES ('0', '2020-07-23 16:50:32', '0', '2020-07-23 16:50:32', 0, 'K04010100015', 11)；

 update stock_supplies_purchase set default_cost_item_code='EXTP1062' where supplies_code in ('K04010100010',
 'K04010100011',
 'K04020100011',
 'K04020100012',
 'K04020100013',
 'K04010100012',
 'K04010100013',
 'K04010100014',
 'K04020100014',
 'K04020100015',
 'K04020100016')；

 -- 新增物料添加费用项配置
 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`,
 `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`,
 `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`,
 `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
 VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04010100016', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；

 INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`,
 `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`,
 `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`,
  `default_warehouse_code`, `default_asset_category`, `base_id`)
  VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04010100017', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；

  INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`,
  `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`,
  `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`,
   `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`)
  VALUES ('0', '2020-08-07 15:37:32', '0', '2020-08-07 15:37:32', 0, 'K04020100018', 6, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '京东卡', 0)；


--修正资产1002011824084的使用状态
update stock_assets set conditions=1 where assets_code='1002011824084'；
INSERT INTO `stock_assets_operation_log` (`operation_type`,`assets_id`,`assets_code`,`assets_name`,`supplies_code`,`warehouse_code`,`sn_code`,
`device_code`,`brand`,`model`,`unit`,`category`,`assets_deploy`,`has_sub`,`company_code`,`company_name`,`status`,`conditions`,`net_value`,`initial_value`,
`scrap_value`,`purchase_type`,`purchase_no`,`depreciation_way`,`cost_dept`,`need_dept`,`holder`,`holder_address`,`holder_time`,`purchase_time`,`storage_time`,
`plan_handle_time`,`use_year_limit`,`regular_maintain`,`maintain_cycle`,`assets_keeper`,`assets_pic`,`saas_assets_code`,`saas_address`,`remark`,`label_url`,
`created_by`,`created_at`,`updated_by`,`updated_at`)
VALUES ('1','25783','1002011824084','手机;小米;MAX3','S01050100039','GDZC140','865847040530436',null,'小米','MAX3',null,'手机',null,'0','-','','1','1',
null,null,null,'1','IN2020040100045',null,'102988',null,'','','2020-07-27 12:07:15',null,'2020-04-01 18:18:24',null,null,'0',null,'10015718',
null,null,null,'','','10122730','2020-08-26 11:10:04','10122730','2020-08-26 11:10:04')；

-- 添加物料费用项
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04010100011', 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '油卡', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04020100013', 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '油卡', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04010100018', 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '油卡', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04020100020', 9, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '油卡', 0);

INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04010100011', 9, 1, -1, -1, 1, -1, '', 'EXTP1148', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04020100013', 9, 1, -1, -1, 1, -1, '', 'EXTP1148', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04010100018', 9, 1, -1, -1, 1, -1, '', 'EXTP1148', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04020100020', 9, 1, -1, -1, 1, -1, '', 'EXTP1148', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);

INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04010100011', 9);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04020100013', 9);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04010100018', 9);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-09 14:37:32', '0', '2020-09-09 14:37:32', 0, 'K04020100020', 9);


-- 修正物料属性
delete from stock_supplies_business_base_info where supplies_code='K04020100003' and warehouse_type_code=2；
delete from stock_supplies_inventory where supplies_code='K04020100003' and warehouse_type_code=2；
delete from stock_supplies_purchase where supplies_code='K04020100003' and warehouse_type_code=2；
update stock_supplies_purchase set default_cost_item_code='EXTP1092' where supplies_code='K04020100003' and warehouse_type_code=9；
update stock_supplies set model_code='100元',remark='油卡；无；100元' where code='K04010100009'；
update stock_supplies set model_code='200元',remark='油卡；无；200元' where code='K04010100010'；
update stock_supplies set model_code='500元',remark='油卡；无；500元' where code='K04010100011'；
update stock_supplies set model_code='1000元',remark='油卡；无；1000元' where code='K04010100015'；
update stock_supplies set model_code='100元',remark='油卡；无；100元' where code='K04020100011'；
update stock_supplies set model_code='200元',remark='油卡；无；200元' where code='K04020100012'；
update stock_supplies set model_code='500元',remark='油卡；无；500元' where code='K04020100013'；

-- 物料添加尾表属性 20200911
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'S01030100365', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '空气净化器', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02020200013', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '山地自行车', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100321', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '家用净水器', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100322', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '吸尘器', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100323', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '行李箱', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02020200014', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '手机支架', 0);
INSERT INTO `stock`.`stock_supplies_inventory`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_inventory_manage`, `is_version_control`, `is_batch_control`, `is_sn_no_control`, `is_batch_expired_control`, `is_sn_no_expired_control`, `is_batch_status_control`, `is_sn_no_status_control`, `is_restrict_warehouse`, `is_allow_split_batch`, `is_allow_merged_batch`, `default_warehouse_code`, `default_asset_category`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02030100045', 11, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, '', '抱枕', 0);


INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'S01030100365', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02020200013', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100321', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100322', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100323', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02020200014', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);
INSERT INTO `stock`.`stock_supplies_purchase`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`, `supplies_code`, `warehouse_type_code`, `is_allow_purchase`, `is_user_approved_supplier`, `is_request_ask_quote`, `is_merge_parity`, `is_request_bidding`, `default_purchase_user`, `default_cost_item_code`, `default_purchase_price`, `is_request_check`, `default_receive_quantity_tolerance`, `is_allow_replace_receive`, `is_allow_not_ordered_receive`, `default_receive_way`, `default_receive_warehouse_code`, `produce_factory`, `service_factory`, `after_sale_factory`, `base_id`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02030100045', 11, 1, -1, -1, 1, -1, '', 'EXTP1062', 0.0000, -1, -1, -1, -1, '', '', '', '', '', 0);


INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'S01030100365', 11);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02020200013', 11);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100321', 11);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100322', 11);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'J03010100323', 11);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02020200014', 11);
INSERT INTO `stock`.`stock_supplies_business_base_info`(`CREATED_BY`, `CREATED_AT`, `UPDATED_BY`, `UPDATED_AT`, `IS_DEL_FLAG`,`supplies_code`, `warehouse_type_code`) VALUES ('0', '2020-09-11 14:37:32', '0', '2020-09-11 14:37:32', 0, 'H02030100045', 11);

update stock_supplies_inventory set default_asset_category='三合一数据线' where supplies_code='H02010300001' and warehouse_type_code=11


-- 工单DCDA20201012000017处理
INSERT INTO `stock_assets_operation_log` (`operation_type`,`assets_id`,`assets_code`,`assets_name`,`supplies_code`,`warehouse_code`,`sn_code`,`device_code`,`brand`,`model`,`unit`,`category`,`assets_deploy`,`has_sub`,`company_code`,`company_name`,`status`,`conditions`,`net_value`,`initial_value`,`scrap_value`,`purchase_type`,`purchase_no`,`depreciation_way`,`cost_dept`,`need_dept`,`holder`,`holder_address`,`holder_time`,`purchase_time`,`storage_time`,`plan_handle_time`,`plan_return_time`,`use_year_limit`,`regular_maintain`,`maintain_cycle`,`assets_keeper`,`assets_pic`,`saas_assets_code`,`saas_address`,`remark`,`label_url`,`created_by`,`created_at`,`updated_by`,`updated_at`) VALUES
('1','3277','1002010502762','笔记本;256G','S01030100052','GDZC001','C02TW4HXHV27',null,'苹果','Mac;256G',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,null,null,null,'','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,null,'0',null,'10073345',null,null,null,'笔记本；苹果；Mac;256G','','10056112','2020-02-24 21:00:29','10122730','2020-10-13 14:00:29'),
('1','222876','1002010510742','笔记本;256G','S01030100509','GDZC001','SFVFYC0DPHV22',null,'苹果','Mac Pro 13.3 MPXQ2CH/A;256G',null,'笔记本',null,'0','-','','1','0',null,null,null,'1','IN2020040104158',null,null,null,null,'','2020-04-01 19:19:59',null,'2020-04-01 19:19:57',null,null,null,'0',null,'10024759',null,null,null,'笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G','','10056112','2020-04-01 19:20:05','10122730','2020-10-13 14:00:29'),
('1','225958','1002010509479','笔记本;256G','S01030100509','GDZC001','SFVFXM2ERHV22',null,'苹果','Mac Pro 13.3 MPXQ2CH/A;256G',null,'笔记本',null,'0','-','','1','0',null,null,null,'1','IN2020040104158',null,null,null,null,'','2020-04-01 19:20:00',null,'2020-04-01 19:19:57',null,null,null,'0',null,'10024759',null,null,null,'笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G','','10056112','2020-04-01 19:20:06','10122730','2020-04-01 19:20:06'),
('1','232865','1002010504015','笔记本;256G','S01030100524','GDZC001','C02VX9EBHV22',null,'苹果','MacBook Pro;256G',null,'笔记本',null,'0','-','','1','0',null,null,null,'1','IN2020040104158',null,null,null,null,'','2020-04-01 19:20:02',null,'2020-04-01 19:19:57',null,null,null,'0',null,'10024759',null,null,null,'笔记本;苹果;MacBook Pro;256G','','10056112','2020-04-01 19:20:08','10122730','2020-10-13 14:00:29'),
('1','233201','1002010502793','笔记本;256G','S01030100524','GDZC001','C02V9YYEHV22',null,'苹果','MacBook Pro;256G',null,'笔记本',null,'0','-','','1','0',null,null,null,'1','IN2020040104158',null,null,null,null,'','2020-04-01 19:20:02',null,'2020-04-01 19:19:57',null,null,null,'0',null,'10024759',null,null,null,'笔记本;苹果;MacBook Pro;256G','','10056112','2020-04-01 19:20:08','10122730','2020-10-13 14:00:29'),
('1','2455','1002010505644','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','SFVFWC2HNHV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10028602',null,null,null,'期初数据','','10056112','2020-06-29 15:34:49','10122730','2020-10-13 14:00:29'),
('1','2380','1002010505674','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','FVFWC4HZHV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10056112','2020-07-13 14:27:03','10122730','2020-10-13 14:00:29'),
('1','3368','1002010502862','笔记本；苹果；Pro13  MPXQ2CH/A;256G','S01030100059','GDZC001','SC02VC0JFHV22',null,'苹果','Pro13  MPXQ2CH/A;256G',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-05-14 11:59:50',null,'2020-02-24 21:00:25',null,null,'0','0',null,'10006820',null,null,null,'屏幕外壳左上角和右下角有轻微磕碰变形','','10056112','2020-07-13 18:36:36','10122730','2020-10-13 14:00:29'),
('1','2356','1002010505683','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','SFVFW3EZPHV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-07-13 10:36:33',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10056112','2020-07-14 10:23:26','10122730','2020-10-13 14:00:29'),
('1','22810','1002010506252','笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G','S01030100509','GDZC001','SFVFWD4UAHV22',null,'苹果','Mac Pro 13.3 MPXQ2CH/A;256G',null,'笔记本',null,'0','-','','1','0',null,null,null,'1','IN2020040100013',null,'100065',null,'','','2020-04-01 18:16:46',null,'2020-04-01 18:16:46',null,null,null,'0',null,'10006820',null,null,null,'','','10056112','2020-07-17 17:26:45','10122730','2020-10-13 14:00:29'),
('1','231698','1002010505587','笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G','S01030100509','GDZC001','SFVFWD039HV22',null,'苹果','Mac Pro 13.3 MPXQ2CH/A;256G',null,'笔记本',null,'0','-','','1','0',null,null,null,'1','IN2020040104158',null,'100065',null,'','','2020-04-01 19:20:01',null,'2020-04-01 19:19:57',null,null,null,'0',null,'10006820',null,null,null,'','','10056112','2020-07-20 11:28:23','10122730','2020-10-13 14:00:29'),
('1','2946','1002010506352','笔记本；苹果；256SSD；8G；I5','S01030100046','GDZC001','SFVFWG8YJHV22',null,'苹果','256SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10029027',null,null,null,'期初数据','','10056112','2020-07-22 17:40:21','10122730','2020-10-13 14:00:29'),
('1','2232','1002010505673','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','SFVFWC4CVHV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10122730','2020-08-20 20:00:00','10122730','2020-10-13 14:00:29'),
('1','2530','1002010502812','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','C02V9YYXHV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10029027',null,null,null,'期初数据','','10122730','2020-08-20 20:00:00','10122730','2020-10-13 14:00:29'),
('1','2571','1002010502727','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','C02V79EBHV27',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-05-15 17:19:28',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10122730','2020-08-20 20:00:00','10122730','2020-10-13 14:00:29'),
('1','2846','1002010506399','笔记本；苹果；256SSD；8G；I5','S01030100046','GDZC001','SFVFWG8PPHV22',null,'苹果','256SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-07-01 17:23:58',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10122730','2020-08-20 20:00:00','10122730','2020-10-13 14:00:29'),
('1','2325','1002010505699','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','SFVFWC2LVHV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-03-11 12:21:29',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10056112','2020-08-21 15:30:14','10122730','2020-10-13 14:00:29'),
('1','2291','1002010504021','笔记本；苹果；256G SSD；8G；I5','S01030100044','GDZC001','SC02VX9M6HV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','1','0',null,null,null,'1','IN2020022400001',null,'100065',null,'','','2020-06-08 14:06:56',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10056112','2020-09-27 16:37:27','10122730','2020-10-13 14:00:29'),
('1','231700','1002010505585','笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G','S01030100509','','SFVFWD1TRHV22',null,'苹果','Mac Pro 13.3 MPXQ2CH/A;256G',null,'笔记本',null,'0','-','','2','0',null,null,null,'1','IN2020040104158',null,'102947',null,'10104614','B07-2','2020-10-12 15:55:37',null,'2020-04-01 19:19:57',null,null,null,'0',null,'10006820',null,null,null,'','','10056112','2020-10-12 15:55:37','10122730','2020-10-13 14:00:29'),
('1','2333','1002010503904','笔记本；苹果；256G SSD；8G；I5','S01030100044','','SC02VVED8HV22',null,'苹果','256G SSD；8G；I5',null,'笔记本',null,'0','-','金瓜子科技发展（北京）','2','0',null,null,null,'1','IN2020022400001',null,'105066',null,'10106937','B03-2','2020-10-13 11:34:39',null,'2020-02-24 21:00:25',null,null,'3','0',null,'10006820',null,null,null,'期初数据','','10056112','2020-10-13 11:34:39','10122730','2020-10-13 14:00:29');

update stock_assets set assets_name='笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G', model='Mac Pro 13.3 MPXQ2CH/A;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010510742';
update stock_assets set assets_name='笔记本;苹果;MacBook Pro;256G', model='MacBook Pro;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010504015';
update stock_assets set assets_name='笔记本；苹果；Mac;256G', model='Mac;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010502762';
update stock_assets set assets_name='笔记本;苹果;MacBook Pro;256G', model='MacBook Pro;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010502793';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010502727';
update stock_assets set assets_name='笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G', model='Mac Pro 13.3 MPXQ2CH/A;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505587';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505674';
update stock_assets set assets_name='笔记本；苹果；Pro13 MPXQ2CH/A;256G', model='Pro13 MPXQ2CH/A;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010502862';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5	', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505644';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505683';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010503904';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5	', model='256G SSD；8G；I5	',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505673';
update stock_assets set assets_name='笔记本；苹果；256SSD；8G；I5', model='256SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010506352';
update stock_assets set assets_name='笔记本；苹果；256SSD；8G；I5', model='256SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010506399';
update stock_assets set assets_name='笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G', model='Mac Pro 13.3 MPXQ2CH/A;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010506252';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505699';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5	', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010502812';
update stock_assets set assets_name='笔记本；苹果；256G SSD；8G；I5	', model='256G SSD；8G；I5',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010504021';
update stock_assets set assets_name='笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G', model='Mac Pro 13.3 MPXQ2CH/A;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010505585';
update stock_assets set assets_name='笔记本;苹果;Mac Pro 13.3 MPXQ2CH/A;256G', model='Mac Pro 13.3 MPXQ2CH/A;256G',updated_by='10122730',updated_at='2020-10-13 13:55:59' where assets_code='1002010509479';