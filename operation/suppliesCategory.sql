-- 物料小类增加配置
alter table stock_supplies_category add COLUMN  `vendor_type` tinyint(4) DEFAULT 0 COMMENT '0否 1是'  AFTER `all_name`;
alter table stock_supplies_category add COLUMN  `bussiness_line` json DEFAULT NULL COMMENT '业务线标识'  AFTER `all_name`;
-- 初始化数据
update stock_supplies_category set vendor_type=1,bussiness_line='[{\"businessCode\":\"B004\",\"businessName\":\"通用\"}]' where supplies_category_id in (3,5,8,9,22,23,24,25,26,27,28,29,30,31,33,34,35,36,38,39,41,42,44,45,47,49,51,53,55,57,59,61,63,65,67,70);
update stock_supplies_category set vendor_type=1,bussiness_line='[{\"businessCode\":\"B001\",\"businessName\":\"二手车\"}]' where supplies_category_id in (74,81,84);
update stock_supplies_category set vendor_type=1,bussiness_line='[{\"businessCode\":\"B002\",\"businessName\":\"新车\"}]' where supplies_category_id =75;
update stock_supplies_category set vendor_type=0 where supplies_category_id =78;