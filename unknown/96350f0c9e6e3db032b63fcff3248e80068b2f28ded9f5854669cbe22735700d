<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.mapper.base.StockSuppliesPurchaseMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockSuppliesPurchase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="IS_DEL_FLAG" jdbcType="INTEGER" property="isDelFlag" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="warehouse_type_code" jdbcType="INTEGER" property="warehouseTypeCode" />
    <result column="is_allow_purchase" jdbcType="INTEGER" property="isAllowPurchase" />
    <result column="inventory_manage_flag" jdbcType="INTEGER" property="inventoryManageFlag" />
    <result column="is_user_approved_supplier" jdbcType="INTEGER" property="isUserApprovedSupplier" />
    <result column="is_request_ask_quote" jdbcType="INTEGER" property="isRequestAskQuote" />
    <result column="is_merge_parity" jdbcType="INTEGER" property="isMergeParity" />
    <result column="is_request_bidding" jdbcType="INTEGER" property="isRequestBidding" />
    <result column="default_purchase_user" jdbcType="VARCHAR" property="defaultPurchaseUser" />
    <result column="default_cost_item_code" jdbcType="VARCHAR" property="defaultCostItemCode" />
    <result column="default_purchase_price" jdbcType="DECIMAL" property="defaultPurchasePrice" />
    <result column="is_request_check" jdbcType="INTEGER" property="isRequestCheck" />
    <result column="default_receive_quantity_tolerance" jdbcType="BIGINT" property="defaultReceiveQuantityTolerance" />
    <result column="is_allow_replace_receive" jdbcType="INTEGER" property="isAllowReplaceReceive" />
    <result column="is_allow_not_ordered_receive" jdbcType="INTEGER" property="isAllowNotOrderedReceive" />
    <result column="default_receive_way" jdbcType="VARCHAR" property="defaultReceiveWay" />
    <result column="default_receive_warehouse_code" jdbcType="VARCHAR" property="defaultReceiveWarehouseCode" />
    <result column="produce_factory" jdbcType="VARCHAR" property="produceFactory" />
    <result column="service_factory" jdbcType="VARCHAR" property="serviceFactory" />
    <result column="after_sale_factory" jdbcType="VARCHAR" property="afterSaleFactory" />
    <result column="receive_type" jdbcType="INTEGER" property="receiveType" />
    <result column="base_id" jdbcType="BIGINT" property="baseId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT, IS_DEL_FLAG, supplies_code, warehouse_type_code, 
    is_allow_purchase, inventory_manage_flag, is_user_approved_supplier, is_request_ask_quote, 
    is_merge_parity, is_request_bidding, default_purchase_user, default_cost_item_code, 
    default_purchase_price, is_request_check, default_receive_quantity_tolerance, is_allow_replace_receive, 
    is_allow_not_ordered_receive, default_receive_way, default_receive_warehouse_code, 
    produce_factory, service_factory, after_sale_factory, receive_type, base_id
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_supplies_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_supplies_purchase
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_supplies_purchase
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchase">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_supplies_purchase (CREATED_BY, CREATED_AT, UPDATED_BY, 
      UPDATED_AT, IS_DEL_FLAG, supplies_code, 
      warehouse_type_code, is_allow_purchase, inventory_manage_flag, 
      is_user_approved_supplier, is_request_ask_quote, 
      is_merge_parity, is_request_bidding, default_purchase_user, 
      default_cost_item_code, default_purchase_price, 
      is_request_check, default_receive_quantity_tolerance, 
      is_allow_replace_receive, is_allow_not_ordered_receive, 
      default_receive_way, default_receive_warehouse_code, 
      produce_factory, service_factory, after_sale_factory, 
      receive_type, base_id)
    values (#{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{isDelFlag,jdbcType=INTEGER}, #{suppliesCode,jdbcType=VARCHAR}, 
      #{warehouseTypeCode,jdbcType=INTEGER}, #{isAllowPurchase,jdbcType=INTEGER}, #{inventoryManageFlag,jdbcType=INTEGER}, 
      #{isUserApprovedSupplier,jdbcType=INTEGER}, #{isRequestAskQuote,jdbcType=INTEGER}, 
      #{isMergeParity,jdbcType=INTEGER}, #{isRequestBidding,jdbcType=INTEGER}, #{defaultPurchaseUser,jdbcType=VARCHAR}, 
      #{defaultCostItemCode,jdbcType=VARCHAR}, #{defaultPurchasePrice,jdbcType=DECIMAL}, 
      #{isRequestCheck,jdbcType=INTEGER}, #{defaultReceiveQuantityTolerance,jdbcType=BIGINT}, 
      #{isAllowReplaceReceive,jdbcType=INTEGER}, #{isAllowNotOrderedReceive,jdbcType=INTEGER}, 
      #{defaultReceiveWay,jdbcType=VARCHAR}, #{defaultReceiveWarehouseCode,jdbcType=VARCHAR}, 
      #{produceFactory,jdbcType=VARCHAR}, #{serviceFactory,jdbcType=VARCHAR}, #{afterSaleFactory,jdbcType=VARCHAR}, 
      #{receiveType,jdbcType=INTEGER}, #{baseId,jdbcType=BIGINT})
  </insert>
<!--  批量插入数据  -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into stock_supplies_purchase (CREATED_BY, CREATED_AT, UPDATED_BY,
    UPDATED_AT, IS_DEL_FLAG, supplies_code,
    warehouse_type_code, is_allow_purchase, inventory_manage_flag,
    is_user_approved_supplier, is_request_ask_quote,
    is_merge_parity, is_request_bidding, default_purchase_user,
    default_cost_item_code, default_purchase_price,
    is_request_check, default_receive_quantity_tolerance,
    is_allow_replace_receive, is_allow_not_ordered_receive,
    default_receive_way, default_receive_warehouse_code,
    produce_factory, service_factory, after_sale_factory,
    receive_type, base_id)
    values
    <foreach collection="stockSuppliesPurchaseList" item="item" separator=",">
      (#{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
      #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.isDelFlag,jdbcType=INTEGER}, #{item.suppliesCode,jdbcType=VARCHAR},
      #{item.warehouseTypeCode,jdbcType=INTEGER}, #{item.isAllowPurchase,jdbcType=INTEGER}, #{item.inventoryManageFlag,jdbcType=INTEGER},
      #{item.isUserApprovedSupplier,jdbcType=INTEGER}, #{item.isRequestAskQuote,jdbcType=INTEGER},
      #{item.isMergeParity,jdbcType=INTEGER}, #{item.isRequestBidding,jdbcType=INTEGER}, #{item.defaultPurchaseUser,jdbcType=VARCHAR},
      #{item.defaultCostItemCode,jdbcType=VARCHAR}, #{item.defaultPurchasePrice,jdbcType=DECIMAL},
      #{item.isRequestCheck,jdbcType=INTEGER}, #{item.defaultReceiveQuantityTolerance,jdbcType=BIGINT},
      #{item.isAllowReplaceReceive,jdbcType=INTEGER}, #{item.isAllowNotOrderedReceive,jdbcType=INTEGER},
      #{item.defaultReceiveWay,jdbcType=VARCHAR}, #{item.defaultReceiveWarehouseCode,jdbcType=VARCHAR},
      #{item.produceFactory,jdbcType=VARCHAR}, #{item.serviceFactory,jdbcType=VARCHAR}, #{item.afterSaleFactory,jdbcType=VARCHAR},
      #{item.receiveType,jdbcType=INTEGER}, #{item.baseId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchase">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_supplies_purchase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
      <if test="isDelFlag != null">
        IS_DEL_FLAG,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="warehouseTypeCode != null">
        warehouse_type_code,
      </if>
      <if test="isAllowPurchase != null">
        is_allow_purchase,
      </if>
      <if test="inventoryManageFlag != null">
        inventory_manage_flag,
      </if>
      <if test="isUserApprovedSupplier != null">
        is_user_approved_supplier,
      </if>
      <if test="isRequestAskQuote != null">
        is_request_ask_quote,
      </if>
      <if test="isMergeParity != null">
        is_merge_parity,
      </if>
      <if test="isRequestBidding != null">
        is_request_bidding,
      </if>
      <if test="defaultPurchaseUser != null">
        default_purchase_user,
      </if>
      <if test="defaultCostItemCode != null">
        default_cost_item_code,
      </if>
      <if test="defaultPurchasePrice != null">
        default_purchase_price,
      </if>
      <if test="isRequestCheck != null">
        is_request_check,
      </if>
      <if test="defaultReceiveQuantityTolerance != null">
        default_receive_quantity_tolerance,
      </if>
      <if test="isAllowReplaceReceive != null">
        is_allow_replace_receive,
      </if>
      <if test="isAllowNotOrderedReceive != null">
        is_allow_not_ordered_receive,
      </if>
      <if test="defaultReceiveWay != null">
        default_receive_way,
      </if>
      <if test="defaultReceiveWarehouseCode != null">
        default_receive_warehouse_code,
      </if>
      <if test="produceFactory != null">
        produce_factory,
      </if>
      <if test="serviceFactory != null">
        service_factory,
      </if>
      <if test="afterSaleFactory != null">
        after_sale_factory,
      </if>
      <if test="receiveType != null">
        receive_type,
      </if>
      <if test="baseId != null">
        base_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelFlag != null">
        #{isDelFlag,jdbcType=INTEGER},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseTypeCode != null">
        #{warehouseTypeCode,jdbcType=INTEGER},
      </if>
      <if test="isAllowPurchase != null">
        #{isAllowPurchase,jdbcType=INTEGER},
      </if>
      <if test="inventoryManageFlag != null">
        #{inventoryManageFlag,jdbcType=INTEGER},
      </if>
      <if test="isUserApprovedSupplier != null">
        #{isUserApprovedSupplier,jdbcType=INTEGER},
      </if>
      <if test="isRequestAskQuote != null">
        #{isRequestAskQuote,jdbcType=INTEGER},
      </if>
      <if test="isMergeParity != null">
        #{isMergeParity,jdbcType=INTEGER},
      </if>
      <if test="isRequestBidding != null">
        #{isRequestBidding,jdbcType=INTEGER},
      </if>
      <if test="defaultPurchaseUser != null">
        #{defaultPurchaseUser,jdbcType=VARCHAR},
      </if>
      <if test="defaultCostItemCode != null">
        #{defaultCostItemCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultPurchasePrice != null">
        #{defaultPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="isRequestCheck != null">
        #{isRequestCheck,jdbcType=INTEGER},
      </if>
      <if test="defaultReceiveQuantityTolerance != null">
        #{defaultReceiveQuantityTolerance,jdbcType=BIGINT},
      </if>
      <if test="isAllowReplaceReceive != null">
        #{isAllowReplaceReceive,jdbcType=INTEGER},
      </if>
      <if test="isAllowNotOrderedReceive != null">
        #{isAllowNotOrderedReceive,jdbcType=INTEGER},
      </if>
      <if test="defaultReceiveWay != null">
        #{defaultReceiveWay,jdbcType=VARCHAR},
      </if>
      <if test="defaultReceiveWarehouseCode != null">
        #{defaultReceiveWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="produceFactory != null">
        #{produceFactory,jdbcType=VARCHAR},
      </if>
      <if test="serviceFactory != null">
        #{serviceFactory,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleFactory != null">
        #{afterSaleFactory,jdbcType=VARCHAR},
      </if>
      <if test="receiveType != null">
        #{receiveType,jdbcType=INTEGER},
      </if>
      <if test="baseId != null">
        #{baseId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchaseExample" resultType="java.lang.Long">
    select count(*) from stock_supplies_purchase
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_supplies_purchase
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelFlag != null">
        IS_DEL_FLAG = #{record.isDelFlag,jdbcType=INTEGER},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseTypeCode != null">
        warehouse_type_code = #{record.warehouseTypeCode,jdbcType=INTEGER},
      </if>
      <if test="record.isAllowPurchase != null">
        is_allow_purchase = #{record.isAllowPurchase,jdbcType=INTEGER},
      </if>
      <if test="record.inventoryManageFlag != null">
        inventory_manage_flag = #{record.inventoryManageFlag,jdbcType=INTEGER},
      </if>
      <if test="record.isUserApprovedSupplier != null">
        is_user_approved_supplier = #{record.isUserApprovedSupplier,jdbcType=INTEGER},
      </if>
      <if test="record.isRequestAskQuote != null">
        is_request_ask_quote = #{record.isRequestAskQuote,jdbcType=INTEGER},
      </if>
      <if test="record.isMergeParity != null">
        is_merge_parity = #{record.isMergeParity,jdbcType=INTEGER},
      </if>
      <if test="record.isRequestBidding != null">
        is_request_bidding = #{record.isRequestBidding,jdbcType=INTEGER},
      </if>
      <if test="record.defaultPurchaseUser != null">
        default_purchase_user = #{record.defaultPurchaseUser,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultCostItemCode != null">
        default_cost_item_code = #{record.defaultCostItemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultPurchasePrice != null">
        default_purchase_price = #{record.defaultPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.isRequestCheck != null">
        is_request_check = #{record.isRequestCheck,jdbcType=INTEGER},
      </if>
      <if test="record.defaultReceiveQuantityTolerance != null">
        default_receive_quantity_tolerance = #{record.defaultReceiveQuantityTolerance,jdbcType=BIGINT},
      </if>
      <if test="record.isAllowReplaceReceive != null">
        is_allow_replace_receive = #{record.isAllowReplaceReceive,jdbcType=INTEGER},
      </if>
      <if test="record.isAllowNotOrderedReceive != null">
        is_allow_not_ordered_receive = #{record.isAllowNotOrderedReceive,jdbcType=INTEGER},
      </if>
      <if test="record.defaultReceiveWay != null">
        default_receive_way = #{record.defaultReceiveWay,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultReceiveWarehouseCode != null">
        default_receive_warehouse_code = #{record.defaultReceiveWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.produceFactory != null">
        produce_factory = #{record.produceFactory,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceFactory != null">
        service_factory = #{record.serviceFactory,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleFactory != null">
        after_sale_factory = #{record.afterSaleFactory,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveType != null">
        receive_type = #{record.receiveType,jdbcType=INTEGER},
      </if>
      <if test="record.baseId != null">
        base_id = #{record.baseId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_supplies_purchase
    set id = #{record.id,jdbcType=BIGINT},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      IS_DEL_FLAG = #{record.isDelFlag,jdbcType=INTEGER},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      warehouse_type_code = #{record.warehouseTypeCode,jdbcType=INTEGER},
      is_allow_purchase = #{record.isAllowPurchase,jdbcType=INTEGER},
      inventory_manage_flag = #{record.inventoryManageFlag,jdbcType=INTEGER},
      is_user_approved_supplier = #{record.isUserApprovedSupplier,jdbcType=INTEGER},
      is_request_ask_quote = #{record.isRequestAskQuote,jdbcType=INTEGER},
      is_merge_parity = #{record.isMergeParity,jdbcType=INTEGER},
      is_request_bidding = #{record.isRequestBidding,jdbcType=INTEGER},
      default_purchase_user = #{record.defaultPurchaseUser,jdbcType=VARCHAR},
      default_cost_item_code = #{record.defaultCostItemCode,jdbcType=VARCHAR},
      default_purchase_price = #{record.defaultPurchasePrice,jdbcType=DECIMAL},
      is_request_check = #{record.isRequestCheck,jdbcType=INTEGER},
      default_receive_quantity_tolerance = #{record.defaultReceiveQuantityTolerance,jdbcType=BIGINT},
      is_allow_replace_receive = #{record.isAllowReplaceReceive,jdbcType=INTEGER},
      is_allow_not_ordered_receive = #{record.isAllowNotOrderedReceive,jdbcType=INTEGER},
      default_receive_way = #{record.defaultReceiveWay,jdbcType=VARCHAR},
      default_receive_warehouse_code = #{record.defaultReceiveWarehouseCode,jdbcType=VARCHAR},
      produce_factory = #{record.produceFactory,jdbcType=VARCHAR},
      service_factory = #{record.serviceFactory,jdbcType=VARCHAR},
      after_sale_factory = #{record.afterSaleFactory,jdbcType=VARCHAR},
      receive_type = #{record.receiveType,jdbcType=INTEGER},
      base_id = #{record.baseId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchase">
    update stock_supplies_purchase
    <set>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelFlag != null">
        IS_DEL_FLAG = #{isDelFlag,jdbcType=INTEGER},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseTypeCode != null">
        warehouse_type_code = #{warehouseTypeCode,jdbcType=INTEGER},
      </if>
      <if test="isAllowPurchase != null">
        is_allow_purchase = #{isAllowPurchase,jdbcType=INTEGER},
      </if>
      <if test="inventoryManageFlag != null">
        inventory_manage_flag = #{inventoryManageFlag,jdbcType=INTEGER},
      </if>
      <if test="isUserApprovedSupplier != null">
        is_user_approved_supplier = #{isUserApprovedSupplier,jdbcType=INTEGER},
      </if>
      <if test="isRequestAskQuote != null">
        is_request_ask_quote = #{isRequestAskQuote,jdbcType=INTEGER},
      </if>
      <if test="isMergeParity != null">
        is_merge_parity = #{isMergeParity,jdbcType=INTEGER},
      </if>
      <if test="isRequestBidding != null">
        is_request_bidding = #{isRequestBidding,jdbcType=INTEGER},
      </if>
      <if test="defaultPurchaseUser != null">
        default_purchase_user = #{defaultPurchaseUser,jdbcType=VARCHAR},
      </if>
      <if test="defaultCostItemCode != null">
        default_cost_item_code = #{defaultCostItemCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultPurchasePrice != null">
        default_purchase_price = #{defaultPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="isRequestCheck != null">
        is_request_check = #{isRequestCheck,jdbcType=INTEGER},
      </if>
      <if test="defaultReceiveQuantityTolerance != null">
        default_receive_quantity_tolerance = #{defaultReceiveQuantityTolerance,jdbcType=BIGINT},
      </if>
      <if test="isAllowReplaceReceive != null">
        is_allow_replace_receive = #{isAllowReplaceReceive,jdbcType=INTEGER},
      </if>
      <if test="isAllowNotOrderedReceive != null">
        is_allow_not_ordered_receive = #{isAllowNotOrderedReceive,jdbcType=INTEGER},
      </if>
      <if test="defaultReceiveWay != null">
        default_receive_way = #{defaultReceiveWay,jdbcType=VARCHAR},
      </if>
      <if test="defaultReceiveWarehouseCode != null">
        default_receive_warehouse_code = #{defaultReceiveWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="produceFactory != null">
        produce_factory = #{produceFactory,jdbcType=VARCHAR},
      </if>
      <if test="serviceFactory != null">
        service_factory = #{serviceFactory,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleFactory != null">
        after_sale_factory = #{afterSaleFactory,jdbcType=VARCHAR},
      </if>
      <if test="receiveType != null">
        receive_type = #{receiveType,jdbcType=INTEGER},
      </if>
      <if test="baseId != null">
        base_id = #{baseId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!--  批量更新  -->
  <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchase">
    <foreach collection="stockSuppliesPurchaseList" item="item" separator=";">
      update stock_supplies_purchase
      <set>
        <if test="item.createdBy != null">
          CREATED_BY = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdAt != null">
          CREATED_AT = #{item.createdAt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updatedBy != null">
          UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.updatedAt != null">
          UPDATED_AT = #{item.updatedAt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.isDelFlag != null">
          IS_DEL_FLAG = #{item.isDelFlag,jdbcType=INTEGER},
        </if>
        <if test="item.suppliesCode != null">
          supplies_code = #{item.suppliesCode,jdbcType=VARCHAR},
        </if>
        <if test="item.warehouseTypeCode != null">
          warehouse_type_code = #{item.warehouseTypeCode,jdbcType=INTEGER},
        </if>
        <if test="item.isAllowPurchase != null">
          is_allow_purchase = #{item.isAllowPurchase,jdbcType=INTEGER},
        </if>
        <if test="item.inventoryManageFlag != null">
          inventory_manage_flag = #{item.inventoryManageFlag,jdbcType=INTEGER},
        </if>
        <if test="item.isUserApprovedSupplier != null">
          is_user_approved_supplier = #{item.isUserApprovedSupplier,jdbcType=INTEGER},
        </if>
        <if test="item.isRequestAskQuote != null">
          is_request_ask_quote = #{item.isRequestAskQuote,jdbcType=INTEGER},
        </if>
        <if test="item.isMergeParity != null">
          is_merge_parity = #{item.isMergeParity,jdbcType=INTEGER},
        </if>
        <if test="item.isRequestBidding != null">
          is_request_bidding = #{item.isRequestBidding,jdbcType=INTEGER},
        </if>
        <if test="item.defaultPurchaseUser != null">
          default_purchase_user = #{item.defaultPurchaseUser,jdbcType=VARCHAR},
        </if>
        <if test="item.defaultCostItemCode != null">
          default_cost_item_code = #{item.defaultCostItemCode,jdbcType=VARCHAR},
        </if>
        <if test="item.defaultPurchasePrice != null">
          default_purchase_price = #{item.defaultPurchasePrice,jdbcType=DECIMAL},
        </if>
        <if test="item.isRequestCheck != null">
          is_request_check = #{item.isRequestCheck,jdbcType=INTEGER},
        </if>
        <if test="item.defaultReceiveQuantityTolerance != null">
          default_receive_quantity_tolerance = #{item.defaultReceiveQuantityTolerance,jdbcType=BIGINT},
        </if>
        <if test="item.isAllowReplaceReceive != null">
          is_allow_replace_receive = #{item.isAllowReplaceReceive,jdbcType=INTEGER},
        </if>
        <if test="item.isAllowNotOrderedReceive != null">
          is_allow_not_ordered_receive = #{item.isAllowNotOrderedReceive,jdbcType=INTEGER},
        </if>
        <if test="item.defaultReceiveWay != null">
          default_receive_way = #{item.defaultReceiveWay,jdbcType=VARCHAR},
        </if>
        <if test="item.defaultReceiveWarehouseCode != null">
          default_receive_warehouse_code = #{item.defaultReceiveWarehouseCode,jdbcType=VARCHAR},
        </if>
        <if test="item.produceFactory != null">
          produce_factory = #{item.produceFactory,jdbcType=VARCHAR},
        </if>
        <if test="item.serviceFactory != null">
          service_factory = #{item.serviceFactory,jdbcType=VARCHAR},
        </if>
        <if test="item.afterSaleFactory != null">
          after_sale_factory = #{item.afterSaleFactory,jdbcType=VARCHAR},
        </if>
        <if test="item.receiveType != null">
          receive_type = #{item.receiveType,jdbcType=INTEGER},
        </if>
        <if test="item.baseId != null">
          base_id = #{item.baseId,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockSuppliesPurchase">
    update stock_supplies_purchase
    set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      IS_DEL_FLAG = #{isDelFlag,jdbcType=INTEGER},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      warehouse_type_code = #{warehouseTypeCode,jdbcType=INTEGER},
      is_allow_purchase = #{isAllowPurchase,jdbcType=INTEGER},
      inventory_manage_flag = #{inventoryManageFlag,jdbcType=INTEGER},
      is_user_approved_supplier = #{isUserApprovedSupplier,jdbcType=INTEGER},
      is_request_ask_quote = #{isRequestAskQuote,jdbcType=INTEGER},
      is_merge_parity = #{isMergeParity,jdbcType=INTEGER},
      is_request_bidding = #{isRequestBidding,jdbcType=INTEGER},
      default_purchase_user = #{defaultPurchaseUser,jdbcType=VARCHAR},
      default_cost_item_code = #{defaultCostItemCode,jdbcType=VARCHAR},
      default_purchase_price = #{defaultPurchasePrice,jdbcType=DECIMAL},
      is_request_check = #{isRequestCheck,jdbcType=INTEGER},
      default_receive_quantity_tolerance = #{defaultReceiveQuantityTolerance,jdbcType=BIGINT},
      is_allow_replace_receive = #{isAllowReplaceReceive,jdbcType=INTEGER},
      is_allow_not_ordered_receive = #{isAllowNotOrderedReceive,jdbcType=INTEGER},
      default_receive_way = #{defaultReceiveWay,jdbcType=VARCHAR},
      default_receive_warehouse_code = #{defaultReceiveWarehouseCode,jdbcType=VARCHAR},
      produce_factory = #{produceFactory,jdbcType=VARCHAR},
      service_factory = #{serviceFactory,jdbcType=VARCHAR},
      after_sale_factory = #{afterSaleFactory,jdbcType=VARCHAR},
      receive_type = #{receiveType,jdbcType=INTEGER},
      base_id = #{baseId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>