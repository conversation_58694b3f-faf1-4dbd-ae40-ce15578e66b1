<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsExtendPhoneMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
    <id column="assets_phone_id" jdbcType="BIGINT" property="assetsPhoneId" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="memory_size" jdbcType="VARCHAR" property="memorySize" />
    <result column="storage_spaces_size" jdbcType="VARCHAR" property="storageSpacesSize" />
    <result column="cpu_model" jdbcType="VARCHAR" property="cpuModel" />
    <result column="sys_version" jdbcType="VARCHAR" property="sysVersion" />
    <result column="screen_size" jdbcType="VARCHAR" property="screenSize" />
    <result column="phone_brand" jdbcType="VARCHAR" property="phoneBrand" />
    <result column="phone_model" jdbcType="VARCHAR" property="phoneModel" />
    <result column="phone_colour" jdbcType="VARCHAR" property="phoneColour" />
    <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    assets_phone_id, assets_code, memory_size, storage_spaces_size, cpu_model, sys_version, 
    screen_size, phone_brand, phone_model, phone_colour, data_status, del_flag, created_by, 
    created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhoneExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_extend_phone
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_extend_phone
    where assets_phone_id = #{assetsPhoneId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_extend_phone
    where assets_phone_id = #{assetsPhoneId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
    <selectKey keyProperty="assetsPhoneId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_extend_phone (assets_code, memory_size, storage_spaces_size, 
      cpu_model, sys_version, screen_size, 
      phone_brand, phone_model, phone_colour, 
      data_status, del_flag, created_by, 
      created_at, updated_by, updated_at
      )
    values (#{assetsCode,jdbcType=VARCHAR}, #{memorySize,jdbcType=VARCHAR}, #{storageSpacesSize,jdbcType=VARCHAR}, 
      #{cpuModel,jdbcType=VARCHAR}, #{sysVersion,jdbcType=VARCHAR}, #{screenSize,jdbcType=VARCHAR}, 
      #{phoneBrand,jdbcType=VARCHAR}, #{phoneModel,jdbcType=VARCHAR}, #{phoneColour,jdbcType=VARCHAR}, 
      #{dataStatus,jdbcType=TINYINT}, #{delFlag,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
    <selectKey keyProperty="assetsPhoneId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_extend_phone
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="memorySize != null">
        memory_size,
      </if>
      <if test="storageSpacesSize != null">
        storage_spaces_size,
      </if>
      <if test="cpuModel != null">
        cpu_model,
      </if>
      <if test="sysVersion != null">
        sys_version,
      </if>
      <if test="screenSize != null">
        screen_size,
      </if>
      <if test="phoneBrand != null">
        phone_brand,
      </if>
      <if test="phoneModel != null">
        phone_model,
      </if>
      <if test="phoneColour != null">
        phone_colour,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="memorySize != null">
        #{memorySize,jdbcType=VARCHAR},
      </if>
      <if test="storageSpacesSize != null">
        #{storageSpacesSize,jdbcType=VARCHAR},
      </if>
      <if test="cpuModel != null">
        #{cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="sysVersion != null">
        #{sysVersion,jdbcType=VARCHAR},
      </if>
      <if test="screenSize != null">
        #{screenSize,jdbcType=VARCHAR},
      </if>
      <if test="phoneBrand != null">
        #{phoneBrand,jdbcType=VARCHAR},
      </if>
      <if test="phoneModel != null">
        #{phoneModel,jdbcType=VARCHAR},
      </if>
      <if test="phoneColour != null">
        #{phoneColour,jdbcType=VARCHAR},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhoneExample" resultType="java.lang.Long">
    select count(*) from stock_assets_extend_phone
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_extend_phone
    <set>
      <if test="record.assetsPhoneId != null">
        assets_phone_id = #{record.assetsPhoneId,jdbcType=BIGINT},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.memorySize != null">
        memory_size = #{record.memorySize,jdbcType=VARCHAR},
      </if>
      <if test="record.storageSpacesSize != null">
        storage_spaces_size = #{record.storageSpacesSize,jdbcType=VARCHAR},
      </if>
      <if test="record.cpuModel != null">
        cpu_model = #{record.cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="record.sysVersion != null">
        sys_version = #{record.sysVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.screenSize != null">
        screen_size = #{record.screenSize,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneBrand != null">
        phone_brand = #{record.phoneBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneModel != null">
        phone_model = #{record.phoneModel,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneColour != null">
        phone_colour = #{record.phoneColour,jdbcType=VARCHAR},
      </if>
      <if test="record.dataStatus != null">
        data_status = #{record.dataStatus,jdbcType=TINYINT},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_extend_phone
    set assets_phone_id = #{record.assetsPhoneId,jdbcType=BIGINT},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      memory_size = #{record.memorySize,jdbcType=VARCHAR},
      storage_spaces_size = #{record.storageSpacesSize,jdbcType=VARCHAR},
      cpu_model = #{record.cpuModel,jdbcType=VARCHAR},
      sys_version = #{record.sysVersion,jdbcType=VARCHAR},
      screen_size = #{record.screenSize,jdbcType=VARCHAR},
      phone_brand = #{record.phoneBrand,jdbcType=VARCHAR},
      phone_model = #{record.phoneModel,jdbcType=VARCHAR},
      phone_colour = #{record.phoneColour,jdbcType=VARCHAR},
      data_status = #{record.dataStatus,jdbcType=TINYINT},
      del_flag = #{record.delFlag,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
    update stock_assets_extend_phone
    <set>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="memorySize != null">
        memory_size = #{memorySize,jdbcType=VARCHAR},
      </if>
      <if test="storageSpacesSize != null">
        storage_spaces_size = #{storageSpacesSize,jdbcType=VARCHAR},
      </if>
      <if test="cpuModel != null">
        cpu_model = #{cpuModel,jdbcType=VARCHAR},
      </if>
      <if test="sysVersion != null">
        sys_version = #{sysVersion,jdbcType=VARCHAR},
      </if>
      <if test="screenSize != null">
        screen_size = #{screenSize,jdbcType=VARCHAR},
      </if>
      <if test="phoneBrand != null">
        phone_brand = #{phoneBrand,jdbcType=VARCHAR},
      </if>
      <if test="phoneModel != null">
        phone_model = #{phoneModel,jdbcType=VARCHAR},
      </if>
      <if test="phoneColour != null">
        phone_colour = #{phoneColour,jdbcType=VARCHAR},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where assets_phone_id = #{assetsPhoneId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
    update stock_assets_extend_phone
    set assets_code = #{assetsCode,jdbcType=VARCHAR},
      memory_size = #{memorySize,jdbcType=VARCHAR},
      storage_spaces_size = #{storageSpacesSize,jdbcType=VARCHAR},
      cpu_model = #{cpuModel,jdbcType=VARCHAR},
      sys_version = #{sysVersion,jdbcType=VARCHAR},
      screen_size = #{screenSize,jdbcType=VARCHAR},
      phone_brand = #{phoneBrand,jdbcType=VARCHAR},
      phone_model = #{phoneModel,jdbcType=VARCHAR},
      phone_colour = #{phoneColour,jdbcType=VARCHAR},
      data_status = #{dataStatus,jdbcType=TINYINT},
      del_flag = #{delFlag,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where assets_phone_id = #{assetsPhoneId,jdbcType=BIGINT}
  </update>
</mapper>