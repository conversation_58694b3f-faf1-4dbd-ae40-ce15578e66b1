<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockDeliveryPlanHeadMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockDeliveryPlanHead">
    <id column="delivery_plan_head_id" jdbcType="BIGINT" property="deliveryPlanHeadId" />
    <result column="delivery_plan_no" jdbcType="VARCHAR" property="deliveryPlanNo" />
    <result column="from_system" jdbcType="VARCHAR" property="fromSystem" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="out_warehouse_code" jdbcType="VARCHAR" property="outWarehouseCode" />
    <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode" />
    <result column="out_stock_type" jdbcType="INTEGER" property="outStockType" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="is_send" jdbcType="INTEGER" property="isSend" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
    <result column="billing_time" jdbcType="TIMESTAMP" property="billingTime" />
    <result column="plan_out_time" jdbcType="TIMESTAMP" property="planOutTime" />
    <result column="adjust_date" jdbcType="TIMESTAMP" property="adjustDate" />
    <result column="plan_return_time" jdbcType="TIMESTAMP" property="planReturnTime" />
    <result column="need_time" jdbcType="TIMESTAMP" property="needTime" />
    <result column="need_send" jdbcType="INTEGER" property="needSend" />
    <result column="need_address" jdbcType="VARCHAR" property="needAddress" />
    <result column="recipients" jdbcType="VARCHAR" property="recipients" />
    <result column="recipients_phone" jdbcType="VARCHAR" property="recipientsPhone" />
    <result column="reason_code" jdbcType="INTEGER" property="reasonCode" />
    <result column="duty_user" jdbcType="VARCHAR" property="dutyUser" />
    <result column="receive_user" jdbcType="VARCHAR" property="receiveUser" />
    <result column="receive_address" jdbcType="VARCHAR" property="receiveAddress" />
    <result column="use_user" jdbcType="VARCHAR" property="useUser" />
    <result column="use_address" jdbcType="VARCHAR" property="useAddress" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="trans_user" jdbcType="VARCHAR" property="transUser" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    delivery_plan_head_id, delivery_plan_no, from_system, company_code, purchase_order_no, 
    vendor_code, out_warehouse_code, in_warehouse_code, out_stock_type, business_type, 
    is_send, biz_no, billing_user, billing_time, plan_out_time, adjust_date, plan_return_time, 
    need_time, need_send, need_address, recipients, recipients_phone, reason_code, duty_user, 
    receive_user, receive_address, use_user, use_address, status, remark, CREATED_BY, 
    CREATED_AT, UPDATED_BY, UPDATED_AT, trans_user
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockDeliveryPlanHeadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_delivery_plan_heads
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_delivery_plan_heads
    where delivery_plan_head_id = #{deliveryPlanHeadId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_delivery_plan_heads
    where delivery_plan_head_id = #{deliveryPlanHeadId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockDeliveryPlanHead">
    insert into stock_delivery_plan_heads (delivery_plan_head_id, delivery_plan_no, 
      from_system, company_code, purchase_order_no, 
      vendor_code, out_warehouse_code, in_warehouse_code, 
      out_stock_type, business_type, is_send, 
      biz_no, billing_user, billing_time, 
      plan_out_time, adjust_date, plan_return_time, 
      need_time, need_send, need_address, 
      recipients, recipients_phone, reason_code, 
      duty_user, receive_user, receive_address, 
      use_user, use_address, status, 
      remark, CREATED_BY, CREATED_AT, 
      UPDATED_BY, UPDATED_AT, trans_user
      )
    values (#{deliveryPlanHeadId,jdbcType=BIGINT}, #{deliveryPlanNo,jdbcType=VARCHAR}, 
      #{fromSystem,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{purchaseOrderNo,jdbcType=VARCHAR}, 
      #{vendorCode,jdbcType=VARCHAR}, #{outWarehouseCode,jdbcType=VARCHAR}, #{inWarehouseCode,jdbcType=VARCHAR}, 
      #{outStockType,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, #{isSend,jdbcType=INTEGER}, 
      #{bizNo,jdbcType=VARCHAR}, #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=TIMESTAMP}, 
      #{planOutTime,jdbcType=TIMESTAMP}, #{adjustDate,jdbcType=TIMESTAMP}, #{planReturnTime,jdbcType=TIMESTAMP}, 
      #{needTime,jdbcType=TIMESTAMP}, #{needSend,jdbcType=INTEGER}, #{needAddress,jdbcType=VARCHAR}, 
      #{recipients,jdbcType=VARCHAR}, #{recipientsPhone,jdbcType=VARCHAR}, #{reasonCode,jdbcType=INTEGER}, 
      #{dutyUser,jdbcType=VARCHAR}, #{receiveUser,jdbcType=VARCHAR}, #{receiveAddress,jdbcType=VARCHAR}, 
      #{useUser,jdbcType=VARCHAR}, #{useAddress,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, #{transUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockDeliveryPlanHead">
    insert into stock_delivery_plan_heads
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryPlanHeadId != null">
        delivery_plan_head_id,
      </if>
      <if test="deliveryPlanNo != null">
        delivery_plan_no,
      </if>
      <if test="fromSystem != null">
        from_system,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="outWarehouseCode != null">
        out_warehouse_code,
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code,
      </if>
      <if test="outStockType != null">
        out_stock_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="isSend != null">
        is_send,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="billingUser != null">
        billing_user,
      </if>
      <if test="billingTime != null">
        billing_time,
      </if>
      <if test="planOutTime != null">
        plan_out_time,
      </if>
      <if test="adjustDate != null">
        adjust_date,
      </if>
      <if test="planReturnTime != null">
        plan_return_time,
      </if>
      <if test="needTime != null">
        need_time,
      </if>
      <if test="needSend != null">
        need_send,
      </if>
      <if test="needAddress != null">
        need_address,
      </if>
      <if test="recipients != null">
        recipients,
      </if>
      <if test="recipientsPhone != null">
        recipients_phone,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="dutyUser != null">
        duty_user,
      </if>
      <if test="receiveUser != null">
        receive_user,
      </if>
      <if test="receiveAddress != null">
        receive_address,
      </if>
      <if test="useUser != null">
        use_user,
      </if>
      <if test="useAddress != null">
        use_address,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
      <if test="transUser != null">
        trans_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryPlanHeadId != null">
        #{deliveryPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="deliveryPlanNo != null">
        #{deliveryPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="fromSystem != null">
        #{fromSystem,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseCode != null">
        #{outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseCode != null">
        #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="outStockType != null">
        #{outStockType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=INTEGER},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planOutTime != null">
        #{planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustDate != null">
        #{adjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="planReturnTime != null">
        #{planReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="needTime != null">
        #{needTime,jdbcType=TIMESTAMP},
      </if>
      <if test="needSend != null">
        #{needSend,jdbcType=INTEGER},
      </if>
      <if test="needAddress != null">
        #{needAddress,jdbcType=VARCHAR},
      </if>
      <if test="recipients != null">
        #{recipients,jdbcType=VARCHAR},
      </if>
      <if test="recipientsPhone != null">
        #{recipientsPhone,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="dutyUser != null">
        #{dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="receiveUser != null">
        #{receiveUser,jdbcType=VARCHAR},
      </if>
      <if test="receiveAddress != null">
        #{receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="useUser != null">
        #{useUser,jdbcType=VARCHAR},
      </if>
      <if test="useAddress != null">
        #{useAddress,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="transUser != null">
        #{transUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockDeliveryPlanHeadExample" resultType="java.lang.Long">
    select count(*) from stock_delivery_plan_heads
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_delivery_plan_heads
    <set>
      <if test="record.deliveryPlanHeadId != null">
        delivery_plan_head_id = #{record.deliveryPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryPlanNo != null">
        delivery_plan_no = #{record.deliveryPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fromSystem != null">
        from_system = #{record.fromSystem,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorCode != null">
        vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outWarehouseCode != null">
        out_warehouse_code = #{record.outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inWarehouseCode != null">
        in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStockType != null">
        out_stock_type = #{record.outStockType,jdbcType=INTEGER},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.isSend != null">
        is_send = #{record.isSend,jdbcType=INTEGER},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billingUser != null">
        billing_user = #{record.billingUser,jdbcType=VARCHAR},
      </if>
      <if test="record.billingTime != null">
        billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planOutTime != null">
        plan_out_time = #{record.planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adjustDate != null">
        adjust_date = #{record.adjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planReturnTime != null">
        plan_return_time = #{record.planReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.needTime != null">
        need_time = #{record.needTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.needSend != null">
        need_send = #{record.needSend,jdbcType=INTEGER},
      </if>
      <if test="record.needAddress != null">
        need_address = #{record.needAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.recipients != null">
        recipients = #{record.recipients,jdbcType=VARCHAR},
      </if>
      <if test="record.recipientsPhone != null">
        recipients_phone = #{record.recipientsPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonCode != null">
        reason_code = #{record.reasonCode,jdbcType=INTEGER},
      </if>
      <if test="record.dutyUser != null">
        duty_user = #{record.dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveUser != null">
        receive_user = #{record.receiveUser,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveAddress != null">
        receive_address = #{record.receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.useUser != null">
        use_user = #{record.useUser,jdbcType=VARCHAR},
      </if>
      <if test="record.useAddress != null">
        use_address = #{record.useAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.transUser != null">
        trans_user = #{record.transUser,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_delivery_plan_heads
    set delivery_plan_head_id = #{record.deliveryPlanHeadId,jdbcType=BIGINT},
      delivery_plan_no = #{record.deliveryPlanNo,jdbcType=VARCHAR},
      from_system = #{record.fromSystem,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      out_warehouse_code = #{record.outWarehouseCode,jdbcType=VARCHAR},
      in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      out_stock_type = #{record.outStockType,jdbcType=INTEGER},
      business_type = #{record.businessType,jdbcType=INTEGER},
      is_send = #{record.isSend,jdbcType=INTEGER},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      billing_user = #{record.billingUser,jdbcType=VARCHAR},
      billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      plan_out_time = #{record.planOutTime,jdbcType=TIMESTAMP},
      adjust_date = #{record.adjustDate,jdbcType=TIMESTAMP},
      plan_return_time = #{record.planReturnTime,jdbcType=TIMESTAMP},
      need_time = #{record.needTime,jdbcType=TIMESTAMP},
      need_send = #{record.needSend,jdbcType=INTEGER},
      need_address = #{record.needAddress,jdbcType=VARCHAR},
      recipients = #{record.recipients,jdbcType=VARCHAR},
      recipients_phone = #{record.recipientsPhone,jdbcType=VARCHAR},
      reason_code = #{record.reasonCode,jdbcType=INTEGER},
      duty_user = #{record.dutyUser,jdbcType=VARCHAR},
      receive_user = #{record.receiveUser,jdbcType=VARCHAR},
      receive_address = #{record.receiveAddress,jdbcType=VARCHAR},
      use_user = #{record.useUser,jdbcType=VARCHAR},
      use_address = #{record.useAddress,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      trans_user = #{record.transUser,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockDeliveryPlanHead">
    update stock_delivery_plan_heads
    <set>
      <if test="deliveryPlanNo != null">
        delivery_plan_no = #{deliveryPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="fromSystem != null">
        from_system = #{fromSystem,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseCode != null">
        out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="outStockType != null">
        out_stock_type = #{outStockType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="isSend != null">
        is_send = #{isSend,jdbcType=INTEGER},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        billing_user = #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        billing_time = #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planOutTime != null">
        plan_out_time = #{planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustDate != null">
        adjust_date = #{adjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="planReturnTime != null">
        plan_return_time = #{planReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="needTime != null">
        need_time = #{needTime,jdbcType=TIMESTAMP},
      </if>
      <if test="needSend != null">
        need_send = #{needSend,jdbcType=INTEGER},
      </if>
      <if test="needAddress != null">
        need_address = #{needAddress,jdbcType=VARCHAR},
      </if>
      <if test="recipients != null">
        recipients = #{recipients,jdbcType=VARCHAR},
      </if>
      <if test="recipientsPhone != null">
        recipients_phone = #{recipientsPhone,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="dutyUser != null">
        duty_user = #{dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="receiveUser != null">
        receive_user = #{receiveUser,jdbcType=VARCHAR},
      </if>
      <if test="receiveAddress != null">
        receive_address = #{receiveAddress,jdbcType=VARCHAR},
      </if>
      <if test="useUser != null">
        use_user = #{useUser,jdbcType=VARCHAR},
      </if>
      <if test="useAddress != null">
        use_address = #{useAddress,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="transUser != null">
        trans_user = #{transUser,jdbcType=VARCHAR},
      </if>
    </set>
    where delivery_plan_head_id = #{deliveryPlanHeadId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockDeliveryPlanHead">
    update stock_delivery_plan_heads
    set delivery_plan_no = #{deliveryPlanNo,jdbcType=VARCHAR},
      from_system = #{fromSystem,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
      in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      out_stock_type = #{outStockType,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      is_send = #{isSend,jdbcType=INTEGER},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      billing_user = #{billingUser,jdbcType=VARCHAR},
      billing_time = #{billingTime,jdbcType=TIMESTAMP},
      plan_out_time = #{planOutTime,jdbcType=TIMESTAMP},
      adjust_date = #{adjustDate,jdbcType=TIMESTAMP},
      plan_return_time = #{planReturnTime,jdbcType=TIMESTAMP},
      need_time = #{needTime,jdbcType=TIMESTAMP},
      need_send = #{needSend,jdbcType=INTEGER},
      need_address = #{needAddress,jdbcType=VARCHAR},
      recipients = #{recipients,jdbcType=VARCHAR},
      recipients_phone = #{recipientsPhone,jdbcType=VARCHAR},
      reason_code = #{reasonCode,jdbcType=INTEGER},
      duty_user = #{dutyUser,jdbcType=VARCHAR},
      receive_user = #{receiveUser,jdbcType=VARCHAR},
      receive_address = #{receiveAddress,jdbcType=VARCHAR},
      use_user = #{useUser,jdbcType=VARCHAR},
      use_address = #{useAddress,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      trans_user = #{transUser,jdbcType=VARCHAR}
    where delivery_plan_head_id = #{deliveryPlanHeadId,jdbcType=BIGINT}
  </update>
</mapper>