package com.gz.eim.am.stock.dto.request.order.plan;

import java.util.List;

/**
 * @author: lish<PERSON><PERSON>
 * @date: 2019/12/11
 * @description:
 */
public class DeliveryPlanHeadFastReqDTO {
    /**
     * 计划出库单集合
     */
    private List<DeliveryPlanHeadReqDTO> deliveryPlanHeadReqDTOList;
    /**
     * 计划出库单头状态
     */
    private Integer headStatus;
    /**
     * 计划入库单行状态
     */
    private Integer lineStatus;
    /**
     * 工号
     */
    private String employeeCode;
    /**
     * 业务单号
     */
    private String bizNo;


    public List<DeliveryPlanHeadReqDTO> getDeliveryPlanHeadReqDTOList() {
        return deliveryPlanHeadReqDTOList;
    }

    public void setDeliveryPlanHeadReqDTOList(List<DeliveryPlanHeadReqDTO> deliveryPlanHeadReqDTOList) {
        this.deliveryPlanHeadReqDTOList = deliveryPlanHeadReqDTOList;
    }

    public Integer getHeadStatus() {
        return headStatus;
    }

    public void setHeadStatus(Integer headStatus) {
        this.headStatus = headStatus;
    }

    public Integer getLineStatus() {
        return lineStatus;
    }

    public void setLineStatus(Integer lineStatus) {
        this.lineStatus = lineStatus;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    @Override
    public String toString() {
        return "DeliveryPlanHeadFastReqDTO{" +
                "deliveryPlanHeadReqDTOList=" + deliveryPlanHeadReqDTOList +
                ", headStatus=" + headStatus +
                ", lineStatus=" + lineStatus +
                ", employeeCode='" + employeeCode + '\'' +
                ", bizNo='" + bizNo + '\'' +
                '}';
    }
}
