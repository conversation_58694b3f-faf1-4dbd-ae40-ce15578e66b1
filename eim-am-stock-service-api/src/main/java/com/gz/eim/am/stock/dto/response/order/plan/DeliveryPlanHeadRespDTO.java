package com.gz.eim.am.stock.dto.response.order.plan;

import com.gz.eim.am.base.dto.response.file.SysAttachDTO;
import com.gz.eim.am.stock.dto.response.demand.StockAssetsDemandHeadRespDTO;
import com.gz.eim.am.stock.dto.response.file.StockAttachDTO;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/10/28
 * @description:
 */
@ToString
public class DeliveryPlanHeadRespDTO {
    /**
     * 实际领用人姓名
     */
    private String receiveUserName;
    /**
     * 实际领用人
     */
    private String receiveUser;
    /**
     * 实际申请人姓名
     */
    private String applyUserName;
    /**
     * 实际申请人
     */
    private String applyUser;
    /**
     * 实际领用人
     */
    private String transUser;
    /**
     * 业务单号
     */
    private String orderNo;
    /**
     * 出库单编号
     */
    private String deliveryPlanNo;
    /**
     * 系统标识
     */
    private String systemCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 出库单状态
     */
    private Integer status;
    /**
     * 调出仓库编码
     */
    private String outWarehouseCode;
    /**
     * 调出仓库管理员
     */
    private String outLinkman;

    /**
     * 调出仓库管理员名称
     */
    private String outLinkmanName;

    /**
     * 调入仓库编码
     */
    private String inWarehouseCode;
    /**
     * 调入仓库管理员
     */
    private String inLinkman;
    /**
     * 调入仓库管理员名称
     */
    private String inLinkmanName;
    /**
     * 实际出库日期
     */
    private Date outStockTime;
    /**
     * 出库单类型
     */
    private Integer outStockType;
    /**
     * 是否需要发货：0 无需发货 1 需要发货
     */
    private Integer isSend;
    /**
     * 责任人工号
     */
    private String dutyUser;
    /**
     * 实际使用人工号
     */
    private String useUser;
    /**
     * 业务单号
     */
    private String bizNo;
    /**
     * 制单人工号
     */
    private String billingUser;
    /**
     * 原因编码
     */
    private Integer reasonCode;

    /**
     * 原因编码名称
     */
    private String reasonTypeName;
    /**
     * 使用人名称
     */
    private String useUserName;
    /**
     * 调出仓库名称
     */
    private String outWarehouseName;
    /**
     * 调出仓库地址
     */
    private String outWarehouseAddress;
    /**
     * 调入仓库库名称
     */
    private String inWarehouseName;
    /**
     * 出库单状态名称
     */
    private String statusName;
    /**
     * 制单人名称
     */
    private String billingUserName;
    /**
     * 资产处置单已处置数量
     */
    private Long disponseCount;
    /**
     * 计划出库单行信息
     */
    private List<DeliveryPlanLineRespDTO> deliveryPlanLineRespDTOS;
    /**
     * 领用人名称
     */
    private String dutyUserName;
    /**
     * 计划出库单id
     */
    private Long deliveryPlanHeadId;
    /**
     * 出库单类型名称
     */
    private String outStockTypeName;

    /**
     * 物料编码名称
     */
    private String supplierName;
    /**
     * 计划出库时间
     */
    private String planOutTime;

    /**
     * 计划归还时间
     */
    private String planReturnTime;

    /**
     * 所属部门名称
     */
    private String useUserDeptName;
    /**
     * 领用人职级名称
     */
    private String useUserHpsJobcdDescr;

    /**
     * 领用人位置
     */
    private String useAddress;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 制单时间
     */
    private String billingTime;

    /**
     * 执照需求日期
     */
    private String needTime;

    /**
     * 执照是否邮寄
     */
    private Integer needSend;

    /**
     * 执照邮寄地址
     */
    private String needAddress;

    /**
     * 执照收件人
     */
    private String recipients;

    /**
     * 执照收件人电话
     */
    private String recipientsPhone;

    /**
     * 附件集合
     */
    private List<StockAttachDTO> stockAttachDTOList;

    /**
     * 是否催办发送呱呱消息 1 是 0 否
     */
    private Integer isUrgeMessage;

    /**
     * 是否催办发送呱呱消息名称
     */
    private String  isUrgeMessageName;

    /**
     * 实际领用地址
     */
    private String receiveAddress;

    /**
     * 对应的需求单信息
     */
    private StockAssetsDemandHeadRespDTO stockAssetsDemandHeadRespDTO;

    /**
     * 领用方式
     */
    private Integer deliveryMethod;

    /**
     * 领用方式描述
     */
    private String deliveryMethodDesc;

    /**
     * 快递单号
     */
    private String trackingNumber;

    /**
     * 发货日期
     */
    private String sendDate;

    /**
     * 发货天数
     */
    private Integer sendDay;

    /**
     * 发货数量
     */
    private Integer sendNumber;

    /**
     * 收货日期
     */
    private String receiveDate;

    public String getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(String receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getDeliveryMethodDesc() {
        return deliveryMethodDesc;
    }

    public void setDeliveryMethodDesc(String deliveryMethodDesc) {
        this.deliveryMethodDesc = deliveryMethodDesc;
    }

    public Integer getSendNumber() {
        return sendNumber;
    }

    public void setSendNumber(Integer sendNumber) {
        this.sendNumber = sendNumber;
    }

    public Integer getSendDay() {
        return sendDay;
    }

    public void setSendDay(Integer sendDay) {
        this.sendDay = sendDay;
    }

    public String getSendDate() {
        return sendDate;
    }

    public void setSendDate(String sendDate) {
        this.sendDate = sendDate;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public Integer getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(Integer deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public void setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
    }

    public StockAssetsDemandHeadRespDTO getStockAssetsDemandHeadRespDTO() {
        return stockAssetsDemandHeadRespDTO;
    }

    public void setStockAssetsDemandHeadRespDTO(StockAssetsDemandHeadRespDTO stockAssetsDemandHeadRespDTO) {
        this.stockAssetsDemandHeadRespDTO = stockAssetsDemandHeadRespDTO;
    }

    public String getTransUser() {
        return transUser;
    }

    public void setTransUser(String transUser) {
        this.transUser = transUser;
    }

    /**
     * 附件
     */
    private List<SysAttachDTO> sysAttachDTOS;

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getUseAddress() {
        return useAddress;
    }

    public void setUseAddress(String useAddress) {
        this.useAddress = useAddress;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDeliveryPlanNo() {
        return deliveryPlanNo;
    }

    public void setDeliveryPlanNo(String deliveryPlanNo) {
        this.deliveryPlanNo = deliveryPlanNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOutWarehouseCode() {
        return outWarehouseCode;
    }

    public void setOutWarehouseCode(String outWarehouseCode) {
        this.outWarehouseCode = outWarehouseCode;
    }

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public Date getOutStockTime() {
        return outStockTime;
    }

    public void setOutStockTime(Date outStockTime) {
        this.outStockTime = outStockTime;
    }

    public Integer getOutStockType() {
        return outStockType;
    }

    public void setOutStockType(Integer outStockType) {
        this.outStockType = outStockType;
    }

    public Integer getIsSend() {
        return isSend;
    }

    public void setIsSend(Integer isSend) {
        this.isSend = isSend;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getUseUser() {
        return useUser;
    }

    public void setUseUser(String useUser) {
        this.useUser = useUser;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public Integer getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(Integer reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getReasonTypeName() {
        return reasonTypeName;
    }

    public void setReasonTypeName(String reasonTypeName) {
        this.reasonTypeName = reasonTypeName;
    }

    public String getUseUserName() {
        return useUserName;
    }

    public void setUseUserName(String useUserName) {
        this.useUserName = useUserName;
    }

    public String getOutWarehouseName() {
        return outWarehouseName;
    }

    public void setOutWarehouseName(String outWarehouseName) {
        this.outWarehouseName = outWarehouseName;
    }

    public String getOutWarehouseAddress() {
        return outWarehouseAddress;
    }

    public void setOutWarehouseAddress(String outWarehouseAddress) {
        this.outWarehouseAddress = outWarehouseAddress;
    }

    public String getInWarehouseName() {
        return inWarehouseName;
    }

    public void setInWarehouseName(String inWarehouseName) {
        this.inWarehouseName = inWarehouseName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getBillingUserName() {
        return billingUserName;
    }

    public void setBillingUserName(String billingUserName) {
        this.billingUserName = billingUserName;
    }

    public Long getDisponseCount() {
        return disponseCount;
    }

    public void setDisponseCount(Long disponseCount) {
        this.disponseCount = disponseCount;
    }

    public List<DeliveryPlanLineRespDTO> getDeliveryPlanLineRespDTOS() {
        return deliveryPlanLineRespDTOS;
    }

    public void setDeliveryPlanLineRespDTOS(List<DeliveryPlanLineRespDTO> deliveryPlanLineRespDTOS) {
        this.deliveryPlanLineRespDTOS = deliveryPlanLineRespDTOS;
    }

    public String getDutyUserName() {
        return dutyUserName;
    }

    public void setDutyUserName(String dutyUserName) {
        this.dutyUserName = dutyUserName;
    }

    public Long getDeliveryPlanHeadId() {
        return deliveryPlanHeadId;
    }

    public void setDeliveryPlanHeadId(Long deliveryPlanHeadId) {
        this.deliveryPlanHeadId = deliveryPlanHeadId;
    }

    public String getOutStockTypeName() {
        return outStockTypeName;
    }

    public void setOutStockTypeName(String outStockTypeName) {
        this.outStockTypeName = outStockTypeName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPlanOutTime() {
        return planOutTime;
    }

    public void setPlanOutTime(String planOutTime) {
        this.planOutTime = planOutTime;
    }

    public String getUseUserDeptName() {
        return this.useUserDeptName;
    }

    public void setUseUserDeptName(final String useUserDeptName) {
        this.useUserDeptName = useUserDeptName;
    }

    public String getUseUserHpsJobcdDescr() {
        return this.useUserHpsJobcdDescr;
    }

    public void setUseUserHpsJobcdDescr(final String useUserHpsJobcdDescr) {
        this.useUserHpsJobcdDescr = useUserHpsJobcdDescr;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }

    public List<StockAttachDTO> getStockAttachDTOList() {
        return stockAttachDTOList;
    }

    public void setStockAttachDTOList(List<StockAttachDTO> stockAttachDTOList) {
        this.stockAttachDTOList = stockAttachDTOList;
    }

    public String getPlanReturnTime() {
        return planReturnTime;
    }

    public void setPlanReturnTime(String planReturnTime) {
        this.planReturnTime = planReturnTime;
    }

    public String getNeedTime() {
        return needTime;
    }

    public void setNeedTime(String needTime) {
        this.needTime = needTime;
    }

    public Integer getNeedSend() {
        return needSend;
    }

    public void setNeedSend(Integer needSend) {
        this.needSend = needSend;
    }

    public String getNeedAddress() {
        return needAddress;
    }

    public void setNeedAddress(String needAddress) {
        this.needAddress = needAddress;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getRecipientsPhone() {
        return recipientsPhone;
    }

    public void setRecipientsPhone(String recipientsPhone) {
        this.recipientsPhone = recipientsPhone;
    }

    public Integer getIsUrgeMessage() {
        return isUrgeMessage;
    }

    public void setIsUrgeMessage(Integer isUrgeMessage) {
        this.isUrgeMessage = isUrgeMessage;
    }

    public String getIsUrgeMessageName() {
        return isUrgeMessageName;
    }

    public void setIsUrgeMessageName(String isUrgeMessageName) {
        this.isUrgeMessageName = isUrgeMessageName;
    }

    public String getOutLinkman() {
        return outLinkman;
    }

    public void setOutLinkman(String outLinkman) {
        this.outLinkman = outLinkman;
    }

    public String getOutLinkmanName() {
        return outLinkmanName;
    }

    public void setOutLinkmanName(String outLinkmanName) {
        this.outLinkmanName = outLinkmanName;
    }

    public String getInLinkman() {
        return inLinkman;
    }

    public void setInLinkman(String inLinkman) {
        this.inLinkman = inLinkman;
    }

    public String getInLinkmanName() {
        return inLinkmanName;
    }

    public void setInLinkmanName(String inLinkmanName) {
        this.inLinkmanName = inLinkmanName;
    }

    public List<SysAttachDTO> getSysAttachDTOS() {
        return sysAttachDTOS;
    }

    public void setSysAttachDTOS(List<SysAttachDTO> sysAttachDTOS) {
        this.sysAttachDTOS = sysAttachDTOS;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    public String getReceiveUser() {
        return receiveUser;
    }

    public void setReceiveUser(String receiveUser) {
        this.receiveUser = receiveUser;
    }
}
