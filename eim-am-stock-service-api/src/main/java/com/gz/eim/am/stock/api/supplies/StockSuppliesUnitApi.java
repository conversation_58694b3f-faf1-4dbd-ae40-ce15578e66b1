package com.gz.eim.am.stock.api.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesUnitReqDTO;

import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/3/13
 * @description 物料单位api
 */

@FeignClient(
    name = "eim-am-stock",
    serviceId = "eim-am-stock",
    path = "/api/am/stock/supplies/unit",
    configuration = FeignClientConfiguration.class
)

public interface StockSuppliesUnitApi {

    /**
     * 所有物料单位
     * @param unitReqDTO
     * @return
     */
    @ApiOperation(value = "查询所有物料单位", notes = "物料单位")
    @GetMapping(value = "/find-all")
    ResponseData selectSuppliesUnit(final SuppliesUnitReqDTO unitReqDTO);

}
