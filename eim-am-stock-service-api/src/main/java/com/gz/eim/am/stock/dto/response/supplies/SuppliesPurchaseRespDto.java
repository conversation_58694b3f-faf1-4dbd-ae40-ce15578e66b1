package com.gz.eim.am.stock.dto.response.supplies;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/7/9
 * <p>
 *
 * </p>
 */
public class SuppliesPurchaseRespDto {
    //主键
    private Long id;

    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 仓库类型编码
     */
    private Integer warehouseTypeCode;
    /**
     * 是否允许采购
     */
    private Integer isAllowPurchase;
    /**
     * 是否启用仓储管理 （0 否  1 是）
     */
    private Integer inventoryManageFlag;
    /**
     *是否使用批准供应商(1: 是，0否)
     */
    private Integer isUserApprovedSupplier;
    /**
     * 是否要求询报价(1: 是，0 : 否)
     */
    private Integer isRequestAskQuote;
    /**
     * 是否合并比价(1：是， 0否)
     */
    private Integer isMergeParity;
    /**
     * 是否要求招投标(1: 是，0否)
     */
    private Integer isRequestBidding;
    /**
     * 默认采购员工号
     */
    private String defaultPurchaseUser;
    /**
     * 默认费用项
     */
    private String defaultCostItemCode;
    /**
     * 默认费用项名称
     */
    private String defaultCostItemName;
    /**
     * 默认采购价格
     */
    private BigDecimal defaultPurchasePrice;
    /**
     * 是否要求校验(1:是，0: 否)
     */
    private Integer isRequestCheck;
    /**
     * 默认接收数量允差
     */
    private Long defaultReceiveQuantityTolerance;
    /**
     * 否允许替代接收(1:是，0: 否）
     */
    private Integer isAllowReplaceReceive;
    /**
     * 是否允许未订购接收(1:是，0: 否)
     */
    private Integer isAllowNotOrderedReceive;
    /**
     * 默认接收方式
     */
    private String defaultReceiveWay;
    /**
     * 默认接收仓库
     */
    private String defaultReceiveWarehouseCode;
    /**
     * 生产厂家
     */
    private String produceFactory;
    /**
     * 服务厂家
     */
    private String serviceFactory;
    /**
     * 售后厂家
     */
    private String afterSaleFactory;
    /**
     * 收货类型
     */
    private Integer receiveType;

    private String remark;

    private List<StockSuppliesPurchaseExtRespDTO> suppliesPurchaseExtRespDTOList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getWarehouseTypeCode() {
        return warehouseTypeCode;
    }

    public void setWarehouseTypeCode(Integer warehouseTypeCode) {
        this.warehouseTypeCode = warehouseTypeCode;
    }

    public Integer getIsAllowPurchase() {
        return isAllowPurchase;
    }

    public void setIsAllowPurchase(Integer isAllowPurchase) {
        this.isAllowPurchase = isAllowPurchase;
    }

    public Integer getIsUserApprovedSupplier() {
        return isUserApprovedSupplier;
    }

    public void setIsUserApprovedSupplier(Integer isUserApprovedSupplier) {
        this.isUserApprovedSupplier = isUserApprovedSupplier;
    }

    public Integer getIsRequestAskQuote() {
        return isRequestAskQuote;
    }

    public void setIsRequestAskQuate(Integer isRequestAskQuote) {
        this.isRequestAskQuote = isRequestAskQuote;
    }

    public Integer getIsMergeParity() {
        return isMergeParity;
    }

    public void setIsMergeParity(Integer isMergeParity) {
        this.isMergeParity = isMergeParity;
    }

    public Integer getIsRequestBidding() {
        return isRequestBidding;
    }

    public void setIsRequestBidding(Integer isRequestBidding) {
        this.isRequestBidding = isRequestBidding;
    }

    public String getDefaultPurchaseUser() {
        return defaultPurchaseUser;
    }

    public void setDefaultPurchaseUser(String defaultPurchaseUser) {
        this.defaultPurchaseUser = defaultPurchaseUser;
    }

    public String getDefaultCostItemCode() {
        return defaultCostItemCode;
    }

    public void setDefaultCostItemCode(String defaultCostItemCode) {
        this.defaultCostItemCode = defaultCostItemCode;
    }

    public BigDecimal getDefaultPurchasePrice() {
        return defaultPurchasePrice;
    }

    public void setDefaultPurchasePrice(BigDecimal defaultPurchasePrice) {
        this.defaultPurchasePrice = defaultPurchasePrice;
    }

    public Integer getIsRequestCheck() {
        return isRequestCheck;
    }

    public void setIsRequestCheck(Integer isRequestCheck) {
        this.isRequestCheck = isRequestCheck;
    }

    public Long getDefaultReceiveQuantityTolerance() {
        return defaultReceiveQuantityTolerance;
    }

    public void setDefaultReceiveQuantityTolerance(Long defaultReceiveQuantityTolerance) {
        this.defaultReceiveQuantityTolerance = defaultReceiveQuantityTolerance;
    }

    public Integer getIsAllowReplaceReceive() {
        return isAllowReplaceReceive;
    }

    public void setIsAllowReplaceReceive(Integer isAllowReplaceReceive) {
        this.isAllowReplaceReceive = isAllowReplaceReceive;
    }

    public Integer getIsAllowNotOrderedReceive() {
        return isAllowNotOrderedReceive;
    }

    public void setIsAllowNotOrderedReceive(Integer isAllowNotOrderedReceive) {
        this.isAllowNotOrderedReceive = isAllowNotOrderedReceive;
    }

    public String getDefaultReceiveWay() {
        return defaultReceiveWay;
    }

    public void setDefaultReceiveWay(String defaultReceiveWay) {
        this.defaultReceiveWay = defaultReceiveWay;
    }

    public String getDefaultReceiveWarehouseCode() {
        return defaultReceiveWarehouseCode;
    }

    public void setDefaultReceiveWarehouseCode(String defaultReceiveWarehouseCode) {
        this.defaultReceiveWarehouseCode = defaultReceiveWarehouseCode;
    }

    public String getProduceFactory() {
        return produceFactory;
    }

    public void setProduceFactory(String produceFactory) {
        this.produceFactory = produceFactory;
    }

    public String getServiceFactory() {
        return serviceFactory;
    }

    public void setServiceFactory(String serviceFactory) {
        this.serviceFactory = serviceFactory;
    }

    public String getAfterSaleFactory() {
        return afterSaleFactory;
    }

    public void setAfterSaleFactory(String afterSaleFactory) {
        this.afterSaleFactory = afterSaleFactory;
    }

    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }

    public Integer getInventoryManageFlag() {
        return inventoryManageFlag;
    }

    public void setInventoryManageFlag(Integer inventoryManageFlag) {
        this.inventoryManageFlag = inventoryManageFlag;
    }

    public void setIsRequestAskQuote(Integer isRequestAskQuote) {
        this.isRequestAskQuote = isRequestAskQuote;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<StockSuppliesPurchaseExtRespDTO> getSuppliesPurchaseExtRespDTOList() {
        return suppliesPurchaseExtRespDTOList;
    }

    public void setSuppliesPurchaseExtRespDTOList(List<StockSuppliesPurchaseExtRespDTO> suppliesPurchaseExtRespDTOList) {
        this.suppliesPurchaseExtRespDTOList = suppliesPurchaseExtRespDTOList;
    }

    public String getDefaultCostItemName() {
        return defaultCostItemName;
    }

    public void setDefaultCostItemName(String defaultCostItemName) {
        this.defaultCostItemName = defaultCostItemName;
    }

    @Override
    public String toString() {
        return "SuppliesPurchaseRespDto{" +
                "id=" + id +
                ", suppliesCode='" + suppliesCode + '\'' +
                ", warehouseTypeCode=" + warehouseTypeCode +
                ", isAllowPurchase=" + isAllowPurchase +
                ", inventoryManageFlag=" + inventoryManageFlag +
                ", isUserApprovedSupplier=" + isUserApprovedSupplier +
                ", isRequestAskQuote=" + isRequestAskQuote +
                ", isMergeParity=" + isMergeParity +
                ", isRequestBidding=" + isRequestBidding +
                ", defaultPurchaseUser='" + defaultPurchaseUser + '\'' +
                ", defaultCostItemCode='" + defaultCostItemCode + '\'' +
                ", defaultPurchasePrice=" + defaultPurchasePrice +
                ", isRequestCheck=" + isRequestCheck +
                ", defaultReceiveQuantityTolerance=" + defaultReceiveQuantityTolerance +
                ", isAllowReplaceReceive=" + isAllowReplaceReceive +
                ", isAllowNotOrderedReceive=" + isAllowNotOrderedReceive +
                ", defaultReceiveWay='" + defaultReceiveWay + '\'' +
                ", defaultReceiveWarehouseCode='" + defaultReceiveWarehouseCode + '\'' +
                ", produceFactory='" + produceFactory + '\'' +
                ", serviceFactory='" + serviceFactory + '\'' +
                ", afterSaleFactory='" + afterSaleFactory + '\'' +
                ", receiveType=" + receiveType +
                ", remark='" + remark + '\'' +
                ", suppliesPurchaseExtRespDTOList=" + suppliesPurchaseExtRespDTOList +
                '}';
    }
}
