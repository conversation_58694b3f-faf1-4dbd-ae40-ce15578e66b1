package com.gz.eim.am.stock.dto.response.discount;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Author: wangjing67
 * @Date: 4/1/21 11:24 上午
 * @description
 */
@ToString
@Data
public class StockLicenseChangeItemsRespDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 单据号
     */
    private String changeNo;

    /**
     * 变更类型
     */
    private Integer changeType;

    /**
     * 变更类型名称
     */
    private String changeTypeName;

    /**
     * 变更前值
     */
    private String beforeValue;

    /**
     * 变更类型为法人信息 为变更前法人名称
     */
    private String beforeValueName;

    /**
     * 变更后值
     */
    private String afterValue;

    /**
     * 变更类型为法人信息 为变更后法人名称
     */
    private String afterValueName;

    /**
     * 变更类型为法人信息 为变更前法人身份证信息
     */
    private String beforeIdCard;

    /**
     * 变更类型为法人信息 为变更后法人身份证信息
     */
    private String afterIdCard;

    /**
     * 单据状态
     */
    private Integer changeStatus;

    /**
     * 单据状态名称
     */
    private String changeStatusName;
    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人名称
     */
    private String createdByName;
    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 物料编码
     */
    private String suppliesCode;

    /**
     * 物料名称
     */
    private String suppliesName;
}
