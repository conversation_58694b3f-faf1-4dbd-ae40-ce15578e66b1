package com.gz.eim.am.stock.api.discount;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.discount.StockLicenseRenewReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: wangjing67
 * @Date: 4/8/21 9:46 下午
 * @description 执照员工续借入口api
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId = "eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockLicenseRenewApi {

    /**
     * 执照续借确认提交接口
     * @param stockLicenseRenewReqDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "执照续借接口", notes = "执照续借")
    @PostMapping(value = "/renew")
    ResponseData licenseRenew(@RequestBody StockLicenseRenewReqDTO stockLicenseRenewReqDTO)throws Exception;


    /**
     * 工作流平台查询执照续借信息
     * @param bizId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "执照续借接口", notes = "执照续借")
    @GetMapping(value = "/wfl/{bizId}")
    ResponseData selectLicenseRenewInfo(@PathVariable("bizId") String bizId)throws Exception;

}
