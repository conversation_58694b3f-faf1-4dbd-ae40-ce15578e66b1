package com.gz.eim.am.stock.dto.response.check;

import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/11/16
 * @description
 */
@Data
@ToString
public class AssetQueryScopeRespDTO {


    /*private Map<Integer,String> assetStatus;

    private Map<String,String> assetsKeeper;

    private Map<String,String> holder;

    private Map<String,String> warehouseCode;

    private String busLargeRegion;

    private String busCity;

    private String assetsCode;

    private String snCode;

    private String purchaseNo;

    private Map<String,String> needDept;

    private String category;
    private String keyWord;*/


    /////////////////////////////////

    /**
     * 是否在库
     */
    private Integer assetStatusInFlag;

    /**
     * 资产所在仓库(在库)
     */
    private Map<String,String> warehouseCodeList;

    /**
     * 资产类别(在库)
     */
    private List<String> inCategoryList;

    /**
     * 费用部门（在库）
     */
    private Map<String,String> inCostDeptList;

    /**
     * 资产管理员(在库)
     */
    private Map<String,String> inAssetsKeeperList;

    /**
     * 是否使用中
     */
    private Integer assetStatusUsedFlag;

    /**
     * 资产所在仓库(使用中)
     */
    private Map<String,String> holderList;

    /**
     * 资产类别(使用中)
     */
    private List<String> usedCategoryList;

    /**
     * 费用部门（使用中）
     */
    private Map<String,String> usedCostDeptList;

    /**
     * 资产管理员(使用中)
     */
    private Map<String,String> usedAssetsKeeperList;

    /**
     * 仓库类型集合
     */
    private List<Integer> warehouseTypeList;

}
