package com.gz.eim.am.stock.dto.request.check;

import com.gz.eim.am.stock.dto.response.check.StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO;
import lombok.Data;

import java.util.List;

/**
 * @className: StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO
 * @description: 库存盘点资产员工异常资产请求头
 * @author: <EMAIL>
 * @date: 2023/11/10
 **/
@Data
public class StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO {
    /**
     * 盘点任务号
     */
    private String checkTaskNo;
    /**
     * 盘点人工号
     */
    private String checkPerson;
    /**
     * 异常资产列表
     */
    private List<StockCheckAssetsEmployeeAbnormalAssetsLineReqDTO> stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList;

}
