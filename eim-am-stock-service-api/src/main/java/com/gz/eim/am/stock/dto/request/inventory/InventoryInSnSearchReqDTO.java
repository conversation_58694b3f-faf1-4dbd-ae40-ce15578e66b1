package com.gz.eim.am.stock.dto.request.inventory;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/2/5
 * @description 入库流水关联sn号查询
 */
@Data
public class InventoryInSnSearchReqDTO extends PageReqDTO {
    /**
     * 流水id
     */
    private Long flowId;

    /**
     * 序列号
     */
    private String snNo;

    /**
     * 仓库编码集合
     */
    private List<String> codes;
}
