package com.gz.eim.am.stock.dto.request.discount;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 1/27/21 6:26 下午
 * @description
 */
@Data
@ToString
public class StockCardSuppliesDiscountQueryDTO extends PageReqDTO {

    private String discountNo;

    private Integer discountType;

    private Integer discountStatus;

    private String createdBy;

    private String createdStartDate;

    private Date  startDate;

    private String createdEndDate;

    private Date endDate;

    private  String warehouseCode;

    private List<String> warehouseCodeList;

}
