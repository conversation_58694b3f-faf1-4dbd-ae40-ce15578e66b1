package com.gz.eim.am.stock.dto.request.supplies;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-21
 */
@Data
@ToString
public class SuppliesQuantityReqDTO extends PageReqDTO {
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 管理编码
     */
    private String manageNo;
    /**
     * 结算中心编码
     */
    private String costCenterCode;
    /**
     * 版本号
     */
    private String version;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 序列号
     */
    private String snNo;

    private List<String> warehouseCodeList;
    /**
     * 批次号
     */
    private List<String> batchNoList;
    /**
     * 序列号集合
     */
    private List<String> snNoList;
    /**
     * 库存状态 0：在库 1：已出库
     */
    private Integer configStatus;
    /**
     * 安装状态 0：未安装 1：安装
     */
    private Integer installStatus;

    /**
     * 业务开始时间
     */
    private String createdStartTime;
    /**
     * 业务结束时间
     */
    private String createdEndTime;

}
