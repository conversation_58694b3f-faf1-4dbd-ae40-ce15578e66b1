package com.gz.eim.am.stock.dto.request.check;

import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/11/4
 * @description
 */
@Data
@ToString
public class StockCheckMissionReqDTO {

    /**
     *盘点计划单号
     */
    private String takingPlanNo;

    /**
     * 盘点任务名称
     */
    private String checkTaskName;

    /**
     * 盘点方式
     */
    private Integer checkTaskMethod;

    /**
     * 责任人
     */
    private String dutyUser;

    /**
     * 任务开始时间
     */
    private String taskBeginTime;

    /**
     * 任务结束时间
     */
    private String taskEndTime;

    /**
     * 任务截止日期
     */
    private String taskLastTime;

    /**
     * 任务下发方式 员工自助盘点必传
     */
    private Integer assignFlag;

    /**
     * 下发到部门时，剔除部门下不进行下发的员工
     */
    private List<String> assignExcludePeoples;

    /**
     * 资产查询范围实体
     */
    private AssetQueryScopeReqDTO assetQueryScopeReqDTO;


}
