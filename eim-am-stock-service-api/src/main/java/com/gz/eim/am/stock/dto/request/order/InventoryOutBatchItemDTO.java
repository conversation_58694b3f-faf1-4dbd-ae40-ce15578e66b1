package com.gz.eim.am.stock.dto.request.order;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 2:22
 */
public class InventoryOutBatchItemDTO {

    private String deliveryNo;

    private String inWarehouseCode;

    private String outWarehouseCode;

    private String suppliesCode;

    private Integer number;

    private String dutyUser;

    private String manageNo;

    private String remark;

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public String getOutWarehouseCode() {
        return outWarehouseCode;
    }

    public void setOutWarehouseCode(String outWarehouseCode) {
        this.outWarehouseCode = outWarehouseCode;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getManageNo() {
        return manageNo;
    }

    public void setManageNo(String manageNo) {
        this.manageNo = manageNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    @Override
    public String toString() {
        return "InventoryOutBatchItemDTO{" +
                "deliveryNo='" + deliveryNo + '\'' +
                ", inWarehouseCode='" + inWarehouseCode + '\'' +
                ", outWarehouseCode='" + outWarehouseCode + '\'' +
                ", suppliesCode='" + suppliesCode + '\'' +
                ", number=" + number +
                ", dutyUser='" + dutyUser + '\'' +
                ", manageNo='" + manageNo + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}