package com.gz.eim.am.stock.dto.request.assets;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2021/4/13
 * @description
 */
@Data
@ToString
public class AssetsEbsSyncReqDTO {

    /**
     * 类型 1：新增表  2：报废表  3：变更表
     */
    private Integer type;

    /**
     * 老批次号 更新条件
     */
    private String oldBatchNo;

    /**
     * 老状态 更新条件
     */
    private Integer oldSyncStatus;

    /**
     * 要替换的批次号
     */
    private String newBatchNo;

    /**
     * 要替换的状态
     */
    private Integer newSyncStatus;

    /**
     * 主键id集合
     */
    private List<Long> ids;
}
