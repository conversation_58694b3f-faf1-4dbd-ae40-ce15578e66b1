package com.gz.eim.am.stock.dto.request.check;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/11/20
 * @description
 */
@Data
@ToString
public class CheckDifferenceListHeadReqDTO extends PageReqDTO {

    private Long headId;

    private String takingPlanNo;

    private String listTitle;

    private String checkTimeQuantum;

    private String checkScope;

    private String checkDuty;

    private String checkTaskMethod;

    private Integer checkAllNumber;

    private Integer autoCheckNumber;

    private Integer checkNormalNumber;

    private Integer checkNoNormalNumber;

    /**
     * 备注字段
     */
    private String checkRemark;

    private Integer type;

    /**
     * 调整日期
     */
    private String adjustTime;

    /**
     * 是否调整所有盘盈亏数据 1：是
     */
    private Integer adjustAllLineFlag;

    /**
     * 行id集合
     */
    private List<Long> lineIds;

    /**
     * 行id
     */
    private Long lineId;

    /**
     * 是否是来自流程平台审批时的查询请求 1：是
     */
    private Integer workFlowRequestFlag;

    /**
     * 行集合
     */
    List<CheckDifferenceListLineReqDTO> checkDifferenceListLineReqDTOS;
}
