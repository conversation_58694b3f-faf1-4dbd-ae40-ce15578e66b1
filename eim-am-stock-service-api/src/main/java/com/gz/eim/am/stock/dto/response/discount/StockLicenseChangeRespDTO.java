package com.gz.eim.am.stock.dto.response.discount;

import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.base.dto.response.file.SysAttachDTO;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 3/30/21 8:23 下午
 * @description
 */
@ToString
@Data
public class StockLicenseChangeRespDTO {

    /**
     * 单据id
     */
    private Long id;

    /**
     * 变更申请单号
     */
    private String changeNo;

    /**
     * 执照物料名称
     */
    private String suppliesName;

    /**
     * 执照物料编码
     */
    private String suppliesCode;

    /**
     * 执照状态
     */
    private Integer assetsStatus;

    /**
     * 执照状态
     */
    private String assetsStatusName;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 计划变更时间
     */
    private Date changeTime;

    /**
     * 单据状态
     */
    private Integer changeStatus;

    /**
     * 单据状态名称
     */
    private String changeStatusName;

    /**
     * 执照仓库
     */
    private String warehouseCode;

    /**
     * 执照仓库名称
     */
    private String warehouseCodeName;

    /**
     * 仓库管理员
     */
    private String linkman;

    /**
     * 仓库管理员名称
     */
    private String linkmanName;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人名称
     */
    private String createdByName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 变更的行信息集合
     */
    private List<StockLicenseChangeItemsRespDTO> stockLicenseChangeItemsRespDTOList;


    /**
     * 附件信息  不含下载url 预览url 缩略图url
     */
    private List<SysAttachDTO> sysAttachDTOS;


    /**
     * 返回附件信息 下载url  预览url  缩略图url
     */
    private List<PictureMetaRespDTO> pictureMetaRespDTOList;

}
