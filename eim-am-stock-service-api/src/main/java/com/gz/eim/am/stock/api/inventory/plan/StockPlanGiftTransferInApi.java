package com.gz.eim.am.stock.api.inventory.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/13
 * @description: 计划礼品调拨调入api
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockPlanGiftTransferInApi {

    /**
     * 礼品/京东卡采购计划入库单分页查询
     *
     * @param inventoryInPlanSearchReqDTO
     * @return
     */
    @ApiOperation(value = "礼品/京东卡采购计划入库单分页查询", notes = "礼品/京东卡采购入库分页查询")
    @GetMapping(value = "/select")
    ResponseData selectCardOrGiftPurchasePlanInventoryIn(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO);

    /**
     * 礼品/京东卡采购计划入库单详情查询
     *
     * @param inventoryInPlanHeadId
     * @return
     */
    @ApiOperation(value = "礼品/京东卡采购计划入库分页查询", notes = "礼品/京东卡采购计划入库单详情查询")
    @GetMapping(value = "/select/{inventoryInPlanHeadId}")
    ResponseData selectCardOrGiftPurchasePlanInventoryInById(@PathVariable("inventoryInPlanHeadId") Long inventoryInPlanHeadId);


    /**
     * 礼品调拨入库单分页查询
     *
     * @param inventoryInPlanSearchReqDTO
     * @return
     */
    @ApiOperation(value = "计划礼品调拨入库单分页查询", notes = "计划礼品调拨入库单")
    @GetMapping(value = "/search")
    ResponseData selectPlanGiftTransferIn(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO);

    /**
     * 礼品调拨入库单详情查询
     *
     * @param inventoryInPlanHeadId
     * @return
     */
    @ApiOperation(value = "计划礼品调拨入库单详情查询", notes = "计划礼品调拨入库单")
    @GetMapping(value = "/search/{inventoryInPlanHeadId}")
    ResponseData selectPlanGiftTransferInById(@PathVariable("inventoryInPlanHeadId") Long inventoryInPlanHeadId);

    /**
     * 礼品调拨入库单入库
     *
     * @param inventoryInPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "计划礼品调拨入库单入库", notes = "计划礼品调拨入库单")
    @PutMapping(value = "inBound")
    ResponseData planGiftTransferInInBound(@RequestBody InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO);

    /**
     * 礼品调拨入库单驳回
     *
     * @param inventoryInPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "计划礼品调拨入库单驳回", notes = "计划礼品调拨入库单")
    @PutMapping(value = "reject")
    ResponseData planGiftTransferInReject(@RequestBody InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO);
}
