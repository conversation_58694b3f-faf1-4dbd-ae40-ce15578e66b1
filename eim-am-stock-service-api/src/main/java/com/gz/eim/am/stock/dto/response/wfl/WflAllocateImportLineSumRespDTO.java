package com.gz.eim.am.stock.dto.response.wfl;

import java.math.BigDecimal;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/9
 * @description: 工作流礼品调拨导入汇总
 */
public class WflAllocateImportLineSumRespDTO {
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 物料名称
     */
    private String suppliesName;
    /**
     * 汇总数量
     */
    private Integer sumNumber;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 总价值
     */
    private BigDecimal sumPrice;

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public Integer getSumNumber() {
        return sumNumber;
    }

    public void setSumNumber(Integer sumNumber) {
        this.sumNumber = sumNumber;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getSumPrice() {
        return sumPrice;
    }

    public void setSumPrice(BigDecimal sumPrice) {
        this.sumPrice = sumPrice;
    }

    @Override
    public String toString() {
        return "WflAllocateImportLineRespDTO{" +
                "suppliesCode='" + suppliesCode + '\'' +
                ", suppliesName='" + suppliesName + '\'' +
                ", sumNumber=" + sumNumber +
                ", unitPrice=" + unitPrice +
                ", sumPrice=" + sumPrice +
                '}';
    }
}
