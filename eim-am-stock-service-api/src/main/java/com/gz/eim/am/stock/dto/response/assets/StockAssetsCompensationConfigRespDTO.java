package com.gz.eim.am.stock.dto.response.assets;

import lombok.Data;

import java.util.Date;

/**
 * @className: StockAssetsCompensationConfigRespDTO
 * @description: 资产赔偿配置返回值
 * @author: <EMAIL>
 * @date: 2022/2/15
 **/
@Data
public class StockAssetsCompensationConfigRespDTO {
    private Long id;

    private String categoryCode;

    private String brand;

    private String cpu;

    private Integer status;

    private String remark;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer delFlag;

}
