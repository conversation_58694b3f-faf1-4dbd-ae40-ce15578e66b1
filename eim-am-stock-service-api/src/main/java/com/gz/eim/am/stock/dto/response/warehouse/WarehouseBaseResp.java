package com.gz.eim.am.stock.dto.response.warehouse;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-09-17 下午 9:17
 */
public class WarehouseBaseResp {

    /**
     * 名称
     */
    private String name;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库面积
     */
    private BigDecimal acreage;

    /**
     * 使用状态(1 正常 , 0 禁用)
     */
    private Integer status;

    /**
     * 国家
     */
    private String country;
    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市
     */
    private String city;
    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 仓库地址
     */
    private String address;
    /**
     * 仓库联系人工号
     */
    private String linkman;
    /**
     * 仓库联系人名称
     */
    private String linkManName;

    private String contactWay;

    private String busLargeRegion;

    private String busCity;

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public BigDecimal getAcreage() {
        return acreage;
    }

    public void setAcreage(BigDecimal acreage) {
        this.acreage = acreage;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }


    public String getLinkManName() {
        return linkManName;
    }

    public void setLinkManName(String linkManName) {
        this.linkManName = linkManName;
    }

    public String getContactWay() {
        return this.contactWay;
    }

    public void setContactWay(final String contactWay) {
        this.contactWay = contactWay;
    }

    public String getBusLargeRegion() {
        return this.busLargeRegion;
    }

    public void setBusLargeRegion(final String busLargeRegion) {
        this.busLargeRegion = busLargeRegion;
    }

    public String getBusCity() {
        return this.busCity;
    }

    public void setBusCity(final String busCity) {
        this.busCity = busCity;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseBaseResp{");
        sb.append("name='").append(name).append('\'');
        sb.append(", warehouseCode='").append(warehouseCode).append('\'');
        sb.append(", acreage=").append(acreage);
        sb.append(", status=").append(status);
        sb.append(", country='").append(country).append('\'');
        sb.append(", countryCode='").append(countryCode).append('\'');
        sb.append(", province='").append(province).append('\'');
        sb.append(", provinceCode='").append(provinceCode).append('\'');
        sb.append(", city='").append(city).append('\'');
        sb.append(", cityCode='").append(cityCode).append('\'');
        sb.append(", area='").append(area).append('\'');
        sb.append(", areaCode='").append(areaCode).append('\'');
        sb.append(", address='").append(address).append('\'');
        sb.append(", linkMan=").append(linkman);
        sb.append(", linkManName='").append(linkManName).append('\'');
        sb.append(", contactWay='").append(contactWay).append('\'');
        sb.append(", busLargeRegion='").append(busLargeRegion).append('\'');
        sb.append(", busCity='").append(busCity).append('\'');
        sb.append('}');
        return sb.toString();
    }
}