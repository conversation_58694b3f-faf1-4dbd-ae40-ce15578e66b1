package com.gz.eim.am.stock.dto.response.demand;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.response.UserRespDTO;
import com.gz.eim.am.stock.dto.response.order.plan.DeliveryPlanHeadRespDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @description: 资产领用头需求单请求类
 * @author: <EMAIL>
 * @date: 2021/10/11
 */
@Data
public class StockAssetsDemandHeadRespDTO {
    /**
     * 实际领用人地址
     */
    private String receiveAddress;
    /**
     * 领用日期
     */
    @JsonFormat(pattern ="yyyy-MM-dd")
    private Date receiveTime;
    /**
     * 实际领用人姓名
     */
    private String receiveUserName;
    /**
     * 实际领用人
     */
    private String receiveUser;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 职级名称
     */
    private String realUseUserHpsJobcdDescr;
    /**
     * 需求单号
     */
    private String demandNo;
    /**
     * 已拥有资产数量
     */
    private Integer applyAssetsNumber;
    /**
     * 领用仓库名称
     */
    private String defaultReceiveWarehouseName;
    /**
     * 领用仓库编码
     */
    private String defaultReceiveWarehouseCode;
    /**
     * 实际领用仓库编码
     */
    private String warehouseCode;
    /**
     * 实际领用仓库名称
     */
    private String warehouseName;
    /**
     * 实际领用仓库编码
     */
    private String realReceiveWarehouseCode;
    /**
     * 实际领用仓库名称
     */
    private String realReceiveWarehouseName;
    /**
     * 领用仓库库管员
     */
    private String defaultReceiveWarehouseLinkman;
    /**
     * 领用仓库库管员名称
     */
    private String defaultReceiveWarehouseLinkmanName;
    /**
     * 需求时间
     */
    @JsonFormat(pattern ="yyyy-MM-dd")
    private Date demandTime;
    /**
     * 资产领用原因代号
     */
    private Integer receiveReason;
    /**
     * 资产领用原因名称
     */
    private String receiveReasonName;
    /**
     * 申请人
     */
    private String applyUser;
    /**
     * 申请人名字
     */
    private String applyUserName;
    /**
     * 申请人所在详细位置
     */
    private String applyUserAddress;
    /**
     * 实际使用人部门id
     */
    private String realUseUserDeptId;
    /**
     * 实际使用人
     */
    private String realUseUser;
    /**
     * 实际使用人名称
     */
    private String realUseUserName;
    /**
     * 补充说明
     */
    private String remark;
    /**
     * 所选择的物料编码和数量集合
     */
    private List<StockAssetsDemandLineRespDTO> stockAssetsDemandLineRespDTOList;
    /**
     * 实际使用人基本信息
     */
    private UserRespDTO realUseUserInfo;
    /**
     * 实际使用人的拥有的资产列表
     */
    private List<AssetsDTO> realUseUserAssetsList;
    /**
     * 单据状态
     */
    private Integer status;
    /**
     * 单据状态名称
     */
    private String statusName;
    /**
     * 领用人集合字符串
     */
    private String receiverUserNameAndCodeList;
    /**
     * 申请人所在省份
     */
    private String applyUserProvince;
    /**
     * 申请人所在市
     */
    private String applyUserCity;
    /**
     * 申请人所在省份描述
     */
    private String applyUserProvinceDesc;
    /**
     * 申请人所在市描述
     */
    private String applyUserCityDesc;
    /**
     * 计划出库单集合
     */
    private List<DeliveryPlanHeadRespDTO> deliveryPlanHeadRespDTOList;
    /**
     * 申请人所在区
     */
    private String applyUserRegion;
    /**
     * 申请人所在区描述
     */
    private String applyUserRegionDesc;
    /**
     * 申请人电话
     */
    private String applyUserPhone;


}