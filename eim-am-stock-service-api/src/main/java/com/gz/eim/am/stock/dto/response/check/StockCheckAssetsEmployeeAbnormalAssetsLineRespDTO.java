package com.gz.eim.am.stock.dto.response.check;

import lombok.Data;

/**
 * @className: StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO
 * @description: 库存盘点资产员工异常资产响应行
 * @author: <EMAIL>
 * @date: 2023/11/10
 **/
@Data
public class StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO {
    /**
     * 任务详情id
     */
    private Long taskDetailId;
    /**
     * 资产id
     */
    private Long assetsId;
    /**
     * 资产编码
     */
    private String assetsCode;
    /**
     * 设备序列号
     */
    private String snCode;
    /**
     * 资产名称
     */
    private String assetsName;
    /**
     * 资产分类
     */
    private String category;
    /**
     * 资产状态
     */
    private String statusName;
    /**
     * 领用日期
     */
    private String holderTime;
    /**
     * 资产管理员工号
     */
    private String assetsKeeper;
    /**
     * 资产管理员姓名
     */
    private String assetsKeeperName;
    /**
     * 异常类型
     */
    private String errMessage;
    /**
     * 异常说明
     */
    private String errDesc;
    /**
     * 处理结果
     */
    private String dealResult;
}
