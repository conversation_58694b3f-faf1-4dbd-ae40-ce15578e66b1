package com.gz.eim.am.stock.dto.response.supplies;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class StockSuppliesPurchaseExtRespDTO {
    private Long id;

    private String suppliesCode;

    private Integer warehouseType;

    private String addPurchaseMark;

    private String costItemCode;

    private String costItemName;

    private Integer isValid;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;


}
