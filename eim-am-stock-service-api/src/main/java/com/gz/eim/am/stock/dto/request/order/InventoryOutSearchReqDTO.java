package com.gz.eim.am.stock.dto.request.order;


import com.gz.eim.am.stock.dto.request.PageReqDTO;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/10/28
 * @description: 出库单查询
 */
public class InventoryOutSearchReqDTO extends PageReqDTO {
    /**
     * 出库单编号
     */
    private String deliveryNo;
    /**
     * 业务单据号
     */
    private String bizNo;

    /**
     * 出库单状态
     */
    private Integer status;
    /**
     * 仓库编码：调出仓库
     */
    private String warehouseCode;
    /**
     * 仓库编码：调入仓库
     */
    private String inWarehouseCode;
    /**
     * 出库人工号
     */
    private String dutyUser;
    /**
     * 实际使用人工号
     */
    private String useUser;
    /**
     * 制单日期:开始
     */
    private String beginBillingTime;
    /**
     * 制单日期：结束
     */
    private String endBillingTime;
    /**
     *  需求日期：开始
     */
    private String beginPlanOutTime;
    /**
     * 需求日期：结束
     */
    private String endPlanOutTime;
    /**
     * 制单人工号
     */
    private String billingUser;
    /**
     * 出库单类别
     */
    private Integer outStockType;

    /**
     * 仓库编码集合
     */
    private List<String> codes;

    /**
     * 单据类型集合
     */
    private List<Integer> outStockTypeList;

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getUseUser() {
        return useUser;
    }

    public void setUseUser(String useUser) {
        this.useUser = useUser;
    }

    public String getBeginBillingTime() {
        return beginBillingTime;
    }

    public void setBeginBillingTime(String beginBillingTime) {
        this.beginBillingTime = beginBillingTime;
    }

    public String getEndBillingTime() {
        return endBillingTime;
    }

    public void setEndBillingTime(String endBillingTime) {
        this.endBillingTime = endBillingTime;
    }

    public String getBeginPlanOutTime() {
        return beginPlanOutTime;
    }

    public void setBeginPlanOutTime(String beginPlanOutTime) {
        this.beginPlanOutTime = beginPlanOutTime;
    }

    public String getEndPlanOutTime() {
        return endPlanOutTime;
    }

    public void setEndPlanOutTime(String endPlanOutTime) {
        this.endPlanOutTime = endPlanOutTime;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public Integer getOutStockType() {
        return outStockType;
    }

    public void setOutStockType(Integer outStockType) {
        this.outStockType = outStockType;
    }

    public List<String> getCodes() {
        return codes;
    }

    public void setCodes(List<String> codes) {
        this.codes = codes;
    }

    public List<Integer> getOutStockTypeList() {
        return outStockTypeList;
    }

    public void setOutStockTypeList(List<Integer> outStockTypeList) {
        this.outStockTypeList = outStockTypeList;
    }

    @Override
    public String toString() {
        return "InventoryOutSearchReqDTO{" +
                "deliveryNo='" + deliveryNo + '\'' +
                ", bizNo='" + bizNo + '\'' +
                ", status=" + status +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", inWarehouseCode='" + inWarehouseCode + '\'' +
                ", dutyUser='" + dutyUser + '\'' +
                ", useUser='" + useUser + '\'' +
                ", beginBillingTime='" + beginBillingTime + '\'' +
                ", endBillingTime='" + endBillingTime + '\'' +
                ", beginPlanOutTime='" + beginPlanOutTime + '\'' +
                ", endPlanOutTime='" + endPlanOutTime + '\'' +
                ", billingUser='" + billingUser + '\'' +
                ", outStockType=" + outStockType +
                ", codes=" + codes +
                ", outStockTypeList=" + outStockTypeList +
                '}';
    }
}
