package com.gz.eim.am.stock.dto.request.warehouse;

import com.gz.eim.am.stock.dto.request.supplies.SuppliesCategoryReqDTO;

import java.util.List;

/**
 * 新建仓
 * <AUTHOR>
 * @date  2019-09-17 下午 9:01
 */

public class WarehouseReqDTO{

    /**
     * 仓库主键
     */
    private Long warehouseId;

    /**
     * 父仓库主键
     */
    private Long parentId;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 仓库编码
     */
    private String code;

    /**
     * 仓库类型
     */
    private Integer type;

    /**
     * 所属成本中心类型（1，部门；2，项目）
     */
    private Integer costCenterType;

    /**
     * 所属成本中心编号
     */
    private String costCenterCode;

    /**
     * 物理类型 1，实体仓；2，虚拟仓
     */
    private Integer physicalType;

    /**
     * 仓库用途：1，发运仓；2，存储仓
     */
    private Integer purpose;

    /**
     * 状态(1 正常 , 0 禁用)
     */
    private Integer status;

    /**
     * 仓库基本信息
     */
    private WarehouseBaseReq warehouseBase;

    /**
     * 仓库负责的物料分类
     */
    private List<String> category;

    /**
     * 库管员, 3.0废弃
     */
    private List<RoleKeeperReq> keeper;

    /**
     * 仓库关联物料分类
     */
    private List<SuppliesCategoryReqDTO> suppliesCategoryList;

    /**
     * 仓库负责的业务类型 3.0废弃
     */
    private List<String> business;

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getCostCenterType() {
        return costCenterType;
    }

    public void setCostCenterType(Integer costCenterType) {
        this.costCenterType = costCenterType;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public Integer getPhysicalType() {
        return physicalType;
    }

    public void setPhysicalType(Integer physicalType) {
        this.physicalType = physicalType;
    }

    public Integer getPurpose() {
        return purpose;
    }

    public void setPurpose(Integer purpose) {
        this.purpose = purpose;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public WarehouseBaseReq getWarehouseBase() {
        return warehouseBase;
    }

    public void setWarehouseBase(WarehouseBaseReq warehouseBase) {
        this.warehouseBase = warehouseBase;
    }

    public List<String> getCategory() {
        return category;
    }

    public void setCategory(List<String> category) {
        this.category = category;
    }

    public List<String> getBusiness() {
        return business;
    }

    public void setBusiness(List<String> business) {
        this.business = business;
    }

    public List<RoleKeeperReq> getKeeper() {
        return keeper;
    }

    public void setKeeper(List<RoleKeeperReq> keeper) {
        this.keeper = keeper;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<SuppliesCategoryReqDTO> getSuppliesCategoryList() {
        return this.suppliesCategoryList;
    }

    public void setSuppliesCategoryList(final List<SuppliesCategoryReqDTO> suppliesCategoryList) {
        this.suppliesCategoryList = suppliesCategoryList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseReqDTO{");
        sb.append("warehouseId=").append(warehouseId);
        sb.append(", parentId=").append(parentId);
        sb.append(", name='").append(name).append('\'');
        sb.append(", code='").append(code).append('\'');
        sb.append(", type=").append(type);
        sb.append(", costCenterType=").append(costCenterType);
        sb.append(", costCenterCode='").append(costCenterCode).append('\'');
        sb.append(", physicalType=").append(physicalType);
        sb.append(", purpose=").append(purpose);
        sb.append(", status=").append(status);
        sb.append(", warehouseBase=").append(warehouseBase);
        sb.append(", category=").append(category);
        sb.append(", keeper=").append(keeper);
        sb.append(", suppliesCategoryList=").append(suppliesCategoryList);
        sb.append(", business=").append(business);
        sb.append('}');
        return sb.toString();
    }
}