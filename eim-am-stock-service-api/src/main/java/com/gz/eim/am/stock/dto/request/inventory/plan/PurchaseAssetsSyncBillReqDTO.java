package com.gz.eim.am.stock.dto.request.inventory.plan;

import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: weijunjie
 * @date: 2020/10/14
 * @description 采购资产同步付款信息
 */
@ToString
public class PurchaseAssetsSyncBillReqDTO {
    /**
     * 验收单行编码
     */
    private String receiveItemNo;
    /**
     * 结算单编码
     */
    private String accountantCode;
    /**
     * 结算单行编码
     */
    private String accountantLineCode;
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 结算单价
     */
    private BigDecimal statementUnitPrice;
    /**
     * 结算不含税价
     */
    private BigDecimal statementPriceExcludingTax;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 付款时间
     */
    private Date payDate;
    /**
     * 发票编码
     */
    private String billCode;
    /**
     * 账单结算的资产数量
     */
    private Integer accountNum;

    public String getReceiveItemNo() {
        return receiveItemNo;
    }

    public void setReceiveItemNo(String receiveItemNo) {
        this.receiveItemNo = receiveItemNo;
    }

    public String getAccountantCode() {
        return accountantCode;
    }

    public void setAccountantCode(String accountantCode) {
        this.accountantCode = accountantCode;
    }

    public String getAccountantLineCode() {
        return accountantLineCode;
    }

    public void setAccountantLineCode(String accountantLineCode) {
        this.accountantLineCode = accountantLineCode;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public Integer getAccountNum() {
        return accountNum;
    }

    public void setAccountNum(Integer accountNum) {
        this.accountNum = accountNum;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public BigDecimal getStatementUnitPrice() {
        return statementUnitPrice;
    }

    public void setStatementUnitPrice(BigDecimal statementUnitPrice) {
        this.statementUnitPrice = statementUnitPrice;
    }

    public BigDecimal getStatementPriceExcludingTax() {
        return statementPriceExcludingTax;
    }

    public void setStatementPriceExcludingTax(BigDecimal statementPriceExcludingTax) {
        this.statementPriceExcludingTax = statementPriceExcludingTax;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }
}
