package com.gz.eim.am.stock.dto.request.discount;

import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 3/30/21 8:22 下午
 * @description
 */
@ToString
@Data
public class StockLicenseChangeReqDTO {

    private Long id;

    private String changeNo;

    private String suppliesName;

    private String suppliesCode;

    private String changeReason;

    private String changeDate;

    private Date changeTime;

    private Integer changeStatus;

    private String warehouseCode;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    /**
     * 上传文件集合
     */
    private List<SysAttachReqDTO> attachList;

    /**
     * 变更行信息集合
     */
    private List<StockLicenseChangeItemsReqDTO> stockLicenseChangeItemsReqDTOList;

}
