package com.gz.eim.am.stock.dto.response.manage;

import lombok.Data;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/2/5
 * @description 库存量详情DTO
 */
@Data
public class StockSuppliesConfigDetailRespDTO {
    /**
     * 库存详情id
     */
    private Long configDetailId;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     *
     */
    private String version;
    /**
     *
     */
    private Integer configType;
    /**
     * 批次号或序列号
     */
    private String manageNo;
    /**
     * 数量
     */
    private Long allNumber;
}
