package com.gz.eim.am.stock.api.inventory.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;

import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: hedahong
 * @date: 2019-12-25 PM 4:28
 * @description: 资产调拨单-计划出库单api
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockPlanAssetsTransferInApi {

    /**
     * 礼品调拨入库单分页查询
     *
     * @param inventoryInPlanSearchReqDTO
     * @return
     */
    @ApiOperation(value = "计划资产调拨入库单分页查询", notes = "计划资产调拨入库单")
    @GetMapping(value = "/search")
    ResponseData selectPlanAssetsTransferIn(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO);

    /**
     * 资产调拨入库单详情查询
     *
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    @ApiOperation(value = "计划资产调拨入库单详情查询", notes = "计划资产调拨入库单")
    @GetMapping(value = "/search/detail")
    ResponseData selectPlanAssetsTransferInById(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 资产调拨入库单入库
     *
     * @param inventoryInPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "计划资产调拨入库单入库", notes = "计划资产调拨入库单")
    @PutMapping(value = "inBound")
    ResponseData planAssetsTransferInInBound(@RequestBody InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO);

    /**
     * 资产调拨入库单驳回
     *
     * @param inventoryInPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "计划资产调拨入库单驳回", notes = "计划资产调拨入库单")
    @PutMapping(value = "reject")
    ResponseData planAssetsTransferInReject(@RequestBody InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO);



    /**
     * 资产调拨入库单取消
     *
     * @param inventoryInPlanHeadId
     * @return
     */
    @ApiOperation(value = "资产调拨入库单取消", notes = "资产调拨入库单取消")
    @GetMapping(value = "/cancel/{inventoryInPlanHeadId}")
    ResponseData cancelPlanAssetsTransferInById(@PathVariable("inventoryInPlanHeadId") Long inventoryInPlanHeadId);
}
