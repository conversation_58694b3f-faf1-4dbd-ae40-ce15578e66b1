package com.gz.eim.am.stock.dto.request.inventory;

import lombok.Data;
import lombok.ToString;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/5/21
 * @description: 资产采购入库batch
 */
@Data
@ToString
public class InventoryAssetImportBatchReqDTO {
    /**
     * 新批code
     */
    private String newBatchCode;
    /**
     * 已绑定批code
     */
    private String bindBatchCode;
    /**
     * 资产信息集合
     */
    private List<InventoryAssetImportReqDTO> inventoryAssetImportBatchReqDTOList;

    /**
     * 类型 1 执照入库
     */
    private Integer type;
}
