package com.gz.eim.am.stock.api.inventory;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportBatchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO;
import io.swagger.annotations.ApiOperation;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: lishuyang
 * @date: 2020/5/22
 * @description: 资产入库临时API
 */
public interface StockInventoryAssetImportApi {
    /**
     * 文件导入
     * @param file
     * @return
     */
    @ApiOperation(value = "文件导入", notes = "文件")
    @PostMapping(value = "/import/{batchCode}")
    ResponseData assetPlanAssetFileImport(@RequestParam("file") MultipartFile file, @PathVariable("batchCode")String batchCode,@RequestParam("code") String code);

    /**
     * 生成批标识
     * @return
     */
    @ApiOperation(value = "生成批标识", notes = "批标识")
    @GetMapping(value = "/generate-batch-code")
    ResponseData generateImportBatchCode();

    /**
     * 保存
     * @param inventoryAssetImportBatchReqDTO
     * @return
     */
    @ApiOperation(value = "保存", notes = "资产入库临时数据")
    @PostMapping(value = "/save")
    ResponseData saveAssetPlanAssetImport(@RequestBody InventoryAssetImportBatchReqDTO inventoryAssetImportBatchReqDTO);

    /**
     * 资产采购行关联数据查询
     * @param inventoryAssetImportSearchReqDTO
     * @return
     */
    @ApiOperation(value = "数据查询", notes = "资产入库临时数据")
    @GetMapping(value = "/search")
    ResponseData selectAssetPlanAssetByBatchCode(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO);
    /**
     * 资产不同类型尾表字段查询
     * @param code      物料编码
     * @return
     */
    @ApiOperation(value = "资产不同类型尾表字段查询", notes = "资产不同类型尾表字段查询")
    @GetMapping(value = "/initAttr")
    ResponseData assetPurchaseInitAttr(@RequestParam("code") String code);

    /**
     * 根据物料所属资产类型，导出相应的excel模版
     * @param code 物料编码
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value = "excel模版导出", notes = "excel模版导出")
    @GetMapping(value = "/download")
    ResponseData download(String code, HttpServletRequest request, HttpServletResponse response);

}
