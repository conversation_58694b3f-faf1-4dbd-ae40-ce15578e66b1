package com.gz.eim.am.stock.api.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: weijunjie
 * @date: 2021/2/20
 * @description
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId = "eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockCheckResultAdjustControllerApi {
    /**
     * 盘点异常数据查询接口
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    @ApiOperation(value = "盘点异常数据查询接口", notes = "盘点异常数据查询接口")
    @GetMapping(value = "/select")
    ResponseData resultAdjustDataSelect(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO);

    /**
     * 盘点差异清单提交审批
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    @ApiOperation(value = "盘点差异清单提交审批", notes = "盘点差异清单提交审批")
    @PostMapping(value = "/approve")
    ResponseData approveCheckResult(@RequestBody CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO);

    /**
     * 盘盈入库单据分页查询接口
     * @param adjustResultReqDTO
     * @return
     */
    @ApiOperation(value = "盘盈入库单据分页查询接口", notes = "盘盈入库单据分页查询接口")
    @GetMapping(value = "/query/more")
    ResponseData checkInventoryQuery(AdjustResultReqDTO adjustResultReqDTO);

    /**
     * 盘亏入库单据分页查询接口
     * @param adjustResultReqDTO
     * @return
     */
    @ApiOperation(value = "盘亏入库单据分页查询接口", notes = "盘亏入库单据分页查询接口")
    @GetMapping(value = "/query/less")
    ResponseData checkDeliveryQuery(AdjustResultReqDTO adjustResultReqDTO);

    /**
     * 明细分页查询接口
     * @param adjustResultReqDTO
     * @return
     */
    @ApiOperation(value = "明细分页查询接口", notes = "明细分页查询接口")
    @GetMapping(value = "/detail")
    ResponseData checkAdjustQueryDetail(AdjustResultReqDTO adjustResultReqDTO);
}
