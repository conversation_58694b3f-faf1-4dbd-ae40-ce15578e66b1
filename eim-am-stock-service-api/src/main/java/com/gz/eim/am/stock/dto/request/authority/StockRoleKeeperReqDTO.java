package com.gz.eim.am.stock.dto.request.authority;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @author: weijunjie
 * @date: 2020/5/18
 * @description
 */
@Data
@ToString
public class StockRoleKeeperReqDTO extends PageReqDTO {

    private Long roleKeeperId;

    private String keeperCode;

    private String deptId;

    private String contactWay;

    private Long roleId;

    private Integer status;

    private Integer delFlag;

    private String createdBy;

    private String createdAt;

    private String updatedBy;

    private String updatedAt;
}
