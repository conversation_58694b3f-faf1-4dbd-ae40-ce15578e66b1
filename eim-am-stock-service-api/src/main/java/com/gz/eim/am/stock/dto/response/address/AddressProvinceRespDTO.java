package com.gz.eim.am.stock.dto.response.address;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: wei<PERSON><PERSON>e
 * @date: 2021/4/28
 * @description
 */
@Data
@ToString
public class AddressProvinceRespDTO {
    /**
     * 省级代码
     */
    private String provinceCode;
    /**
     * 省级名称
     */
    private String provinceName;
    /**
     * 省级下面的地级市
     */
    private List<AddressCityRespDTO> addressCityRespDTOList;
}
