package com.gz.eim.am.stock.dto.response.supplies;

import java.util.List;

/**
 * 库存查询
 * <AUTHOR>
 * @date 2019-09-17 下午 9:21
 */
public class SuppliesConfigRespDTO {
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 物料名称
     */
    private String suppliesName;
    /**
     * 物料描述
     */
    private String suppliesRemark;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     *结算中心编码
     */
    private String costCenterCode;
    /**
     *结算中心名称
     */
    private String costCenterName;
    /**
     *总库存数
     */
    private String totalNumber;
    /**
     *可用库存数
     */
    private String useNumber;
    /**
     *物料单位
     */
    private String unit;

    /**
     * 序列号集合，用于gps批量出库查询
     */
    private List<String> snNoList;
    /**
     * 物料下序列号的数量，用于gps批量出库查询
     */
    private Integer suppliesToSnnoNum;
    /**
     * 序列号
     */
    private String snNo;
    /**
     * 库存状态 0：在库 1：已出库
     */
    private String configStatusName;
    /**
     * 安装状态 0：未安装 1：安装
     */
    private String installStatusName;

    /**
     * 物料类型
     */
    private Integer type;


    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public String getTotalNumber() {
        return totalNumber;
    }

    public void setTotalNumber(String totalNumber) {
        this.totalNumber = totalNumber;
    }

    public String getUseNumber() {
        return useNumber;
    }

    public void setUseNumber(String useNumber) {
        this.useNumber = useNumber;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public List<String> getSnNoList() {
        return snNoList;
    }

    public void setSnNoList(List<String> snNoList) {
        this.snNoList = snNoList;
    }

    public Integer getSuppliesToSnnoNum() {
        return suppliesToSnnoNum;
    }

    public void setSuppliesToSnnoNum(Integer suppliesToSnnoNum) {
        this.suppliesToSnnoNum = suppliesToSnnoNum;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getConfigStatusName() {
        return configStatusName;
    }

    public void setConfigStatusName(String configStatusName) {
        this.configStatusName = configStatusName;
    }

    public String getInstallStatusName() {
        return installStatusName;
    }

    public void setInstallStatusName(String installStatusName) {
        this.installStatusName = installStatusName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }


    public String getSuppliesRemark() {
        return suppliesRemark;
    }

    public void setSuppliesRemark(String suppliesRemark) {
        this.suppliesRemark = suppliesRemark;
    }

    @Override
    public String toString() {
        return "SuppliesConfigRespDTO{" +
                "suppliesCode='" + suppliesCode + '\'' +
                ", suppliesName='" + suppliesName + '\'' +
                ", suppliesRemark='" + suppliesRemark + '\'' +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", warehouseName='" + warehouseName + '\'' +
                ", costCenterCode='" + costCenterCode + '\'' +
                ", costCenterName='" + costCenterName + '\'' +
                ", totalNumber=" + totalNumber +
                ", useNumber=" + useNumber +
                ", unit='" + unit + '\'' +
                ", snNoList=" + snNoList +
                ", suppliesToSnnoNum=" + suppliesToSnnoNum +
                ", snNo='" + snNo + '\'' +
                ", configStatusName='" + configStatusName + '\'' +
                ", installStatusName='" + installStatusName + '\'' +
                ", type=" + type +
                '}';
    }
}
