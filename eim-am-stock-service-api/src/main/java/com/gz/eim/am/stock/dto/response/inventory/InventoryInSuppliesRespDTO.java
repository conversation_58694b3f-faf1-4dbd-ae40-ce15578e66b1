package com.gz.eim.am.stock.dto.response.inventory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/10/28
 * @description: 入库单明细
 */
public class  InventoryInSuppliesRespDTO {

    /**
     * 入库单明细主键id
     */
    private Long inSuppliesId;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 物料名称
     */
    private String suppliesName;
    /**
     * 单位名称
     */
    private String unitName;
    /**
     * 计划入库数量
     */
    private Integer number;
    /**
     * 实际入库数量
     */
    private Integer realNumber;
    /**
     * 状态
     */
    private Integer status;

    /**
     * 差异原因
     */
    private String differenceReason;
    /**
     * 标签生成节点
     */
    private Integer printLabelType;
    /**
     * 标签生成节点名称
     */
    private String printLabelTypeName;
    /**
     * 实际入库时间
     */
    private String inventoryInTime;
    /**
     * 物料描述
     */
    private String suppliesRemark;

    /**
     * 关联资产编号
     */
    private List<String> assets;

    /**
     * 入库计划单详情id
     */
    private  Long inventoryInPlanLineId;

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }


    public String getDifferenceReason() {
        return differenceReason;
    }

    public void setDifferenceReason(String differenceReason) {
        this.differenceReason = differenceReason;
    }

    public Integer getPrintLabelType() {
        return printLabelType;
    }

    public void setPrintLabelType(Integer printLabelType) {
        this.printLabelType = printLabelType;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPrintLabelTypeName() {
        return printLabelTypeName;
    }

    public void setPrintLabelTypeName(String printLabelTypeName) {
        this.printLabelTypeName = printLabelTypeName;
    }

    public Long getInSuppliesId() {
        return inSuppliesId;
    }

    public void setInSuppliesId(Long inSuppliesId) {
        this.inSuppliesId = inSuppliesId;
    }

    public String getInventoryInTime() {
        return inventoryInTime;
    }

    public void setInventoryInTime(String inventoryInTime) {
        this.inventoryInTime = inventoryInTime;
    }

    public String getSuppliesRemark() {
        return suppliesRemark;
    }

    public void setSuppliesRemark(String suppliesRemark) {
        this.suppliesRemark = suppliesRemark;
    }

    public List<String> getAssets() {
        return this.assets;
    }

    public void setAssets(final List<String> assets) {
        this.assets = assets;
    }

    public Long getInventoryInPlanLineId() {
        return this.inventoryInPlanLineId;
    }

    public void setInventoryInPlanLineId(final Long inventoryInPlanLineId) {
        this.inventoryInPlanLineId = inventoryInPlanLineId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InventoryInSuppliesRespDTO{");
        sb.append("inSuppliesId=").append(inSuppliesId);
        sb.append(", suppliesCode='").append(suppliesCode).append('\'');
        sb.append(", suppliesName='").append(suppliesName).append('\'');
        sb.append(", unitName='").append(unitName).append('\'');
        sb.append(", number=").append(number);
        sb.append(", realNumber=").append(realNumber);
        sb.append(", status=").append(status);
        sb.append(", differenceReason='").append(differenceReason).append('\'');
        sb.append(", printLabelType=").append(printLabelType);
        sb.append(", printLabelTypeName='").append(printLabelTypeName).append('\'');
        sb.append(", inventoryInTime='").append(inventoryInTime).append('\'');
        sb.append(", suppliesRemark='").append(suppliesRemark).append('\'');
        sb.append(", assets=").append(assets);
        sb.append(", inventoryInPlanLineId=").append(inventoryInPlanLineId);
        sb.append('}');
        return sb.toString();
    }
}
