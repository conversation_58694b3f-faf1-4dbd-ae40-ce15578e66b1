package com.gz.eim.am.stock.api.inventory.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/26
 * @description: 资产归还入库api
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockPlanAssetRemandApi {
    /**
     * 保存计划资产归还单
     * @param inventoryInPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "保存计划资产归还单", notes = "计划资产采购归还单")
    @PostMapping(value = "/save")
    ResponseData savePlanAssetRemand(@RequestBody InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO);

    /**
     * 计划资产归还单分页查询
     * @param inventoryInPlanSearchReqDTO
     * @return
     */
    @ApiOperation(value = "计划资产归还单分页查询", notes = "计划资产归还单")
    @GetMapping(value = "/search")
    ResponseData selectPlanAssetRemand(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO);

    /**
     * 计划资产归还单详情查询
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    @ApiOperation (value = "计划资产归还单详情查询", notes = "计划资产归还单")
    @GetMapping(value = "/search/detail")
    ResponseData selectPlanAssetRemandById(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 计划资产采购入库单入库
     * @param inventoryInPlanHeadReqDTO
     * @return
     */
    @ApiOperation (value = "计划资产归还单归还", notes = "计划资计划资产归还单")
    @PutMapping(value = "inBound")
    ResponseData assetPlanAssetRemandInBound(@RequestBody InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO);

    /**
     * 计划资产归还单打印
     * @param inventoryInPlanHeadId
     * @return
     */
    @ApiOperation (value = "计划资产归还单打印", notes = "计划资计划资产归还单")
    @GetMapping(value = "print/{inventoryInPlanHeadId}")
    ResponseData assetPlanAssetRemandPrint(@PathVariable("inventoryInPlanHeadId")Long inventoryInPlanHeadId);

    /**
     * 查询持有人的资产
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    @ApiOperation (value = "计划资产归还单归还", notes = "资产")
    @GetMapping(value = "/assets/search")
    ResponseData selectAssetByRemand(@RequestParam("holder") String holder, @RequestParam("remandWarehouseCode")String remandWarehouseCode, @RequestParam("assetsCode")String assetsCode,@RequestParam("pageNum")Integer pageNum,@RequestParam("pageSize")Integer pageSize);

    /**
     * 资产采购计划入库单初始化
     * @param batchId
     * @return
     */
    @ApiOperation(value = "资产采购计划入库单初始化", notes = "计划资产采购入库单")
    @GetMapping(value = "/initialization")
    ResponseData assetRemandInitialization(@RequestParam("batchId") Integer batchId);

    /**
     * 计划资产归还单取消
     * @param inventoryInPlanHeadId
     * @return
     */
    @ApiOperation (value = "计划资产归还单取消", notes = "计划资产归还单取消")
    @GetMapping(value = "/cancel/{inventoryInPlanHeadId}")
    ResponseData cancelPlanAssetRemandById(@PathVariable("inventoryInPlanHeadId")Long inventoryInPlanHeadId);
}
