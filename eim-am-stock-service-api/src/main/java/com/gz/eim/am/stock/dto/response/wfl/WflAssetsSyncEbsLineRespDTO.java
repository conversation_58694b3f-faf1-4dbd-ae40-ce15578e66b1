package com.gz.eim.am.stock.dto.response.wfl;

import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: weijunjie
 * @date: 2020/10/13
 * @description
 */
@ToString
public class WflAssetsSyncEbsLineRespDTO {

    /**
     * 资产编码
     */
    private String assetsCode;

    /**
     * 资产财务大类
     */
    private String assetCategorySegment1;

    /**
     * 资产财务小类
     */
    private String assetCategorySegment2;

    /**
     * 资产来源
     */
    private String assetKeySegment1;

    /**
     * 实物大类
     */
    private String assetKeySegment2;

    /**
     * 实物小类
     */
    private String assetKeySegment3;

    /**
     * 业务线
     */
    private String bookTypeCode;

    /**
     * 资产启用日期
     */
    private String datePlacedInService;

    /**
     * 资产名称
     */
    private String description;

    /**
     * 折旧方法
     */
    private String deprnMethodCode;

    /**
     * 资产成本
     */
    private BigDecimal fixedAssetsCost;

    /**
     * 发票编号
     */
    private String invoiceNumber;

    /**
     * 资产使用公司
     */
    private String locSegment1;

    /**
     * 资产使用部门
     */
    private String locSegment2;

    /**
     * 资产实物地点
     */
    private String locSegment3;

    /**
     * 资产寿命
     */
    private Integer lifeInMonths;

    /**
     * 资产型号
     */
    private String modelNumber;

    /**
     * 公司段
     */
    private String accSegment1;

    /**
     * bu段
     */
    private String accSegment2;

    /**
     * 成本中心段
     */
    private String accSegment3;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetCategorySegment1() {
        return assetCategorySegment1;
    }

    public void setAssetCategorySegment1(String assetCategorySegment1) {
        this.assetCategorySegment1 = assetCategorySegment1;
    }

    public String getAssetCategorySegment2() {
        return assetCategorySegment2;
    }

    public void setAssetCategorySegment2(String assetCategorySegment2) {
        this.assetCategorySegment2 = assetCategorySegment2;
    }

    public String getAssetKeySegment1() {
        return assetKeySegment1;
    }

    public void setAssetKeySegment1(String assetKeySegment1) {
        this.assetKeySegment1 = assetKeySegment1;
    }

    public String getAssetKeySegment2() {
        return assetKeySegment2;
    }

    public void setAssetKeySegment2(String assetKeySegment2) {
        this.assetKeySegment2 = assetKeySegment2;
    }

    public String getAssetKeySegment3() {
        return assetKeySegment3;
    }

    public void setAssetKeySegment3(String assetKeySegment3) {
        this.assetKeySegment3 = assetKeySegment3;
    }

    public String getBookTypeCode() {
        return bookTypeCode;
    }

    public void setBookTypeCode(String bookTypeCode) {
        this.bookTypeCode = bookTypeCode;
    }

    public String getDatePlacedInService() {
        return datePlacedInService;
    }

    public void setDatePlacedInService(String datePlacedInService) {
        this.datePlacedInService = datePlacedInService;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDeprnMethodCode() {
        return deprnMethodCode;
    }

    public void setDeprnMethodCode(String deprnMethodCode) {
        this.deprnMethodCode = deprnMethodCode;
    }

    public BigDecimal getFixedAssetsCost() {
        return fixedAssetsCost;
    }

    public void setFixedAssetsCost(BigDecimal fixedAssetsCost) {
        this.fixedAssetsCost = fixedAssetsCost;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getLocSegment1() {
        return locSegment1;
    }

    public void setLocSegment1(String locSegment1) {
        this.locSegment1 = locSegment1;
    }

    public String getLocSegment2() {
        return locSegment2;
    }

    public void setLocSegment2(String locSegment2) {
        this.locSegment2 = locSegment2;
    }

    public String getLocSegment3() {
        return locSegment3;
    }

    public void setLocSegment3(String locSegment3) {
        this.locSegment3 = locSegment3;
    }

    public Integer getLifeInMonths() {
        return lifeInMonths;
    }

    public void setLifeInMonths(Integer lifeInMonths) {
        this.lifeInMonths = lifeInMonths;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
    }

    public String getAccSegment1() {
        return accSegment1;
    }

    public void setAccSegment1(String accSegment1) {
        this.accSegment1 = accSegment1;
    }

    public String getAccSegment2() {
        return accSegment2;
    }

    public void setAccSegment2(String accSegment2) {
        this.accSegment2 = accSegment2;
    }

    public String getAccSegment3() {
        return accSegment3;
    }

    public void setAccSegment3(String accSegment3) {
        this.accSegment3 = accSegment3;
    }
}
