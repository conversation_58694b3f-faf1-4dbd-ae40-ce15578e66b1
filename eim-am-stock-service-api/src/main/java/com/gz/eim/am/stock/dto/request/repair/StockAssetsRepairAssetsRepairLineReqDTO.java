package com.gz.eim.am.stock.dto.request.repair;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @className: StockAssetsRepairAssetsRepairLineReqDTO
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/12/14
 **/
@Data
public class StockAssetsRepairAssetsRepairLineReqDTO extends StockAssetsRepairLineBaseReqDTO{

    private BigDecimal lastCost;

    private String faultDesc;

    private BigDecimal repairMoney;

    private Integer repairStatus;

    private String repairDetail;

    private Integer assetsConditions;

}
