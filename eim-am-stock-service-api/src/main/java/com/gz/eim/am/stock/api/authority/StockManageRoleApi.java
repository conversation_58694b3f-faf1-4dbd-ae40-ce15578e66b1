package com.gz.eim.am.stock.api.authority;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.assets.AssetTransferHeadReqDTO;
import com.gz.eim.am.stock.dto.request.authority.StockManageRoleReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: weijunjie
 * @date: 2020/5/17
 * @description 角色操作api
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockManageRoleApi {

    /**
     * 新建或修改角色
     * @param stockManageRoleReqDTO
     * @return
     */
    @ApiOperation(value = "角色创建", notes = "角色创建")
    @PostMapping(value = "/save")
    ResponseData save(@RequestBody StockManageRoleReqDTO stockManageRoleReqDTO);

    /**
     * 角色分页查询
     * @param stockManageRoleReqDTO
     * @return
     */
    @ApiOperation(value = "角色分页查询", notes = "角色分页查询")
    @GetMapping(value = "/search")
    ResponseData selectManageRoleOut(StockManageRoleReqDTO stockManageRoleReqDTO);
}
