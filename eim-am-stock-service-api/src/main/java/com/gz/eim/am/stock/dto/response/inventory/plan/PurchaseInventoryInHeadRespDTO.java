package com.gz.eim.am.stock.dto.response.inventory.plan;

import lombok.ToString;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/10/14
 * @description
 */
@ToString
public class PurchaseInventoryInHeadRespDTO {
    /**
     * 采购订单号
     */
    private String purchaseOrderNo;
    /**
     * 验收单单号
     */
    private String bizNo;
    /**
     * 入库申请单编码
     */
    private String inventoryInPlanNo;
    /**
     * 申请入库单头id
     */
    private Long inventoryInHeadId;
    /**
     * 入库申请单行信息
     */
    private List<PurchaseInventoryInLineRespDTO> purchaseInventoryInLineRespDTOs;

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public String getInventoryInPlanNo() {
        return inventoryInPlanNo;
    }

    public Long getInventoryInHeadId() {
        return inventoryInHeadId;
    }

    public void setInventoryInHeadId(Long inventoryInHeadId) {
        this.inventoryInHeadId = inventoryInHeadId;
    }

    public void setInventoryInPlanNo(String inventoryInPlanNo) {
        this.inventoryInPlanNo = inventoryInPlanNo;
    }

    public List<PurchaseInventoryInLineRespDTO> getPurchaseInventoryInLineRespDTOs() {
        return purchaseInventoryInLineRespDTOs;
    }

    public void setPurchaseInventoryInLineRespDTOs(List<PurchaseInventoryInLineRespDTO> purchaseInventoryInLineRespDTOs) {
        this.purchaseInventoryInLineRespDTOs = purchaseInventoryInLineRespDTOs;
    }
}
