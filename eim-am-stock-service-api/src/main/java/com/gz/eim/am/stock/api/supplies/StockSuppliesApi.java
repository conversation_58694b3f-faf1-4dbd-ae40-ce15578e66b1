package com.gz.eim.am.stock.api.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandLineReqDTO;
import com.gz.eim.am.stock.dto.request.supplies.*;

import com.gz.eim.am.stock.dto.response.PagerRespDto;
import com.gz.eim.am.stock.dto.response.SuppliesRespDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesResponseDTO;
import feign.QueryMap;

import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;

import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2019-12-17 AM 11:43
 */

@FeignClient(
    name = "eim-am-stock",
    serviceId = "eim-am-stock",
    path = "/api/am/stock/supplies",
    configuration = FeignClientConfiguration.class
)
public interface StockSuppliesApi {

    /**
     * 物料模糊查询
     * @param param
     * @param limit
     * @param warehouseCode
     * @return
     */
    @ApiOperation(value = "物料模糊查询", notes = "物料")
    @GetMapping(value = "/vague")
    ResponseData selectSupplies(@RequestParam("param") String param, @RequestParam("limit") Integer limit, @RequestParam("warehouseCode") String warehouseCode, @RequestParam("docType") Integer docType,@RequestParam("suppliesType") Integer suppliesType);

    /**
     * 新增物料信息
     * @param suppliesDTO
     * @return
     */
    @ApiOperation(value = "新建物料", notes = "物料")
    @PostMapping(value = "/add")
    ResponseData saveSupplies(@RequestBody SuppliesDTO suppliesDTO);


    /**
     * 修改物料信息
     * @param suppliesDTO
     * @return
     */
    @ApiOperation(value = "修改物料", notes = "物料")
    @PostMapping(value = "/update")
    ResponseData updateSupplies(@RequestBody SuppliesDTO suppliesDTO);

    /**
     * 搜索物料信息
     * @param suppliesReqDTO
     * @return
     */
    @ApiOperation(value = "搜索物料", notes = "物料")
    @GetMapping(value = "/search")
    ResponseData selectSupplies(@QueryMap SuppliesReqDTO suppliesReqDTO);

    /**
     * 导出Excel
     * @param suppliesReqDTO
     * @param request
     * @param response
     * @return
     */
    /*@ApiOperation(value = "导出物料", notes = "物料")
    @GetMapping(value = "/download")
    ResponseData download(@QueryMap final SuppliesReqDTO suppliesReqDTO, HttpServletRequest request, HttpServletResponse response);
*/
    /**
     * 采购系统根据条件查询物料
     * @param suppliesPurchaseSearchReqDto
     * @return
     */
    @ApiOperation(value = "搜索物料", notes = "物料")
    @GetMapping(value = "/page-by-purchase")
    ResponseData<PagerRespDto<SuppliesResponseDTO>> pageByPurchase(@QueryMap SuppliesPurchaseSearchReqDto suppliesPurchaseSearchReqDto);


    /**
     * 根据条件查询物料采购属性
     * @param suppliesPurchaseQueryReqDTO
     * @return
     */
    @ApiOperation(value = "搜索物料", notes = "物料")
    @GetMapping(value = "/search-stock-supplies-purchase")
    ResponseData searchStockSuppliesPurchase(@QueryMap SuppliesPurchaseQueryReqDTO suppliesPurchaseQueryReqDTO);

    /**
     * 根据参数查询对应的物料信息
     * @param suppliesPropertyRepDTO
     * @return
     */
    @ApiOperation(value = "根据参数查询对应的物料信息", notes = "物料")
    @GetMapping(value = "/query-supplies-detail")
    ResponseData querySuppliesDetail(@QueryMap  SuppliesPropertyRepDTO suppliesPropertyRepDTO);


    /**
     * 新建物料相关属性信息
     * @param suppliesPropertyRepDTO
     * @return
     */
    @ApiOperation(value = "新建物料相关属性信息", notes = "物料")
    @PostMapping(value = "/save-supplies-property")
    ResponseData saveSuppliesProperty(@RequestBody SuppliesPropertyRepDTO suppliesPropertyRepDTO);


    /**
     * 根据参数查询对应的物料属性信息
     * @param suppliesPropertyRepDTO
     * @return
     */
    @ApiOperation(value = "根据参数查询对应的物料属性信息", notes = "物料")
    @GetMapping(value = "/query-supplies-property-detail")
    ResponseData querySuppliesPropertyDetail(@QueryMap  SuppliesPropertyRepDTO suppliesPropertyRepDTO);


    /**
     * 根据参数查询对应的物料属性信息
     * @param suppliesPropertyRepDTO
     * @return
     */
    @ApiOperation(value = "修改物料相关属性信息", notes = "物料")
    @PostMapping(value = "/update-supplies-property")
    ResponseData updateSuppliesProperty(@RequestBody  SuppliesPropertyRepDTO suppliesPropertyRepDTO);

    /**
     * @param: suppliesReqDTO
     * @description: 根据仓库编码和物料编码集合查询物料
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/13
     */
    @ApiOperation(value = "根据仓库编码和物料编码集合查询物料", notes = "根据仓库编码和物料编码集合查询物料")
    @GetMapping(value = "/querySuppliesQuantity")
    ResponseData querySuppliesQuantity(@QueryMap SuppliesReqDTO suppliesReqDTO);
}
