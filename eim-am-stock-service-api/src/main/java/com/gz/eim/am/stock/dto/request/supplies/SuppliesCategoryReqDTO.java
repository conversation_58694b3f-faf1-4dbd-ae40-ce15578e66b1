package com.gz.eim.am.stock.dto.request.supplies;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2019-12-11 AM 11:09
 */
@Data
@ToString
public class SuppliesCategoryReqDTO {
    /**
     * 主键id
     */
    private Long suppliesCategoryId;

    /**
     * 上级父id
     */
    private Long parentId;

    /**
     * 物料分类编码
     */
    private String code;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 物料分类级别
     */
    private Integer level;

    /**
     * 分类类型 1：可添加物料 2：不可添加物料
     */
    private Integer type;

    /**
     * 使用状态 0：禁用 1：启用
     */
    private Integer status;
    /**
     * 供应商类别
     */
    private Integer vendorType;

}
