package com.gz.eim.am.stock.dto.response.warehouse;

import com.gz.eim.am.stock.dto.request.supplies.SuppliesCategoryDTO;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesCategoryReqDTO;
import com.gz.eim.am.stock.dto.response.RoleKeeperResp;

import java.util.List;

/**
 * 仓详情
 * <AUTHOR>
 * @date  2019-09-17 下午 9:01
 */

public class WarehouseRespDTO {

    /**
     * 仓库主键
     */
    private String warehouseId;

    /**
     * 父仓库主键
     */
    private Long parentId;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 仓库编码
     */
    private String code;

    /**
     * 仓库类型
     */
    private Integer type;

    /**
     * 所属成本中心类型（1，部门；2，项目）
     */
    private Integer costCenterType;

    /**
     * 所属成本中心编号
     */
    private String costCenterCode;

    /**
     * 物理类型 1，实体仓；2，虚拟仓
     */
    private Integer physicalType;

    /**
     * 仓库用途：1，发运仓；2，存储仓
     */
    private Integer purpose;

    /**
     * 状态(1 正常 , 0 禁用)
     */
    private Integer status;

    /**
     * 仓库基本信息
     */
    private WarehouseBaseResp warehouseBase;

    /**
     * 仓库负责的物料分类
     */
    private List<String> category;

    /**
     * 库管员
     */
    private List<RoleKeeperResp> keeper;

    /**
     * 仓库关联物料分类
     */
    private List<SuppliesCategoryDTO> suppliesCategoryList;

    /**
     * 仓库负责的业务类型
     */
    private List<String> business;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门编码路径
     */
    private String deptFullId;

    /**
     * 部门名称路径
     */
    private String deptFullName;
    /**
     * 仓库管理员
     */
    private String linkman;

    /**
     * 仓库管理员名称
     */
    private String  linkmanName;

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getCostCenterType() {
        return costCenterType;
    }

    public void setCostCenterType(Integer costCenterType) {
        this.costCenterType = costCenterType;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public Integer getPhysicalType() {
        return physicalType;
    }

    public void setPhysicalType(Integer physicalType) {
        this.physicalType = physicalType;
    }

    public Integer getPurpose() {
        return purpose;
    }

    public void setPurpose(Integer purpose) {
        this.purpose = purpose;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public WarehouseBaseResp getWarehouseBase() {
        return warehouseBase;
    }

    public void setWarehouseBase(WarehouseBaseResp warehouseBase) {
        this.warehouseBase = warehouseBase;
    }

    public List<String> getCategory() {
        return category;
    }

    public void setCategory(List<String> category) {
        this.category = category;
    }

    public List<String> getBusiness() {
        return business;
    }

    public void setBusiness(List<String> business) {
        this.business = business;
    }

    public List<RoleKeeperResp> getKeeper() {
        return keeper;
    }

    public void setKeeper(List<RoleKeeperResp> keeper) {
        this.keeper = keeper;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptFullId() {
        return deptFullId;
    }

    public void setDeptFullId(String deptFullId) {
        this.deptFullId = deptFullId;
    }

    public String getDeptFullName() {
        return deptFullName;
    }

    public void setDeptFullName(String deptFullName) {
        this.deptFullName = deptFullName;
    }

    public List<SuppliesCategoryDTO> getSuppliesCategoryList() {
        return this.suppliesCategoryList;
    }

    public void setSuppliesCategoryList(final List<SuppliesCategoryDTO> suppliesCategoryList) {
        this.suppliesCategoryList = suppliesCategoryList;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getLinkmanName() {
        return linkmanName;
    }

    public void setLinkmanName(String linkmanName) {
        this.linkmanName = linkmanName;
    }

    @Override
    public String toString() {
        return "WarehouseRespDTO{" +
                "warehouseId='" + warehouseId + '\'' +
                ", parentId=" + parentId +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", type=" + type +
                ", costCenterType=" + costCenterType +
                ", costCenterCode='" + costCenterCode + '\'' +
                ", physicalType=" + physicalType +
                ", purpose=" + purpose +
                ", status=" + status +
                ", warehouseBase=" + warehouseBase +
                ", category=" + category +
                ", keeper=" + keeper +
                ", suppliesCategoryList=" + suppliesCategoryList +
                ", business=" + business +
                ", deptName='" + deptName + '\'' +
                ", deptFullId='" + deptFullId + '\'' +
                ", deptFullName='" + deptFullName + '\'' +
                ", linkman='" + linkman + '\'' +
                ", linkmanName='" + linkmanName + '\'' +
                '}';
    }
}
