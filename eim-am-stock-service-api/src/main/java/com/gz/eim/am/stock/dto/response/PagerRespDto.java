package com.gz.eim.am.stock.dto.response;

import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Objects;

/**
 * @author: l<PERSON><PERSON>ang
 * @date: 2020/7/9
 * <p>
 *  分页返回工具
 * </p>
 */
public class PagerRespDto<T> {

    private Long count;

    private List<T> list;

    private Integer pageNum = 1;
    private Integer pageSize = 20;
    private int pages;

    public PagerRespDto() {
    }

    public PagerRespDto(List<T> list) {
        this.list = list;
    }

    public PagerRespDto(Long count, List<T> list) {
        this.count = count;
        this.list = list;
    }
    public PagerRespDto(PageInfo<T> pageInfo){
        this.list = pageInfo.getList();
        this.pages = pageInfo.getPages();
        this.pageNum = pageInfo.getPageNum();
        this.pageSize = pageInfo.getPageSize();
        this.count = pageInfo.getTotal();
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    /**
     * 计算当前总页数
     * @return
     */
    public  int calculationPages(){
        if(Objects.isNull (this.count)){
            this.count = 0L;
        }

        return (int) ((this.count + this.pageSize -1) / pageSize);
    }
}
