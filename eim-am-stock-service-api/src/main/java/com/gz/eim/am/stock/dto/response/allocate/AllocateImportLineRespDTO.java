package com.gz.eim.am.stock.dto.response.allocate;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/6
 * @description:
 */
public class AllocateImportLineRespDTO {
    /**
     * 行id
     */
    private Long lineId;
    /**
     * 头id
     */
    private Long headId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 导入的调出仓库名称
     */
    private String outImportWarehouseName;
    /**
     * 匹配的调出仓库编码
     */
    private String outWarehouseCode;
    /**
     * 导入的调入仓库名称
     */
    private String inImportWarehouseName;
    /**
     * 匹配的调入仓库编码
     */
    private String inWarehouseCode;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 数量
     */
    private Integer number;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 匹配的调出仓库名称
     */
    private String outWarehouseName;
    /**
     * 匹配的调入仓库名称
     */
    private String inWarehouseName;
    /**
     * 匹配的物料名称
     */
    private String suppliesName;

    /**
     * 资产编码
     */
    private String assetsCode;
    /**
     * 资产名称
     */
    private String assetsName;

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public Long getHeadId() {
        return headId;
    }

    public void setHeadId(Long headId) {
        this.headId = headId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getOutImportWarehouseName() {
        return outImportWarehouseName;
    }

    public void setOutImportWarehouseName(String outImportWarehouseName) {
        this.outImportWarehouseName = outImportWarehouseName;
    }

    public String getOutWarehouseCode() {
        return outWarehouseCode;
    }

    public void setOutWarehouseCode(String outWarehouseCode) {
        this.outWarehouseCode = outWarehouseCode;
    }

    public String getInImportWarehouseName() {
        return inImportWarehouseName;
    }

    public void setInImportWarehouseName(String inImportWarehouseName) {
        this.inImportWarehouseName = inImportWarehouseName;
    }

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getOutWarehouseName() {
        return outWarehouseName;
    }

    public void setOutWarehouseName(String outWarehouseName) {
        this.outWarehouseName = outWarehouseName;
    }

    public String getInWarehouseName() {
        return inWarehouseName;
    }

    public void setInWarehouseName(String inWarehouseName) {
        this.inWarehouseName = inWarehouseName;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    @Override
    public String toString() {
        return "AllocateImportLineRespDTO{" +
                "lineId=" + lineId +
                ", headId=" + headId +
                ", activityName='" + activityName + '\'' +
                ", outImportWarehouseName='" + outImportWarehouseName + '\'' +
                ", outWarehouseCode='" + outWarehouseCode + '\'' +
                ", inImportWarehouseName='" + inImportWarehouseName + '\'' +
                ", inWarehouseCode='" + inWarehouseCode + '\'' +
                ", suppliesCode='" + suppliesCode + '\'' +
                ", number=" + number +
                ", status=" + status +
                ", errorMessage='" + errorMessage + '\'' +
                ", outWarehouseName='" + outWarehouseName + '\'' +
                ", inWarehouseName='" + inWarehouseName + '\'' +
                ", suppliesName='" + suppliesName + '\'' +
                ", assetsCode='" + assetsCode + '\'' +
                ", assetsName='" + assetsName + '\'' +
                '}';
    }
}
