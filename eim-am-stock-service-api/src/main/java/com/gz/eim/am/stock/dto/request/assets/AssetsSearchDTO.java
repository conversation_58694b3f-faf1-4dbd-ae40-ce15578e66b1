package com.gz.eim.am.stock.dto.request.assets;

import com.gz.eim.am.stock.dto.request.PageReqDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;
import lombok.ToString;
/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/6
 * @description:
 */
@Data
@ToString
public class AssetsSearchDTO extends PageReqDTO {
    /**
     * 资产领用出库单行单编码
     **/
    private List<String> demandItemNoList;
    /**
     * 资产编码，用于精准搜索
     **/
    private String code;
    /**
     * 资产状态
     **/
    private Integer conditions;
    /**
     * 是否同步EBS
     **/
    private Integer extractStatus;
    /**
     * 供应商名称
     **/
    private String vendorName;
    /**
     * 开始资产领用时间
     **/
    private String startHolderTime;
    /**
     * 结束资产领用时间
     **/
    private String endHolderTime;
    /**
     * 资产创建开始时间
     **/
    private String startTime;
    /**
     * 资产创建结束时间
     **/
    private String endTime;
    /**
     * 计划入库单类型
     **/
    private Integer inventoryInPlanType;
    /**
     * 计划入库资产行单状态
     **/
    private Integer inStockStatus;

    private Long assetsId;

    private String assetsCode;

    private String assetsName;

    private String suppliesCode;

    private String suppliesName;

    private String warehouseCode;

    private String snCode;

    private String deviceCode;

    private String brand;

    private String model;

    private String unit;

    private String category;

    private String extCpu;

    private String extRamMemory;

    private String extHardDisk;

    // 资产类型需要传入：
    // ZZ01：执照；YZ01：印章
    private String categoryCode;

    private String assetsDeploy;

    private Integer hasSub;

    private String companyCode;

    private String companyName;

    private Integer status;

    private BigDecimal netValue;

    private BigDecimal initialValue;

    private BigDecimal scrapValue;

    private Integer purchaseType;

    private String purchaseNo;

    private Integer depreciationWay;

    private String costDept;

    private Date purchaseTime;

    private Date storageTime;

    private Date planHandleTime;

    private Integer useYearLimit;

    private Integer regularMaintain;

    private String maintainCycle;

    private String assetsKeeper;

    private String assetsPic;

    private String saasAssetsCode;

    private String saasAddress;

    private String remark;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private String needDept;

    private String holder;
    /**
     * 资产号码和资产名字
     */
    private String assetsCodeAndName;

    /**
     * 资产使用情况集合
     */
    private List<Integer> conditionList;

    /**
     * 所在大区
     */
    private String busLargeRegion;

    /**
     * 所在城市
     */
    private String busCity;

    /**
     * 该集合下状态的资产被过滤掉
     */
    private List<Integer> noGetStatus;

    /**
     * 要过滤掉的资产使用状态
     */
    private List<Integer> noGetConditionList;

    /**
     * 仓库类型集合
     */
    private List<Integer> warehouseTypes;

    /**
     * 执照检索参数（根跟编码和名称来匹配查询）
     */
    private String licenseParam;


    /**
     * 资产状态
     */
    private List<Integer> assetStatusList;
    /**
     * 资产管理员
     */
    private List<String> assetsKeeperList;

    /**
     * 资产持有人
     */
    private List<String> holderList;

    /**
     * 资产所在仓库
     */
    private List<String> warehouseCodeList;

    /**
     * 资产所在大区
     */
    private List<String> busLargeRegionList;


    /**
     * 资产所在城市
     */
    private List<String> busCityList;

    /**
     * 资产编码
     */
    private List<String> assetsCodeList;

    /**
     * 设备序列号
     */
    private List<String> snCodeList;

    /**
     * 采购码
     */
    private List<String> purchaseNoList;

    /**
     * 需求部门
     */
    private List<String> needDeptList;

    /**
     * 资产类别
     */
    private List<String> categoryList;
    /**
     * 资产类别编码集合
     */
    private List<String> categoryCodeList;
    /**
     * 不再范围内的资产类别
     */
    private List<String> noCategoryCodeList;
    /**
     * 关键字
     */
    private List<String> keyWordList;
    /**
     * 物料编码集合
     */
    private List<String> suppliesCodeList;


    /**
     * 关键字
     */
    private String keyWord;

    /**
     * 不包含报废资产（报废单据查询使用参数）
     */
    private Integer scrapAssetsFlag;
    /**
     * 是否在审批中：1是审批中的  0未审批
     */
    private Integer approveStatus;
    /**
     * 是否只模糊匹配右边
     */
    private Boolean rightLike;

    /**
     * 是否需要分页：1需要  0不需要
     */
    private Integer usePaging;
}
