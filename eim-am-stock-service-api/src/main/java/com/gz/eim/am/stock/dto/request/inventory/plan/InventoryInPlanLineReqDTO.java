package com.gz.eim.am.stock.dto.request.inventory.plan;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计划入库单行
 * <AUTHOR>
 * @date  2019-09-25 下午 9:01
 */
@ToString
public class InventoryInPlanLineReqDTO extends PageReqDTO {
    /**
     * 入库类型 类型： 1：执照入库  2：印章入库
     */
    private int type;
    /**
     * 计划入库单行id
     */
    private Long inventoryInPlanLineId;
    /**
     * 计划入库单头id
     */
    private Long inventoryInPlanHeadId;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 计划入库数量
     */
    private Integer number;
    /**
     * 采购价格
     */
    private BigDecimal purchasePrice;
    /**
     * 本次入库数量
     */
    private Integer thisNumber;
    /**
     * 驳回数量
     */
    private Integer rejectNumber;
    /**
     * 资产编码
     */
    private String assetsCode;
    /**
     * 序列号
     */
    private String snNo;
    /**
     * 计划入库单行状态
     */
    private Integer status;
    /**
     * 关联资产导入code
     */
    private String inventoryAssetBatchCode;
    /**
     * 计划入库时间
     */
    private String planInTime;
    /**
     * 差异原因
     */
    private String differenceReason;
    /**
     * 是否分页，空值不分页
     */
    private Integer noPage;
    /**
     * 资产编码集合
     */
    private List<String> assets;
    /**
     * 序列号集合
     */
    private List<String> snNos;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否归还：0：否；1：是
     */
    private Integer isReturn;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Integer getRejectNumber() {
        return rejectNumber;
    }

    public void setRejectNumber(Integer rejectNumber) {
        this.rejectNumber = rejectNumber;
    }

    public Long getInventoryInPlanLineId() {
        return inventoryInPlanLineId;
    }

    public void setInventoryInPlanLineId(Long inventoryInPlanLineId) {
        this.inventoryInPlanLineId = inventoryInPlanLineId;
    }

    public Long getInventoryInPlanHeadId() {
        return inventoryInPlanHeadId;
    }

    public void setInventoryInPlanHeadId(Long inventoryInPlanHeadId) {
        this.inventoryInPlanHeadId = inventoryInPlanHeadId;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getThisNumber() {
        return thisNumber;
    }

    public void setThisNumber(Integer thisNumber) {
        this.thisNumber = thisNumber;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getInventoryAssetBatchCode() {
        return inventoryAssetBatchCode;
    }

    public void setInventoryAssetBatchCode(String inventoryAssetBatchCode) {
        this.inventoryAssetBatchCode = inventoryAssetBatchCode;
    }

    public String getPlanInTime() {
        return planInTime;
    }

    public void setPlanInTime(String planInTime) {
        this.planInTime = planInTime;
    }

    public String getDifferenceReason() {
        return differenceReason;
    }

    public void setDifferenceReason(String differenceReason) {
        this.differenceReason = differenceReason;
    }

    public List<String> getAssets() {
        return this.assets;
    }

    public void setAssets(final List<String> assets) {
        this.assets = assets;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public Integer getNoPage() {
        return noPage;
    }

    public void setNoPage(Integer noPage) {
        this.noPage = noPage;
    }

    public List<String> getSnNos() {
        return snNos;
    }

    public void setSnNos(List<String> snNos) {
        this.snNos = snNos;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }
}