package com.gz.eim.am.stock.dto.request.inventory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 入库单明细
 * <AUTHOR>
 * @date  2019-09-25 下午 9:01
 */
public class InventoryInSuppliesDTO {
    /**
     *  入库单明细id
     */
    private Long inSuppliesId;
    /**
     * 入库单id
     */
    private Long inventoryInId;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 计划入库数量
     */
    private Integer number;
    /**
     * 实际入库数量
     */
    private Integer realNumber;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 价格
     */
    private Integer price;
    /**
     * 品质
     */
    private Integer quality;
    /**
     * 差异原因
     */
    private String differenceReason;
    /**
     * 单位体积
     */
    private BigDecimal volume;
    /**
     * 总体积
     */
    private BigDecimal volumeAmount;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 序列号
     */
    private String snNo;
    /**
     * 标签生成节点
     */
    private Integer printLabelType;
    /**
     * 实际入库时间
     */
    private String inventoryInTime;
    /**
     * 本次入库数量
     */
    private Integer thisNumber;

    public Long getInventoryInId() {
        return inventoryInId;
    }

    public void setInventoryInId(Long inventoryInId) {
        this.inventoryInId = inventoryInId;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getQuality() {
        return quality;
    }

    public void setQuality(Integer quality) {
        this.quality = quality;
    }

    public String getDifferenceReason() {
        return differenceReason;
    }

    public void setDifferenceReason(String differenceReason) {
        this.differenceReason = differenceReason;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public BigDecimal getVolumeAmount() {
        return volumeAmount;
    }

    public void setVolumeAmount(BigDecimal volumeAmount) {
        this.volumeAmount = volumeAmount;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public Integer getPrintLabelType() {
        return printLabelType;
    }

    public void setPrintLabelType(Integer printLabelType) {
        this.printLabelType = printLabelType;
    }

    public Long getInSuppliesId() {
        return inSuppliesId;
    }

    public void setInSuppliesId(Long inSuppliesId) {
        this.inSuppliesId = inSuppliesId;
    }

    public String getInventoryInTime() {
        return inventoryInTime;
    }

    public void setInventoryInTime(String inventoryInTime) {
        this.inventoryInTime = inventoryInTime;
    }

    public Integer getThisNumber() {
        return thisNumber;
    }

    public void setThisNumber(Integer thisNumber) {
        this.thisNumber = thisNumber;
    }

    @Override
    public String toString() {
        return "InventoryInSuppliesDTO{" +
                "inSuppliesId=" + inSuppliesId +
                ", inventoryInId=" + inventoryInId +
                ", suppliesCode='" + suppliesCode + '\'' +
                ", number=" + number +
                ", realNumber=" + realNumber +
                ", status=" + status +
                ", price=" + price +
                ", quality=" + quality +
                ", differenceReason='" + differenceReason + '\'' +
                ", volume=" + volume +
                ", volumeAmount=" + volumeAmount +
                ", batchNo='" + batchNo + '\'' +
                ", snNo='" + snNo + '\'' +
                ", printLabelType=" + printLabelType +
                ", inventoryInTime='" + inventoryInTime + '\'' +
                ", thisNumber=" + thisNumber +
                '}';
    }
}