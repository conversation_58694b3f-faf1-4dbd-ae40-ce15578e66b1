package com.gz.eim.am.stock.dto.request.order.plan;

import lombok.ToString;
import java.math.BigDecimal;

/**
 * @author: weijunjie
 * @date: 2020/12/23
 * @description
 */
@ToString
public class PurchaseDeliveryLineDTO {
    /**
     * 采购订单行id
     */
    private Long purchaseOrderLineId;
    /**
     * 验收单行编码
     */
    private String receiveItemNo;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 出库数量
     */
    private Integer receiveNum;
    /**
     * 物料单价
     */
    private BigDecimal unitPrice;



    public Long getPurchaseOrderLineId() {
        return purchaseOrderLineId;
    }

    public void setPurchaseOrderLineId(Long purchaseOrderLineId) {
        this.purchaseOrderLineId = purchaseOrderLineId;
    }

    public String getReceiveItemNo() {
        return receiveItemNo;
    }

    public void setReceiveItemNo(String receiveItemNo) {
        this.receiveItemNo = receiveItemNo;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Integer receiveNum) {
        this.receiveNum = receiveNum;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

}
