package com.gz.eim.am.stock.dto.response.external;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/2
 * @description:
 */
public class DeliverySuppliesRespDTO {
    /**
     * sn号
     */
    private String snNo;
    /**
     * 物料编码+sn号
     */
    private String suppliesCodeAndSnNo;
    /**
     * 物料名称
     */
    private String suppliesName;
    /**
     * 物料品牌
     */
    private String brandCode;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 物料编码
     */
    private String suppliesCode;

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getSuppliesCodeAndSnNo() {
        return suppliesCodeAndSnNo;
    }

    public void setSuppliesCodeAndSnNo(String suppliesCodeAndSnNo) {
        this.suppliesCodeAndSnNo = suppliesCodeAndSnNo;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }


    @Override
    public String toString() {
        return "DeliverySuppliesRespDTO{" +
                "snNo='" + snNo + '\'' +
                ", suppliesCodeAndSnNo='" + suppliesCodeAndSnNo + '\'' +
                ", suppliesName='" + suppliesName + '\'' +
                ", brandCode='" + brandCode + '\'' +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", suppliesCode='" + suppliesCode + '\'' +
                '}';
    }
}
