package com.gz.eim.am.stock.dto.response.assets;


import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/3/13
 * @description: 资产编码
 */

@Data
@ToString
public class AssetCodeRespDTO {
    private Long id;
    /**
     * 资产编码
     */
    private String assetCode;
    /**
     * 资产分类编码
     */
    private String categoryCode;
    /**
     * 公司编码
     */
    private String companyCode;
    /**
     * 是否使用
     */
    private Integer isUsed;
    /**
     * 操作工号
     */
    private String operating;
    /**
     * 是否已入库
     */
    private Integer isIn;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 资产分类名称
     */
    private String categoryName;
    /**
     * 二维码url
     */
    private String qrImgUrl;

    /**
     * 条形码url
     */
    private String barImgUrl;

    /**
     * 序号
     */
    private Integer serialNumber;
}