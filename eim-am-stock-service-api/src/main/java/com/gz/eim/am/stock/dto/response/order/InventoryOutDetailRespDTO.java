package com.gz.eim.am.stock.dto.response.order;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/10/28
 * @description: 出库单详情
 */
public class InventoryOutDetailRespDTO {
    /**
     * 出库单详情主键id
     */
    private Long deliveryDetailId;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 物料名称
     */
    private String suppliesName;
    /**
     *  计划调出数量
     */
    private Integer number;

    private Byte quality;
    /**
     * 实际调出数量
     */
    private Integer realNumber;
    /**
     * 差异原因
     */
    private String differenceReason;
    /**
     *
     */
    private String assetsCode;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 序列号
     */
    private String snNo;
    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 计划出库时间
     */
    private String planOutTime;
    /**
     * 实际调出编码
     */
    private String realWarehouseCode;
    /**
     * 实际调出仓库名称
     */
    private String realWarehouseCodeName;
    /**
     * 实际出库时间
     */
    private String inventoryOutTime;

    /**
     * 是否发运
     */
    private Integer isSend;
    /**
     * 是否发运名称
     */
    private String isSendName;

    /**
     * 批次或序列号
     */
    private String manageNo;

    /**
     * 出库单详细状态
     */
    private Integer status;

    /**
     * 当前库存量
     */
    private Long allNumber;
    /**
     * 物料描述
     */
    private String suppliesRemark;

    /**
     * 关联资产编码列表
     */
    private List<String> assets;

    /**
     * 关联资产SN号列表
     */
    private List<String> snList;

    private Long deliveryPlanLineId;

    public Long getDeliveryDetailId() {
        return deliveryDetailId;
    }

    public void setDeliveryDetailId(Long deliveryDetailId) {
        this.deliveryDetailId = deliveryDetailId;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Byte getQuality() {
        return quality;
    }

    public void setQuality(Byte quality) {
        this.quality = quality;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public String getDifferenceReason() {
        return differenceReason;
    }

    public void setDifferenceReason(String differenceReason) {
        this.differenceReason = differenceReason;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPlanOutTime() {
        return planOutTime;
    }

    public void setPlanOutTime(String planOutTime) {
        this.planOutTime = planOutTime;
    }

    public String getRealWarehouseCode() {
        return realWarehouseCode;
    }

    public void setRealWarehouseCode(String realWarehouseCode) {
        this.realWarehouseCode = realWarehouseCode;
    }

    public String getRealWarehouseCodeName() {
        return realWarehouseCodeName;
    }

    public void setRealWarehouseCodeName(String realWarehouseCodeName) {
        this.realWarehouseCodeName = realWarehouseCodeName;
    }

    public String getInventoryOutTime() {
        return inventoryOutTime;
    }

    public void setInventoryOutTime(String inventoryOutTime) {
        this.inventoryOutTime = inventoryOutTime;
    }

    public Integer getIsSend() {
        return isSend;
    }

    public void setIsSend(Integer isSend) {
        this.isSend = isSend;
    }

    public String getIsSendName() {
        return isSendName;
    }

    public void setIsSendName(String isSendName) {
        this.isSendName = isSendName;
    }

    public String getManageNo() {
        return manageNo;
    }

    public void setManageNo(String manageNo) {
        this.manageNo = manageNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getAllNumber() {
        return allNumber;
    }

    public void setAllNumber(Long allNumber) {
        this.allNumber = allNumber;
    }

    public String getSuppliesRemark() {
        return suppliesRemark;
    }

    public void setSuppliesRemark(String suppliesRemark) {
        this.suppliesRemark = suppliesRemark;
    }

    public List<String> getAssets() {
        return this.assets;
    }

    public void setAssets(final List<String> assets) {
        this.assets = assets;
    }

    public List<String> getSnList() {
        return this.snList;
    }

    public void setSnList(final List<String> snList) {
        this.snList = snList;
    }

    public Long getDeliveryPlanLineId() {
        return this.deliveryPlanLineId;
    }

    public void setDeliveryPlanLineId(final Long deliveryPlanLineId) {
        this.deliveryPlanLineId = deliveryPlanLineId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InventoryOutDetailRespDTO{");
        sb.append("deliveryDetailId=").append(deliveryDetailId);
        sb.append(", suppliesCode='").append(suppliesCode).append('\'');
        sb.append(", suppliesName='").append(suppliesName).append('\'');
        sb.append(", number=").append(number);
        sb.append(", quality=").append(quality);
        sb.append(", realNumber=").append(realNumber);
        sb.append(", differenceReason='").append(differenceReason).append('\'');
        sb.append(", assetsCode='").append(assetsCode).append('\'');
        sb.append(", batchNo='").append(batchNo).append('\'');
        sb.append(", snNo='").append(snNo).append('\'');
        sb.append(", unitName='").append(unitName).append('\'');
        sb.append(", planOutTime='").append(planOutTime).append('\'');
        sb.append(", realWarehouseCode='").append(realWarehouseCode).append('\'');
        sb.append(", realWarehouseCodeName='").append(realWarehouseCodeName).append('\'');
        sb.append(", inventoryOutTime='").append(inventoryOutTime).append('\'');
        sb.append(", isSend=").append(isSend);
        sb.append(", isSendName='").append(isSendName).append('\'');
        sb.append(", manageNo='").append(manageNo).append('\'');
        sb.append(", status=").append(status);
        sb.append(", allNumber=").append(allNumber);
        sb.append(", suppliesRemark='").append(suppliesRemark).append('\'');
        sb.append(", assets=").append(assets);
        sb.append(", snList=").append(snList);
        sb.append(", deliveryPlanLineId=").append(deliveryPlanLineId);
        sb.append('}');
        return sb.toString();
    }
}
