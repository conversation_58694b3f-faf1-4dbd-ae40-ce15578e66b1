package com.gz.eim.am.stock.api.repair;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.assets.StockSelfServiceAssetsCategoryConfigApi;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairApproveReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairHeadReqDTO;
import feign.QueryMap;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 资产维修api
 * @author: <EMAIL>
 * @date: 2022/12/6
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId = "eim-am-stock",
        configuration = StockSelfServiceAssetsCategoryConfigApi.class
)
public interface StockAssetsRepairApi {

    /**
     * @param: stockAssetsRepairApproveReqDTO
     * @description: IT审批页审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/6
     */
    @ApiOperation(value = "IT审批页审批", notes = "IT审批页审批")
    @PostMapping(value = "/ITApprove")
    ResponseData ITApprove(@RequestBody StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO);

    /**
     * @param: stockAssetsRepairApproveReqDTO
     * @description: 行政审批页审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/6
     */
    @ApiOperation(value = "行政审批页审批", notes = "行政审批页审批")
    @PostMapping(value = "/adminApprove")
    ResponseData adminApprove(@RequestBody StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO);

    /**
     * @param: stockAssetsRepairApproveReqDTO
     * @description: 资产持有人审批页审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/6
     */
    @ApiOperation(value = "资产持有人审批页审批", notes = "资产持有人审批页审批")
    @PostMapping(value = "/assetsHolderApprove")
    ResponseData assetsHolderApprove(@RequestBody StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO);

    /**
     * @param: file, repairType
     * @description: 导入资产excel
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    @ApiOperation(value = "导入资产excel", notes = "导入资产excel")
    @PostMapping(value = "/importAssetsExcel")
    ResponseData importAssetsExcel(@RequestParam("file") MultipartFile file, @RequestParam("repairType") Integer repairType);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单保存和提交
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    @ApiOperation(value = "资产维修单提交和保存", notes = "资产维修单提交和保存")
    @PostMapping(value = "/repairSaveOrSubmit")
    ResponseData repairSaveOrSubmit(@RequestBody StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单详情查询
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    @ApiOperation(value = "资产维修单详情查询", notes = "资产维修单详情查询")
    @GetMapping(value = "/queryRepairDetail")
    ResponseData queryRepairDetail(@RequestParam(name = "id", required = false) Long id, @RequestParam(name = "repairNo", required = false) String repairNo);

    /**
     * @param: repairNo, request, response
     * @description: 导出资产明细
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    @ApiOperation(value = "导出资产明细", notes = "导出资产明细")
    @GetMapping(value = "/exportAssetsExcel")
    ResponseData exportAssetsExcel(@RequestParam("repairNo") String repairNo, HttpServletRequest request, HttpServletResponse response);

    /**
     * @param: file, repairType
     * @param: status
     * @description: 导入批量更新资产excel
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    @ApiOperation(value = "导入批量更新资产excel", notes = "导入批量更新资产excel")
    @PostMapping(value = "/importBatchUpdateAssetsExcel")
    ResponseData importBatchUpdateAssetsExcel(@RequestParam("file") MultipartFile file, @RequestParam("repairType") Integer repairType, @RequestParam("status") Integer status);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单验收提交
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    @ApiOperation(value = "资产维修单验收提交", notes = "资产维修单验收提交")
    @PostMapping(value = "/repairCheckSubmit")
    ResponseData repairCheckSubmit(@RequestBody StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单验收审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    @ApiOperation(value = "资产维修单验收审批", notes = "资产维修单验收审批")
    @PostMapping(value = "/repairCheckApprove")
    ResponseData repairCheckApprove(@RequestBody StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 删除资产维修单
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    @ApiOperation(value = "删除资产维修单", notes = "删除资产维修单")
    @PostMapping(value = "/repairDelete")
    ResponseData repairDelete(@RequestBody StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单列表查询
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    @ApiOperation(value = "资产维修单列表查询", notes = "资产维修单列表查询")
    @GetMapping(value = "/queryRepairList")
    ResponseData queryRepairList(@QueryMap StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO);
}
