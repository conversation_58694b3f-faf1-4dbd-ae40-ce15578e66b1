package com.gz.eim.am.stock.dto.request.supplies;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;


 /**
   * @param:
   * @description: 物料和部门或职级配置信息请求类
   * @return:
   * @author: <EMAIL>
   * @date: 2021/10/11
   */
@Data
@ToString
public class StockSelfServiceAssetsCategoryConfigReqDTO extends PageReqDTO {
    /**
     * 查询条件公用
     */
    private String param;
     /**
      * 实际使用人
      */
     private String realUseUser;

}
