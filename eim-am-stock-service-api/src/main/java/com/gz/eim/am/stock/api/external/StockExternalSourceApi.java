package com.gz.eim.am.stock.api.external;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.dto.request.external.order.StockSuppliesQuantityReduceHeadReqDTO;
import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsCompensationRecordRespDTO;
import feign.QueryMap;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/2
 * @description:  外部来源访问
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        path = "/api/am/stock",
        configuration = FeignClientConfiguration.class

)
public interface StockExternalSourceApi {

    /**
     * 查询领用出库的序列号物料的信息
     * @param snNo
     * @return
     */
    @ApiOperation(value = "领用出库的序列号物料的信息", notes = "序列号")
    @GetMapping(value = "/external/useDeliverySupplies")
    ResponseData selectUseDeliverySuppliesBySnNo(@RequestParam("snNo") String snNo);

    /**
     * 查询当前人员是否是卡仓库管理员 或执照管理员或执照的使用人
     * @param empId
     * @param type
     * @return
     */
    @ApiOperation(value = "", notes = "离职系统对接")
    @GetMapping(value = "/external/cardWarehouseQuantity")
    ResponseData selectCardWarehouseQuantity(@RequestParam("empId") String empId,@RequestParam("type") Integer type);


    /**
     * 库存记录查询
     * @param dto
     * @return
     */
    @ApiOperation(value = "全部物料库存记录查询", notes = "库存")
    @GetMapping(value = "/external/queryStockQuantity")
    ResponseData queryStockQuantity(@QueryMap StockSuppliesQuantityReqDTO dto);


    /**
     * 外部扣减库存接口
     * @param dto
     * @return
     */
    @ApiOperation(value = "兑换京东卡", notes = "库存")
    @PostMapping(value = "/external/convertInventorySupplies")
    ResponseData convertInventorySupplies(@RequestBody StockSuppliesQuantityReduceHeadReqDTO dto);


    /**
     * 获取当前人员名下未归还印章数据
     * @param empId
     * @return
     */
    @ApiOperation(value = "获取人员名下借用的印章", notes = "库存")
    @GetMapping(value = "/external/queryUserBorrowSeal")
    ResponseData queryUserBorrowSeal(@RequestParam("empId") String empId,@RequestParam("sendMsg") Boolean sendMsg);

    /**
     * 获取当前人员名下未归还执照数据
     * @param empId
     * @return
     */
    @ApiOperation(value = "获取人员名下借用的执照", notes = "库存")
    @GetMapping(value = "/external/queryUserBorrowLicense")
    ResponseData queryUserBorrowLicense(@RequestParam("empId") String empId,@RequestParam("sendMsg") Boolean sendMsg,@RequestParam("isFlowPlatform") Integer isFlowPlatform);

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 离职查询人员名下资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/9/12
     */
    @GetMapping("/external/compensation/queryCompensationAssets")
    ResponseData<List<StockAssetsCompensationRecordRespDTO>> queryCompensationAssets(@QueryMap StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO);
}
