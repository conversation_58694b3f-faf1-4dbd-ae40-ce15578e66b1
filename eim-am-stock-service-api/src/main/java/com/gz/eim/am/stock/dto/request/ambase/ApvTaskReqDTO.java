package com.gz.eim.am.stock.dto.request.ambase;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ApvTaskReqDTO {
    private Long id;

    private String sn;

    private String flowId;

    private String formId;

    private String bizId;

    private List<String> bizIdList;

    private Integer flowCategoryId;

    private String categoryCode;

    private String nodeCode;

    private String nodeName;

    private String apprvoeTitle;

    private String applyUser;

    private String approveUser;

    private String status;

    private String actionCode;

    private String actionValue;

    private String actionLabel;

    private Date apvDate;

    private Integer apvSource;

    private String remark;

    private Date startDate;

    private Date expireDate;

    private Date expireNotifyDate;

    private Integer delayMaxNum;

    private Integer delayCurNum;

    private Integer overTimeMaxNum;

    private Integer overTimeCurNum;

    private Date overTimeDateTime;

    private Integer isBeta;

    private String betaUsers;

    private String nodeType;

    private Integer notify;

    private String notifyContent;

    private String callBackStr;

    private Integer emergency;

    private String mailCheckCode;

    private Integer mailCheckCodeStatus;

    private Date mailCheckCodeBegin;

    private Date mailCheckCodeEnd;

    private Integer isAgent;

    private String agentUser;

    private String applyUserName;

    private String isApprove;

    private Date createDate;

    private String createUser;

    private Date updateDate;

    private String updateUser;

    private String processId;

    private String batchNo;

    private String opinion;

}