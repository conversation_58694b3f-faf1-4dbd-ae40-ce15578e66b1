package com.gz.eim.am.stock.api.order;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutBatchReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutSnSearchReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 10:43
 */

@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockDeliveryApi {

    /**
     * 批量新增入库单
     * @param dto
     * @return
     */
    @ApiOperation(value = "批量新增出库单", notes = "出库单")
    @PostMapping(value = "/delivery/batch")
    ResponseData batchInventoryOut(@RequestBody InventoryOutBatchReqDTO dto);

    /**
     * 库存月结储存
     * @param limit
     * @return
     */
    @ApiOperation(value = "库存月结", notes = "出库单")
    @GetMapping(value = "/delivery/stockQuantityMonthEnd")
    ResponseData stockQuantityMonthEnd(@RequestParam("limit") Integer limit);

    /**
     * 新增出库单
     * @param inventoryOutDTO
     * @return
     */
    @ApiOperation(value = "保存出库单", notes = "出库单")
    @PostMapping(value = "/delivery")
    ResponseData saveInventoryOut(@RequestBody InventoryOutReqDTO inventoryOutDTO);


    /**
     * @param: file,empId
     * @description: 手动执行GPS出库
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/4/26
     */
    @ApiOperation(value = "手动执行GPS出库", notes = "手动执行GPS出库")
    @PostMapping(value = "/delivery/manualGpsDeliveryByImportExcel")
    ResponseData manualGpsDeliveryByImportExcel(@RequestParam("file") MultipartFile file, @RequestParam("empId") String empId);

    /**
     * 出库单分页查询
     * @param inventoryOutSearchReqDTO
     * @return
     */
    @ApiOperation (value = "出库单分页查询", notes = "出库单")
    @GetMapping (value = "/delivery")
    ResponseData selectInventoryOut(InventoryOutSearchReqDTO inventoryOutSearchReqDTO);

    /**
     * 根据出库单查询出库单
     * @param deliveryId
     * @return
     */
    @ApiOperation (value = "根据出库单查询出库单", notes = "出库单")
    @GetMapping(value = "/delivery/{deliveryId}")
    ResponseData selectInventoryOutByDeliveryId(@PathVariable("deliveryId")Long deliveryId);

    /**
     * 根据出库单编号查询出库单(流程平台回调)
     * @param bizId
     * @return
     */
    @ApiOperation (value = "根据出库单查询出库单", notes = "出库单")
    @GetMapping(value = "/delivery/wfl/{bizId}")
    ResponseData selectInventoryOutByDeliveryNo(@PathVariable("bizId")String bizId);


    /**
     * 修改入库单
     * @param inventoryOutDTO
     * @return
     */
    @ApiOperation (value = "出库单出库", notes = "出库单")
    @PutMapping(value = "delivery")
    ResponseData inventoryOutInStorage(@RequestBody InventoryOutReqDTO inventoryOutDTO);

    /**
     * 出库单分页查询
     * @param inventoryOutSearchReqDTO
     * @return
     */
    @ApiOperation (value = "可出库的出库单分页查询", notes = "出库单")
    @GetMapping (value = "/delivery/accessible")
    ResponseData selectAccessibleInventoryOut(InventoryOutSearchReqDTO inventoryOutSearchReqDTO);

    /**
     * 出库单序列号分页查询
     * @param inventoryOutSnSearchReqDTO
     * @return
     */
    @ApiOperation (value = "出库单序列号分页查询", notes = "sn")
    @GetMapping(value = "/delivery/sn/search")
    ResponseData selectInventoryOutSn(InventoryOutSnSearchReqDTO inventoryOutSnSearchReqDTO);

    /**
     * 出库单流水导出
     * @param inventoryFlowReqDTO
     * @param request
     * @param response
     * @return
     */
    @ApiOperation (value = "出库单流水导出", notes = "出库单流水")
    @GetMapping (value = "/delivery/flow/export")
    void exportInventoryOut(InventoryFlowReqDTO inventoryFlowReqDTO, HttpServletRequest request, HttpServletResponse response);
}
