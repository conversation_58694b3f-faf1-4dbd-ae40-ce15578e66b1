package com.gz.eim.am.stock.dto.request.supplies;

import com.gz.eim.am.stock.dto.request.PageReqDTO;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2020/7/10
 * <p>
 *   物料基本属性和采购属性查询Dto
 * </p>
 */
public class SuppliesPurchaseSearchReqDto extends PageReqDTO {
    private String code;
    /**
     * 物料级别
     */
    private Integer level;

    /**
     * 物料分类编码
     */
    private String catCode;

    /**
     * 物料分类编码集合
     */
    private List<String> catCodeList;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 采购需求id
     */
    private String categoryId;
    /**
     * 物料分类全编码
     */
    private String catFullCode;
    /**
     * 仓库分类编码
     */
    private Integer warehouseType;

    /**
     * 仓库分类编码集合
     */
    private List<Integer> warehouseTypeList;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getCatCode() {
        return catCode;
    }

    public void setCatCode(String catCode) {
        this.catCode = catCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCatFullCode() {
        return catFullCode;
    }

    public void setCatFullCode(String catFullCode) {
        this.catFullCode = catFullCode;
    }

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
    }

    public List<String> getCatCodeList() {
        return catCodeList;
    }

    public void setCatCodeList(List<String> catCodeList) {
        this.catCodeList = catCodeList;
    }

    public List<Integer> getWarehouseTypeList() {
        return warehouseTypeList;
    }

    public void setWarehouseTypeList(List<Integer> warehouseTypeList) {
        this.warehouseTypeList = warehouseTypeList;
    }

    @Override
    public String toString() {
        return "SuppliesPurchaseSearchReqDto{" +
                "code='" + code + '\'' +
                ", level=" + level +
                ", catCode='" + catCode + '\'' +
                ", catCodeList=" + catCodeList +
                ", name='" + name + '\'' +
                ", remark='" + remark + '\'' +
                ", categoryId='" + categoryId + '\'' +
                ", catFullCode='" + catFullCode + '\'' +
                ", warehouseType=" + warehouseType +
                ", warehouseTypeList=" + warehouseTypeList +
                '}';
    }
}
