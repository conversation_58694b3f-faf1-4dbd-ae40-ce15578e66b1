package com.gz.eim.am.stock.dto.response.order;


import com.gz.eim.am.base.dto.response.file.SysAttachDTO;

import java.util.Date;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/10/28
 * @description:
 */
public class InventoryOutRespDTO {
    /**
     * 业务单号
     */
    private String orderNo;
    /**
     * 出库单编号
     */
    private String deliveryNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 出库单状态
     */
    private Integer status;
    /**
     * 调出仓库编码
     */
    private String warehouseCode;
    /**
     * 调入仓库编码
     */
    private String inWarehouseCode;
    /**
     * 实际出库日期
     */
    private Date outStockTime;
    /**
     * 出库单类型
     */
    private Integer outStockType;
    /**
     * 是否需要发货：0 无需发货 1 需要发货
     */
    private Integer isSend;
    /**
     * 指定发货日期
     */
    private Date sendTime;
    /**
     * 配送日期
     */
    private Date deliveryTime;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 出库人工号
     */
    private String dutyUser;
    /**
     * 实际使用人工号
     */
    private String useUser;
    /**
     * 业务单号
     */
    private String bizNo;
    /**
     * 制单人工号
     */
    private String billingUser;
    /**
     * 原因编码
     */
    private Integer reasonCode;

    /**
     * 原因编码名称
     */
    private String reasonTypeName;
    /**
     * 使用人名称
     */
    private String useUserName;
    /**
     * 调出仓库名称
     */
    private String warehouseName;
    /**
     * 调出仓库地址
     */
    private String warehouseAddress;
    /**
     * 调入查库名称
     */
    private String inWarehouseName;
    /**
     * 出库单状态名称
     */
    private String statusName;
    /**
     * 制单人名称
     */
    private String billingUserName;
    /**
     * 出库单详细信息
     */
    private List<InventoryOutDetailRespDTO> detailDTOList;
    /**
     * 领用人名称
     */
    private String dutyUserName;
    /**
     * 入库单id
     */
    private Long deliveryId;
    /**
     * 出库单类型名称
     */
    private String outStockTypeName;

    /**
     * 物料编码名称
     */
    private String supplierName;
    /**
     * 计划出库时间
     */
    private String planOutTime;

    /**
     * 附件
     */
    private List<SysAttachDTO> sysAttachDTOS;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public Date getOutStockTime() {
        return outStockTime;
    }

    public void setOutStockTime(Date outStockTime) {
        this.outStockTime = outStockTime;
    }

    public Integer getOutStockType() {
        return outStockType;
    }

    public void setOutStockType(Integer outStockType) {
        this.outStockType = outStockType;
    }

    public Integer getIsSend() {
        return isSend;
    }

    public void setIsSend(Integer isSend) {
        this.isSend = isSend;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getUseUser() {
        return useUser;
    }

    public void setUseUser(String useUser) {
        this.useUser = useUser;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public Integer getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(Integer reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getReasonTypeName() {
        return reasonTypeName;
    }

    public void setReasonTypeName(String reasonTypeName) {
        this.reasonTypeName = reasonTypeName;
    }

    public String getUseUserName() {
        return useUserName;
    }

    public void setUseUserName(String useUserName) {
        this.useUserName = useUserName;
    }

    public String getInWarehouseName() {
        return inWarehouseName;
    }

    public void setInWarehouseName(String inWarehouseName) {
        this.inWarehouseName = inWarehouseName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public String getBillingUserName() {
        return billingUserName;
    }

    public void setBillingUserName(String billingUserName) {
        this.billingUserName = billingUserName;
    }

    public List<InventoryOutDetailRespDTO> getDetailDTOList() {
        return detailDTOList;
    }

    public void setDetailDTOList(List<InventoryOutDetailRespDTO> detailDTOList) {
        this.detailDTOList = detailDTOList;
    }

    public String getDutyUserName() {
        return dutyUserName;
    }

    public void setDutyUserName(String dutyUserName) {
        this.dutyUserName = dutyUserName;
    }

    public Long getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(Long deliveryId) {
        this.deliveryId = deliveryId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getWarehouseAddress() {
        return warehouseAddress;
    }

    public void setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress;
    }

    public String getOutStockTypeName() {
        return outStockTypeName;
    }

    public void setOutStockTypeName(String outStockTypeName) {
        this.outStockTypeName = outStockTypeName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPlanOutTime() {
        return planOutTime;
    }

    public void setPlanOutTime(String planOutTime) {
        this.planOutTime = planOutTime;
    }


    public List<SysAttachDTO> getSysAttachDTOS() {
        return sysAttachDTOS;
    }

    public void setSysAttachDTOS(List<SysAttachDTO> sysAttachDTOS) {
        this.sysAttachDTOS = sysAttachDTOS;
    }

    @Override
    public String toString() {
        return "InventoryOutRespDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", inWarehouseCode='" + inWarehouseCode + '\'' +
                ", outStockTime=" + outStockTime +
                ", outStockType=" + outStockType +
                ", isSend=" + isSend +
                ", sendTime=" + sendTime +
                ", deliveryTime=" + deliveryTime +
                ", approvalStatus=" + approvalStatus +
                ", dutyUser='" + dutyUser + '\'' +
                ", useUser='" + useUser + '\'' +
                ", bizNo='" + bizNo + '\'' +
                ", billingUser='" + billingUser + '\'' +
                ", reasonCode=" + reasonCode +
                ", reasonTypeName='" + reasonTypeName + '\'' +
                ", useUserName='" + useUserName + '\'' +
                ", warehouseName='" + warehouseName + '\'' +
                ", warehouseAddress='" + warehouseAddress + '\'' +
                ", inWarehouseName='" + inWarehouseName + '\'' +
                ", statusName='" + statusName + '\'' +
                ", billingUserName='" + billingUserName + '\'' +
                ", detailDTOList=" + detailDTOList +
                ", dutyUserName='" + dutyUserName + '\'' +
                ", deliveryId=" + deliveryId +
                ", outStockTypeName='" + outStockTypeName + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", planOutTime='" + planOutTime + '\'' +
                '}';
    }
}
