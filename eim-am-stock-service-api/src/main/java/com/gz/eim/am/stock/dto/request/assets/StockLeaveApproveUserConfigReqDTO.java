package com.gz.eim.am.stock.dto.request.assets;

import lombok.Data;

import java.util.Date;

/**
 * @className: StockLeaveApproveUserConfigReqDTO
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/4/11
 **/
@Data
public class StockLeaveApproveUserConfigReqDTO {

    private Long id;

    private String approveUserId;

    private String entryLocationCode;

    private String cityCode;

    private String provinceCode;

    private Integer status;

    private String remark;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer delFlag;
}
