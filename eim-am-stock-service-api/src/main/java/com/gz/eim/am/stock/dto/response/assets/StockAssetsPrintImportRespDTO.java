package com.gz.eim.am.stock.dto.response.assets;

import lombok.Data;

import java.util.Date;

/**
 * @className: StockAssetsPrintImportRespDTO
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2021/12/22
 **/
@Data
public class StockAssetsPrintImportRespDTO {

    private Long id;

    private String assetsCode;

    /**
     * 资产分类名称
     */
    private String categoryName;
 /**
   * 这里是为了和另一个接口的字段名称一致
   */
    private String assetCode;

    private String assetsName;

    private String snCode;

    private String labelUrl;

    private String batchNo;

    private Integer status;

    private String remark;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer delFlag;
}
