package com.gz.eim.am.stock.api.order.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadFastReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import feign.QueryMap;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: lishuyang
 * @date: 2019/12/13
 * @description: 计划礼品调拨调出api
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId="eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockPlanGiftTransferOutApi {
    /**
     * 保存计划礼品调拨出库单
     * @param deliveryPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "保存计划礼品调拨出库单", notes = "计划礼品调拨出库单")
    @PostMapping(value = "/save")
    ResponseData savePlanGiftTransferOut(@RequestBody DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO);

    /**
     * 礼品调拨出库单分页查询
     * @param deliveryPlanSearchReqDTO
     * @return
     */
    @ApiOperation (value = "计划礼品调拨出库单分页查询", notes = "计划礼品调拨出库单")
    @GetMapping(value = "/search")
    ResponseData selectPlanGiftTransferOut(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO);

    /**
     * 礼品调拨出库单详情查询
     * @param deliveryPlanHeadId
     * @return
     */
    @ApiOperation (value = "计划礼品调拨出库单详情查询", notes = "计划礼品调拨出库单")
    @GetMapping(value = "/search/{deliveryPlanHeadId}")
    ResponseData selectPlanGiftTransferOutById(@PathVariable("deliveryPlanHeadId")Long deliveryPlanHeadId);

    /**
     * 礼品调拨出库单出库
     * @param deliveryPlanHeadReqDTO
     * @return
     */
    @ApiOperation (value = "计划礼品调拨出库单出库", notes = "计划礼品调拨出库单")
    @PutMapping(value = "outBound")
    ResponseData planGiftTransferOutOutBound(@RequestBody DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO);

    /**
     * // 京东卡/油卡库存管理增加内容
     * <p>
     * 调出催办发送呱呱消息接口
     *
     * @param deliveryPlanHeadId
     * @return
     */
    @ApiOperation(value = "调出催办接口", notes = "计划礼品调拨出库单")
    @GetMapping(value = "/urge-send-message/{deliveryPlanHeadId}")
    ResponseData urgeSendMessage(@PathVariable("deliveryPlanHeadId") Long deliveryPlanHeadId);

    /**
     * 计划出库单快速出库
     * @param deliveryPlanHeadFastReqDTO
     * @return
     */
    @ApiOperation(value = "计划出库单快速出库", notes = "计划出库单集合")
    @PutMapping(value = "fast")
    ResponseData deliveryPlanListFastOutBound(@RequestBody DeliveryPlanHeadFastReqDTO deliveryPlanHeadFastReqDTO);


    /**
     * 保存计划礼品领用出库单
     * @param deliveryPlanHeadReqDTO
     * @return
     */
    @ApiOperation(value = "保存计划礼品领用出库单", notes = "计划礼品领用出库单")
    @PostMapping(value = "/insert")
    ResponseData insertPlanGiftTransferUse(@RequestBody DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO);


    /**
     * 查询计划礼品领用出库单
     * @param deliveryPlanSearchReqDTO
     * @return
     */
    @ApiOperation(value = "查询计划礼品领用出库单", notes = "计划礼品领用出库单")
    @GetMapping(value = "/select")
    ResponseData selectPlanGiftTransferUse(@QueryMap DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO);


    /**
     * 查询计划礼品领用出库单详情
     * @param deliveryPlanHeadId
     * @return
     */
    @ApiOperation(value = "查询计划礼品领用出库单详情", notes = "计划礼品领用出库单")
    @GetMapping(value = "/select/{deliveryPlanHeadId}")
    ResponseData selectPlanGiftTransferUseDetail(@PathVariable("deliveryPlanHeadId") Long deliveryPlanHeadId);

    /**
     * 根据计划出库单编号查询出库单(流程平台回调)
     * @param bizId
     * @return
     */
    @ApiOperation (value = "根据出库单查询出库单", notes = "出库单")
    @GetMapping(value = "/select/wfl/{bizId}")
    ResponseData selectDeliveryPlanByDeliveryPlanNo(@PathVariable("bizId")String bizId);




}
