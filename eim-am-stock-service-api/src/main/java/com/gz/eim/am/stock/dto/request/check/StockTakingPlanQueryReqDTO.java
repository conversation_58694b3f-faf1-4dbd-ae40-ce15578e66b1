package com.gz.eim.am.stock.dto.request.check;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 10/30/20 5:49 下午
 * @description
 */
@ToString
@Data
public class StockTakingPlanQueryReqDTO extends PageReqDTO {

    /**
     * 盘点计划单号
     */
    private String takingPlanNo;

    /**
     * 制单人
     */
    private String createdBy;

    /**
     * 盘点单状态 0 进行中 1 已完成
     */
    private Integer billStatus;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 开始时间字符串
     */
    private String startDateStr;

    /**
     * 结束时间字符串
     */
    private String endDateStr;

    /**
     * 初始化 1 是 2 否
     */
    private Integer initFlag;

    private List<String> createdByList;

    /**
     * 当前登录人
     */
    private String  currentLoginUser;
    /**
     * 盘点计划名称
     */
    private String takingPlanName;
}
