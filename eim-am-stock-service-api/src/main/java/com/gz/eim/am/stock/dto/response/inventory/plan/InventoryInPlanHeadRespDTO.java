package com.gz.eim.am.stock.dto.response.inventory.plan;



import java.util.Date;
import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/10/28
 * @description:
 */
public class InventoryInPlanHeadRespDTO {
    /**
     * 计划入库单行计划入库数量之和
     */
    private Integer totalQuantity;
    /**
     * 计划入库单id
     */
    private Long inventoryInPlanHeadId;
    /**
     * 业务单号
     */
    private String bizNo;
    /**
     * 系统表示码
     */
    private String systemCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 计划入库单号
     */
    private String inventoryInPlanNo;
    /**
     * 调入仓库
     */
    private String inWarehouseCode;
    /**
     * 调入仓库管理员
     */
    private String inLinkman;
    /**
     * 调入仓库管理员名称
     */
    private String inLinkmanName;
    /**
     * 调出仓库
     */
    private String outWarehouseCode;
    /**
     * 调出仓库管理员
     */
    private String outLinkman;
    /**
     * 调出仓库管理员名称
     */
    private String outLinkmanName;

    /**
     * 入库类别
     */
    private Integer inventoryInPlanType;
    /**
     * 制单人工号
     */
    private String billingUser;
    /**
     * 制单时间
     */
    private String billingTime;
    /**
     * 入库原因
     */
    private Integer reasonCode;
    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 收货人工号
     */
    private String receiveUser;
    /**
     * 计划入库时间
     */
    private String planInTime;
    /**
     * 送货单号
     */
    private String deliveryNo;
    /**
     * 计划出库单状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 责任人工号/持有人工号
     */
    private String dutyUser;
    /**
     * 需求部门编码
     */
    private String demandDeptCode;
    /**
     * 收货人名称
     */
    private String receiveUserName;
    /**
     * 收货人电话
     */
    private String receiveUserPhone;
    /**
     * 制单人名称
     */
    private String billingUserName;
    /**
     * 入库仓库地址
     */
    private String inWarehouseAddress;
    /**
     * 入库仓库名称
     */
    private String inWarehouseName;
    /**
     * 入库状态名称
     */
    private String statusName;
    /**
     * 入库单详细信息
     */
    private List<InventoryInPlanLineRespDTO> inventoryInPlanLineRespDTOS;
    /**
     * 入库单类别名称
     */
    private String inventoryInPlanTypeName;

    /**
     * 原因编码名称
     */
    private String reasonTypeName;
    /**
     * 调出仓库名称
     */
    private String outWarehouseName;
    /**
     * 调出仓库地址
     */
    private String outWarehouseAddress;
    /**
     * 责任人名称/持有人名称
     */
    private String dutyUserName;
    /**
     * 需求部门名称
     */
    private String demandDeptName;
    /**
     * 计划入库单行资产集合
     */
    private List<InventoryPlanAssetRespDTO>  inventoryPlanAssetRespDTOS;
    /**
     * 归还人部门名称
     */
    private String dutyUserDeptName;
    /**
     * 归还人职级
     */
    private String dutyUserOccasionName;
    /**
     * 计划入库单行资产数据数量（调入和归还明细查询）
     */
    private long lineAssetsCount;

    /**
     * 计划入库单行资产数据（调入和归还明细查询）
     */
    private List<InventoryInPlanLineAssetRespDTO> inventoryInPlanLineAssetRespDTOS;


    public Long getInventoryInPlanHeadId() {
        return inventoryInPlanHeadId;
    }

    public void setInventoryInPlanHeadId(Long inventoryInPlanHeadId) {
        this.inventoryInPlanHeadId = inventoryInPlanHeadId;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public String getInventoryInPlanNo() {
        return inventoryInPlanNo;
    }

    public void setInventoryInPlanNo(String inventoryInPlanNo) {
        this.inventoryInPlanNo = inventoryInPlanNo;
    }

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public String getOutWarehouseCode() {
        return outWarehouseCode;
    }

    public void setOutWarehouseCode(String outWarehouseCode) {
        this.outWarehouseCode = outWarehouseCode;
    }

    public Integer getInventoryInPlanType() {
        return inventoryInPlanType;
    }

    public void setInventoryInPlanType(Integer inventoryInPlanType) {
        this.inventoryInPlanType = inventoryInPlanType;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }

    public Integer getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(Integer reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getReceiveUser() {
        return receiveUser;
    }

    public void setReceiveUser(String receiveUser) {
        this.receiveUser = receiveUser;
    }

    public String getPlanInTime() {
        return planInTime;
    }

    public void setPlanInTime(String planInTime) {
        this.planInTime = planInTime;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public String getDemandDeptCode() {
        return demandDeptCode;
    }

    public void setDemandDeptCode(String demandDeptCode) {
        this.demandDeptCode = demandDeptCode;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    public String getReceiveUserPhone() {
        return receiveUserPhone;
    }

    public void setReceiveUserPhone(String receiveUserPhone) {
        this.receiveUserPhone = receiveUserPhone;
    }

    public String getBillingUserName() {
        return billingUserName;
    }

    public void setBillingUserName(String billingUserName) {
        this.billingUserName = billingUserName;
    }

    public String getInWarehouseAddress() {
        return inWarehouseAddress;
    }

    public void setInWarehouseAddress(String inWarehouseAddress) {
        this.inWarehouseAddress = inWarehouseAddress;
    }

    public String getInWarehouseName() {
        return inWarehouseName;
    }

    public void setInWarehouseName(String inWarehouseName) {
        this.inWarehouseName = inWarehouseName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public List<InventoryInPlanLineRespDTO> getInventoryInPlanLineRespDTOS() {
        return this.inventoryInPlanLineRespDTOS;
    }

    public void setInventoryInPlanLineRespDTOS(final List<InventoryInPlanLineRespDTO> inventoryInPlanLineRespDTOS) {
        this.inventoryInPlanLineRespDTOS = inventoryInPlanLineRespDTOS;
    }

    public String getInventoryInPlanTypeName() {
        return inventoryInPlanTypeName;
    }

    public void setInventoryInPlanTypeName(String inventoryInPlanTypeName) {
        this.inventoryInPlanTypeName = inventoryInPlanTypeName;
    }

    public String getReasonTypeName() {
        return reasonTypeName;
    }

    public void setReasonTypeName(String reasonTypeName) {
        this.reasonTypeName = reasonTypeName;
    }

    public String getOutWarehouseName() {
        return outWarehouseName;
    }

    public void setOutWarehouseName(String outWarehouseName) {
        this.outWarehouseName = outWarehouseName;
    }

    public String getOutWarehouseAddress() {
        return outWarehouseAddress;
    }

    public void setOutWarehouseAddress(String outWarehouseAddress) {
        this.outWarehouseAddress = outWarehouseAddress;
    }

    public String getDutyUserName() {
        return dutyUserName;
    }

    public void setDutyUserName(String dutyUserName) {
        this.dutyUserName = dutyUserName;
    }

    public String getDemandDeptName() {
        return demandDeptName;
    }

    public void setDemandDeptName(String demandDeptName) {
        this.demandDeptName = demandDeptName;
    }

    public List<InventoryPlanAssetRespDTO> getInventoryPlanAssetRespDTOS() {
        return inventoryPlanAssetRespDTOS;
    }

    public void setInventoryPlanAssetRespDTOS(List<InventoryPlanAssetRespDTO> inventoryPlanAssetRespDTOS) {
        this.inventoryPlanAssetRespDTOS = inventoryPlanAssetRespDTOS;
    }

    public String getDutyUserDeptName() {
        return dutyUserDeptName;
    }

    public void setDutyUserDeptName(String dutyUserDeptName) {
        this.dutyUserDeptName = dutyUserDeptName;
    }

    public String getDutyUserOccasionName() {
        return dutyUserOccasionName;
    }

    public void setDutyUserOccasionName(String dutyUserOccasionName) {
        this.dutyUserOccasionName = dutyUserOccasionName;
    }

    public List<InventoryInPlanLineAssetRespDTO> getInventoryInPlanLineAssetRespDTOS() {
        return inventoryInPlanLineAssetRespDTOS;
    }

    public void setInventoryInPlanLineAssetRespDTOS(List<InventoryInPlanLineAssetRespDTO> inventoryInPlanLineAssetRespDTOS) {
        this.inventoryInPlanLineAssetRespDTOS = inventoryInPlanLineAssetRespDTOS;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public long getLineAssetsCount() {
        return lineAssetsCount;
    }

    public void setLineAssetsCount(long lineAssetsCount) {
        this.lineAssetsCount = lineAssetsCount;
    }

    public String getInLinkman() {
        return inLinkman;
    }

    public void setInLinkman(String inLinkman) {
        this.inLinkman = inLinkman;
    }

    public String getInLinkmanName() {
        return inLinkmanName;
    }

    public void setInLinkmanName(String inLinkmanName) {
        this.inLinkmanName = inLinkmanName;
    }

    public String getOutLinkman() {
        return outLinkman;
    }

    public void setOutLinkman(String outLinkman) {
        this.outLinkman = outLinkman;
    }

    public String getOutLinkmanName() {
        return outLinkmanName;
    }

    public void setOutLinkmanName(String outLinkmanName) {
        this.outLinkmanName = outLinkmanName;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }
}
