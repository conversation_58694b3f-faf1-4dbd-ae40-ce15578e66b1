package com.gz.eim.am.stock.dto.response.assets;

import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2021/1/6
 * @description
 */
@Data
@ToString
public class AssetsOperationLogRespDTO {
    private Long id;

    private String assetsCode;
    /**
     * 修改类型 1 手动更新，2 业务更新， 3 初始化导入
     */
    private Integer operationType;

    private String operationTypeText;

    private Date updatedAt;

    private String updatedByName;

    private String content;

    /**
     * 图片是否存在差异：0无差异；1有差异
     */
    private Integer assetPicDiff;

    /**
     * 变更前图片url
     */
    private List<String> lastPicList;
    /**
     * 变更后图片url
     */
    private List<String> nextPicList;
}
