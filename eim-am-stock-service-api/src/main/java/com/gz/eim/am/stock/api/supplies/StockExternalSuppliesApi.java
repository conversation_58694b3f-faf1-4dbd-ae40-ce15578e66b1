package com.gz.eim.am.stock.api.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesExternalReqDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesExternalRespDTO;

import feign.QueryMap;

import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @describe 获取物料相关信息
 * <AUTHOR>
 * @date 2020-05-18 14:45
 **/
@FeignClient(
    name = "eim-am-stock",
    serviceId = "eim-am-stock",
    path = "/api/am/stock/external/supplies",
    configuration = FeignClientConfiguration.class
)

public interface StockExternalSuppliesApi {

    /**
     * 批量获取
     * <AUTHOR>
     * @date 2020-05-18 14:56
     * @param externalReqDTO
     * @return list
     * @throws Exception ex
     **/
    @ApiOperation(value = "批量获取", notes = "物料信息")
    @GetMapping(value = "/find-by-code")
    ResponseData<SuppliesExternalRespDTO> getSuppliesByCode(@QueryMap final SuppliesExternalReqDTO externalReqDTO);

}
