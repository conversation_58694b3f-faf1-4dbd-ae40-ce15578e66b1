package com.gz.eim.am.stock.dto.request.discount;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Author: wangjing67
 * @Date: 4/1/21 11:22 上午
 * @description  变更行信息
 */
@ToString
@Data
public class StockLicenseChangeItemsReqDTO {

    private Long id;

    private String changeNo;

    private Integer changeType;

    private String beforeValue;

    private String afterValue;

    private String beforeIdCard;

    private String afterIdCard;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;
}
