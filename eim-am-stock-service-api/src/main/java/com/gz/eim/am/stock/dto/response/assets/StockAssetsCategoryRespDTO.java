package com.gz.eim.am.stock.dto.response.assets;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class StockAssetsCategoryRespDTO {
    private Long id;

    private String parentCategoryCode;

    private String parentCategoryName;

    private String categoryCode;

    private String categoryName;

    private Integer defaultUsedTime;

    private BigDecimal defaultUsedCost;

    private Integer status;

    private Integer syncEbsFlag;

    private BigDecimal lowestAmount;

    private Integer delFlag;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private String pictureUrl;
}