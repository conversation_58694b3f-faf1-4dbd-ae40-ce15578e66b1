package com.gz.eim.am.stock.dto.request.order.plan;

import lombok.ToString;
import java.util.Date;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/12/23
 * @description
 */
@ToString
public class PurchaseDeliveryHeadDTO {

    /**
     * 单据类型
     */
    private Integer docType;
    /**
     * 公司编码
     */
    private String companyCode;
    /**
     * 需求部门编码
     */
    private String demandDeptCode;
    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 系统识别码
     */
    private String systemCode;
    /**
     * 制单人
     */
    private String billingUser;
    /**
     * 出库仓库编码
     */
    private String warehouseCode;
    /**
     * 出库仓库类型
     */
    private Integer warehouseType;
    /**
     * 计划出库日期
     */
    private Date planOutTime;
    /**
     * 采购订单号
     */
    private String purchaseOrderNo;
    /**
     * 退货单单号
     */
    private String bizNo;
    /**
     * 出库备注
     */
    private String remark;
    /**
     * 订单行集合
     */
    private List<PurchaseDeliveryLineDTO> purchaseDeliveryLineDTOS;

    public Integer getDocType() {
        return docType;
    }

    public void setDocType(Integer docType) {
        this.docType = docType;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getDemandDeptCode() {
        return demandDeptCode;
    }

    public void setDemandDeptCode(String demandDeptCode) {
        this.demandDeptCode = demandDeptCode;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
    }

    public Date getPlanOutTime() {
        return planOutTime;
    }

    public void setPlanOutTime(Date planOutTime) {
        this.planOutTime = planOutTime;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<PurchaseDeliveryLineDTO> getPurchaseDeliveryLineDTOS() {
        return purchaseDeliveryLineDTOS;
    }

    public void setPurchaseDeliveryLineDTOS(List<PurchaseDeliveryLineDTO> purchaseDeliveryLineDTOS) {
        this.purchaseDeliveryLineDTOS = purchaseDeliveryLineDTOS;
    }
}
