package com.gz.eim.am.stock.dto.request.inventory;

import java.util.List;

/**
 * 入库单
 * <AUTHOR>
 * @date  2019-09-25 下午 9:01
 */
public class InventoryInDTO {

    /**
     * 入库单id
     */
    private Long inventoryInId;
    /**
     * 入库批次号
     */
    private String inventoryInBatch;
    /**
     * 入库单编号
     */
    private String inventoryInNo;
    /**
     * 调入仓库
     */
    private String inWarehouseCode;
    /**
     * 调出仓库
     */
    private String outWarehouseCode;
    /**
     * 入库单类别
     */
    private Integer inventoryInType;
    /**
     * 送货单号
     */
    private String deliveryNo;
    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 公司主体
     */
    private String companyCode;
    /**
     * 项目
     */
    private String project;
    /**
     * 公司主体
     */
    private String activity;
    /**
     * 入库人工号
     */
    private String inventoryInUser;
    /**
     * 实际入库时间
     */
    private String inventoryInTime;
    /**
     * 入库单状态
     */
    private Integer status;
    /**
     * 计划入库时间
     */
    private String planInTime;
    /**
     * 责任人
     */
    private String dutyUser;
    /**
     * 审批状态
     */
    private Byte approvalStatus;
    /**
     * 公司名称
     */
    private String company;
    /**
     * 业务单号
     */
    private String bizNo;
    /**
     * 物料编码
     */
    private String supplierCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 收货人工号
     */
    private String receiveUser;
    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * 制单人工号
     */
    private String billingUser;
    /**
     * 制单时间
     */
    private String billingTime;
    /**
     * 入库单详细
     */
    private List<InventoryInSuppliesDTO> supplies;
    /**
     * 是否直接入库
     */
    private Integer isDirectInStock;

    public Long getInventoryInId() {
        return inventoryInId;
    }

    public void setInventoryInId(Long inventoryInId) {
        this.inventoryInId = inventoryInId;
    }

    public String getInventoryInBatch() {
        return inventoryInBatch;
    }

    public void setInventoryInBatch(String inventoryInBatch) {
        this.inventoryInBatch = inventoryInBatch;
    }

    public String getInventoryInNo() {
        return inventoryInNo;
    }

    public void setInventoryInNo(String inventoryInNo) {
        this.inventoryInNo = inventoryInNo;
    }

    public String getInWarehouseCode() {
        return inWarehouseCode;
    }

    public void setInWarehouseCode(String inWarehouseCode) {
        this.inWarehouseCode = inWarehouseCode;
    }

    public String getOutWarehouseCode() {
        return outWarehouseCode;
    }

    public void setOutWarehouseCode(String outWarehouseCode) {
        this.outWarehouseCode = outWarehouseCode;
    }

    public Integer getInventoryInType() {
        return inventoryInType;
    }

    public void setInventoryInType(Integer inventoryInType) {
        this.inventoryInType = inventoryInType;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getActivity() {
        return activity;
    }

    public void setActivity(String activity) {
        this.activity = activity;
    }

    public String getInventoryInUser() {
        return inventoryInUser;
    }

    public void setInventoryInUser(String inventoryInUser) {
        this.inventoryInUser = inventoryInUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser;
    }

    public Byte getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Byte approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReceiveUser() {
        return receiveUser;
    }

    public void setReceiveUser(String receiveUser) {
        this.receiveUser = receiveUser;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public List<InventoryInSuppliesDTO> getSupplies() {
        return supplies;
    }

    public void setSupplies(List<InventoryInSuppliesDTO> supplies) {
        this.supplies = supplies;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public Integer getIsDirectInStock() {
        return isDirectInStock;
    }

    public void setIsDirectInStock(Integer isDirectInStock) {
        this.isDirectInStock = isDirectInStock;
    }

    public String getInventoryInTime() {
        return inventoryInTime;
    }

    public void setInventoryInTime(String inventoryInTime) {
        this.inventoryInTime = inventoryInTime;
    }

    public String getPlanInTime() {
        return planInTime;
    }

    public void setPlanInTime(String planInTime) {
        this.planInTime = planInTime;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }

    @Override
    public String toString() {
        return "InventoryInDTO{" +
                "inventoryInId=" + inventoryInId +
                ", inventoryInBatch='" + inventoryInBatch + '\'' +
                ", inventoryInNo='" + inventoryInNo + '\'' +
                ", inWarehouseCode='" + inWarehouseCode + '\'' +
                ", outWarehouseCode='" + outWarehouseCode + '\'' +
                ", inventoryInType=" + inventoryInType +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", deptCode='" + deptCode + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", project='" + project + '\'' +
                ", activity='" + activity + '\'' +
                ", inventoryInUser='" + inventoryInUser + '\'' +
                ", inventoryInTime=" + inventoryInTime +
                ", status=" + status +
                ", planInTime=" + planInTime +
                ", dutyUser='" + dutyUser + '\'' +
                ", approvalStatus=" + approvalStatus +
                ", company='" + company + '\'' +
                ", bizNo='" + bizNo + '\'' +
                ", supplierCode='" + supplierCode + '\'' +
                ", remark='" + remark + '\'' +
                ", receiveUser='" + receiveUser + '\'' +
                ", vendorCode='" + vendorCode + '\'' +
                ", vendorName='" + vendorName + '\'' +
                ", billingUser='" + billingUser + '\'' +
                ", billingTime=" + billingTime +
                ", supplies=" + supplies +
                ", isDirectInStock=" + isDirectInStock +
                '}';
    }
}