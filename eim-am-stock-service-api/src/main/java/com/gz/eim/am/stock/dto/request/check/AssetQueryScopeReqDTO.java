package com.gz.eim.am.stock.dto.request.check;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @Author: wangjing67
 * @Date: 11/2/20 4:32 下午
 * @description
 */
@Data
@ToString
public class AssetQueryScopeReqDTO extends PageReqDTO {

    /*private List<Integer> assetStatusList;

    *//**
     * 资产管理员
     *//*
    private List<String> assetsKeeperList;


    *//**
     * 资产持有人
     *//*
    private List<String> holderList;


    *//**
     * 资产所在仓库
     *//*
    private List<String> warehouseCodeList;

    *//**
     * 资产所在大区集合
     *//*
    private List<String> busLargeRegionList;

    *//**
     * 资产所在大区
     *//*
    private String busLargeRegion;

    *//**
     * 资产所在城市集合
     *//*
    private List<String> busCityList;

    *//**
     * 资产所在城市
     *//*
    private String busCity;

    *//**
     * 资产编码集合
     *//*
    private List<String> assetsCodeList;

    *//**
     * 资产编码
     *//*
    private String assetsCode;

    *//**
     * 设备序列号集合
     *//*
    private List<String> snCodeList;

    *//**
     * 设备序列号
     *//*
    private String snCode;

    *//**
     * 采购码集合
     *//*
    private List<String> purchaseNoList;

    *//**
     * 采购码
     *//*
    private String purchaseNo;

    *//**
     * 需求部门
     *//*
    private List<String> needDeptList;

    *//**
     * 资产类别
     *//*
    private List<String> categoryList;

    *//**
     * 资产类别
     *//*
    private String category;
    *//**
     * 关键字集合
     *//*
    private List<String> keyWordList;

    private String keyWord;*/
    /**
     * 资产编码
     */
    private String assetsCode;


    /////////////////////查询条件调整/////////////////////////
    /**
     * 是否在库
     */
    private Integer assetStatusInFlag;

    /**
     * 资产所在仓库(在库)
     */
    private List<String> warehouseCodeList;

    /**
     * 资产类别(在库)
     */
    private List<String> inCategoryList;

    /**
     * 费用部门（在库）
     */
    private List<String> inCostDeptList;

    /**
     * 资产管理员(在库)
     */
    private List<String> inAssetsKeeperList;

    /**
     * 是否使用中
     */
    private Integer assetStatusUsedFlag;

    /**
     * 资产所在仓库(使用中)
     */
    private List<String> holderList;

    /**
     * 资产类别(使用中)
     */
    private List<String> usedCategoryList;

    /**
     * 费用部门（使用中）
     */
    private List<String> usedCostDeptList;

    /**
     * 资产管理员(使用中)
     */
    private List<String> usedAssetsKeeperList;


    /*****************以下为盘点任务需要参数*******************/

    /**
     * 盘点计划单号
     */
    private String takingPlanNo;
    /**
     * 快照中的资产是否被移除 0 未移除 1 已移除
     */
    private Integer snapshotAssetDeleteFlag;

    /**
     * 快照中的资产是否分配到任务0 为分配 1已分配
     */
    private Integer snapshotAssetAssignFlag;

    /**
     * 盘点任务id
     */
    private Long checkTaskId;
    /**
     * 资产状态
     */
    private Integer assetStatus;
    /**
     * 资产使用情况
     */
    private Integer assetsConditions;
    /**
     * 资产持有人
     */
    private String holder;
    /**
     * 资产管理员
     */
    private String assetsKeeper;
    /**
     * 资产所在仓库
     */
    private String warehouseCode;
    /**
     * 资产所属部门
     */
    private String needDept;
    /**
     * 盘点状态
     */
    private Integer checkFlag;
    /**
     * 盘点人
     */
    private String checkPeople;
    /**
     * 是否为新上报的资产
     */
    private Integer newInsertFlag;
    /**
     * 是否盘点异常
     */
    private Integer difference;
    /**
     * 不包含的盘点状态
     */
    private Integer noContainCheckFlag;
    /**
     * 不包含的资产状态
     */
    private Integer noContainAssetStatus;
    /**
     * 差异清单头id
     */
    private Long headId;
    /**
     * 差异清单行备注是否为空
     */
    private Integer remarkIsNull;
    /**
     * 盘点方式默认盘点人代码
     */
    private String defaultCheckPeople;
    /**
     * 任务创建人
     */
    private String taskCreatedBy;
    /**
     * 盘点差异数据类型
     */
    private Integer type;

    /**
     * 不包含的盘点调整
     */
    private Integer noContainCheckType;

    /**
     * 是否线下盘点
     */
    private Integer offlineCheckFlag;

    /**
     * 差异是否完成调整
     */
    private Integer adjustFlag;

    /**
     * 是否系统自动盘点
     */
    private Integer isAutoCheck;

    /**
     * 仓库类型
     */
    private List<Integer> warehouseTypeList;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 排除人员集合
     */
    private List<String> excludePeopleList;

    /**
     * 判断参数是否全部为空
     * @return
     */
    public boolean paramAllIsNull(){
        if (this.assetStatusInFlag == null && this.assetStatusUsedFlag == null
                && CollectionUtils.isEmpty(this.warehouseCodeList) && CollectionUtils.isEmpty(this.inCategoryList)
                && CollectionUtils.isEmpty(this.inCostDeptList) && CollectionUtils.isEmpty(this.inAssetsKeeperList)
                && CollectionUtils.isEmpty(this.holderList) && CollectionUtils.isEmpty(this.usedCategoryList)
                && CollectionUtils.isEmpty(this.usedCostDeptList) && CollectionUtils.isEmpty(this.usedAssetsKeeperList)
                && CollectionUtils.isEmpty(this.warehouseTypeList)){
            return true;
        }
        return false;
    }

    /**
     * 清空数据，重复利用该对象做查询入参
     */
    public void clear(){
        this.assetStatusInFlag = null;
        this.warehouseCodeList = null;
        this.inCategoryList = null;
        this.inCostDeptList = null;
        this.inAssetsKeeperList = null;
        this.assetStatusUsedFlag = null;
        this.holderList = null;
        this.usedCategoryList = null;
        this.usedCostDeptList = null;
        this.usedAssetsKeeperList = null;
        this.warehouseTypeList = null;
    }

    /**
     * 初始化在库标识
     */
    public void initAssetStatusInFlag() {
        if (CollectionUtils.isNotEmpty(this.getInAssetsKeeperList()) || CollectionUtils.isNotEmpty(this.getInCategoryList())
                || CollectionUtils.isNotEmpty(this.getInCostDeptList()) || CollectionUtils.isNotEmpty(this.getWarehouseCodeList())) {
            this.setAssetStatusInFlag(1);
        }
    }

    /**
     * 初始化使用中标识
     */
    public void initAssetStatusUsedFlag() {
        if (CollectionUtils.isNotEmpty(this.getUsedAssetsKeeperList()) || CollectionUtils.isNotEmpty(this.getUsedCategoryList())
                || CollectionUtils.isNotEmpty(this.getUsedCostDeptList()) || CollectionUtils.isNotEmpty(this.getHolderList())) {
            this.setAssetStatusUsedFlag(1);
        }
    }
}
