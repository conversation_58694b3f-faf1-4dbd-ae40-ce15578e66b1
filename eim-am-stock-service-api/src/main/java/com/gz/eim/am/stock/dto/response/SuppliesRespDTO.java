package com.gz.eim.am.stock.dto.response;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019-10-11 下午 8:27
 */
public class SuppliesRespDTO {

    private String code;

    private String name;

    private String unit;

    private String remark;

    private BigDecimal purchasePrice;

    private Integer manageType;

    private String costItemCode;

    /**
     * 真实库存量
     */
    private BigDecimal actualQuantity;

    /**
     * 物料类型
     */
    private Integer type;

    /**
     * 法人信息
     */
    private String licenseLegalPerson;

    /**
     * 法人姓名
     */
    private String licenseLegalPersonName;

    /**
     * 法人身份证信息
     */
    private String corporateIdNumber;

    /**
     * 注册地址
     */
    private String registeredAddress;
    /**
     * 资产类型编码
     */
    private String categoryCode;

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public BigDecimal getActualQuantity() {
        return actualQuantity;
    }

    public void setActualQuantity(BigDecimal actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getPurchasePrice() {
        return this.purchasePrice;
    }

    public void setPurchasePrice(final BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public Integer getManageType() {
        return this.manageType;
    }

    public void setManageType(final Integer manageType) {
        this.manageType = manageType;
    }

    public String getCostItemCode() {
        return costItemCode;
    }

    public void setCostItemCode(String costItemCode) {
        this.costItemCode = costItemCode;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getLicenseLegalPerson() {
        return licenseLegalPerson;
    }

    public void setLicenseLegalPerson(String licenseLegalPerson) {
        this.licenseLegalPerson = licenseLegalPerson;
    }

    public String getLicenseLegalPersonName() {
        return licenseLegalPersonName;
    }

    public void setLicenseLegalPersonName(String licenseLegalPersonName) {
        this.licenseLegalPersonName = licenseLegalPersonName;
    }

    public String getCorporateIdNumber() {
        return corporateIdNumber;
    }

    public void setCorporateIdNumber(String corporateIdNumber) {
        this.corporateIdNumber = corporateIdNumber;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    @Override
    public String toString() {
        return "SuppliesRespDTO{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", unit='" + unit + '\'' +
                ", remark='" + remark + '\'' +
                ", purchasePrice=" + purchasePrice +
                ", manageType=" + manageType +
                ", costItemCode='" + costItemCode + '\'' +
                ", type=" + type +
                ", licenseLegalPerson='" + licenseLegalPerson + '\'' +
                ", licenseLegalPersonName='" + licenseLegalPersonName + '\'' +
                ", corporateIdNumber='" + corporateIdNumber + '\'' +
                ", registeredAddress='" + registeredAddress + '\'' +
                '}';
    }
}
