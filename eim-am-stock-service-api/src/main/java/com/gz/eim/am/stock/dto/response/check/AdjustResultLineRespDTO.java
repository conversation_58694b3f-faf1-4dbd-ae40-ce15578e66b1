package com.gz.eim.am.stock.dto.response.check;

import lombok.Data;
import lombok.ToString;
import java.math.BigDecimal;

/**
 * @author: weijunjie
 * @date: 2021/2/25
 * @description
 */
@Data
@ToString
public class AdjustResultLineRespDTO {
    /**
     * 资产编码
     */
    private String assetsCode;
    /**
     * 资产名称
     */
    private String assetsName;
    /**
     * 资产状态编码
     */
    private Integer assetsStatus;
    /**
     * 资产状态名称
     */
    private String assetsStatusName;
    /**
     * 仓库
     */
    private String warehouseCodeName;
    /**
     * 资产管理员编码
     */
    private String assetsKeeperCode;
    /**
     * 资产管理员名称
     */
    private String assetsKeeperName;
    /**
     * 所属公司
     */
    private String companyName;
    /**
     * 剩余使用年限
     */
    private Integer timeLeft;
    /**
     * 预估金额（人民币）
     */
    private BigDecimal estimatedAmount;

}
