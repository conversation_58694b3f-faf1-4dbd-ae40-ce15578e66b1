package com.gz.eim.am.stock.api.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.config.FeignClientConfiguration;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @author: weijunjie
 * @date: 2020/11/18
 * @description
 */
@FeignClient(
        name = "eim-am-stock",
        serviceId = "eim-am-stock",
        configuration = FeignClientConfiguration.class
)
public interface StockCheckOperatorControllerApi {

    /**
     * 盘点文件上传接口
     * @param checkTaskId
     * @param file
     * @return
     * @throws IOException
     */
    @ApiOperation(value = "盘点文件上传", notes = "盘点文件上传")
    @PostMapping(value = "/import/{checkTaskId}")
    ResponseData checkFileImport(@RequestParam("file") MultipartFile file,  @PathVariable("checkTaskId") Long checkTaskId) throws IOException;

    /**
     * 盘点上传数据查询接口
     * @param assetQueryScopeReqDTO
     * @return
      */
    @ApiOperation(value = "盘点上传数据查询", notes = "盘点上传数据查询")
    @GetMapping(value = "/uploadDataShow")
    ResponseData uploadDataShow(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 盘点上传数据确认接口
     * @param checkTaskId
     * @return
     */
    @ApiOperation(value = "盘点上传数据查询", notes = "盘点上传数据查询")
    @PostMapping(value = "/uploadDataConfirm/{checkTaskId}")
    ResponseData uploadDataConfirm(@PathVariable Long checkTaskId);

}
