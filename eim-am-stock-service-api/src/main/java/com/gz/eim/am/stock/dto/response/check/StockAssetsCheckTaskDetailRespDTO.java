package com.gz.eim.am.stock.dto.response.check;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @Author: wangjing67
 * @Date: 11/9/20 5:36 下午
 * @description
 */
@Data
@ToString
public class StockAssetsCheckTaskDetailRespDTO {

    private Integer checkMethod;

    private String checkMethodName;

    private Long taskDetailId;

    private Long checkTaskId;

    private String takingPlanNo;

    private String checkPeople;

    private String checkPeopleName;

    private String assetsKeeper;

    private String holderAddressProvinceName;

    private String holderAddressCityName;

    private String holderAddressCountyName;

    private String assetsKeeperName;

    private String snapshotAssetsCode;

    private String snapshotAssetsName;

    private String snapshotSnNo;

    private String snapshotAssetsCategory;

    private Integer snapshotAssetsStatus;

    private String snapshotAssetsStatusName;

    private Integer snapshotAssetsConditions;

    private String snapshotAssetsConditionsName;

    private String snapshotAssetsHolder;

    private String snapshotAssetsHolderName;

    private Date snapshotHolderTime;

    private String snapshotHolderAddress;

    private String snapshotWarehouseCode;

    private String snapshotWarehouseName;

    private String snapshotModel;

    private String snapshotBrand;

    private String snapshotAssetsCpu;

    private String snapshotHardDisk;

    private String snapshotRamMemory;

    private String realAssetsCpu;

    private String realHardDisk;

    private String realRamMemory;

    private String realAssetsCode;

    private String realAssetsName;

    private Integer realAssetsStatus;

    private String realAssetsStatusName;

    private Integer realAssetsConditions;

    private String realAssetsConditionsName;

    private String realAssetsSnno;

    private String realAssetsModel;

    private String realAssetsBrand;

    private String realAssetsHolder;

    private String realAssetsHolderName;

    private String realHolderAddress;

    private Date realHolderTime;

    private String realWarehouseCode;

    private String realWarehouseName;

    private String realPicturesUrl;

    private String needDept;

    private String costDept;

    private String costDeptName;

    private Integer snapshotNumber;

    private Integer realNumber;

    private Integer difference;

    private String differenceName;

    private Integer ownFlag;

    private String errMessage;

    private String errDesc;

    private Integer newInsertFlag;

    private Integer checkFlag;

    private String checkFlagName;

    private String remark;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    /**
     * 首字母
     */
    private String firstCharacter;

    private Integer isNeedTakePicture;

    private String checkDate;

    private Integer isAutoCheck;

    private String guaguaLoginSn;

}
