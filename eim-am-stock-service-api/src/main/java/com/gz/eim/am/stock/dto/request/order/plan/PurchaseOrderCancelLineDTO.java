package com.gz.eim.am.stock.dto.request.order.plan;

import lombok.ToString;
import java.util.Objects;

/**
 * @author: weijunjie
 * @date: 2020/12/23
 * @description
 */
@ToString
public class PurchaseOrderCancelLineDTO {
    /**
     * 采购订单号
     */
    private String purchaseOrderNo;
    /**
     * 验收单行编码
     */
    private String receiveItemNo;
    /**
     * 取消备注
     */
    private String cancelRemark;
    /**
     * 返回取消结果（0:取消处理失败 1：取消处理成功）
     */
    private Integer cancelResultCode;
    /**
     * 取消失败描述
     */
    private String  errDescribe;


    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getReceiveItemNo() {
        return receiveItemNo;
    }

    public void setReceiveItemNo(String receiveItemNo) {
        this.receiveItemNo = receiveItemNo;
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
    }

    public Integer getCancelResultCode() {
        return cancelResultCode;
    }

    public void setCancelResultCode(Integer cancelResultCode) {
        this.cancelResultCode = cancelResultCode;
    }

    public String getErrDescribe() {
        return errDescribe;
    }

    public void setErrDescribe(String errDescribe) {
        this.errDescribe = errDescribe;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PurchaseOrderCancelLineDTO that = (PurchaseOrderCancelLineDTO) o;
        return Objects.equals(purchaseOrderNo, that.purchaseOrderNo) &&
                Objects.equals(receiveItemNo, that.receiveItemNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(purchaseOrderNo, receiveItemNo);
    }
}
