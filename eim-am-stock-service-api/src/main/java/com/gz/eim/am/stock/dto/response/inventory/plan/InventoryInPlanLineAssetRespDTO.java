package com.gz.eim.am.stock.dto.response.inventory.plan;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: weijunjie
 * @date: 2020/7/27
 * @description
 */
public class InventoryInPlanLineAssetRespDTO {

    /**
     * 资产编号
     */
    String assetCode;
    /**
     * 序列号
     */
    private String snNo;

    /* 5.0V添加属性 */
    /**
     * 行资产id
     */
    private Long id;
    /**
     * 资产情况编码
     */
    private Integer assetsCondition;
    /**
     * 资产情况名称
     */
    private String assetsConditionName;
    /**
     * 处理方式编码
     */
    private Integer dealType;
    /**
     * 处理方式名称
     */
    private String dealTypeName;
    /**
     * 实际入库仓库编码
     */
    private String realInWarehouse;
    /**
     * 实际入库仓库名称
     */
    private String realInWarehouseName;
    /**
     * 责任主体编码
     */
    private Integer dutyBody;
    /**
     * 责任主体名称
     */
    private String dutyBodyName;
    /**
     * 责任人工号
     */
    private String personLiable;
    /**
     * 责任人名称
     */
    private String personLiableName;
    /**
     * 资产金额
     */
    private BigDecimal amount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 资产入库状态
     */
    private Integer status;
    /**
     * 资产入库状态名
     */
    private String statusName;
    /**
     * 资产赔偿支付状态
     */
    private Integer payStatus;
    /**
     * 资产赔偿支付状态名
     */
    private String payStatusName;
    /**
     * 资产编码
     */
    private String assetsCode;
    /**
     * 资产名称
     */
    private String assetsName;
    /**
     * 调出时间
     */
    private String outWareHouseTime;

    /**
     * 出库时间，对应数据库
     */
    private Date outTime;
    /**
     * 资产品牌
     */
    private String brand;
    /**
     * 资产型号
     */
    private String model;

    /**
     * 领用备注信息
     */
    private String explainRemark;

    /**
     * 执照法人信息
     */
    private String licenseLegalPerson;


    /**
     * 执照法人信息
     */
    private String licenseLegalPersonName;

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getAssetsCondition() {
        return assetsCondition;
    }

    public void setAssetsCondition(Integer assetsCondition) {
        this.assetsCondition = assetsCondition;
    }

    public String getAssetsConditionName() {
        return assetsConditionName;
    }

    public void setAssetsConditionName(String assetsConditionName) {
        this.assetsConditionName = assetsConditionName;
    }

    public Integer getDealType() {
        return dealType;
    }

    public void setDealType(Integer dealType) {
        this.dealType = dealType;
    }

    public String getDealTypeName() {
        return dealTypeName;
    }

    public void setDealTypeName(String dealTypeName) {
        this.dealTypeName = dealTypeName;
    }

    public String getRealInWarehouse() {
        return realInWarehouse;
    }

    public void setRealInWarehouse(String realInWarehouse) {
        this.realInWarehouse = realInWarehouse;
    }

    public String getRealInWarehouseName() {
        return realInWarehouseName;
    }

    public void setRealInWarehouseName(String realInWarehouseName) {
        this.realInWarehouseName = realInWarehouseName;
    }

    public Integer getDutyBody() {
        return dutyBody;
    }

    public void setDutyBody(Integer dutyBody) {
        this.dutyBody = dutyBody;
    }

    public String getDutyBodyName() {
        return dutyBodyName;
    }

    public void setDutyBodyName(String dutyBodyName) {
        this.dutyBodyName = dutyBodyName;
    }

    public String getPersonLiable() {
        return personLiable;
    }

    public void setPersonLiable(String personLiable) {
        this.personLiable = personLiable;
    }

    public String getPersonLiableName() {
        return personLiableName;
    }

    public void setPersonLiableName(String personLiableName) {
        this.personLiableName = personLiableName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayStatusName() {
        return payStatusName;
    }

    public void setPayStatusName(String payStatusName) {
        this.payStatusName = payStatusName;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public String getOutWareHouseTime() {
        return outWareHouseTime;
    }

    public void setOutWareHouseTime(String outWareHouseTime) {
        this.outWareHouseTime = outWareHouseTime;
    }

    public Date getOutTime() {
        return outTime;
    }

    public void setOutTime(Date outTime) {
        this.outTime = outTime;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }


    public String getExplainRemark() {
        return explainRemark;
    }

    public void setExplainRemark(String explainRemark) {
        this.explainRemark = explainRemark;
    }

    public String getLicenseLegalPerson() {
        return licenseLegalPerson;
    }

    public void setLicenseLegalPerson(String licenseLegalPerson) {
        this.licenseLegalPerson = licenseLegalPerson;
    }

    public String getLicenseLegalPersonName() {
        return licenseLegalPersonName;
    }

    public void setLicenseLegalPersonName(String licenseLegalPersonName) {
        this.licenseLegalPersonName = licenseLegalPersonName;
    }

    @Override
    public String toString() {
        return "InventoryInPlanLineAssetRespDTO{" +
                "assetCode='" + assetCode + '\'' +
                ", snNo='" + snNo + '\'' +
                ", id=" + id +
                ", assetsCondition=" + assetsCondition +
                ", assetsConditionName='" + assetsConditionName + '\'' +
                ", dealType=" + dealType +
                ", dealTypeName='" + dealTypeName + '\'' +
                ", realInWarehouse='" + realInWarehouse + '\'' +
                ", realInWarehouseName='" + realInWarehouseName + '\'' +
                ", dutyBody=" + dutyBody +
                ", dutyBodyName='" + dutyBodyName + '\'' +
                ", personLiable='" + personLiable + '\'' +
                ", personLiableName='" + personLiableName + '\'' +
                ", amount=" + amount +
                ", remark='" + remark + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", payStatus=" + payStatus +
                ", payStatusName='" + payStatusName + '\'' +
                ", assetsCode='" + assetsCode + '\'' +
                ", assetsName='" + assetsName + '\'' +
                ", outWareHouseTime='" + outWareHouseTime + '\'' +
                ", outTime=" + outTime +
                ", brand='" + brand + '\'' +
                ", model='" + model + '\'' +
                ", explainRemark='" + explainRemark + '\'' +
                ", licenseLegalPerson='" + licenseLegalPerson + '\'' +
                ", licenseLegalPersonName='" + licenseLegalPersonName + '\'' +
                '}';
    }
}
