package com.gz.eim.am.stock.dto.request.authority;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @author: weijunjie
 * @date: 2020/5/17
 * @description TODO
 */
@Data
@ToString
public class StockManageRoleReqDTO extends PageReqDTO {
    private Long roleId;

    private Integer roleType;

    private String warehouseCode;

    private String roleName;

    private String startDate;

    private String endDate;

    private Integer status;

    private Integer delFlag;
}
