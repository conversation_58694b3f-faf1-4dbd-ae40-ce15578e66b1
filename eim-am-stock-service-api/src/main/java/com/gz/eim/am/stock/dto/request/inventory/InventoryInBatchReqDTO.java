package com.gz.eim.am.stock.dto.request.inventory;

import com.gz.eim.am.stock.dto.request.PageReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-25 上午 11:39
 */
public class InventoryInBatchReqDTO {

    /**
     * 入库类型
     */
    private Integer inventoryInType;

    /**
     * 入库时间
     */
    private String inventoryInTime;

    /**
     * 入库人
     */
    private String inventoryInUser;

    /**
     * 入库明细
     */
    private List<InventoryInBatchItemDTO> items;

    public Integer getInventoryInType() {
        return inventoryInType;
    }

    public void setInventoryInType(Integer inventoryInType) {
        this.inventoryInType = inventoryInType;
    }

    public String getInventoryInTime() {
        return inventoryInTime;
    }

    public void setInventoryInTime(String inventoryInTime) {
        this.inventoryInTime = inventoryInTime;
    }

    public String getInventoryInUser() {
        return inventoryInUser;
    }

    public void setInventoryInUser(String inventoryInUser) {
        this.inventoryInUser = inventoryInUser;
    }

    public List<InventoryInBatchItemDTO> getItems() {
        return items;
    }

    public void setItems(List<InventoryInBatchItemDTO> items) {
        this.items = items;
    }

    @Override
    public String toString() {
        return "InventoryBatchReqDTO{" +
                "inventoryInType=" + inventoryInType +
                ", inventoryInTime='" + inventoryInTime + '\'' +
                ", inventoryInUser='" + inventoryInUser + '\'' +
                ", items=" + items +
                '}';
    }
}
