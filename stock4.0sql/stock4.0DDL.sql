use stock;
ALTER TABLE `stock`.`stock_inventory_asset_import`
ADD COLUMN `type` INT(2) NOT NULL DEFAULT 0 COMMENT '导入类型： 0 excel导入， 1手工添加' AFTER `asset_type_name`;

ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `label_url` varchar(255) NOT NULL DEFAULT '' COMMENT '标签url' AFTER `remark`;

ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `label_url` varchar(255) NOT NULL DEFAULT '' COMMENT '标签url' AFTER `remark`;

ALTER TABLE stock_delivery_plan_lines
add column `del_flag` int(2) not null DEFAULT 0 COMMENT '是否删除，0否，1是' AFTER assets_code ;
ALTER TABLE `stock`.`stock_delivery_plan_heads`
ADD COLUMN `business_type` int(2) NOT NULL DEFAULT 0 COMMENT '业务类型' AFTER `out_stock_type`;

CREATE TABLE `stock_assets_document` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `document_no` varchar(64) NOT NULL COMMENT '单据编码',
  `type` int(2) NOT NULL DEFAULT '0' COMMENT '单据类型  1:资产转移',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '状态：0 已保存， 1：待审批，2：已拒绝，3：待处理，4.已处理',
  `reason_code` int(2) NOT NULL DEFAULT '0' COMMENT '制单原因',
  `billing_user` varchar(32) NOT NULL COMMENT '制单人工号',
  `billing_time` datetime NOT NULL COMMENT '制单日期',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `new_assets_keeper` varchar(32) NOT NULL DEFAULT '' COMMENT '新管理员工号',
  `del_flag` int(2) NOT NULL DEFAULT '0' COMMENT '是否删除：0 否， 1是',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_stock_assets_document_1` (`document_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产单据表'

CREATE TABLE `stock_assets_document_detail` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `document_no` varchar(64) NOT NULL COMMENT '资产单据号',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `old_assets_keeper` varchar(45) NOT NULL DEFAULT '' COMMENT '旧资产管理员',
  `new_assets_keeper` varchar(32) NOT NULL DEFAULT '' COMMENT '新资产管理员',
  `status` int(1) NOT NULL DEFAULT '0' COMMENT '状态：0未处理，1. 已处理',
  `del_flag` int(1) NOT NULL DEFAULT '0' COMMENT '删除标识： 0 否， 1是',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  KEY `stock_assets_document_detail_1` (`document_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产单据详情表'


CREATE TABLE `stock_wfl` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `flow_id` varchar(50) DEFAULT '' COMMENT '流程标识',
  `biz_id` varchar(64) NOT NULL DEFAULT '' COMMENT '业务单号',
  `lob_no` varchar(64) NOT NULL DEFAULT '' COMMENT '业务线编号',
  `wfl_param` json NOT NULL COMMENT '发起工作流参数',
  `initiate_result` int(2) NOT NULL DEFAULT '0' COMMENT '发起结果：0成功， 1失败, 2超时或其他',
  `error_message` varchar(255) NOT NULL DEFAULT '' COMMENT '错误原因',
  `approved_result` int(2) NOT NULL DEFAULT '0' COMMENT '审批结果：1拒绝， 2通过',
  `approved_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '完成时间',
  `created_by` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL DEFAULT '' COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_stock_wfl_1` (`biz_id`) USING BTREE,
  KEY `idx_stock_wfl_2` (`lob_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流表'

CREATE TABLE `stock_sys_log` (
  `id` bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sys_action` varchar(200) NOT NULL COMMENT '执行操作',
  `request_id` varchar(128) DEFAULT NULL COMMENT 'requestId',
  `trace_id` varchar(128) DEFAULT NULL COMMENT 'traceId',
  `resources` varchar(100) DEFAULT NULL COMMENT '操作的资源',
  `remoteHost` varchar(20) NOT NULL COMMENT '远程IP',
  `input_params` varchar(5000) DEFAULT NULL COMMENT '请求参数',
  `output_params` varchar(5000) DEFAULT NULL COMMENT '返回参数',
  `user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '操作人',
  `request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '请求时间',
  `completion_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '请求完成时间',
  `consume_time` bigint(20) NOT NULL COMMENT '消耗时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓储接口访问日志表';

ALTER TABLE `stock`.`stock_role_keeper`
ADD COLUMN `dept_id` VARCHAR(45) NOT NULL DEFAULT '' COMMENT '部门id' AFTER `keeper_code`,
ADD COLUMN `del_flag` INT(2) NOT NULL DEFAULT 0 COMMENT '是否逻辑删除' AFTER `status`,
CHANGE COLUMN `contact_way` `contact_way` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '联系方式',
CHANGE COLUMN `status` `status` INT(2) NOT NULL DEFAULT 1 COMMENT '状态 1 可用，0 禁用' AFTER `role_id`,
CHANGE COLUMN `role_id` `role_id` BIGINT(18) NOT NULL DEFAULT 0 COMMENT '角色主键' ,
CHANGE COLUMN `created_by` `created_by` VARCHAR(32) NOT NULL DEFAULT 0 COMMENT '创建人' ,
CHANGE COLUMN `updated_by` `updated_by` VARCHAR(32) NOT NULL DEFAULT 0 COMMENT '最近修改人' ;

ALTER TABLE `stock`.`stock_manage_role`
ADD COLUMN `start_date` TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '有效开始时间' AFTER `warehouse_code`,
ADD COLUMN `end_date` TIMESTAMP NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '有效结束时间' AFTER `start_date`,
CHANGE COLUMN `role_type` `role_type` TINYINT(4) UNSIGNED NOT NULL DEFAULT '0' COMMENT '库管员类型 ( 0 超级管理员, 1 仓库读写管理员，2 仓库读管理员)' ,
CHANGE COLUMN `warehouse_code` `warehouse_code` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '仓库编码',
-- ALTER TABLE `stock`.`stock_manage_role`
-- CHANGE COLUMN `role_type` `role_type` INT(2) NOT NULL DEFAULT '0' COMMENT '库管员类型 ( 0 超级管理员, 1 仓库读写管理员，2 仓库读管理员)' ,
ADD COLUMN `del_flag` INT(2) NOT NULL DEFAULT 0 COMMENT '是否逻辑删除' AFTER `status`,
ADD UNIQUE INDEX `stock_manage_role_1` USING BTREE (`role_name`);

CREATE TABLE `stock`.`stock_role_warehouse` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键',
  `role_id` BIGINT(18) NOT NULL DEFAULT 0,
  `warehouse_type` INT(2) NOT NULL DEFAULT -1 COMMENT '仓库类型',
  `warehouse_code` VARCHAR(64) NOT NULL DEFAULT '',
  `role_status` INT(2) NOT NULL DEFAULT 1 COMMENT '是否有效： 0 否 ，1是',
  `del_flag` VARCHAR(45) NOT NULL DEFAULT 0 COMMENT '是否逻辑删除',
  `created_by` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '创建人',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_by` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '更新人',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_stock_role_warehouse_1` USING BTREE (`role_id`))
COMMENT = '角色仓库关联表';

ALTER TABLE `stock`.`stock_allocate_import_heads`
ADD COLUMN `type` int(2) NOT NULL  DEFAULT 0 COMMENT '导入类型：0：礼品批量调拨， 1:资产批量调拨'  AFTER `head_code`;

ALTER TABLE `stock`.`stock_allocate_import_heads`
ADD COLUMN `type` int(2) NOT NULL  DEFAULT 0 COMMENT '导入类型：0：礼品批量调拨， 1:资产批量调拨'  AFTER `head_code`;

ALTER TABLE `stock`.`stock_allocate_import_lines`
MODIFY COLUMN `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识0，未删除；1，已删除' AFTER `status`,
ADD COLUMN `assets_code` varchar(64) NOT NULL DEFAULT '' COMMENT '资产编码' AFTER `UPDATED_AT`;

ALTER TABLE `stock`.`stock_wfl`
ADD COLUMN `system_module` varchar(255) NOT NULL DEFAULT '' AFTER `id`,
MODIFY COLUMN `flow_id` varchar(50) NOT NULL DEFAULT '' COMMENT '流程标识' AFTER `system_module`;

