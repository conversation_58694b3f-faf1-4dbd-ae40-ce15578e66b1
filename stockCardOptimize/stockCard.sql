-- 变更资产类别
update stock_assets_category set category_name='硬盘' where category_name='服务器硬盘';
update stock_supplies_inventory set default_asset_category='硬盘' where default_asset_category='服务器硬盘';

-- 服务器尾表
CREATE TABLE `stock_assets_extend_server` (
  `assets_server_id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `brand` varchar(64) DEFAULT NULL COMMENT '品牌',
  `model` varchar(255) DEFAULT NULL COMMENT '型号',
  `processor` varchar(64) NOT NULL COMMENT '处理器',
  `ram_memory` varchar(64) NOT NULL COMMENT '内存容量',
  `hard_disk_capacity` varchar(64) NOT NULL COMMENT '硬盘容量',
  `data_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效状态（0无效，1有效）',
  `del_flag` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否删除（0无效，1有效）',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`assets_server_id`) USING BTREE,
  KEY `idx_stock_assets_extend_server_1` (`assets_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产卡片服务器尾表';

-- 硬盘尾表
CREATE TABLE `stock_assets_extend_disk` (
  `assets_disk_id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `brand` varchar(64) DEFAULT NULL COMMENT '品牌',
  `model` varchar(255) DEFAULT NULL COMMENT '型号',
  `hard_disk_capacity` varchar(64) NOT NULL COMMENT '硬盘容量',
  `data_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效状态（0无效，1有效）',
  `del_flag` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否删除（0无效，1有效）',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`assets_disk_id`) USING BTREE,
  KEY `idx_stock_assets_extend_disk_1` (`assets_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产卡片硬盘尾表';

-- 主机尾表
CREATE TABLE `stock_assets_extend_host_machine` (
  `assets_host_id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `brand` varchar(64) DEFAULT NULL COMMENT '品牌',
  `model` varchar(255) DEFAULT NULL COMMENT '型号',
  `processor` varchar(64) NOT NULL COMMENT '处理器',
  `ram_memory` varchar(64) NOT NULL COMMENT '内存容量',
  `hard_disk_capacity` varchar(64) NOT NULL COMMENT '硬盘容量',
  `data_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效状态（0无效，1有效）',
  `del_flag` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否删除（0无效，1有效）',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`assets_host_id`) USING BTREE,
  KEY `idx_stock_assets_extend_host_machine_1` (`assets_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产卡片主机尾表';

-- 台式机尾表
CREATE TABLE `stock_assets_extend_desktop_pc` (
  `assets_desktop_id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `brand` varchar(64) DEFAULT NULL COMMENT '品牌',
  `model` varchar(255) DEFAULT NULL COMMENT '型号',
  `processor` varchar(64) NOT NULL COMMENT '处理器',
  `ram_memory` varchar(64) NOT NULL COMMENT '内存容量',
  `hard_disk_capacity` varchar(64) NOT NULL COMMENT '硬盘容量',
  `data_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效状态（0无效，1有效）',
  `del_flag` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否删除（0无效，1有效）',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`assets_desktop_id`) USING BTREE,
  KEY `idx_stock_assets_extend_desktop_pc_1` (`assets_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产卡片台式机尾表';

-- 尾表配置数据
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('服务器', '品牌', 'brand', 'varchar', 0, 'attr1', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('服务器', '型号', 'model', 'varchar', 0, 'attr2', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('服务器', '处理器', 'processor', 'varchar', 1, 'attr3', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('服务器', '内存容量', 'ramMemory', 'varchar', 1, 'attr4', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('服务器', '硬盘容量', 'hardDiskCapacity', 'varchar', 1, 'attr5', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');

INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('硬盘', '品牌', 'brand', 'varchar', 0, 'attr1', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('硬盘', '型号', 'model', 'varchar', 0, 'attr2', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('硬盘', '硬盘容量', 'hardDiskCapacity', 'varchar', 1, 'attr3', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');

INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('主机', '品牌', 'brand', 'varchar', 0, 'attr1', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('主机', '型号', 'model', 'varchar', 0, 'attr2', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('主机', '处理器', 'processor', 'varchar', 1, 'attr3', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('主机', '内存容量', 'ramMemory', 'varchar', 1, 'attr4', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('主机', '硬盘容量', 'hardDiskCapacity', 'varchar', 1, 'attr5', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');

INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('台式机', '品牌', 'brand', 'varchar', 0, 'attr1', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('台式机', '型号', 'model', 'varchar', 0, 'attr2', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('台式机', '处理器', 'processor', 'varchar', 1, 'attr3', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('台式机', '内存容量', 'ramMemory', 'varchar', 1, 'attr4', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');
INSERT INTO `stock_assets_attr_config`(`category`, `attr_name`, `attr_code`, `attr_type`, `in_required`, `mapping_attr`, `order_number`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES ('台式机', '硬盘容量', 'hardDiskCapacity', 'varchar', 1, 'attr5', 1, 1, '10122730', '2021-01-06 12:00:27', '10122730', '2021-01-06 12:00:27');

-- 资产卡片日志表资产id和资产编码字段分别添加索引
CREATE INDEX index_stock_assets_operation_log_01 ON `stock_assets_operation_log` (`assets_id`);
CREATE INDEX index_stock_assets_operation_log_02 ON `stock_assets_operation_log` (`assets_code`);


-- ambase
update meta_data set md_key='document_warehouse' where md_key='document-warehouse';