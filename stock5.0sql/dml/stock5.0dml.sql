-- 计划入库单数据迁移
-- 采购申请单迁移
insert into stock_inventory_in_plan_lines_assets
select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,c.assets_code,
1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT
from stock_inventory_in_plan_heads d
 INNER JOIN stock_inventory_in_plan_lines a on d.inventory_in_plan_head_id=a.inventory_in_plan_head_id
 inner join stock_inventory_in_supplies b on a.inventory_in_plan_line_id=b.inventory_in_plan_line_id
 inner join stock_inventory_in_supplies_assets c on b.in_supplies_id=c.in_supplies_id
 where d.inventory_in_plan_type='8';

insert into stock_inventory_in_plan_lines_sns
 select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,c.SERIAL_NUMBER,
 1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT
 from stock_inventory_in_plan_heads d
  INNER JOIN stock_inventory_in_plan_lines a on d.inventory_in_plan_head_id=a.inventory_in_plan_head_id
  inner join stock_inventory_in_supplies b on a.inventory_in_plan_line_id=b.inventory_in_plan_line_id
  inner join stock_inventory_in_supplies_sns c on b.in_supplies_id=c.in_supplies_id
  where d.inventory_in_plan_type='8';

-- 调拨计划入库单的迁移
insert into stock_inventory_in_plan_lines_assets
select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.assets_code,
0,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
 INNER JOIN stock_delivery b on a.delivery_no=b.delivery_no
  inner join stock_delivery_detail f on f.delivery_id=b.delivery_id
 inner join stock_delivery_detail_assets c on f.delivery_detail_id=c.delivery_detail_id
 where a.inventory_in_plan_type='6';

insert into stock_inventory_in_plan_lines_assets
select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.assets_code,
0,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
 INNER JOIN stock_delivery_history b on a.delivery_no=b.delivery_no
  inner join stock_delivery_detail_history f on f.delivery_id=b.delivery_id
 inner join stock_delivery_detail_assets c on f.delivery_detail_id=c.delivery_detail_id
 where a.inventory_in_plan_type='6';

insert into stock_inventory_in_plan_lines_sns
select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.SERIAL_NUMBER,
1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
 INNER JOIN stock_delivery b on a.delivery_no=b.delivery_no
  inner join stock_delivery_detail f on f.delivery_id=b.delivery_id
 inner join stock_delivery_detail_sns c on f.delivery_detail_id=c.delivery_detail_id
 where a.inventory_in_plan_type='6';

insert into stock_inventory_in_plan_lines_sns
select null,a.inventory_in_plan_head_id,(select d.inventory_in_plan_line_id from stock_inventory_in_plan_lines d where
a.inventory_in_plan_head_id=d.inventory_in_plan_head_id and d.supplies_code=f.supplies_code),c.SERIAL_NUMBER,
1,1,0,c.CREATED_BY,c.CREATED_AT,c.UPDATED_BY,c.UPDATED_AT from stock_inventory_in_plan_heads a
 INNER JOIN stock_delivery_history b on a.delivery_no=b.delivery_no
  inner join stock_delivery_detail_history f on f.delivery_id=b.delivery_id
 inner join stock_delivery_detail_sns c on f.delivery_detail_id=c.delivery_detail_id
 where a.inventory_in_plan_type='6';

update stock_inventory_in_plan_lines_assets a set a.in_stock_status=1 where
 exists (select 1 from stock_inventory_in_supplies b
         inner join stock_inventory_in_supplies_assets c
				 on b.in_supplies_id=c.in_supplies_id where a.inventory_in_plan_line_id=b.inventory_in_plan_line_id
				 and a.assets_code=c.assets_code)
 and exists (select 1 from stock_inventory_in_plan_heads d where  a.inventory_in_plan_head_id=d.inventory_in_plan_head_id
          and d.inventory_in_plan_type='6');


-- 归还计划入库单的迁移
insert into stock_inventory_in_plan_lines_assets
select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,a.assets_code,
(case a.status when 1 then 0 else 1 end),1,0,a.CREATED_BY,a.CREATED_AT,a.UPDATED_BY,a.UPDATED_AT from stock_inventory_in_plan_heads d
 INNER JOIN stock_inventory_in_plan_lines a on a.inventory_in_plan_head_id=d.inventory_in_plan_head_id
 where d.inventory_in_plan_type='7';

insert into stock_inventory_in_plan_lines_sns
select null,d.inventory_in_plan_head_id,a.inventory_in_plan_line_id,c.sn_code,
(case a.status when 1 then 0 else 1 end),1,0,a.CREATED_BY,a.CREATED_AT,a.UPDATED_BY,a.UPDATED_AT from stock_inventory_in_plan_heads d
 INNER JOIN stock_inventory_in_plan_lines a on a.inventory_in_plan_head_id=d.inventory_in_plan_head_id
 inner join stock_assets c on a.assets_code=c.assets_code
 where d.inventory_in_plan_type='7';


-- 资产模块配置表初始化数据
insert into stock_assets_attr_config values (null,'common','资产编号','assetCode','varchar',1,'assetCode',1,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'common','SN号','snNo','varchar',0,'snNo',2,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','内存','memorySize','varchar',1,'attr1',1,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','存储空间','storageSpacesSize','varchar',0,'attr2',2,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','cpu型号','cpuModel','varchar',0,'attr3',3,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','系统版本','sysVersion','varchar',0,'attr4',4,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','屏幕尺寸','screenSize','varchar',0,'attr5',5,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','品牌','phoneBrand','varchar',0,'attr6',6,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','型号','phoneModel','varchar',0,'attr7',7,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'手机','颜色','phoneColour','varchar',0,'attr8',8,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','处理器','aioProcessor','varchar',0,'attr1',1,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','操作系统','operateSystem','varchar',0,'attr2',2,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','内存容量','ramMemory','varchar',0,'attr3',3,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','硬盘容量版本','hardDiskCapacity','varchar',0,'attr4',4,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','硬盘类型','hddType','varchar',0,'attr5',5,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','显卡','videoCard','varchar',0,'attr6',6,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','屏幕尺寸','screenSize','varchar',0,'attr7',7,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','屏幕分辨率','screenResolution','varchar',0,'attr8',8,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','品牌','aioBrand','varchar',0,'attr9',9,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','型号','aioModel','varchar',0,'attr10',10,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'一体机','颜色','aioColour','varchar',0,'attr11',11,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','处理器','pcProcessor','varchar',0,'attr1',1,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','操作系统','operateSystem','varchar',0,'attr2',2,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','内存容量','ramMemory','varchar',0,'attr3',3,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','硬盘容量版本','hardDiskCapacity','varchar',0,'attr4',4,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','硬盘类型','hddType','varchar',0,'attr5',5,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','显卡','videoCard','varchar',0,'attr6',6,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','屏幕尺寸','screenSize','varchar',0,'attr7',7,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','屏幕分辨率','screenResolution','varchar',0,'attr8',8,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','品牌','pcBrand','varchar',0,'attr9',9,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','型号','pcModel','varchar',0,'attr10',10,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
insert into stock_assets_attr_config values (null,'笔记本电脑','颜色','pcColour','varchar',0,'attr11',11,1,'10122730','2020-07-07 12:00:27','10122730','2020-07-07 12:00:27');
