package com.gz.eim.am.stock.entity;

import lombok.Data;

import java.util.Date;

/**
 * @className: StockCheckInDoubtDataRespDO
 * @description: 库存盘点差异数据返回值
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Data
public class StockCheckInDoubtDataRespDO {
    /**
     * 资产编码
     */
    private String assetsCode;
    /**
     * 账面设备序列号
     */
    private String snapshotSnNo;
    /**
     * 资产名称
     */
    private String assetsName;
    /**
     * 资产账面持有人
     */
    private String snapshotAssetsHolder;
    /**
     * 资产账面持有人姓名
     */
    private String snapshotAssetsHolderName;
    /**
     * 资产账面持有人邮箱
     */
    private String snapshotAssetsHolderEmail;
    /**
     * 领用时间
     */
    private Date holderTime;
    /**
     * 盘点结束时间
     */
    private Date approveTime;
    /**
     * 盘点人工号
     */
    private String checkPeople;
    /**
     * 盘点计划单号
     */
    private String takingPlanNo;
    /**
     * 盘点计划名称
     */
    private String takingPlanName;
    /**
     * 盘点异常类型
     */
    private String errMessage;
    /**
     * 盘点异常描述
     */
    private String errDesc;
    /**
     * 盘点时间
     */
    private Date checkTime;
    /**
     * 资产使用状态
     */
    private Integer snapshotAssetsStatus;

}
