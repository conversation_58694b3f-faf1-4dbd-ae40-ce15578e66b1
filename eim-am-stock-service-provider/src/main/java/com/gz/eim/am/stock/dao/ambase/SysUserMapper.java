package com.gz.eim.am.stock.dao.ambase;

import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SysUserMapper extends AmbaseCommonMapper<SysUser> {
    /**
     * 根据id获取用户集合信息
     * @param empIdList
     * @return
     */
    List<SysUserBasicInfo> queryUserBasicInfoByEmpIdList(List<String> empIdList);

    /**
     * 根据上级id集合获取用户集合信息
     * @param supervisorIdList
     * @return
     */
    List<SysUserBasicInfo> selectUserBasicInfoBySupervisorIdList(List<String> supervisorIdList);

    /**
     * 根据id获取用户基本信息
     * @param empId
     * @return
     */
    SysUserBasicInfo queryUserBasicInfoByEmpId(final String empId);


    /**
     * 根据id获取用户基本信息
     * @param empId
     * @return
     */
    Map<String,String> queryUserInfoMapByEmpId(final String empId);

    /**
     * 根据ids 查询员工邮箱
     * @return
     */
    List<String> selectUsersEmailByIds(@Param("empIds") List<String> empIds);

    /**
     * 查询部门下所有的员工信息
     * @param deptIds
     * @return
     */
    List<SysUserBasicInfo> selectUserEmailsAndEmpIdByDeptIds(@Param("deptIds") List<String> deptIds,@Param("empTypeList")List<Integer> empTypeList);

    /**
     * @param: deptIds
     * @description: 查询部门下的用户id集合
     * @return: List<String>
     * @author: <EMAIL>
     * @date: 2023/12/1
     */
    List<String> selectUserEmpIdListByDeptIds(List<String> deptIds);
}
