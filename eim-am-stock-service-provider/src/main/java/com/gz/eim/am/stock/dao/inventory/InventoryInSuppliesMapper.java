package com.gz.eim.am.stock.dao.inventory;


import com.gz.eim.am.stock.dto.response.inventory.InventoryInSuppliesRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInSupplies;
import com.gz.eim.am.stock.entity.vo.StockInventoryInSuppliesInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/10/30
 * @description:
 */
public interface InventoryInSuppliesMapper {
    /**
     * 插入多条
     * @param inventoryInSupplies
     * @return
     */
    int insertStockInventoryInSuppliesList(@Param("inventoryInSupplies")List<StockInventoryInSupplies> inventoryInSupplies);

    /**
     * 删除入库单详细
     * @param inventoryInId
     * @return
     */
    int deleteStockInventoryInSuppliesByInventoryInId(@Param("inventoryInId") Long inventoryInId);

    /**
     * 更改入库操作入库单明细
     * @param stockInventoryInSupplies
     * @return
     */
    int updateStorageStockInventorySuppliesByPrimaryKey(StockInventoryInSupplies stockInventoryInSupplies);

    /**
     * 根据入库单ID查询入库DNA详情
     * @param inventoryInId
     * @return
     */
    List<InventoryInSuppliesRespDTO> selectInventoryInSuppliesRespDTOByInventoryInId(Long inventoryInId);

    /**
     * 批量插入入库单行
     * @param stockInventoryInSuppliesInfoList
     * @return
     */
    int insertStockInventoryInSuppliesInfoList(List<StockInventoryInSuppliesInfo> stockInventoryInSuppliesInfoList);

    /**
     * 插入入库单明细
     * @param inventoryInSupplies
     * @return
     */
    int insert(StockInventoryInSupplies inventoryInSupplies);
}
