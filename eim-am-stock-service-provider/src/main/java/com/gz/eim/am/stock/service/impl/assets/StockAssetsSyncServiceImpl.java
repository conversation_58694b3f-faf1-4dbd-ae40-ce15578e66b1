package com.gz.eim.am.stock.service.impl.assets;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.stock.constant.AssetSyncEbsConstant;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.assets.StockAssetsSyncToEbsChangeMapper;
import com.gz.eim.am.stock.dao.assets.StockAssetsSyncToEbsMapper;
import com.gz.eim.am.stock.dao.assets.StockAssetsSyncToEbsScrapMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsEbsSyncChangeMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsEbsSyncMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsEbsSyncScrapMapper;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.vo.StockAssetsSyncEbsChangeInfo;
import com.gz.eim.am.stock.entity.vo.StockAssetsSyncEbsInfo;
import com.gz.eim.am.stock.entity.vo.StockAssetsSyncEbsScrapInfo;
import com.gz.eim.am.stock.service.assets.StockAssetsSyncService;
import com.gz.eim.am.stock.util.common.OkHttpClientUtils;
import com.gz.eim.am.stock.util.em.StockAssetsSyncEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


import java.util.*;

/**
 * @author: weijunjie
 * @date: 2020/10/10
 * @description
 */
@Slf4j
@Service
public class StockAssetsSyncServiceImpl implements StockAssetsSyncService {

    @Value("${project.syncebs.syncUrl}")
    private String syncUrl;

    @Value("${project.syncebs.queryUrl}")
    private String queryUrl;

    @Value("${project.syncebs.scrapSyncUrl}")
    private String scrapSyncUrl;

    @Value("${project.syncebs.scrapQueryUrl}")
    private String scrapQueryUrl;

    @Value("${project.syncebs.changeSyncUrl}")
    private String changeSyncUrl;

    @Value("${project.syncebs.changeQueryUrl}")
    private String changeQueryUrl;

    @Autowired
    StockAssetsEbsSyncMapper stockAssetsEbsSyncMapper;

    @Autowired
    StockAssetsSyncToEbsMapper stockAssetsSyncToEbsMapper;

    @Autowired
    StockAssetsEbsSyncScrapMapper stockAssetsEbsSyncScrapMapper;

    @Autowired
    StockAssetsEbsSyncChangeMapper stockAssetsEbsSyncChangeMapper;

    @Autowired
    StockAssetsSyncToEbsScrapMapper stockAssetsSyncToEbsScrapMapper;

    @Autowired
    StockAssetsSyncToEbsChangeMapper stockAssetsSyncToEbsChangeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateSyncStatusByBatchNo(String batchNo, Integer oldStatus, Integer newStatus) {
        if (StringUtils.isEmpty(batchNo) || null == oldStatus || null == newStatus) {
            log.info("/StockAssetsSyncServiceImpl/updateSyncStatusByBatchNo参数不全 batchno{},oldstatus{},newstatus{}", batchNo, oldStatus, newStatus);
            return ResponseData.createFailResult("参数不全");
        }
        StockAssetsEbsSyncExample example = new StockAssetsEbsSyncExample();
        StockAssetsEbsSyncExample.Criteria criteria = example.createCriteria();
        criteria.andBatchNoEqualTo(batchNo);
        criteria.andSyncStatusEqualTo(oldStatus);

        StockAssetsEbsSync stockAssetsEbsSync = new StockAssetsEbsSync();
        stockAssetsEbsSync.setSyncStatus(newStatus);

        stockAssetsEbsSyncMapper.updateByExampleSelective(stockAssetsEbsSync, example);

        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData assetSyncToEbs(String bizId, Integer syncStatus, Integer type) {

        if (StringUtils.isEmpty(bizId) || syncStatus == null || type == null) {
            return ResponseData.createFailResult("请录入参数");
        }
        switch (type) {
            case 1:
                //推送新增资产数据
                syncNewAssets(bizId, syncStatus);
                break;
            case 2:
                //推送报废资产数据
                syncScrapAssets(bizId, syncStatus);
                break;
            case 3:
                //推送变更资产数据
                syncChangeAssets(bizId, syncStatus);
                break;
            default:
                //推送所有模块数据
                syncAllModelAssets(bizId, syncStatus);
        }

        return ResponseData.createSuccessResult();
    }

    @Override
    public void syncEbsResult(String queryCode, Integer type) {
        if (StringUtils.isEmpty(queryCode) || type == null) {
            return;
        }
        switch (type) {
            case 1:
                //查询新增资产数据
                queryNewAssets(queryCode);
                break;
            case 2:
                //查询报废资产数据
                queryScrapAssets(queryCode);
                break;
            case 3:
                //查询变更资产数据
                queryChangeAssets(queryCode);
                break;
            default:
                log.info("不存在的模块类型");
        }
    }

    @Override
    public void syncAllModelAssets(String batchNo, Integer syncStatus) {
        //推送新增资产数据
        syncNewAssets(batchNo, syncStatus);
        //推送变更资产数据
        syncChangeAssets(batchNo, syncStatus);
        //推送报废资产数据
        syncScrapAssets(batchNo, syncStatus);
    }

    @Override
    public void syncNewAssets(String batchNo, Integer syncStatus) {
        Random r = new Random(1);

        while (true) {
            //查询要同步的资产，每次同步200条
            StockAssetsEbsSyncExample example = new StockAssetsEbsSyncExample();
            StockAssetsEbsSyncExample.Criteria criteria = example.createCriteria();
            criteria.andBatchNoEqualTo(batchNo);
            criteria.andSyncStatusEqualTo(syncStatus);
            example.setLimit(CommonConstant.DEFAULT_FILE_DELIVERY_LIMIT_COUNT);
            example.setOrderByClause("billing_time");
            List<StockAssetsEbsSync> stockAssetsEbsSyncList = stockAssetsEbsSyncMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(stockAssetsEbsSyncList)) {
                break;
            }

            final List<Long> stockAssetsEbsSyncIdList = new ArrayList<>(stockAssetsEbsSyncList.size());
            JSONArray jsonArray = new JSONArray();

            for (StockAssetsEbsSync dto : stockAssetsEbsSyncList) {
                StockAssetsSyncEbsInfo stockAssetsSyncEbsInfo = new StockAssetsSyncEbsInfo();
                BeanUtils.copyProperties(dto, stockAssetsSyncEbsInfo);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("data", stockAssetsSyncEbsInfo);
                jsonArray.add(jsonObject);
                stockAssetsEbsSyncIdList.add(dto.getId());
            }

            Map<String, Object> parm = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
            parm.put("p_data", JsonUtil.getJsonString(jsonArray));
            parm.put("p_iface_type", "ADD");
            log.info("新增资产推送入参：{}", JsonUtil.getJsonString(jsonArray));
            String responseJson = OkHttpClientUtils.post(syncUrl, parm);
            log.info("ebs返回报送结果{}", responseJson);
            JSONObject obj = JSONObject.parseObject(responseJson);

            StockAssetsEbsSync stockAssetsEbsSync = new StockAssetsEbsSync();
            criteria.andIdIn(stockAssetsEbsSyncIdList);
            if (obj == null) {
                String queryCode = "system" + r.nextInt(100000);
                stockAssetsEbsSync.setSyncStatus(StockAssetsSyncEnum.SyncStatus.IN_DEFAULT.getValue());
                stockAssetsEbsSync.setQueryCode(queryCode);
                stockAssetsEbsSync.setErrorMessage("接口调用失败");
                stockAssetsEbsSyncMapper.updateByExampleSelective(stockAssetsEbsSync, example);
                continue;
            }
            JSONObject dataObject = obj.getJSONObject(AssetSyncEbsConstant.DATA);
            //获取返回的查询码
            String queryCode = StringUtils.isEmpty(dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO)) ? "system" + r.nextInt(100000) : dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO);
            if (AssetSyncEbsConstant.RETURN_STATUS.equals(dataObject.get(AssetSyncEbsConstant.X_RETURN_STATUS))) {
                stockAssetsEbsSync.setSyncStatus(StockAssetsSyncEnum.SyncStatus.WAIT_IN.getValue());
                if (StringUtils.isEmpty(dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO))) {
                    log.error("资产同步ebs，ebs返回查询码为空。同步批次号{},返回状态码{},返回查询码{},返回错误结果{}", batchNo, dataObject.getString(AssetSyncEbsConstant.X_RETURN_STATUS)
                            , dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO), dataObject.getString(AssetSyncEbsConstant.X_ERROR_MESSAGE));
                }
                stockAssetsEbsSync.setQueryCode(queryCode);
            } else {
                stockAssetsEbsSync.setSyncStatus(StockAssetsSyncEnum.SyncStatus.IN_DEFAULT.getValue());
                stockAssetsEbsSync.setQueryCode(queryCode);
                stockAssetsEbsSync.setErrorMessage(dataObject.getString(AssetSyncEbsConstant.X_ERROR_MESSAGE));
            }
            stockAssetsEbsSyncMapper.updateByExampleSelective(stockAssetsEbsSync, example);
        }

    }

    @Override
    public void syncScrapAssets(String batchNo, Integer syncStatus) {
        Random r = new Random(1);

        while (true) {
            //查询要同步的资产，每次同步200条
            StockAssetsEbsSyncScrapExample example = new StockAssetsEbsSyncScrapExample();
            StockAssetsEbsSyncScrapExample.Criteria criteria = example.createCriteria();
            criteria.andBatchNoEqualTo(batchNo);
            criteria.andSyncStatusEqualTo(syncStatus);
            example.setLimit(CommonConstant.DEFAULT_FILE_DELIVERY_LIMIT_COUNT);
            example.setOrderByClause("billing_time");
            List<StockAssetsEbsSyncScrap> stockAssetsEbsSyncScrapList = stockAssetsEbsSyncScrapMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(stockAssetsEbsSyncScrapList)) {
                break;
            }

            final List<Long> stockAssetsEbsSyncIdList = new ArrayList<>(stockAssetsEbsSyncScrapList.size());
            JSONArray jsonArray = new JSONArray();

            for (StockAssetsEbsSyncScrap dto : stockAssetsEbsSyncScrapList) {
                StockAssetsSyncEbsScrapInfo stockAssetsSyncEbsScrapInfo = new StockAssetsSyncEbsScrapInfo();
                BeanUtils.copyProperties(dto, stockAssetsSyncEbsScrapInfo);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("data", stockAssetsSyncEbsScrapInfo);
                jsonArray.add(jsonObject);
                stockAssetsEbsSyncIdList.add(dto.getId());
            }

            Map<String, Object> parm = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
            parm.put("p_data", JsonUtil.getJsonString(jsonArray));
            parm.put("p_iface_type", "RET");
            log.info("报废资产推送入参：{}", JsonUtil.getJsonString(jsonArray));
            String responseJson = OkHttpClientUtils.post(scrapSyncUrl, parm);
            log.info("ebs返回报送结果{}", responseJson);
            JSONObject obj = JSONObject.parseObject(responseJson);

            StockAssetsEbsSyncScrap stockAssetsEbsSyncScrap = new StockAssetsEbsSyncScrap();
            criteria.andIdIn(stockAssetsEbsSyncIdList);
            if (obj == null) {
                String queryCode = "system" + r.nextInt(100000);
                stockAssetsEbsSyncScrap.setSyncStatus(StockAssetsSyncEnum.SyncStatus.IN_DEFAULT.getValue());
                stockAssetsEbsSyncScrap.setQueryCode(queryCode);
                stockAssetsEbsSyncScrap.setErrorMessage("接口调用失败");
                stockAssetsEbsSyncScrapMapper.updateByExampleSelective(stockAssetsEbsSyncScrap, example);
                continue;
            }

            JSONObject dataObject = obj.getJSONObject(AssetSyncEbsConstant.DATA);
            //获取返回的查询码
            String queryCode = StringUtils.isEmpty(dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO)) ? "system" + r.nextInt(100000) : dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO);
            if (AssetSyncEbsConstant.RETURN_STATUS.equals(dataObject.getString(AssetSyncEbsConstant.X_RETURN_STATUS))) {
                stockAssetsEbsSyncScrap.setSyncStatus(StockAssetsSyncEnum.SyncStatus.WAIT_IN.getValue());
                if (StringUtils.isEmpty(dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO))) {
                    log.error("资产同步ebs，ebs返回查询码为空。同步批次号{},返回状态码{},返回查询码{},返回错误结果{}", batchNo, dataObject.getString(AssetSyncEbsConstant.X_RETURN_STATUS)
                            , dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO), dataObject.getString(AssetSyncEbsConstant.X_ERROR_MESSAGE));
                }
                stockAssetsEbsSyncScrap.setQueryCode(queryCode);
            } else {
                stockAssetsEbsSyncScrap.setSyncStatus(StockAssetsSyncEnum.SyncStatus.IN_DEFAULT.getValue());
                stockAssetsEbsSyncScrap.setQueryCode(queryCode);
                stockAssetsEbsSyncScrap.setErrorMessage(dataObject.getString(AssetSyncEbsConstant.X_ERROR_MESSAGE));
            }
            stockAssetsEbsSyncScrapMapper.updateByExampleSelective(stockAssetsEbsSyncScrap, example);
        }
    }

    @Override
    public void syncChangeAssets(String batchNo, Integer syncStatus) {
        Random r = new Random(1);

        while (true) {
            //查询要同步的资产，每次同步200条
            StockAssetsEbsSyncChangeExample example = new StockAssetsEbsSyncChangeExample();
            StockAssetsEbsSyncChangeExample.Criteria criteria = example.createCriteria();
            criteria.andBatchNoEqualTo(batchNo);
            criteria.andSyncStatusEqualTo(syncStatus);
            example.setLimit(CommonConstant.DEFAULT_FILE_DELIVERY_LIMIT_COUNT);
            example.setOrderByClause("billing_time");
            List<StockAssetsEbsSyncChange> stockAssetsEbsSyncChangeList = stockAssetsEbsSyncChangeMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(stockAssetsEbsSyncChangeList)) {
                break;
            }

            final List<Long> stockAssetsEbsSyncIdList = new ArrayList<>(stockAssetsEbsSyncChangeList.size());
            JSONArray jsonArray = new JSONArray();

            for (StockAssetsEbsSyncChange dto : stockAssetsEbsSyncChangeList) {
                StockAssetsSyncEbsChangeInfo stockAssetsSyncEbsChangeInfo = new StockAssetsSyncEbsChangeInfo();
                BeanUtils.copyProperties(dto, stockAssetsSyncEbsChangeInfo);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("data", stockAssetsSyncEbsChangeInfo);
                jsonArray.add(jsonObject);
                stockAssetsEbsSyncIdList.add(dto.getId());
            }

            Map<String, Object> parm = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
            parm.put("p_data", JsonUtil.getJsonString(jsonArray));
            parm.put("p_iface_type", "ADJ");
            log.info("变更资产推送入参：{}", JsonUtil.getJsonString(jsonArray));
            String responseJson = OkHttpClientUtils.post(changeSyncUrl, parm);
            log.info("ebs返回报送结果{}", responseJson);
            JSONObject obj = JSONObject.parseObject(responseJson);

            StockAssetsEbsSyncChange stockAssetsEbsSyncChange = new StockAssetsEbsSyncChange();
            criteria.andIdIn(stockAssetsEbsSyncIdList);
            if (obj == null) {
                String queryCode = "system" + r.nextInt(100000);
                stockAssetsEbsSyncChange.setSyncStatus(StockAssetsSyncEnum.SyncStatus.IN_DEFAULT.getValue());
                stockAssetsEbsSyncChange.setQueryCode(queryCode);
                stockAssetsEbsSyncChange.setErrorMessage("接口调用失败");
                stockAssetsEbsSyncChangeMapper.updateByExampleSelective(stockAssetsEbsSyncChange, example);
                continue;
            }
            JSONObject dataObject = obj.getJSONObject(AssetSyncEbsConstant.DATA);
            //获取返回的查询码
            String queryCode = StringUtils.isEmpty(dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO)) ? "system" + r.nextInt(100000) : dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO);
            if (AssetSyncEbsConstant.RETURN_STATUS.equals(dataObject.getString(AssetSyncEbsConstant.X_RETURN_STATUS))) {
                stockAssetsEbsSyncChange.setSyncStatus(StockAssetsSyncEnum.SyncStatus.WAIT_IN.getValue());
                if (StringUtils.isEmpty(dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO))) {
                    log.error("资产同步ebs，ebs返回查询码为空。同步批次号{},返回状态码{},返回查询码{},返回错误结果{}", batchNo, dataObject.getString(AssetSyncEbsConstant.X_RETURN_STATUS)
                            , dataObject.getString(AssetSyncEbsConstant.X_BATCH_NO), dataObject.getString(AssetSyncEbsConstant.X_ERROR_MESSAGE));
                }
                stockAssetsEbsSyncChange.setQueryCode(queryCode);
            } else {
                stockAssetsEbsSyncChange.setSyncStatus(StockAssetsSyncEnum.SyncStatus.IN_DEFAULT.getValue());
                stockAssetsEbsSyncChange.setQueryCode(queryCode);
                stockAssetsEbsSyncChange.setErrorMessage(dataObject.getString(AssetSyncEbsConstant.X_ERROR_MESSAGE));
            }
            stockAssetsEbsSyncChangeMapper.updateByExampleSelective(stockAssetsEbsSyncChange, example);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void queryNewAssets(String queryCode) {
        if (!StringUtils.isEmpty(queryCode)) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(AssetSyncEbsConstant.P_BATCH_NO, queryCode);
            paramMap.put(AssetSyncEbsConstant.P_CURRENT, "");
            paramMap.put(AssetSyncEbsConstant.P_PAGE_SIZES, "");
            //调用ebs获取批次处理结果
            String responseResult = OkHttpClientUtils.get(queryUrl, paramMap);
            log.info("ebs返回查询结果{}", responseResult);
            JSONObject jsonObject = JSONObject.parseObject(responseResult);
            if (StringUtils.equalsIgnoreCase(jsonObject.getString(AssetSyncEbsConstant.CODE), ResponseCode.SUCCESS_CODE) && null != jsonObject.getJSONObject(AssetSyncEbsConstant.DATA) && !CollectionUtils.isEmpty(jsonObject.getJSONObject(AssetSyncEbsConstant.DATA).getJSONArray(AssetSyncEbsConstant.RECORDS))) {
                JSONArray array = jsonObject.getJSONObject(AssetSyncEbsConstant.DATA).getJSONArray(AssetSyncEbsConstant.RECORDS);
                List<StockAssetsEbsSync> stockAssetsEbsSyncList = new ArrayList<>(array.size());
                for (int i = 0; i < array.size(); i++) {
                    JSONObject obj = array.getJSONObject(i);
                    //除了未处理的状态，其它状态都进行数据更新
                    if (!AssetSyncEbsConstant.RETURN_STATUS_NEW.equals(obj.get(AssetSyncEbsConstant.ERROR_CODE))) {
                        StockAssetsEbsSync stockAssetsEbsSync = new StockAssetsEbsSync();
                        stockAssetsEbsSync.setQueryCode(queryCode);
                        stockAssetsEbsSync.setBookTypeCode((String) obj.get(AssetSyncEbsConstant.BOOK_TYPE_CODE));
                        stockAssetsEbsSync.setAssetsCode((String) obj.get(AssetSyncEbsConstant.TAG_NUMBER));
                        stockAssetsEbsSync.setBusinessNo((String) obj.get(AssetSyncEbsConstant.BUSINESS_NO));
                        stockAssetsEbsSync.setFaCode((String) obj.get(AssetSyncEbsConstant.ASSET_NUMBER));
                        stockAssetsEbsSync.setAssetId((Integer) obj.get(AssetSyncEbsConstant.ASSET_ID));
                        stockAssetsEbsSync.setUpdatedAt(new Date());
                        if (AssetSyncEbsConstant.RETURN_STATUS.equals(obj.get(AssetSyncEbsConstant.ERROR_CODE))) {
                            stockAssetsEbsSync.setSyncStatus(StockAssetsSyncEnum.SyncStatus.SYNC_SUCCESS.getValue());
                        } else {
                            stockAssetsEbsSync.setSyncStatus(StockAssetsSyncEnum.SyncStatus.SYNC_DEFAULT.getValue());
                            stockAssetsEbsSync.setErrorMessage((String) obj.get(AssetSyncEbsConstant.ERROR_MESSAGE));
                        }
                        stockAssetsEbsSyncList.add(stockAssetsEbsSync);
                    }
                }

                //更新状态、入库结果到记录表
                if (!CollectionUtils.isEmpty(stockAssetsEbsSyncList)) {
                    stockAssetsSyncToEbsMapper.updateMultipleSelective(stockAssetsEbsSyncList);
                }
            }else {
                log.error("获取新增资产ebs返回查询结果错误,queryCode={},response={}",queryCode,responseResult);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void queryScrapAssets(String queryCode) {
        if (!StringUtils.isEmpty(queryCode)) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(AssetSyncEbsConstant.P_BATCH_NO, queryCode);
            paramMap.put(AssetSyncEbsConstant.P_CURRENT, "");
            paramMap.put(AssetSyncEbsConstant.P_PAGE_SIZES, "");
            //调用ebs获取批次处理结果
            String responseResult = OkHttpClientUtils.get(scrapQueryUrl, paramMap);
            log.info("ebs返回查询结果{}", responseResult);
            JSONObject jsonObject = JSONObject.parseObject(responseResult);
            if (StringUtils.equalsIgnoreCase(jsonObject.getString(AssetSyncEbsConstant.CODE), ResponseCode.SUCCESS_CODE) && null != jsonObject.getJSONObject(AssetSyncEbsConstant.DATA) && !CollectionUtils.isEmpty(jsonObject.getJSONObject(AssetSyncEbsConstant.DATA).getJSONArray(AssetSyncEbsConstant.RECORDS))) {
                JSONArray jsonArray = jsonObject.getJSONObject(AssetSyncEbsConstant.DATA).getJSONArray(AssetSyncEbsConstant.RECORDS);
                List<StockAssetsEbsSyncScrap> stockAssetsEbsSyncList = new ArrayList<>(jsonArray.size());
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject obj = jsonArray.getJSONObject(i);
                    //除了未处理的状态，其它状态都进行数据更新
                    if (!AssetSyncEbsConstant.RETURN_STATUS_NEW.equals(obj.get(AssetSyncEbsConstant.ERROR_CODE))) {
                        StockAssetsEbsSyncScrap stockAssetsEbsSyncScrap = new StockAssetsEbsSyncScrap();
                        stockAssetsEbsSyncScrap.setQueryCode(queryCode);
                        stockAssetsEbsSyncScrap.setAssetsCode((String) obj.get(AssetSyncEbsConstant.TAG_NUMBER));
                        stockAssetsEbsSyncScrap.setBusinessNo((String) obj.get(AssetSyncEbsConstant.BUSINESS_NO));
                        stockAssetsEbsSyncScrap.setFaCode((String) obj.get(AssetSyncEbsConstant.ASSET_NUMBER));
                        stockAssetsEbsSyncScrap.setAssetId((Integer) obj.get(AssetSyncEbsConstant.ASSET_ID));
                        stockAssetsEbsSyncScrap.setUpdatedAt(new Date());
                        if (AssetSyncEbsConstant.RETURN_STATUS.equals(obj.get(AssetSyncEbsConstant.ERROR_CODE))) {
                            stockAssetsEbsSyncScrap.setSyncStatus(StockAssetsSyncEnum.SyncStatus.SYNC_SUCCESS.getValue());
                        } else {
                            stockAssetsEbsSyncScrap.setSyncStatus(StockAssetsSyncEnum.SyncStatus.SYNC_DEFAULT.getValue());
                            stockAssetsEbsSyncScrap.setErrorMessage((String) obj.get(AssetSyncEbsConstant.ERROR_MESSAGE));
                        }
                        stockAssetsEbsSyncList.add(stockAssetsEbsSyncScrap);
                    }
                }

                //更新状态、入库结果到记录表
                if (!CollectionUtils.isEmpty(stockAssetsEbsSyncList)) {
                    stockAssetsSyncToEbsScrapMapper.updateMultipleSelective(stockAssetsEbsSyncList);
                }
            }else{

                log.error("获取报废资产ebs返回查询结果错误,queryCode={},response={}",queryCode,responseResult);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void queryChangeAssets(String queryCode) {
        if (!StringUtils.isEmpty(queryCode)) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(AssetSyncEbsConstant.P_BATCH_NO, queryCode);
            paramMap.put(AssetSyncEbsConstant.P_CURRENT, "");
            paramMap.put(AssetSyncEbsConstant.P_PAGE_SIZES, "");
            //调用ebs获取批次处理结果
            String responseResult = OkHttpClientUtils.get(changeQueryUrl, paramMap);
            log.info("ebs返回查询结果{}", responseResult);
            JSONObject jsonObject = JSONObject.parseObject(responseResult);
            if (StringUtils.equalsIgnoreCase(jsonObject.getString(AssetSyncEbsConstant.CODE), ResponseCode.SUCCESS_CODE) && null != jsonObject.getJSONObject(AssetSyncEbsConstant.DATA) && !CollectionUtils.isEmpty(jsonObject.getJSONObject(AssetSyncEbsConstant.DATA).getJSONArray(AssetSyncEbsConstant.RECORDS))) {
                JSONArray jsonArray = jsonObject.getJSONObject(AssetSyncEbsConstant.DATA).getJSONArray(AssetSyncEbsConstant.RECORDS);
                List<StockAssetsEbsSyncChange> stockAssetsEbsSyncList = new ArrayList<>(jsonArray.size());
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject obj = jsonArray.getJSONObject(i);
                    //除了未处理的状态，其它状态都进行数据更新
                    if (!AssetSyncEbsConstant.RETURN_STATUS_NEW.equals(obj.get(AssetSyncEbsConstant.ERROR_CODE))) {
                        StockAssetsEbsSyncChange stockAssetsEbsSyncChange = new StockAssetsEbsSyncChange();
                        stockAssetsEbsSyncChange.setQueryCode(queryCode);
                        stockAssetsEbsSyncChange.setAssetsCode((String) obj.get(AssetSyncEbsConstant.TAG_NUMBER));
                        stockAssetsEbsSyncChange.setBusinessNo((String) obj.get(AssetSyncEbsConstant.BUSINESS_NO));
                        stockAssetsEbsSyncChange.setFaCode((String) obj.get(AssetSyncEbsConstant.ASSET_NUMBER));
                        stockAssetsEbsSyncChange.setAssetId((Integer) obj.get(AssetSyncEbsConstant.ASSET_ID));
                        stockAssetsEbsSyncChange.setUpdatedAt(new Date());
                        if (AssetSyncEbsConstant.RETURN_STATUS.equals(obj.get(AssetSyncEbsConstant.ERROR_CODE))) {
                            stockAssetsEbsSyncChange.setSyncStatus(StockAssetsSyncEnum.SyncStatus.SYNC_SUCCESS.getValue());
                        } else {
                            stockAssetsEbsSyncChange.setSyncStatus(StockAssetsSyncEnum.SyncStatus.SYNC_DEFAULT.getValue());
                            stockAssetsEbsSyncChange.setErrorMessage((String) obj.get(AssetSyncEbsConstant.ERROR_MESSAGE));
                        }
                        stockAssetsEbsSyncList.add(stockAssetsEbsSyncChange);
                    }
                }

                //更新状态、入库结果到记录表
                if (!CollectionUtils.isEmpty(stockAssetsEbsSyncList)) {
                    stockAssetsSyncToEbsChangeMapper.updateMultipleSelective(stockAssetsEbsSyncList);
                }
            }else {
                log.error("获取变更资产ebs返回查询结果错误,queryCode={},response={}",queryCode,responseResult);
            }
        }
    }

}
