package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;

/**
 * @author: lishuyang
 * @date: 2020/3/16
 * @description 导出资产编码
 */

@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportAssetCodeEntity implements ExportModel {
    /**
     * 序号
     */
    @ExportField(name = "序号")
    private Integer serialNumber;
    /**
     * 公司名称
     */
    @ExportField(name = "公司名称")
    private String companyName;
    /**
     * 资产分类名称
     */
    @ExportField(name = "资产分类名称")
    private String categoryName;
    /**
     * 资产编码
     */
    @ExportField(name = "资产编码")
    private String assetCode;


    @Override
    public String getSheetName() {
        return "资产编码";
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }
}
