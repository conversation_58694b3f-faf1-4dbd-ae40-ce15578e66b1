package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetCompany;
import com.gz.eim.am.stock.entity.StockAssetCompanyExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockAssetCompanyMapper {
    long countByExample(StockAssetCompanyExample example);

    int deleteByExample(StockAssetCompanyExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetCompany record);

    int insertSelective(StockAssetCompany record);

    List<StockAssetCompany> selectByExample(StockAssetCompanyExample example);

    StockAssetCompany selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetCompany record, @Param("example") StockAssetCompanyExample example);

    int updateByExample(@Param("record") StockAssetCompany record, @Param("example") StockAssetCompanyExample example);

    int updateByPrimaryKeySelective(StockAssetCompany record);

    int updateByPrimaryKey(StockAssetCompany record);
}