package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockInventoryInPlanHeadExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockInventoryInPlanHeadExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andInventoryInPlanHeadIdIsNull() {
            addCriterion("inventory_in_plan_head_id is null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdIsNotNull() {
            addCriterion("inventory_in_plan_head_id is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdEqualTo(Long value) {
            addCriterion("inventory_in_plan_head_id =", value, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdNotEqualTo(Long value) {
            addCriterion("inventory_in_plan_head_id <>", value, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdGreaterThan(Long value) {
            addCriterion("inventory_in_plan_head_id >", value, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdGreaterThanOrEqualTo(Long value) {
            addCriterion("inventory_in_plan_head_id >=", value, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdLessThan(Long value) {
            addCriterion("inventory_in_plan_head_id <", value, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdLessThanOrEqualTo(Long value) {
            addCriterion("inventory_in_plan_head_id <=", value, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdIn(List<Long> values) {
            addCriterion("inventory_in_plan_head_id in", values, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdNotIn(List<Long> values) {
            addCriterion("inventory_in_plan_head_id not in", values, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdBetween(Long value1, Long value2) {
            addCriterion("inventory_in_plan_head_id between", value1, value2, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanHeadIdNotBetween(Long value1, Long value2) {
            addCriterion("inventory_in_plan_head_id not between", value1, value2, "inventoryInPlanHeadId");
            return (Criteria) this;
        }

        public Criteria andBizNoIsNull() {
            addCriterion("biz_no is null");
            return (Criteria) this;
        }

        public Criteria andBizNoIsNotNull() {
            addCriterion("biz_no is not null");
            return (Criteria) this;
        }

        public Criteria andBizNoEqualTo(String value) {
            addCriterion("biz_no =", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoNotEqualTo(String value) {
            addCriterion("biz_no <>", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoGreaterThan(String value) {
            addCriterion("biz_no >", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoGreaterThanOrEqualTo(String value) {
            addCriterion("biz_no >=", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoLessThan(String value) {
            addCriterion("biz_no <", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoLessThanOrEqualTo(String value) {
            addCriterion("biz_no <=", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoLike(String value) {
            addCriterion("biz_no like", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoNotLike(String value) {
            addCriterion("biz_no not like", value, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoIn(List<String> values) {
            addCriterion("biz_no in", values, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoNotIn(List<String> values) {
            addCriterion("biz_no not in", values, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoBetween(String value1, String value2) {
            addCriterion("biz_no between", value1, value2, "bizNo");
            return (Criteria) this;
        }

        public Criteria andBizNoNotBetween(String value1, String value2) {
            addCriterion("biz_no not between", value1, value2, "bizNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoIsNull() {
            addCriterion("inventory_in_plan_no is null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoIsNotNull() {
            addCriterion("inventory_in_plan_no is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoEqualTo(String value) {
            addCriterion("inventory_in_plan_no =", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotEqualTo(String value) {
            addCriterion("inventory_in_plan_no <>", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoGreaterThan(String value) {
            addCriterion("inventory_in_plan_no >", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_in_plan_no >=", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoLessThan(String value) {
            addCriterion("inventory_in_plan_no <", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoLessThanOrEqualTo(String value) {
            addCriterion("inventory_in_plan_no <=", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoLike(String value) {
            addCriterion("inventory_in_plan_no like", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotLike(String value) {
            addCriterion("inventory_in_plan_no not like", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoIn(List<String> values) {
            addCriterion("inventory_in_plan_no in", values, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotIn(List<String> values) {
            addCriterion("inventory_in_plan_no not in", values, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoBetween(String value1, String value2) {
            addCriterion("inventory_in_plan_no between", value1, value2, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotBetween(String value1, String value2) {
            addCriterion("inventory_in_plan_no not between", value1, value2, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeIsNull() {
            addCriterion("Warehouse_type is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeIsNotNull() {
            addCriterion("Warehouse_type is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeEqualTo(Integer value) {
            addCriterion("Warehouse_type =", value, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeNotEqualTo(Integer value) {
            addCriterion("Warehouse_type <>", value, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeGreaterThan(Integer value) {
            addCriterion("Warehouse_type >", value, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("Warehouse_type >=", value, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeLessThan(Integer value) {
            addCriterion("Warehouse_type <", value, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("Warehouse_type <=", value, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeIn(List<Integer> values) {
            addCriterion("Warehouse_type in", values, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeNotIn(List<Integer> values) {
            addCriterion("Warehouse_type not in", values, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeBetween(Integer value1, Integer value2) {
            addCriterion("Warehouse_type between", value1, value2, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("Warehouse_type not between", value1, value2, "warehouseType");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeIsNull() {
            addCriterion("in_warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeIsNotNull() {
            addCriterion("in_warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeEqualTo(String value) {
            addCriterion("in_warehouse_code =", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeNotEqualTo(String value) {
            addCriterion("in_warehouse_code <>", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeGreaterThan(String value) {
            addCriterion("in_warehouse_code >", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("in_warehouse_code >=", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeLessThan(String value) {
            addCriterion("in_warehouse_code <", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("in_warehouse_code <=", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeLike(String value) {
            addCriterion("in_warehouse_code like", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeNotLike(String value) {
            addCriterion("in_warehouse_code not like", value, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeIn(List<String> values) {
            addCriterion("in_warehouse_code in", values, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeNotIn(List<String> values) {
            addCriterion("in_warehouse_code not in", values, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeBetween(String value1, String value2) {
            addCriterion("in_warehouse_code between", value1, value2, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("in_warehouse_code not between", value1, value2, "inWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeIsNull() {
            addCriterion("out_warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeIsNotNull() {
            addCriterion("out_warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeEqualTo(String value) {
            addCriterion("out_warehouse_code =", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeNotEqualTo(String value) {
            addCriterion("out_warehouse_code <>", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeGreaterThan(String value) {
            addCriterion("out_warehouse_code >", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_warehouse_code >=", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeLessThan(String value) {
            addCriterion("out_warehouse_code <", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("out_warehouse_code <=", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeLike(String value) {
            addCriterion("out_warehouse_code like", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeNotLike(String value) {
            addCriterion("out_warehouse_code not like", value, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeIn(List<String> values) {
            addCriterion("out_warehouse_code in", values, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeNotIn(List<String> values) {
            addCriterion("out_warehouse_code not in", values, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeBetween(String value1, String value2) {
            addCriterion("out_warehouse_code between", value1, value2, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andOutWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("out_warehouse_code not between", value1, value2, "outWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeIsNull() {
            addCriterion("inventory_in_plan_type is null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeIsNotNull() {
            addCriterion("inventory_in_plan_type is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeEqualTo(Integer value) {
            addCriterion("inventory_in_plan_type =", value, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeNotEqualTo(Integer value) {
            addCriterion("inventory_in_plan_type <>", value, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeGreaterThan(Integer value) {
            addCriterion("inventory_in_plan_type >", value, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_in_plan_type >=", value, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeLessThan(Integer value) {
            addCriterion("inventory_in_plan_type <", value, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_in_plan_type <=", value, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeIn(List<Integer> values) {
            addCriterion("inventory_in_plan_type in", values, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeNotIn(List<Integer> values) {
            addCriterion("inventory_in_plan_type not in", values, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeBetween(Integer value1, Integer value2) {
            addCriterion("inventory_in_plan_type between", value1, value2, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_in_plan_type not between", value1, value2, "inventoryInPlanType");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNull() {
            addCriterion("billing_user is null");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNotNull() {
            addCriterion("billing_user is not null");
            return (Criteria) this;
        }

        public Criteria andBillingUserEqualTo(String value) {
            addCriterion("billing_user =", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotEqualTo(String value) {
            addCriterion("billing_user <>", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThan(String value) {
            addCriterion("billing_user >", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThanOrEqualTo(String value) {
            addCriterion("billing_user >=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThan(String value) {
            addCriterion("billing_user <", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThanOrEqualTo(String value) {
            addCriterion("billing_user <=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLike(String value) {
            addCriterion("billing_user like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotLike(String value) {
            addCriterion("billing_user not like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserIn(List<String> values) {
            addCriterion("billing_user in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotIn(List<String> values) {
            addCriterion("billing_user not in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserBetween(String value1, String value2) {
            addCriterion("billing_user between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotBetween(String value1, String value2) {
            addCriterion("billing_user not between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNull() {
            addCriterion("billing_time is null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNotNull() {
            addCriterion("billing_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeEqualTo(Date value) {
            addCriterion("billing_time =", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotEqualTo(Date value) {
            addCriterion("billing_time <>", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThan(Date value) {
            addCriterion("billing_time >", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("billing_time >=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThan(Date value) {
            addCriterion("billing_time <", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThanOrEqualTo(Date value) {
            addCriterion("billing_time <=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIn(List<Date> values) {
            addCriterion("billing_time in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotIn(List<Date> values) {
            addCriterion("billing_time not in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeBetween(Date value1, Date value2) {
            addCriterion("billing_time between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotBetween(Date value1, Date value2) {
            addCriterion("billing_time not between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andReasonCodeIsNull() {
            addCriterion("reason_code is null");
            return (Criteria) this;
        }

        public Criteria andReasonCodeIsNotNull() {
            addCriterion("reason_code is not null");
            return (Criteria) this;
        }

        public Criteria andReasonCodeEqualTo(Integer value) {
            addCriterion("reason_code =", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeNotEqualTo(Integer value) {
            addCriterion("reason_code <>", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeGreaterThan(Integer value) {
            addCriterion("reason_code >", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("reason_code >=", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeLessThan(Integer value) {
            addCriterion("reason_code <", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeLessThanOrEqualTo(Integer value) {
            addCriterion("reason_code <=", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeIn(List<Integer> values) {
            addCriterion("reason_code in", values, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeNotIn(List<Integer> values) {
            addCriterion("reason_code not in", values, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeBetween(Integer value1, Integer value2) {
            addCriterion("reason_code between", value1, value2, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("reason_code not between", value1, value2, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNull() {
            addCriterion("vendor_code is null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIsNotNull() {
            addCriterion("vendor_code is not null");
            return (Criteria) this;
        }

        public Criteria andVendorCodeEqualTo(String value) {
            addCriterion("vendor_code =", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotEqualTo(String value) {
            addCriterion("vendor_code <>", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThan(String value) {
            addCriterion("vendor_code >", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_code >=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThan(String value) {
            addCriterion("vendor_code <", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLessThanOrEqualTo(String value) {
            addCriterion("vendor_code <=", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeLike(String value) {
            addCriterion("vendor_code like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotLike(String value) {
            addCriterion("vendor_code not like", value, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeIn(List<String> values) {
            addCriterion("vendor_code in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotIn(List<String> values) {
            addCriterion("vendor_code not in", values, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeBetween(String value1, String value2) {
            addCriterion("vendor_code between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorCodeNotBetween(String value1, String value2) {
            addCriterion("vendor_code not between", value1, value2, "vendorCode");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNull() {
            addCriterion("vendor_name is null");
            return (Criteria) this;
        }

        public Criteria andVendorNameIsNotNull() {
            addCriterion("vendor_name is not null");
            return (Criteria) this;
        }

        public Criteria andVendorNameEqualTo(String value) {
            addCriterion("vendor_name =", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotEqualTo(String value) {
            addCriterion("vendor_name <>", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThan(String value) {
            addCriterion("vendor_name >", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vendor_name >=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThan(String value) {
            addCriterion("vendor_name <", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLessThanOrEqualTo(String value) {
            addCriterion("vendor_name <=", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameLike(String value) {
            addCriterion("vendor_name like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotLike(String value) {
            addCriterion("vendor_name not like", value, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameIn(List<String> values) {
            addCriterion("vendor_name in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotIn(List<String> values) {
            addCriterion("vendor_name not in", values, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameBetween(String value1, String value2) {
            addCriterion("vendor_name between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andVendorNameNotBetween(String value1, String value2) {
            addCriterion("vendor_name not between", value1, value2, "vendorName");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("Company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("Company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("Company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("Company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("Company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("Company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("Company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("Company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("Company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("Company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("Company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("Company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("Company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("Company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeIsNull() {
            addCriterion("system_code is null");
            return (Criteria) this;
        }

        public Criteria andSystemCodeIsNotNull() {
            addCriterion("system_code is not null");
            return (Criteria) this;
        }

        public Criteria andSystemCodeEqualTo(String value) {
            addCriterion("system_code =", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeNotEqualTo(String value) {
            addCriterion("system_code <>", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeGreaterThan(String value) {
            addCriterion("system_code >", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("system_code >=", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeLessThan(String value) {
            addCriterion("system_code <", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeLessThanOrEqualTo(String value) {
            addCriterion("system_code <=", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeLike(String value) {
            addCriterion("system_code like", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeNotLike(String value) {
            addCriterion("system_code not like", value, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeIn(List<String> values) {
            addCriterion("system_code in", values, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeNotIn(List<String> values) {
            addCriterion("system_code not in", values, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeBetween(String value1, String value2) {
            addCriterion("system_code between", value1, value2, "systemCode");
            return (Criteria) this;
        }

        public Criteria andSystemCodeNotBetween(String value1, String value2) {
            addCriterion("system_code not between", value1, value2, "systemCode");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIsNull() {
            addCriterion("receive_user is null");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIsNotNull() {
            addCriterion("receive_user is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveUserEqualTo(String value) {
            addCriterion("receive_user =", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserNotEqualTo(String value) {
            addCriterion("receive_user <>", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserGreaterThan(String value) {
            addCriterion("receive_user >", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserGreaterThanOrEqualTo(String value) {
            addCriterion("receive_user >=", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserLessThan(String value) {
            addCriterion("receive_user <", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserLessThanOrEqualTo(String value) {
            addCriterion("receive_user <=", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserLike(String value) {
            addCriterion("receive_user like", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserNotLike(String value) {
            addCriterion("receive_user not like", value, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIn(List<String> values) {
            addCriterion("receive_user in", values, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserNotIn(List<String> values) {
            addCriterion("receive_user not in", values, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserBetween(String value1, String value2) {
            addCriterion("receive_user between", value1, value2, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andReceiveUserNotBetween(String value1, String value2) {
            addCriterion("receive_user not between", value1, value2, "receiveUser");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeIsNull() {
            addCriterion("plan_in_time is null");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeIsNotNull() {
            addCriterion("plan_in_time is not null");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeEqualTo(Date value) {
            addCriterion("plan_in_time =", value, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeNotEqualTo(Date value) {
            addCriterion("plan_in_time <>", value, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeGreaterThan(Date value) {
            addCriterion("plan_in_time >", value, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("plan_in_time >=", value, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeLessThan(Date value) {
            addCriterion("plan_in_time <", value, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeLessThanOrEqualTo(Date value) {
            addCriterion("plan_in_time <=", value, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeIn(List<Date> values) {
            addCriterion("plan_in_time in", values, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeNotIn(List<Date> values) {
            addCriterion("plan_in_time not in", values, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeBetween(Date value1, Date value2) {
            addCriterion("plan_in_time between", value1, value2, "planInTime");
            return (Criteria) this;
        }

        public Criteria andPlanInTimeNotBetween(Date value1, Date value2) {
            addCriterion("plan_in_time not between", value1, value2, "planInTime");
            return (Criteria) this;
        }

        public Criteria andAdjustDateIsNull() {
            addCriterion("adjust_date is null");
            return (Criteria) this;
        }

        public Criteria andAdjustDateIsNotNull() {
            addCriterion("adjust_date is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustDateEqualTo(Date value) {
            addCriterion("adjust_date =", value, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateNotEqualTo(Date value) {
            addCriterion("adjust_date <>", value, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateGreaterThan(Date value) {
            addCriterion("adjust_date >", value, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateGreaterThanOrEqualTo(Date value) {
            addCriterion("adjust_date >=", value, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateLessThan(Date value) {
            addCriterion("adjust_date <", value, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateLessThanOrEqualTo(Date value) {
            addCriterion("adjust_date <=", value, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateIn(List<Date> values) {
            addCriterion("adjust_date in", values, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateNotIn(List<Date> values) {
            addCriterion("adjust_date not in", values, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateBetween(Date value1, Date value2) {
            addCriterion("adjust_date between", value1, value2, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andAdjustDateNotBetween(Date value1, Date value2) {
            addCriterion("adjust_date not between", value1, value2, "adjustDate");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoIsNull() {
            addCriterion("delivery_no is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoIsNotNull() {
            addCriterion("delivery_no is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoEqualTo(String value) {
            addCriterion("delivery_no =", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoNotEqualTo(String value) {
            addCriterion("delivery_no <>", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoGreaterThan(String value) {
            addCriterion("delivery_no >", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoGreaterThanOrEqualTo(String value) {
            addCriterion("delivery_no >=", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoLessThan(String value) {
            addCriterion("delivery_no <", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoLessThanOrEqualTo(String value) {
            addCriterion("delivery_no <=", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoLike(String value) {
            addCriterion("delivery_no like", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoNotLike(String value) {
            addCriterion("delivery_no not like", value, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoIn(List<String> values) {
            addCriterion("delivery_no in", values, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoNotIn(List<String> values) {
            addCriterion("delivery_no not in", values, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoBetween(String value1, String value2) {
            addCriterion("delivery_no between", value1, value2, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoNotBetween(String value1, String value2) {
            addCriterion("delivery_no not between", value1, value2, "deliveryNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoIsNull() {
            addCriterion("Purchase_order_no is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoIsNotNull() {
            addCriterion("Purchase_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoEqualTo(String value) {
            addCriterion("Purchase_order_no =", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoNotEqualTo(String value) {
            addCriterion("Purchase_order_no <>", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoGreaterThan(String value) {
            addCriterion("Purchase_order_no >", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("Purchase_order_no >=", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoLessThan(String value) {
            addCriterion("Purchase_order_no <", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoLessThanOrEqualTo(String value) {
            addCriterion("Purchase_order_no <=", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoLike(String value) {
            addCriterion("Purchase_order_no like", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoNotLike(String value) {
            addCriterion("Purchase_order_no not like", value, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoIn(List<String> values) {
            addCriterion("Purchase_order_no in", values, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoNotIn(List<String> values) {
            addCriterion("Purchase_order_no not in", values, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoBetween(String value1, String value2) {
            addCriterion("Purchase_order_no between", value1, value2, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseOrderNoNotBetween(String value1, String value2) {
            addCriterion("Purchase_order_no not between", value1, value2, "purchaseOrderNo");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andDutyUserIsNull() {
            addCriterion("duty_user is null");
            return (Criteria) this;
        }

        public Criteria andDutyUserIsNotNull() {
            addCriterion("duty_user is not null");
            return (Criteria) this;
        }

        public Criteria andDutyUserEqualTo(String value) {
            addCriterion("duty_user =", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserNotEqualTo(String value) {
            addCriterion("duty_user <>", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserGreaterThan(String value) {
            addCriterion("duty_user >", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserGreaterThanOrEqualTo(String value) {
            addCriterion("duty_user >=", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserLessThan(String value) {
            addCriterion("duty_user <", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserLessThanOrEqualTo(String value) {
            addCriterion("duty_user <=", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserLike(String value) {
            addCriterion("duty_user like", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserNotLike(String value) {
            addCriterion("duty_user not like", value, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserIn(List<String> values) {
            addCriterion("duty_user in", values, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserNotIn(List<String> values) {
            addCriterion("duty_user not in", values, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserBetween(String value1, String value2) {
            addCriterion("duty_user between", value1, value2, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyUserNotBetween(String value1, String value2) {
            addCriterion("duty_user not between", value1, value2, "dutyUser");
            return (Criteria) this;
        }

        public Criteria andDutyDeptIsNull() {
            addCriterion("duty_dept is null");
            return (Criteria) this;
        }

        public Criteria andDutyDeptIsNotNull() {
            addCriterion("duty_dept is not null");
            return (Criteria) this;
        }

        public Criteria andDutyDeptEqualTo(String value) {
            addCriterion("duty_dept =", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptNotEqualTo(String value) {
            addCriterion("duty_dept <>", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptGreaterThan(String value) {
            addCriterion("duty_dept >", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptGreaterThanOrEqualTo(String value) {
            addCriterion("duty_dept >=", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptLessThan(String value) {
            addCriterion("duty_dept <", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptLessThanOrEqualTo(String value) {
            addCriterion("duty_dept <=", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptLike(String value) {
            addCriterion("duty_dept like", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptNotLike(String value) {
            addCriterion("duty_dept not like", value, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptIn(List<String> values) {
            addCriterion("duty_dept in", values, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptNotIn(List<String> values) {
            addCriterion("duty_dept not in", values, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptBetween(String value1, String value2) {
            addCriterion("duty_dept between", value1, value2, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyDeptNotBetween(String value1, String value2) {
            addCriterion("duty_dept not between", value1, value2, "dutyDept");
            return (Criteria) this;
        }

        public Criteria andDutyAddressIsNull() {
            addCriterion("duty_address is null");
            return (Criteria) this;
        }

        public Criteria andDutyAddressIsNotNull() {
            addCriterion("duty_address is not null");
            return (Criteria) this;
        }

        public Criteria andDutyAddressEqualTo(String value) {
            addCriterion("duty_address =", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressNotEqualTo(String value) {
            addCriterion("duty_address <>", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressGreaterThan(String value) {
            addCriterion("duty_address >", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressGreaterThanOrEqualTo(String value) {
            addCriterion("duty_address >=", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressLessThan(String value) {
            addCriterion("duty_address <", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressLessThanOrEqualTo(String value) {
            addCriterion("duty_address <=", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressLike(String value) {
            addCriterion("duty_address like", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressNotLike(String value) {
            addCriterion("duty_address not like", value, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressIn(List<String> values) {
            addCriterion("duty_address in", values, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressNotIn(List<String> values) {
            addCriterion("duty_address not in", values, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressBetween(String value1, String value2) {
            addCriterion("duty_address between", value1, value2, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andDutyAddressNotBetween(String value1, String value2) {
            addCriterion("duty_address not between", value1, value2, "dutyAddress");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CREATED_BY like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CREATED_BY not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("CREATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("CREATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("CREATED_AT =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("CREATED_AT <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("CREATED_AT >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("CREATED_AT <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("CREATED_AT in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("CREATED_AT not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("UPDATED_BY =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("UPDATED_BY <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("UPDATED_BY >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("UPDATED_BY <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("UPDATED_BY like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("UPDATED_BY not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("UPDATED_BY in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("UPDATED_BY not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("UPDATED_BY between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("UPDATED_BY not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("UPDATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("UPDATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("UPDATED_AT =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("UPDATED_AT <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("UPDATED_AT >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("UPDATED_AT <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("UPDATED_AT in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("UPDATED_AT not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeIsNull() {
            addCriterion("demand_dept_code is null");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeIsNotNull() {
            addCriterion("demand_dept_code is not null");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeEqualTo(String value) {
            addCriterion("demand_dept_code =", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeNotEqualTo(String value) {
            addCriterion("demand_dept_code <>", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeGreaterThan(String value) {
            addCriterion("demand_dept_code >", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("demand_dept_code >=", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeLessThan(String value) {
            addCriterion("demand_dept_code <", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeLessThanOrEqualTo(String value) {
            addCriterion("demand_dept_code <=", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeLike(String value) {
            addCriterion("demand_dept_code like", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeNotLike(String value) {
            addCriterion("demand_dept_code not like", value, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeIn(List<String> values) {
            addCriterion("demand_dept_code in", values, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeNotIn(List<String> values) {
            addCriterion("demand_dept_code not in", values, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeBetween(String value1, String value2) {
            addCriterion("demand_dept_code between", value1, value2, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptCodeNotBetween(String value1, String value2) {
            addCriterion("demand_dept_code not between", value1, value2, "demandDeptCode");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdIsNull() {
            addCriterion("demand_dept_id is null");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdIsNotNull() {
            addCriterion("demand_dept_id is not null");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdEqualTo(String value) {
            addCriterion("demand_dept_id =", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdNotEqualTo(String value) {
            addCriterion("demand_dept_id <>", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdGreaterThan(String value) {
            addCriterion("demand_dept_id >", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdGreaterThanOrEqualTo(String value) {
            addCriterion("demand_dept_id >=", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdLessThan(String value) {
            addCriterion("demand_dept_id <", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdLessThanOrEqualTo(String value) {
            addCriterion("demand_dept_id <=", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdLike(String value) {
            addCriterion("demand_dept_id like", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdNotLike(String value) {
            addCriterion("demand_dept_id not like", value, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdIn(List<String> values) {
            addCriterion("demand_dept_id in", values, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdNotIn(List<String> values) {
            addCriterion("demand_dept_id not in", values, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdBetween(String value1, String value2) {
            addCriterion("demand_dept_id between", value1, value2, "demandDeptId");
            return (Criteria) this;
        }

        public Criteria andDemandDeptIdNotBetween(String value1, String value2) {
            addCriterion("demand_dept_id not between", value1, value2, "demandDeptId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}