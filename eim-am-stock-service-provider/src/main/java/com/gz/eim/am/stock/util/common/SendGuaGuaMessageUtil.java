package com.gz.eim.am.stock.util.common;


import com.fuu.eim.support.activity.MessageUtil;
import com.fuu.eim.support.base.activity.ActivityRespDTO;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.PropertyConstants;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.util.em.AllocateImportLineEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yangjifan1
 * @Date: 10/15/21
 * @description 发送呱呱消息工具类
 */
@Slf4j
@Component
public class SendGuaGuaMessageUtil {

    @Value("${project.wfl.systemId}")
    private String initSystemId;

    @Value("${project.wfl.checkCode}")
    private String initCheckCode;

    private static String systemId;

    private static String checkCode;

    @PostConstruct
    public void init(){
        this.systemId = initSystemId;
        this.checkCode = initCheckCode;
    }

    private static MessageUtil messageUtil;

    @Autowired
    public SendGuaGuaMessageUtil(MessageUtil messageUtil) {
        SendGuaGuaMessageUtil.messageUtil = messageUtil;
    }

    /**
     * 发送呱呱消息
     *
     * @param taskCode
     * @param users
     */
    public static void sendMessage(String taskCode, String users, String messageContent,List<Map<String, String>> content) {
        Map<String, Object> param = new HashMap<>(4);
        try {

            //消息内容
            Map<String, String> map1 = new HashMap<>(2);
            map1.put("key", "messageContent");
            map1.put("value", messageContent);
            content.add(map1);
            //收件人
            Map<String, String> map2 = new HashMap<>(2);
            map2.put("key", "recipients");
            map2.put("value", users);
            content.add(map2);


            //组织调用消息平台参数
            param.put("SystemId", systemId);
            param.put("CheckCode", checkCode);
            param.put("TaskCode", taskCode);
            param.put("TaskRequestJson", JsonUtil.getJsonString(content));

            log.info("send guagua message params:{}", param);
            ActivityRespDTO respDTO = messageUtil.addTaskRequest(param);
            if (!AllocateImportLineEnum.Status.SUCCESS.getCode().equals(respDTO.getStatus())) {
                log.info("发送呱呱消息失败：{}", respDTO);
            } else {
                log.info("发送呱呱消息成功：{}", respDTO);
            }
        } catch (Exception e) {
            log.error("发送呱呱消息异常：{}", param, e);
        }
    }

    /**
     * 发送呱呱消息
     *
     * @param taskCode
     * @param content
     */
    public static void sendMessage(String taskCode, List<Map<String, String>> content) {
        Map<String, Object> param = new HashMap<>(4);
        try {

            //组织调用消息平台参数
            param.put("SystemId", systemId);
            param.put("CheckCode", checkCode);
            param.put("TaskCode", taskCode);
            param.put("TaskRequestJson", JsonUtil.getJsonString(content));

            log.info("send guagua message params:{}", param);
            ActivityRespDTO respDTO = messageUtil.addTaskRequest(param);
            if (!AllocateImportLineEnum.Status.SUCCESS.getCode().equals(respDTO.getStatus())) {
                log.info("发送呱呱消息失败：{}", respDTO);
            } else {
                log.info("发送呱呱消息成功：{}", respDTO);
            }
        } catch (Exception e) {
            log.error("发送呱呱消息异常：{}", param, e);
        }
    }

        /**
         * 发送呱呱消息
         * @param taskCode
         * @param emailList
         * @param messageContent
         */
        public static void sendGuaGuaMessageList(String taskCode, List<String> emailList, String messageContent) {
        log.info("SendGuaGuaMessageUtil.sendGuaGuaEmailList,开始推送呱呱消息----------------,总数为：" + emailList.size());
        List<List<String>>emailListList = ListUtil.splitList(emailList, CommonConstant.DEFAULT_FILE_DELIVERY_LIMIT_COUNT);
        for (List<String> splitEmailList : emailListList) {
            String emailListString = splitEmailList.stream().collect(Collectors.joining(";"));
            SendGuaGuaMessageUtil.sendMessage(taskCode, emailListString, messageContent, new ArrayList<>());
        }
        log.info("SendGuaGuaMessageUtil.sendGuaGuaMessage,开始推送呱呱结束----------------");
    }
}
