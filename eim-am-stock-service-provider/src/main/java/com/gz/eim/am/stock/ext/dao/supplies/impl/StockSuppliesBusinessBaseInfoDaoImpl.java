package com.gz.eim.am.stock.ext.dao.supplies.impl;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.entity.StockSupplies;
import com.gz.eim.am.stock.entity.StockSuppliesBusinessBaseInfo;
import com.gz.eim.am.stock.entity.StockSuppliesBusinessBaseInfoExample;
import com.gz.eim.am.stock.ext.dao.supplies.StockSuppliesBusinessBaseInfoDao;
import com.gz.eim.am.stock.mapper.base.StockSuppliesBusinessBaseInfoMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: lishuyang
 * @date: 2020/7/13
 * <p>
 *    物料业务属性基本信息dao 实现类
 * </p>
 */
@Repository
public class StockSuppliesBusinessBaseInfoDaoImpl implements StockSuppliesBusinessBaseInfoDao {
    @Resource
    private StockSuppliesBusinessBaseInfoMapper stockSuppliesBusinessBaseInfoMapper;

    @Override
    public StockSuppliesBusinessBaseInfo getBySuppliesCodeAndWarehouseType(String suppliesCode , Integer warehouseType) {
        if(StringUtils.isBlank (suppliesCode) || Objects.isNull (warehouseType)){
            return null;
        }
        StockSuppliesBusinessBaseInfoExample example = new StockSuppliesBusinessBaseInfoExample ();
        StockSuppliesBusinessBaseInfoExample.Criteria criteria = example.createCriteria ();

        criteria.andSuppliesCodeEqualTo (suppliesCode);
        criteria.andWarehouseTypeCodeEqualTo (warehouseType);
        criteria.andIsDelFlagEqualTo (CommonConstant.NUMBER_ZERO);
        List<StockSuppliesBusinessBaseInfo> stockSuppliesBusinessBaseInfoList = stockSuppliesBusinessBaseInfoMapper.selectByExample (example);
        if(!CollectionUtils.isEmpty (stockSuppliesBusinessBaseInfoList)){
            return stockSuppliesBusinessBaseInfoList.get(CommonConstant.NUMBER_ZERO);
        }

        return null;
    }

    @Override
    public List<StockSuppliesBusinessBaseInfo> listByWarehouseType(Integer warehouseType) {
        if(Objects.isNull (warehouseType)){
            return Collections.emptyList ();
        }
        StockSuppliesBusinessBaseInfoExample example = new StockSuppliesBusinessBaseInfoExample ();
        StockSuppliesBusinessBaseInfoExample.Criteria criteria = example.createCriteria ();

        criteria.andWarehouseTypeCodeEqualTo (warehouseType);
        criteria.andIsDelFlagEqualTo (CommonConstant.NUMBER_ZERO);

        return stockSuppliesBusinessBaseInfoMapper.selectByExample (example);
    }
}
