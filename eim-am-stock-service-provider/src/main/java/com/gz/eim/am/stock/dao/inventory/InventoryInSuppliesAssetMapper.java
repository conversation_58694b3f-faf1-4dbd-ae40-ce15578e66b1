package com.gz.eim.am.stock.dao.inventory;

import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInSuppliesAsset;

import java.util.List;

/**
 * @author: lish<PERSON><PERSON>
 * @date: 2019/12/21
 * @description:
 */
public interface InventoryInSuppliesAssetMapper {
    /**
     * 根据计划入库单行
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    List<InventoryInPlanLineAssetRespDTO> selectByPage(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 批量插入
     * @param suppliesAssetList
     * @return
     */
    Integer insertMultiple(final List<StockInventoryInSuppliesAsset> suppliesAssetList);

    /**
     * 根据计划入库单行查询管理资产行数
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    Long selectCountByParam(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);
}

