package com.gz.eim.am.stock.util.em;

import com.gz.eim.am.stock.util.common.CodeEnumUtil;

/**
 * <AUTHOR>
 * @date 2019-09-24 下午 2:51
 */
public class InventoryInPlanLineEnum {

    /**
     * 入库单详细状态
     */
    public enum Status{
        /**
         * 冻结
         */
        FREEZE(0),
        /**
         * 待入库
         */
        WAIT_IN(1),
        /**
         * 部分入库
         */
        SECTION_IN(2),
        /**
         * 已入库
         */
        ALREADY_IN(3),
        /**
         * 驳回
         */
        REJECT(4),
        /**
         * 取消
         */
        CANCEL(5),
        ;
        Status(Integer status){
            this.status = status;
        }
        private Integer status;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    /**
     * 物料质量
     */
    public enum Quality{
        /**
         * 良品
         */
        GOOD(1),
        /**
         * 残次品
         */
        DEFECTIVE(2),
        /**
         *
         * 待检品
         */
        PENDING_INSPECTION(3),
        ;
        Quality(Integer type){
            this.type = type;
        }
        private Integer type;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }

    /**
     * 打印类型
     */
    public enum PrintLabelType implements ICodeEnum {
        /**
         * 不打印
         */
        NOT_PRINT(1, "不打印"),
        /**
         * 创建入库单打印
         */
        CREATE_IN(2, "创建入库单打印"),
        /**
         *
         * 入库时打印
         */
        IN_STORE(3, "入库时打印"),
        ;

        PrintLabelType(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;

        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        public static PrintLabelType fromCode(Integer code) {
            return CodeEnumUtil.fromCode(PrintLabelType.class, code);
        }
    }


    /**
     * 资产归还单详细状态
     */
    public enum AssetRemandStatus implements ICodeEnum{
        /**
         * 冻结
         */
        FREEZE(0, "冻结"),
        /**
         * 待归还
         */
        WAIT_IN(1, "待归还"),

        /**
         * 已归还
         */
        ALREADY_IN(3, "已归还")
        ;
        AssetRemandStatus(Integer code, String value) {
            this.code = code;
            this.value = value;
        }
        private Integer code;
        private String value;

        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        public static AssetRemandStatus fromCode(Integer code) {
            return CodeEnumUtil.fromCode(AssetRemandStatus.class, code);
        }
    }

}
