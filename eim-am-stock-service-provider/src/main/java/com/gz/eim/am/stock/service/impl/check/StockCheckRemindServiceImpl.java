package com.gz.eim.am.stock.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.support.util.DateUtils;
import com.guazi.gzencrypt.common.constants.CryptType;
import com.gz.eim.am.common.enums.EncryptModuleEnum;
import com.gz.eim.am.common.util.EncryptsPlus;
import com.gz.eim.am.stock.constant.*;
import com.gz.eim.am.stock.dao.base.StockAssetsCheckTaskDetailMapper;
import com.gz.eim.am.stock.dao.base.StockTakingPlanMapper;
import com.gz.eim.am.stock.dao.check.StockCheckRemindMapper;
import com.gz.eim.am.stock.dto.request.check.StockCheckQueryRemindLeaderCheckDetailReqDTO;
import com.gz.eim.am.stock.dto.request.check.StockCheckSendCheckRemindPopFrameReqDTO;
import com.gz.eim.am.stock.dto.response.check.StockCheckQueryRemindLeaderCheckDetailHeadRespDTO;
import com.gz.eim.am.stock.dto.response.check.StockCheckQueryRemindLeaderCheckDetailLineRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.StockBeanMap;
import com.gz.eim.am.stock.entity.vo.StockCheckInDoubtDataExportExcel;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.check.StockCheckRemindService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.common.FileUtil;
import com.gz.eim.am.stock.util.common.SendGuaGuaMessageUtil;
import com.gz.eim.am.stock.util.em.StockCheckMissionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: StockCheckRemindServiceImpl
 * @description: 资产盘点提醒ServiceImpl
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Slf4j
@Service
public class StockCheckRemindServiceImpl implements StockCheckRemindService {
    @Autowired
    private StockCheckRemindMapper stockCheckRemindMapper;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockCheckRemindServiceHelper stockCheckRemindServiceHelper;
    @Autowired
    private StockAssetsCheckTaskDetailMapper stockAssetsCheckTaskDetailMapper;
    @Autowired
    private StockTakingPlanMapper stockTakingPlanMapper;
    @Autowired
    private FileUtil fileUtil;
    @Override
    public ResponseData sendCheckInDoubtDataExcelToAdministration(String checkDate) throws Exception{
        if(StringUtils.isBlank(checkDate)){
            // 获取昨天一天的数据
            checkDate = DateUtils.dateFormat(DateUtils.dateAddDays(new Date(), -CommonConstant.NUMBER_ONE), DateUtils.DATE_PATTERN);
        }
        Date checkStartDate = DateUtils.dateParse(checkDate, DateUtils.DATE_PATTERN);
        Date checkEndDate = DateUtils.dateParse(DateTimeUtil.addTimeEndEnd(checkDate), DateUtils.DATE_TIME_PATTERN);
        // 查询未结束的盘点计划所有的盘点人信息
        List<StockCheckInDoubtDataRespDO> stockCheckInDoubtDataRespDOList = stockCheckRemindMapper.selectCheckInDoubtDataList(checkStartDate, checkEndDate);
        if(CollectionUtils.isEmpty(stockCheckInDoubtDataRespDOList)){
            return ResponseData.createSuccessResult();
        }
        log.info("StockCheckRemindServiceImpl.stockCheckInDoubtDataRespDOList开始处理盘点存疑数据，数据总条数为：{} 条", stockCheckInDoubtDataRespDOList.size());
        Map<String, List<StockCheckInDoubtDataRespDO>> stockCheckInDoubtDataRespDOListMap = new HashMap<>();
        Set<String> checkPersonSet = new HashSet<>();
        for (StockCheckInDoubtDataRespDO stockCheckInDoubtDataRespDO : stockCheckInDoubtDataRespDOList) {
            String checkPeople = stockCheckInDoubtDataRespDO.getCheckPeople();
            if(StringUtils.isEmpty(checkPeople)){
                continue;
            }
            List<StockCheckInDoubtDataRespDO> tempStockCheckInDoubtDataRespDOList = stockCheckInDoubtDataRespDOListMap.computeIfAbsent(stockCheckInDoubtDataRespDO.getTakingPlanNo(), k -> new ArrayList<>());
            String[] checkPeopleArray = checkPeople.split(StringConstant.SEMI_COLON_HALF);
            if(checkPeopleArray.length <= CommonConstant.NUMBER_ONE){
                checkPersonSet.add(checkPeople);
                tempStockCheckInDoubtDataRespDOList.add(stockCheckInDoubtDataRespDO);
            }else {
                for (String checkPeopleTemp : checkPeopleArray) {
                    checkPersonSet.add(checkPeopleTemp);
                    StockCheckInDoubtDataRespDO tempStockCheckInDoubtDataRespDO = new StockCheckInDoubtDataRespDO();
                    BeanUtils.copyProperties(stockCheckInDoubtDataRespDO, tempStockCheckInDoubtDataRespDO);
                    tempStockCheckInDoubtDataRespDO.setCheckPeople(checkPeopleTemp);
                    tempStockCheckInDoubtDataRespDOList.add(tempStockCheckInDoubtDataRespDO);
                }
            }
        }
        log.info("StockCheckRemindServiceImpl.stockCheckInDoubtDataRespDOList，开始处理盘点存疑数据，计划单编号为：{}", JSON.toJSONString(stockCheckInDoubtDataRespDOListMap.keySet()));
        // 查询所有盘点人的基本信息
        List<SysUserBasicInfo> checkPersonList = ambaseCommonService.selectUserBasicListByEmpIdList(new ArrayList<>(checkPersonSet));
        if(CollectionUtils.isEmpty(checkPersonList)){
            return ResponseData.createSuccessResult();
        }
        Set<String> superUserSet = new HashSet<>();
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = new HashMap<>(checkPersonList.size());
        for (SysUserBasicInfo sysUserBasicInfo : checkPersonList) {
            sysUserBasicInfoMap.put(sysUserBasicInfo.getEmpId(), sysUserBasicInfo);
            stockCheckRemindServiceHelper.setDecryptSysUserBasicInfoPhone(sysUserBasicInfo);
            superUserSet.add(sysUserBasicInfo.getSupervisorId());
        }
        // 排除相同的人，避免重复查询
        superUserSet.removeAll(checkPersonSet);
        List<SysUserBasicInfo> superUserList = ambaseCommonService.selectUserBasicListByEmpIdList(new ArrayList<>(superUserSet));
        for (SysUserBasicInfo sysUserBasicInfo : superUserList) {
            stockCheckRemindServiceHelper.setDecryptSysUserBasicInfoPhone(sysUserBasicInfo);
            sysUserBasicInfoMap.put(sysUserBasicInfo.getEmpId(), sysUserBasicInfo);
        }
        // 获取excel相关数据
        Map<String, List<StockCheckInDoubtDataExportExcel>> stockCheckInDoubtDataExportExcelListMap = stockCheckRemindServiceHelper.getStockCheckInDoubtDataExportExcelList(stockCheckInDoubtDataRespDOListMap, sysUserBasicInfoMap);
        if(MapUtils.isEmpty(stockCheckInDoubtDataExportExcelListMap)){
            return ResponseData.createSuccessResult();
        }
        // 生成excel发送给行政同学
        stockCheckRemindServiceHelper.sendCheckInDoubtDataExcelToAdministration(stockCheckInDoubtDataExportExcelListMap);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData sendCheckRemindMessageToEmployee(Integer days) throws Exception {
        // 查询未结束盘点的员工信息
//        if(null == days || days <= CommonConstant.NUMBER_ZERO){
//            days = StockAssetsCheckConstant.ASSETS_CHECK_SEND_CHECK_REMIND_END_BEFORE_DAYS;
//        }
//        Date currentDate = DateUtils.dateParse(DateUtils.dateFormat(new Date(), DateUtils.DATE_PATTERN), DateUtils.DATE_PATTERN);
//        Date endCheckDate = DateUtils.dateAddSeconds(DateUtils.dateAddDays(currentDate, days), -CommonConstant.NUMBER_ONE);
        StockCheckNotCheckPeopleReqDO stockCheckNotCheckPeopleReqDO = new StockCheckNotCheckPeopleReqDO();
//        stockCheckNotCheckPeopleReqDO.setCheckStartDate(currentDate);
//        stockCheckNotCheckPeopleReqDO.setCheckEndDate(endCheckDate);
        stockCheckNotCheckPeopleReqDO.setCheckStartDate(new Date());
        List<StockCheckNotCheckPeopleRespDO> stockCheckNotCheckPeopleRespDOList = stockCheckRemindMapper.selectNotCheckPeopleList(stockCheckNotCheckPeopleReqDO);
        if(CollectionUtils.isEmpty(stockCheckNotCheckPeopleRespDOList)){
            return ResponseData.createSuccessResult();
        }
        for (StockCheckNotCheckPeopleRespDO stockCheckNotCheckPeopleRespDO : stockCheckNotCheckPeopleRespDOList) {
            // 手机号解密
            String checkPeoplePhone = stockCheckNotCheckPeopleRespDO.getCheckPeoplePhone();
            if (StringUtils.isBlank(checkPeoplePhone)) {
                continue;
            }
            try {
                checkPeoplePhone = EncryptsPlus.decrypt(EncryptModuleEnum.DATA.value, CryptType.PHONE, checkPeoplePhone);
                if (StringUtils.isBlank(checkPeoplePhone)) {
                    continue;
                }
                stockCheckNotCheckPeopleRespDO.setCheckPeople(checkPeoplePhone);
            } catch (Exception e) {
                log.error("StockCheckRemindServiceImpl.sendCheckInDoubtDataExcelToAdministration调用解密出现异常，请求参数为：module:{},type:{},crypt:{}", EncryptModuleEnum.PDM.value, CryptType.PHONE.getCode(), checkPeoplePhone);
            }
        }
        // 给未盘点的员工发送盘点提醒
        stockCheckRemindServiceHelper.doSendCheckRemindMessageToEmployee(stockCheckNotCheckPeopleRespDOList);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData sendCheckRemindToLeader(String supvLvlId) throws Exception {
        if(StringUtils.isBlank(supvLvlId)){
            supvLvlId = StockAssetsCheckConstant.ASSETS_CHECK_SEND_CHECK_REMIND_LEADER_SUPV_LVL_ID;
        }
        Map<String, StockCheckNotCheckPeopleSendToLeaderDataRespDO> stockCheckNotCheckPeopleSendToLeaderDataRespDOMap = new HashMap<>();
        StockCheckNotCheckPeopleReqDO stockCheckNotCheckPeopleReqDO = new StockCheckNotCheckPeopleReqDO();
        stockCheckNotCheckPeopleReqDO.setCheckStartDate(new Date());
        List<StockCheckNotCheckPeopleRespDO> stockCheckNotCheckPeopleRespDOList = stockCheckRemindMapper.selectNotCheckPeopleList(stockCheckNotCheckPeopleReqDO);
        if(CollectionUtils.isEmpty(stockCheckNotCheckPeopleRespDOList)){
            return ResponseData.createSuccessResult();
        }
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = new HashMap<>();
        int notCheckPeopleTotal = stockCheckNotCheckPeopleRespDOList.size();
        // 获取上级领导相关的信息
        stockCheckRemindServiceHelper.getStockCheckNotCheckPeopleSendToLeaderDataRespDOList(stockCheckNotCheckPeopleSendToLeaderDataRespDOMap, stockCheckNotCheckPeopleRespDOList,
                sysUserBasicInfoMap, supvLvlId);
        // 发送呱呱消息和邮件提醒
        stockCheckRemindServiceHelper.sendCheckRemindToLeader(stockCheckNotCheckPeopleSendToLeaderDataRespDOMap, supvLvlId, notCheckPeopleTotal);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData<StockCheckQueryRemindLeaderCheckDetailHeadRespDTO> queryRemindLeaderCheckDetail(StockCheckQueryRemindLeaderCheckDetailReqDTO stockCheckQueryRemindLeaderCheckDetailReqDTO) throws Exception {
        // 参数校验
        String takingPlanNo = stockCheckQueryRemindLeaderCheckDetailReqDTO.getTakingPlanNo();
        if(StringUtils.isBlank(takingPlanNo)){
            return ResponseData.createFailResult("盘点计划单号不能为空");
        }
        String supervisorId = stockCheckQueryRemindLeaderCheckDetailReqDTO.getSupervisorId();
        if(StringUtils.isBlank(supervisorId)){
            return ResponseData.createFailResult("上级领导工号不能为空");
        }
        String supvLvlId = stockCheckQueryRemindLeaderCheckDetailReqDTO.getSupvLvlId();
        if(StringUtils.isBlank(supvLvlId)){
            return ResponseData.createFailResult("不能为空");
        }
        // 查询盘点计划单
        StockTakingPlanExample stockTakingPlanExample = new StockTakingPlanExample();
        StockTakingPlanExample.Criteria stockTakingPlanCriteria = stockTakingPlanExample.createCriteria();
        stockTakingPlanCriteria.andTakingPlanNoEqualTo(takingPlanNo);
        List<StockTakingPlan> stockTakingPlanList = stockTakingPlanMapper.selectByExample(stockTakingPlanExample);
        if(CollectionUtils.isEmpty(stockTakingPlanList)){
            return ResponseData.createFailResult("盘点计划单不存在");
        }
        // 递归查找领导的下级，直到找到小于等于规定标准职级的员工退出递归
        Set<String> checkPeopleSet = new HashSet<>();
        Set<String> supervisorIdSet = new HashSet<>();
        supervisorIdSet.add(supervisorId);
        checkPeopleSet = stockCheckRemindServiceHelper.getCheckPeopleList(checkPeopleSet, supervisorIdSet, supvLvlId);
        // 根据员工工号集合和盘点单号，查询存在未盘点资产的员工
//        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
//        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
//        criteria.andTakingPlanNoEqualTo(takingPlanNo);
//        criteria.andCheckFlagNotEqualTo(StockCheckMissionEnum.CheckFlag.YES.getValue());
//        criteria.andCheckPeopleIn(checkPeopleList);
//        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
//        if(CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
//            return ResponseData.createSuccessResult();
//        }
//        Set<String> notCheckPeopleSet = stockAssetsCheckTaskDetailList.stream().map(StockAssetsCheckTaskDetail :: getCheckPeople).collect(Collectors.toSet());
        StockCheckSelectCheckPeopleDataReqDO stockCheckSelectCheckPeopleDataReqDO = new StockCheckSelectCheckPeopleDataReqDO();
        stockCheckSelectCheckPeopleDataReqDO.setTakingPlanNo(takingPlanNo);
        stockCheckSelectCheckPeopleDataReqDO.setCheckPeopleList(new ArrayList<>(checkPeopleSet));
        stockCheckSelectCheckPeopleDataReqDO.setCheckFlag(StockCheckMissionEnum.CheckFlag.NO.getValue());
        List<StockCheckSelectCheckPeopleDataRespDO> stockCheckSelectCheckPeopleDataRespDOList = stockCheckRemindMapper.selectCheckPeopleDataList(stockCheckSelectCheckPeopleDataReqDO);
        if(CollectionUtils.isEmpty(stockCheckSelectCheckPeopleDataRespDOList)){
            return ResponseData.createSuccessResult();
        }
        // 查询盘点人信息
        Set<String> notCheckPeopleSet = stockCheckSelectCheckPeopleDataRespDOList.stream().map(StockCheckSelectCheckPeopleDataRespDO :: getCheckPeople).collect(Collectors.toSet());
        stockCheckSelectCheckPeopleDataReqDO.setCheckFlag(null);
        stockCheckSelectCheckPeopleDataReqDO.setCheckPeopleList(new ArrayList<>(notCheckPeopleSet));
        stockCheckSelectCheckPeopleDataRespDOList = stockCheckRemindMapper.selectCheckPeopleDataList(stockCheckSelectCheckPeopleDataReqDO);
        notCheckPeopleSet.add(supervisorId);
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(notCheckPeopleSet));
        // 按照盘点人进行汇总
        List<StockCheckQueryRemindLeaderCheckDetailLineRespDTO> stockCheckQueryRemindLeaderCheckDetailHeadRespDTOList = stockCheckRemindServiceHelper.getStockCheckQueryRemindLeaderCheckDetailHeadRespDTOList(stockCheckSelectCheckPeopleDataRespDOList, sysUserBasicInfoMap, stockTakingPlanList.get(CommonConstant.NUMBER_ZERO));
        StockCheckQueryRemindLeaderCheckDetailHeadRespDTO stockCheckQueryRemindLeaderCheckDetailHeadRespDTO = new StockCheckQueryRemindLeaderCheckDetailHeadRespDTO();
        stockCheckQueryRemindLeaderCheckDetailHeadRespDTO.setRemindLeaderCheckDetailLineRespDTOList(stockCheckQueryRemindLeaderCheckDetailHeadRespDTOList);
        return ResponseData.createSuccessResult(stockCheckQueryRemindLeaderCheckDetailHeadRespDTO);
    }

    @Override
    public ResponseData sendCheckRemindPopFrameToNotCheckPeople(StockCheckSendCheckRemindPopFrameReqDTO stockCheckSendCheckRemindPopFrameReqDTO) throws Exception{
        // 查询未盘点的人userId集合
        List<String> userIdList = stockCheckRemindMapper.selectNotCheckPeopleUserIdList();
        log.info("查询到未盘点人共{}人，需要在呱呱发送弹框提醒盘点", JSON.toJSONString(userIdList));
        if(CollectionUtils.isEmpty(userIdList)){
            return ResponseData.createSuccessResult();
        }
        // 处理请求参数
        stockCheckSendCheckRemindPopFrameReqDTO = stockCheckRemindServiceHelper.dealSendCheckRemindPopFrameReqDTO(stockCheckSendCheckRemindPopFrameReqDTO);
        // 获取sql
        String sql = stockCheckRemindServiceHelper.getSendCheckRemindPopFrameSql(userIdList, stockCheckSendCheckRemindPopFrameReqDTO);
        // 把sql转化未txt文本上传到文件平台上
        String fileName = "发送盘点提醒弹窗sql" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".txt";
        MockMultipartFile file = new MockMultipartFile("file", fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), sql.getBytes(StandardCharsets.UTF_8));
        Map<String, String> returnMap = fileUtil.uploadFile(file);
        if(null == returnMap){
            log.error("StockCheckRemindServiceImpl.sendCheckRemindPopFrameToNotCheckPeople上传文件失败，sql为：{}", sql);
            return ResponseData.createFailResult("上传文件失败");
        }
        log.info("StockCheckRemindServiceImpl.sendCheckRemindPopFrameToNotCheckPeople上传文件成功，返回参数为：{}", JSON.toJSONString(returnMap));
        String downloadUrl = returnMap.get(FileUtilConstant.DOWNLOAD_URL);
        if(StringUtils.isBlank(downloadUrl)){
            log.error("StockCheckRemindServiceHelper.sendCheckRemindPopFrameToNotCheckPeople下载链接不存在，sql为：{}", sql);
            return ResponseData.createFailResult("下载链接不存在");
        }
        // 发送sql文件相关邮件
        List<Map<String, String>> contentList = new ArrayList<>(CommonConstant.NUMBER_ONE);
        contentList.add(StockBeanMap.putKeyValueNew(WorkflowConstants.DOWNLOAD_URL, downloadUrl));
        SendGuaGuaMessageUtil.sendMessage(PropertyConstants.CHECK_REMIND_POP_FRAME_EMAIL, contentList);
        return ResponseData.createSuccessResult();
    }

}
