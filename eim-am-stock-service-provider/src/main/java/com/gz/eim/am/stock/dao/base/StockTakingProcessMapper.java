package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockTakingProcess;
import com.gz.eim.am.stock.entity.StockTakingProcessExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockTakingProcessMapper {
    long countByExample(StockTakingProcessExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockTakingProcess record);

    int insertSelective(StockTakingProcess record);

    List<StockTakingProcess> selectByExample(StockTakingProcessExample example);

    StockTakingProcess selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockTakingProcess record, @Param("example") StockTakingProcessExample example);

    int updateByExample(@Param("record") StockTakingProcess record, @Param("example") StockTakingProcessExample example);

    int updateByPrimaryKeySelective(StockTakingProcess record);

    int updateByPrimaryKey(StockTakingProcess record);
}