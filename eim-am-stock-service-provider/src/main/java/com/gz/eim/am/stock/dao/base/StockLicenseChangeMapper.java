package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockLicenseChange;
import com.gz.eim.am.stock.entity.StockLicenseChangeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockLicenseChangeMapper {
    long countByExample(StockLicenseChangeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockLicenseChange record);

    int insertSelective(StockLicenseChange record);

    List<StockLicenseChange> selectByExample(StockLicenseChangeExample example);

    StockLicenseChange selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockLicenseChange record, @Param("example") StockLicenseChangeExample example);

    int updateByExample(@Param("record") StockLicenseChange record, @Param("example") StockLicenseChangeExample example);

    int updateByPrimaryKeySelective(StockLicenseChange record);

    int updateByPrimaryKey(StockLicenseChange record);
}