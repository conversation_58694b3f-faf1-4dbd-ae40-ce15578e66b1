package com.gz.eim.am.stock.service.impl.supplies;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockAssetsReceivePushWarehouseConfigMapper;
import com.gz.eim.am.stock.dto.request.supplies.StockAssetsReceivePushWarehouseConfigReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsReceivePushWarehouseConfig;
import com.gz.eim.am.stock.entity.StockAssetsReceivePushWarehouseConfigExample;
import com.gz.eim.am.stock.service.supplies.StockAssetsReceivePushWarehouseConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @className: StockAssetsReceivePushWarehouseConfigServiceImpl
 * @description: 资产领用仓库推送配置表
 * @author: <EMAIL>
 * @date: 2021/11/12
 **/
@Service
public class StockAssetsReceivePushWarehouseConfigServiceImpl implements StockAssetsReceivePushWarehouseConfigService {
    @Autowired
    private StockAssetsReceivePushWarehouseConfigMapper stockAssetsReceivePushWarehouseConfigMapper;
    /**
     * @param:
     * @description: 查询资产领用仓库推送配置表集合
     * @return:
     * @author: <EMAIL>
     * @date: 2021/11/12
     */
    @Override
    public List<StockAssetsReceivePushWarehouseConfig> getStockAssetsReceivePushWarehouseConfigList(StockAssetsReceivePushWarehouseConfigReqDTO stockAssetsReceivePushWarehouseConfigReqDTO) {
        StockAssetsReceivePushWarehouseConfigExample  stockAssetsReceivePushWarehouseConfigExample= new StockAssetsReceivePushWarehouseConfigExample();
        StockAssetsReceivePushWarehouseConfigExample.Criteria criteria = stockAssetsReceivePushWarehouseConfigExample.createCriteria();
        if(StringUtils.isNotEmpty(stockAssetsReceivePushWarehouseConfigReqDTO.getEntryLocation())){
            criteria.andEntryLocationEqualTo(stockAssetsReceivePushWarehouseConfigReqDTO.getEntryLocation());
        }
        criteria.andStatusEqualTo(CommonConstant.NUMBER_ONE);
        criteria.andDelFlagEqualTo(CommonConstant.NUMBER_ZERO);
        return stockAssetsReceivePushWarehouseConfigMapper.selectByExample(stockAssetsReceivePushWarehouseConfigExample);
    }
}
