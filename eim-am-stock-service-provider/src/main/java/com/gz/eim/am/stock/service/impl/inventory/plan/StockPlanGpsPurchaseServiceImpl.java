package com.gz.eim.am.stock.service.impl.inventory.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.JsonUtil;
import com.google.common.collect.Lists;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dto.request.inventory.plan.*;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.StockGpsSnsTailRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.service.inventory.plan.*;
import com.gz.eim.am.stock.service.manage.StockSuppliesConfigDetailService;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/12/15
 * @description
 */
@Service
@Slf4j
public class StockPlanGpsPurchaseServiceImpl implements StockPlanGpsPurchaseService {

    /**
     * 返回值标识：count
     */
    private static final String DATA_COUNT = "count";
    /**
     * 返回值标识：inventoryInPlanLineAssetRespDTOS
     */
    private static final String DATA_INV_PLAN_LINE_ASSET_RESP_DTO_S = "inventoryInPlanLineAssetRespDTOS";
    /**
     * 校验参数
     */
    private static final String CHECKCODE = "stock";

    @Autowired
    private StockInventoryInPlanHeadService stockInventoryInPlanHeadService;
    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;

    @Autowired
    private StockWarehouseService stockWarehouseService;

    @Autowired
    private StockWarehouseBaseService stockWarehouseBaseService;

    @Autowired
    private StockSuppliesService stockSuppliesService;

    @Autowired
    private StockInventoryInPlanLineService stockInventoryInPlanLineService;

    @Autowired
    private StockSuppliesConfigDetailService stockSuppliesConfigDetailService;

    @Autowired
    private StockInventoryInPlanLineSnsService stockInventoryInPlanLineSnsService;

    @Autowired
    private StockGpsSnsTailService stockGpsSnsTailService;

    @Autowired
    private StockPlanHelper stockInventoryPlanHelper;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanSnsPurchase(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException {
        String checkReturn = checkSaveParam (inventoryInPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank (checkReturn)) {
            return ResponseData.createFailResult (checkReturn);
        }


        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead ();
        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = new ArrayList<>();

        //准备插入参数
        prepareSaveDbBeanDTO (inventoryInPlanHeadReqDTO, stockInventoryInPlanHead, stockInventoryInPlanLineList, user);

        ///插入计划入库单
        stockInventoryInPlanHeadService.insert (stockInventoryInPlanHead);

        //导入计划入库单行
        if (!CollectionUtils.isEmpty (stockInventoryInPlanLineList)) {
            //查找入库单
            stockInventoryInPlanLineList.forEach (stockInventoryInPlanLine -> stockInventoryInPlanLine.setInventoryInPlanHeadId (stockInventoryInPlanHead.getInventoryInPlanHeadId ()));
            Integer count = stockInventoryInPlanLineService.batchInsertStockInventoryPlanLine (stockInventoryInPlanLineList);
            if (count == null || count < 1) {
                log.error ("批量插入计划入库单单号错误，计划入库单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo ());
                throw new ServiceUncheckedException(ResponseCode.SYSTEM_ERROR.getMessage ());
            }
        }

        return ResponseData.createSuccessResult ();
    }

    @Override
    public ResponseData selectPlanSnsPurchase(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO, JwtUser user) {
        inventoryInPlanSearchReqDTO.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.GPS_PURCHASE.getCode ());
        return stockInventoryInPlanHeadService.selectInventoryInPlan (inventoryInPlanSearchReqDTO, user);
    }

    @Override
    public ResponseData cancelPlanSnsPurchaseById(Long inventoryInPlanHeadId) {
        return stockInventoryInPlanHeadService.cancelInventoryInPlanById(inventoryInPlanHeadId);
    }

    @Override
    public ResponseData selectPlanSnsPurchaseById(Long inventoryInPlanHeadId, JwtUser user) {
        InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO = new InventoryInPlanLineReqDTO();
        inventoryInPlanLineReqDTO.setInventoryInPlanHeadId(inventoryInPlanHeadId);
        return stockInventoryInPlanHeadService.selectInventoryInPlanById (inventoryInPlanLineReqDTO, user, true);
    }

    @Override
    public ResponseData selectPlanSnsPurchaseLineGpsByLineId(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user) {
        if (null == inventoryInPlanLineReqDTO || null == inventoryInPlanLineReqDTO.getInventoryInPlanLineId ()) {
            return ResponseData.createFailResult ("必填参数为空");
        }

        Map<String, Object> result = new HashMap<>(2);
        //授权查询
        List<String> employeeWs = this.stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, null);
        if (CollectionUtils.isEmpty (employeeWs)) {
            return ResponseData.createSuccessResult ("权限不足");
        }

        Long count = stockInventoryInPlanLineSnsService.selectCountByParam (inventoryInPlanLineReqDTO);
        if (null == count || count <= 0) {
            result.put (DATA_COUNT, 0);
            result.put (DATA_INV_PLAN_LINE_ASSET_RESP_DTO_S, Lists.newArrayList ());
        }

        result.put (DATA_COUNT, count);
        //设置分页
        if (null == inventoryInPlanLineReqDTO.getPageSize ()
                || null == inventoryInPlanLineReqDTO.getPageNum ()) {
            inventoryInPlanLineReqDTO.initPageParam ();
        }
        inventoryInPlanLineReqDTO.setStartNum ((inventoryInPlanLineReqDTO.getPageNum () - 1) * inventoryInPlanLineReqDTO.getPageSize ());

        List<InventoryInPlanLineAssetRespDTO> inventoryInPlanLineAssetRespDTOS = stockInventoryInPlanLineSnsService.selectByPage (inventoryInPlanLineReqDTO);
        result.put (DATA_INV_PLAN_LINE_ASSET_RESP_DTO_S, inventoryInPlanLineAssetRespDTOS);
        return ResponseData.createSuccessResult (result);
    }

    @Override
    public ResponseData searchGpsDetailBySns(StockGpsSnsTailReqDTO stockGpsSnsTailReqDTO) {
        //1.校验参数
        if (stockGpsSnsTailReqDTO == null){
            throw new ServiceUncheckedException("参数不能为空");
        }
        if (!CHECKCODE.equals(stockGpsSnsTailReqDTO.getSystemCode())){
            log.info("金融gps接口获取系统标识:"+stockGpsSnsTailReqDTO.getSystemCode());
            throw new ServiceUncheckedException("系统标识不能为空");
        }
        if (CollectionUtils.isEmpty(stockGpsSnsTailReqDTO.getSnNos()) || stockGpsSnsTailReqDTO.getSnNos().size()>CommonConstant.MAX_OUT_SYSTEM_QUERY_COUNT){
            throw new ServiceUncheckedException("序列号数组不能为空且要小于200条");
        }
        //2.查询数据
        List<String> snNos = stockGpsSnsTailReqDTO.getSnNos();
        log.info("查询gps序列号参数:"+ JsonUtil.getJsonString(snNos));
        Map<String, StockGpsSnsTailRespDTO> resultMap = stockGpsSnsTailService.getGpsTailMapBySns(snNos);
        //3.返回结果
        return ResponseData.createSuccessResult(resultMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData gpsPlanPurchaseInBound(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user) throws ParseException {
        //1.参数校验
        String checkInBoundParam = checkInBoundParam (inventoryInPlanLineReqDTO, user);
        if (StringUtils.isNotBlank (checkInBoundParam)) {
            return ResponseData.createFailResult (checkInBoundParam);
        }
        //2.更新申请入库单头表和行表信息
        updatePlanHeadAndLineStatus(inventoryInPlanLineReqDTO,user);
        //3.生成行下的序列号表数据
        List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsList = new ArrayList<>();
        prepareInventoryInAssetsAndSnDTO(inventoryInPlanLineReqDTO,stockInventoryInPlanLinesSnsList,user);
        stockInventoryInPlanLineSnsService.batchInsertStockInventoryPlanLineSns(stockInventoryInPlanLinesSnsList);
        //4.生成入库单，gps尾表，库存数据
        log.info("gps入库单："+inventoryInPlanLineReqDTO.getInventoryInPlanHeadId()+";开始入库");
        InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO = new InventoryInPlanHeadReqDTO();
        inventoryInPlanHeadReqDTO.setInventoryInPlanHeadId(inventoryInPlanLineReqDTO.getInventoryInPlanHeadId());
        List<InventoryInPlanLineReqDTO> inventoryInPlanLineReqDTOList = new ArrayList<>(1);
        inventoryInPlanLineReqDTOList.add(inventoryInPlanLineReqDTO);
        inventoryInPlanHeadReqDTO.setInventoryInPlanLineReqDTOS(inventoryInPlanLineReqDTOList);
        stockInventoryInPlanHeadService.generateStockInventory (inventoryInPlanHeadReqDTO, user);
        log.info("gps入库单："+inventoryInPlanLineReqDTO.getInventoryInPlanHeadId()+";入库完成");
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData updateGpsInstallStatus(StockGpsStatusChangeReqDTO stockGpsStatusChangeReqDTO) {
        //1.校验参数
        if (stockGpsStatusChangeReqDTO == null) {
            throw new ServiceUncheckedException("参数不能为空");
        }
        if (!CHECKCODE.equals(stockGpsStatusChangeReqDTO.getSystemCode())) {
            log.info("金融gps接口获取系统标识:" + stockGpsStatusChangeReqDTO.getSystemCode());
            throw new ServiceUncheckedException("系统标识不能为空");
        }

        Integer installStatus = stockGpsStatusChangeReqDTO.getInstallStatus();
        List<String> snNos = stockGpsStatusChangeReqDTO.getSnNos();
        log.info("更新gps序列号参数:" + JsonUtil.getJsonString(snNos) + "; 更新状态:" + installStatus);
        if (null == installStatus || !StockGpsTailEnum.gpsInstallStatusList.contains(installStatus)) {
            throw new ServiceUncheckedException("更新状态不合法");
        }
        if (CollectionUtils.isEmpty(snNos) || snNos.size() > CommonConstant.MAX_OUT_SYSTEM_QUERY_COUNT) {
            throw new ServiceUncheckedException("序列号数组不能为空且要小于200条");
        }

        //2.更新数据
        stockGpsSnsTailService.batchUpdateInstallStatus(snNos, installStatus);
        //3.返回结果
        return ResponseData.createSuccessResult();
    }

    /**
     * 组织行资产表和行序列号表数据
     * @param inventoryInPlanLineReqDTO
     * @param stockInventoryInPlanLinesSnsList
     * @param user
     */
    private void prepareInventoryInAssetsAndSnDTO(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsList,JwtUser user){
        List<String> snNos = inventoryInPlanLineReqDTO.getSnNos();
        snNos.stream().forEach(dto->{
            StockInventoryInPlanLinesSns stockinventoryinplanlinessns = new StockInventoryInPlanLinesSns();
            stockinventoryinplanlinessns.setInventoryInPlanHeadId(inventoryInPlanLineReqDTO.getInventoryInPlanHeadId());
            stockinventoryinplanlinessns.setSnNo(dto);
            stockinventoryinplanlinessns.setInventoryInPlanLineId(inventoryInPlanLineReqDTO.getInventoryInPlanLineId());
            stockinventoryinplanlinessns.setDataStatus(CommonEnum.status.YES.getValue());
            stockinventoryinplanlinessns.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
            stockinventoryinplanlinessns.setDelFlag(CommonEnum.status.YES.getValue());
            stockinventoryinplanlinessns.setCreatedAt(new Date());
            stockinventoryinplanlinessns.setCreatedBy(user.getEmployeeCode());
            stockinventoryinplanlinessns.setUpdatedAt(new Date());
            stockinventoryinplanlinessns.setUpdatedBy(user.getEmployeeCode());
            stockInventoryInPlanLinesSnsList.add(stockinventoryinplanlinessns);
        });
    }

    /**
     * gps申请单入库时更新申请入库单头表和行表状态
     * @param inventoryInPlanLineReqDTO
     */
    private void updatePlanHeadAndLineStatus(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user) {
        StockInventoryInPlanLine stockInventoryInPlanLine = stockInventoryInPlanLineService.selectByPrimaryKey (inventoryInPlanLineReqDTO.getInventoryInPlanLineId ());
        int thisNumber = inventoryInPlanLineReqDTO.getThisNumber ();
        int realNumber = stockInventoryInPlanLine.getRealNumber () == null ? 0 : stockInventoryInPlanLine.getRealNumber ();
        //入库详细状态、入库实际数量、实际入库时间
        if ((realNumber + thisNumber) < stockInventoryInPlanLine.getNumber ()) {
            stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.SECTION_IN.getStatus ());
            stockInventoryInPlanLine.setRealNumber (realNumber + thisNumber);
        } else {
            stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
            stockInventoryInPlanLine.setRealNumber (stockInventoryInPlanLine.getNumber ());
        }
        stockInventoryInPlanLine.setUpdatedBy (user.getEmployeeCode ());
        stockInventoryInPlanLine.setUpdatedAt(new Date());
        Boolean updateLineResult = stockInventoryInPlanLineService.updatePrimaryKeySelective (stockInventoryInPlanLine);
        if (!updateLineResult) {
            log.error ("更新Gps申请入库单行错误, stockInventoryInPlanLine:{}", stockInventoryInPlanLine.toString ());
            throw new ServiceUncheckedException ("系统错误");
        }

        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead ();
        stockInventoryInPlanHead.setUpdatedBy (user.getEmployeeCode ());
        stockInventoryInPlanHead.setInventoryInPlanHeadId (inventoryInPlanLineReqDTO.getInventoryInPlanHeadId ());
        List<StockInventoryInPlanLine> inventoryInPlanLines  = stockInventoryInPlanLineService.selectNotAlreadyIn (inventoryInPlanLineReqDTO.getInventoryInPlanHeadId ());
        if(org.springframework.util.CollectionUtils.isEmpty (inventoryInPlanLines)){
            stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.ALREADY_IN.getCode ());
        }else {
            stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.SECTION_IN.getCode ());
        }
        // 更新计划资产归还单
        Boolean updateHeadResult = stockInventoryInPlanHeadService.updatePrimaryKeySelective (stockInventoryInPlanHead);
        if (!updateHeadResult) {
            throw new ServiceUncheckedException ("更新Gps申请入库单错误");
        }

        //调用采购接口实时更新采购入库验收单状态
        try {
            stockInventoryPlanHelper.submitPurchaseInventoryInPlan(inventoryInPlanLineReqDTO.getInventoryInPlanHeadId(), user);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }





    /**
     * gps采购入库参数校验
     * @param inventoryInPlanLineReqDTO
     * @param user
     * @return
     * @throws ParseException
     */
    private String checkInBoundParam(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user) throws ParseException {
        if (null == inventoryInPlanLineReqDTO || null == inventoryInPlanLineReqDTO.getInventoryInPlanLineId() || null == inventoryInPlanLineReqDTO.getInventoryInPlanHeadId()
                || CollectionUtils.isEmpty(inventoryInPlanLineReqDTO.getSnNos())) {
            return "必填参数为空";
        }

        if (null == inventoryInPlanLineReqDTO.getThisNumber() || inventoryInPlanLineReqDTO.getSnNos().size() != inventoryInPlanLineReqDTO.getThisNumber()){
            return "入库数量参数和实际入库gps数量不相等";
        }

        long count = inventoryInPlanLineReqDTO.getSnNos().stream().distinct().count();
        if (count<inventoryInPlanLineReqDTO.getThisNumber()){
            return "提交的数据中存在重复序列号";
        }

        StockInventoryInPlanHead stockInventoryInPlanHead = stockInventoryInPlanHeadService.selectById(inventoryInPlanLineReqDTO.getInventoryInPlanHeadId());
        if (stockInventoryInPlanHead == null) {
            return "该申请入库单不存在";
        }

        if (!InventoryInPlanHeadEnum.Status.WAIT_IN.getCode().equals(stockInventoryInPlanHead.getStatus()) && !InventoryInPlanHeadEnum.Status.SECTION_IN.getCode().equals(stockInventoryInPlanHead.getStatus())) {
            return "该申请入库单非待入库状态";
        }

        //仓库权限查询
        List<String> wc = this.stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, stockInventoryInPlanHead.getInWarehouseCode());
        if (CollectionUtils.isEmpty(wc)) {
            return "无操作仓库的权限";
        }
        //校验行数据
        StockInventoryInPlanLine stockInventoryInPlanLine = stockInventoryInPlanLineService.selectByPrimaryKey (inventoryInPlanLineReqDTO.getInventoryInPlanLineId ());
        if (null == stockInventoryInPlanLine || null == stockInventoryInPlanLine.getInventoryInPlanLineId ()) {
            return "数据不存在;";
        }

        if (!stockInventoryInPlanLine.getInventoryInPlanHeadId ().equals (inventoryInPlanLineReqDTO.getInventoryInPlanHeadId ())) {
            return "数据与头标识不匹配;";
        }

        List<StockSupplies> stockSuppliesList = stockSuppliesService.getEnableSuppliesList (stockInventoryInPlanLine.getSuppliesCode ());
        if (CollectionUtils.isEmpty (stockSuppliesList)) {
            return "数据的采购码不存在或已禁用;";
        }

        if (!stockSuppliesService.judgeAuthByWarehouseCode (stockInventoryInPlanHead.getInWarehouseCode (), stockSuppliesList.get (CommonConstant.NUMBER_ZERO))) {
            return "数据的采购码不属于调入仓库;";
        }

        if (null != stockSuppliesList.get (0).getManageType () && stockSuppliesList.get (0).getManageType ().equals (SuppliesEnum.ManageType.BATCH_MANAGE.getType ())) {
            return "数据采购码不能为批次控制的;";
        }

        int realNumber = stockInventoryInPlanLine.getRealNumber () == null ? 0 : stockInventoryInPlanLine.getRealNumber ();
        if ((inventoryInPlanLineReqDTO.getThisNumber () + realNumber) > stockInventoryInPlanLine.getNumber ()) {
            return "实际入库数量不能大于计划入库数量;";
        }

        //批量查询库存
        List<String> existSns = this.stockSuppliesConfigDetailService.selectExistsSnsByBatch (null, inventoryInPlanLineReqDTO.getSuppliesCode (), DateUtils.dateFormat (new Date (), DateUtils.MONTH_PATTERN), inventoryInPlanLineReqDTO.getSnNos());
        if (!CollectionUtils.isEmpty (existSns)) {
            StringBuilder sb = new StringBuilder();
            for (String snNo : existSns) {
                sb.append("序列号" + snNo+ "已存在;");
            }
            if (sb.length () != 0) {
                return sb.toString ();
            }
        }

        return null;
    }


    /**
     * 校验保存计划资产采购入库单参数
     *
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) {
        if (null == inventoryInPlanHeadReqDTO) {
            return "必填参数为空";
        }

        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getInWarehouseCode ())) {
            return "调入仓库不能为空";
        }

        //仓库权限查询
        List<String> wc = this.stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        if (CollectionUtils.isEmpty (wc)) {
            return "无操作仓库的权限";
        }

        //仓库是否有效
        StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode (), WarehouseEnum.Status.NORMAL.getStatus ());
        if (null == stockWarehouse) {
            return "该仓库已禁用";
        }


        //仓库地址是否存在
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        if (null == stockWarehouseBase || StringUtils.isBlank (stockWarehouseBase.getAddress ())) {
            return "找不到调入仓库对应的地址";
        }

        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getPlanInTime ())) {
            return "计划入库时间不能为空";
        }

        if (CollectionUtils.isEmpty (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ())) {
            return "无计划入库的数据";
        }

        //校验计划入库单详细
        StringBuilder sb = new StringBuilder ();
        for (int i = 0; i < inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().size (); i++) {
            InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO = inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().get (i);
            if (StringUtils.isBlank (inventoryInPlanLineReqDTO.getSuppliesCode ())) {
                sb.append ("第" + (i + 1) + "行数据的采购码不能为空");
                continue;
            }

            List<StockSupplies> stockSuppliesList = stockSuppliesService.getEnableSuppliesList (inventoryInPlanLineReqDTO.getSuppliesCode ());
            if (CollectionUtils.isEmpty (stockSuppliesList)) {
                sb.append ("第" + (i + 1) + "行数据的采购码不能为空不存在或已禁用");
                continue;
            }

            if (!stockSuppliesService.judgeAuthByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode (), stockSuppliesList.get (CommonConstant.NUMBER_ZERO))) {
                sb.append ("第" + (i + 1) + "行数据的采购码不属于调入仓库");
                continue;
            }

            if (null != stockSuppliesList.get (0).getManageType () && stockSuppliesList.get (0).getManageType ().equals (SuppliesEnum.ManageType.BATCH_MANAGE.getType ())) {
                sb.append ("第" + (i + 1) + "数据采购码不能为批次控制的;");
                continue;
            }

            if (inventoryInPlanLineReqDTO.getNumber () == null || inventoryInPlanLineReqDTO.getNumber () <= 0) {
                sb.append ("计划入库单详细数据的第" + (i + 1) + "条数据的计划入库数量不能小于0");
                continue;
            }
        }
        if (sb.length () != 0) {
            return sb.toString ();
        }

        return null;
    }

    /**
     * 准备入库参数
     *
     * @param inventoryInPlanHeadReqDTO
     * @param stockInventoryInPlanHead
     * @param stockInventoryInPlanLineList
     * @param user
     * @throws ParseException
     */
    private void prepareSaveDbBeanDTO(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead, List<StockInventoryInPlanLine> stockInventoryInPlanLineList, JwtUser user) throws ParseException {
        stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.WAIT_IN.getCode ());
        stockInventoryInPlanHead.setCreatedBy (user.getEmployeeCode ());
        stockInventoryInPlanHead.setInWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        stockInventoryInPlanHead.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.GPS_PURCHASE.getCode ());
        stockInventoryInPlanHead.setVendorCode (inventoryInPlanHeadReqDTO.getVendorCode ());
        stockInventoryInPlanHead.setVendorName (inventoryInPlanHeadReqDTO.getVendorName ());
        stockInventoryInPlanHead.setPurchaseOrderNo(inventoryInPlanHeadReqDTO.getBizNo ());
        stockInventoryInPlanHead.setBizNo(inventoryInPlanHeadReqDTO.getBizNo());
        stockInventoryInPlanHead.setDeliveryNo (inventoryInPlanHeadReqDTO.getDeliveryNo ());
        stockInventoryInPlanHead.setPlanInTime(StringUtils.isBlank (inventoryInPlanHeadReqDTO.getPlanInTime ()) ? new Date() : DateUtils.dateParse (inventoryInPlanHeadReqDTO.getPlanInTime (), DateUtils.DATE_PATTERN));
        stockInventoryInPlanHead.setBillingUser(inventoryInPlanHeadReqDTO.getBillingUser () != null ? inventoryInPlanHeadReqDTO.getBillingUser () : user.getEmployeeCode ());
        stockInventoryInPlanHead.setRemark (inventoryInPlanHeadReqDTO.getRemark ());
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        //收货人为仓库的linkman
        if (stockWarehouseBase != null) {
            stockInventoryInPlanHead.setReceiveUser (stockWarehouseBase.getLinkman ());
        }
        stockInventoryInPlanHead.setOutWarehouseCode (inventoryInPlanHeadReqDTO.getOutWarehouseCode ());
        stockInventoryInPlanHead.setBillingTime(StringUtils.isBlank (inventoryInPlanHeadReqDTO.getBillingTime ()) ? new Date () : DateUtils.dateParse (inventoryInPlanHeadReqDTO.getBillingTime (), DateUtils.DATE_PATTERN));

        stockInventoryInPlanHead.setDutyUser (inventoryInPlanHeadReqDTO.getDutyUser ());
        stockInventoryInPlanHead.setUpdatedBy (user.getEmployeeCode ());

        stockInventoryInPlanHead.setDemandDeptCode (inventoryInPlanHeadReqDTO.getDemandDeptCode ());
        stockInventoryInPlanHead.setCompanyCode(inventoryInPlanHeadReqDTO.getCompanyCode());
        //组装计划入库单行
        List<InventoryInPlanLineReqDTO> inventoryInPlanLineReqDTOList = inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ();
        if (!CollectionUtils.isEmpty (inventoryInPlanLineReqDTOList)) {
            //获取所有物料编码
            List<String> suppliesCodes = inventoryInPlanLineReqDTOList.stream().map(dto->dto.getSuppliesCode()).collect(Collectors.toList());
            Map<String,StockSupplies> suppliesMap = stockSuppliesService.getSuppliesMapByCodes(null,suppliesCodes);
            StockSupplies stockSupplies = new StockSupplies();

            for (InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO : inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ()) {
                StockInventoryInPlanLine stockInventoryInPlanLine = new StockInventoryInPlanLine ();
                //物料编码
                stockInventoryInPlanLine.setSuppliesCode (inventoryInPlanLineReqDTO.getSuppliesCode ());
                //计划入库数量
                stockInventoryInPlanLine.setNumber (inventoryInPlanLineReqDTO.getNumber ());
                stockInventoryInPlanLine.setRealNumber (0);
                stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.WAIT_IN.getStatus ());
                stockInventoryInPlanLine.setUnitPrice(suppliesMap.getOrDefault(inventoryInPlanLineReqDTO.getSuppliesCode(),stockSupplies).getPurchasePrice());
                stockInventoryInPlanLine.setCreatedBy (user.getEmployeeCode ());
                stockInventoryInPlanLine.setUpdatedBy (user.getEmployeeCode ());

                stockInventoryInPlanLineList.add (stockInventoryInPlanLine);
            }
        }
    }
}
