package com.gz.eim.am.stock.util.em;

/**
 * <AUTHOR>
 * @date 2019-09-24 下午 2:51
 */
public class WarehouseEnum {

    /**
     * 结算中心类型
     */
    public enum CostCenterType{
        /**
         * 部门
         */
        DEPT(1),
        /**
         * 项目
         */
        PROJECT(2)
        ;
        CostCenterType(Integer type){
            this.type = type;
        }
        private Integer type;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        boolean checkType(Integer type){
            return type.equals(DEPT.type) || type.equals(PROJECT.type);
        }
    }

    /**
     * 仓库状态
     */
    public enum Status{
        /**
         * 禁用
         */
        FORBID(0),
        /**
         * 正常
         */
        NORMAL(1)
        ;
        Status(Integer status){
            this.status = status;
        }
        private Integer status;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    /**
     * 仓库类型
     */
    public enum Type{
        /**
         * 物理仓
         */
        PARENT(0,"物理仓"),
        /**
         * 金融GPS仓
         */
        NORMAL(1,"金融GPS仓"),
        /**
         *
         * 固资
         */
        SOLID_CAPITAL(2,"固资仓"),
        /**
         * 低值
         */
        LOW_VALUE(3,"低值仓"),
        /**
         * 耗材
         */
        CONSUMABLES(4,"耗材仓"),
        /**
         * 礼品仓
         */
        PRESENT(5,"新车礼品仓"),
        /**
         * 费用仓
         */
        COST_CAPITAL(6,"费用仓"),
        /**
         * 服务仓
         */
        SERVE_CAPITAL(7,"服务仓"),
        /**
         * 新车运营仓
         */
        NEW_BUSSINESS(8,"新车运营仓"),
        /**
         * 新车营销仓
         */
        NEW_MARKET(9,"新车营销仓"),
        /**
         * 二手车运营仓
         */
        OLD_BUSSINESS(10,"二手车运营仓"),
        /**
         * 二手车营销仓
         */
        OLD_MARKET(11,"二手车营销仓"),
        /**
         * 执照仓
         */
        LICENSE_SEAL(12,"执照仓/印章仓"),
        /**
         * 后市场运营仓
         */
        OLDMARKER_BUSSINESS(13,"后市场运营仓"),
        /**
         * 后市场营销仓
         */
        OLDMARKER_MARKET(14,"后市场营销仓"),
        /**
         * 非金融GPS仓
         */
        NOBANK_MARKET(15,"非金融GPS仓"),
        /**
         * 无形资产仓
         */
        VIRTUAL_MARKET(16,"无形资产仓"),
        /**
         * 卡仓
         */
        CARD_MARkET(17,"卡仓"),
        ;
        Type(Integer type,String desc){
            this.type = type;
            this.desc = desc;
        }
        private Integer type;
        private String desc;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    /**
     * 仓库物理类型
     */
    public enum PhysicalType{
        /**
         * 实体仓
         */
        ENTITY(1),
        /**
         * 虚拟仓
         */
        VIRTUAL(2),
        ;
        PhysicalType(Integer type){
            this.type = type;
        }
        private Integer type;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }

    /**
     * 仓库用途
     */
    public enum Purpose{
        /**
         * 发货仓
         */
        DELIVER(1),
        /**
         * 虚拟仓
         */
        STORAGE(2),
        ;
        Purpose(Integer type){
            this.type = type;
        }
        private Integer type;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }

    /**
     * 仓库基本信息状态
     */
    public enum BaseStatus{
        /**
         * 禁用
         */
        FORBID(0),
        /**
         * 正常
         */
        NORMAL(1)
        ;
        BaseStatus(Integer status){
            this.status = status;
        }
        private Integer status;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
