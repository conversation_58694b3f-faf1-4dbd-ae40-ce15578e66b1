package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockInventoryInPlanLine {
    private Long inventoryInPlanLineId;

    private Long inventoryInPlanHeadId;

    private String simCompanyCode;

    private String serveCompanyCode;

    private String suppliesCode;

    private Integer number;

    private Integer realNumber;

    private Integer rejectNumber;

    private String assetsCode;

    private Integer status;

    private Date planInTime;

    private Long purchaseOrderLineId;

    private String receiveItemNo;

    private BigDecimal unitPrice;

    private BigDecimal unitTaxRate;

    private BigDecimal simUnitPrice;

    private BigDecimal simTaxRate;

    private BigDecimal serveUnitPrice;

    private BigDecimal serveTaxRate;

    private BigDecimal priceExcludingTax;

    private BigDecimal totalPrice;

    private String currency;

    private String cancelRemark;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private String version;

    private BigDecimal simReNewUnitPrice;

    private BigDecimal simReNewTaxRate;

    private BigDecimal serveReNewUnitPrice;

    private BigDecimal serveReNewTaxRate;

    private BigDecimal simGroupReNewUnitPrice;

    private BigDecimal simGroupReNewTaxRate;

    private BigDecimal serveGroupReNewUnitPrice;

    private BigDecimal serveGroupReNewTaxRate;

    public Long getInventoryInPlanLineId() {
        return inventoryInPlanLineId;
    }

    public void setInventoryInPlanLineId(Long inventoryInPlanLineId) {
        this.inventoryInPlanLineId = inventoryInPlanLineId;
    }

    public Long getInventoryInPlanHeadId() {
        return inventoryInPlanHeadId;
    }

    public void setInventoryInPlanHeadId(Long inventoryInPlanHeadId) {
        this.inventoryInPlanHeadId = inventoryInPlanHeadId;
    }

    public String getSimCompanyCode() {
        return simCompanyCode;
    }

    public void setSimCompanyCode(String simCompanyCode) {
        this.simCompanyCode = simCompanyCode == null ? null : simCompanyCode.trim();
    }

    public String getServeCompanyCode() {
        return serveCompanyCode;
    }

    public void setServeCompanyCode(String serveCompanyCode) {
        this.serveCompanyCode = serveCompanyCode == null ? null : serveCompanyCode.trim();
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode == null ? null : suppliesCode.trim();
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public Integer getRejectNumber() {
        return rejectNumber;
    }

    public void setRejectNumber(Integer rejectNumber) {
        this.rejectNumber = rejectNumber;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getPlanInTime() {
        return planInTime;
    }

    public void setPlanInTime(Date planInTime) {
        this.planInTime = planInTime;
    }

    public Long getPurchaseOrderLineId() {
        return purchaseOrderLineId;
    }

    public void setPurchaseOrderLineId(Long purchaseOrderLineId) {
        this.purchaseOrderLineId = purchaseOrderLineId;
    }

    public String getReceiveItemNo() {
        return receiveItemNo;
    }

    public void setReceiveItemNo(String receiveItemNo) {
        this.receiveItemNo = receiveItemNo == null ? null : receiveItemNo.trim();
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitTaxRate() {
        return unitTaxRate;
    }

    public void setUnitTaxRate(BigDecimal unitTaxRate) {
        this.unitTaxRate = unitTaxRate;
    }

    public BigDecimal getSimUnitPrice() {
        return simUnitPrice;
    }

    public void setSimUnitPrice(BigDecimal simUnitPrice) {
        this.simUnitPrice = simUnitPrice;
    }

    public BigDecimal getSimTaxRate() {
        return simTaxRate;
    }

    public void setSimTaxRate(BigDecimal simTaxRate) {
        this.simTaxRate = simTaxRate;
    }

    public BigDecimal getServeUnitPrice() {
        return serveUnitPrice;
    }

    public void setServeUnitPrice(BigDecimal serveUnitPrice) {
        this.serveUnitPrice = serveUnitPrice;
    }

    public BigDecimal getServeTaxRate() {
        return serveTaxRate;
    }

    public void setServeTaxRate(BigDecimal serveTaxRate) {
        this.serveTaxRate = serveTaxRate;
    }

    public BigDecimal getPriceExcludingTax() {
        return priceExcludingTax;
    }

    public void setPriceExcludingTax(BigDecimal priceExcludingTax) {
        this.priceExcludingTax = priceExcludingTax;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public String getCancelRemark() {
        return cancelRemark;
    }

    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark == null ? null : cancelRemark.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    public BigDecimal getSimReNewUnitPrice() {
        return simReNewUnitPrice;
    }

    public void setSimReNewUnitPrice(BigDecimal simReNewUnitPrice) {
        this.simReNewUnitPrice = simReNewUnitPrice;
    }

    public BigDecimal getSimReNewTaxRate() {
        return simReNewTaxRate;
    }

    public void setSimReNewTaxRate(BigDecimal simReNewTaxRate) {
        this.simReNewTaxRate = simReNewTaxRate;
    }

    public BigDecimal getServeReNewUnitPrice() {
        return serveReNewUnitPrice;
    }

    public void setServeReNewUnitPrice(BigDecimal serveReNewUnitPrice) {
        this.serveReNewUnitPrice = serveReNewUnitPrice;
    }

    public BigDecimal getServeReNewTaxRate() {
        return serveReNewTaxRate;
    }

    public void setServeReNewTaxRate(BigDecimal serveReNewTaxRate) {
        this.serveReNewTaxRate = serveReNewTaxRate;
    }

    public BigDecimal getSimGroupReNewUnitPrice() {
        return simGroupReNewUnitPrice;
    }

    public void setSimGroupReNewUnitPrice(BigDecimal simGroupReNewUnitPrice) {
        this.simGroupReNewUnitPrice = simGroupReNewUnitPrice;
    }

    public BigDecimal getSimGroupReNewTaxRate() {
        return simGroupReNewTaxRate;
    }

    public void setSimGroupReNewTaxRate(BigDecimal simGroupReNewTaxRate) {
        this.simGroupReNewTaxRate = simGroupReNewTaxRate;
    }

    public BigDecimal getServeGroupReNewUnitPrice() {
        return serveGroupReNewUnitPrice;
    }

    public void setServeGroupReNewUnitPrice(BigDecimal serveGroupReNewUnitPrice) {
        this.serveGroupReNewUnitPrice = serveGroupReNewUnitPrice;
    }

    public BigDecimal getServeGroupReNewTaxRate() {
        return serveGroupReNewTaxRate;
    }

    public void setServeGroupReNewTaxRate(BigDecimal serveGroupReNewTaxRate) {
        this.serveGroupReNewTaxRate = serveGroupReNewTaxRate;
    }
}