package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockInventoryInAssetsConditions {
    private Long id;

    private Long inventoryInPlanHeadId;

    private Long inventoryInPlanLineAssetsId;

    private String assetsCode;

    private Integer assetsCondition;

    private Integer dealType;

    private String realInWarehouse;

    private Integer dutyBody;

    private String personLiable;

    private BigDecimal amount;

    private BigDecimal netValue;

    private BigDecimal disposeAmount;

    private String remark;

    private Integer payStatus;

    private Integer conditionStatus;

    private Integer delFlag;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInventoryInPlanHeadId() {
        return inventoryInPlanHeadId;
    }

    public void setInventoryInPlanHeadId(Long inventoryInPlanHeadId) {
        this.inventoryInPlanHeadId = inventoryInPlanHeadId;
    }

    public Long getInventoryInPlanLineAssetsId() {
        return inventoryInPlanLineAssetsId;
    }

    public void setInventoryInPlanLineAssetsId(Long inventoryInPlanLineAssetsId) {
        this.inventoryInPlanLineAssetsId = inventoryInPlanLineAssetsId;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public Integer getAssetsCondition() {
        return assetsCondition;
    }

    public void setAssetsCondition(Integer assetsCondition) {
        this.assetsCondition = assetsCondition;
    }

    public Integer getDealType() {
        return dealType;
    }

    public void setDealType(Integer dealType) {
        this.dealType = dealType;
    }

    public String getRealInWarehouse() {
        return realInWarehouse;
    }

    public void setRealInWarehouse(String realInWarehouse) {
        this.realInWarehouse = realInWarehouse == null ? null : realInWarehouse.trim();
    }

    public Integer getDutyBody() {
        return dutyBody;
    }

    public void setDutyBody(Integer dutyBody) {
        this.dutyBody = dutyBody;
    }

    public String getPersonLiable() {
        return personLiable;
    }

    public void setPersonLiable(String personLiable) {
        this.personLiable = personLiable == null ? null : personLiable.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getNetValue() {
        return netValue;
    }

    public void setNetValue(BigDecimal netValue) {
        this.netValue = netValue;
    }

    public BigDecimal getDisposeAmount() {
        return disposeAmount;
    }

    public void setDisposeAmount(BigDecimal disposeAmount) {
        this.disposeAmount = disposeAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Integer getConditionStatus() {
        return conditionStatus;
    }

    public void setConditionStatus(Integer conditionStatus) {
        this.conditionStatus = conditionStatus;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}