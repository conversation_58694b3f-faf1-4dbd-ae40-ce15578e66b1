package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsCheckTask;
import com.gz.eim.am.stock.entity.StockAssetsCheckTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsCheckTaskMapper {
    long countByExample(StockAssetsCheckTaskExample example);

    int deleteByPrimaryKey(Long checkTaskId);

    int insert(StockAssetsCheckTask record);

    int insertSelective(StockAssetsCheckTask record);

    List<StockAssetsCheckTask> selectByExample(StockAssetsCheckTaskExample example);

    StockAssetsCheckTask selectByPrimaryKey(Long checkTaskId);

    int updateByExampleSelective(@Param("record") StockAssetsCheckTask record, @Param("example") StockAssetsCheckTaskExample example);

    int updateByExample(@Param("record") StockAssetsCheckTask record, @Param("example") StockAssetsCheckTaskExample example);

    int updateByPrimaryKeySelective(StockAssetsCheckTask record);

    int updateByPrimaryKey(StockAssetsCheckTask record);
}