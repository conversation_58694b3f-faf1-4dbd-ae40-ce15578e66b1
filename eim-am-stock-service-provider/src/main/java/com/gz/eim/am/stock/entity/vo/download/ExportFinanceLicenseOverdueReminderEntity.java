package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import com.fuu.eim.tool.excel.constant.ExportType;

import java.util.Date;

/**
  * @description: 开户许可证逾期提醒文件模版
  * @author: <EMAIL>
  * @date: 2024/7/23
  */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportFinanceLicenseOverdueReminderEntity implements ExportModel {

   @ExportField(name = "资产编码")
   private String assetsCode;

   @ExportField(name = "资产名称")
   private String assetsName;

   @ExportField(name = "计划归还时间", type = ExportType.DATE, format = "yyyy-MM-dd")
   private Date planReturnTime;

   @ExportField(name = "当前借用人")
   private String holder;

   @ExportField(name = "当前借用人名称")
   private String holderName;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public Date getPlanReturnTime() {
        return planReturnTime;
    }

    public void setPlanReturnTime(Date planReturnTime) {
        this.planReturnTime = planReturnTime;
    }

    public String getHolder() {
        return holder;
    }

    public void setHolder(String holder) {
        this.holder = holder;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    @Override
   public String getSheetName() {
       return "执照信息";
   }
}
