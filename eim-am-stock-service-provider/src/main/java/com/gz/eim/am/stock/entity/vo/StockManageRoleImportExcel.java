package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;

/**
 * @Author: wangjing67
 * @Date: 6/28/21 6:32 下午
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockManageRoleImportExcel implements ExportModel {

    @ExportField(name = "角色名称")
    private String roleName;

    @ExportField(name = "有效开始日期")
    private String startDate;

    @ExportField(name = "有效截止日期")
    private String endDate;

    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
