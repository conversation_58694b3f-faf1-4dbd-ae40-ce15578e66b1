package com.gz.eim.am.stock.service.order;

import com.gz.eim.am.stock.dto.response.order.InventoryOutRespDTO;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/6/1
 * @description
 */
public interface StockDeliveryHistoryService {

    /**
     * 根据出库编号获取出库单-资产详情
     * @param deliveryNo
     * @return
     */
    InventoryOutRespDTO selectStockDeliveryAllByDeliveryNo(final String deliveryNo);

    /**
     * 根据计划出库单获取出库单行列表
     * @param deliveryPlanHeadId
     * @return
     */
    List<InventoryOutRespDTO> selectDeliveryByPlanHeadId(final Long deliveryPlanHeadId);
}
