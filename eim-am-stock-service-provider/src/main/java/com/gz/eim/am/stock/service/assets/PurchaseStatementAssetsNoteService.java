package com.gz.eim.am.stock.service.assets;

import com.gz.eim.am.stock.entity.PurchaseStatementAssetsNote;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2021/4/6
 * @description 资产结算单数据记录
 */
public interface PurchaseStatementAssetsNoteService {

    /**
     * 批量插入结算记录数据
     * @param purchaseStatementAssetsNoteList
     * @return
     */
    boolean batchInsert(List<PurchaseStatementAssetsNote> purchaseStatementAssetsNoteList);
}
