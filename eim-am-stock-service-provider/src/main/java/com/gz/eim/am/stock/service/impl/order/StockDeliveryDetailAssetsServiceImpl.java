package com.gz.eim.am.stock.service.impl.order;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockDeliveryDetailAssetMapper;
import com.gz.eim.am.stock.dao.order.DeliveryDetailAssetMapper;
import com.gz.eim.am.stock.dao.order.DeliveryDetailMapper;
import com.gz.eim.am.stock.entity.StockDeliveryDetailAsset;
import com.gz.eim.am.stock.entity.StockDeliveryDetailAssetExample;
import com.gz.eim.am.stock.entity.StockDeliveryDetailAssetReqDO;
import com.gz.eim.am.stock.service.order.StockDeliveryDetailAssetsService;
import com.gz.eim.am.stock.util.common.ListUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: lishuyang
 * @date: 2020/4/1
 * @description 出库单行关联资产service实现类
 */
@Slf4j
@Service
public class StockDeliveryDetailAssetsServiceImpl  implements StockDeliveryDetailAssetsService {

    @Autowired
    private DeliveryDetailAssetMapper deliveryDetailAssetMapper;
    @Autowired
    private StockDeliveryDetailAssetMapper stockDeliveryDetailAssetMapper;
    @Override
    public boolean insertMultiple(List<StockDeliveryDetailAsset> stockDeliveryDetailAssetList) {
        if(CollectionUtils.isEmpty(stockDeliveryDetailAssetList)){
            return false;
        }


        if (stockDeliveryDetailAssetList.size() > 0) {
            //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
            int toIndex = CommonConstant.MAX_INSERT_COUNT;
            for (int i = 0; i < stockDeliveryDetailAssetList.size(); i += CommonConstant.MAX_INSERT_COUNT) {
                if (i + CommonConstant.MAX_INSERT_COUNT > stockDeliveryDetailAssetList.size()) {
                    toIndex = stockDeliveryDetailAssetList.size() - i;
                }
                List<StockDeliveryDetailAsset> newSubDetailList = stockDeliveryDetailAssetList.subList(i, i + toIndex);
                deliveryDetailAssetMapper.insertMultiple(newSubDetailList);
            }
        }
        return true;
    }

    @Override
    public List<StockDeliveryDetailAsset> selectList(StockDeliveryDetailAssetReqDO stockDeliveryDetailAssetReqDO) {
        if(null == stockDeliveryDetailAssetReqDO){
            return new ArrayList<>();
        }
        StockDeliveryDetailAssetExample stockDeliveryDetailAssetExample = new StockDeliveryDetailAssetExample();
        StockDeliveryDetailAssetExample.Criteria criteria = stockDeliveryDetailAssetExample.createCriteria();
        if(stockDeliveryDetailAssetReqDO.getDeliveryId() != null){
            criteria.andDeliveryIdEqualTo(stockDeliveryDetailAssetReqDO.getDeliveryId());
        }
        if(StringUtils.isNotBlank(stockDeliveryDetailAssetReqDO.getAssetsCode())){
            criteria.andAssetsCodeEqualTo(stockDeliveryDetailAssetReqDO.getAssetsCode());
        }
        if(CollectionUtils.isNotEmpty(stockDeliveryDetailAssetReqDO.getAssetsCodeList())){
            criteria.andAssetsCodeIn(stockDeliveryDetailAssetReqDO.getAssetsCodeList());
        }
        return stockDeliveryDetailAssetMapper.selectByExample(stockDeliveryDetailAssetExample);
    }

    @Override
    public StockDeliveryDetailAsset selectOne(StockDeliveryDetailAssetReqDO stockDeliveryDetailAssetReqDO) {
        List<StockDeliveryDetailAsset> stockDeliveryDetailAssetList = selectList(stockDeliveryDetailAssetReqDO);
        if(CollectionUtils.isEmpty(stockDeliveryDetailAssetList)){
            return null;
        }
        return stockDeliveryDetailAssetList.get(CommonConstant.NUMBER_ZERO);
    }

    @Override
    public int batchUpdate(List<StockDeliveryDetailAsset> stockDeliveryDetailAssetList) {
        if(CollectionUtils.isEmpty(stockDeliveryDetailAssetList)){
            return CommonConstant.NUMBER_ZERO;
        }
        int count = CommonConstant.NUMBER_ZERO;
        // 如果大于1000条分批次插入
        if(stockDeliveryDetailAssetList.size() <= CommonConstant.MAX_UPDATE_COUNT){
            count =  deliveryDetailAssetMapper.batchUpdate(stockDeliveryDetailAssetList);
            log.info("StockDeliveryDetailAssetsServiceImpl.batchUpdate已经更新条数为：" + count);
            return count;
        }
        List<List<StockDeliveryDetailAsset>> stockDeliveryDetailAssetListList = ListUtil.splitList(stockDeliveryDetailAssetList, CommonConstant.MAX_INSERT_COUNT);
        for (List<StockDeliveryDetailAsset> StockDeliveryDetailAssets : stockDeliveryDetailAssetListList) {
            count += deliveryDetailAssetMapper.batchUpdate(StockDeliveryDetailAssets);
            log.info("StockDeliveryDetailAssetsServiceImpl.batchUpdate已经更新条数为：" + count);
        }
        return count;
    }
}
