package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.PageReqDTO;
import com.gz.eim.am.stock.dto.request.assets.*;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.vo.StockAssetsExtendCommonVo;
import io.swagger.models.auth.In;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 资产分类
 * <AUTHOR>
 * @date 2019-12-15 PM 10:00
 */
public interface StockAssetsService {

    /**
     * @param: assetsSearchDTO
     * @description: 查询资产Map信息
     * @return: Map<String, AssetsDTO>
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    Map<String, AssetsDTO> selectAssetDTOMapByAssetsDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * @param: assetsSearchDTO
     * @description: 查询资产Map信息
     * @return: List<AssetsDTO>
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    List<AssetsDTO> selectAssetDTOListByAssetsDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * 导出Excel：根据资产报废单查询资产信息
     * @param assetsSearchDTO
     * @return
     */
    ResponseData downLoadAssetsByScrapExcel(AssetsSearchDTO assetsSearchDTO, HttpServletRequest request, HttpServletResponse response)throws Exception ;

    /**
     * 根据资产报废单查询资产信息
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssetsByScrap(AssetsSearchDTO assetsSearchDTO);

    /**
     * 导出Excel根据资产入库申请单查询资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    ResponseData downLoadAssetsByInventoryPlanExcel(AssetsSearchDTO assetsSearchDTO, HttpServletRequest request, HttpServletResponse response)throws Exception ;

    /**
     * 根据资产入库申请单查询资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssetsByInventoryPlan(AssetsSearchDTO assetsSearchDTO);

    /**
     * 保存资产
     * @param assetsDTO
     * @return
     */
    ResponseData saveAssets(final AssetsDTO assetsDTO);

    /**
     * 更新资产
     * @param assetsDTO
     * @return
     */
    ResponseData updateAssets(final AssetsDTO assetsDTO);

    /**
     * 获取资产
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssets(final AssetsSearchDTO assetsSearchDTO);
    /**
     * 报废获取资产
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssetsByAssetsScrap(final AssetsSearchDTO assetsSearchDTO);
    /**
     * 搜索资产信息
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssetsByDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * 查询资产列表返回列表
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssets> selectAssetsList(final AssetsSearchDTO assetsSearchDTO);
    /**
     * 查询资产列表
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssets> selectAssetsListByAssetsSearchDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * ids or codes 获取资产
     * @param ids
     * @param codes
     * @return
     */
    List<StockAssets> selectAssets(final List<Long> ids, final List<String> codes);

    /**
     * 根据资产编码查询资产信息
     * @param code
     * @return
     */
    StockAssets selectAssetsByCode(final String code);

    /**
     * 根据资产id 查询资产信息
     * @param assetId
     * @return
     */
    StockAssets selectAssetsById(final Long assetId);

    /**
     * 更新资产信息，并记录资产更新日志
     * @param assets
     * @return
     */
    Integer updateAssets(final StockAssets assets);

    /**
    * @param: stockAssets
    * @description: 更新资产信息
    * @return: int
    * @author: <EMAIL>
    * @date: 2022/12/12
    */
    Integer updateOne(StockAssets stockAssets);


    /**
     * 批量插入资产
     * @param stockAssetsList
     * @param user
     * @return
     */
    boolean batchOperating(List<StockAssets> stockAssetsList, JwtUser user);

    /**
     * 通过资产编码批量更新资产
     * @param stockAssetsList
     * @return
     */
    Integer batchUpdateByAssetsCodeList(List<StockAssets> stockAssetsList);

    /**
     * 批量更新资产
     * @param stockAssetsList
     * @return
     */
    Integer batchUpdate(List<StockAssets> stockAssetsList);

    /**
     * 批量更新资产
     * @param assetsList
     * @return
     */
    Integer updateMultipleSelective(final List<StockAssets> assetsList);
    /**
     * 设置资产DTO关联值
     * @param assetsDTO
     * @return
     */
    AssetsDTO settingRelationValues(final AssetsDTO assetsDTO);
    /**
     * 设置资产DTO关联值
     * @param assetsDTOList
     * @return
     */
    List<AssetsDTO> settingRelationValues(final List<AssetsDTO> assetsDTOList);

    /**
     * 资产实体对象转DTO
     * @param assetsList
     * @return
     */
    List<AssetsDTO> modelToDTO(final List<StockAssets> assetsList);

    /**
     * 资产实体对象转DTO
     * @param assets
     * @return
     */
    AssetsDTO modelToDTO(final StockAssets assets);

    /**
     *
     * 查询当前持有人的持有的资产
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    List<AssetsDTO> selectAssetByHolder(String holder, String remandWarehouseCode);


    /**
     *
     * 分页查询当前持有人的持有的资产
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    List<AssetsDTO> selectAssetByHolder(String holder, String remandWarehouseCode,String assetsCode,Integer pageNum,Integer pageSize);

    /**
     *
     * 查询当前持有人的持有的资产
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    Long countAssetByHolder(String holder, String remandWarehouseCode);

    /**
     *
     * 查询当前持有人的持有的资产
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    List<AssetsDTO> selectAssetByHolder(String holder, String remandWarehouseCode,List<String> assetsCodeList);

    /**
     * 搜索资产业务记录
     * @param searchDTO
     * @return
     */
    ResponseData selectAssetsBusinessRecord(AssetsSearchDTO searchDTO);

    /**
     * 根据资产编码集合获取资产map集合
     * @param assetCodes
     * @return
     */
    Map<String,StockAssets> selectAssetsMapByCodes(List<String> assetCodes);

    /**
     * 根据资产编码集合获取资产map集合和其他信息获取资产Map
     * @param assetsSearchDTO
     * @return
     */
    Map<String,StockAssets> selectAssetsMapByAssetsSearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 资产标签补打
     * @param assetCodeReqDTO
     * @return
     */
    ResponseData serverPrintAssetCode(AssetCodeReqDTO assetCodeReqDTO, JwtUser user) throws IOException;

    /**
     * 资产标签补打
     * @param assetsCode
     * @return
     */
    ResponseData selectCostByAssetsCode(String assetsCode);

    /**
     * 查询人员在某些仓库类型下领用的资产信息 过滤掉哪些资产信息
     * @param holder
     * @param categoryCodes
     * @return
     */
    List<AssetsDTO> selectAssetByHolderAndWareTypes(String holder, List<String> categoryCodes,List<String> assetsCodeList);

    /**
     * 根据验收单行查询资产
     * @param receiveItemNo
     * @return
     */
    List<StockAssets> selectAssetsByReceiveItemNo(String receiveItemNo,Integer pageCount);

    /**
     * 根据资产id查询资产全量信息
     * @param assetsId
     * @return
     */
    ResponseData selectAssetsDetailById(Long assetsId);

    /**
     * 获取资产
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssetsExtendCommonVo> selectAssetsExtend(final AssetsSearchDTO assetsSearchDTO);

    /**
     * 分页查询当前登录人下的资产明细
     * @param pageReqDTO
     * @return
     */
    ResponseData selectAssetsByHolder(PageReqDTO pageReqDTO);

    /**
     * 获取资产的尾表属性
     * @param category
     * @param assetsCode
     * @return
     */
    Map<String, String> getAssetsExtendAttr(String category, String assetsCode);

    /**
     * 序列号获取资产
     * @param snCodes
     * @return
     */
    List<StockAssets> selectAssets(final List<String> snCodes);

    /**
     * 根据物料编码查询资产信息
     * @param suppliesCode
     * @return
     */
    List<StockAssets> selectAssetsBySuppliesCode(String suppliesCode);

    /**
     * 查询资产尾表信息
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssetsExtendInfo(AssetsSearchDTO assetsSearchDTO);
    /**
     * @param:
     * @description: 根据资产类型查询资产
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/9
     */
    public ResponseData selectAssetByType(String companyName, Integer type, JwtUser user,Integer pageNum, Integer pageSize);
    /**
     * @param:
     * @description: 根据资产类型和资产持有人以及根据资产编码和名称查询资产
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/9  
     */
    public ResponseData selectAssetByTypeAndHolder(String holder,String assetsCodeAndName, Integer type, JwtUser user,Integer pageNum, Integer pageSize,Integer isPC, Integer isSearch, Integer approveStatus);
    /**
     * @param:
     * @description: TODO
     * @return: 设置判断是否登陆人为资产所有人
     * @author: <EMAIL>
     * @date: 2021/7/27
     */
    public void setJudgeHolderHaveAssets(AssetsDTO assetsDTO);
    /**
     * @param:
     * @description: TODO
     * @return: 设置判断该资产是否已经在审批中了
     * @author: <EMAIL>
     * @date: 2021/7/27
     */
    public void setJudgeHolderAssetsIsApproval(AssetsDTO assetsDTO);


    /**
     * 上传生成资产编码
     * @return
     * @throws IOException
     */
    void batchPrintAssetsCode(MultipartFile file, String printKey) throws IOException;

    /**
     * 定时发送资产全量数据文件
     * @param
     * @return
     */
    ResponseData sendAssetsEmail();

    /**
     * @param:
     * @description: service调用查询全量资产
     * @return:
     * @author: <EMAIL>
     * @date: 2022/2/18
     */
    List<AssetsDTO> selectAssetsByService(final AssetsSearchDTO assetsSearchDTO);

     /**
       * @param:
       * @description: 批量修改资产
       * @return:
       * @author: <EMAIL>
       * @date: 2022/2/28
       */
    int batchUpdateAssetsSameMessage(AssetsSearchDTO assetsSearchDTO, List<String> assetsCodeList);

    /**
     * @param: assetsSearchDTO
     * @description: 领用搜索资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/13
     */
    ResponseData searchAssetsByReceive(AssetsSearchDTO assetsSearchDTO);

    /**
     * @param: assetsSearchDTO
     * @description: 归还搜索资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/13
     */
    ResponseData searchAssetsByRemand(AssetsSearchDTO assetsSearchDTO);
    /**
     * @param: stockAssetsNumberGroupByCategoryCodeReqDTO
     * @description: 通过资产分类分组后查询资产数量
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    ResponseData queryAssetsNumberGroupByCategoryCode(StockAssetsNumberGroupByCategoryCodeReqDTO stockAssetsNumberGroupByCategoryCodeReqDTO);
    /**
     * @param: assetsSearchDTO
     * @description: 查询资产信息
     * @return: StockAssets
     * @author: <EMAIL>
     * @date: 2023/12/1
     */
    StockAssets selectOne(AssetsSearchDTO assetsSearchDTO);
    /**
     * @param: assetsSearchDTO
     * @description: 查询资产信息
     * @return: List<StockAssets>
     * @author: <EMAIL>
     * @date: 2023/12/1
     */
    List<StockAssets> selectList(AssetsSearchDTO assetsSearchDTO);
}
