package com.gz.eim.am.stock.mapper.base;

import com.gz.eim.am.stock.entity.StockSupplies;
import com.gz.eim.am.stock.entity.StockSuppliesExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSuppliesMapper {
    long countByExample(StockSuppliesExample example);

    int deleteByPrimaryKey(Long suppliesId);

    int insert(StockSupplies record);

    int batchInsert(@Param("stockSuppliesList") List<StockSupplies> stockSuppliesList);

    int insertSelective(StockSupplies record);

    List<StockSupplies> selectByExample(StockSuppliesExample example);

    StockSupplies selectByPrimaryKey(Long suppliesId);

    int updateByExampleSelective(@Param("record") StockSupplies record, @Param("example") StockSuppliesExample example);

    int updateByExample(@Param("record") StockSupplies record, @Param("example") StockSuppliesExample example);

    int updateByPrimaryKeySelective(StockSupplies record);

    int batchUpdate(@Param("stockSuppliesList") List<StockSupplies> stockSuppliesList);

    int updateByPrimaryKey(StockSupplies record);


}