package com.gz.eim.am.stock.service.impl.inventory.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.StockAssetsCompensationConstant;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineAssetReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanHeadRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryPlanAssetRespDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.vo.StockAssetsInitializationInfo;
import com.gz.eim.am.stock.entity.vo.StockInventoryInPlanLinesAssetsInfo;
import com.gz.eim.am.stock.entity.vo.StockInventoryInPlanLinesSnsInfo;
import com.gz.eim.am.stock.service.assets.StockAssetsCompensationRecordService;
import com.gz.eim.am.stock.service.assets.StockAssetsInitializationService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.inventory.StockInventoryInAssetsConditionService;
import com.gz.eim.am.stock.service.inventory.plan.*;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanHeadService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.em.*;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lishuyang
 * @date: 2019/12/27
 * @description:
 */
@Service
public class StockPlanAssetRemandServiceImpl implements StockPlanAssetRemandService {
    private final Logger logger = LoggerFactory.getLogger(StockPlanAssetRemandServiceImpl.class);

    @Autowired
    private StockInventoryInPlanHeadService stockInventoryInPlanHeadService;

    @Autowired
    private StockInventoryInPlanLineService stockInventoryInPlanLineService;

    @Autowired
    private StockWarehouseService stockWarehouseService;

    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;

    @Autowired
    private StockAssetsService stockAssetsService;

    @Autowired
    private StockWarehouseBaseService stockWarehouseBaseService;
    @Autowired
    private StockAssetsInitializationService stockAssetsInitializationService;

    @Autowired
    StockInventoryInPlanLineAssetsService stockInventoryInPlanLineAssetsService;
    @Autowired
    StockInventoryInPlanLineSnsService stockInventoryInPlanLineSnsService;
    @Autowired
    StockInventoryInAssetsConditionService stockInventoryInAssetsConditionService;
    @Autowired
    StockDeliveryPlanHeadService stockDeliveryPlanHeadService;
    @Autowired
    private StockAssetsCompensationRecordService stockAssetsCompensationRecordService;

    @Lazy
    @Autowired
    private AssetsMapper assetsMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanAssetRemand(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException {
        String checkReturn = checkSaveParam(inventoryInPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank(checkReturn)) {
            return ResponseData.createFailResult(checkReturn);
        }


        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead();
        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = new ArrayList<>();
        List<StockInventoryInPlanLinesAssetsInfo> stockInventoryInPlanLinesAssetsList = new ArrayList<>();
        List<StockInventoryInPlanLinesSnsInfo> stockInventoryInPlanLinesSnsList = new ArrayList<>();

        //准备插入参数
        prepareSaveDbBeanDTO(inventoryInPlanHeadReqDTO, stockInventoryInPlanHead, stockInventoryInPlanLineList, stockInventoryInPlanLinesAssetsList, stockInventoryInPlanLinesSnsList, user);

        ///插入计划入库单
        stockInventoryInPlanHeadService.insert(stockInventoryInPlanHead);

        //导入计划入库单行
        if (!CollectionUtils.isEmpty(stockInventoryInPlanLineList)) {
            //查找入库单
            stockInventoryInPlanLineList.forEach(stockInventoryInPlanLine -> stockInventoryInPlanLine.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId()));
            Integer count = stockInventoryInPlanLineService.batchInsertStockInventoryPlanLine(stockInventoryInPlanLineList);
            if (count == null || count < 1) {
                logger.error("批量插入资产归还单行错误，资产归还单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo());
                throw new ServiceUncheckedException(ResponseCode.SYSTEM_ERROR.getMessage());
            }
        }

        //导入入库单行资产
        List<StockInventoryInPlanLinesAssets> stockInventoryInPlanLinesAssets = new ArrayList<>(stockInventoryInPlanLinesAssetsList.size());
        if (!CollectionUtils.isEmpty(stockInventoryInPlanLinesAssetsList)) {
            stockInventoryInPlanLinesAssetsList.forEach(stockInventoryInPlanLinesAsset -> {
                StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets1 = new StockInventoryInPlanLinesAssets();
                BeanUtils.copyProperties(stockInventoryInPlanLinesAsset, stockInventoryInPlanLinesAssets1);
                stockInventoryInPlanLinesAssets1.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
                stockInventoryInPlanLineList.forEach(stockInventoryInPlanLine -> {
                    if (stockInventoryInPlanLinesAsset.getSuppliesCode().equals(stockInventoryInPlanLine.getSuppliesCode())) {
                        stockInventoryInPlanLinesAssets1.setInventoryInPlanLineId(stockInventoryInPlanLine.getInventoryInPlanLineId());
                    }
                });
                stockInventoryInPlanLinesAssets.add(stockInventoryInPlanLinesAssets1);
            });
            Integer count = stockInventoryInPlanLineAssetsService.batchInsertStockInventoryPlanLineAssets(stockInventoryInPlanLinesAssets);
            if (count == null || count < 1) {
                logger.error("批量插入资产归还单行下资产表错误，资产归还单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo());
                throw new ServiceUncheckedException(ResponseCode.SYSTEM_ERROR.getMessage());
            }
        }

        //导入入库单行序列号
        List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsSet = new ArrayList<>(stockInventoryInPlanLinesSnsList.size());
        if (!CollectionUtils.isEmpty(stockInventoryInPlanLinesSnsList)) {
            stockInventoryInPlanLinesSnsList.forEach(dto -> {
                StockInventoryInPlanLinesSns stockInventoryInPlanLinesSns = new StockInventoryInPlanLinesSns();
                BeanUtils.copyProperties(dto, stockInventoryInPlanLinesSns);
                stockInventoryInPlanLinesSns.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
                stockInventoryInPlanLineList.forEach(stockInventoryInPlanLine -> {
                    if (dto.getSuppliesCode().equals(stockInventoryInPlanLine.getSuppliesCode())) {
                        stockInventoryInPlanLinesSns.setInventoryInPlanLineId(stockInventoryInPlanLine.getInventoryInPlanLineId());
                    }
                });
                stockInventoryInPlanLinesSnsSet.add(stockInventoryInPlanLinesSns);
            });
            Integer snsCount = stockInventoryInPlanLineSnsService.batchInsertStockInventoryPlanLineSns(stockInventoryInPlanLinesSnsSet);
            if (snsCount == null || snsCount < 1) {
                logger.error("批量插入资产归还单行下资产序列号表错误，资产归还单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo());
                throw new ServiceUncheckedException(ResponseCode.SYSTEM_ERROR.getMessage());
            }
        }

        return ResponseData.createSuccessResult(stockInventoryInPlanHead.getInventoryInPlanHeadId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePlanAssetRemandByService(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws Exception {
        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead ();
        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = new ArrayList<> ();
        List<StockInventoryInPlanLinesAssetsInfo> stockInventoryInPlanLinesAssetsList = new ArrayList<>();
        List<StockInventoryInPlanLinesSnsInfo> stockInventoryInPlanLinesSnsList = new ArrayList<>();
        // 保存所有资产的入库情况到记录表
        List<StockInventoryInAssetsConditions> stockInventoryInAssetsConditionList = new ArrayList<>();

        //准备插入参数
        prepareSaveDbBeanDTOByService (inventoryInPlanHeadReqDTO, stockInventoryInPlanHead, stockInventoryInPlanLineList, stockInventoryInPlanLinesAssetsList, stockInventoryInPlanLinesSnsList, stockInventoryInAssetsConditionList, user);
        ///插入计划入库单
        stockInventoryInPlanHeadService.insert (stockInventoryInPlanHead);
        // 存放物料和行id的关联关系
        Map<String, Long> suppliesCodeAndPlanLineIdMap = new HashMap<>();
        // 存放资产和资产行id的关联关系
        Map<String, Long> assetsCodeAndPlanAssetsIdMap = new HashMap<>();
        // 存放资产
        //导入计划入库单行
        if (!CollectionUtils.isEmpty (stockInventoryInPlanLineList)) {
            //插入入库单行信息
            stockInventoryInPlanLineList.forEach (stockInventoryInPlanLine -> stockInventoryInPlanLine.setInventoryInPlanHeadId (stockInventoryInPlanHead.getInventoryInPlanHeadId ()));
            Integer count = stockInventoryInPlanLineService.batchInsertStockInventoryPlanLine (stockInventoryInPlanLineList);
            if (count == null || count < 1) {
                logger.error ("批量插入资产归还单行错误，资产归还单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo ());
                throw new ServiceUncheckedException (ResponseCode.SYSTEM_ERROR.getMessage ());
            }
            // 设置行关联关系Map
            for (StockInventoryInPlanLine stockInventoryInPlanLine : stockInventoryInPlanLineList){
                suppliesCodeAndPlanLineIdMap.put(stockInventoryInPlanLine.getSuppliesCode(), stockInventoryInPlanLine.getInventoryInPlanLineId());
            }
        }
        //导入入库单行资产
        List<StockInventoryInPlanLinesAssets> stockInventoryInPlanLinesAssets = new ArrayList<>(stockInventoryInPlanLinesAssetsList.size());
        List<String> assetsCodes = new ArrayList<>(stockInventoryInPlanLinesAssetsList.size());
        if (!CollectionUtils.isEmpty(stockInventoryInPlanLinesAssetsList)){
            stockInventoryInPlanLinesAssetsList.forEach(stockInventoryInPlanLinesAsset->{
                StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets1 = new StockInventoryInPlanLinesAssets();
                BeanUtils.copyProperties(stockInventoryInPlanLinesAsset,stockInventoryInPlanLinesAssets1);
                stockInventoryInPlanLinesAssets1.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
                Long lineId = suppliesCodeAndPlanLineIdMap.get(stockInventoryInPlanLinesAsset.getSuppliesCode());
                if(lineId != null){
                    stockInventoryInPlanLinesAssets1.setInventoryInPlanLineId(lineId);
                }
                stockInventoryInPlanLinesAssets.add(stockInventoryInPlanLinesAssets1);
                assetsCodes.add(stockInventoryInPlanLinesAssets1.getAssetsCode());
            });
            Integer count = stockInventoryInPlanLineAssetsService.batchInsertStockInventoryPlanLineAssets(stockInventoryInPlanLinesAssets);
            if (count == null || count < 1) {
                logger.error ("批量插入资产归还单行下资产表错误，资产归还单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo ());
                throw new ServiceUncheckedException (ResponseCode.SYSTEM_ERROR.getMessage ());
            }
            for (StockInventoryInPlanLinesAssets inventoryInPlanLinesAssets : stockInventoryInPlanLinesAssets) {
                assetsCodeAndPlanAssetsIdMap.put(inventoryInPlanLinesAssets.getAssetsCode(), inventoryInPlanLinesAssets.getId());
            }
        }

        //导入入库单行序列号
        List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsSet = new ArrayList<>(stockInventoryInPlanLinesSnsList.size());
        if (!CollectionUtils.isEmpty(stockInventoryInPlanLinesSnsList)){
            stockInventoryInPlanLinesSnsList.forEach(dto->{
                StockInventoryInPlanLinesSns stockInventoryInPlanLinesSns = new StockInventoryInPlanLinesSns();
                BeanUtils.copyProperties(dto,stockInventoryInPlanLinesSns);
                stockInventoryInPlanLinesSns.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
                Long lineId = suppliesCodeAndPlanLineIdMap.get(dto.getSuppliesCode());
                if(lineId != null){
                    stockInventoryInPlanLinesSns.setInventoryInPlanLineId(lineId);
                }
                stockInventoryInPlanLinesSnsSet.add(stockInventoryInPlanLinesSns);
            });
            Integer snsCount = stockInventoryInPlanLineSnsService.batchInsertStockInventoryPlanLineSns(stockInventoryInPlanLinesSnsSet);
            if (snsCount == null || snsCount < 1) {
                logger.error ("批量插入资产归还单行下资产序列号表错误，资产归还单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo ());
                throw new ServiceUncheckedException (ResponseCode.SYSTEM_ERROR.getMessage ());
            }
        }

        // 导入设置的资产情况信息
        if(!CollectionUtils.isEmpty(stockInventoryInAssetsConditionList)){
            for (StockInventoryInAssetsConditions stockInventoryInAssetsConditions : stockInventoryInAssetsConditionList) {
                stockInventoryInAssetsConditions.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
                Long assetsLineId = assetsCodeAndPlanAssetsIdMap.get(stockInventoryInAssetsConditions.getAssetsCode());
                if(assetsLineId != null){
                    stockInventoryInAssetsConditions.setInventoryInPlanLineAssetsId(assetsLineId);
                }
            }
            Integer count = stockInventoryInAssetsConditionService.batchInsertAssetConditions(stockInventoryInAssetsConditionList);
            if (CommonConstant.NUMBER_ZERO.equals(count)) {
                logger.error("保存资产入库情况数据异常");
                throw new ServiceUncheckedException("保存资产入库情况数据异常");
            }
        }

        // 放入头单id
        inventoryInPlanHeadReqDTO.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
        // 入参的行信息也需要放入行id
        for (InventoryInPlanLineAssetReqDTO inventoryInPlanLineAssetReqDTO : inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS()) {
            Long planAssetsId = assetsCodeAndPlanAssetsIdMap.get(inventoryInPlanLineAssetReqDTO.getAssetsCode());
            if(planAssetsId != null){
                inventoryInPlanLineAssetReqDTO.setId(planAssetsId);
            }
        }
        // 直接进行入库操作
        stockInventoryInPlanHeadService.generateStockInventoryStronger(inventoryInPlanHeadReqDTO,user);
        // 这里防止hr临时修改离职人日期，导致不能更新资产在离职赔偿表中的归还状态
        StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO = new StockAssetsCompensationRecordReqDTO();
        stockAssetsCompensationRecordReqDTO.setCompensationUser(stockInventoryInPlanHead.getDutyUser());
        stockAssetsCompensationRecordReqDTO.setAssetsCodeList(assetsCodes);
        stockAssetsCompensationRecordReqDTO.setDelFlag(CommonConstant.NUMBER_ZERO);
        stockAssetsCompensationRecordReqDTO.setIsReturn(AssetsCompensationEnum.isReturn.NO_RETURN.getValue());
        List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList = stockAssetsCompensationRecordService.selectStockAssetsCompensationRecordList(stockAssetsCompensationRecordReqDTO);
        // 如果此人的资产在赔偿表中有数据，就给他更新赔偿表中的归还状态为是，归还仓库为选择的归还仓库，以及是否报废为否
        if(CollectionUtils.isEmpty(stockAssetsCompensationRecordList)){
            return;
        }
        Date currentDate = new Date();
        Date returnDate = DateUtils.dateParse(DateUtils.dateFormat(currentDate, DateUtils.DATE_PATTERN), DateUtils.DATE_PATTERN);
        // 如果赔偿表同时存在在职导入和系统生成或者审批的数据，只要不是归还状态，就都更新，因为只有上一次归还了，才能再次领用
        List<StockAssetsCompensationRecord> updateStockAssetsCompensationRecordList = new ArrayList<>();
        for (StockAssetsCompensationRecord stockAssetsCompensationRecord : stockAssetsCompensationRecordList) {
            stockAssetsCompensationRecord.setIsReturn(AssetsCompensationEnum.isReturn.YES_RETURN.getValue());
            stockAssetsCompensationRecord.setIsNeedScrap(AssetsCompensationEnum.isNeedScrap.NO_SCRAP.getValue());
            stockAssetsCompensationRecord.setReturnWarehouseCode(stockInventoryInPlanHead.getInWarehouseCode());
            stockAssetsCompensationRecord.setReturnTime(returnDate);
            stockAssetsCompensationRecord.setUpdatedAt(currentDate);
            stockAssetsCompensationRecord.setUpdatedBy(user.getEmployeeCode());
            updateStockAssetsCompensationRecordList.add(stockAssetsCompensationRecord);
        }
        // 批量更新
        stockAssetsCompensationRecordService.batchUpdate(updateStockAssetsCompensationRecordList);
    }

    /**
     * 赋值保存资产归还单
     *
     * @param inventoryInPlanHeadReqDTO
     * @param stockInventoryInPlanHead
     * @param stockInventoryInPlanLineList
     * @param stockInventoryInPlanLinesAssetsList
     * @param stockInventoryInPlanLinesSnsList
     * @param user
     */
    private void prepareSaveDbBeanDTO(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead, List<StockInventoryInPlanLine> stockInventoryInPlanLineList,
                                      List<StockInventoryInPlanLinesAssetsInfo> stockInventoryInPlanLinesAssetsList, List<StockInventoryInPlanLinesSnsInfo> stockInventoryInPlanLinesSnsList, JwtUser user) throws ParseException {
        stockInventoryInPlanHead.setStatus(InventoryInPlanHeadEnum.Status.WAIT_IN.getCode());
        stockInventoryInPlanHead.setCreatedBy(user.getEmployeeCode());
        stockInventoryInPlanHead.setInWarehouseCode(inventoryInPlanHeadReqDTO.getInWarehouseCode());
        stockInventoryInPlanHead.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.ASSET_REMAND.getCode());
        stockInventoryInPlanHead.setDeliveryNo(inventoryInPlanHeadReqDTO.getDeliveryNo());
        if (StringUtils.isBlank(inventoryInPlanHeadReqDTO.getPlanInTime())) {
            stockInventoryInPlanHead.setPlanInTime(new Date());
        } else {
            stockInventoryInPlanHead.setPlanInTime(DateUtils.dateParse(inventoryInPlanHeadReqDTO.getPlanInTime(), DateUtils.DATE_PATTERN));
        }
        if (inventoryInPlanHeadReqDTO.getBillingUser() != null) {
            stockInventoryInPlanHead.setBillingUser(inventoryInPlanHeadReqDTO.getBillingUser());
        } else {
            stockInventoryInPlanHead.setBillingUser(user.getEmployeeCode());
        }
        stockInventoryInPlanHead.setRemark(inventoryInPlanHeadReqDTO.getRemark());

        if (StringUtils.isBlank(inventoryInPlanHeadReqDTO.getBillingTime())) {
            stockInventoryInPlanHead.setBillingTime(new Date());
        } else {
            stockInventoryInPlanHead.setBillingTime(DateUtils.dateParse(inventoryInPlanHeadReqDTO.getBillingTime(), DateUtils.DATE_PATTERN));
        }

        stockInventoryInPlanHead.setReasonCode(inventoryInPlanHeadReqDTO.getReasonCode());
        stockInventoryInPlanHead.setDutyUser(inventoryInPlanHeadReqDTO.getDutyUser());
        stockInventoryInPlanHead.setUpdatedBy(user.getEmployeeCode());

        //计划入库单行
        List<AssetsDTO> stockAssetsList = stockAssetsService.selectAssetByHolder(inventoryInPlanHeadReqDTO.getDutyUser(), inventoryInPlanHeadReqDTO.getInWarehouseCode(), inventoryInPlanHeadReqDTO.getAssetsCodeList());
        if (!CollectionUtils.isEmpty(stockAssetsList)) {
            stockInventoryInPlanHead.setDutyDept(stockAssetsList.get(0).getHolderDept());
            stockInventoryInPlanHead.setDutyAddress(stockAssetsList.get(0).getHolderAddress());
        }
        Map<String, StockInventoryInPlanLine> stringListMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        for (AssetsDTO assetsDTO : stockAssetsList) {
            //创建入库单行下的资产表
            StockInventoryInPlanLinesAssetsInfo stockInventoryInPlanLinesAssets = new StockInventoryInPlanLinesAssetsInfo();
            stockInventoryInPlanLinesAssets.setAssetsCode(assetsDTO.getAssetsCode());
            stockInventoryInPlanLinesAssets.setDataStatus(CommonEnum.status.YES.getValue());
            stockInventoryInPlanLinesAssets.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.WAIT_IN.getCode());
            stockInventoryInPlanLinesAssets.setDelFlag(CommonEnum.status.YES.getValue());
            stockInventoryInPlanLinesAssets.setCreatedAt(new Date());
            stockInventoryInPlanLinesAssets.setCreatedBy(user.getEmployeeCode());
            stockInventoryInPlanLinesAssets.setUpdatedAt(new Date());
            stockInventoryInPlanLinesAssets.setUpdatedBy(user.getEmployeeCode());
            stockInventoryInPlanLinesAssets.setSuppliesCode(assetsDTO.getSuppliesCode());
            stockInventoryInPlanLinesAssetsList.add(stockInventoryInPlanLinesAssets);

            //创建入库单行下的序列号表
            if (StringUtils.isNotBlank(assetsDTO.getSnCode())) {
                StockInventoryInPlanLinesSnsInfo stockinventoryinplanlinessns = new StockInventoryInPlanLinesSnsInfo();
                stockinventoryinplanlinessns.setSnNo(assetsDTO.getSnCode());
                stockinventoryinplanlinessns.setDataStatus(CommonEnum.status.YES.getValue());
                stockinventoryinplanlinessns.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.WAIT_IN.getCode());
                stockinventoryinplanlinessns.setDelFlag(CommonEnum.status.YES.getValue());
                stockinventoryinplanlinessns.setCreatedAt(new Date());
                stockinventoryinplanlinessns.setCreatedBy(user.getEmployeeCode());
                stockinventoryinplanlinessns.setUpdatedAt(new Date());
                stockinventoryinplanlinessns.setUpdatedBy(user.getEmployeeCode());
                stockinventoryinplanlinessns.setSuppliesCode(assetsDTO.getSuppliesCode());
                stockInventoryInPlanLinesSnsList.add(stockinventoryinplanlinessns);
            }

            //创建入库单行表
            StockInventoryInPlanLine stockInventoryInPlanLine = null;
            if (null == stringListMap.get(assetsDTO.getSuppliesCode())) {
                stockInventoryInPlanLine = new StockInventoryInPlanLine();
                stockInventoryInPlanLine.setSuppliesCode(assetsDTO.getSuppliesCode());
                stockInventoryInPlanLine.setPlanInTime(new Date());
                stockInventoryInPlanLine.setNumber(1);
                stockInventoryInPlanLine.setStatus(InventoryInPlanLineEnum.Status.WAIT_IN.getStatus());
                stockInventoryInPlanLine.setCreatedAt(new Date());
                stockInventoryInPlanLine.setUpdatedAt(new Date());
                stockInventoryInPlanLine.setCreatedBy(user.getEmployeeCode());
                stockInventoryInPlanLine.setUpdatedBy(user.getEmployeeCode());
                stringListMap.put(assetsDTO.getSuppliesCode(), stockInventoryInPlanLine);
            } else {
                stockInventoryInPlanLine = stringListMap.get(assetsDTO.getSuppliesCode());
                stockInventoryInPlanLine.setNumber(stockInventoryInPlanLine.getNumber() + 1);
            }
        }
        stockInventoryInPlanLineList.addAll(stringListMap.values());
    }

    /**
     * 赋值保存资产归还单
     * @param inventoryInPlanHeadReqDTO
     * @param stockInventoryInPlanHead
     * @param stockInventoryInPlanLineList
     * @param stockInventoryInPlanLinesAssetsList
     * @param stockInventoryInPlanLinesSnsList
     * @param stockInventoryInAssetsConditionList
     * @param user
     */
    private void prepareSaveDbBeanDTOByService(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead, List<StockInventoryInPlanLine> stockInventoryInPlanLineList,
                                               List<StockInventoryInPlanLinesAssetsInfo> stockInventoryInPlanLinesAssetsList, List<StockInventoryInPlanLinesSnsInfo> stockInventoryInPlanLinesSnsList, List<StockInventoryInAssetsConditions> stockInventoryInAssetsConditionList, JwtUser user) throws ParseException {
        Date currentDate = new Date();
        stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.ALREADY_IN.getCode ());
        stockInventoryInPlanHead.setCreatedBy (user.getEmployeeCode ());
        stockInventoryInPlanHead.setInWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        stockInventoryInPlanHead.setInventoryInPlanType (inventoryInPlanHeadReqDTO.getInventoryInPlanType());
        stockInventoryInPlanHead.setDeliveryNo (inventoryInPlanHeadReqDTO.getDeliveryNo ());
        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getPlanInTime ())) {
            stockInventoryInPlanHead.setPlanInTime (currentDate);
        } else {
            stockInventoryInPlanHead.setPlanInTime (DateUtils.dateParse (inventoryInPlanHeadReqDTO.getPlanInTime (), DateUtils.DATE_PATTERN));
        }
        if (StringUtils.isNotBlank(inventoryInPlanHeadReqDTO.getBillingUser())) {
            stockInventoryInPlanHead.setBillingUser (inventoryInPlanHeadReqDTO.getBillingUser ());
        } else {
            stockInventoryInPlanHead.setBillingUser (user.getEmployeeCode ());
        }
        stockInventoryInPlanHead.setRemark (inventoryInPlanHeadReqDTO.getRemark ());

        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getBillingTime ())) {
            stockInventoryInPlanHead.setBillingTime (currentDate);
        } else {
            stockInventoryInPlanHead.setBillingTime (DateUtils.dateParse (inventoryInPlanHeadReqDTO.getBillingTime (), DateUtils.DATE_TIME_PATTERN));
        }

        stockInventoryInPlanHead.setReasonCode (inventoryInPlanHeadReqDTO.getReasonCode ());
        stockInventoryInPlanHead.setUpdatedBy (user.getEmployeeCode ());

        //计划入库单行
        List<InventoryInPlanLineAssetReqDTO> inventoryInPlanLineAssetReqDTOList = inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS();
        if(!CollectionUtils.isEmpty(inventoryInPlanLineAssetReqDTOList)){
            stockInventoryInPlanHead.setDutyUser (inventoryInPlanLineAssetReqDTOList.get(CommonConstant.NUMBER_ZERO).getHolder());
            stockInventoryInPlanHead.setDutyDept(inventoryInPlanLineAssetReqDTOList.get(CommonConstant.NUMBER_ZERO).getHolderDept());
            stockInventoryInPlanHead.setDutyAddress(inventoryInPlanLineAssetReqDTOList.get(CommonConstant.NUMBER_ZERO).getHolderAddress());
        }
        Map<String,StockInventoryInPlanLine> stringListMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        for(InventoryInPlanLineAssetReqDTO inventoryInPlanLineAssetReqDTO : inventoryInPlanLineAssetReqDTOList) {
            //创建入库单行下的资产表
            StockInventoryInPlanLinesAssetsInfo stockInventoryInPlanLinesAssets = new StockInventoryInPlanLinesAssetsInfo();
            stockInventoryInPlanLinesAssets.setAssetsCode(inventoryInPlanLineAssetReqDTO.getAssetsCode ());
            stockInventoryInPlanLinesAssets.setDataStatus(CommonEnum.status.YES.getValue());
            stockInventoryInPlanLinesAssets.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
            stockInventoryInPlanLinesAssets.setDelFlag(CommonEnum.status.YES.getValue());
            stockInventoryInPlanLinesAssets.setCreatedAt(currentDate);
            stockInventoryInPlanLinesAssets.setCreatedBy(user.getEmployeeCode());
            stockInventoryInPlanLinesAssets.setUpdatedAt(currentDate);
            stockInventoryInPlanLinesAssets.setUpdatedBy(user.getEmployeeCode());
            stockInventoryInPlanLinesAssets.setSuppliesCode(inventoryInPlanLineAssetReqDTO.getSuppliesCode());
            stockInventoryInPlanLinesAssetsList.add(stockInventoryInPlanLinesAssets);

            // 拼装资产情况信息
            StockInventoryInAssetsConditions stockInventoryInAssetsConditions = new StockInventoryInAssetsConditions();
            stockInventoryInAssetsConditions.setAmount(inventoryInPlanLineAssetReqDTO.getAmount() != null ? inventoryInPlanLineAssetReqDTO.getAmount() : new BigDecimal("0"));
            stockInventoryInAssetsConditions.setAssetsCode(inventoryInPlanLineAssetReqDTO.getAssetsCode());
            stockInventoryInAssetsConditions.setAssetsCondition(inventoryInPlanLineAssetReqDTO.getAssetsCondition());
            stockInventoryInAssetsConditions.setConditionStatus(CommonEnum.status.YES.getValue());
            stockInventoryInAssetsConditions.setCreatedAt(currentDate);
            stockInventoryInAssetsConditions.setCreatedBy(user.getEmployeeCode());
            stockInventoryInAssetsConditions.setDealType(inventoryInPlanLineAssetReqDTO.getDealType());
            stockInventoryInAssetsConditions.setDelFlag(CommonEnum.delFlag.YES.getValue());
            stockInventoryInAssetsConditions.setDisposeAmount(new BigDecimal("0"));
            stockInventoryInAssetsConditions.setDutyBody(inventoryInPlanLineAssetReqDTO.getDutyBody() != null ? inventoryInPlanLineAssetReqDTO.getDutyBody() : 0);
//            stockInventoryInAssetsConditions.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
//            stockInventoryInAssetsConditions.setInventoryInPlanLineAssetsId(dto.getId());
            stockInventoryInAssetsConditions.setNetValue(new BigDecimal("0"));
            stockInventoryInAssetsConditions.setPayStatus(InventoryInPlanLineAssetsEnum.PayStatus.NO_PAY.getCode());
            stockInventoryInAssetsConditions.setPersonLiable(StringUtils.isNotBlank(inventoryInPlanLineAssetReqDTO.getPersonLiable()) ? inventoryInPlanLineAssetReqDTO.getPersonLiable() : "");
            stockInventoryInAssetsConditions.setRealInWarehouse(inventoryInPlanLineAssetReqDTO.getRealInWarehouse());
            stockInventoryInAssetsConditions.setRemark(StringUtils.isNotBlank(inventoryInPlanLineAssetReqDTO.getRemark()) ? inventoryInPlanLineAssetReqDTO.getRemark() : "");
            stockInventoryInAssetsConditions.setUpdatedAt(currentDate);
            stockInventoryInAssetsConditions.setUpdatedBy(user.getEmployeeCode());
            stockInventoryInAssetsConditions.setRealInWarehouse(inventoryInPlanHeadReqDTO.getInWarehouseCode());
            stockInventoryInAssetsConditionList.add(stockInventoryInAssetsConditions);

            //创建入库单行下的序列号表
            if (StringUtils.isNotBlank(inventoryInPlanLineAssetReqDTO.getSnCode())){
                StockInventoryInPlanLinesSnsInfo stockinventoryinplanlinessns = new StockInventoryInPlanLinesSnsInfo();
                stockinventoryinplanlinessns.setSnNo(inventoryInPlanLineAssetReqDTO.getSnCode());
                stockinventoryinplanlinessns.setDataStatus(CommonEnum.status.YES.getValue());
                stockinventoryinplanlinessns.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
                stockinventoryinplanlinessns.setDelFlag(CommonEnum.status.YES.getValue());
                stockinventoryinplanlinessns.setCreatedAt(currentDate);
                stockinventoryinplanlinessns.setCreatedBy(user.getEmployeeCode());
                stockinventoryinplanlinessns.setUpdatedAt(currentDate);
                stockinventoryinplanlinessns.setUpdatedBy(user.getEmployeeCode());
                stockinventoryinplanlinessns.setSuppliesCode(inventoryInPlanLineAssetReqDTO.getSuppliesCode());
                stockInventoryInPlanLinesSnsList.add(stockinventoryinplanlinessns);
            }

            //创建入库单行表
            StockInventoryInPlanLine stockInventoryInPlanLine;
            if (null == stringListMap.get(inventoryInPlanLineAssetReqDTO.getSuppliesCode ())){
                stockInventoryInPlanLine = new StockInventoryInPlanLine();
                stockInventoryInPlanLine.setSuppliesCode (inventoryInPlanLineAssetReqDTO.getSuppliesCode ());
                stockInventoryInPlanLine.setPlanInTime (currentDate);
                stockInventoryInPlanLine.setNumber (CommonConstant.NUMBER_ONE);
                stockInventoryInPlanLine.setRealNumber (CommonConstant.NUMBER_ONE);
                stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
                stockInventoryInPlanLine.setCreatedAt(currentDate);
                stockInventoryInPlanLine.setUpdatedAt(currentDate);
                stockInventoryInPlanLine.setCreatedBy (user.getEmployeeCode ());
                stockInventoryInPlanLine.setUpdatedBy (user.getEmployeeCode ());
                stringListMap.put(inventoryInPlanLineAssetReqDTO.getSuppliesCode (),stockInventoryInPlanLine);
            }else{
                stockInventoryInPlanLine = stringListMap.get(inventoryInPlanLineAssetReqDTO.getSuppliesCode ());
                stockInventoryInPlanLine.setNumber(stockInventoryInPlanLine.getNumber() + CommonConstant.NUMBER_ONE);
                stockInventoryInPlanLine.setRealNumber (stockInventoryInPlanLine.getNumber());
            }
        }
        stockInventoryInPlanLineList.addAll(stringListMap.values());
    }

    /**
     * 校验
     *
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) {
        if (null == inventoryInPlanHeadReqDTO) {
            return "必填参数为空";
        }

        if (StringUtils.isBlank(inventoryInPlanHeadReqDTO.getInWarehouseCode())) {
            return "归还仓库不能为空";
        }

        //仓库权限查询
        List<String> wc = this.stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, inventoryInPlanHeadReqDTO.getInWarehouseCode());
        if (CollectionUtils.isEmpty(wc)) {
            return "无操作仓库的权限";
        }

        //仓库是否有效
        StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode(inventoryInPlanHeadReqDTO.getInWarehouseCode(), WarehouseEnum.Status.NORMAL.getStatus());
        if (null == stockWarehouse) {
            return "该仓库已禁用";
        }


        //仓库地址是否存在
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode(inventoryInPlanHeadReqDTO.getInWarehouseCode());
        if (null == stockWarehouseBase || StringUtils.isBlank(stockWarehouseBase.getAddress())) {
            return "找不到调入仓库对应的地址";
        }

        if (StringUtils.isBlank(inventoryInPlanHeadReqDTO.getPlanInTime())) {
            return "预计归还时间不能为空";
        }

        if (StringUtils.isBlank(inventoryInPlanHeadReqDTO.getDutyUser())) {
            return "持有人不能为空";
        }

        if (null == inventoryInPlanHeadReqDTO.getReasonCode()) {
            return "归还事由不能为空";
        }


        List<AssetsDTO> stockAssetsList = stockAssetsService.selectAssetByHolder(inventoryInPlanHeadReqDTO.getDutyUser(), inventoryInPlanHeadReqDTO.getInWarehouseCode());
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            return "当前选择的持有人没有可以归还到仓库" + inventoryInPlanHeadReqDTO.getInWarehouseCode() + "的资产";
        }
        //校验归还的资产是否是使用中
        List<String> assetsCodeList = inventoryInPlanHeadReqDTO.getAssetsCodeList();
        for (AssetsDTO assetsDTO : stockAssetsList) {
            if (assetsCodeList.contains(assetsDTO.getAssetsCode())) {
                if (AssetsEnum.Conditions.TRANSFER.getValue().equals(assetsDTO.getConditions())){
                    return "资产编码"+assetsDTO.getAssetsCode()+"正在转移审批中，无法归还";
                }
                if(!MetaDataEnum.yesOrNo.NO.getValue().equals(assetsDTO.getApproveStatus())){
                    return "资产编码"+assetsDTO.getAssetsCode()+"正在报废/IT工单审批中或者已经报废，无法归还";
                }
            }
        }

        return null;
    }

    @Override
    public ResponseData selectPlanAssetRemand(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO, JwtUser user) {
        inventoryInPlanSearchReqDTO.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.ASSET_REMAND.getCode());

        return stockInventoryInPlanHeadService.selectInventoryInPlan(inventoryInPlanSearchReqDTO, user);
    }

    @Override
    public ResponseData selectPlanAssetRemandById(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user) {
        return stockInventoryInPlanHeadService.selectInventoryInPlanById(inventoryInPlanLineReqDTO, user, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData assetPlanAssetRemandInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws InvocationTargetException, IllegalAccessException, ParseException {

        //1。校验数据（包括申请单头数据，入库的行下资产数据，仓库权限）
        String checkInBoundParam = checkRemandInBoundParam(inventoryInPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank(checkInBoundParam)) {
            return ResponseData.createFailResult(checkInBoundParam);
        }
        //2。保存所有资产的入库情况到记录表
        List<StockInventoryInAssetsConditions> stockInventoryInAssetsConditionList = new ArrayList<>();
        prepareLineAssetConditionData(inventoryInPlanHeadReqDTO, stockInventoryInAssetsConditionList, user);
        Integer count = stockInventoryInAssetsConditionService.batchInsertAssetConditions(stockInventoryInAssetsConditionList);
        if (CommonConstant.NUMBER_ZERO.equals(count)) {
            logger.error("保存资产入库情况数据异常");
            throw new ServiceUncheckedException("保存资产入库情况数据异常");
        }
        //3。更新申请单头表，行表，行资产表状态
        List<Long> ids = inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().stream().map(dto -> dto.getId()).collect(Collectors.toList());
        stockInventoryInPlanLineAssetsService.batchUpdateStockInventoryPlanLineAssets(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode(), user.getEmployeeCode(), ids);

        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = stockInventoryInPlanLineService.selectLinesGroupByIdAndStatus(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
        ;
        if (CollectionUtils.isEmpty(stockInventoryInPlanLineList)) {
            logger.error("查询已入库的资产数据异常");
            throw new ServiceUncheckedException("查询已入库的资产数据异常");
        }
        stockInventoryInPlanLineList.stream().forEach(dto -> {
            if (dto.getNumber().equals(dto.getRealNumber())) {
                dto.setStatus(InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus());
            } else if (dto.getRealNumber() != null && dto.getNumber().intValue() > dto.getRealNumber().intValue()) {
                dto.setStatus(InventoryInPlanLineEnum.Status.SECTION_IN.getStatus());
            }
            dto.setUpdatedAt(new Date());
            dto.setUpdatedBy(user.getEmployeeCode());
            stockInventoryInPlanLineService.updatePrimaryKeySelective(dto);
        });

        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead();
        stockInventoryInPlanHead.setUpdatedBy(user.getEmployeeCode());
        stockInventoryInPlanHead.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
        List<StockInventoryInPlanLine> inventoryInPlanLines = stockInventoryInPlanLineService.selectNotAlreadyIn(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
        if (CollectionUtils.isEmpty(inventoryInPlanLines)) {
            stockInventoryInPlanHead.setStatus(InventoryInPlanHeadEnum.Status.ALREADY_IN.getCode());
        } else {
            stockInventoryInPlanHead.setStatus(InventoryInPlanHeadEnum.Status.SECTION_IN.getCode());
        }
        // 更新计划资产归还单
        Boolean updateHeadResult = stockInventoryInPlanHeadService.updatePrimaryKeySelective(stockInventoryInPlanHead);
        if (!updateHeadResult) {
            logger.error("更新计划资产归还单错误, stockInventoryInPlanHead:{}", stockInventoryInPlanHead.toString());
            throw new ServiceUncheckedException("更新计划资产归还单错误");
        }

        //4。生成入库单，库存信息
        stockInventoryInPlanHeadService.generateStockInventoryStronger(inventoryInPlanHeadReqDTO, user);

        //5。选择性更新资产状态
        List<String> assetsCodes = inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().stream().map(dto -> dto.getAssetsCode()).collect(Collectors.toList());
        Map<String, StockAssets> stringStockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodes);

        List<StockAssets> noNormalInventoryInAssetList = new ArrayList<>();
        inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().stream().forEach(dto -> {
            StockAssets stockAssets = stringStockAssetsMap.get(dto.getAssetsCode());
            stockAssets.setUpdatedAt(new Date());
            stockAssets.setUpdatedBy(user.getEmployeeCode());
            if (!InventoryInPlanLineAssetsEnum.Condition.NORMLAL.getCode().equals(dto.getAssetsCondition()) && InventoryInPlanLineAssetsEnum.DealType.NORMLAL.getCode().equals(dto.getDealType())) {
                stockAssets.setConditions(AssetsEnum.Conditions.BREAK.getValue());
                stockAssets.setStatus(AssetsEnum.statusType.IDLE.getValue());
                stockAssets.setHolder("");
                noNormalInventoryInAssetList.add(stockAssets);
            } else if (InventoryInPlanLineAssetsEnum.DealType.IN_DISCAERD.getCode().equals(dto.getDealType()) || InventoryInPlanLineAssetsEnum.DealType.DISCAERD.getCode().equals(dto.getDealType())) {
                stockAssets.setConditions(AssetsEnum.Conditions.WAIT_DISCAERD.getValue());
                stockAssets.setStatus(AssetsEnum.statusType.IDLE.getValue());
                stockAssets.setHolder("");
                noNormalInventoryInAssetList.add(stockAssets);
            } else if (InventoryInPlanLineAssetsEnum.DealType.WAIT_REPAIR.getCode().equals(dto.getDealType())) {
                stockAssets.setConditions(AssetsEnum.Conditions.WAIT_REPAIR.getValue());
                stockAssets.setStatus(AssetsEnum.statusType.IDLE.getValue());
                stockAssets.setHolder("");
                noNormalInventoryInAssetList.add(stockAssets);
            }
        });
        if (!CollectionUtils.isEmpty(noNormalInventoryInAssetList)) {
            stockAssetsService.updateMultipleSelective(noNormalInventoryInAssetList);
        }

//        //6。选择性进行资产的处置出库
//        stockDeliveryPlanHeadService.generateStockDelivery(inventoryInPlanHeadReqDTO,user);
        StockInventoryInPlanHead newStockInventoryInPlanHead = stockInventoryInPlanHeadService.selectById(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
        StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO = new StockAssetsCompensationRecordReqDTO();
        stockAssetsCompensationRecordReqDTO.setCompensationUser(newStockInventoryInPlanHead.getDutyUser());
        stockAssetsCompensationRecordReqDTO.setAssetsCodeList(assetsCodes);
        stockAssetsCompensationRecordReqDTO.setDelFlag(CommonConstant.NUMBER_ZERO);
        stockAssetsCompensationRecordReqDTO.setIsReturn(AssetsCompensationEnum.isReturn.NO_RETURN.getValue());
        List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList = stockAssetsCompensationRecordService.selectStockAssetsCompensationRecordList(stockAssetsCompensationRecordReqDTO);
        // 7。如果此人的资产在赔偿表中有数据，就给他更新赔偿表中的归还状态为是，归还仓库为选择的归还仓库，以及是否报废为否
        if(CollectionUtils.isEmpty(stockAssetsCompensationRecordList)){
            return ResponseData.createSuccessResult();
        }
        // 如果赔偿表同时存在在职导入和系统生成或者审批的数据，只要不是归还状态，就都更新，因为只有上一次归还了，才能再次领用
        List<StockAssetsCompensationRecord> updateStockAssetsCompensationRecordList = new ArrayList<>();
        for (StockAssetsCompensationRecord stockAssetsCompensationRecord : stockAssetsCompensationRecordList) {
            Date currentDate = new Date();
            stockAssetsCompensationRecord.setIsReturn(AssetsCompensationEnum.isReturn.YES_RETURN.getValue());
            stockAssetsCompensationRecord.setIsNeedScrap(AssetsCompensationEnum.isNeedScrap.NO_SCRAP.getValue());
            stockAssetsCompensationRecord.setReturnWarehouseCode(newStockInventoryInPlanHead.getInWarehouseCode());
            stockAssetsCompensationRecord.setReturnTime(DateUtils.dateParse(DateUtils.dateFormat(currentDate, DateUtils.DATE_PATTERN), DateUtils.DATE_PATTERN));
            stockAssetsCompensationRecord.setUpdatedAt(currentDate);
            stockAssetsCompensationRecord.setUpdatedBy(user.getEmployeeCode());
            updateStockAssetsCompensationRecordList.add(stockAssetsCompensationRecord);
        }
        // 批量更新
        stockAssetsCompensationRecordService.batchUpdate(updateStockAssetsCompensationRecordList);
        return ResponseData.createSuccessResult();
    }

    /**
     * 归还入库组装参数
     *
     * @param inventoryInPlanHeadReqDTO
     * @param stockInventoryInAssetsConditionList
     * @param user
     */
    private void prepareLineAssetConditionData(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, List<StockInventoryInAssetsConditions> stockInventoryInAssetsConditionList, JwtUser user) {
        inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().stream().forEach(dto -> {
            StockInventoryInAssetsConditions stockInventoryInAssetsConditions = new StockInventoryInAssetsConditions();
            stockInventoryInAssetsConditions.setAmount(dto.getAmount() != null ? dto.getAmount() : new BigDecimal("0"));
            stockInventoryInAssetsConditions.setAssetsCode(dto.getAssetsCode());
            stockInventoryInAssetsConditions.setAssetsCondition(dto.getAssetsCondition());
            stockInventoryInAssetsConditions.setConditionStatus(CommonEnum.status.YES.getValue());
            stockInventoryInAssetsConditions.setCreatedAt(new Date());
            stockInventoryInAssetsConditions.setCreatedBy(user.getEmployeeCode());
            stockInventoryInAssetsConditions.setDealType(dto.getDealType());
            stockInventoryInAssetsConditions.setDelFlag(CommonEnum.delFlag.YES.getValue());
            stockInventoryInAssetsConditions.setDisposeAmount(new BigDecimal("0"));
            stockInventoryInAssetsConditions.setDutyBody(dto.getDutyBody() != null ? dto.getDutyBody() : 0);
            stockInventoryInAssetsConditions.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
            stockInventoryInAssetsConditions.setInventoryInPlanLineAssetsId(dto.getId());
            stockInventoryInAssetsConditions.setNetValue(new BigDecimal("0"));
            stockInventoryInAssetsConditions.setPayStatus(InventoryInPlanLineAssetsEnum.PayStatus.NO_PAY.getCode());
            stockInventoryInAssetsConditions.setPersonLiable(StringUtils.isNotBlank(dto.getPersonLiable()) ? dto.getPersonLiable() : "");
            stockInventoryInAssetsConditions.setRealInWarehouse(StringUtils.isNotBlank(dto.getRealInWarehouse()) ? dto.getRealInWarehouse() : inventoryInPlanHeadReqDTO.getInWarehouseCode());
            stockInventoryInAssetsConditions.setRemark(StringUtils.isNotBlank(dto.getRemark()) ? dto.getRemark() : "");
            stockInventoryInAssetsConditions.setUpdatedAt(new Date());
            stockInventoryInAssetsConditions.setUpdatedBy(user.getEmployeeCode());
            stockInventoryInAssetsConditionList.add(stockInventoryInAssetsConditions);
        });
    }

    @Override
    public ResponseData assetPlanAssetRemandPrint(Long inventoryInPlanHeadId, JwtUser user) {
        if (inventoryInPlanHeadId == null) {
            return ResponseData.createResult(ResponseCode.PARAMETER_ERROR);
        }
        //查询入库单
        InventoryInPlanHeadRespDTO inventoryInPlanHeadRespDTO = stockInventoryInPlanHeadService.selectByInventoryInPlanHeadId(inventoryInPlanHeadId);
        if (null == inventoryInPlanHeadRespDTO) {
            return ResponseData.createFailResult("该计划入库单不存在");
        }
        //授权查询
        List<String> employeeWs = this.stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, null);
        Boolean roleResult = CollectionUtils.isEmpty(employeeWs)
                || (!employeeWs.get(0).equals(ManageRoleEnum.Type.ALL.getValue()) && !employeeWs.contains(inventoryInPlanHeadRespDTO.getInWarehouseCode()));
        if (roleResult) {
            return ResponseData.createFailResult("权限不足");
        }

        if (inventoryInPlanHeadRespDTO.getReasonCode() != null) {
            inventoryInPlanHeadRespDTO.setReasonTypeName(InventoryInPlanHeadEnum.Reason.fromCode(inventoryInPlanHeadRespDTO.getReasonCode()).getValue());
        }

       /* StockInventoryInPlanLine stockInventoryInPlanLine = new StockInventoryInPlanLine ();
        stockInventoryInPlanLine.setInventoryInPlanHeadId (inventoryInPlanHeadId);
        stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
        List<InventoryPlanAssetRespDTO> inventoryInPlanLineRespDTOList = stockInventoryInPlanLineService.selectInventoryPlanAssetRespDTOBySelective (stockInventoryInPlanLine);*/
        StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets = new StockInventoryInPlanLinesAssets();
        stockInventoryInPlanLinesAssets.setInventoryInPlanHeadId(inventoryInPlanHeadId);
        stockInventoryInPlanLinesAssets.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
        List<InventoryPlanAssetRespDTO> inventoryInPlanLineRespDTOList = stockInventoryInPlanLineAssetsService.selectInventoryPlanAssetRespDTO(stockInventoryInPlanLinesAssets);
        inventoryInPlanHeadRespDTO.setInventoryPlanAssetRespDTOS(inventoryInPlanLineRespDTOList);
        return ResponseData.createSuccessResult(inventoryInPlanHeadRespDTO);
    }


    @Override
    public ResponseData selectAssetByRemand(String holder, String remandWarehouseCode, String assetsCode, Integer pageNum, Integer pageSize) {
        if (StringUtils.isBlank(holder) || StringUtils.isBlank(remandWarehouseCode)) {
            return ResponseData.createSuccessResult();
        }
        Map<String, Object> resultMap = new HashMap<>();
        List<AssetsDTO> stockAssets = stockAssetsService.selectAssetByHolder(holder, remandWarehouseCode, assetsCode, pageNum, pageSize);
        Long count = assetsMapper.countAssetByHolder(holder, remandWarehouseCode);
        resultMap.put("data", stockAssets);
        resultMap.put("count", count);

        return ResponseData.createSuccessResult(resultMap);
    }

    @Override
    public ResponseData assetRemandInitialization(Integer batchId, JwtUser user) {
        if (null == batchId) {
            return ResponseData.createFailResult("必填参不能为空");
        }
        //生成资产采购计划入库单
        ///插入计划入库单
        StockAssetsInitialization stockAssetsInitialization = new StockAssetsInitialization();

        stockAssetsInitialization.setBatchId(batchId);
        stockAssetsInitialization.setType(AssetsInitializationEnum.type.REMAND.getValue());
        stockAssetsInitialization.setDeleteFlag(AssetsInitializationEnum.deleteFlag.NO.getValue());

        List<StockAssetsInitializationInfo> warehouseCodeList = stockAssetsInitializationService.selectWarehouseCodeByBatchId(stockAssetsInitialization);
        if (CollectionUtils.isEmpty(warehouseCodeList)) {
            return ResponseData.createFailResult("未找到本地需要导入的仓库数据");
        }
        for (StockAssetsInitializationInfo stockAssetsInitializationInfo : warehouseCodeList) {

            stockAssetsInitialization.setWarehouseCode(stockAssetsInitializationInfo.getWarehouseCode());
            logger.info("开始初始仓库:{} 的数据", stockAssetsInitializationInfo.getWarehouseCode());
            String assetRemandInitializationResult = stockInventoryInPlanHeadService.assetRemandInitialization(user, stockAssetsInitialization);
            if (StringUtils.isNotBlank(assetRemandInitializationResult)) {
                logger.error("初始仓库:{} 的数据错误,{}:", stockAssetsInitializationInfo.getWarehouseCode(), assetRemandInitializationResult);
            }

            stockAssetsInitializationService.updateDelFlagByParam(stockAssetsInitializationInfo);
            logger.info("初始仓库:{} 的数据结束", stockAssetsInitializationInfo.getWarehouseCode());
        }
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData cancelPlanAssetRemandById(Long inventoryInPlanHeadId) {
        return stockInventoryInPlanHeadService.cancelInventoryInPlanById(inventoryInPlanHeadId);
    }

    private void prepareAssetRemandInBoundDbBeanByDTO(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, List<StockInventoryInPlanLine> stockInventoryInPlanLineList, JwtUser user) {
        //入库单是否是已入库
        if (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS() != null && !inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS().isEmpty()) {

            for (InventoryInPlanLineReqDTO lineReqDTO : inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS()) {

                StockInventoryInPlanLine stockInventoryInPlanLine = new StockInventoryInPlanLine();
                stockInventoryInPlanLine.setAssetsCode(lineReqDTO.getAssetsCode());
                stockInventoryInPlanLine.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
                stockInventoryInPlanLine.setRealNumber(1);
                stockInventoryInPlanLine.setStatus(InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus());
                stockInventoryInPlanLine.setUpdatedBy(user.getEmployeeCode());
                stockInventoryInPlanLineList.add(stockInventoryInPlanLine);

                StockInventoryInPlanLine stockInventoryInPlanLine1 = new StockInventoryInPlanLine();
                stockInventoryInPlanLine1.setAssetsCode(lineReqDTO.getAssetsCode());
                stockInventoryInPlanLine1.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
                List<InventoryInPlanLineRespDTO> inventoryInPlanLineRespDTOList = stockInventoryInPlanLineService.selectBySelective(stockInventoryInPlanLine1);
                lineReqDTO.setInventoryInPlanLineId(inventoryInPlanLineRespDTOList.get(0).getInventoryInPlanLineId());
                lineReqDTO.setThisNumber(1);
                List<String> assets = new ArrayList<>();
                assets.add(lineReqDTO.getAssetsCode());
                lineReqDTO.setAssets(assets);

            }
        }
    }

    /**
     * 校验归还单
     *
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    private String checkRemandInBoundParam(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) {
        if (null == inventoryInPlanHeadReqDTO || null == inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId() || CollectionUtils.isEmpty(inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS())) {
            return "必填参数为空";
        }

        StockInventoryInPlanHead stockInventoryInPlanHead = stockInventoryInPlanHeadService.selectById(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
        if (null == stockInventoryInPlanHead) {
            return "该资产归还单不存在";
        }

        //将默认入库仓库赋值，后面使用
        inventoryInPlanHeadReqDTO.setInWarehouseCode(stockInventoryInPlanHead.getInWarehouseCode());

        //仓库权限查询
        List<String> inWareHouse = new ArrayList<>();
        inWareHouse.add(inventoryInPlanHeadReqDTO.getInWarehouseCode());
        inWareHouse.addAll(inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().stream().filter(dto -> StringUtils.isNotBlank(dto.getRealInWarehouse())).map(dto -> dto.getRealInWarehouse()).collect(Collectors.toList()));
        List<String> employeeWc = stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, null);
        Boolean roleResult = CollectionUtils.isEmpty(employeeWc)
                || (!employeeWc.get(0).equals(ManageRoleEnum.Type.ALL.getValue())
                && !employeeWc.contains(stockInventoryInPlanHead.getInWarehouseCode()));
        if (roleResult) {
            logger.info("资产归还出错，权限不足");
            throw new ServiceUncheckedException("如下仓库权限不足：" + inWareHouse.removeAll(employeeWc));
        }
        //校验仓库存在且有效
        List<WarehouseRespDTO> warehouseRespDTOS = stockWarehouseService.selectWarehouseDetailByCode(inWareHouse);
        if (CollectionUtils.isEmpty(warehouseRespDTOS)) {
            logger.info("资产归还错误，没有查询到归还仓库");
            throw new ServiceUncheckedException("资产归还错误，没有查询到归还仓库");
        } else {
            for (WarehouseRespDTO warehouseRespDTO : warehouseRespDTOS) {
                if (WarehouseEnum.Status.FORBID.getStatus().equals(warehouseRespDTO.getStatus())) {
                    return "该仓库已经被禁用：" + warehouseRespDTO.getCode();
                }
            }
        }

        //校验入库单详细
        List<AssetsDTO> assetsDTOList = stockAssetsService.selectAssetByHolder(stockInventoryInPlanHead.getDutyUser(), stockInventoryInPlanHead.getInWarehouseCode());
        if (CollectionUtils.isEmpty(assetsDTOList)) {
            return "当前选择的持有人没有可以归还到仓库" + stockInventoryInPlanHead.getInWarehouseCode() + "的资产";
        }
        StringBuilder sb = new StringBuilder();
        List<String> assetCodes = new ArrayList<>();
        for (int i = 0; i < inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().size(); i++) {

            InventoryInPlanLineAssetReqDTO inventoryInPlanLineAssetReqDTO = inventoryInPlanHeadReqDTO.getInventoryInPlanLineAssetReqDTOS().get(i);

            if (StringUtils.isBlank(inventoryInPlanLineAssetReqDTO.getAssetsCode())) {
                sb.append("第" + i + 1 + "行条资产编号为空");
                continue;
            } else if (assetCodes.contains(inventoryInPlanLineAssetReqDTO.getAssetsCode())) {
                sb.append("第" + i + 1 + "行条资产编号重复");
                continue;
            } else {
                assetCodes.add(inventoryInPlanLineAssetReqDTO.getAssetsCode());
            }

            StockAssets stockAssets = stockAssetsService.selectAssetsByCode(inventoryInPlanLineAssetReqDTO.getAssetsCode());
            if (null == stockAssets) {
                sb.append("第" + i + 1 + "行条资产不存在");
                continue;
            }
            // 验证资产是否在转移中
            if(AssetsEnum.Conditions.TRANSFER.getValue().equals(stockAssets.getConditions())){
                sb.append("第" + i + 1 + "行条资产在转移审批中，无法归还");
                continue;
            }
            // 验证资产是否在报废/IT工单审批中
            if(!MetaDataEnum.yesOrNo.NO.getValue().equals(stockAssets.getApproveStatus())){
                sb.append("第" + i + 1 + "行条资产在报废/IT工单审批中或者已经报废，无法归还");
                continue;
            }

            if (!stockAssets.getStatus().equals(AssetsEnum.statusType.USED.getValue()) || !stockAssets.getHolder().equals(stockInventoryInPlanHead.getDutyUser())) {
                sb.append("第" + i + 1 + "行条资产不属于当前持有人");
                continue;
            }

            StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets = new StockInventoryInPlanLinesAssets();
            stockInventoryInPlanLinesAssets.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
            stockInventoryInPlanLinesAssets.setAssetsCode(inventoryInPlanLineAssetReqDTO.getAssetsCode());
            List<InventoryPlanAssetRespDTO> inventoryPlanAssetRespDTOS = stockInventoryInPlanLineAssetsService.selectInventoryPlanAssetRespDTO(stockInventoryInPlanLinesAssets);
            if (CollectionUtils.isEmpty(inventoryPlanAssetRespDTOS)) {
                sb.append("第" + i + 1 + "行条资产编号和当前头不匹配");
                continue;
            }

            if (inventoryPlanAssetRespDTOS.get(0).getStatus().equals(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode())) {
                sb.append("第" + i + 1 + "行状态为已入库不可重复操作");
                continue;
            }

            String validResult = checkRemandInBoundLineParam(inventoryInPlanLineAssetReqDTO);
            if (StringUtils.isNotBlank(validResult)) {
                sb.append(validResult);
            }

        }

        if (sb.length() > 0) {
            return sb.toString();
        }
        return null;
    }

    private String checkRemandInBoundLineParam(InventoryInPlanLineAssetReqDTO inventoryInPlanLineAssetReqDTO) {
        //"资产情况"和"处理方式"不能为空
        if (null == inventoryInPlanLineAssetReqDTO.getAssetsCondition()) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "的资产情况不能为空值";
        }
        if (null == inventoryInPlanLineAssetReqDTO.getDealType()) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "的处理方式不能为空值";
        }
        //资产情况为有损入库时，处理方式不能为直接报废
        if (InventoryInPlanLineAssetsEnum.Condition.DAMAGE.getCode().equals(inventoryInPlanLineAssetReqDTO.getAssetsCondition()) &&
                InventoryInPlanLineAssetsEnum.DealType.DISCAERD.getCode().equals(inventoryInPlanLineAssetReqDTO.getDealType())) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "为有损入库，处理方式不可以直接报废";
        }
        //资产情况为丢失时，处理方式必须为直接报废
        if (InventoryInPlanLineAssetsEnum.Condition.LOSE.getCode().equals(inventoryInPlanLineAssetReqDTO.getAssetsCondition()) &&
                !InventoryInPlanLineAssetsEnum.DealType.DISCAERD.getCode().equals(inventoryInPlanLineAssetReqDTO.getDealType())) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "为丢失或赔偿时，处理方式必须为直接报废";
        }
        //资产情况不等于正常时，责任主体不能为空
        if (!InventoryInPlanLineAssetsEnum.Condition.NORMLAL.getCode().equals(inventoryInPlanLineAssetReqDTO.getAssetsCondition()) &&
                inventoryInPlanLineAssetReqDTO.getDutyBody() == null) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "为非正常入库，责任主体不能为空";
        }
        //责任主体为个人时，责任人不能为空
        if (InventoryInPlanLineAssetsEnum.DutyBody.PERSONAL.getCode().equals(inventoryInPlanLineAssetReqDTO.getDutyBody()) &&
                StringUtils.isBlank(inventoryInPlanLineAssetReqDTO.getPersonLiable())) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "责任主体为个人，责任人不能为空";
        }
        //当责任主体为个人时，金额不能为空
        if (InventoryInPlanLineAssetsEnum.DutyBody.PERSONAL.getCode().equals(inventoryInPlanLineAssetReqDTO.getDutyBody()) &&
                (inventoryInPlanLineAssetReqDTO.getAmount() == null || inventoryInPlanLineAssetReqDTO.getAmount().compareTo(BigDecimal.ZERO) == 0)) {
            return "资产编码：" + inventoryInPlanLineAssetReqDTO.getAssetsCode() + "责任主体为个人，要有赔偿金额";
        }
        return null;
    }
}
