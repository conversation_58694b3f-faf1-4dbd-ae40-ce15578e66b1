package com.gz.eim.am.stock.service.impl.authority;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.authority.StockManageRoleExtendMapper;
import com.gz.eim.am.stock.dao.base.StockManageRoleMapper;
import com.gz.eim.am.stock.dto.request.authority.StockManageRoleReqDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.authority.StockManageRoleRespDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.vo.StockManageRoleImportExcel;
import com.gz.eim.am.stock.entity.vo.StockRoleKeeperImportExcel;
import com.gz.eim.am.stock.entity.vo.StockRoleWarehouseImportExcel;
import com.gz.eim.am.stock.service.authority.StockManageRoleUpgradeService;
import com.gz.eim.am.stock.util.em.MetaDataEnum;
import com.gz.eim.am.stock.util.em.StockAuthorityEnum;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/5/17
 * @description
 */
@Service
@Slf4j
public class StockManageRoleUpgradeServiceImpl implements StockManageRoleUpgradeService {

    @Autowired
    private StockManageRoleMapper stockManageRoleMapper;

    @Autowired
    private StockManageRoleExtendMapper stockManageRoleExtendMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData save(StockManageRoleReqDTO stockManageRoleReqDTO, JwtUser user) {
        //新建校验参数
        if(stockManageRoleReqDTO.getRoleId() == null){
            String checkSaveParam = checkSaveParam (stockManageRoleReqDTO, user);
            if (StringUtils.isNotBlank (checkSaveParam)) {
                return ResponseData.createFailResult (checkSaveParam);
            }
        }else{
            //修改校验参数
            if(StringUtils.isBlank(stockManageRoleReqDTO.getEndDate())){
                return ResponseData.createFailResult ("要修改的角色止期不能为空；");
            }
        }
        log.info("资产转移保存校验参数已通过》》");
        //日期格式转换
        Date startDate = null;
        Date endDate = null;
        try{
            startDate = DateUtils.dateParse(stockManageRoleReqDTO.getStartDate(),DateUtils.DATE_PATTERN);
            endDate = DateUtils.dateParse(stockManageRoleReqDTO.getEndDate(),DateUtils.DATE_PATTERN);
        }catch (ParseException e){
            e.printStackTrace();
        }
        //修改操作
        if(stockManageRoleReqDTO.getRoleId() !=null && !"".equals(stockManageRoleReqDTO.getRoleId())){
            StockManageRole stockManageRole = new StockManageRole();
            stockManageRole.setUpdatedBy(user.getEmployeeCode());
            stockManageRole.setUpdatedAt(new Date());
            stockManageRole.setEndDate(endDate);

            StockManageRoleExample stockManageRoleExample = new StockManageRoleExample();
            StockManageRoleExample.Criteria criteria = stockManageRoleExample.createCriteria();
            criteria.andRoleIdEqualTo(stockManageRoleReqDTO.getRoleId());
            stockManageRoleMapper.updateByExampleSelective(stockManageRole,stockManageRoleExample);

        }else{
            //新增操作
            //保存到角色表
            StockManageRole stockManageRole = new StockManageRole();
            stockManageRole.setRoleType(stockManageRoleReqDTO.getRoleType() != null ? stockManageRoleReqDTO.getRoleType() : 0);
            stockManageRole.setWarehouseCode(stockManageRoleReqDTO.getWarehouseCode() !=null ? stockManageRoleReqDTO.getWarehouseCode() : "");
            stockManageRole.setRoleName(stockManageRoleReqDTO.getRoleName());
            stockManageRole.setCreatedAt(new Date());
            stockManageRole.setCreatedBy(user.getEmployeeCode());
            stockManageRole.setDelFlag(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
            stockManageRole.setStatus(StockAuthorityEnum.Status.VALID.getCode());
            stockManageRole.setEndDate(endDate);
            stockManageRole.setStartDate(startDate);
            stockManageRole.setUpdatedAt(new Date());
            stockManageRole.setUpdatedBy(user.getEmployeeCode());
            stockManageRoleMapper.insert(stockManageRole);
        }

        return ResponseData.createSuccessResult ();
    }

    @Override
    public ResponseData selectManageRoles(StockManageRoleReqDTO stockManageRoleReqDTO) {
        //获取总条数
        StockManageRoleExample stockManageRoleExample = new StockManageRoleExample();
        StockManageRoleExample.Criteria criteria = stockManageRoleExample.createCriteria();
        if (StringUtils.isNotBlank(stockManageRoleReqDTO.getRoleName())){
            criteria.andRoleNameLike("%"+stockManageRoleReqDTO.getRoleName()+"%");
        }
        long count = stockManageRoleMapper.countByExample(stockManageRoleExample);
        //设置分页数据
        stockManageRoleReqDTO.initPageParam();
        stockManageRoleExample.setLimit(stockManageRoleReqDTO.getPageSize());
        stockManageRoleExample.setOffset(stockManageRoleReqDTO.getStartNum());
        String orderByClause = " role_id desc";
        if (StringUtils.isNotBlank(stockManageRoleReqDTO.getSortColumns())) {
            orderByClause = stockManageRoleReqDTO.getSortColumns();
        }
        stockManageRoleExample.setOrderByClause(orderByClause);
        //分页查询获取数据列表
        List<StockManageRole> stockManageRoles = stockManageRoleMapper.selectByExample(stockManageRoleExample);
        List<StockManageRoleRespDTO> stockManageRoleRespDTOS = new ArrayList<>();
        HashSet<String> empIds = new HashSet<>();

        if(stockManageRoles != null){
            stockManageRoles.stream().forEach(dto->{
                StockManageRoleRespDTO stockManageRoleRespDTO = new StockManageRoleRespDTO();
                BeanUtils.copyProperties(dto,stockManageRoleRespDTO);
                try {
                    stockManageRoleRespDTO.setCreatedAt(DateUtils.dateFormat(dto.getCreatedAt(),DateUtils.DATE_PATTERN));
                    stockManageRoleRespDTO.setStartDate(DateUtils.dateFormat(dto.getStartDate(),DateUtils.DATE_PATTERN));
                    stockManageRoleRespDTO.setEndDate(DateUtils.dateFormat(dto.getEndDate(),DateUtils.DATE_PATTERN));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                stockManageRoleRespDTOS.add(stockManageRoleRespDTO);
            });
        }

        //返回结果
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        pageRespDTO.setCount(count);
        pageRespDTO.setPageNum(stockManageRoleReqDTO.getPageNum());
        pageRespDTO.setPageSize(stockManageRoleReqDTO.getPageSize());
        pageRespDTO.setStartNum(stockManageRoleReqDTO.getStartNum());
        pageRespDTO.setData(stockManageRoleRespDTOS);
        return pageRespDTO;
    }

    @Override
    public StockManageRole selectManageRoleById(long roleId) {
        return stockManageRoleMapper.selectByPrimaryKey(roleId);
    }

    @Override
    public List<StockManageRole> selectManageRolesByRoleNames(List<String> roleName) {

        List<StockManageRole> stockManageRoles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roleName)) {
            //去重
            HashSet<String> roleNameHashSet = new HashSet<>(roleName);
            Set<String> roleNameSet = roleNameHashSet;
            List<String> roleNameSetList = new ArrayList<>(roleNameSet);

            int toIndex = CommonConstant.MAX_QUERY_COUNT;
            for (int i = 0; i < roleNameSetList.size(); i += CommonConstant.MAX_QUERY_COUNT) {
                if (i + CommonConstant.MAX_QUERY_COUNT > roleNameSetList.size()) {
                    toIndex = roleNameSetList.size() - i;
                }

                List<String> subRoleNameList = roleNameSetList.subList(i, i + toIndex);
                StockManageRoleExample example = new StockManageRoleExample();
                StockManageRoleExample.Criteria criteria = example.createCriteria();
                criteria.andRoleNameIn(subRoleNameList);
                criteria.andStartDateLessThanOrEqualTo(new Date());
                criteria.andEndDateGreaterThanOrEqualTo(new Date());
                criteria.andStatusEqualTo(StockAuthorityEnum.Status.VALID.getCode());
                criteria.andDelFlagEqualTo(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
                List<StockManageRole> stockManageRoleList = stockManageRoleMapper.selectByExample(example);
                stockManageRoles.addAll(stockManageRoleList);
            }
        }
        return stockManageRoles;

    }

    @Override
    public List<StockManageRole> selectManageRolesByRoleIds(List<Long> roleIds) {
        List<StockManageRole> stockManageRoles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(roleIds)) {
            //去重
            HashSet<Long> roleNameHashSet = new HashSet<>(roleIds);
            List<Long> roleNameSetList = new ArrayList<>(roleNameHashSet);

            int toIndex = CommonConstant.MAX_QUERY_COUNT;
            for (int i = 0; i < roleNameSetList.size(); i += CommonConstant.MAX_QUERY_COUNT) {
                if (i + CommonConstant.MAX_QUERY_COUNT > roleNameSetList.size()) {
                    toIndex = roleNameSetList.size() - i;
                }

                List<Long> subRoleNameList = roleNameSetList.subList(i, i + toIndex);
                StockManageRoleExample example = new StockManageRoleExample();
                StockManageRoleExample.Criteria criteria = example.createCriteria();
                criteria.andRoleIdIn(subRoleNameList);
                List<StockManageRole> stockManageRoleList = stockManageRoleMapper.selectByExample(example);
                stockManageRoles.addAll(stockManageRoleList);
            }
        }
        return stockManageRoles;
    }

    @Override
    public ResponseData batchSaveManageRole(MultipartFile file)throws Exception {

        JwtUser user = SecurityUtil.getJwtUser();

        final List<StockManageRoleImportExcel> importExcelList = ExcelUtil.importExcel(file.getInputStream(),
                StockManageRoleImportExcel.class);

        //文件数据不能为空
        if (org.apache.commons.collections.CollectionUtils.isEmpty(importExcelList)) {
            return ResponseData.createFailResult("文件内数据为空");
        }

        //清单中不能有空的列
        final List<String> roleNames = importExcelList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getRoleName()))
                .map(item -> item.getRoleName())
                .collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(roleNames) || importExcelList.size() != roleNames.size()) {
            return ResponseData.createFailResult("文件内数据量与解析的角色名称数量对应不上，角色名称存在为空的列");
        }

        //校验参数
        String checkSaveParam = checkBatchSaveParam (importExcelList,roleNames);
        if (StringUtils.isNotBlank (checkSaveParam)) {
            return ResponseData.createFailResult (checkSaveParam);
        }
        log.info("批量导入角色保存校验参数已通过》》");

        List<StockManageRole> stockManageRoleList = new ArrayList<>();
        importExcelList.stream().forEach(dto->{
            StockManageRole stockManageRole = new StockManageRole();
            stockManageRole.setWarehouseCode("");
            stockManageRole.setRoleName(dto.getRoleName());
            try {
                stockManageRole.setStartDate(DateUtils.dateParse(dto.getStartDate(),DateUtils.DATE_PATTERN));
                stockManageRole.setEndDate(DateUtils.dateParse(dto.getEndDate(),DateUtils.DATE_PATTERN));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            stockManageRole.setRoleType(MetaDataEnum.yesOrNo.NO.getValue());
            stockManageRole.setStatus(MetaDataEnum.yesOrNo.YES.getValue());
            stockManageRole.setDelFlag(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
            stockManageRole.setCreatedBy(user.getEmployeeCode());
            stockManageRole.setCreatedAt(new Date());
            stockManageRole.setUpdatedBy(user.getEmployeeCode());
            stockManageRole.setUpdatedAt(new Date());
            stockManageRoleList.add(stockManageRole);
        });

        stockManageRoleExtendMapper.batchInsertStockManageRole(stockManageRoleList);

        return ResponseData.createSuccessResult ();
    }

    /**
     * 批量导入参数校验
     * @param importExcelList
     * @return
     */
    private String checkBatchSaveParam(List<StockManageRoleImportExcel> importExcelList,List<String> roleNames) {

        List<StockManageRole> stockManageRoles = selectManageRolesByRoleNames(roleNames);
        List<StockManageRoleImportExcel> stockManageRoleImportExcelList = ConvertUtil.convertToType(StockManageRoleImportExcel.class,stockManageRoles);

        //数据去重
        importExcelList = importExcelList.stream().distinct().collect(Collectors.toList());
        importExcelList.removeAll(stockManageRoleImportExcelList);

        StringBuffer stringBuffer = new StringBuffer();
        if(StringUtils.isNotBlank(stringBuffer.toString())){
            return stringBuffer.toString();
        }
        return null;
    }


    /**
     * 新建角色检验前端传入的参数
     * @param stockManageRoleReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(StockManageRoleReqDTO stockManageRoleReqDTO, JwtUser user) {

        if(stockManageRoleReqDTO == null){
            return "必填参数不能为空";
        }
        if(StringUtils.isBlank(stockManageRoleReqDTO.getRoleName())){
            return "角色名称不能为空";
        }
        if(StringUtils.isBlank(stockManageRoleReqDTO.getStartDate()) || StringUtils.isBlank(stockManageRoleReqDTO.getEndDate())){
            return "角色的有效日期不能为空";
        }

        StockManageRoleExample stockManageRoleExample = new StockManageRoleExample();
        StockManageRoleExample.Criteria criteria = stockManageRoleExample.createCriteria();
        criteria.andRoleNameEqualTo(stockManageRoleReqDTO.getRoleName());
        List<StockManageRole> stockManageRoles = stockManageRoleMapper.selectByExample(stockManageRoleExample);
        if(stockManageRoles != null && stockManageRoles.size()>0 && stockManageRoleReqDTO.getRoleId() == null){
            return "角色名称重复";
        }

        return null;
    }
}
