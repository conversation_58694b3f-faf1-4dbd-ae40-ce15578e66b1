package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.entity.vo.StockCheckDataImportExcel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/11/18
 * @description
 */
public interface StockCheckOperatorService {

    /**
     * 线下盘点数据上传
     * @param checkExcelList
     * @param checkTaskId
     * @return
     */
    ResponseData checkDataImport(List<StockCheckDataImportExcel> checkExcelList,Long checkTaskId);

    /**
     * 盘点上传数据查询
     * @param assetQueryScopeReqDTO
     * @return
     */
    ResponseData uploadDataShow(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 任务线下盘点确认
     * @param checkTaskId
     * @return
     * @throws Exception
     */
    ResponseData uploadDataConfirm(Long checkTaskId) throws Exception;
}
