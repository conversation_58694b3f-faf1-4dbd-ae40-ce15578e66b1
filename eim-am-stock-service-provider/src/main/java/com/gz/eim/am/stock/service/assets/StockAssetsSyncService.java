package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;

/**
 * @author: wei<PERSON><PERSON><PERSON>
 * @date: 2020/10/10
 * @description
 */
public interface StockAssetsSyncService {

    /**
     * 更新记录表的数据状态
     *
     * @param batchNo   更新批次
     * @param oldStatus 原状态
     * @param newStatus 要更新的最新状态
     * @return
     */
    ResponseData updateSyncStatusByBatchNo(String batchNo, Integer oldStatus, Integer newStatus);

    /**
     * 同步数据到ebs
     *
     * @param bizId      批次号
     * @param syncStatus 状态
     * @param type       所有模块 1新增 2报废 3变更
     * @return
     */
    ResponseData assetSyncToEbs(String bizId, Integer syncStatus, Integer type);

    /**
     * 同步ebs处理结果
     *
     * @param queryCode
     * @param type
     */
    void syncEbsResult(String queryCode, Integer type);


    ///////////////////ebs同步二期//////////////////////

    /**
     * 推送所有模块数据
     *
     * @param batchNo
     * @param syncStatus
     * @return
     */
    void syncAllModelAssets(String batchNo, Integer syncStatus);

    /**
     * 推送新增资产（采购、盘盈）
     *
     * @param batchNo * @param syncStatus
     * @return
     */
    void syncNewAssets(String batchNo, Integer syncStatus);

    /**
     * 推送报废资产（报废、盘亏）
     *
     * @param batchNo * @param syncStatus
     * @return
     */
    void syncScrapAssets(String batchNo, Integer syncStatus);

    /**
     * 提送变更资产（结算、领用、归还、调出、调入）
     *
     * @param batchNo * @param syncStatus
     * @return
     */
    void syncChangeAssets(String batchNo, Integer syncStatus);

    /**
     * 查询新增资产（采购、盘盈）
     *
     * @param queryCode
     * @return
     */
    void queryNewAssets(String queryCode);

    /**
     * 查询报废资产（报废、盘亏）
     *
     * @param queryCode
     * @return
     */
    void queryScrapAssets(String queryCode);

    /**
     * 查询变更资产（结算、领用、归还、调出、调入）
     *
     * @param queryCode
     * @return
     */
    void queryChangeAssets(String queryCode);
}
