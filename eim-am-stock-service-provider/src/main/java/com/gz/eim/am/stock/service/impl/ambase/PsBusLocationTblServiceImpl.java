package com.gz.eim.am.stock.service.impl.ambase;

import com.gz.eim.am.stock.dao.ambase.PsBusLocationTblMapper;
import com.gz.eim.am.stock.entity.PsBusLocationTblDO;
import com.gz.eim.am.stock.entity.ambase.PsBusLocationTbl;
import com.gz.eim.am.stock.entity.ambase.PsBusLocationTblExample;
import com.gz.eim.am.stock.service.ambase.PsBusLocationTblService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PsBusLocationTblServiceImpl implements PsBusLocationTblService {

    @Autowired
    private PsBusLocationTblMapper psBusLocationTblMapper;

    @Override
    public Map<String, PsBusLocationTbl> selectPsBusLocationTblMap(PsBusLocationTblDO psBusLocationTblDO) {
        List<PsBusLocationTbl> psBusLocationTblList = selectPsBusLocationTblList(psBusLocationTblDO);
        if(CollectionUtils.isEmpty(psBusLocationTblList)){
            return new HashMap<>();
        }
        return psBusLocationTblList.stream().collect(Collectors.toMap(PsBusLocationTbl :: getLocation, psBusLocationTbl -> psBusLocationTbl, (k1, k2) -> k2));
    }

    @Override
    public List<PsBusLocationTbl> selectPsBusLocationTblList(PsBusLocationTblDO psBusLocationTblDO) {
        if(null == psBusLocationTblDO){
            return new ArrayList<>();
        }
        PsBusLocationTblExample psBusLocationTblExample = new PsBusLocationTblExample();
        PsBusLocationTblExample.Criteria criteria = psBusLocationTblExample.createCriteria();
        if(CollectionUtils.isNotEmpty(psBusLocationTblDO.getLocationList())){
            criteria.andLocationIn(psBusLocationTblDO.getLocationList());
        }
        return psBusLocationTblMapper.selectByExample(psBusLocationTblExample);
    }
}
