package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.base.api.wfl.WflAuthUtilApi;
import com.gz.eim.am.base.dto.request.wfl.WflAuthVerifyDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.assets.AssetsDocumentExtendMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsDocumentDetailMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsDocumentMapper;
import com.gz.eim.am.stock.dao.base.StockWflMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsDocumentHeadRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsDocumentLineRespDTO;
import com.gz.eim.am.stock.dto.response.file.StockAttachDTO;
import com.gz.eim.am.stock.dto.response.wfl.WlfAssetsTransferLineRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysApvTask;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetTransferWflService;
import com.gz.eim.am.stock.service.assets.StockAssetsOperationLogService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.manage.StockSuppliesQuantityService;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/5/10
 * @description TODO
 */
@Service
@Slf4j
public class StockAssetTransferWflServiceImpl implements StockAssetTransferWflService {

    @Autowired
    StockAssetsDocumentMapper stockAssetsDocumentMapper;

    @Autowired
    StockAssetsDocumentDetailMapper stockAssetsDocumentDetailMapper;

    @Autowired
    AssetsDocumentExtendMapper assetsDocumentExtendMapper;

    @Autowired
    StockAssetsService stockAssetsService;

    @Autowired
    StockWflMapper stockWflMapper;

    @Autowired
    StockAssetsOperationLogService stockAssetsOperationLogService;

    @Autowired
    AmbaseCommonService ambaseCommonService;

    @Autowired
    WflAuthUtilApi wflAuthUtilApi;

    @Autowired
    private StockSuppliesQuantityService stockSuppliesQuantityService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData complete(String bizNo, String lobNo, Integer status) {

        //对编号和数据进行校验
        String checkSaveParam = checkSaveParam(bizNo, status);
        if (StringUtils.isNotBlank(checkSaveParam)) {
            return ResponseData.createFailResult(checkSaveParam);
        }

        //更新转移头状态
        StockAssetsDocumentExample example = new StockAssetsDocumentExample();
        StockAssetsDocumentExample.Criteria criteria = example.createCriteria();
        criteria.andDocumentNoEqualTo(bizNo);
        StockAssetsDocument stockAssetsDocument = new StockAssetsDocument();
        stockAssetsDocument.setHeadStatus(status);
        stockAssetsDocument.setUpdatedAt(new Date());
        log.info("开始更新转移头状态。。");
        stockAssetsDocumentMapper.updateByExampleSelective(stockAssetsDocument, example);
        // 查询行信息
        StockAssetsDocumentDetailExample stockAssetsDocumentDetailExample = new StockAssetsDocumentDetailExample();
        StockAssetsDocumentDetailExample.Criteria criteria1 = stockAssetsDocumentDetailExample.createCriteria();
        criteria1.andDocumentNoEqualTo(bizNo);
        List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = stockAssetsDocumentDetailMapper.selectByExample(stockAssetsDocumentDetailExample);
        List<String> assetscode = new ArrayList<>();
        // 批量更新资产编码的时候使用
        List<StockAssets> updateAssetsList = new ArrayList<>();
        stockAssetsDocumentDetailList.stream().forEach(dto -> {
            String assetsCode = dto.getAssetsCode();
            assetscode.add(assetsCode);
            // 组装批量更新的资产
            StockAssets stockAssets = new StockAssets();
            stockAssets.setAssetsCode(assetsCode);
            stockAssets.setUpdatedBy(dto.getCreatedBy());
            stockAssets.setUpdatedAt(new Date());
            stockAssets.setAssetsKeeper(dto.getNewAssetsKeeper());
            updateAssetsList.add(stockAssets);
        });
        //审批通过则更新转移行以及资产状态
        if (AssetTransferEnum.Status.SECTION_IN.getCode().equals(status)) {
            log.info("审批通过，更新其它表数据状态。。");
            //转移行状态更新为已处理
            StockAssetsDocumentDetail stockAssetsDocumentDetail = new StockAssetsDocumentDetail();
            stockAssetsDocumentDetail.setLineStatus(AssetTransferEnum.LineStatus.FINISH.getCode());
            stockAssetsDocumentDetail.setUpdatedAt(new Date());
            stockAssetsDocumentDetailMapper.updateByExampleSelective(stockAssetsDocumentDetail, stockAssetsDocumentDetailExample);
            if(StringUtils.equalsIgnoreCase(FlowCodeEnum.ASSETS_TRANSFER.getLob(),lobNo)){
                //资产卡片管理员更新为新的管理员
                stockAssetsService.batchUpdateByAssetsCodeList(updateAssetsList);
            }else if(StringUtils.equalsIgnoreCase(FlowCodeEnum.ASSETS_TRANSFER_HOLDER.getLob(),lobNo)){
                //资产卡片持有人更新为新持有人
                assetsDocumentExtendMapper.batchUpdateAssetHolderByDocumentNo(bizNo);
            }
        }

        // 修改工作流记录表状态 TODO
        StockWflExample stockWflExample = new StockWflExample();
        StockWflExample.Criteria criteria2 = stockWflExample.createCriteria();
        criteria2.andBizIdEqualTo(bizNo);
        criteria2.andLobNoEqualTo(lobNo);
        StockWfl stockWfl = new StockWfl();
        Date date = new Date();
        stockWfl.setUpdatedAt(date);
        stockWfl.setApprovedTime(date);
        stockWfl.setApprovedResult(AssetTransferEnum.Status.SECTION_IN.getCode().equals(status) ? WorkFlowStatusCodeEnum.ApprovedResult.WAIT_IN.getCode() : WorkFlowStatusCodeEnum.ApprovedResult.REFUSE.getCode());
        stockWflMapper.updateByExampleSelective(stockWfl, stockWflExample);

        // 如果是资产持有人转移，无论是拒绝还是通过都需要把资产的使用状态修改为正常，同时新增修改记录
        if(StringUtils.equalsIgnoreCase(FlowCodeEnum.ASSETS_TRANSFER_HOLDER.getLob(),lobNo)){
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setConditions(AssetsEnum.Conditions.NORMAL.getValue());
            stockAssetsService.batchUpdateAssetsSameMessage(assetsSearchDTO, assetscode);
        }
        // 如果是资产管理员转移而且是审批拒绝不会记录日志
        if(StringUtils.equalsIgnoreCase(FlowCodeEnum.ASSETS_TRANSFER.getLob(),lobNo) && AssetTransferEnum.Status.REFUSE.getCode().equals(status)){
            return ResponseData.createSuccessResult();
        }
        //资产卡片变更记录表记录变更数据
        List<StockAssets> stockAssets = stockAssetsService.selectAssets(null, assetscode);
        stockAssetsOperationLogService.insertMultipleOperationLog(stockAssets, AssetsEnum.operationType.BUSINESS);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData selectWflLineByDocumentNo(String bizId) {
        //对编号和数据进行校验
        String checkSelectParam = checkSelectParam(bizId);
        if (StringUtils.isNotBlank(checkSelectParam)) {
            return ResponseData.createFailResult(checkSelectParam);
        }
        List<WlfAssetsTransferLineRespDTO> wlfAssetsTransferLineRespDTOS = assetsDocumentExtendMapper.selectWflWlfTransferLineByDocumentNo(bizId);
        if (CollectionUtils.isEmpty(wlfAssetsTransferLineRespDTOS)) {
            wlfAssetsTransferLineRespDTOS = new ArrayList<>();
        }
        return ResponseData.createSuccessResult(wlfAssetsTransferLineRespDTOS);
    }


    /**
     * 本地校验方法
     *
     * @param bizno
     * @param status
     * @return
     */
    private String checkSaveParam(String bizno, Integer status) {
        //校验业务编号是否存在
        StockAssetsDocumentExample example = new StockAssetsDocumentExample();
        StockAssetsDocumentExample.Criteria criteria = example.createCriteria();
        criteria.andDocumentNoEqualTo(bizno);
        List<StockAssetsDocument> stockAssetsDocumentList = stockAssetsDocumentMapper.selectByExample(example);
        if (null == stockAssetsDocumentList || stockAssetsDocumentList.size() < 1) {
            return "业务单据号不存在";
        }
        //校验当前单据状态是否为审批中
        StockAssetsDocument stockAssetsDocument = stockAssetsDocumentList.get(0);
        if (!AssetTransferEnum.Status.APPROVE.getCode().equals(stockAssetsDocument.getHeadStatus())) {
            return "该单据审批流程已处理过，请勿重复发起";
        }
        return null;
    }

    /**
     * 校验查询参数
     *
     * @param bizId
     * @return
     */
    private String checkSelectParam(String bizId) {

        //校验当前用户是否存在且为当前流程的创建人或审批人
        JwtUser user = SecurityUtil.getJwtUser();
        if (user.getEmployeeCode() == null) {
            return "当前用户不存在";
        }

//        //校验当前人员是否有该单据的查询权限
//        com.gz.eim.am.base.dto.ResponseData<Boolean> data = wflAuthUtilApi.verifyPermission(WflAuthVerifyDTO.newInst(bizId, null, user.getEmployeeCode()));
//        Boolean flag = data.getData();
//
//        /*List<SysApvTask> sysApvTasks = ambaseCommonService.selectApvTastBybizNo(bizId);
//        if(CollectionUtils.isEmpty(sysApvTasks)){
//            return "工作流信息不存在";
//        }
//        boolean flag = false;
//        for(SysApvTask sysApvTask : sysApvTasks){
//            if(user.getEmployeeCode().equals(sysApvTask.getApplyUser()) || user.getEmployeeCode().equals(sysApvTask.getApproveUser())){
//                flag = true;
//            }
//        }*/
//        if (!flag) {
//            return "当前用户没有该流程查看权限";
//        }
        return null;
    }

    @Override
    public ResponseData selectWflLineLicenseTransferByDocumentNo(String bizId) {
        //创建集合存储员工id
        HashSet<String> empIds = new HashSet<>();
        //查询资产转移头
        StockAssetsDocumentExample StockAssetsDocumentExample = new StockAssetsDocumentExample();
        StockAssetsDocumentExample.Criteria criteria = StockAssetsDocumentExample.createCriteria();
        // 判断是否通过订单id查询
        criteria.andDocumentNoEqualTo(bizId);
        StockAssetsDocument stockAssetsDocument = stockAssetsDocumentMapper.selectByExample(StockAssetsDocumentExample).get(0);
        StockAssetsDocumentHeadRespDTO stockAssetsDocumentHeadRespDTO = new StockAssetsDocumentHeadRespDTO();
        BeanUtils.copyProperties(stockAssetsDocument, stockAssetsDocumentHeadRespDTO);
        AssetTransferEnum.Type type = AssetTransferEnum.Type.fromCode(stockAssetsDocument.getType());
        if(type != null){
            stockAssetsDocumentHeadRespDTO.setTypeDesc(type.getValue());
        }
        AssetTransferEnum.Reason reason = AssetTransferEnum.Reason.fromCode(stockAssetsDocument.getReasonCode());
        if(reason != null){
            stockAssetsDocumentHeadRespDTO.setReasonCodeDesc(reason.getValue());
        }
        empIds.add(stockAssetsDocumentHeadRespDTO.getBillingUser());
        empIds.add(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper());
        empIds.add(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder());
        //查询资产转移行
        List<StockAssetsDocumentLineRespDTO> stockAssetsDocumentLineRespDTOList = assetsDocumentExtendMapper.selectAssetsDocumentDetailByDocumentNo(stockAssetsDocumentHeadRespDTO.getDocumentNo());
        stockAssetsDocumentLineRespDTOList.stream().forEach(dto -> {
            AssetsEnum.statusType[] statusTypes = AssetsEnum.statusType.values();
            for (int i = 0; i < statusTypes.length; i++) {
                if (statusTypes[i].getValue().equals(dto.getAssetStatus())) {
                    dto.setAssetStatusName(statusTypes[i].getDesc());
                    break;
                }
            }
            dto.setTransferStatusName(AssetTransferEnum.LineStatus.FINISH.getCode().equals(dto.getTransferStatus()) ? AssetTransferEnum.LineStatus.FINISH.getValue() : AssetTransferEnum.LineStatus.NO_FINISH.getValue());
            empIds.add(dto.getOldHolder());
            empIds.add(dto.getHolder());
            empIds.add(dto.getNewAssetsKeeper());
            empIds.add(dto.getOldAssetsKeeper());
        });
        List<String> empIdList = new ArrayList<>();
        empIdList.addAll(empIds);
        //为返回对象赋值员工名称
        //Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        Map<String, SysUserBasicInfo> sysUserMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(empIdList);
        stockAssetsDocumentHeadRespDTO.setNewAssetsKeeperName(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper()).getName() : "");
        stockAssetsDocumentHeadRespDTO.setBillingUserName(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()).getName() : null);
        stockAssetsDocumentHeadRespDTO.setHpsJobcdDescr(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()).getHpsJobcdDescr() : "");
        stockAssetsDocumentHeadRespDTO.setNewAssetsHolderName(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()).getName() : null);
        stockAssetsDocumentHeadRespDTO.setNewHpsJobcdDescr(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()).getHpsJobcdDescr() : "");
        stockAssetsDocumentLineRespDTOList.stream().forEach(dto -> {
            dto.setNewAssetsKeeperName(sysUserMap.get(dto.getNewAssetsKeeper()) != null ? sysUserMap.get(dto.getNewAssetsKeeper()).getName() : null);
            dto.setOldAssetsKeeperName(sysUserMap.get(dto.getOldAssetsKeeper()) != null ? sysUserMap.get(dto.getOldAssetsKeeper()).getName() : null);
            dto.setHolderName(sysUserMap.get(dto.getOldHolder()) != null ? sysUserMap.get(dto.getOldHolder()).getName() : null);
        });
        // 设置旧资产持有人
        if (StringUtils.isNotEmpty(stockAssetsDocumentLineRespDTOList.get(0).getOldHolder())) {
            SysUserBasicInfo oldUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockAssetsDocumentLineRespDTOList.get(0).getOldHolder());
            stockAssetsDocumentHeadRespDTO.setHolder(oldUserBasicInfo.getEmpId());
            stockAssetsDocumentHeadRespDTO.setHolderName(oldUserBasicInfo.getName());
            stockAssetsDocumentHeadRespDTO.setDeptCode(oldUserBasicInfo.getDeptId());
            stockAssetsDocumentHeadRespDTO.setDeptName(oldUserBasicInfo.getDeptName());
            stockAssetsDocumentHeadRespDTO.setHpsJobcdDescr(oldUserBasicInfo.getHpsJobcdDescr());
            stockAssetsDocumentHeadRespDTO.setDeptFullId(oldUserBasicInfo.getDeptFullId());
            stockAssetsDocumentHeadRespDTO.setDeptFullName(oldUserBasicInfo.getDeptFullName());
        }
        // 设置新资产持有人
        if (StringUtils.isNotEmpty(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder())) {
            SysUserBasicInfo newUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder());
            stockAssetsDocumentHeadRespDTO.setNewDeptCode(newUserBasicInfo.getDeptId());
            stockAssetsDocumentHeadRespDTO.setNewDeptName(newUserBasicInfo.getDeptName());
            stockAssetsDocumentHeadRespDTO.setNewHpsJobcdDescr(newUserBasicInfo.getHpsJobcdDescr());
            stockAssetsDocumentHeadRespDTO.setNewDeptFullId(newUserBasicInfo.getDeptFullId());
            stockAssetsDocumentHeadRespDTO.setNewDeptFullName(newUserBasicInfo.getDeptFullName());
        }
        stockAssetsDocumentHeadRespDTO.setStockAssetsDocumentLineRespDTOs(stockAssetsDocumentLineRespDTOList);
        //返回结果
        return ResponseData.createSuccessResult(stockAssetsDocumentHeadRespDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData completeLicenseTransfer(String bizNo, String lobNo, Integer status) {

        //对编号和数据进行校验
        String checkSaveParam = checkSaveParam(bizNo, status);
        if (StringUtils.isNotBlank(checkSaveParam)) {
            return ResponseData.createFailResult(checkSaveParam);
        }
        Date currentDate = new Date();
        //更新转移头状态
        StockAssetsDocumentExample example = new StockAssetsDocumentExample();
        StockAssetsDocumentExample.Criteria StockAssetsDocumentExampleCriteria = example.createCriteria();
        StockAssetsDocumentExampleCriteria.andDocumentNoEqualTo(bizNo);
        StockAssetsDocument stockAssetsDocument = new StockAssetsDocument();
        stockAssetsDocument.setHeadStatus(status);
        stockAssetsDocument.setUpdatedAt(currentDate);
        stockAssetsDocumentMapper.updateByExampleSelective(stockAssetsDocument, example);
        // 查询行信息
        StockAssetsDocumentDetailExample stockAssetsDocumentDetailExample = new StockAssetsDocumentDetailExample();
        StockAssetsDocumentDetailExample.Criteria stockAssetsDocumentDetailExampleCriteria = stockAssetsDocumentDetailExample.createCriteria();
        stockAssetsDocumentDetailExampleCriteria.andDocumentNoEqualTo(bizNo);
        List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = stockAssetsDocumentDetailMapper.selectByExample(stockAssetsDocumentDetailExample);
        List<String> assetsCodeList = stockAssetsDocumentDetailList.stream().map(StockAssetsDocumentDetail :: getAssetsCode).collect(Collectors.toList());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, assetsCodeList);
        // 审批通过则更新转移行以及资产信息
        if (AssetTransferEnum.Status.SECTION_IN.getCode().equals(status)) {
            List<StockSuppliesQuantity> stockSuppliesQuantityList = new ArrayList<>();
            Map<String, StockSuppliesQuantity> stringStockSuppliesQuantityMap = new HashMap<>();
            // 转移行状态更新为已处理
            StockAssetsDocumentDetail stockAssetsDocumentDetail = new StockAssetsDocumentDetail();
            stockAssetsDocumentDetail.setLineStatus(AssetTransferEnum.LineStatus.FINISH.getCode());
            stockAssetsDocumentDetail.setUpdatedAt(currentDate);
            stockAssetsDocumentDetailMapper.updateByExampleSelective(stockAssetsDocumentDetail, stockAssetsDocumentDetailExample);
            StockAssetsDocumentDetail stockAssetsDocumentDetailTemp = stockAssetsDocumentDetailList.get(CommonConstant.NUMBER_ZERO);
            String newHolder = stockAssetsDocumentDetailTemp.getNewHolder();
            String newDeptCode = stockAssetsDocumentDetailTemp.getNewDeptCode();
            // 更新执照的持有人以及审批状态
            for (StockAssets stockAssets : stockAssetsList) {
                stockAssets.setHolder(newHolder);
                stockAssets.setHolderDept(newDeptCode);
                stockAssets.setHolderAddress(stockAssetsDocumentDetailTemp.getNewHolderAddress());
                stockAssets.setCostDept(newDeptCode);
                stockAssets.setNeedDept(newDeptCode);
                stockAssets.setApproveStatus(MetaDataEnum.yesOrNo.NO.getValue());
                stockAssets.setUpdatedAt(currentDate);
                // 如果执照是在库状态，就修改资产为使用中，并修改仓库为空串，并减库存
                if(AssetsEnum.statusType.IDLE.getValue().equals(stockAssets.getStatus())){
                    stockAssets.setStatus(AssetsEnum.statusType.USED.getValue());
                    stockAssets.setWarehouseCode(StringConstant.EMPTY);
                    String warehouseCode = stockAssets.getWarehouseCode();
                    String suppliesCode = stockAssets.getSuppliesCode();
                    String key = warehouseCode + StringConstant.HYPHEN + suppliesCode;
                    StockSuppliesQuantity stockSuppliesQuantity = stringStockSuppliesQuantityMap.get(key);
                    if(null == stockSuppliesQuantity){
                        stockSuppliesQuantity = new StockSuppliesQuantity();
                        stockSuppliesQuantity.setWarehouseCode(warehouseCode);
                        stockSuppliesQuantity.setSuppliesCode(suppliesCode);
                        stockSuppliesQuantity.setStatus(StockSuppliesQuantityEnum.status.IN_STOCK.getValue());
                        stockSuppliesQuantity.setQuantity(new BigDecimal(CommonConstant.NUMBER_ONE));
                        stockSuppliesQuantityList.add(stockSuppliesQuantity);
                        stringStockSuppliesQuantityMap.put(key, stockSuppliesQuantity);
                    }else {
                        stockSuppliesQuantity.setQuantity(stockSuppliesQuantity.getQuantity().add(new BigDecimal(CommonConstant.NUMBER_ONE)));
                    }
                }
            }
            // 更新资产状态
            stockAssetsService.batchUpdate(stockAssetsList);
            // 扣减库存数量
            if(!CollectionUtils.isEmpty(stockSuppliesQuantityList)){
                for (StockSuppliesQuantity stockSuppliesQuantity : stockSuppliesQuantityList) {
                    stockSuppliesQuantityService.subtractQuantity(stockSuppliesQuantity);
                }
            }
            // 资产卡片变更记录表记录变更数据
            stockAssetsOperationLogService.insertMultipleOperationLog(stockAssetsList, AssetsEnum.operationType.BUSINESS);
        }else {
            for (StockAssets stockAssets : stockAssetsList) {
                stockAssets.setApproveStatus(MetaDataEnum.yesOrNo.NO.getValue());
                stockAssets.setUpdatedAt(currentDate);
            }
            stockAssetsService.batchUpdate(stockAssetsList);
        }
        return ResponseData.createSuccessResult();
    }
}
