package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfig;
import com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSelfServiceAssetsCategoryConfigMapper {
    long countByExample(StockSelfServiceAssetsCategoryConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockSelfServiceAssetsCategoryConfig record);

    int insertSelective(StockSelfServiceAssetsCategoryConfig record);

    List<StockSelfServiceAssetsCategoryConfig> selectByExample(StockSelfServiceAssetsCategoryConfigExample example);

    StockSelfServiceAssetsCategoryConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockSelfServiceAssetsCategoryConfig record, @Param("example") StockSelfServiceAssetsCategoryConfigExample example);

    int updateByExample(@Param("record") StockSelfServiceAssetsCategoryConfig record, @Param("example") StockSelfServiceAssetsCategoryConfigExample example);

    int updateByPrimaryKeySelective(StockSelfServiceAssetsCategoryConfig record);

    int updateByPrimaryKey(StockSelfServiceAssetsCategoryConfig record);
}