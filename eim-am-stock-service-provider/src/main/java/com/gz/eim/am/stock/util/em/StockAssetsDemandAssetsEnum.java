package com.gz.eim.am.stock.util.em;

import com.gz.eim.am.stock.util.common.CodeEnumUtil;

/**
   * @description: 资产需求单资产枚举类
   * @author: <EMAIL>
   * @date: 2023/7/6
   */
public class StockAssetsDemandAssetsEnum {
    /**
     * 资产状态
     */
    public enum DeliveryStatus {
        /**
         *
         */
        NOT_DELIVERY(1, "未领用"),
        /**
         * 使用中
         */
        HAVE_DELIVERY(2, "已领用"),
        /**
         * 已发出
         */
        SYSTEM_AUTOMATIC_DELIVERY(3, "系统自动领用");

        DeliveryStatus(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    /**
     * 领用方式
     */
    public enum DeliveryMethod implements ICodeEnum {
        /**
         * 自取
         */
        RECEIVE_BY_MYSELF (1, "自取"),
        /**
         * 邮寄
         */
        SEND(2, "邮寄");


        DeliveryMethod(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;

        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        /**
         * 根据code返回指定枚举类型中的相应值
         *
         * @param code 指定code
         */
        public static DeliveryMethod fromCode(Integer code) {
            return CodeEnumUtil.fromCode (DeliveryMethod.class, code);
        }
    }

}
