package com.gz.eim.am.stock.service.warehouse;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.warehouse.WarehouseReqDTO;
import com.gz.eim.am.stock.dto.request.warehouse.WarehouseSearchReqDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.StockWarehouse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 11:14
 */
public interface StockWarehouseService {

    /**
     * 仓库模糊插叙
     * @param param
     * @param limit
     * @param user
     * @param isCrossedAuthority
     * @return
     */
    ResponseData queryWarehouseByParam(String param, Integer limit,JwtUser user, Integer isCrossedAuthority, Integer partAuthFlag, String warehouseTypeList);

    /**
     * 分页查询仓库
     * @param reqDTO
     * @param user
     * @return
     */
    ResponseData queryWarehouse(WarehouseSearchReqDTO reqDTO,JwtUser user);

    /**
     * 新增仓库
     * @param warehouseReqDTO
     * @param user
     * @return
     */
    ResponseData addWarehouse(WarehouseReqDTO warehouseReqDTO, JwtUser user);

    /**
     * 修改仓库
     * @param warehouseReqDTO
     * @param user
     * @return
     */
    ResponseData modifyWarehouse(WarehouseReqDTO warehouseReqDTO, JwtUser user);

    /**
     * 根据仓库编码查询仓库
     * @param warehouseCode
     * @param status
     * @return
     */
    StockWarehouse selectByWarehouseCode(String warehouseCode,Integer status);

    /**
     * 查询仓库详情
     * @param warehouseCode
     * @param user
     * @return
     */
    ResponseData selectWarehouseDetail(String warehouseCode,JwtUser user);

    /**
     * 根据仓库编码查询仓库列表
     * @param warehouseCode
     * @return
     */
    List<WarehouseRespDTO> selectWarehouseDetailByCode(final List<String> warehouseCode);

    /**
     * @param:
     * @description: TODO
     * @return:
     * @author: <EMAIL>
     * @date: 2023/6/26
     */
    WarehouseRespDTO selectWarehouseDetailByCode(String warehouseCode);

    /**
     * 根据部门获取仓库
     * @param deptName
     * @return
     */
    List<StockWarehouse> selectByDeptName(String deptName);

    /**
     * 根据属性获取仓库
     * @param stockWarehouse
     * @return
     */
    List<StockWarehouse> selectByStockWarehouse(StockWarehouse stockWarehouse);
    /**
     * 根据仓库编码获取仓库详细Map
     * @param warehouseCodes
     * @return
     */
    Map<String, WarehouseRespDTO> selectWarehouseDetailMapByCode(List<String> warehouseCodes);

    /**
     * 根据仓库类型查询所有仓库
     * @param warehouseTypes
     * @return
     */
    List<StockWarehouse> selectWarehouseByTypes(List<Integer> warehouseTypes);

    /**
     * @param: reqDTO
     * @description: 领用资产查看仓库信息
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/13
     */
    ResponseData queryWarehouseByReceiveAssets(WarehouseSearchReqDTO warehouseSearchReqDTO, JwtUser user);
}
