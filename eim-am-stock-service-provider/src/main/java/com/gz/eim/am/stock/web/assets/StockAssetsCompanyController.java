package com.gz.eim.am.stock.web.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.assets.StockAssetsCompanyApi;
import com.gz.eim.am.stock.dto.request.assets.AssetCompanyReqDTO;
import com.gz.eim.am.stock.service.assets.StockAssetCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: eim-am-stock
 * @version: V 1.0
 * @description: 仓储公司信息查询
 * @author: guoyanlong
 * @create: 2020-03-13 14:23
 **/
@RestController
@RequestMapping("/api/am/stock/assets")
@Slf4j
public class StockAssetsCompanyController implements StockAssetsCompanyApi {

    @Autowired
    private StockAssetCompanyService stockAssetCompanyService;
    @Override
    public ResponseData selectAssetsCompany(AssetCompanyReqDTO companyReqDTO) {
        log.info("/api/am/stock/assets/selectAssetsCompany ....", companyReqDTO.toString());
        return stockAssetCompanyService.selectAssetsCompany(companyReqDTO);
    }
}
