package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockAssetsEbsSyncChangeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockAssetsEbsSyncChangeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNull() {
            addCriterion("assets_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNotNull() {
            addCriterion("assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeEqualTo(String value) {
            addCriterion("assets_code =", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotEqualTo(String value) {
            addCriterion("assets_code <>", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThan(String value) {
            addCriterion("assets_code >", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("assets_code >=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThan(String value) {
            addCriterion("assets_code <", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("assets_code <=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLike(String value) {
            addCriterion("assets_code like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotLike(String value) {
            addCriterion("assets_code not like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIn(List<String> values) {
            addCriterion("assets_code in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotIn(List<String> values) {
            addCriterion("assets_code not in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeBetween(String value1, String value2) {
            addCriterion("assets_code between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("assets_code not between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andQueryCodeIsNull() {
            addCriterion("query_code is null");
            return (Criteria) this;
        }

        public Criteria andQueryCodeIsNotNull() {
            addCriterion("query_code is not null");
            return (Criteria) this;
        }

        public Criteria andQueryCodeEqualTo(String value) {
            addCriterion("query_code =", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotEqualTo(String value) {
            addCriterion("query_code <>", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeGreaterThan(String value) {
            addCriterion("query_code >", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("query_code >=", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeLessThan(String value) {
            addCriterion("query_code <", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeLessThanOrEqualTo(String value) {
            addCriterion("query_code <=", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeLike(String value) {
            addCriterion("query_code like", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotLike(String value) {
            addCriterion("query_code not like", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeIn(List<String> values) {
            addCriterion("query_code in", values, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotIn(List<String> values) {
            addCriterion("query_code not in", values, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeBetween(String value1, String value2) {
            addCriterion("query_code between", value1, value2, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotBetween(String value1, String value2) {
            addCriterion("query_code not between", value1, value2, "queryCode");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNull() {
            addCriterion("sync_status is null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNotNull() {
            addCriterion("sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusEqualTo(Integer value) {
            addCriterion("sync_status =", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotEqualTo(Integer value) {
            addCriterion("sync_status <>", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThan(Integer value) {
            addCriterion("sync_status >", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("sync_status >=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThan(Integer value) {
            addCriterion("sync_status <", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThanOrEqualTo(Integer value) {
            addCriterion("sync_status <=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIn(List<Integer> values) {
            addCriterion("sync_status in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotIn(List<Integer> values) {
            addCriterion("sync_status not in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusBetween(Integer value1, Integer value2) {
            addCriterion("sync_status between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("sync_status not between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIsNull() {
            addCriterion("error_message is null");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIsNotNull() {
            addCriterion("error_message is not null");
            return (Criteria) this;
        }

        public Criteria andErrorMessageEqualTo(String value) {
            addCriterion("error_message =", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotEqualTo(String value) {
            addCriterion("error_message <>", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageGreaterThan(String value) {
            addCriterion("error_message >", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageGreaterThanOrEqualTo(String value) {
            addCriterion("error_message >=", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLessThan(String value) {
            addCriterion("error_message <", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLessThanOrEqualTo(String value) {
            addCriterion("error_message <=", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLike(String value) {
            addCriterion("error_message like", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotLike(String value) {
            addCriterion("error_message not like", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIn(List<String> values) {
            addCriterion("error_message in", values, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotIn(List<String> values) {
            addCriterion("error_message not in", values, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageBetween(String value1, String value2) {
            addCriterion("error_message between", value1, value2, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotBetween(String value1, String value2) {
            addCriterion("error_message not between", value1, value2, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateIsNull() {
            addCriterion("extract_start_date is null");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateIsNotNull() {
            addCriterion("extract_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateEqualTo(Date value) {
            addCriterion("extract_start_date =", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateNotEqualTo(Date value) {
            addCriterion("extract_start_date <>", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateGreaterThan(Date value) {
            addCriterion("extract_start_date >", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("extract_start_date >=", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateLessThan(Date value) {
            addCriterion("extract_start_date <", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateLessThanOrEqualTo(Date value) {
            addCriterion("extract_start_date <=", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateIn(List<Date> values) {
            addCriterion("extract_start_date in", values, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateNotIn(List<Date> values) {
            addCriterion("extract_start_date not in", values, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateBetween(Date value1, Date value2) {
            addCriterion("extract_start_date between", value1, value2, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateNotBetween(Date value1, Date value2) {
            addCriterion("extract_start_date not between", value1, value2, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateIsNull() {
            addCriterion("extract_end_date is null");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateIsNotNull() {
            addCriterion("extract_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateEqualTo(Date value) {
            addCriterion("extract_end_date =", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateNotEqualTo(Date value) {
            addCriterion("extract_end_date <>", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateGreaterThan(Date value) {
            addCriterion("extract_end_date >", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("extract_end_date >=", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateLessThan(Date value) {
            addCriterion("extract_end_date <", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateLessThanOrEqualTo(Date value) {
            addCriterion("extract_end_date <=", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateIn(List<Date> values) {
            addCriterion("extract_end_date in", values, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateNotIn(List<Date> values) {
            addCriterion("extract_end_date not in", values, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateBetween(Date value1, Date value2) {
            addCriterion("extract_end_date between", value1, value2, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateNotBetween(Date value1, Date value2) {
            addCriterion("extract_end_date not between", value1, value2, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNull() {
            addCriterion("business_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNotNull() {
            addCriterion("business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeEqualTo(Integer value) {
            addCriterion("business_type =", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotEqualTo(Integer value) {
            addCriterion("business_type <>", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThan(Integer value) {
            addCriterion("business_type >", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_type >=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThan(Integer value) {
            addCriterion("business_type <", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThanOrEqualTo(Integer value) {
            addCriterion("business_type <=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIn(List<Integer> values) {
            addCriterion("business_type in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotIn(List<Integer> values) {
            addCriterion("business_type not in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeBetween(Integer value1, Integer value2) {
            addCriterion("business_type between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("business_type not between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIsNull() {
            addCriterion("business_no is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIsNotNull() {
            addCriterion("business_no is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNoEqualTo(String value) {
            addCriterion("business_no =", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotEqualTo(String value) {
            addCriterion("business_no <>", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoGreaterThan(String value) {
            addCriterion("business_no >", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoGreaterThanOrEqualTo(String value) {
            addCriterion("business_no >=", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLessThan(String value) {
            addCriterion("business_no <", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLessThanOrEqualTo(String value) {
            addCriterion("business_no <=", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLike(String value) {
            addCriterion("business_no like", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotLike(String value) {
            addCriterion("business_no not like", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIn(List<String> values) {
            addCriterion("business_no in", values, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotIn(List<String> values) {
            addCriterion("business_no not in", values, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoBetween(String value1, String value2) {
            addCriterion("business_no between", value1, value2, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotBetween(String value1, String value2) {
            addCriterion("business_no not between", value1, value2, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoIsNull() {
            addCriterion("business_line_no is null");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoIsNotNull() {
            addCriterion("business_line_no is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoEqualTo(String value) {
            addCriterion("business_line_no =", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotEqualTo(String value) {
            addCriterion("business_line_no <>", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoGreaterThan(String value) {
            addCriterion("business_line_no >", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoGreaterThanOrEqualTo(String value) {
            addCriterion("business_line_no >=", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoLessThan(String value) {
            addCriterion("business_line_no <", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoLessThanOrEqualTo(String value) {
            addCriterion("business_line_no <=", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoLike(String value) {
            addCriterion("business_line_no like", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotLike(String value) {
            addCriterion("business_line_no not like", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoIn(List<String> values) {
            addCriterion("business_line_no in", values, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotIn(List<String> values) {
            addCriterion("business_line_no not in", values, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoBetween(String value1, String value2) {
            addCriterion("business_line_no between", value1, value2, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotBetween(String value1, String value2) {
            addCriterion("business_line_no not between", value1, value2, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNull() {
            addCriterion("billing_user is null");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNotNull() {
            addCriterion("billing_user is not null");
            return (Criteria) this;
        }

        public Criteria andBillingUserEqualTo(String value) {
            addCriterion("billing_user =", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotEqualTo(String value) {
            addCriterion("billing_user <>", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThan(String value) {
            addCriterion("billing_user >", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThanOrEqualTo(String value) {
            addCriterion("billing_user >=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThan(String value) {
            addCriterion("billing_user <", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThanOrEqualTo(String value) {
            addCriterion("billing_user <=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLike(String value) {
            addCriterion("billing_user like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotLike(String value) {
            addCriterion("billing_user not like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserIn(List<String> values) {
            addCriterion("billing_user in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotIn(List<String> values) {
            addCriterion("billing_user not in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserBetween(String value1, String value2) {
            addCriterion("billing_user between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotBetween(String value1, String value2) {
            addCriterion("billing_user not between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNull() {
            addCriterion("billing_time is null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNotNull() {
            addCriterion("billing_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeEqualTo(String value) {
            addCriterion("billing_time =", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotEqualTo(String value) {
            addCriterion("billing_time <>", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThan(String value) {
            addCriterion("billing_time >", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThanOrEqualTo(String value) {
            addCriterion("billing_time >=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThan(String value) {
            addCriterion("billing_time <", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThanOrEqualTo(String value) {
            addCriterion("billing_time <=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLike(String value) {
            addCriterion("billing_time like", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotLike(String value) {
            addCriterion("billing_time not like", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIn(List<String> values) {
            addCriterion("billing_time in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotIn(List<String> values) {
            addCriterion("billing_time not in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeBetween(String value1, String value2) {
            addCriterion("billing_time between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotBetween(String value1, String value2) {
            addCriterion("billing_time not between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIsNull() {
            addCriterion("assets_name is null");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIsNotNull() {
            addCriterion("assets_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsNameEqualTo(String value) {
            addCriterion("assets_name =", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotEqualTo(String value) {
            addCriterion("assets_name <>", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameGreaterThan(String value) {
            addCriterion("assets_name >", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameGreaterThanOrEqualTo(String value) {
            addCriterion("assets_name >=", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLessThan(String value) {
            addCriterion("assets_name <", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLessThanOrEqualTo(String value) {
            addCriterion("assets_name <=", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLike(String value) {
            addCriterion("assets_name like", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotLike(String value) {
            addCriterion("assets_name not like", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIn(List<String> values) {
            addCriterion("assets_name in", values, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotIn(List<String> values) {
            addCriterion("assets_name not in", values, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameBetween(String value1, String value2) {
            addCriterion("assets_name between", value1, value2, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotBetween(String value1, String value2) {
            addCriterion("assets_name not between", value1, value2, "assetsName");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeIsNull() {
            addCriterion("last_book_type_code is null");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeIsNotNull() {
            addCriterion("last_book_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeEqualTo(String value) {
            addCriterion("last_book_type_code =", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeNotEqualTo(String value) {
            addCriterion("last_book_type_code <>", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeGreaterThan(String value) {
            addCriterion("last_book_type_code >", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("last_book_type_code >=", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeLessThan(String value) {
            addCriterion("last_book_type_code <", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("last_book_type_code <=", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeLike(String value) {
            addCriterion("last_book_type_code like", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeNotLike(String value) {
            addCriterion("last_book_type_code not like", value, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeIn(List<String> values) {
            addCriterion("last_book_type_code in", values, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeNotIn(List<String> values) {
            addCriterion("last_book_type_code not in", values, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeBetween(String value1, String value2) {
            addCriterion("last_book_type_code between", value1, value2, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastBookTypeCodeNotBetween(String value1, String value2) {
            addCriterion("last_book_type_code not between", value1, value2, "lastBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeIsNull() {
            addCriterion("last_company_code is null");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeIsNotNull() {
            addCriterion("last_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeEqualTo(String value) {
            addCriterion("last_company_code =", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeNotEqualTo(String value) {
            addCriterion("last_company_code <>", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeGreaterThan(String value) {
            addCriterion("last_company_code >", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("last_company_code >=", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeLessThan(String value) {
            addCriterion("last_company_code <", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("last_company_code <=", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeLike(String value) {
            addCriterion("last_company_code like", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeNotLike(String value) {
            addCriterion("last_company_code not like", value, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeIn(List<String> values) {
            addCriterion("last_company_code in", values, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeNotIn(List<String> values) {
            addCriterion("last_company_code not in", values, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeBetween(String value1, String value2) {
            addCriterion("last_company_code between", value1, value2, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("last_company_code not between", value1, value2, "lastCompanyCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeIsNull() {
            addCriterion("last_dept_code is null");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeIsNotNull() {
            addCriterion("last_dept_code is not null");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeEqualTo(String value) {
            addCriterion("last_dept_code =", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeNotEqualTo(String value) {
            addCriterion("last_dept_code <>", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeGreaterThan(String value) {
            addCriterion("last_dept_code >", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("last_dept_code >=", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeLessThan(String value) {
            addCriterion("last_dept_code <", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeLessThanOrEqualTo(String value) {
            addCriterion("last_dept_code <=", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeLike(String value) {
            addCriterion("last_dept_code like", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeNotLike(String value) {
            addCriterion("last_dept_code not like", value, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeIn(List<String> values) {
            addCriterion("last_dept_code in", values, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeNotIn(List<String> values) {
            addCriterion("last_dept_code not in", values, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeBetween(String value1, String value2) {
            addCriterion("last_dept_code between", value1, value2, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastDeptCodeNotBetween(String value1, String value2) {
            addCriterion("last_dept_code not between", value1, value2, "lastDeptCode");
            return (Criteria) this;
        }

        public Criteria andLastAddressIsNull() {
            addCriterion("last_address is null");
            return (Criteria) this;
        }

        public Criteria andLastAddressIsNotNull() {
            addCriterion("last_address is not null");
            return (Criteria) this;
        }

        public Criteria andLastAddressEqualTo(String value) {
            addCriterion("last_address =", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressNotEqualTo(String value) {
            addCriterion("last_address <>", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressGreaterThan(String value) {
            addCriterion("last_address >", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressGreaterThanOrEqualTo(String value) {
            addCriterion("last_address >=", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressLessThan(String value) {
            addCriterion("last_address <", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressLessThanOrEqualTo(String value) {
            addCriterion("last_address <=", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressLike(String value) {
            addCriterion("last_address like", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressNotLike(String value) {
            addCriterion("last_address not like", value, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressIn(List<String> values) {
            addCriterion("last_address in", values, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressNotIn(List<String> values) {
            addCriterion("last_address not in", values, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressBetween(String value1, String value2) {
            addCriterion("last_address between", value1, value2, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andLastAddressNotBetween(String value1, String value2) {
            addCriterion("last_address not between", value1, value2, "lastAddress");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeIsNull() {
            addCriterion("new_book_type_code is null");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeIsNotNull() {
            addCriterion("new_book_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeEqualTo(String value) {
            addCriterion("new_book_type_code =", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeNotEqualTo(String value) {
            addCriterion("new_book_type_code <>", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeGreaterThan(String value) {
            addCriterion("new_book_type_code >", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("new_book_type_code >=", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeLessThan(String value) {
            addCriterion("new_book_type_code <", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("new_book_type_code <=", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeLike(String value) {
            addCriterion("new_book_type_code like", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeNotLike(String value) {
            addCriterion("new_book_type_code not like", value, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeIn(List<String> values) {
            addCriterion("new_book_type_code in", values, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeNotIn(List<String> values) {
            addCriterion("new_book_type_code not in", values, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeBetween(String value1, String value2) {
            addCriterion("new_book_type_code between", value1, value2, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewBookTypeCodeNotBetween(String value1, String value2) {
            addCriterion("new_book_type_code not between", value1, value2, "newBookTypeCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeIsNull() {
            addCriterion("new_company_code is null");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeIsNotNull() {
            addCriterion("new_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeEqualTo(String value) {
            addCriterion("new_company_code =", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeNotEqualTo(String value) {
            addCriterion("new_company_code <>", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeGreaterThan(String value) {
            addCriterion("new_company_code >", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("new_company_code >=", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeLessThan(String value) {
            addCriterion("new_company_code <", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("new_company_code <=", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeLike(String value) {
            addCriterion("new_company_code like", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeNotLike(String value) {
            addCriterion("new_company_code not like", value, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeIn(List<String> values) {
            addCriterion("new_company_code in", values, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeNotIn(List<String> values) {
            addCriterion("new_company_code not in", values, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeBetween(String value1, String value2) {
            addCriterion("new_company_code between", value1, value2, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("new_company_code not between", value1, value2, "newCompanyCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeIsNull() {
            addCriterion("new_dept_code is null");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeIsNotNull() {
            addCriterion("new_dept_code is not null");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeEqualTo(String value) {
            addCriterion("new_dept_code =", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeNotEqualTo(String value) {
            addCriterion("new_dept_code <>", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeGreaterThan(String value) {
            addCriterion("new_dept_code >", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("new_dept_code >=", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeLessThan(String value) {
            addCriterion("new_dept_code <", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeLessThanOrEqualTo(String value) {
            addCriterion("new_dept_code <=", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeLike(String value) {
            addCriterion("new_dept_code like", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeNotLike(String value) {
            addCriterion("new_dept_code not like", value, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeIn(List<String> values) {
            addCriterion("new_dept_code in", values, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeNotIn(List<String> values) {
            addCriterion("new_dept_code not in", values, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeBetween(String value1, String value2) {
            addCriterion("new_dept_code between", value1, value2, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewDeptCodeNotBetween(String value1, String value2) {
            addCriterion("new_dept_code not between", value1, value2, "newDeptCode");
            return (Criteria) this;
        }

        public Criteria andNewAddressIsNull() {
            addCriterion("new_address is null");
            return (Criteria) this;
        }

        public Criteria andNewAddressIsNotNull() {
            addCriterion("new_address is not null");
            return (Criteria) this;
        }

        public Criteria andNewAddressEqualTo(String value) {
            addCriterion("new_address =", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressNotEqualTo(String value) {
            addCriterion("new_address <>", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressGreaterThan(String value) {
            addCriterion("new_address >", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressGreaterThanOrEqualTo(String value) {
            addCriterion("new_address >=", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressLessThan(String value) {
            addCriterion("new_address <", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressLessThanOrEqualTo(String value) {
            addCriterion("new_address <=", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressLike(String value) {
            addCriterion("new_address like", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressNotLike(String value) {
            addCriterion("new_address not like", value, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressIn(List<String> values) {
            addCriterion("new_address in", values, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressNotIn(List<String> values) {
            addCriterion("new_address not in", values, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressBetween(String value1, String value2) {
            addCriterion("new_address between", value1, value2, "newAddress");
            return (Criteria) this;
        }

        public Criteria andNewAddressNotBetween(String value1, String value2) {
            addCriterion("new_address not between", value1, value2, "newAddress");
            return (Criteria) this;
        }

        public Criteria andLastPriceIsNull() {
            addCriterion("last_price is null");
            return (Criteria) this;
        }

        public Criteria andLastPriceIsNotNull() {
            addCriterion("last_price is not null");
            return (Criteria) this;
        }

        public Criteria andLastPriceEqualTo(String value) {
            addCriterion("last_price =", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceNotEqualTo(String value) {
            addCriterion("last_price <>", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceGreaterThan(String value) {
            addCriterion("last_price >", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceGreaterThanOrEqualTo(String value) {
            addCriterion("last_price >=", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceLessThan(String value) {
            addCriterion("last_price <", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceLessThanOrEqualTo(String value) {
            addCriterion("last_price <=", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceLike(String value) {
            addCriterion("last_price like", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceNotLike(String value) {
            addCriterion("last_price not like", value, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceIn(List<String> values) {
            addCriterion("last_price in", values, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceNotIn(List<String> values) {
            addCriterion("last_price not in", values, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceBetween(String value1, String value2) {
            addCriterion("last_price between", value1, value2, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andLastPriceNotBetween(String value1, String value2) {
            addCriterion("last_price not between", value1, value2, "lastPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceIsNull() {
            addCriterion("new_price is null");
            return (Criteria) this;
        }

        public Criteria andNewPriceIsNotNull() {
            addCriterion("new_price is not null");
            return (Criteria) this;
        }

        public Criteria andNewPriceEqualTo(String value) {
            addCriterion("new_price =", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceNotEqualTo(String value) {
            addCriterion("new_price <>", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceGreaterThan(String value) {
            addCriterion("new_price >", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceGreaterThanOrEqualTo(String value) {
            addCriterion("new_price >=", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceLessThan(String value) {
            addCriterion("new_price <", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceLessThanOrEqualTo(String value) {
            addCriterion("new_price <=", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceLike(String value) {
            addCriterion("new_price like", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceNotLike(String value) {
            addCriterion("new_price not like", value, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceIn(List<String> values) {
            addCriterion("new_price in", values, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceNotIn(List<String> values) {
            addCriterion("new_price not in", values, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceBetween(String value1, String value2) {
            addCriterion("new_price between", value1, value2, "newPrice");
            return (Criteria) this;
        }

        public Criteria andNewPriceNotBetween(String value1, String value2) {
            addCriterion("new_price not between", value1, value2, "newPrice");
            return (Criteria) this;
        }

        public Criteria andBillCodeIsNull() {
            addCriterion("bill_code is null");
            return (Criteria) this;
        }

        public Criteria andBillCodeIsNotNull() {
            addCriterion("bill_code is not null");
            return (Criteria) this;
        }

        public Criteria andBillCodeEqualTo(String value) {
            addCriterion("bill_code =", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotEqualTo(String value) {
            addCriterion("bill_code <>", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeGreaterThan(String value) {
            addCriterion("bill_code >", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_code >=", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLessThan(String value) {
            addCriterion("bill_code <", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLessThanOrEqualTo(String value) {
            addCriterion("bill_code <=", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLike(String value) {
            addCriterion("bill_code like", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotLike(String value) {
            addCriterion("bill_code not like", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeIn(List<String> values) {
            addCriterion("bill_code in", values, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotIn(List<String> values) {
            addCriterion("bill_code not in", values, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeBetween(String value1, String value2) {
            addCriterion("bill_code between", value1, value2, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotBetween(String value1, String value2) {
            addCriterion("bill_code not between", value1, value2, "billCode");
            return (Criteria) this;
        }

        public Criteria andVersionIdIsNull() {
            addCriterion("version_id is null");
            return (Criteria) this;
        }

        public Criteria andVersionIdIsNotNull() {
            addCriterion("version_id is not null");
            return (Criteria) this;
        }

        public Criteria andVersionIdEqualTo(Integer value) {
            addCriterion("version_id =", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotEqualTo(Integer value) {
            addCriterion("version_id <>", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdGreaterThan(Integer value) {
            addCriterion("version_id >", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("version_id >=", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdLessThan(Integer value) {
            addCriterion("version_id <", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("version_id <=", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdIn(List<Integer> values) {
            addCriterion("version_id in", values, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotIn(List<Integer> values) {
            addCriterion("version_id not in", values, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("version_id between", value1, value2, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("version_id not between", value1, value2, "versionId");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNull() {
            addCriterion("change_reason is null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIsNotNull() {
            addCriterion("change_reason is not null");
            return (Criteria) this;
        }

        public Criteria andChangeReasonEqualTo(String value) {
            addCriterion("change_reason =", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotEqualTo(String value) {
            addCriterion("change_reason <>", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThan(String value) {
            addCriterion("change_reason >", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonGreaterThanOrEqualTo(String value) {
            addCriterion("change_reason >=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThan(String value) {
            addCriterion("change_reason <", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLessThanOrEqualTo(String value) {
            addCriterion("change_reason <=", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonLike(String value) {
            addCriterion("change_reason like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotLike(String value) {
            addCriterion("change_reason not like", value, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonIn(List<String> values) {
            addCriterion("change_reason in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotIn(List<String> values) {
            addCriterion("change_reason not in", values, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonBetween(String value1, String value2) {
            addCriterion("change_reason between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andChangeReasonNotBetween(String value1, String value2) {
            addCriterion("change_reason not between", value1, value2, "changeReason");
            return (Criteria) this;
        }

        public Criteria andFaCodeIsNull() {
            addCriterion("fa_code is null");
            return (Criteria) this;
        }

        public Criteria andFaCodeIsNotNull() {
            addCriterion("fa_code is not null");
            return (Criteria) this;
        }

        public Criteria andFaCodeEqualTo(String value) {
            addCriterion("fa_code =", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotEqualTo(String value) {
            addCriterion("fa_code <>", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeGreaterThan(String value) {
            addCriterion("fa_code >", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("fa_code >=", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeLessThan(String value) {
            addCriterion("fa_code <", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeLessThanOrEqualTo(String value) {
            addCriterion("fa_code <=", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeLike(String value) {
            addCriterion("fa_code like", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotLike(String value) {
            addCriterion("fa_code not like", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeIn(List<String> values) {
            addCriterion("fa_code in", values, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotIn(List<String> values) {
            addCriterion("fa_code not in", values, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeBetween(String value1, String value2) {
            addCriterion("fa_code between", value1, value2, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotBetween(String value1, String value2) {
            addCriterion("fa_code not between", value1, value2, "faCode");
            return (Criteria) this;
        }

        public Criteria andAssetIdIsNull() {
            addCriterion("asset_id is null");
            return (Criteria) this;
        }

        public Criteria andAssetIdIsNotNull() {
            addCriterion("asset_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssetIdEqualTo(Integer value) {
            addCriterion("asset_id =", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdNotEqualTo(Integer value) {
            addCriterion("asset_id <>", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdGreaterThan(Integer value) {
            addCriterion("asset_id >", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("asset_id >=", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdLessThan(Integer value) {
            addCriterion("asset_id <", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdLessThanOrEqualTo(Integer value) {
            addCriterion("asset_id <=", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdIn(List<Integer> values) {
            addCriterion("asset_id in", values, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdNotIn(List<Integer> values) {
            addCriterion("asset_id not in", values, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdBetween(Integer value1, Integer value2) {
            addCriterion("asset_id between", value1, value2, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdNotBetween(Integer value1, Integer value2) {
            addCriterion("asset_id not between", value1, value2, "assetId");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}