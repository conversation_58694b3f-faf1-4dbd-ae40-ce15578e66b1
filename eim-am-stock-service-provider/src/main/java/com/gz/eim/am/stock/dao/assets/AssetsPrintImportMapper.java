package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.StockAssetsPrintImport;
import com.gz.eim.am.stock.entity.StockAssetsPrintImportExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AssetsPrintImportMapper {

     /**
       * @param:
       * @description: 批量查询
       * @return:
       * @author: <EMAIL>
       * @date: 2021/12/22
       */
    int batchInsert(List<StockAssetsPrintImport> stockAssetsPrintImportList);

    /**
     * @param:
     * @description: 批量更新
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    int batchUpdate(List<StockAssetsPrintImport> stockAssetsPrintImportList);

    /**
     * @param:
     * @description: 批量更新
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    int batchUpdateByNo(List<StockAssetsPrintImport> stockAssetsPrintImportList);
}