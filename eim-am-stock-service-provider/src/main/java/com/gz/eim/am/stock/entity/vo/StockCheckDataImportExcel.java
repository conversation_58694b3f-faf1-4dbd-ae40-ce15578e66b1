package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * @author: weijunjie
 * @date: 2020/11/18
 * @description
 */
@Slf4j
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockCheckDataImportExcel implements ExportModel {

    @ExportField(name = "资产编码")
    private String assetsCode;

    @ExportField(name = "是否差异")
    private String differenceImport;

    private Integer difference;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getDifferenceImport() {
        return differenceImport;
    }

    public void setDifferenceImport(String differenceImport) {
        this.differenceImport = differenceImport;
    }

    public Integer getDifference() {
        return difference;
    }

    public void setDifference(Integer difference) {
        this.difference = difference;
    }

    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        StockCheckDataImportExcel that = (StockCheckDataImportExcel) o;
        return Objects.equals(assetsCode, that.assetsCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(assetsCode);
    }

    /**
     * 将类型转换
     */
    public void copyFieldValue(){
        try{
            this.difference = new Integer(this.differenceImport);
        }catch (Exception e){
            log.info("盘点上传资产："+this.assetsCode+"对应的异常不是规定的类型或者为空");
            throw new ServiceUncheckedException("盘点上传资产："+this.assetsCode+"对应的异常不是规定的类型或者为空");
        }
    }
}
