package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class CheckDifferenceListLine {
    private Long lineId;

    private Long headId;

    private String snapshotAssetsCode;

    private String snapshotSnNo;

    private String assetsKeeper;

    private String snapshotAssetsName;

    private Integer snapshotAssetsStatus;

    private Integer snapshotAssetsConditions;

    private String snapshotAssetsHolder;

    private String snapshotWarehouseCode;

    private Integer realAssetsStatus;

    private Integer realAssetsConditions;

    private String realAssetsHolder;

    private String realHolderAddress;

    private String realWarehouseCode;

    private String realBrand;

    private String realModel;

    private String realCpu;

    private String realRamMemory;

    private String realHardDisk;

    private String realPicturesUrl;

    private Integer snapshotNumber;

    private Integer realNumber;

    private Integer difference;

    private String diffMessage;

    private String checkResult;

    private Integer adviseHandleMethod;

    private Integer actualHandleMethod;

    private BigDecimal estimatedAmount;

    private BigDecimal netValue;

    private String companyCode;

    private Integer timeLeft;

    private String suppliesCode;

    private Integer adjustFlag;

    private String bizNo;

    private Integer approveCheckFlag;

    private String adjustAssetsCode;

    private String adjustSnCode;

    private String remark;

    private Integer offlineCheckFlag;

    private Date checkDate;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public Long getHeadId() {
        return headId;
    }

    public void setHeadId(Long headId) {
        this.headId = headId;
    }

    public String getSnapshotAssetsCode() {
        return snapshotAssetsCode;
    }

    public void setSnapshotAssetsCode(String snapshotAssetsCode) {
        this.snapshotAssetsCode = snapshotAssetsCode == null ? null : snapshotAssetsCode.trim();
    }

    public String getSnapshotSnNo() {
        return snapshotSnNo;
    }

    public void setSnapshotSnNo(String snapshotSnNo) {
        this.snapshotSnNo = snapshotSnNo == null ? null : snapshotSnNo.trim();
    }

    public String getAssetsKeeper() {
        return assetsKeeper;
    }

    public void setAssetsKeeper(String assetsKeeper) {
        this.assetsKeeper = assetsKeeper == null ? null : assetsKeeper.trim();
    }

    public String getSnapshotAssetsName() {
        return snapshotAssetsName;
    }

    public void setSnapshotAssetsName(String snapshotAssetsName) {
        this.snapshotAssetsName = snapshotAssetsName == null ? null : snapshotAssetsName.trim();
    }

    public Integer getSnapshotAssetsStatus() {
        return snapshotAssetsStatus;
    }

    public void setSnapshotAssetsStatus(Integer snapshotAssetsStatus) {
        this.snapshotAssetsStatus = snapshotAssetsStatus;
    }

    public Integer getSnapshotAssetsConditions() {
        return snapshotAssetsConditions;
    }

    public void setSnapshotAssetsConditions(Integer snapshotAssetsConditions) {
        this.snapshotAssetsConditions = snapshotAssetsConditions;
    }

    public String getSnapshotAssetsHolder() {
        return snapshotAssetsHolder;
    }

    public void setSnapshotAssetsHolder(String snapshotAssetsHolder) {
        this.snapshotAssetsHolder = snapshotAssetsHolder == null ? null : snapshotAssetsHolder.trim();
    }

    public String getSnapshotWarehouseCode() {
        return snapshotWarehouseCode;
    }

    public void setSnapshotWarehouseCode(String snapshotWarehouseCode) {
        this.snapshotWarehouseCode = snapshotWarehouseCode == null ? null : snapshotWarehouseCode.trim();
    }

    public Integer getRealAssetsStatus() {
        return realAssetsStatus;
    }

    public void setRealAssetsStatus(Integer realAssetsStatus) {
        this.realAssetsStatus = realAssetsStatus;
    }

    public Integer getRealAssetsConditions() {
        return realAssetsConditions;
    }

    public void setRealAssetsConditions(Integer realAssetsConditions) {
        this.realAssetsConditions = realAssetsConditions;
    }

    public String getRealAssetsHolder() {
        return realAssetsHolder;
    }

    public void setRealAssetsHolder(String realAssetsHolder) {
        this.realAssetsHolder = realAssetsHolder == null ? null : realAssetsHolder.trim();
    }

    public String getRealHolderAddress() {
        return realHolderAddress;
    }

    public void setRealHolderAddress(String realHolderAddress) {
        this.realHolderAddress = realHolderAddress == null ? null : realHolderAddress.trim();
    }

    public String getRealWarehouseCode() {
        return realWarehouseCode;
    }

    public void setRealWarehouseCode(String realWarehouseCode) {
        this.realWarehouseCode = realWarehouseCode == null ? null : realWarehouseCode.trim();
    }

    public String getRealBrand() {
        return realBrand;
    }

    public void setRealBrand(String realBrand) {
        this.realBrand = realBrand == null ? null : realBrand.trim();
    }

    public String getRealModel() {
        return realModel;
    }

    public void setRealModel(String realModel) {
        this.realModel = realModel == null ? null : realModel.trim();
    }

    public String getRealCpu() {
        return realCpu;
    }

    public void setRealCpu(String realCpu) {
        this.realCpu = realCpu == null ? null : realCpu.trim();
    }

    public String getRealRamMemory() {
        return realRamMemory;
    }

    public void setRealRamMemory(String realRamMemory) {
        this.realRamMemory = realRamMemory == null ? null : realRamMemory.trim();
    }

    public String getRealHardDisk() {
        return realHardDisk;
    }

    public void setRealHardDisk(String realHardDisk) {
        this.realHardDisk = realHardDisk == null ? null : realHardDisk.trim();
    }

    public String getRealPicturesUrl() {
        return realPicturesUrl;
    }

    public void setRealPicturesUrl(String realPicturesUrl) {
        this.realPicturesUrl = realPicturesUrl == null ? null : realPicturesUrl.trim();
    }

    public Integer getSnapshotNumber() {
        return snapshotNumber;
    }

    public void setSnapshotNumber(Integer snapshotNumber) {
        this.snapshotNumber = snapshotNumber;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public Integer getDifference() {
        return difference;
    }

    public void setDifference(Integer difference) {
        this.difference = difference;
    }

    public String getDiffMessage() {
        return diffMessage;
    }

    public void setDiffMessage(String diffMessage) {
        this.diffMessage = diffMessage == null ? null : diffMessage.trim();
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult == null ? null : checkResult.trim();
    }

    public Integer getAdviseHandleMethod() {
        return adviseHandleMethod;
    }

    public void setAdviseHandleMethod(Integer adviseHandleMethod) {
        this.adviseHandleMethod = adviseHandleMethod;
    }

    public Integer getActualHandleMethod() {
        return actualHandleMethod;
    }

    public void setActualHandleMethod(Integer actualHandleMethod) {
        this.actualHandleMethod = actualHandleMethod;
    }

    public BigDecimal getEstimatedAmount() {
        return estimatedAmount;
    }

    public void setEstimatedAmount(BigDecimal estimatedAmount) {
        this.estimatedAmount = estimatedAmount;
    }

    public BigDecimal getNetValue() {
        return netValue;
    }

    public void setNetValue(BigDecimal netValue) {
        this.netValue = netValue;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public Integer getTimeLeft() {
        return timeLeft;
    }

    public void setTimeLeft(Integer timeLeft) {
        this.timeLeft = timeLeft;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode == null ? null : suppliesCode.trim();
    }

    public Integer getAdjustFlag() {
        return adjustFlag;
    }

    public void setAdjustFlag(Integer adjustFlag) {
        this.adjustFlag = adjustFlag;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo == null ? null : bizNo.trim();
    }

    public Integer getApproveCheckFlag() {
        return approveCheckFlag;
    }

    public void setApproveCheckFlag(Integer approveCheckFlag) {
        this.approveCheckFlag = approveCheckFlag;
    }

    public String getAdjustAssetsCode() {
        return adjustAssetsCode;
    }

    public void setAdjustAssetsCode(String adjustAssetsCode) {
        this.adjustAssetsCode = adjustAssetsCode == null ? null : adjustAssetsCode.trim();
    }

    public String getAdjustSnCode() {
        return adjustSnCode;
    }

    public void setAdjustSnCode(String adjustSnCode) {
        this.adjustSnCode = adjustSnCode == null ? null : adjustSnCode.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getOfflineCheckFlag() {
        return offlineCheckFlag;
    }

    public void setOfflineCheckFlag(Integer offlineCheckFlag) {
        this.offlineCheckFlag = offlineCheckFlag;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}