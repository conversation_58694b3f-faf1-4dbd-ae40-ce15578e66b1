package com.gz.eim.am.stock.util.excel;

import com.fuu.eim.tool.excel.constant.ExportType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @discription 导入字段配置注解
 * @datetime 2020-04-17
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ImportFieldPlus {
    String name();

    String required() default "N";  // Y/N

    ExportType type() default ExportType.STRING;

    String format() default "";
}
