package com.gz.eim.am.stock.service.authority;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.authority.StockManageRoleReqDTO;
import com.gz.eim.am.stock.entity.StockManageRole;
import com.gz.eim.am.stock.entity.vo.StockManageRoleImportExcel;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.ws.Response;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/5/17
 * @description
 */
public interface StockManageRoleUpgradeService {

    /**
     * 创建角色逻辑处理
     * @param stockManageRoleReqDTO
     * @param user
     * @return
     */
    ResponseData save(StockManageRoleReqDTO stockManageRoleReqDTO, JwtUser user);

    /**
     * 分页查询角色列表
     * @param stockManageRoleReqDTO
     * @return
     */
    ResponseData selectManageRoles(StockManageRoleReqDTO stockManageRoleReqDTO);

    /**
     * 根据角色id查询角色
     * @param roleId
     * @return
     */
    StockManageRole selectManageRoleById(long roleId);

    /**
     * 根据角色名称列表查询当前有效的角色对象列表
     * @param roleName
     * @return
     */
    List<StockManageRole> selectManageRolesByRoleNames(List<String> roleName);

    /**
     * 根据角色id列表查询当前角色对象列表
     * @param roleIds
     * @return
     */
    List<StockManageRole> selectManageRolesByRoleIds(List<Long> roleIds);

    /**
     * 批量新增角色
     * @param file
     * @return
     */
    ResponseData batchSaveManageRole(MultipartFile file) throws Exception;
}
