package com.gz.eim.am.stock.service.assets;

import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationConfigReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsCompensationConfig;
import java.util.List;

/**
   * @description: 资产赔偿配置
   * @author: <EMAIL>
   * @date: 2022/2/15
   */
public interface StockAssetsCompensationConfigService {

    /**
     * @param: stockAssetsCompensationConfigReqDTO
     * @description: 查询资产赔偿集合
     * @return: List<StockAssetsCompensationConfigRespDTO>
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    List<StockAssetsCompensationConfig> selectStockAssetsCompensationConfigList(StockAssetsCompensationConfigReqDTO stockAssetsCompensationConfigReqDTO);
}
