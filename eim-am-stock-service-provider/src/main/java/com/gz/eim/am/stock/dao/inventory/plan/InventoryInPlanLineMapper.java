package com.gz.eim.am.stock.dao.inventory.plan;

import com.gz.eim.am.stock.dto.request.order.plan.PurchaseOrderCancelLineDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryPlanAssetRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInPlanHead;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLine;
import com.gz.eim.am.stock.entity.vo.StockInventoryInPlanLineInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/12/12
 * @description:
 */
public interface InventoryInPlanLineMapper {
    /**
     * 插入单条返回主键
     *
     * @param stockInventoryInPlanLine
     * @return
     */
    int insert(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * 批量插入计划入库单行
     *
     * @param stockInventoryInPlanLineList
     * @return
     */
    Integer batchInsertStockInventoryPlanLine(List<StockInventoryInPlanLine> stockInventoryInPlanLineList);

    /**
     * 根据参数获取计划入库单行
     *
     * @param stockInventoryInPlanLine
     * @return
     */
    List<InventoryInPlanLineRespDTO> selectBySelective(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * 根据参数获取计划入库单行关联资产行
     *
     * @param stockInventoryInPlanLine
     * @return
     */
    List<InventoryPlanAssetRespDTO> selectInventoryPlanAssetRespDTOBySelective(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * 批量插入计划入库单行
     *
     * @param newSubDetailList
     * @return
     */
    int batchInsertStockInventoryPlanLineInfo(List<StockInventoryInPlanLineInfo> newSubDetailList);

    /**
     * 分组行id，入库状态，count，查询到每个行已入库的数量
     * @param inventoryHeadId
     * @return
     */
    List<StockInventoryInPlanLine> selectLinesGroupByIdAndStatus(Long inventoryHeadId);

    /**
     * 根据条件查询不能被取消的申请单行数据
     * @param inventoryPurchaseOrderCancelLineDTOs
     * @param type
     * @return
     */
    List<PurchaseOrderCancelLineDTO> selectNoCancelLines(@Param("inventoryPurchaseOrderCancelLineDTOs") List<PurchaseOrderCancelLineDTO> inventoryPurchaseOrderCancelLineDTOs, @Param("type") Integer type);

    /**
     * 根据验收单行编号更新行状态
     * @param receiveItemNos
     * @param status
     * @return
     */
    Integer updateStatusByReceiveItemNos(@Param("receiveItemNos") List<String> receiveItemNos,@Param("status") Integer status);

    /**
     * 根据条件更新申请单行数据
     * @param inventoryPurchaseOrderCancelLineDTOs
     * @param empId
     * @return
     */
    Integer updateLineStatusByPurchaseDto(@Param("inventoryPurchaseOrderCancelLineDTOs") List<PurchaseOrderCancelLineDTO> inventoryPurchaseOrderCancelLineDTOs,@Param("empId")String empId);

    /**
     * @param: list
     * @description: 批量更新逻辑
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/3/3
     */
    int batchUpdate(List<StockInventoryInPlanLine> list);
}
