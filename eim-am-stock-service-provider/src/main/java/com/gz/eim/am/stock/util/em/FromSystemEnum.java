package com.gz.eim.am.stock.util.em;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/07/02
 */
public enum FromSystemEnum {


    /**
     * 激励平台
     */
    WELFARE("WELFARE", "激励平台"),

    ;

    FromSystemEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;
    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
