package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockCardSuppliesDiscountDetail {
    private Long id;

    private String discountNo;

    private String convertSuppliesCode;

    private String switchSuppliesCode;

    private Integer convertNumber;

    private BigDecimal convertResult;

    private BigDecimal discountScale;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDiscountNo() {
        return discountNo;
    }

    public void setDiscountNo(String discountNo) {
        this.discountNo = discountNo == null ? null : discountNo.trim();
    }

    public String getConvertSuppliesCode() {
        return convertSuppliesCode;
    }

    public void setConvertSuppliesCode(String convertSuppliesCode) {
        this.convertSuppliesCode = convertSuppliesCode == null ? null : convertSuppliesCode.trim();
    }

    public String getSwitchSuppliesCode() {
        return switchSuppliesCode;
    }

    public void setSwitchSuppliesCode(String switchSuppliesCode) {
        this.switchSuppliesCode = switchSuppliesCode == null ? null : switchSuppliesCode.trim();
    }

    public Integer getConvertNumber() {
        return convertNumber;
    }

    public void setConvertNumber(Integer convertNumber) {
        this.convertNumber = convertNumber;
    }

    public BigDecimal getConvertResult() {
        return convertResult;
    }

    public void setConvertResult(BigDecimal convertResult) {
        this.convertResult = convertResult;
    }

    public BigDecimal getDiscountScale() {
        return discountScale;
    }

    public void setDiscountScale(BigDecimal discountScale) {
        this.discountScale = discountScale;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}