package com.gz.eim.am.stock.service.impl.assets;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.support.util.DateUtils;
import com.guazi.avro.finance.apply.Common;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.MetaDataConstants;
import com.gz.eim.am.stock.constant.PropertyConstants;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecord;
import com.gz.eim.am.stock.entity.ambase.MetaData;
import com.gz.eim.am.stock.entity.ambase.PsBusJobdataInactiveTbl;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.vo.StockAssetsCompensationRecordEmailEntity;
import com.gz.eim.am.stock.entity.vo.download.ExportLicenseOverdueReminderEntity;
import com.gz.eim.am.stock.entity.vo.download.ExportSealOverdueReminderEntity;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.ambase.PsBusJobdataInactiveTblService;
import com.gz.eim.am.stock.service.assets.StockAssetsCompensationRecordService;
import com.gz.eim.am.stock.service.assets.StockAssetsLeaveNoReturnService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.util.common.ExcelUtils;
import com.gz.eim.am.stock.util.common.FileUtil;
import com.gz.eim.am.stock.util.common.SendGuaGuaMessageUtil;
import com.gz.eim.am.stock.util.em.AssetsCompensationEnum;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.MetaDataEnum;
import com.gz.eim.am.stock.util.em.StockAssetsCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: yangjifan1
 * @date: 2022/03/29
 * @description: 离职未归还资产实现
 */
@Service
@Slf4j
public class StockAssetsLeaveNoReturnServiceImpl implements StockAssetsLeaveNoReturnService {

    @Autowired
    private FileUtil fileUtil;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private PsBusJobdataInactiveTblService psBusJobdataInactiveTblService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private StockAssetsCompensationRecordService stockAssetsCompensationRecordService;
    

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData getLeaveNoReturnAssets(String leaveDateStr) throws ParseException {
        Date leaveDate = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isBlank(leaveDateStr)) {
            leaveDate = DateUtils.dateAddDays(sdf.parse(sdf.format(new Date())), -1);
        }else {
            leaveDate = sdf.parse(leaveDateStr);
        }

        //获取当天的离职人员
        List<PsBusJobdataInactiveTbl> leaveUserListByLeaveDate = psBusJobdataInactiveTblService.getLeaveUserListByLeaveDate(leaveDate);
        if(CollectionUtils.isEmpty(leaveUserListByLeaveDate)){
            return ResponseData.createSuccessResult();
        }
        List<String> empIdList = new ArrayList<>(leaveUserListByLeaveDate.size());
        leaveUserListByLeaveDate.forEach(psBusJobdataInactiveTbl -> {
            empIdList.add(psBusJobdataInactiveTbl.getEmplid());
        });

        //获取是否有未归还的资产
        AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
        assetsSearchDTO.setHolderList(empIdList);
        assetsSearchDTO.setStatus(AssetsEnum.statusType.USED.getValue());
        List<String> categoryCodeList = new ArrayList<>();
        categoryCodeList.add(StockAssetsCategoryEnum.category.LICENSE.getValue());
        categoryCodeList.add(StockAssetsCategoryEnum.category.SEAL.getValue());
        categoryCodeList.add(StockAssetsCategoryEnum.category.FINANCE_LICENCE.getValue());
        categoryCodeList.add(StockAssetsCategoryEnum.category.FINANCE_SEAL.getValue());
        assetsSearchDTO.setNoCategoryCodeList(categoryCodeList);
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssetsListByAssetsSearchDTO(assetsSearchDTO);
        if(CollectionUtils.isEmpty(stockAssetsList)){
            return ResponseData.createSuccessResult();
        }
        //判断是否在资产赔偿表生成过，如果没有在赔偿记录表里插入
        StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO = new StockAssetsCompensationRecordReqDTO();
        stockAssetsCompensationRecordReqDTO.setLeaveTime(leaveDate);
        stockAssetsCompensationRecordReqDTO.setDelFlag(MetaDataEnum.yesOrNo.NO.getValue());
        List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList = stockAssetsCompensationRecordService.selectStockAssetsCompensationRecordList(stockAssetsCompensationRecordReqDTO);
        if(stockAssetsCompensationRecordList.size() == stockAssetsList.size()){
            return ResponseData.createSuccessResult();
        }
        Set<String> existLeaveAssetsRecord = stockAssetsCompensationRecordList.stream().map(stockAssetsCompensationRecord -> stockAssetsCompensationRecord.getCompensationUser() + stockAssetsCompensationRecord.getAssetsCode()).collect(Collectors.toSet());
        List<StockAssetsCompensationRecord> addStockAssetsCompensationRecordList = new ArrayList<>();
        Date currentDate = new Date();
        for(StockAssets stockAssets:stockAssetsList){
            if(existLeaveAssetsRecord.contains(stockAssets.getHolder() + stockAssets.getAssetsCode())){
                continue;
            }
            StockAssetsCompensationRecord stockAssetsCompensationRecord = new StockAssetsCompensationRecord();
            stockAssetsCompensationRecord.setAssetsCode(stockAssets.getAssetsCode());
            stockAssetsCompensationRecord.setIsReturn(AssetsCompensationEnum.isReturn.NO_RETURN.getValue());
            stockAssetsCompensationRecord.setCompensationUser(stockAssets.getHolder());
            stockAssetsCompensationRecord.setLeaveTime(leaveDate);
            stockAssetsCompensationRecord.setIsNeedScrap(MetaDataEnum.yesOrNo.YES.getValue());
            stockAssetsCompensationRecord.setSyncPaySystemStatus(AssetsCompensationEnum.syncPaySystemStatus.NO_SYNC.getValue());
            stockAssetsCompensationRecord.setDataSource(AssetsCompensationEnum.dataResource.SYSTEM.getValue());
            stockAssetsCompensationRecord.setCreatedBy(CommonConstant.SYSTEM);
            stockAssetsCompensationRecord.setCreatedAt(currentDate);
            stockAssetsCompensationRecord.setUpdatedBy(CommonConstant.SYSTEM);
            stockAssetsCompensationRecord.setUpdatedAt(currentDate);
            stockAssetsCompensationRecord.setDelFlag(MetaDataEnum.yesOrNo.NO.getValue());
            stockAssetsCompensationRecord.setCompensationMoney(BigDecimal.ZERO);
            stockAssetsCompensationRecord.setAdviseCompensationMoney(BigDecimal.ZERO);
            addStockAssetsCompensationRecordList.add(stockAssetsCompensationRecord);
        }
        log.info("StockAssetsLeaveNoReturnServiceImpl.getLeaveNoReturnAssets 需插入资产赔偿表"+addStockAssetsCompensationRecordList.size()+"条数据,addStockAssetsCompensationRecordList = {}", JSONObject.toJSONString(addStockAssetsCompensationRecordList));
        int count = stockAssetsCompensationRecordService.batchInsert(addStockAssetsCompensationRecordList);
        log.info("StockAssetsLeaveNoReturnServiceImpl.getLeaveNoReturnAssets 实际插入资产赔偿表"+count+"条数据");
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData queryLastMonthNoReturnAssetsAndSendEmail() throws Exception {
        // 查询配置项
        Map<String, String> stringStringMap = ambaseCommonService.selectMetaDataMapByParam(MetaDataConstants.STOCK_MODULE_ASSETS, MetaDataConstants.LEAVE_COMPENSATION);
        String startDateOfMonth;
        String endDateOfMonth;
        if (MapUtils.isEmpty(stringStringMap) || StringUtils.isBlank(startDateOfMonth = stringStringMap.get(MetaDataConstants.START_DATA_OF_MONTH)) || StringUtils.isBlank(endDateOfMonth = stringStringMap.get(MetaDataConstants.END_DATA_OF_MONTH))) {
            log.error("meta_date表中leave_compensation数据未配置");
            return ResponseData.createSuccessResult();
        }
        Date date = DateUtils.dateParse(DateUtils.dateFormat(new Date(), DateUtils.MONTH_PATTERN), DateUtils.MONTH_PATTERN);
//        date = DateUtils.dateAddMonths(date, -CommonConstant.NUMBER_ONE);
        StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO = new StockAssetsCompensationRecordReqDTO();
        // 这里转化为yyyy-MM格式以后，默认为每个月的第一天，所以需要减去这一天再加自己配置的内容
        stockAssetsCompensationRecordReqDTO.setLeaveTimeStart(DateUtils.dateAddDays(date, Integer.parseInt(startDateOfMonth) - CommonConstant.NUMBER_ONE));
        stockAssetsCompensationRecordReqDTO.setLeaveTimeEnd(DateUtils.dateAddDays(date, Integer.parseInt(endDateOfMonth) - CommonConstant.NUMBER_ONE));
        stockAssetsCompensationRecordReqDTO.setDelFlag(MetaDataEnum.yesOrNo.NO.getValue());
        stockAssetsCompensationRecordReqDTO.setIsReturn(MetaDataEnum.yesOrNo.NO.getValue());
        stockAssetsCompensationRecordReqDTO.setCompensationMoney(BigDecimal.ZERO);
        List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList = stockAssetsCompensationRecordService.selectStockAssetsCompensationRecordList(stockAssetsCompensationRecordReqDTO);
        if (CollectionUtils.isEmpty(stockAssetsCompensationRecordList)) {
            return ResponseData.createSuccessResult();
        }
        int size = stockAssetsCompensationRecordList.size();
        List<String> assetsCodeList = new ArrayList<>(size);
        List<String> empIdSet = new ArrayList<>();
        List<StockAssetsCompensationRecordEmailEntity> stockAssetsCompensationRecordEmailEntityList = new ArrayList<>(size);
        for (StockAssetsCompensationRecord stockAssetsCompensationRecord : stockAssetsCompensationRecordList) {
            StockAssetsCompensationRecordEmailEntity stockAssetsCompensationRecordEmailEntity = ConvertUtil.convertToType(StockAssetsCompensationRecordEmailEntity.class, stockAssetsCompensationRecord);
            assetsCodeList.add(stockAssetsCompensationRecord.getAssetsCode());
            empIdSet.add(stockAssetsCompensationRecord.getCompensationUser());
            stockAssetsCompensationRecordEmailEntityList.add(stockAssetsCompensationRecordEmailEntity);
        }
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(new ArrayList<>(empIdSet));
        Map<String, SysUser> sysUserMap = new HashMap<>();
        List<String> leaderIdSet = new ArrayList<>();
        for (SysUser sysUser : sysUserList) {
            sysUserMap.put(sysUser.getEmpId(), sysUser);
            leaderIdSet.add(sysUser.getSupervisorId());
        }
        Map<String, SysUser> leaderMap = ambaseCommonService.selectSysUserMapByIds(new ArrayList<>(leaderIdSet));
        for (StockAssetsCompensationRecordEmailEntity stockAssetsCompensationRecordEmailEntity : stockAssetsCompensationRecordEmailEntityList) {
            StockAssets stockAssets = stockAssetsMap.get(stockAssetsCompensationRecordEmailEntity.getAssetsCode());
            if(stockAssets != null){
                stockAssetsCompensationRecordEmailEntity.setAssetsName(stockAssets.getAssetsName());
            }
            SysUser sysUser = sysUserMap.get(stockAssetsCompensationRecordEmailEntity.getCompensationUser());
            if(sysUser != null){
                stockAssetsCompensationRecordEmailEntity.setCompensationUserName(sysUser.getName());
                SysUser leader = leaderMap.get(sysUser.getSupervisorId());
                if(leader != null){
                    stockAssetsCompensationRecordEmailEntity.setSupervisor(leader.getName());
                }
            }
        }
        // 这里需要设置附件
        MockMultipartFile excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(stockAssetsCompensationRecordEmailEntityList, PropertyConstants.BEGIN_OF_MONTH_LEAVE_COMPENSATION_MESSAGE);
        if (excelToMockMultipartFile != null) {
            // 开始上传
            Map<String, String> returnMap = fileUtil.uploadFile(excelToMockMultipartFile);
            if (MapUtils.isEmpty(returnMap)) {
                log.error("StockAssetsLeaveNoReturnServiceImpl.queryLastMonthNoReturnAssetsAndSendEmail，上传文件失败------，资产赔偿内容为：{}", JSON.toJSONString(stockAssetsCompensationRecordEmailEntityList));
                return ResponseData.createSuccessResult();
            } else {
                log.info("StockAssetsLeaveNoReturnServiceImpl.queryLastMonthNoReturnAssetsAndSendEmail，上传文件成功，返回内容为：{}", JSONObject.toJSONString(returnMap));
                String filedId = returnMap.get("fileId");
                if (StringUtils.isBlank(filedId)) {
                    log.error("StockAssetsLeaveNoReturnServiceImpl.queryLastMonthNoReturnAssetsAndSendEmail，上传文件失败------，返回filedId为空，返回内容为：{}", JSONObject.toJSONString(returnMap));
                    return ResponseData.createSuccessResult();
                }
                // 开始发送邮件提醒
                // 发送email
                List<Map<String, String>> content = new ArrayList<>(CommonConstant.NUMBER_ONE);
                Map<String, String> map1 = new HashMap<>();
                map1.put("key", "filedId");
                map1.put("value", filedId);
                content.add(map1);
                Map<String, String> map2 = new HashMap<>();
                map2.put("key", "endDateOfMonth");
                map2.put("value", endDateOfMonth);
                content.add(map2);
                SendGuaGuaMessageUtil.sendMessage(PropertyConstants.LAST_MONTH_NO_RETURN_ASSETS_COMPENSATION_MESSAGE, content);
            }
        }
        return ResponseData.createSuccessResult();
    }
}
