package com.gz.eim.am.stock.dao.order;

import com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutSnSearchReqDTO;
import com.gz.eim.am.stock.dto.response.InventoryFlowRespDTO;
import com.gz.eim.am.stock.dto.response.external.DeliverySuppliesRespDTO;
import com.gz.eim.am.stock.dto.response.manage.StockSuppliesConfigDetailRespDTO;
import com.gz.eim.am.stock.dto.response.order.InventoryOutRespDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO;
import com.gz.eim.am.stock.entity.StockDelivery;
import com.gz.eim.am.stock.entity.vo.StockDeliveryInfo;
import com.gz.eim.am.stock.entity.vo.download.ExportInventoryFlowEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeliveryMapper {

    /**
     * 插入单条
     * @param record
     * @return
     */
    int insert(StockDelivery record);

    /**
     * 入库流水
     * @param dto
     * @return
     */
    List<InventoryFlowRespDTO> selectInventoryOutFlow(InventoryFlowReqDTO dto);

    /**
     * 入库流水计数
     * @param dto
     * @return
     */
    Long countInventoryOutFlow(InventoryFlowReqDTO dto);

    /**
     * 批量删除发货单
     * @param deliveryIds
     * @return
     */
    int batchDeleteByIds(List<Long> deliveryIds);

    /**
     * 批量修改状态
     * @param list
     * @param status
     * @return
     */
    int batchUpdateStatus(@Param("list")List<Long> list,@Param("status")Integer status);

    /**
     * 根据参数查询出库单数量
     * @param inventoryOutSearchReqDTO
     * @return
     */
    Long selectCountByParam(InventoryOutSearchReqDTO inventoryOutSearchReqDTO);

    /**
     * 根据参数查询出库单数量
     * @param inventoryOutSearchReqDTO
     * @return
     */
    List<InventoryOutRespDTO>  selectByPage(InventoryOutSearchReqDTO inventoryOutSearchReqDTO);

    /**
     * 根据出库单id获取出库单
     * @param deliveryId
     * @return
     */
    InventoryOutRespDTO selectInventoryOutRespDTOByDeliveryId(Long deliveryId);

    /**
     * 根据参数查询可出库的出库单数量
     * @param inventoryOutSearchReqDTO
     * @return
     */
    Long selectAccessibleCountByParam(InventoryOutSearchReqDTO inventoryOutSearchReqDTO);

    /**
     * 根据参数查询可出库的出库单
     * @param inventoryOutSearchReqDTO
     * @return
     */
    List<InventoryOutRespDTO>  selectAccessibleByPage(InventoryOutSearchReqDTO inventoryOutSearchReqDTO);

    /**
     * 根据参数查询出库单的物料信息
     * @param snNo
     * @param outStockType
     * @return
     */
    DeliverySuppliesRespDTO selectDeliverySuppliesRespDTOByParam(@Param("snNo")String snNo, @Param("outStockType")Integer outStockType);

    /**
     * 根据参数查询出库单序列号数量
     * @param inventoryOutSnSearchReqDTO
     * @return
     */
    Long selectSnCountByParam(InventoryOutSnSearchReqDTO inventoryOutSnSearchReqDTO);

    /**
     * 根据参数分页查询出库单序列号
     * @param inventoryOutSnSearchReqDTO
     * @return
     */
    List<String> selectSnByPage(InventoryOutSnSearchReqDTO inventoryOutSnSearchReqDTO);

    /**
     * 导出出库流水
     * @param inventoryFlowReqDTO
     * @return
     */
    List<InventoryFlowRespDTO> exportInventoryOutFlow(InventoryFlowReqDTO inventoryFlowReqDTO);

    /**
     * 批量插插入出库单
     * @param stockDeliveryList
     */
    void batchInsert(List<StockDelivery> stockDeliveryList);

    /**
     * 根据物料和仓库获取使用数量
     * @param wareCodeList
     * @param suppliesCodeList
     * @return
     */
    List<SuppliesConfigRespDTO> selectSuppliesConfigRespDTOList(@Param("wareCodeList") List<String> wareCodeList,@Param("suppliesCodeList") List<String> suppliesCodeList);

    /**
     * 根据物料获取使用数量
     * @param suppliesCodeList
     * @return
     */
    List<StockSuppliesConfigDetailRespDTO> selectSuppliesConfigDetailRespDTOList(@Param("suppliesCodeList") List<String> suppliesCodeList);

    /**
     * 批量插入出库单信息
     * @param stockDeliveryInfoList
     * @return
     */
    Integer batchInsertDeliveryInfo(List<StockDeliveryInfo> stockDeliveryInfoList);

    /**
     * 查询资产领用
     * @param empIdList
     * @param outStockType
     * @return
     */
    List<StockDeliveryInfo> selectDeliveryInfoList(@Param("empIdList") List<String> empIdList,@Param("outStockType") Integer outStockType);

}