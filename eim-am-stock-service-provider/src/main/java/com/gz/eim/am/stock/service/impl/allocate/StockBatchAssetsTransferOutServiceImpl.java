package com.gz.eim.am.stock.service.impl.allocate;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dto.request.allocate.AllocateImportHeadReqDTO;
import com.gz.eim.am.stock.dto.request.allocate.AllocateImportSearchReqDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.response.allocate.AllocateImportHeadRespDTO;
import com.gz.eim.am.stock.dto.response.allocate.AllocateImportLineRespDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.vo.StockBatchAssetsTransferOutImportExcel;
import com.gz.eim.am.stock.service.allocate.StockAllocateImportHeadService;
import com.gz.eim.am.stock.service.allocate.StockAllocateImportLineService;
import com.gz.eim.am.stock.service.allocate.StockBatchAssetsTransferOutService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanHeadService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanLineService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lishuyang
 * @date: 2020/5/18
 * @description:
 */
@Slf4j
@Service
public class StockBatchAssetsTransferOutServiceImpl implements StockBatchAssetsTransferOutService {
    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;
    @Autowired
    private StockAllocateImportLineService stockAllocateImportLineService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private StockWarehouseService stockWarehouseService;
    @Autowired
    private StockAllocateImportHeadService stockAllocateImportHeadService;
    @Autowired
    private StockDeliveryPlanHeadService stockDeliveryPlanHeadService;
    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData assetBatchTransferOutImport(MultipartFile file , JwtUser user) throws IOException {
        if (file == null) {
            return ResponseData.createFailResult ("必填参数为空");
        }

        //权限校验
        List<String> wc = this.stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode () , null , null);

        if (CollectionUtils.isEmpty (wc)) {
            return ResponseData.createFailResult ("权限不足");
        }

        List<StockBatchAssetsTransferOutImportExcel> stockBatchAssetsTransferOutImportExcelList = ExcelUtil.importExcel (file.getInputStream () , StockBatchAssetsTransferOutImportExcel.class);
        if (CollectionUtils.isEmpty (stockBatchAssetsTransferOutImportExcelList)) {
            return ResponseData.createFailResult ("文件内数据为空");
        }

        //匹配错误的导入行
        List<StockAllocateImportLine> errorStockAllocateImportLineList = new ArrayList<> ();
        //匹配正确的导入行
        List<StockAllocateImportLine> successStockAllocateImportLineList = new ArrayList<> ();
        //匹配头
        StockAllocateImportHead stockAllocateImportHead = new StockAllocateImportHead ();
        //数据赋值
        prepareAllocateImportDbBeanByExcel (stockBatchAssetsTransferOutImportExcelList, stockAllocateImportHead, errorStockAllocateImportLineList, successStockAllocateImportLineList, wc, user);


        //插入头
        stockAllocateImportHeadService.insert (stockAllocateImportHead);

        //批量插入行数据
        errorStockAllocateImportLineList.addAll (successStockAllocateImportLineList);
        errorStockAllocateImportLineList.forEach (stockAllocateImportLine -> stockAllocateImportLine.setHeadId (stockAllocateImportHead.getHeadId ()));
        stockAllocateImportLineService.batchInsertStockAllocateImportLine (errorStockAllocateImportLineList);
        AllocateImportHeadRespDTO allocateImportHeadRespDTO = new AllocateImportHeadRespDTO ();
        allocateImportHeadRespDTO.setHeadCode (stockAllocateImportHead.getHeadCode ());
        return ResponseData.createSuccessResult (allocateImportHeadRespDTO);
    }

    @Override
    public ResponseData selectStockBatchAssetsTransferOutImport(AllocateImportSearchReqDTO allocateImportSearchReqDTO, JwtUser user) {
        if ( null == allocateImportSearchReqDTO ||StringUtils.isBlank (allocateImportSearchReqDTO.getHeadCode ())) {
            return ResponseData.createFailResult ("必填参数为空");
        }


        StockAllocateImportHead stockAllocateImportHead = new StockAllocateImportHead ();
        stockAllocateImportHead.setType (AllocateImportHeadEnum.type.BATCH_ASSETS_TRANSFER_OUT.getCode ());
        stockAllocateImportHead.setHeadCode (allocateImportSearchReqDTO.getHeadCode ());
        stockAllocateImportHead.setBillingUser (user.getEmployeeCode ());
        //获取资产调拨导入头
        List<AllocateImportHeadRespDTO> allocateImportHeadRespDtoList = stockAllocateImportHeadService.selectRespDTOByParam (stockAllocateImportHead);
        if (CollectionUtils.isEmpty (allocateImportHeadRespDtoList)) {
            AllocateImportHeadRespDTO allocateImportHeadRespDTO = new AllocateImportHeadRespDTO ();
            allocateImportHeadRespDTO.setCount (CommonConstant.NUMBER_LONG_ZERO);
            return ResponseData.createSuccessResult (allocateImportHeadRespDTO);
        } else if (allocateImportHeadRespDtoList.size () > CommonConstant.NUMBER_ONE) {
            log.info ("导入批code不唯一, {}", allocateImportSearchReqDTO.getHeadCode ());
            throw new ServiceUncheckedException ("系统错误");
        }

        //获取行数量
        Long count = stockAllocateImportLineService.countStockAllocateImportLineByHeadId (allocateImportHeadRespDtoList.get (0).getHeadId ());

        if (null == count || count.equals (CommonConstant.NUMBER_LONG_ZERO)) {
            allocateImportHeadRespDtoList.get (CommonConstant.NUMBER_ZERO).setCount (CommonConstant.NUMBER_LONG_ZERO);
            return ResponseData.createSuccessResult (allocateImportHeadRespDtoList.get (CommonConstant.NUMBER_ZERO));
        }

        //获取行集合
        //设置分页
        if (allocateImportSearchReqDTO.getPageSize () == null
                || allocateImportSearchReqDTO.getPageNum () == null) {
            allocateImportSearchReqDTO.initPageParam ();
        }
        allocateImportSearchReqDTO.setStartNum ((allocateImportSearchReqDTO.getPageNum () - CommonConstant.NUMBER_ONE) * allocateImportSearchReqDTO.getPageSize ());
        allocateImportSearchReqDTO.setHeadId (allocateImportHeadRespDtoList.get (CommonConstant.NUMBER_ZERO).getHeadId ());
        List<AllocateImportLineRespDTO>  allocateImportLineRespDTOList = stockAllocateImportLineService.selectAllocateImportLineByHeadId (allocateImportSearchReqDTO);
        //获取资产的信息
        List<String> assetCode = allocateImportLineRespDTOList.stream ().filter (allocateImportLineRespDTO -> StringUtils.isNotBlank (allocateImportLineRespDTO.getAssetsCode ())).map (AllocateImportLineRespDTO::getAssetsCode).collect(Collectors.toList());
        Map<String, StockAssets> stockAssetsMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(assetCode)){
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setAssetsCodeList(assetCode);
            stockAssetsMap = stockAssetsService.selectAssetsMapByAssetsSearchDTO(assetsSearchDTO);
        }
        for(AllocateImportLineRespDTO allocateImportLineRespDTO : allocateImportLineRespDTOList){
            if(StringUtils.isNotBlank (allocateImportLineRespDTO.getAssetsCode ()) && null != stockAssetsMap && null != stockAssetsMap.get(allocateImportLineRespDTO.getAssetsCode ())){
                allocateImportLineRespDTO.setAssetsName (stockAssetsMap.get(allocateImportLineRespDTO.getAssetsCode ()).getAssetsName ());
            }
        }

        allocateImportHeadRespDtoList.get (CommonConstant.NUMBER_ZERO).setAllocateImportLineRespDTOList (allocateImportLineRespDTOList);
        allocateImportHeadRespDtoList.get (CommonConstant.NUMBER_ZERO).setCount (count);

        return ResponseData.createSuccessResult (allocateImportHeadRespDtoList.get (CommonConstant.NUMBER_ZERO));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData saveBatchAssetsTransferOutImport(AllocateImportHeadReqDTO allocateImportHeadReqDTO, JwtUser user) {
        //校验参数
        String checkSaveParamResult = checkSaveParam (allocateImportHeadReqDTO, user);
        if (StringUtils.isNotBlank (checkSaveParamResult)) {
            return ResponseData.createFailResult (checkSaveParamResult);
        }
        //赋值更新数据
        StockAllocateImportHead updateStockAllocateImportHead = new StockAllocateImportHead ();
        prepareSave (updateStockAllocateImportHead, user);

        StockAllocateImportHead stockAllocateImportHeadParam = new StockAllocateImportHead ();
        stockAllocateImportHeadParam.setHeadCode (allocateImportHeadReqDTO.getHeadCode ());
        stockAllocateImportHeadService.updateByPramSelective (updateStockAllocateImportHead, stockAllocateImportHeadParam);

        //生成资产计划调拨出库单
        this.batchGeneratePlanAssetsTransferOut (stockAllocateImportHeadParam, user);

        return ResponseData.createSuccessResult ();
    }

    @Override
    public ResponseData vagueAllocateImportHead(String param, Integer limit, JwtUser user) {

        return stockAllocateImportHeadService.vagueAllocateImportHeadByParam (param, limit, user, AllocateImportHeadEnum.type.BATCH_ASSETS_TRANSFER_OUT.getCode ());
    }

    /**
     * 批量生成资产计划调拨出库单
     *
     * @param stockAllocateImportHeadParam
     * @param user
     */
    private void batchGeneratePlanAssetsTransferOut(StockAllocateImportHead stockAllocateImportHeadParam, JwtUser user) {
        List<StockAllocateImportHead> stockAllocateImportHeadList = stockAllocateImportHeadService.selectByParam (stockAllocateImportHeadParam, null, null, null);
        if (CollectionUtils.isEmpty (stockAllocateImportHeadList) || stockAllocateImportHeadList.size () > CommonConstant.NUMBER_ONE) {
            throw new ServiceUncheckedException ("导入头不存在或存在多个");
        }

        StockAllocateImportLine stockAllocateImportLineParam = new StockAllocateImportLine ();
        stockAllocateImportLineParam.setHeadId (stockAllocateImportHeadList.get (CommonConstant.NUMBER_ZERO).getHeadId ());
        stockAllocateImportLineParam.setStatus (AllocateImportLineEnum.Status.SUCCESS.getCode ());
        stockAllocateImportLineParam.setDelFlag (CommonEnum.delFlag.NO.getValue ());
        List<StockAllocateImportLine> stockAllocateImportLineList = stockAllocateImportLineService.selectAllocateImportLineByParam (stockAllocateImportLineParam, null, null, null);
        if(CollectionUtils.isEmpty (stockAllocateImportLineList)){
            throw new ServiceUncheckedException ("该编码下不存在需要导入的数据");
        }

        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = this.prepareGeneratePlanAssetsTransferOutHead (stockAllocateImportHeadList.get(CommonConstant.NUMBER_ZERO) ,stockAllocateImportLineList, user);
        if(CollectionUtils.isEmpty (stockAllocateImportHeadList)){
            throw new ServiceUncheckedException ("该编码下不存在需要导生成计划单头的数据");
        }

        stockDeliveryPlanHeadService.batchInsertStockDeliveryPlanHead (stockDeliveryPlanHeadList);

        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = this.prepareGeneratePlanAssetsTransferOutLine (stockAllocateImportLineList, stockDeliveryPlanHeadList, user);
        stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLine (stockDeliveryPlanLineList);
    }

    /**
     * 赋值计划资产调拨出库单行
     *
     * @param stockAllocateImportLineList
     * @param stockDeliveryPlanHeadList
     * @param user
     * @return
     */
    private List<StockDeliveryPlanLine> prepareGeneratePlanAssetsTransferOutLine(List<StockAllocateImportLine> stockAllocateImportLineList, List<StockDeliveryPlanHead> stockDeliveryPlanHeadList, JwtUser user) {
        Map<String, List<StockAllocateImportLine>> stringListMap = stockAllocateImportLineList.stream ().collect (Collectors.groupingBy (StockBatchAssetsTransferOutServiceImpl::generatePlanAssetsTransferOutLineGroupString));
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = new ArrayList<> (stringListMap.size ());
        stringListMap.forEach ((key, value) ->{
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine ();
            stockDeliveryPlanLine.setSuppliesCode (value.get(CommonConstant.NUMBER_ZERO).getSuppliesCode ());
            stockDeliveryPlanLine.setPlanOutTime (new Date ());
            stockDeliveryPlanLine.setNumber (value.size ());
            stockDeliveryPlanLine.setRealNumber (CommonConstant.NUMBER_ZERO);
            stockDeliveryPlanLine.setStatus (DeliveryPlanLineEnum.Status.WAIT_OUT.getCode ());
            stockDeliveryPlanLine.setCreatedBy (user.getEmployeeCode ());
            stockDeliveryPlanLine.setUpdatedBy (user.getEmployeeCode ());
            stockDeliveryPlanHeadList.forEach (stockDeliveryPlanHead -> {
                if(stockDeliveryPlanHead.getOutWarehouseCode ().equals (value.get (CommonConstant.NUMBER_ZERO).getOutWarehouseCode ()) && stockDeliveryPlanHead.getInWarehouseCode ().equals (value.get (CommonConstant.NUMBER_ZERO).getInWarehouseCode ())){
                    stockDeliveryPlanLine.setDeliveryPlanHeadId (stockDeliveryPlanHead.getDeliveryPlanHeadId ());
                }
            });
            stockDeliveryPlanLineList.add (stockDeliveryPlanLine);
        });
        return stockDeliveryPlanLineList;
    }

    /**
     * 赋值计划资产调拨出库单头
     *
     * @param stockAllocateImportLineList
     * @param user
     * @return
     */
    private List<StockDeliveryPlanHead> prepareGeneratePlanAssetsTransferOutHead(StockAllocateImportHead stockAllocateImportHead, List<StockAllocateImportLine> stockAllocateImportLineList, JwtUser user){

        Map<String, List<StockAllocateImportLine>> stringListMap = stockAllocateImportLineList.stream ().collect (Collectors.groupingBy (StockBatchAssetsTransferOutServiceImpl::generatePlanAssetsTransferOutHeadGroupString));
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = new ArrayList<> (stringListMap.size ());
        stringListMap.forEach ((key, value) ->{
            StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
            stockDeliveryPlanHead.setDeliveryPlanNo (stockDeliveryPlanHeadService.getDeliveryPlanNo (DeliveryPlanHeadEnum.OutType.TRANSFER.getCode ()));
            stockDeliveryPlanHead.setOutStockType (DeliveryPlanHeadEnum.OutType.ASSET_TRANSFER.getCode ());
            stockDeliveryPlanHead.setOutWarehouseCode (value.get(CommonConstant.NUMBER_ZERO).getOutWarehouseCode ());
            stockDeliveryPlanHead.setInWarehouseCode (value.get(CommonConstant.NUMBER_ZERO).getInWarehouseCode ());
            stockDeliveryPlanHead.setDutyUser (user.getEmployeeCode ());
            stockDeliveryPlanHead.setReasonCode (DeliveryPlanHeadEnum.Reason.HEADQUARTERS_ISSUED.getCode ());
            stockDeliveryPlanHead.setBillingTime (new Date ());
            stockDeliveryPlanHead.setBillingUser (user.getEmployeeCode ());
            stockDeliveryPlanHead.setPlanOutTime (new Date ());
            stockDeliveryPlanHead.setStatus (DeliveryPlanHeadEnum.Status.WAIT_OUT.getCode ());
            stockDeliveryPlanHead.setUpdatedBy (user.getEmployeeCode ());
            stockDeliveryPlanHead.setCreatedBy (user.getEmployeeCode ());
            stockDeliveryPlanHead.setUseAddress ("");
            stockDeliveryPlanHead.setBizNo (stockAllocateImportHead.getHeadCode ());
            stockDeliveryPlanHeadList.add (stockDeliveryPlanHead);
        });

        return stockDeliveryPlanHeadList;
    }

    /**
     * 生成分组计划资产调拨单头的字符串
     *
     * @param stockAllocateImportLine
     * @return
     */
    private static String generatePlanAssetsTransferOutLineGroupString(StockAllocateImportLine stockAllocateImportLine) {
        return stockAllocateImportLine.getOutWarehouseCode () + StringConstant.PAND + stockAllocateImportLine.getInWarehouseCode () + StringConstant.PAND + stockAllocateImportLine.getSuppliesCode ();
    }

    /**
     * 生成分组计划资产调拨单头的字符串
     *
     * @param stockAllocateImportLine
     * @return
     */
    private static String generatePlanAssetsTransferOutHeadGroupString(StockAllocateImportLine stockAllocateImportLine) {
        return stockAllocateImportLine.getOutWarehouseCode () + StringConstant.PAND + stockAllocateImportLine.getInWarehouseCode ();
    }


    /**
     * 赋值保存
     *
     * @param stockAllocateImportHead
     * @param user
     * @param user
     */
    private void prepareSave(StockAllocateImportHead stockAllocateImportHead , JwtUser user) {
        stockAllocateImportHead.setStatus (AllocateImportHeadEnum.Status.APPROVED.getCode ());
        stockAllocateImportHead.setUpdatedBy (user.getEmployeeCode ());
    }

    /**
     * 校验保存参数
     *
     * @param allocateImportHeadReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(AllocateImportHeadReqDTO allocateImportHeadReqDTO , JwtUser user) {
        if (null == allocateImportHeadReqDTO || StringUtils.isBlank (allocateImportHeadReqDTO.getHeadCode ())) {
            return "必填参数为空";
        }

        StockAllocateImportHead stockAllocateImportHead = new StockAllocateImportHead ();
        stockAllocateImportHead.setHeadCode (allocateImportHeadReqDTO.getHeadCode ());
        stockAllocateImportHead.setType (AllocateImportHeadEnum.type.BATCH_ASSETS_TRANSFER_OUT.getCode ());
        stockAllocateImportHead.setBillingUser (user.getEmployeeCode ());
        List<StockAllocateImportHead> allocateImportHeadList = stockAllocateImportHeadService.selectByParam (stockAllocateImportHead , null , null , null);
        if (CollectionUtils.isEmpty (allocateImportHeadList)) {
            return "该编号不存在";
        } else if (allocateImportHeadList.size () > CommonConstant.NUMBER_ONE) {
            log.error ("当前单据不唯一，{}" , allocateImportHeadReqDTO.getHeadCode ());
            return "系统错误";
        }

        if (!allocateImportHeadList.get (CommonConstant.NUMBER_ZERO).getStatus ().equals (AllocateImportHeadEnum.Status.SAVE.getCode ())
                || allocateImportHeadList.get (CommonConstant.NUMBER_ZERO).getDelFlag ().equals (CommonEnum.delFlag.YES.getValue ())) {
            return "当前单据不可提交";
        }

        return null;
    }

    /**
     * excel数据赋值到bean
     *
     * @param stockBatchAssetsTransferOutImportExcelList
     * @param stockAllocateImportHead
     * @param errorStockAllocateImportLineList
     * @param successStockAllocateImportLineList
     * @param wc
     * @param user
     */
    private void prepareAllocateImportDbBeanByExcel(List<StockBatchAssetsTransferOutImportExcel> stockBatchAssetsTransferOutImportExcelList , StockAllocateImportHead stockAllocateImportHead , List<StockAllocateImportLine> errorStockAllocateImportLineList , List<StockAllocateImportLine> successStockAllocateImportLineList , List<String> wc , JwtUser user) {
        List<String> assetsCodeList = stockBatchAssetsTransferOutImportExcelList.stream ().filter (a -> StringUtils.isNotBlank (a.getAssetsCode ())).map (StockBatchAssetsTransferOutImportExcel::getAssetsCode).collect (Collectors.toList ());
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes (assetsCodeList);
        List<String> warehouseCodes = stockBatchAssetsTransferOutImportExcelList.stream ().filter (a -> StringUtils.isNotBlank (a.getInWarehouseCode ())).map (StockBatchAssetsTransferOutImportExcel::getInWarehouseCode).collect (Collectors.toList ());
        Map<String, WarehouseRespDTO> stockWarehouseMap = stockWarehouseService.selectWarehouseDetailMapByCode (warehouseCodes);

        prepareAllocateImportLineDbBeanByExcel (stockBatchAssetsTransferOutImportExcelList , user , errorStockAllocateImportLineList , successStockAllocateImportLineList , stockAssetsMap , stockWarehouseMap , wc);

        stockAllocateImportHead.setBillingUser (user.getEmployeeCode ());
        if (!CollectionUtils.isEmpty (errorStockAllocateImportLineList)) {
            stockAllocateImportHead.setStatus (AllocateImportHeadEnum.Status.ERROR.getCode ());
        } else {
            stockAllocateImportHead.setStatus (AllocateImportHeadEnum.Status.SAVE.getCode ());
        }
        stockAllocateImportHead.setDelFlag (AllocateImportHeadEnum.DelFlag.NO.getCode ());
        stockAllocateImportHead.setCreatedBy (user.getEmployeeCode ());
        stockAllocateImportHead.setUpdatedBy (user.getEmployeeCode ());
        stockAllocateImportHead.setType (AllocateImportHeadEnum.type.BATCH_ASSETS_TRANSFER_OUT.getCode ());
        stockAllocateImportHead.setHeadCode (OrderUtil.getOrderNo (OrderEnum.ALLOCATE_IN_IMPORT));
    }

    /**
     * excel数据赋值到bean
     *
     * @param stockBatchAssetsTransferOutImportExcelList
     * @param user
     * @param errorStockAllocateImportLineList
     * @param successStockAllocateImportLineList
     * @param stockAssetsMap
     * @param stockWarehouseMap
     * @param wc
     */
    private void prepareAllocateImportLineDbBeanByExcel(List<StockBatchAssetsTransferOutImportExcel> stockBatchAssetsTransferOutImportExcelList , JwtUser user , List<StockAllocateImportLine> errorStockAllocateImportLineList ,
                                                        List<StockAllocateImportLine> successStockAllocateImportLineList , Map<String, StockAssets> stockAssetsMap , Map<String, WarehouseRespDTO> stockWarehouseMap , List<String> wc) {
        //用于判重
        List<String> judgeAssetCodes = new ArrayList<> ();
        for (StockBatchAssetsTransferOutImportExcel stockBatchAssetsTransferOutImportExcel : stockBatchAssetsTransferOutImportExcelList) {
            StockAllocateImportLine stockAllocateImportLine = new StockAllocateImportLine ();
            stockAllocateImportLine.setCreatedBy (user.getEmployeeCode ());
            stockAllocateImportLine.setUpdatedBy (user.getEmployeeCode ());
            stockAllocateImportLine.setNumber (CommonConstant.NUMBER_ZERO);
            stockAllocateImportLine.setDelFlag (CommonEnum.delFlag.NO.getValue ());
            stockAllocateImportLine.setAssetsCode ("");
            if (StringUtils.isBlank (stockBatchAssetsTransferOutImportExcel.getAssetsCode ())) {
                stockAllocateImportLine.setErrorMessage ("资产编码不能为空");
                stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.ERROR.getCode ());
                errorStockAllocateImportLineList.add (stockAllocateImportLine);
                continue;
            }

            //资产编码是否重复
            if (judgeAssetCodes.contains (stockBatchAssetsTransferOutImportExcel.getAssetsCode ())) {
                stockAllocateImportLine.setErrorMessage ("调拨资产编码重复");
                stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.ERROR.getCode ());
                errorStockAllocateImportLineList.add (stockAllocateImportLine);
                continue;
            }
            judgeAssetCodes.add (stockBatchAssetsTransferOutImportExcel.getAssetsCode ());

            if (null == stockAssetsMap || null == stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ())) {
                stockAllocateImportLine.setErrorMessage ("资产编码" + stockBatchAssetsTransferOutImportExcel.getAssetsCode () + "不存在；");
                stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.ERROR.getCode ());
                errorStockAllocateImportLineList.add (stockAllocateImportLine);
                continue;
            }

            if (!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getApproveStatus())) {
                stockAllocateImportLine.setErrorMessage ("资产编码" + stockBatchAssetsTransferOutImportExcel.getAssetsCode () + "在报废审批中，或已报废，不能录入；");
                stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.ERROR.getCode ());
                errorStockAllocateImportLineList.add (stockAllocateImportLine);
                continue;
            }

            stockAllocateImportLine.setAssetsCode (stockBatchAssetsTransferOutImportExcel.getAssetsCode ());
            if (!AssetsEnum.statusType.IDLE.getValue ().equals (stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getStatus ())) {
                stockAllocateImportLine.setErrorMessage ("资产编码" + stockBatchAssetsTransferOutImportExcel.getAssetsCode () + "不在库；");
                stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.ERROR.getCode ());
                errorStockAllocateImportLineList.add (stockAllocateImportLine);
                continue;
            }

            StringBuilder sb = new StringBuilder ();
            boolean isRole = StringUtils.isBlank (stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getWarehouseCode ()) || (!wc.get (CommonConstant.NUMBER_ZERO).equals (ManageRoleEnum.Type.ALL.getValue ()) && !wc.contains (stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getWarehouseCode ()));
            if (isRole) {
                sb.append ("资产编码").append (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).append ("仓库为空或当前用户无权限；");
            } else {
                stockAllocateImportLine.setOutWarehouseCode (stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getWarehouseCode ());
                stockAllocateImportLine.setNumber (CommonConstant.NUMBER_ONE);
                stockAllocateImportLine.setSuppliesCode (stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getSuppliesCode ());
            }

            if (StringUtils.isBlank (stockBatchAssetsTransferOutImportExcel.getInWarehouseCode ())) {
                sb.append ("调入仓库不能为空");
            } else if (null == stockWarehouseMap || null == stockWarehouseMap.get (stockBatchAssetsTransferOutImportExcel.getInWarehouseCode ())) {
                sb.append ("调入仓库").append (stockBatchAssetsTransferOutImportExcel.getInWarehouseCode ()).append ("不存在；");
            } else if (stockAssetsMap.get (stockBatchAssetsTransferOutImportExcel.getAssetsCode ()).getWarehouseCode ().equals (stockBatchAssetsTransferOutImportExcel.getInWarehouseCode ())) {
                sb.append ("当前资产编码的所在仓库与调入仓库不能相同;");
            } else {
                stockAllocateImportLine.setInWarehouseCode (stockBatchAssetsTransferOutImportExcel.getInWarehouseCode ());
            }

            if (sb.length () > CommonConstant.NUMBER_ZERO) {
                stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.ERROR.getCode ());
                stockAllocateImportLine.setErrorMessage (sb.toString ());
                errorStockAllocateImportLineList.add (stockAllocateImportLine);
                continue;
            }

            stockAllocateImportLine.setStatus (AllocateImportLineEnum.Status.SUCCESS.getCode ());
            successStockAllocateImportLineList.add (stockAllocateImportLine);
        }
    }
}
