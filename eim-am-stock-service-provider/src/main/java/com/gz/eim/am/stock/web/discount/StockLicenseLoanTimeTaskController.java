package com.gz.eim.am.stock.web.discount;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.RedisUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.service.discount.StockLicenseLoanTimeTaskService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wangjing67
 * @Date: 4/7/21 11:11 上午
 * @description  执照逾期归还提醒定时任务
 */
@RestController
@RequestMapping("/api/am/stock/license/remind")
@Slf4j
public class StockLicenseLoanTimeTaskController {


    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockLicenseLoanTimeTaskService stockLicenseLoanTimeTaskService;


    /**
     * 执照逾期归还提醒
     * @return
     */
    @GetMapping(value = "/send-message")
    public ResponseData mtPushSendMessage() {
        log.info("/api/am/stock/license/remind/send-message");
        ResponseData res = null;
        try {
            res = this.stockLicenseLoanTimeTaskService.mtPushSendMessage();
        }catch(Exception e){
            log.error("执照/印章逾期归还提醒发送消息出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    public static void main(String[] args) {
        Date beginDate = DateTimeUtil.getDayStart(new Date());
        beginDate = DateUtils.dateAddDays(beginDate, -10);
        System.out.println(beginDate);

    }

}
