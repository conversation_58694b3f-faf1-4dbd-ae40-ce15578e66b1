package com.gz.eim.am.stock.service.check;

import com.gz.eim.am.stock.entity.StockTakingProcess;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 10/30/20 5:38 下午
 * @description 盘点流程接口
 */
public interface StockTakingProcessService {


    /**
     * 根据盘点计划单号查询盘点流程信息
     * @param takingPlanNo 盘点计划单号
     * @return
     * @throws Exception
     */
    StockTakingProcess queryByTakingPlanNo(String takingPlanNo)throws Exception;

    /**
     * 根据盘点计划单号选择性更新各流程节点
     * @param stockTakingProcess
     * @param takingPlanNo
     * @return
     */
    int updateTakingPlanStaus(StockTakingProcess stockTakingProcess,String takingPlanNo);

    /**
     * 根据盘点计划单号选择性更新各流程节点,开启新事物
     * @param stockTakingProcess
     * @param takingPlanNo
     * @return
     */
    int updateStausUseNewTransactional(StockTakingProcess stockTakingProcess,String takingPlanNo);
}
