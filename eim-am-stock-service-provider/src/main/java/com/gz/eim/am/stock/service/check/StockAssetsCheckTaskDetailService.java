package com.gz.eim.am.stock.service.check;

import com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetail;
import com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailReqDO;

import java.util.List;

public interface StockAssetsCheckTaskDetailService {
    /**
     * @param: stockAssetsCheckTaskDetailList
     * @description: 批量更新资产盘点详情列表
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/11/13
     */
    int batchUpdate(List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList);

    /**
     * @param: stockAssetsCheckTaskDetailReqDO
     * @description: 查询盘点详情集合
     * @return: List<StockAssetsCheckTaskDetail>
     * @author: <EMAIL>
     * @date: 2023/12/9
     */
    List<StockAssetsCheckTaskDetail> selectList(StockAssetsCheckTaskDetailReqDO stockAssetsCheckTaskDetailReqDO);
    /**
     * @param: stockAssetsCheckTaskDetailReqDO
     * @description: 查询盘点详情
     * @return: StockAssetsCheckTaskDetail
     * @author: <EMAIL>
     * @date: 2023/12/9
     */
    StockAssetsCheckTaskDetail selectOne(StockAssetsCheckTaskDetailReqDO stockAssetsCheckTaskDetailReqDO);


}
