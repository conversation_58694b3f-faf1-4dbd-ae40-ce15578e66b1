package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import lombok.Data;

@Data
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockAssetsRepairCheckAssetsConfigurationUpgradeExportExcel implements ExportModel {

    @ExportField(name="资产编码")
    private String assetsCode;

    @ExportField(name="升级类型")
    private String upgradeTypeDesc;

    @ExportField(name="验收状态")
    private String checkStatusDesc;


    @Override
    public String getSheetName() {
        return "资产明细";
    }
}
