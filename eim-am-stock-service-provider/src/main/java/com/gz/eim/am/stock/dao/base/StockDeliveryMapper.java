package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockDelivery;
import com.gz.eim.am.stock.entity.StockDeliveryExample;
import java.util.List;

import com.gz.eim.am.stock.entity.vo.StockDeliveryInfo;
import org.apache.ibatis.annotations.Param;

public interface StockDeliveryMapper {
    long countByExample(StockDeliveryExample example);

    int deleteByPrimaryKey(Long deliveryId);

    int insert(StockDelivery record);

    int insertSelective(StockDelivery record);

    List<StockDelivery> selectByExample(StockDeliveryExample example);

    StockDelivery selectByPrimaryKey(Long deliveryId);

    int updateByExampleSelective(@Param("record") StockDelivery record, @Param("example") StockDeliveryExample example);

    int updateByExample(@Param("record") StockDelivery record, @Param("example") StockDeliveryExample example);

    int updateByPrimaryKeySelective(StockDelivery record);

    int updateByPrimaryKey(StockDelivery record);

}