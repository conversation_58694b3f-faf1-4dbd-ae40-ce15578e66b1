package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockAssetsCompensationRecord {
    private Long id;

    private String assetsCode;

    private String auditUser;

    private Date auditTime;

    private BigDecimal compensationMoney;

    private Integer compensationMethod;

    private BigDecimal adviseCompensationMoney;

    private Integer compensationReason;

    private Integer isRegistrationCompensation;

    private Integer isReturn;

    private String compensationUser;

    private Date returnTime;

    private String returnWarehouseCode;

    private Date leaveTime;

    private Integer isNeedScrap;

    private Integer syncPaySystemStatus;

    private Integer dataSource;

    private String formId;

    private String remark;

    private Integer assetsRepairDealMethod;

    private String newAssetsCode;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser == null ? null : auditUser.trim();
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public BigDecimal getCompensationMoney() {
        return compensationMoney;
    }

    public void setCompensationMoney(BigDecimal compensationMoney) {
        this.compensationMoney = compensationMoney;
    }

    public Integer getCompensationMethod() {
        return compensationMethod;
    }

    public void setCompensationMethod(Integer compensationMethod) {
        this.compensationMethod = compensationMethod;
    }

    public BigDecimal getAdviseCompensationMoney() {
        return adviseCompensationMoney;
    }

    public void setAdviseCompensationMoney(BigDecimal adviseCompensationMoney) {
        this.adviseCompensationMoney = adviseCompensationMoney;
    }

    public Integer getCompensationReason() {
        return compensationReason;
    }

    public void setCompensationReason(Integer compensationReason) {
        this.compensationReason = compensationReason;
    }

    public Integer getIsRegistrationCompensation() {
        return isRegistrationCompensation;
    }

    public void setIsRegistrationCompensation(Integer isRegistrationCompensation) {
        this.isRegistrationCompensation = isRegistrationCompensation;
    }

    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }

    public String getCompensationUser() {
        return compensationUser;
    }

    public void setCompensationUser(String compensationUser) {
        this.compensationUser = compensationUser == null ? null : compensationUser.trim();
    }

    public Date getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(Date returnTime) {
        this.returnTime = returnTime;
    }

    public String getReturnWarehouseCode() {
        return returnWarehouseCode;
    }

    public void setReturnWarehouseCode(String returnWarehouseCode) {
        this.returnWarehouseCode = returnWarehouseCode == null ? null : returnWarehouseCode.trim();
    }

    public Date getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(Date leaveTime) {
        this.leaveTime = leaveTime;
    }

    public Integer getIsNeedScrap() {
        return isNeedScrap;
    }

    public void setIsNeedScrap(Integer isNeedScrap) {
        this.isNeedScrap = isNeedScrap;
    }

    public Integer getSyncPaySystemStatus() {
        return syncPaySystemStatus;
    }

    public void setSyncPaySystemStatus(Integer syncPaySystemStatus) {
        this.syncPaySystemStatus = syncPaySystemStatus;
    }

    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId == null ? null : formId.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getAssetsRepairDealMethod() {
        return assetsRepairDealMethod;
    }

    public void setAssetsRepairDealMethod(Integer assetsRepairDealMethod) {
        this.assetsRepairDealMethod = assetsRepairDealMethod;
    }

    public String getNewAssetsCode() {
        return newAssetsCode;
    }

    public void setNewAssetsCode(String newAssetsCode) {
        this.newAssetsCode = newAssetsCode == null ? null : newAssetsCode.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}