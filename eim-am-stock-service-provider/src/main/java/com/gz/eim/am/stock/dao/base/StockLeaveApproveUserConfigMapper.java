package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockLeaveApproveUserConfig;
import com.gz.eim.am.stock.entity.StockLeaveApproveUserConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockLeaveApproveUserConfigMapper {
    long countByExample(StockLeaveApproveUserConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockLeaveApproveUserConfig record);

    int insertSelective(StockLeaveApproveUserConfig record);

    List<StockLeaveApproveUserConfig> selectByExample(StockLeaveApproveUserConfigExample example);

    StockLeaveApproveUserConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockLeaveApproveUserConfig record, @Param("example") StockLeaveApproveUserConfigExample example);

    int updateByExample(@Param("record") StockLeaveApproveUserConfig record, @Param("example") StockLeaveApproveUserConfigExample example);

    int updateByPrimaryKeySelective(StockLeaveApproveUserConfig record);

    int updateByPrimaryKey(StockLeaveApproveUserConfig record);
}