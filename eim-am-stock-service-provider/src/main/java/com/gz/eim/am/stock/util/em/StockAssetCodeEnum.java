package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/3/13
 * @description 资产编号枚举
 */
public class StockAssetCodeEnum {
    /**
     * 是否已使用
     */
    public enum isUsed {

        /**
         * 否
         */
        NO(0, "否"),
        /**
         * 使用中
         */
        YES(1, "是"),
        ;

        isUsed(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<Integer, String> isUsedMap =
            Arrays.stream(StockAssetCodeEnum.isUsed.values()).collect(
                    Collectors.toMap(StockAssetCodeEnum.isUsed::getValue, StockAssetCodeEnum.isUsed::getDesc));

    /**
     * 是否已入库
     */
    public enum isIn {

        /**
         * 未
         */
        NO(0, "未入库"),
        /**
         * 已入库
         */
        YES(1, "是"),
        ;

        isIn(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<Integer, String> isInMap =
            Arrays.stream(StockAssetCodeEnum.isIn.values()).collect(
                    Collectors.toMap(StockAssetCodeEnum.isIn::getValue, StockAssetCodeEnum.isIn::getDesc));

}
