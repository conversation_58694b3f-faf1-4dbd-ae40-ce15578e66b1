package com.gz.eim.am.stock.util.em;

import com.gz.eim.am.stock.util.common.CodeEnumUtil;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/6
 * @description:
 */
public class AllocateImportHeadEnum {

    public enum  Status implements ICodeEnum{
        /**
         * 已保存
         */
        SAVE(0, "已保存"),
        /**
         * 审批中
         */
        APPROVE (1, "审批中"),
        /**
         * 已拒绝
         */
        REFUSE (2, "已拒绝"),
        /**
         * 审批通过
         */
        APPROVED(3, "审批通过"),
        /**
         * 错误
         */
        ERROR(100, "错误");

        Status(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;
        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        /**
         * 根据code返回指定枚举类型中的相应值
         *
         * @param code 指定code
         */
        public static Status fromCode(Integer code) {
            return CodeEnumUtil.fromCode (Status.class, code);
        }
    }

    /**
     * 是否已删除
     */
    public enum  DelFlag implements ICodeEnum{
        /**
         * 未删除
         */
        NO(0, "未删除"),
        /**
         * 已删除
         */
        YES(1, "已删除");

        DelFlag(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;
        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        /**
         * 根据code返回指定枚举类型中的相应值
         *
         * @param code 指定code
         */
        public static AllocateImportLineEnum.DelFlag fromCode(Integer code) {
            return CodeEnumUtil.fromCode (AllocateImportLineEnum.DelFlag.class, code);
        }
    }

    /**
     * 操作
     */
    public enum  Operate implements ICodeEnum{
        /**
         * 清除
         */
        CLEAR(0, "清除"),
        /**
         * 保存
         */
        SAVE (1, "保存"),
        /**
         * 审批
         */
        APPROVE (2, "审批");

        Operate(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;
        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        /**
         * 根据code返回指定枚举类型中的相应值
         *
         * @param code 指定code
         */
        public static Operate fromCode(Integer code) {
            return CodeEnumUtil.fromCode (Operate.class, code);
        }
    }

    public enum  type implements ICodeEnum{
        /**
         * 礼品批量调出
         */
        BATCH_GIFT_TRANSFER_OUT(0, "礼品批量调出"),
        /**
         * 资产批量调出
         */
        BATCH_ASSETS_TRANSFER_OUT (1, "资产批量调出"),
        ;

        type(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;
        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        /**
         * 根据code返回指定枚举类型中的相应值
         *
         * @param code 指定code
         */
        public static type fromCode(Integer code) {
            return CodeEnumUtil.fromCode (type.class, code);
        }
    }
}
