package com.gz.eim.am.stock.entity.vo.supplies;

import com.gz.eim.am.stock.dto.request.supplies.SuppliesPurchaseSearchReqDto;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/7/10
 * <p>
 *   物料基本属性和采购属性查询
 * </p>
 */
public class SuppliesPurchaseSearchVo extends SuppliesPurchaseSearchReqDto {

    /**
     * 费用项目集合
     */
    private List<String> costItemList;

    public List<String> getCostItemList() {
        return costItemList;
    }

    public void setCostItemList(List<String> costItemList) {
        this.costItemList = costItemList;
    }

    @Override
    public String toString() {
        return "SuppliesPurchaseSearchVo{" +
                "costItemList=" + costItemList +
                '}';
    }
}
