package com.gz.eim.am.stock.service.manage;


import com.gz.eim.am.stock.entity.StockSuppliesQuantity;

/**
 * <AUTHOR>
 * @date 2021-06-10
 */
public interface StockSuppliesQuantityService {

    void addQuantity(StockSuppliesQuantity stockSuppliesQuantity);

    void subtractQuantity(StockSuppliesQuantity stockSuppliesQuantity);

    void addReservedQuantity(StockSuppliesQuantity stockSuppliesQuantity);

    void subtractReservedQuantity(StockSuppliesQuantity stockSuppliesQuantity);
}
