package com.gz.eim.am.stock.util.common;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/12/11
 * @description
 */
public class ListUtil {

    /**
     * 按指定大小，分隔集合，将集合按规定个数分为n个部分
     *
     * @param list 集合
     * @param len 每组大小
     * @return 分组后的集合
     */
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if (list == null || list.size() == 0 || len < 1) {
            return null;
        }
        int size = list.size();
        int count = (size + len - 1) / len;
        List<List<T>> result = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, ((i + 1) * len > size ? size : len * (i + 1)));
            result.add(subList);
        }
        return result;
    }
}
