package com.gz.eim.am.stock.service.impl.inventory;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockInventoryInSuppliesMapper;
import com.gz.eim.am.stock.dao.inventory.InventoryInSuppliesMapper;
import com.gz.eim.am.stock.dto.response.inventory.InventoryInSuppliesRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInSupplies;
import com.gz.eim.am.stock.entity.StockInventoryInSuppliesExample;
import com.gz.eim.am.stock.entity.vo.StockInventoryInSuppliesInfo;
import com.gz.eim.am.stock.service.inventory.StockInventoryInSuppliesService;
import com.gz.eim.am.stock.util.em.InventoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 7:02
 */

@Service
public class StockInventoryInSuppliesServiceImpl implements StockInventoryInSuppliesService {

    @Autowired
    private StockInventoryInSuppliesMapper stockInventoryInSuppliesMapper;
    @Autowired
    private InventoryInSuppliesMapper inventoryInSuppliesMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(StockInventoryInSupplies inventoryInSupplies) {
        return this.inventoryInSuppliesMapper.insert(inventoryInSupplies);
    }

    @Override
    public StockInventoryInSupplies getStockInventoryInSuppliesByPrimaryKey(Long inSuppliesId) {
        return this.stockInventoryInSuppliesMapper.selectByPrimaryKey (inSuppliesId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStockInventorySuppliesListByInventoryInId(List<StockInventoryInSupplies> suppliesList, Long inventoryInId) {
        //删除旧的入库单详细
        inventoryInSuppliesMapper.deleteStockInventoryInSuppliesByInventoryInId (inventoryInId);
        if(suppliesList != null && !suppliesList.isEmpty ()){
            int count = inventoryInSuppliesMapper.insertStockInventoryInSuppliesList (suppliesList);
            if(count < 1){
                return false;
            }
        }
        return true;
    }

    @Override
    public List<StockInventoryInSupplies> getStockInventoryInSuppliesListOfInventoryIn(Long inventoryInId) {
        if(inventoryInId == null){
            return Collections.emptyList();
        }
        StockInventoryInSuppliesExample example = new StockInventoryInSuppliesExample ();
        StockInventoryInSuppliesExample.Criteria criteria = example.createCriteria ();
        criteria.andInventoryInIdEqualTo (inventoryInId);
        return stockInventoryInSuppliesMapper.selectByExample (example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStockInventoryInSuppliesByInventoryInId(Long inventoryInId) {
        inventoryInSuppliesMapper.deleteStockInventoryInSuppliesByInventoryInId (inventoryInId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertStockInventoryInSuppliesList(List<StockInventoryInSupplies> stockInventoryInSuppliesList) {
        if(CollectionUtils.isEmpty (stockInventoryInSuppliesList)){
            return null;
        }
        return  inventoryInSuppliesMapper.insertStockInventoryInSuppliesList (stockInventoryInSuppliesList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStorageStockInventorySuppliesList(List<StockInventoryInSupplies> stockInventoryInSuppliesList) {
        if(CollectionUtils.isEmpty (stockInventoryInSuppliesList)){
            return  false;
        }

        for(StockInventoryInSupplies inventoryInSupplies : stockInventoryInSuppliesList){
            int count = inventoryInSuppliesMapper.updateStorageStockInventorySuppliesByPrimaryKey (inventoryInSupplies);
            if(count < 1) {
                return false;
            }
        }

        return true;
    }

    @Override
    public List<InventoryInSuppliesRespDTO> selectInventoryInSuppliesRespDTOByInventoryInId(Long inventoryInId) {
        if(inventoryInId == null){
            return  new ArrayList<> ();
        }
        return inventoryInSuppliesMapper.selectInventoryInSuppliesRespDTOByInventoryInId (inventoryInId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insertStockInventoryInSuppliesInfoList(List<StockInventoryInSuppliesInfo> stockInventoryInSuppliesInfoList) {
        if(CollectionUtils.isEmpty (stockInventoryInSuppliesInfoList)){
            return null;
        }
        int count = 0;
       List<StockInventoryInSuppliesInfo> newStockInventoryInSuppliesInfoList = new ArrayList<>();
        if(stockInventoryInSuppliesInfoList.size() > 0){
            //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
            int toIndex= CommonConstant.MAX_INSERT_COUNT;
            for(int i = 0;i<stockInventoryInSuppliesInfoList.size();i+=CommonConstant.MAX_INSERT_COUNT){
                if(i+CommonConstant.MAX_INSERT_COUNT>stockInventoryInSuppliesInfoList.size()){
                    toIndex=stockInventoryInSuppliesInfoList.size()-i;
                }
                List<StockInventoryInSuppliesInfo> newSubDetailList = stockInventoryInSuppliesInfoList.subList(i,i+toIndex);
                int insertCount = inventoryInSuppliesMapper.insertStockInventoryInSuppliesInfoList (newSubDetailList);
                count = count + insertCount;
                newStockInventoryInSuppliesInfoList.addAll(newSubDetailList);
            }
        }


        stockInventoryInSuppliesInfoList = new ArrayList<>();
        stockInventoryInSuppliesInfoList.addAll(newStockInventoryInSuppliesInfoList);

        return count;
    }

    @Override
    public List<StockInventoryInSupplies> selectNotAlready(Long inventoryInId) {
        if(null == inventoryInId){
            return Collections.emptyList ();
        }

        StockInventoryInSuppliesExample example = new StockInventoryInSuppliesExample();
        StockInventoryInSuppliesExample.Criteria criteria = example.createCriteria ();
        criteria.andInventoryInIdEqualTo (inventoryInId);
        criteria.andStatusNotEqualTo (InventoryEnum.Status.ALREADY_IN.getCode ());

        return stockInventoryInSuppliesMapper.selectByExample (example);
    }

}
