package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockTakingProcess {
    private Long id;

    private String takingPlanNo;

    private Integer startTakingPlan;

    private Integer startTakingPlanStatus;

    private Date startTakingPlanFinishDate;

    private Integer generateTakingSnapshot;

    private Integer generateTakingSnapshotStatus;

    private Date generateTakingSnapshotDate;

    private Integer confirmTakingSnapshot;

    private Integer confirmTakingSnapshotStatus;

    private Date confirmTakingSnapshotDate;

    private Integer createTakingTask;

    private Integer createTakingTaskStatus;

    private Date createTakingTaskDate;

    private Integer assignTakingTask;

    private Integer assignTakingTaskStatus;

    private Date assignTakingTaskDate;

    private Integer inputTakingData;

    private Integer inputTakingDataStatus;

    private Date inputTakingDataDate;

    private Integer generateTakingResult;

    private Integer generateTakingResultStatus;

    private Date generateTakingResultDate;

    private Integer approvalTaking;

    private Integer approvalTakingStatus;

    private Date approvalTakingDate;

    private Integer performTakingAdjust;

    private Integer performTakingAdjustStatus;

    private Date performTakingAdjustDate;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTakingPlanNo() {
        return takingPlanNo;
    }

    public void setTakingPlanNo(String takingPlanNo) {
        this.takingPlanNo = takingPlanNo == null ? null : takingPlanNo.trim();
    }

    public Integer getStartTakingPlan() {
        return startTakingPlan;
    }

    public void setStartTakingPlan(Integer startTakingPlan) {
        this.startTakingPlan = startTakingPlan;
    }

    public Integer getStartTakingPlanStatus() {
        return startTakingPlanStatus;
    }

    public void setStartTakingPlanStatus(Integer startTakingPlanStatus) {
        this.startTakingPlanStatus = startTakingPlanStatus;
    }

    public Date getStartTakingPlanFinishDate() {
        return startTakingPlanFinishDate;
    }

    public void setStartTakingPlanFinishDate(Date startTakingPlanFinishDate) {
        this.startTakingPlanFinishDate = startTakingPlanFinishDate;
    }

    public Integer getGenerateTakingSnapshot() {
        return generateTakingSnapshot;
    }

    public void setGenerateTakingSnapshot(Integer generateTakingSnapshot) {
        this.generateTakingSnapshot = generateTakingSnapshot;
    }

    public Integer getGenerateTakingSnapshotStatus() {
        return generateTakingSnapshotStatus;
    }

    public void setGenerateTakingSnapshotStatus(Integer generateTakingSnapshotStatus) {
        this.generateTakingSnapshotStatus = generateTakingSnapshotStatus;
    }

    public Date getGenerateTakingSnapshotDate() {
        return generateTakingSnapshotDate;
    }

    public void setGenerateTakingSnapshotDate(Date generateTakingSnapshotDate) {
        this.generateTakingSnapshotDate = generateTakingSnapshotDate;
    }

    public Integer getConfirmTakingSnapshot() {
        return confirmTakingSnapshot;
    }

    public void setConfirmTakingSnapshot(Integer confirmTakingSnapshot) {
        this.confirmTakingSnapshot = confirmTakingSnapshot;
    }

    public Integer getConfirmTakingSnapshotStatus() {
        return confirmTakingSnapshotStatus;
    }

    public void setConfirmTakingSnapshotStatus(Integer confirmTakingSnapshotStatus) {
        this.confirmTakingSnapshotStatus = confirmTakingSnapshotStatus;
    }

    public Date getConfirmTakingSnapshotDate() {
        return confirmTakingSnapshotDate;
    }

    public void setConfirmTakingSnapshotDate(Date confirmTakingSnapshotDate) {
        this.confirmTakingSnapshotDate = confirmTakingSnapshotDate;
    }

    public Integer getCreateTakingTask() {
        return createTakingTask;
    }

    public void setCreateTakingTask(Integer createTakingTask) {
        this.createTakingTask = createTakingTask;
    }

    public Integer getCreateTakingTaskStatus() {
        return createTakingTaskStatus;
    }

    public void setCreateTakingTaskStatus(Integer createTakingTaskStatus) {
        this.createTakingTaskStatus = createTakingTaskStatus;
    }

    public Date getCreateTakingTaskDate() {
        return createTakingTaskDate;
    }

    public void setCreateTakingTaskDate(Date createTakingTaskDate) {
        this.createTakingTaskDate = createTakingTaskDate;
    }

    public Integer getAssignTakingTask() {
        return assignTakingTask;
    }

    public void setAssignTakingTask(Integer assignTakingTask) {
        this.assignTakingTask = assignTakingTask;
    }

    public Integer getAssignTakingTaskStatus() {
        return assignTakingTaskStatus;
    }

    public void setAssignTakingTaskStatus(Integer assignTakingTaskStatus) {
        this.assignTakingTaskStatus = assignTakingTaskStatus;
    }

    public Date getAssignTakingTaskDate() {
        return assignTakingTaskDate;
    }

    public void setAssignTakingTaskDate(Date assignTakingTaskDate) {
        this.assignTakingTaskDate = assignTakingTaskDate;
    }

    public Integer getInputTakingData() {
        return inputTakingData;
    }

    public void setInputTakingData(Integer inputTakingData) {
        this.inputTakingData = inputTakingData;
    }

    public Integer getInputTakingDataStatus() {
        return inputTakingDataStatus;
    }

    public void setInputTakingDataStatus(Integer inputTakingDataStatus) {
        this.inputTakingDataStatus = inputTakingDataStatus;
    }

    public Date getInputTakingDataDate() {
        return inputTakingDataDate;
    }

    public void setInputTakingDataDate(Date inputTakingDataDate) {
        this.inputTakingDataDate = inputTakingDataDate;
    }

    public Integer getGenerateTakingResult() {
        return generateTakingResult;
    }

    public void setGenerateTakingResult(Integer generateTakingResult) {
        this.generateTakingResult = generateTakingResult;
    }

    public Integer getGenerateTakingResultStatus() {
        return generateTakingResultStatus;
    }

    public void setGenerateTakingResultStatus(Integer generateTakingResultStatus) {
        this.generateTakingResultStatus = generateTakingResultStatus;
    }

    public Date getGenerateTakingResultDate() {
        return generateTakingResultDate;
    }

    public void setGenerateTakingResultDate(Date generateTakingResultDate) {
        this.generateTakingResultDate = generateTakingResultDate;
    }

    public Integer getApprovalTaking() {
        return approvalTaking;
    }

    public void setApprovalTaking(Integer approvalTaking) {
        this.approvalTaking = approvalTaking;
    }

    public Integer getApprovalTakingStatus() {
        return approvalTakingStatus;
    }

    public void setApprovalTakingStatus(Integer approvalTakingStatus) {
        this.approvalTakingStatus = approvalTakingStatus;
    }

    public Date getApprovalTakingDate() {
        return approvalTakingDate;
    }

    public void setApprovalTakingDate(Date approvalTakingDate) {
        this.approvalTakingDate = approvalTakingDate;
    }

    public Integer getPerformTakingAdjust() {
        return performTakingAdjust;
    }

    public void setPerformTakingAdjust(Integer performTakingAdjust) {
        this.performTakingAdjust = performTakingAdjust;
    }

    public Integer getPerformTakingAdjustStatus() {
        return performTakingAdjustStatus;
    }

    public void setPerformTakingAdjustStatus(Integer performTakingAdjustStatus) {
        this.performTakingAdjustStatus = performTakingAdjustStatus;
    }

    public Date getPerformTakingAdjustDate() {
        return performTakingAdjustDate;
    }

    public void setPerformTakingAdjustDate(Date performTakingAdjustDate) {
        this.performTakingAdjustDate = performTakingAdjustDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}