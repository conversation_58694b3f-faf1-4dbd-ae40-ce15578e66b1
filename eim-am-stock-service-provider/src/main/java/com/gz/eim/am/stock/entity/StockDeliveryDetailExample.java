package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockDeliveryDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockDeliveryDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDeliveryDetailIdIsNull() {
            addCriterion("delivery_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdIsNotNull() {
            addCriterion("delivery_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdEqualTo(Long value) {
            addCriterion("delivery_detail_id =", value, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdNotEqualTo(Long value) {
            addCriterion("delivery_detail_id <>", value, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdGreaterThan(Long value) {
            addCriterion("delivery_detail_id >", value, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_detail_id >=", value, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdLessThan(Long value) {
            addCriterion("delivery_detail_id <", value, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_detail_id <=", value, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdIn(List<Long> values) {
            addCriterion("delivery_detail_id in", values, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdNotIn(List<Long> values) {
            addCriterion("delivery_detail_id not in", values, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdBetween(Long value1, Long value2) {
            addCriterion("delivery_detail_id between", value1, value2, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_detail_id not between", value1, value2, "deliveryDetailId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdIsNull() {
            addCriterion("delivery_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdIsNotNull() {
            addCriterion("delivery_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdEqualTo(Long value) {
            addCriterion("delivery_id =", value, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdNotEqualTo(Long value) {
            addCriterion("delivery_id <>", value, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdGreaterThan(Long value) {
            addCriterion("delivery_id >", value, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_id >=", value, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdLessThan(Long value) {
            addCriterion("delivery_id <", value, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_id <=", value, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdIn(List<Long> values) {
            addCriterion("delivery_id in", values, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdNotIn(List<Long> values) {
            addCriterion("delivery_id not in", values, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdBetween(Long value1, Long value2) {
            addCriterion("delivery_id between", value1, value2, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andDeliveryIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_id not between", value1, value2, "deliveryId");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeIsNull() {
            addCriterion("supplies_code is null");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeIsNotNull() {
            addCriterion("supplies_code is not null");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeEqualTo(String value) {
            addCriterion("supplies_code =", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotEqualTo(String value) {
            addCriterion("supplies_code <>", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeGreaterThan(String value) {
            addCriterion("supplies_code >", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeGreaterThanOrEqualTo(String value) {
            addCriterion("supplies_code >=", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeLessThan(String value) {
            addCriterion("supplies_code <", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeLessThanOrEqualTo(String value) {
            addCriterion("supplies_code <=", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeLike(String value) {
            addCriterion("supplies_code like", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotLike(String value) {
            addCriterion("supplies_code not like", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeIn(List<String> values) {
            addCriterion("supplies_code in", values, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotIn(List<String> values) {
            addCriterion("supplies_code not in", values, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeBetween(String value1, String value2) {
            addCriterion("supplies_code between", value1, value2, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotBetween(String value1, String value2) {
            addCriterion("supplies_code not between", value1, value2, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andNumberIsNull() {
            addCriterion("number is null");
            return (Criteria) this;
        }

        public Criteria andNumberIsNotNull() {
            addCriterion("number is not null");
            return (Criteria) this;
        }

        public Criteria andNumberEqualTo(Integer value) {
            addCriterion("number =", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualTo(Integer value) {
            addCriterion("number <>", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThan(Integer value) {
            addCriterion("number >", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("number >=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThan(Integer value) {
            addCriterion("number <", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualTo(Integer value) {
            addCriterion("number <=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberIn(List<Integer> values) {
            addCriterion("number in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotIn(List<Integer> values) {
            addCriterion("number not in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberBetween(Integer value1, Integer value2) {
            addCriterion("number between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("number not between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andQualityIsNull() {
            addCriterion("quality is null");
            return (Criteria) this;
        }

        public Criteria andQualityIsNotNull() {
            addCriterion("quality is not null");
            return (Criteria) this;
        }

        public Criteria andQualityEqualTo(Integer value) {
            addCriterion("quality =", value, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityNotEqualTo(Integer value) {
            addCriterion("quality <>", value, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityGreaterThan(Integer value) {
            addCriterion("quality >", value, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quality >=", value, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityLessThan(Integer value) {
            addCriterion("quality <", value, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityLessThanOrEqualTo(Integer value) {
            addCriterion("quality <=", value, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityIn(List<Integer> values) {
            addCriterion("quality in", values, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityNotIn(List<Integer> values) {
            addCriterion("quality not in", values, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityBetween(Integer value1, Integer value2) {
            addCriterion("quality between", value1, value2, "quality");
            return (Criteria) this;
        }

        public Criteria andQualityNotBetween(Integer value1, Integer value2) {
            addCriterion("quality not between", value1, value2, "quality");
            return (Criteria) this;
        }

        public Criteria andRealNumberIsNull() {
            addCriterion("real_number is null");
            return (Criteria) this;
        }

        public Criteria andRealNumberIsNotNull() {
            addCriterion("real_number is not null");
            return (Criteria) this;
        }

        public Criteria andRealNumberEqualTo(Integer value) {
            addCriterion("real_number =", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberNotEqualTo(Integer value) {
            addCriterion("real_number <>", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberGreaterThan(Integer value) {
            addCriterion("real_number >", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("real_number >=", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberLessThan(Integer value) {
            addCriterion("real_number <", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberLessThanOrEqualTo(Integer value) {
            addCriterion("real_number <=", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberIn(List<Integer> values) {
            addCriterion("real_number in", values, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberNotIn(List<Integer> values) {
            addCriterion("real_number not in", values, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberBetween(Integer value1, Integer value2) {
            addCriterion("real_number between", value1, value2, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("real_number not between", value1, value2, "realNumber");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonIsNull() {
            addCriterion("difference_reason is null");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonIsNotNull() {
            addCriterion("difference_reason is not null");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonEqualTo(String value) {
            addCriterion("difference_reason =", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonNotEqualTo(String value) {
            addCriterion("difference_reason <>", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonGreaterThan(String value) {
            addCriterion("difference_reason >", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonGreaterThanOrEqualTo(String value) {
            addCriterion("difference_reason >=", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonLessThan(String value) {
            addCriterion("difference_reason <", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonLessThanOrEqualTo(String value) {
            addCriterion("difference_reason <=", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonLike(String value) {
            addCriterion("difference_reason like", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonNotLike(String value) {
            addCriterion("difference_reason not like", value, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonIn(List<String> values) {
            addCriterion("difference_reason in", values, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonNotIn(List<String> values) {
            addCriterion("difference_reason not in", values, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonBetween(String value1, String value2) {
            addCriterion("difference_reason between", value1, value2, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andDifferenceReasonNotBetween(String value1, String value2) {
            addCriterion("difference_reason not between", value1, value2, "differenceReason");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andSnNoIsNull() {
            addCriterion("sn_no is null");
            return (Criteria) this;
        }

        public Criteria andSnNoIsNotNull() {
            addCriterion("sn_no is not null");
            return (Criteria) this;
        }

        public Criteria andSnNoEqualTo(String value) {
            addCriterion("sn_no =", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotEqualTo(String value) {
            addCriterion("sn_no <>", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoGreaterThan(String value) {
            addCriterion("sn_no >", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoGreaterThanOrEqualTo(String value) {
            addCriterion("sn_no >=", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLessThan(String value) {
            addCriterion("sn_no <", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLessThanOrEqualTo(String value) {
            addCriterion("sn_no <=", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLike(String value) {
            addCriterion("sn_no like", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotLike(String value) {
            addCriterion("sn_no not like", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoIn(List<String> values) {
            addCriterion("sn_no in", values, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotIn(List<String> values) {
            addCriterion("sn_no not in", values, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoBetween(String value1, String value2) {
            addCriterion("sn_no between", value1, value2, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotBetween(String value1, String value2) {
            addCriterion("sn_no not between", value1, value2, "snNo");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeIsNull() {
            addCriterion("inventory_out_time is null");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeIsNotNull() {
            addCriterion("inventory_out_time is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeEqualTo(Date value) {
            addCriterion("inventory_out_time =", value, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeNotEqualTo(Date value) {
            addCriterion("inventory_out_time <>", value, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeGreaterThan(Date value) {
            addCriterion("inventory_out_time >", value, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("inventory_out_time >=", value, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeLessThan(Date value) {
            addCriterion("inventory_out_time <", value, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeLessThanOrEqualTo(Date value) {
            addCriterion("inventory_out_time <=", value, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeIn(List<Date> values) {
            addCriterion("inventory_out_time in", values, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeNotIn(List<Date> values) {
            addCriterion("inventory_out_time not in", values, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeBetween(Date value1, Date value2) {
            addCriterion("inventory_out_time between", value1, value2, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andInventoryOutTimeNotBetween(Date value1, Date value2) {
            addCriterion("inventory_out_time not between", value1, value2, "inventoryOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeIsNull() {
            addCriterion("plan_out_time is null");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeIsNotNull() {
            addCriterion("plan_out_time is not null");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeEqualTo(Date value) {
            addCriterion("plan_out_time =", value, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeNotEqualTo(Date value) {
            addCriterion("plan_out_time <>", value, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeGreaterThan(Date value) {
            addCriterion("plan_out_time >", value, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("plan_out_time >=", value, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeLessThan(Date value) {
            addCriterion("plan_out_time <", value, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeLessThanOrEqualTo(Date value) {
            addCriterion("plan_out_time <=", value, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeIn(List<Date> values) {
            addCriterion("plan_out_time in", values, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeNotIn(List<Date> values) {
            addCriterion("plan_out_time not in", values, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeBetween(Date value1, Date value2) {
            addCriterion("plan_out_time between", value1, value2, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andPlanOutTimeNotBetween(Date value1, Date value2) {
            addCriterion("plan_out_time not between", value1, value2, "planOutTime");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeIsNull() {
            addCriterion("real_warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeIsNotNull() {
            addCriterion("real_warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeEqualTo(String value) {
            addCriterion("real_warehouse_code =", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotEqualTo(String value) {
            addCriterion("real_warehouse_code <>", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeGreaterThan(String value) {
            addCriterion("real_warehouse_code >", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("real_warehouse_code >=", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeLessThan(String value) {
            addCriterion("real_warehouse_code <", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("real_warehouse_code <=", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeLike(String value) {
            addCriterion("real_warehouse_code like", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotLike(String value) {
            addCriterion("real_warehouse_code not like", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeIn(List<String> values) {
            addCriterion("real_warehouse_code in", values, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotIn(List<String> values) {
            addCriterion("real_warehouse_code not in", values, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeBetween(String value1, String value2) {
            addCriterion("real_warehouse_code between", value1, value2, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("real_warehouse_code not between", value1, value2, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andIsSendIsNull() {
            addCriterion("is_send is null");
            return (Criteria) this;
        }

        public Criteria andIsSendIsNotNull() {
            addCriterion("is_send is not null");
            return (Criteria) this;
        }

        public Criteria andIsSendEqualTo(Integer value) {
            addCriterion("is_send =", value, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendNotEqualTo(Integer value) {
            addCriterion("is_send <>", value, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendGreaterThan(Integer value) {
            addCriterion("is_send >", value, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_send >=", value, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendLessThan(Integer value) {
            addCriterion("is_send <", value, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendLessThanOrEqualTo(Integer value) {
            addCriterion("is_send <=", value, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendIn(List<Integer> values) {
            addCriterion("is_send in", values, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendNotIn(List<Integer> values) {
            addCriterion("is_send not in", values, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendBetween(Integer value1, Integer value2) {
            addCriterion("is_send between", value1, value2, "isSend");
            return (Criteria) this;
        }

        public Criteria andIsSendNotBetween(Integer value1, Integer value2) {
            addCriterion("is_send not between", value1, value2, "isSend");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdIsNull() {
            addCriterion("delivery_plan_line_id is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdIsNotNull() {
            addCriterion("delivery_plan_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdEqualTo(Long value) {
            addCriterion("delivery_plan_line_id =", value, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdNotEqualTo(Long value) {
            addCriterion("delivery_plan_line_id <>", value, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdGreaterThan(Long value) {
            addCriterion("delivery_plan_line_id >", value, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("delivery_plan_line_id >=", value, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdLessThan(Long value) {
            addCriterion("delivery_plan_line_id <", value, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdLessThanOrEqualTo(Long value) {
            addCriterion("delivery_plan_line_id <=", value, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdIn(List<Long> values) {
            addCriterion("delivery_plan_line_id in", values, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdNotIn(List<Long> values) {
            addCriterion("delivery_plan_line_id not in", values, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdBetween(Long value1, Long value2) {
            addCriterion("delivery_plan_line_id between", value1, value2, "deliveryPlanLineId");
            return (Criteria) this;
        }

        public Criteria andDeliveryPlanLineIdNotBetween(Long value1, Long value2) {
            addCriterion("delivery_plan_line_id not between", value1, value2, "deliveryPlanLineId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}