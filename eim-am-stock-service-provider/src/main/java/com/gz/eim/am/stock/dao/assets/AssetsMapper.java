package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.dto.request.assets.AssetsCheckSearchDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.response.assets.AssetsBusinessRecordRespDTO;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockAssetsNumberGroupByCategoryCodeRespDO;
import com.gz.eim.am.stock.entity.StockAssetsNumberGroupByCategoryCodeReqDO;
import com.gz.eim.am.stock.entity.StockAssetsOperationLog;
import com.gz.eim.am.stock.entity.vo.StockAssetsExtendCommonVo;
import com.gz.eim.am.stock.entity.vo.StockAssetsInfo;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/24
 * @description:
 */
public interface AssetsMapper {
    /**
     * 根据报废资产信息查询报废资产数量
     *
     * @param assetsSearchDTO
     * @return
     */
    Long countAssetsByScrap(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据报废资产信息查询报废资产信息
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssetsInfo> selectAssetsByScrap(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据资产入库申请单查询数量
     *
     * @param assetsSearchDTO
     * @return
     */
    Long countAssetsByInventoryPlan(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据资产入库申请单查询资产信息
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssetsInfo> selectAssetsByInventoryPlan(AssetsSearchDTO assetsSearchDTO);
    /**
     * 批量插入资产
     *
     * @param newStockAssetsList
     * @return
     */
    Integer batchInsert(final List<StockAssets> newStockAssetsList);

    /**
     * 通过资产编码批量更新资产
     *
     * @param newStockAssetsList
     * @return
     */
    Integer batchUpdateByAssetsCodeList(List<StockAssets> newStockAssetsList);

    /**
     * 批量更新资产
     * @param stockAssetsList
     * @return
     */
    Integer batchUpdate(List<StockAssets> stockAssetsList);


    /**
     * 批量更新资产
     *
     * @param stockAssetsList
     * @return
     */
    Integer updateMultipleSelective(final List<StockAssets> stockAssetsList);

    /**
     * 批量插入资产操作日志记录
     *
     * @param stockAssetsOperationLogList
     * @return
     */
    Integer insertMultipleOperationLog(final List<StockAssetsOperationLog> stockAssetsOperationLogList);
    /**
     * @param:
     * @description: 根据资产类型和资产持有人以及根据资产编码和名称查询资产
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/9
     */
    List<StockAssetsInfo> selectAssetByTypeAndHolder(AssetsSearchDTO assetsSearchDTO);
    /**
     * @param:
     * @description: 根据资产类型和资产持有人以及根据资产编码和名称查询资产数量
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/9
     */
    Long countAssetByTypeAndHolder(AssetsSearchDTO assetsSearchDTO);
    /**
     * 查找持有人持有的
     *
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    List<StockAssets> selectAssetByHolder(@Param("holder") String holder, @Param("remandWarehouseCode") String remandWarehouseCode);

    /**
     * 分页查找持有人持有的
     *
     * @param holder
     * @param remandWarehouseCode
     * @param assetsCode
     * @param startNum
     * @param pageSize
     * @return
     */
    List<StockAssets> selectAssetByHolderLimit(@Param("holder") String holder, @Param("remandWarehouseCode") String remandWarehouseCode, @Param("assetsCode") String assetsCode, @Param("startNum") Integer startNum, @Param("pageSize") Integer pageSize);

    /**
     * 查找持有人持有的
     *
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    Long countAssetByHolder(@Param("holder") String holder, @Param("remandWarehouseCode") String remandWarehouseCode);

    /**
     * 查找持有人持有的
     *
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    List<StockAssets> selectAssetByHolderAndAssetsCodeList(@Param("holder") String holder, @Param("remandWarehouseCode") String remandWarehouseCode, @Param("assetsCodeList") List<String> assetsCodeList);

    /**
     * 查询业务记录
     *
     * @param assetsSearchDTO
     * @return
     */
    List<AssetsBusinessRecordRespDTO> selectAssetsBusinessRecordByParam(AssetsSearchDTO assetsSearchDTO);

    /**
     * 查询集合中存在的资产编码
     *
     * @param assetsCodes
     * @return
     */
    List<String> selectNoExistsCodeByCodes(List<String> assetsCodes);

    /**
     * 根据资产编码集合查询资产对象集合
     *
     * @param assetsCodes
     * @return
     */
    List<StockAssets> selectAssetsByCodes(List<String> assetsCodes);

    /**
     * 根据条件查询资产卡片数量
     *
     * @param assetsSearchDTO
     * @return
     */
    Long selectCountBySearchDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据条件查询资产卡片新
     *
     * @param assetsSearchDTO
     * @return
     */
    Long selectCountNewBySearchDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据条件查询资产卡片数量，报废使用
     *
     * @param assetsSearchDTO
     * @return
     */
    Long selectCountByAssetsScrap(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据条件查询资产卡片
     *
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssets> selectAssetsBySearchDTO(AssetsSearchDTO assetsSearchDTO);
    /**
     * 根据条件查询资产卡片新方法
     *
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssets> selectAssetsNewBySearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 查询全量资产数据
     *
     * @param assetsSearchDTO
     * @return
     */
    List<AssetsDTO> selectAllAssetsBySearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 根据条件查询资产卡片，资产报废使用
     *
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssets> selectAssetsByAssetsScrap(AssetsSearchDTO assetsSearchDTO);

    /**
     * @param:
     * @description: 根据资产编号
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/11
     */
    StockAssets selectAssetsOneBySearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 查询人员在某些仓库类型下领用的资产信息
     *
     * @param holder
     * @param categoryCodes
     * @return
     */
    List<AssetsDTO> selectAssetByHolderAndWareTypes(@Param("holder") String holder, @Param("categoryCodes") List<String> categoryCodes, @Param("assetsCodeList") List<String> assetsCodeList);


    /**
     * 根据条件查询资产信息集合
     *
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssetsInfo> queryAssetsBySearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 根据条件查询资产数量
     *
     * @param assetsSearchDTO
     * @return
     */
    Long queryCountBySearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 根据条件查询资产信息集合(带尾表属性)
     *
     * @param assetsSearchDTO
     * @return
     */
    List<StockAssetsExtendCommonVo> queryAssetsExtendBySearchDTO(AssetsSearchDTO assetsSearchDTO);

    /**
     * 根据条件查询资产信息数量(带尾表属性)
     *
     * @param assetsSearchDTO
     * @return
     */
    Long queryAssetsExtendCount(AssetsSearchDTO assetsSearchDTO);

    /**
     * 查询执照资产使用中的资产信息
     *
     * @param status
     * @param warehouseTypes
     * @return
     */
    List<StockAssets> selectOverReturn(@Param("status") Integer status, @Param("warehouseTypes") List<Integer> warehouseTypes, @Param("planReturnTime") Date lessThanCurrentDateTenDay);


    /**
     * 根据employId 查询资产使用人为employId或资产管理员为employId 使用中的 资产信息
     *
     * @param status
     * @param employId
     * @param categoryCodes
     * @return
     */
    List<AssetsDTO> selectByParams(@Param("status") Integer status, @Param("employId") String employId, @Param("categoryCodes") List<String> categoryCodes);

    /**
     * 资产盘点查询范围(在库数量)
     *
     * @param assetsCheckSearchDTO
     * @return
     */
    Long queryCheckCountIdle(AssetsCheckSearchDTO assetsCheckSearchDTO);

    /**
     * 资产盘点查询范围(使用中数量)
     *
     * @param assetsCheckSearchDTO
     * @return
     */
    Long queryCheckCountUsed(AssetsCheckSearchDTO assetsCheckSearchDTO);

    /**
     * 资产盘点查询范围(使用中数量)
     *
     * @param assetsCheckSearchDTO
     * @return
     */
    List<StockAssetsInfo> queryCheckAssetsIdle(AssetsCheckSearchDTO assetsCheckSearchDTO);

    /**
     * 资产盘点查询范围(使用中数量)
     *
     * @param assetsCheckSearchDTO
     * @return
     */
    List<StockAssetsInfo> queryCheckAssetsUsed(AssetsCheckSearchDTO assetsCheckSearchDTO);

    /**
     * 获取当前人员名下未归还印章数据
     * @param status
     * @param employId
     * @param categoryCode
     * @return
     */
    List<AssetsDTO> selectByUserParams(@Param("status") Integer status, @Param("employId") String employId, @Param("categoryCode") String categoryCode);

    /**
     * @param: stockAssetsNumberByCategoryCodeReqDO
     * @description:
     * @return:
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    List<StockAssetsNumberGroupByCategoryCodeRespDO> selectAssetsNumberGroupByCategoryCode(StockAssetsNumberGroupByCategoryCodeReqDO stockAssetsNumberGroupByCategoryCodeReqDO);

}

