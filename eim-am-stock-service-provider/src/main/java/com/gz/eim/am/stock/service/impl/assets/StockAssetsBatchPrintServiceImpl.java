package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsPrintImportReqDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsPrintImportRespDTO;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockAssetsPrintImport;
import com.gz.eim.am.stock.entity.vo.SuppliesCodePrintExcel;
import com.gz.eim.am.stock.service.assets.StockAssetsBatchPrintService;
import com.gz.eim.am.stock.service.assets.StockAssetsPrintImportService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.util.common.PrintUtil;
import com.gz.eim.am.stock.util.em.StockAssetsPrintImportEnum;
import com.gz.eim.plt.print.dto.request.PrintTaskReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: StockAssetsBatchPrintServiceImpl
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2021/12/24
 **/
@Service
@Slf4j
public class StockAssetsBatchPrintServiceImpl implements StockAssetsBatchPrintService {
    @Autowired
    private StockAssetsPrintImportService stockAssetsPrintImportService;
    @Autowired
    private PrintUtil printUtil;
    @Autowired
    private StockAssetsService stockAssetsService;

    /**
     * @param:
     * @description: 上传批量打印资产编码
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData upLoadBatchPrintAssetsCode(MultipartFile file, JwtUser user) throws Exception {
        // 校验逻辑
        List<String> assetsCodeList = new ArrayList<>();
        Map<String, StockAssets> stockAssetsMap = new HashMap<>();
        // 校验是否合法
        String stringErrorMessage = checkStockUpLoadBatchPrintAssetsCode(file, assetsCodeList, stockAssetsMap);
        if(StringUtils.isNotEmpty(stringErrorMessage)){
            return ResponseData.createFailResult(stringErrorMessage);
        }
        // 获取批次号
        String batchNo = DateUtils.dateFormat(new Date(), "yyyyMMddHHmmss");
        // 这里交给前端打印，后端无需生成标签
        // 需要生成的标签的资产编码
        //List<StockAssetsPrintImportReqDTO> batchUpdateStockAssetsPrintImportsReqDTOList = new ArrayList<>();
        // 需要入库临时表的数据
        List<StockAssetsPrintImport> batchInsertStockAssetsPrintImportsList = new ArrayList<>();
        // 拼装入库信息
        for (String assetsCode : assetsCodeList) {
            // 这里在前面校验过，一定是存在的
            StockAssets stockAssets = stockAssetsMap.get(assetsCode);
            // 拷贝公共信息
            StockAssetsPrintImport stockAssetsPrintImport = new StockAssetsPrintImport();
            stockAssetsPrintImport.setAssetsCode(assetsCode);
            stockAssetsPrintImport.setBatchNo(batchNo);
            stockAssetsPrintImport.setAssetsName(stockAssets.getAssetsName());
            stockAssetsPrintImport.setSnCode(stockAssets.getSnCode());
            stockAssetsPrintImport.setCreatedBy(user.getEmployeeCode());
            stockAssetsPrintImport.setUpdatedBy(user.getEmployeeCode());
            stockAssetsPrintImport.setStatus(StockAssetsPrintImportEnum.Status.PREPARE.getCode());
            // 这里交给前端打印，所以后端无需生成url，默认已经生成完成
//            stockAssetsPrintImport.setStatus(StockAssetsPrintImportEnum.Status.CREATE.getCode());
//            // 拷贝资产信息，判断是否已经生成了url标签，没有的话还需要生成
//            stockAssetsPrintImport.setAssetsName(stockAssets.getAssetsName());
//            stockAssetsPrintImport.setSnCode(stockAssets.getSnCode());
//            if (StringUtils.isNotEmpty(stockAssets.getLabelUrl())) {
//                stockAssetsPrintImport.setLabelUrl(stockAssets.getLabelUrl());
//                // 已经存在，就是准备的状态
//                stockAssetsPrintImport.setStatus(StockAssetsPrintImportEnum.Status.PREPARE.getCode());
//            } else {
//                // 这里准备异步创建
//                StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO = ConvertUtil.convertToType(StockAssetsPrintImportReqDTO.class, stockAssetsPrintImport);
//                stockAssetsPrintImportReqDTO.setCategory(stockAssets.getCategory());
//                batchUpdateStockAssetsPrintImportsReqDTOList.add(stockAssetsPrintImportReqDTO);
//            }
            batchInsertStockAssetsPrintImportsList.add(stockAssetsPrintImport);
        }
        // 批量插入
        stockAssetsPrintImportService.batchInsert(batchInsertStockAssetsPrintImportsList);
        // 这里交给前端打印，无需后端异步生成pdf
//        // 异步批量生成资产编码标签，然后更新数据库
//        if(!CollectionUtils.isEmpty(batchUpdateStockAssetsPrintImportsReqDTOList)){
//            // 获取计算机逻辑线程数
//            int coreThreadNum = Runtime.getRuntime().availableProcessors();
//            // 向上取整，保证用到核心线程数
//            int size = (int)Math.ceil((double)batchUpdateStockAssetsPrintImportsReqDTOList.size()/coreThreadNum);
//            List<List<StockAssetsPrintImportReqDTO>> listList = ListUtil.splitList(batchUpdateStockAssetsPrintImportsReqDTOList, size);
//            for (List<StockAssetsPrintImportReqDTO> stockAssetsPrintImportReqDTOS : listList) {
//                stockAssetsPrintImportService.batchUpdateAssetsImportAndCreatePdf(stockAssetsPrintImportReqDTOS, user);
//            }
//        }
        return ResponseData.createSuccessResult(batchNo);
    }

     /**
       * @param:
       * @description: 资产打印上传之前的校验
       * @return:
       * @author: <EMAIL>
       * @date: 2021/12/24
       */
    private String checkStockUpLoadBatchPrintAssetsCode(MultipartFile file, List<String> assetsCodeList, Map<String, StockAssets> stockAssetsMap) throws Exception{
        List<SuppliesCodePrintExcel> suppliesCodePrintExcels = ExcelUtil.importExcel(file.getInputStream(), SuppliesCodePrintExcel.class);
        if (CollectionUtils.isEmpty(suppliesCodePrintExcels)) {
            return "excel文件内容不能为空";
        }
        if(suppliesCodePrintExcels.size() > CommonConstant.BATCH_GENERATE_ASSET_CODE_MAX_NUMBER){
            return "上传条数大于200条，请分批上传";
        }
        // 校验资产的信息，不合法的资产，会将信息存储到备注中
        List<String> assetsCodeCheckList = suppliesCodePrintExcels.stream().map(SuppliesCodePrintExcel::getAssetsCode).collect(Collectors.toList());
        // 判断资产编码是否重复
        StringBuilder errorStringBuilder = new StringBuilder();
        Set<String> assetsCodeSet = new HashSet<>();
        for (String assetsCode : assetsCodeCheckList) {
            if(!assetsCodeSet.add(assetsCode)){
                errorStringBuilder.append(assetsCode + ";");
            }else {
                assetsCodeList.add(assetsCode);
            }
        }
        if(errorStringBuilder.length() > CommonConstant.NUMBER_ZERO){
            return "资产编码重复，重复的资产编码为：" + errorStringBuilder;
        }
        // 判断资产编码是否存在
        // 查询已经存在的资产编码
        Map<String, StockAssets> stockAssetsCheckMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeCheckList);
        if(MapUtils.isEmpty(stockAssetsCheckMap)){
            return "导入资产编码全部不存在，请检查后重试";
        }
        for (String assetsCode : assetsCodeCheckList) {
            if(!stockAssetsCheckMap.containsKey(assetsCode)){
                errorStringBuilder.append(assetsCode + ";");
            }else {
                stockAssetsMap.put(assetsCode, stockAssetsCheckMap.get(assetsCode));
            }
        }
        if(errorStringBuilder.length() > CommonConstant.NUMBER_ZERO){
            return "资产编码不存在，不存在的资产编码为：" + errorStringBuilder;
        }
        return "";
    }

    /**
     * @param:
     * @description: 查询批量打印资产编码
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Override
    public ResponseData selectBatchPrintAssetsCode(StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO) throws Exception{
        if(StringUtils.isEmpty(stockAssetsPrintImportReqDTO.getBatchNo())){
            throw new Exception("批量打印资产的批次号不能为空");
        }
        // 获取条数
        long count = stockAssetsPrintImportService.countBatchPrintAssetsCode(stockAssetsPrintImportReqDTO);
        if(count <= 0){
            PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
            pageRespDTO.setCount(count);
            pageRespDTO.setPageNum(stockAssetsPrintImportReqDTO.getPageNum());
            pageRespDTO.setPageSize(stockAssetsPrintImportReqDTO.getPageSize());
            return pageRespDTO;
        }
        // 初始化分页
        stockAssetsPrintImportReqDTO.initPageParam();
        List<StockAssetsPrintImport> stockAssetsPrintImportList = stockAssetsPrintImportService.selectBatchPrintAssetsCode(stockAssetsPrintImportReqDTO);
        if(CollectionUtils.isEmpty(stockAssetsPrintImportList)){
            return ResponseData.createFailResult("此批次号没有上传的资产信息");
        }
        List<String> assetsCodeList = stockAssetsPrintImportList.stream().map(StockAssetsPrintImport :: getAssetsCode).collect(Collectors.toList());
        // 查询资产信息Map
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        if(MapUtils.isEmpty(stockAssetsMap)){
            return ResponseData.createFailResult("此批次号没有上传的资产信息");
        }
        // 循环拷贝返回值
        List<StockAssetsPrintImportRespDTO> stockAssetsPrintImportRespDTOList = new ArrayList<>();
        for (StockAssetsPrintImport stockAssetsPrintImport : stockAssetsPrintImportList) {
            StockAssetsPrintImportRespDTO stockAssetsPrintImportRespDTO = ConvertUtil.convertToType(StockAssetsPrintImportRespDTO.class, stockAssetsPrintImport);
            stockAssetsPrintImportRespDTO.setAssetCode(stockAssetsPrintImport.getAssetsCode());
            StockAssets stockAssets = stockAssetsMap.get(stockAssetsPrintImport.getAssetsCode());
            if(stockAssets != null){
                stockAssetsPrintImportRespDTO.setCategoryName(stockAssets.getCategory());
            }
            stockAssetsPrintImportRespDTOList.add(stockAssetsPrintImportRespDTO);
        }
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes(stockAssetsPrintImportRespDTOList);
        pageRespDTO.setCount(count);
        pageRespDTO.setPageNum(stockAssetsPrintImportReqDTO.getPageNum());
        pageRespDTO.setPageSize(stockAssetsPrintImportReqDTO.getPageSize());
        return pageRespDTO;
    }

    /**
     * @param:
     * @description: 开始批量打印资产编码
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData submitBatchPrintAssetsCode(List<StockAssetsPrintImportReqDTO> stockAssetsPrintImportReqDTOList, String printKey) {
        String errorMessage = checkStockAssetsPrintImportReqDTOList(stockAssetsPrintImportReqDTOList, printKey);
        // 错误信息校验
        if(StringUtils.isNotEmpty(errorMessage)){
            return ResponseData.createFailResult(errorMessage);
        }
        // 这里交给前端打印，直接更新数据就可以
        List<StockAssetsPrintImport> updateStockAssetsPrintImportList = new ArrayList<>();
        for (StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO : stockAssetsPrintImportReqDTOList) {
            // 同时生成更新数据
            StockAssetsPrintImport stockAssetsPrintImport = ConvertUtil.convertToType(StockAssetsPrintImport.class, stockAssetsPrintImportReqDTO);
            stockAssetsPrintImport.setStatus(StockAssetsPrintImportEnum.Status.NODE.getCode());
            updateStockAssetsPrintImportList.add(stockAssetsPrintImport);
        }
        log.info("开始更新临时表数据为打印完成---------");
        int num = stockAssetsPrintImportService.batchUpdate(updateStockAssetsPrintImportList,false);
        boolean flag = num > CommonConstant.NUMBER_ZERO;
        log.info("更新临时表数据为打印完成状态为：" + flag + "---------");
        return ResponseData.createSuccessResult();
        // 这里交给前端打印
//        // 调用打印服务
//        log.info("开始调用打印服务进行打印---------");
//        List<PrintTaskReqDTO> printTaskReqDTOList = new ArrayList<>();
//        List<StockAssetsPrintImport> updateStockAssetsPrintImportList = new ArrayList<>();
//        for (StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO : stockAssetsPrintImportReqDTOList) {
//            PrintTaskReqDTO printTaskReqDTO = new PrintTaskReqDTO();
//            printTaskReqDTO.setFileDownloadUrl(stockAssetsPrintImportReqDTO.getLabelUrl());
//            printTaskReqDTO.setPrintKey(printKey);
//            printTaskReqDTO.setWidth((int) FileConstant.ASSET_CODE_FILE_Y);
//            printTaskReqDTO.setHeight((int) FileConstant.ASSET_CODE_FILE_X);
//            printTaskReqDTOList.add(printTaskReqDTO);
//            // 同时生成更新数据
//            StockAssetsPrintImport stockAssetsPrintImport = ConvertUtil.convertToType(StockAssetsPrintImport.class, stockAssetsPrintImportReqDTO);
//            stockAssetsPrintImport.setStatus(StockAssetsPrintImportEnum.Status.NODE.getCode());
//            updateStockAssetsPrintImportList.add(stockAssetsPrintImport);
//        }
//
//        //调用打印
//        boolean printResult = printUtil.savePrintTask(printTaskReqDTOList);
//        log.info("调用打印服务进行打印完成，调用状态为" + printResult + "---------");
//        // 回更状态信息，为打印完成
//        if(!printResult){
//            return ResponseData.createFailResult("资产标签打印失败请稍后重试。。。。。。");
//        }
//        log.info("开始更新临时表数据为打印完成---------");
//        int num = stockAssetsPrintImportService.batchUpdate(updateStockAssetsPrintImportList,false);
//        boolean flag = num > CommonConstant.NUMBER_ZERO;
//        log.info("更新临时表数据为打印完成状态为：" + flag + "---------");
//        return ResponseData.createSuccessResult();
    }

    /**
     * @param:
     * @description: 校验资产编码批量打印内容
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    private String checkStockAssetsPrintImportReqDTOList(List<StockAssetsPrintImportReqDTO> stockAssetsPrintImportReqDTOList, String printKey) {
        if(CollectionUtils.isEmpty(stockAssetsPrintImportReqDTOList)){
            return "批量打印内容不能为空";
        }
        if(stockAssetsPrintImportReqDTOList.size() > CommonConstant.BATCH_GENERATE_ASSET_CODE_MAX_NUMBER){
            return "打印信息条数大于200条，请分批打印";
        }
        // 这里交给前端打印，无需传打印机编号
//        if(StringUtils.isEmpty(printKey)){
//            return "请选择打印机";
//        }
        // 校验空值字段
        StringBuilder emptyStringBuilder = new StringBuilder();
        for (int i = 0; i < stockAssetsPrintImportReqDTOList.size(); i++) {
            StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO = stockAssetsPrintImportReqDTOList.get(i);
            if(null == stockAssetsPrintImportReqDTO){
                emptyStringBuilder.append("第" + (i + 1) + "行的数据不能为空");
                continue;
            }
            if(null == stockAssetsPrintImportReqDTO.getId()){
                emptyStringBuilder.append("第" + (i + 1) + "行的id不能为空");
            }
        }
        // 获取到所有的idList
        List<Long> idList = stockAssetsPrintImportReqDTOList.stream().map(StockAssetsPrintImportReqDTO :: getId).collect(Collectors.toList());
        StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO = new StockAssetsPrintImportReqDTO();
        stockAssetsPrintImportReqDTO.setIdList(idList);
        List<StockAssetsPrintImport> stockAssetsPrintImportList = stockAssetsPrintImportService.selectBatchPrintAssetsCode(stockAssetsPrintImportReqDTO);
        if(CollectionUtils.isEmpty(stockAssetsPrintImportList)){
            return "批量打印内容不存在";
        }
        // 转化为map，方便进行校验
        Map<Long, StockAssetsPrintImport> stockAssetsPrintImportMap = stockAssetsPrintImportList.stream().collect(Collectors.toMap(StockAssetsPrintImport :: getId, stockAssetsPrintImport -> stockAssetsPrintImport,(v1, v2) -> v2));
        // 判断传入数据是否存在
        StringBuilder judgeExistBuilder = new StringBuilder();
        // 判断备注信息是否为空
        StringBuilder judgeRemarkBuilder = new StringBuilder();
        // 判断状态是否为完成状态
        StringBuilder judgeDoneStatusBuilder = new StringBuilder();
        // 判断状态是否为创建中状态
        StringBuilder judgeCreateStatusBuilder = new StringBuilder();
        for (int i = 0; i < stockAssetsPrintImportReqDTOList.size(); i++) {
            StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTOFor = stockAssetsPrintImportReqDTOList.get(i);
            StockAssetsPrintImport stockAssetsPrintImport = stockAssetsPrintImportMap.get(stockAssetsPrintImportReqDTOFor.getId());
            if(stockAssetsPrintImport == null){
                judgeExistBuilder.append("第" + (i + 1) + "行数据在表中不存在；");
                continue;
            }
//            if(StringUtils.isNotEmpty(stockAssetsPrintImport.getRemark())){
//                judgeRemarkBuilder.append("第" + (i + 1) + "行数据异常信息不是空，不能提交；");
//                continue;
//            }
            if(StockAssetsPrintImportEnum.Status.NODE.getCode().equals(stockAssetsPrintImport.getStatus())){
                judgeDoneStatusBuilder.append("第" + (i + 1) + "行数据已经在本批次打印，不要重复打印；");
                continue;
            }
            // 这里交给前端打印，没有生成中的状态
//            if(StockAssetsPrintImportEnum.Status.CREATE.getCode().equals(stockAssetsPrintImport.getStatus())){
//                judgeCreateStatusBuilder.append("第" + (i + 1) + "行数据的标签还未生成，请稍等。。。。。。；");
//                continue;
//            }
            stockAssetsPrintImportReqDTOFor.setLabelUrl(stockAssetsPrintImport.getLabelUrl());
        }
        // 返回异常信息
        if(judgeExistBuilder.length() > 0){
            return judgeExistBuilder.toString();
        }
        if(judgeRemarkBuilder.length() > 0){
            return judgeRemarkBuilder.toString();
        }
        if(judgeDoneStatusBuilder.length() > 0){
            return judgeDoneStatusBuilder.toString();
        }
        if(judgeCreateStatusBuilder.length() > 0){
            return judgeCreateStatusBuilder.toString();
        }
        return "";
    }
}
