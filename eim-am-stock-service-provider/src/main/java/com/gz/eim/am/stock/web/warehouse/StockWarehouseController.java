package com.gz.eim.am.stock.web.warehouse;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.warehouse.StockWarehouseApi;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.dto.request.warehouse.WarehouseReqDTO;
import com.gz.eim.am.stock.dto.request.warehouse.WarehouseSearchReqDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.interceptor.DocTypeAspect;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseConfigService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 10:47
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/api/am/stock")
public class StockWarehouseController implements StockWarehouseApi {

    @Autowired
    private StockWarehouseService service;
    @Autowired
    private StockWarehouseConfigService stockWarehouseConfigService;

    @Override
    public ResponseData addWarehouse(WarehouseReqDTO warehouseReqDTO) {
        log.info("/api/am/stock/warehouse {}", warehouseReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.addWarehouse(warehouseReqDTO, user);
        } catch (Exception e){
            log.error("仓库新增异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectWarehouse(WarehouseSearchReqDTO reqDTO) {
        log.info("/api/am/stock/warehouse param{}", reqDTO.toString());
        ResponseData res = null;
        try {

            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.queryWarehouse(reqDTO, user);
        } catch (Exception e){
            log.error("仓库查询异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @DocTypeAnnotation(DocTypeConstant.RECEIVE_PLAN_OUT)
    @Override
    public ResponseData queryWarehouseByReceiveAssets(WarehouseSearchReqDTO warehouseSearchReqDTO) {
        log.info("/api/am/stock/queryWarehouseByReceiveAssets,warehouseSearchReqDTO{}", JSON.toJSONString(warehouseSearchReqDTO));
        ResponseData res;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.queryWarehouseByReceiveAssets(warehouseSearchReqDTO, user);
        } catch (Exception e){
            log.error("领用资产查看仓库信息异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectWarehouseVague(String param, Integer limit, Integer isCrossedAuthority,Integer docType,Integer partAuthFlag,String warehouseTypeList) {
        log.info("/api/am/stock/warehouse/vague param:{},limit:{},docType:{},warehouseTypeList{}", param,limit,docType,warehouseTypeList);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            if (docType != null){
                DocTypeAspect.threadLocal.set(docType);
            }
            res = this.service.queryWarehouseByParam(param, limit, user, isCrossedAuthority, partAuthFlag, warehouseTypeList);
            if (docType != null){
                DocTypeAspect.threadLocal.remove();
            }
        } catch (Exception e){
            log.error("仓库模糊查询异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectWarehouseByCode(String warehouseCode) {
        log.info("/api/am/stock/warehouse/{}", warehouseCode);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.selectWarehouseDetail(warehouseCode, user);
        } catch (Exception e) {
            log.error("仓库编码查询异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData updateWarehouse(WarehouseReqDTO warehouseReqDTO) {
        log.info("/api/am/stock/warehouse/{}", warehouseReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.modifyWarehouse(warehouseReqDTO,user);
        } catch (Exception e){
            log.error("仓库编辑异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    @Override
    public ResponseData getWarehouseByCode(final List<String> codes) {
        log.info("/api/am/stock/warehouse/find-by-code/{}", codes);
        List<WarehouseRespDTO> respDTOList = this.service.selectWarehouseDetailByCode(codes);
        return ResponseData.createSuccessResult(respDTOList);
    }

    @Override
    public ResponseData getDemandWarehouseConfig() {
        log.info("/api/am/stock/warehouse/getDemandWarehouseConfig");
        return stockWarehouseConfigService.queryDemandBillWarehouseConfig();
    }
}
