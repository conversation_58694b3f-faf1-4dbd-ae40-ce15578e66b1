package com.gz.eim.am.stock.service.impl.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandHeadReqDTO;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandLineReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.dto.response.order.plan.DeliveryPlanHeadRespDTO;
import com.gz.eim.am.stock.dto.response.order.plan.DeliveryPlanLineRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.StockDeliveryPlanLineInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsInitializationService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.manage.StockSuppliesConfigService;
import com.gz.eim.am.stock.service.order.plan.*;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.util.em.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: hedahong
 * @date: 2019-12-30 PM 5:58
 * @description: 资产领用
 */
@Service
public class StockPlanAssetsReceiveServiceImpl implements StockPlanAssetsReceiveService {
    @Autowired
    private StockDeliveryPlanHeadService stockDeliveryPlanHeadService;
    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;
    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;
    @Autowired
    private StockSuppliesConfigService stockSuppliesConfigService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockAssetsInitializationService stockAssetsInitializationService;
    @Autowired
    private StockWarehouseBaseService stockWarehouseBaseService;
    @Autowired
    private StockPlanAssetsDemandLineService stockPlanAssetsDemandLineService;
    @Autowired
    private StockPlanAssetsDemandHeadService stockPlanAssetsDemandHeadService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanAssetsReceive(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws ParseException {
        String checkSaveParam = checkSaveParam(deliveryPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank(checkSaveParam)) {
            return ResponseData.createFailResult(checkSaveParam);
        }

        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead();
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = new ArrayList<>();
        //资产领用领用出库单赋值
        prepareSaveDbBeanDTO(deliveryPlanHeadReqDTO, stockDeliveryPlanHead, stockDeliveryPlanLineList, user);
        stockDeliveryPlanHeadService.insertStockDeliveryPlanHead(stockDeliveryPlanHead);
        if (!CollectionUtils.isEmpty(stockDeliveryPlanLineList)) {
            stockDeliveryPlanLineList.forEach(stockDeliveryPlanLine -> stockDeliveryPlanLine.setDeliveryPlanHeadId(stockDeliveryPlanHead.getDeliveryPlanHeadId()));
            stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLine(stockDeliveryPlanLineList);
        }

        return ResponseData.createSuccessResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanAssetsReceiveByService(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws Exception {
        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead();
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = new ArrayList<>();
        //资产领用领用出库单赋值
        prepareSaveDbBeanDTO(deliveryPlanHeadReqDTO, stockDeliveryPlanHead, stockDeliveryPlanLineList, user);
        stockDeliveryPlanHeadService.insertStockDeliveryPlanHead(stockDeliveryPlanHead);
        Long deliveryPlanHeadId = stockDeliveryPlanHead.getDeliveryPlanHeadId();
        deliveryPlanHeadReqDTO.setDeliveryPlanHeadId(deliveryPlanHeadId);
        if (!CollectionUtils.isEmpty(stockDeliveryPlanLineList)) {
            stockDeliveryPlanLineList.forEach(stockDeliveryPlanLine -> stockDeliveryPlanLine.setDeliveryPlanHeadId(deliveryPlanHeadId));
            stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLine(stockDeliveryPlanLineList);
        }
        //生成出库单
        stockDeliveryPlanHeadService.generateStockDelivery(deliveryPlanHeadReqDTO, Boolean.TRUE, user);
        return ResponseData.createSuccessResult();
    }

    /**
     * 保存领用领用出库单赋值
     */
    private void prepareSaveDbBeanDTO(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO,
                                      StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> stockDeliveryPlanLineList, JwtUser user) throws ParseException {

        stockDeliveryPlanHead.setDeliveryPlanNo(stockDeliveryPlanHeadService.getDeliveryPlanNo(DeliveryPlanHeadEnum.OutType.ASSET_USE.getCode()));
        stockDeliveryPlanHead.setOutStockType(DeliveryPlanHeadEnum.OutType.ASSET_USE.getCode());
        stockDeliveryPlanHead.setOutWarehouseCode(deliveryPlanHeadReqDTO.getOutWarehouseCode());
        stockDeliveryPlanHead.setDutyUser(deliveryPlanHeadReqDTO.getDutyUser());
        stockDeliveryPlanHead.setUseUser(deliveryPlanHeadReqDTO.getUseUser());
        if (StringUtils.isNotBlank(stockDeliveryPlanHead.getDutyUser())) {
            stockDeliveryPlanHead.setDutyUser(user.getEmployeeCode());
        }
        if (null != deliveryPlanHeadReqDTO.getReasonCode()) {
            stockDeliveryPlanHead.setReasonCode(deliveryPlanHeadReqDTO.getReasonCode());
        } else {
            stockDeliveryPlanHead.setReasonCode(DeliveryPlanHeadEnum.Reason.ENTRY_USE.getCode());
        }
        if (StringUtils.isNotBlank(deliveryPlanHeadReqDTO.getPlanReturnTime())) {
            stockDeliveryPlanHead.setPlanReturnTime(DateUtils.dateParse(deliveryPlanHeadReqDTO.getPlanReturnTime(), DateUtils.DATE_PATTERN));
        }
        stockDeliveryPlanHead.setBillingTime(new Date());
        stockDeliveryPlanHead.setBillingUser(user.getEmployeeCode());
        stockDeliveryPlanHead.setRemark(deliveryPlanHeadReqDTO.getRemark());
        if (StringUtils.isNotBlank(deliveryPlanHeadReqDTO.getPlanOutTime())) {
            stockDeliveryPlanHead.setPlanOutTime(DateUtils.dateParse(deliveryPlanHeadReqDTO.getPlanOutTime(), DateUtils.DATE_PATTERN));
        } else {
            stockDeliveryPlanHead.setPlanOutTime(DateUtils.dateTimeToDate(new Date()));
        }
        stockDeliveryPlanHead.setStatus(deliveryPlanHeadReqDTO.getStatus());
        if (null == stockDeliveryPlanHead.getStatus()) {
            stockDeliveryPlanHead.setStatus(DeliveryPlanHeadEnum.Status.WAIT_OUT.getCode());
        }

        stockDeliveryPlanHead.setUpdatedBy(user.getEmployeeCode());
        stockDeliveryPlanHead.setCreatedBy(user.getEmployeeCode());

        if (StringUtils.isBlank(deliveryPlanHeadReqDTO.getUseAddress())) {
            stockDeliveryPlanHead.setUseAddress("");
        } else {
            stockDeliveryPlanHead.setUseAddress(deliveryPlanHeadReqDTO.getUseAddress());
        }

        for (DeliveryPlanLineReqDTO deliveryPlanLineReqDTO : deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS()) {
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine();
            stockDeliveryPlanLine.setSuppliesCode(deliveryPlanLineReqDTO.getSuppliesCode());
            stockDeliveryPlanLine.setAssetsCode(deliveryPlanLineReqDTO.getAssetsCode());
            stockDeliveryPlanLine.setPlanOutTime(stockDeliveryPlanHead.getPlanOutTime());
            //默认1个
            stockDeliveryPlanLine.setNumber(CommonConstant.NUMBER_ONE);
            Integer realNumber = deliveryPlanLineReqDTO.getRealNumber();
            stockDeliveryPlanLine.setRealNumber(null != realNumber ? realNumber : CommonConstant.NUMBER_ONE);
            Integer status = deliveryPlanLineReqDTO.getStatus();
            stockDeliveryPlanLine.setStatus(null != status ? status : DeliveryPlanLineEnum.Status.WAIT_OUT.getCode());
            stockDeliveryPlanLine.setCreatedBy(user.getEmployeeCode());
            stockDeliveryPlanLine.setUpdatedBy(user.getEmployeeCode());
            //优化项
            stockDeliveryPlanLine.setExplainRemark("");

            stockDeliveryPlanLineList.add(stockDeliveryPlanLine);
        }

    }

    /**
     * 校验保存领用出库单参数
     */
    private String checkSaveParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) {
        if (null == deliveryPlanHeadReqDTO) {
            return "必填参数为空";
        }

        if (StringUtils.isBlank(deliveryPlanHeadReqDTO.getOutWarehouseCode())) {
            return "调出仓库不能为空";
        }

        if (StringUtils.isBlank(deliveryPlanHeadReqDTO.getUseUser())) {
            return "领用人不能为空;";
        }
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(deliveryPlanHeadReqDTO.getUseUser());
        if (null == sysUserBasicInfo) {
            return "领用人不存在";
        }
        // 判断领用人是否为外包和实习生
        Integer empType = sysUserBasicInfo.getEmpType();
        if(!SysUserEnum.empType.FORMAL.getValue().equals(empType) && !SysUserEnum.empType.OUTSOURCING.getValue().equals(empType)){
            return "领用人只能是正式或正式外签员工";
        }
        //查询仓库权限
        final List<String> wc = stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, null);
        if (CollectionUtils.isEmpty(wc)) {
            return "无操作仓库的权限";
        }

        if (!wc.get(0).equals(ManageRoleEnum.Type.ALL.getValue())) {
            if (!wc.contains(deliveryPlanHeadReqDTO.getOutWarehouseCode())) {
                return "无操作调出仓库的权限";
            }
        }
        //新建领用单详情可为空
        if (CollectionUtils.isEmpty(deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS())) {
            return null;
        }
        final StringBuilder sb = new StringBuilder();
        final Integer detailSize = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().size();
        final List<String> receiveAssets = new ArrayList<>(detailSize);
        for (int i = 0; i < detailSize; i++) {
            final DeliveryPlanLineReqDTO lineReqDTO = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().get(i);
            if (StringUtils.isBlank(lineReqDTO.getAssetsCode())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产编码不能为空;");
                continue;
            }
            final StockAssets assets = this.stockAssetsService.selectAssetsByCode(lineReqDTO.getAssetsCode());
            if (null == assets) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产信息查询不到，请检查输入；");
                continue;
            }
            if (StringUtils.isBlank(assets.getWarehouseCode()) || !assets.getWarehouseCode().equals(deliveryPlanHeadReqDTO.getOutWarehouseCode())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产不在当前仓库、或者已出库");
                continue;
            }
            if (!assets.getStatus().equals(AssetsEnum.statusType.IDLE.getValue())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产不是在库状态");
                continue;
            }
            if (!AssetsEnum.Conditions.NORMAL.getValue().equals(assets.getConditions())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产不是正常状态，不能领用");
                continue;
            }
            if (!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(assets.getApproveStatus())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产已经报废或者在报废/IT工单审批中，不能领用");
                continue;
            }
            if (receiveAssets.contains(lineReqDTO.getAssetsCode())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的资产编码重复;");
                continue;
            } else {
                receiveAssets.add(lineReqDTO.getAssetsCode());
            }
            lineReqDTO.setSuppliesCode(assets.getSuppliesCode());
        }
        if (sb.length() > 0) {
            return sb.toString();
        }

        return null;
    }

    @Override
    public ResponseData selectPlanAssetsReceive(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO, JwtUser user) {
        deliveryPlanSearchReqDTO.setOutStockType(DeliveryPlanHeadEnum.OutType.ASSET_USE.getCode());
        return stockDeliveryPlanHeadService.selectDeliveryPlan(deliveryPlanSearchReqDTO, user);
    }


    private ResponseData selectPlanAssetsReceiveDetailById(Long deliveryPlanHeadId, JwtUser user, Integer detailType) {
        if (deliveryPlanHeadId == null) {
            return ResponseData.createResult(ResponseCode.PARAMETER_ERROR);
        }
        final DeliveryPlanHeadRespDTO deliveryPlanHeadRespDTO =
                stockDeliveryPlanHeadService.selectDeliveryPlanHeadByDeliveryPlanHeadId(deliveryPlanHeadId);
        if (null == deliveryPlanHeadRespDTO) {
            return ResponseData.createFailResult("无该资产领用单");
        }
        if (!DeliveryEnum.OutType.ASSET_USE.getCode().equals(deliveryPlanHeadRespDTO.getOutStockType()) && !DeliveryPlanHeadEnum.OutType.SELF_RECEIVE.getCode().equals(deliveryPlanHeadRespDTO.getOutStockType())) {
            return ResponseData.createFailResult("没有找到对应的领用单");
        }
        List<String> employeeWc = stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, null);
        boolean roleResult = CollectionUtils.isEmpty(employeeWc) || (!employeeWc.get(0).equals(ManageRoleEnum.Type.ALL.getValue()) && !employeeWc.contains(deliveryPlanHeadRespDTO.getOutWarehouseCode()));

        if (roleResult) {
            return ResponseData.createFailResult("权限不足");
        }
        //设置状态名称、类别名称
        if (deliveryPlanHeadRespDTO.getStatus() != null) {
            deliveryPlanHeadRespDTO.setStatusName(DeliveryPlanHeadEnum.Status.fromCode(deliveryPlanHeadRespDTO.getStatus()).getValue());
        }
        if (deliveryPlanHeadRespDTO.getOutStockType() != null) {
            deliveryPlanHeadRespDTO.setOutStockTypeName(DeliveryPlanHeadEnum.OutType.fromCode(deliveryPlanHeadRespDTO.getOutStockType()).getValue());
        }
        if (deliveryPlanHeadRespDTO.getReasonCode() != null) {
            deliveryPlanHeadRespDTO.setReasonTypeName(DeliveryPlanHeadEnum.Reason.fromCode(deliveryPlanHeadRespDTO.getReasonCode()).getValue());
        }

        StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine();
        stockDeliveryPlanLine.setDeliveryPlanHeadId(deliveryPlanHeadId);
        List<DeliveryPlanLineRespDTO> deliveryPlanLineRespDTOS = stockDeliveryPlanLineService.selectDeliveryPlanRespDTOByParam(stockDeliveryPlanLine);
        deliveryPlanLineRespDTOS.forEach(deliveryPlanLineRespDTO -> {
            if (deliveryPlanLineRespDTO.getIsSend() != null) {
                deliveryPlanLineRespDTO.setIsSendName(DeliveryPlanLineEnum.Send.fromCode(deliveryPlanLineRespDTO.getIsSend()).getValue()
                );
            }

            //当前库存量

            StockSuppliesQuantityReqDTO stockSuppliesQuantityReqDTO = new StockSuppliesQuantityReqDTO();
            if (StringUtils.isNoneBlank(deliveryPlanLineRespDTO.getRealWarehouseCode())) {
                stockSuppliesQuantityReqDTO.setWarehouseCode(deliveryPlanLineRespDTO.getRealWarehouseCode());
            } else {
                stockSuppliesQuantityReqDTO.setWarehouseCode(deliveryPlanHeadRespDTO.getOutWarehouseCode());
            }
            stockSuppliesQuantityReqDTO.setSuppliesCode(deliveryPlanLineRespDTO.getSuppliesCode());
            StockSuppliesConfigResult stockSuppliesConfigResult = stockSuppliesConfigService.queryByParam(stockSuppliesQuantityReqDTO, user);
            if (stockSuppliesConfigResult != null) {
                deliveryPlanLineRespDTO.setAllNumber(stockSuppliesConfigResult.getAllNumber());
            } else {
                deliveryPlanLineRespDTO.setAllNumber(0L);
            }
        });
        // 查询人员信息
        Set<String> empIdSet = new HashSet<>();
        empIdSet.add(deliveryPlanHeadRespDTO.getUseUser());
        empIdSet.add(deliveryPlanHeadRespDTO.getReceiveUser());
        stockDeliveryPlanLineService.settingDeliveryPlanLineAssets(deliveryPlanLineRespDTOS);
        // 查询单据的申请人
        if (CollectionUtils.isNotEmpty(deliveryPlanLineRespDTOS)) {
            if (StringUtils.isNotEmpty(deliveryPlanLineRespDTOS.get(0).getReceiveItemNo())) {
                StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO = new StockAssetsDemandLineReqDTO();
                stockAssetsDemandLineReqDTO.setDemandItemNo(deliveryPlanLineRespDTOS.get(0).getReceiveItemNo());
                StockAssetsDemandLine stockAssetsDemandLine = stockPlanAssetsDemandLineService.getStockAssetsDemandLine(stockAssetsDemandLineReqDTO);
                if (stockAssetsDemandLine != null) {
                    StockAssetsDemandHeadReqDTO stockAssetsDemandHeadReqDTO = new StockAssetsDemandHeadReqDTO();
                    stockAssetsDemandHeadReqDTO.setDemandNo(stockAssetsDemandLine.getDemandNo());
                    StockAssetsDemandHead stockAssetsDemandHead = stockPlanAssetsDemandHeadService.getStockAssetsDemandHead(stockAssetsDemandHeadReqDTO);
                    empIdSet.add(stockAssetsDemandHead.getApplyUser());
                    deliveryPlanHeadRespDTO.setApplyUser(stockAssetsDemandHead.getApplyUser());
                }
            }
        }
        // 查询人员信息
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(empIdSet));
        if (sysUserBasicInfoMap != null) {
            // 设置人员信息
            if (sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getApplyUser()) != null) {
                deliveryPlanHeadRespDTO.setApplyUserName(sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getApplyUser()).getName());
            }
            if (sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getUseUser()) != null) {
                deliveryPlanHeadRespDTO.setUseUserName(sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getUseUser()).getName());
                if (null != detailType && detailType == 1) {
                    if (sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getUseUser()) != null) {
                        deliveryPlanHeadRespDTO.setUseUserHpsJobcdDescr(sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getUseUser()).getHpsJobcdDescr());
                        deliveryPlanHeadRespDTO.setUseUserDeptName(sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getUseUser()).getDeptFullName());

                    }
                }
            }
            if (sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getReceiveUser()) != null) {
                deliveryPlanHeadRespDTO.setReceiveUserName(sysUserBasicInfoMap.get(deliveryPlanHeadRespDTO.getReceiveUser()).getName());
            }
        }
        if (DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode().equals(deliveryPlanHeadRespDTO.getStatus())) {
            deliveryPlanLineRespDTOS = deliveryPlanLineRespDTOS.stream()
                    .filter(line -> DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode().equals(line.getStatus()))
                    .collect(Collectors.toList());
        }
        deliveryPlanHeadRespDTO.setDeliveryPlanLineRespDTOS(deliveryPlanLineRespDTOS);
        return ResponseData.createSuccessResult(deliveryPlanHeadRespDTO);
    }

    @Override
    public ResponseData selectPlanAssetsReceiveById(Long deliveryPlanHeadId, JwtUser user) {
        return selectPlanAssetsReceiveDetailById(deliveryPlanHeadId, user, 0);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData planAssetsReceiveBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws InvocationTargetException, IllegalAccessException, ParseException, RuntimeException {
        String checkParam = this.checkOutBoundParam(deliveryPlanHeadReqDTO, user);
        if (checkParam != null) {
            return ResponseData.createFailResult(checkParam);
        }

        //赋值
        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead();
        List<StockDeliveryPlanLine> newDeliveryPlanLineList = new ArrayList<>();
        List<StockDeliveryPlanLine> existDeliveryPlanLineList = new ArrayList<>();
        prepareOutBoundDbBeanDTO(deliveryPlanHeadReqDTO, user, stockDeliveryPlanHead, newDeliveryPlanLineList, existDeliveryPlanLineList);
        //更新出库单
        Boolean updateHeadResult = stockDeliveryPlanHeadService.updatePrimaryKeySelective(stockDeliveryPlanHead);
        if (!updateHeadResult) {
            throw new ServiceUncheckedException("系统错误");
        }

        if (existDeliveryPlanLineList.size() > 0) {
            for (StockDeliveryPlanLine stockDeliveryPlanLine : existDeliveryPlanLineList) {
                Boolean updateLineResult = stockDeliveryPlanLineService.updatePrimaryKeySelective(stockDeliveryPlanLine);
                if (!updateLineResult) {
                    throw new ServiceUncheckedException("系统错误");
                }
            }
        }
        if (newDeliveryPlanLineList.size() > 0) {
            stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLine(newDeliveryPlanLineList);
        }
        StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine();
        stockDeliveryPlanLine.setDeliveryPlanHeadId(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());
        List<DeliveryPlanLineRespDTO> deliveryPlanLineRespDTOS = stockDeliveryPlanLineService.selectDeliveryPlanRespDTOByParam(stockDeliveryPlanLine);
        deliveryPlanHeadReqDTO.setDeliveryPlanLineReqDTOS(new ArrayList<>());
        deliveryPlanLineRespDTOS.stream()
                .forEach(line -> {
                    if (DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode().equals(line.getStatus())) {
                        DeliveryPlanLineReqDTO planLineReqDTO = new DeliveryPlanLineReqDTO();
                        BeanUtils.copyProperties(line, planLineReqDTO);

                        planLineReqDTO.setThisNumber(1);
                        planLineReqDTO.setDeliveryPlanHeadId(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());
                        deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().add(planLineReqDTO);
                    }
                });


        //生成出库单
        stockDeliveryPlanHeadService.generateStockDelivery(deliveryPlanHeadReqDTO, Boolean.FALSE, user);


        return ResponseData.createSuccessResult();
    }


    /**
     * 出库操作赋值, 支持多次出库
     */
    @Deprecated
    private void prepareOutBoundDbBeanDTO(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user, StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> stockDeliveryPlanLineList) {
        //出库单是否是已出库
        Boolean isOutBoundAlready = true;
        for (DeliveryPlanLineReqDTO deliveryPlanLineReqDTO : deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS()) {
            //获取出库单详细
            StockDeliveryPlanLine stockDeliveryPlanLine = stockDeliveryPlanLineService.selectById(deliveryPlanLineReqDTO.getDeliveryPlanLineId());
            int thisNumber = deliveryPlanLineReqDTO.getThisNumber() == null ? 0 : deliveryPlanLineReqDTO.getThisNumber();
            int realNumber = stockDeliveryPlanLine.getRealNumber() == null ? 0 : stockDeliveryPlanLine.getRealNumber();
            //入库详细状态、入库实际数量、实际入库时间
            if (thisNumber == 0) {
                continue;
            }
            if ((realNumber + thisNumber) < stockDeliveryPlanLine.getNumber()) {
                stockDeliveryPlanLine.setStatus(DeliveryDetailEnum.Status.SECTION_OUT.getCode());
                stockDeliveryPlanLine.setRealNumber(realNumber + thisNumber);
                isOutBoundAlready = false;
            } else {
                stockDeliveryPlanLine.setStatus(DeliveryDetailEnum.Status.ALREADY_OUT.getCode());
                stockDeliveryPlanLine.setRealNumber(stockDeliveryPlanLine.getNumber());
            }
            stockDeliveryPlanLine.setAssetsCode(deliveryPlanLineReqDTO.getAssetsCode());
            stockDeliveryPlanLine.setUpdatedBy(user.getEmployeeCode());

            stockDeliveryPlanLineList.add(stockDeliveryPlanLine);
        }

        if (isOutBoundAlready) {
            stockDeliveryPlanHead.setStatus(DeliveryEnum.Status.ALREADY_OUT.getCode());
        } else {
            stockDeliveryPlanHead.setStatus(DeliveryEnum.Status.SECTION_OUT.getCode());
        }
        stockDeliveryPlanHead.setUpdatedBy(user.getEmployeeCode());
        stockDeliveryPlanHead.setDeliveryPlanHeadId(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());
    }

    /**
     * 出库操作赋值 一次出库
     */
    private void prepareOutBoundDbBeanDTO(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user,
                                          StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> newDeliveryPlanLineList,
                                          List<StockDeliveryPlanLine> existsDeliveryPlanLineList) {

        StockDeliveryPlanHead deliveryPlanHead =
                this.stockDeliveryPlanHeadService.selectDeliveryPlanById(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());

        for (DeliveryPlanLineReqDTO deliveryPlanLineReqDTO : deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS()) {
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine();
            stockDeliveryPlanLine.setDeliveryPlanHeadId(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());
            stockDeliveryPlanLine.setStatus(DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode());
            stockDeliveryPlanLine.setRealNumber(1);
            stockDeliveryPlanLine.setAssetsCode(deliveryPlanLineReqDTO.getAssetsCode());
            stockDeliveryPlanLine.setSuppliesCode(deliveryPlanLineReqDTO.getSuppliesCode());
            stockDeliveryPlanLine.setUpdatedBy(user.getEmployeeCode());
            stockDeliveryPlanLine.setPlanOutTime(deliveryPlanHead.getPlanOutTime());
            stockDeliveryPlanLine.setExplainRemark(deliveryPlanLineReqDTO.getExplainRemark());
            if (null != deliveryPlanLineReqDTO.getDeliveryPlanLineId()) {
                stockDeliveryPlanLine.setDeliveryPlanLineId(deliveryPlanLineReqDTO.getDeliveryPlanLineId());
                stockDeliveryPlanLine.setExplainRemark(deliveryPlanLineReqDTO.getExplainRemark());
                existsDeliveryPlanLineList.add(stockDeliveryPlanLine);
            } else {
                stockDeliveryPlanLine.setNumber(1);
                stockDeliveryPlanLine.setCreatedBy(user.getEmployeeCode());
                newDeliveryPlanLineList.add(stockDeliveryPlanLine);
            }
        }
        stockDeliveryPlanHead.setStatus(DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode());
        stockDeliveryPlanHead.setUpdatedBy(user.getEmployeeCode());
        stockDeliveryPlanHead.setDeliveryPlanHeadId(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());
    }

    /**
     * 校验出库操作参数
     */
    private String checkOutBoundParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) {
        if (null == deliveryPlanHeadReqDTO || null == deliveryPlanHeadReqDTO.getDeliveryPlanHeadId() || CollectionUtils.isEmpty(deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS())) {
            return "必填参数为空";
        }

        StockDeliveryPlanHead stockDeliveryPlanHead = stockDeliveryPlanHeadService.selectDeliveryPlanById(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId());
        if (null == stockDeliveryPlanHead) {
            return "该领用出库单不存在";
        }

        if (!DeliveryPlanHeadEnum.OutType.ASSET_USE.getCode().equals(stockDeliveryPlanHead.getOutStockType())) {
            return "该领用出库单不存在, 数据错误";
        }

        if (DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode().equals(stockDeliveryPlanHead.getStatus())) {
            return "该领用出库单已出库";
        }
        if(StringUtils.isEmpty(stockDeliveryPlanHead.getUseUser())){
            return "单据领用人不能为空";
        }
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockDeliveryPlanHead.getUseUser());
        if (null == sysUserBasicInfo) {
            return "领用人不存在";
        }
        // 判断领用人是否为外包和实习生
        Integer empType = sysUserBasicInfo.getEmpType();
        if(!SysUserEnum.empType.FORMAL.getValue().equals(empType) && !SysUserEnum.empType.OUTSOURCING.getValue().equals(empType)){
            return "领用人只能是正式或正式外签员工";
        }
        List<String> employeeWc = stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, null);
        boolean roleResult = CollectionUtils.isEmpty(employeeWc) || (!employeeWc.get(0).equals(ManageRoleEnum.Type.ALL.getValue()) && !employeeWc.contains(stockDeliveryPlanHead.getOutWarehouseCode()));
        if (roleResult) {
            return "权限不足";
        }

        String checkOutBoundLineParamResult = this.checkOutBoundLineParam(deliveryPlanHeadReqDTO, stockDeliveryPlanHead);
        if (StringUtils.isNotBlank(checkOutBoundLineParamResult)) {
            return checkOutBoundLineParamResult;
        }

        return null;
    }

    /**
     * 入库操作校验行参数
     *
     * @param deliveryPlanHeadReqDTO
     * @param stockDeliveryPlanHead
     * @return
     */
    private String checkOutBoundLineParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, StockDeliveryPlanHead stockDeliveryPlanHead) {
        StringBuilder sb = new StringBuilder();
        Boolean thisNumberAllZero = true;
        final Integer detailSize = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().size();
        final List<String> receiveAssets = new ArrayList<>(detailSize);
        for (int i = 0; i < deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().size(); i++) {
            DeliveryPlanLineReqDTO deliveryPlanLineReqDTO = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().get(i);

            if (null != deliveryPlanLineReqDTO.getDeliveryPlanLineId()) {
                StockDeliveryPlanLine stockDeliveryPlanLine = stockDeliveryPlanLineService.selectById(deliveryPlanLineReqDTO.getDeliveryPlanLineId());
                if (null == stockDeliveryPlanLine) {
                    sb.append("第" + (i + CommonConstant.NUMBER_ONE) + "行数据的行标识无效；");
                    continue;
                }

                if (!stockDeliveryPlanLine.getDeliveryPlanHeadId().equals(deliveryPlanHeadReqDTO.getDeliveryPlanHeadId())) {
                    sb.append("第" + (i + CommonConstant.NUMBER_ONE) + "行数据的行与头不匹配；");
                    continue;
                }
                deliveryPlanLineReqDTO.setAssetsCode(stockDeliveryPlanLine.getAssetsCode());
            } else {
                if (StringUtils.isBlank(deliveryPlanLineReqDTO.getAssetsCode())) {
                    sb.append("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产编码不能为空;");
                    continue;
                }
            }

            final StockAssets assets = stockAssetsService.selectAssetsByCode(deliveryPlanLineReqDTO.getAssetsCode());
            if (null == assets) {
                sb.append("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产信息查询不到，请检查输入；");
                continue;
            }
            if (!AssetsEnum.statusType.IDLE.getValue().equals(assets.getStatus())) {
                sb.append("第" + (i + CommonConstant.NUMBER_ONE) + "行数据的本次出库资产不是`在库`状态");
                continue;
            }
            if (StringUtils.isNotBlank(assets.getWarehouseCode()) && !assets.getWarehouseCode().equals(stockDeliveryPlanHead.getOutWarehouseCode())) {
                sb.append("第" + (i + CommonConstant.NUMBER_ONE) + "行数据的本次出库资产不属于当前仓库或者已出库");
                continue;
            }
            if (!AssetsEnum.Conditions.NORMAL.getValue().equals(assets.getConditions())) {
                sb.append("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产不是正常状态，不能领用");
                continue;
            }
            if (!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(assets.getApproveStatus())) {
                sb.append("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产已经报废或者在报废/IT工单审批中，不能领用");
                continue;
            }
            if (receiveAssets.contains(deliveryPlanLineReqDTO.getAssetsCode())) {
                sb.append("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产编码重复;");
                continue;
            } else {
                receiveAssets.add(deliveryPlanLineReqDTO.getAssetsCode());
            }
            thisNumberAllZero = false;
            deliveryPlanLineReqDTO.setSuppliesCode(assets.getSuppliesCode());
        }
        if (sb.length() > 0) {
            return sb.toString();
        }

        if (thisNumberAllZero) {
            return "至少存在一行本次出库数量不为空";
        }

        return null;
    }

    @Override
    public ResponseData getAssetsReceivePrintout(Long inventoryInPlanHeadId, JwtUser user) {
        return selectPlanAssetsReceiveDetailById(inventoryInPlanHeadId, user, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData assetPlanAssetReceiveInitialization(Integer batchId, JwtUser user) {
        String checkResult = checkAssetPlanAssetReceiveInitialization(batchId);
        if (StringUtils.isNotBlank(checkResult)) {
            return ResponseData.createFailResult(checkResult);
        }

        List<StockAssetsInitialization> stockAssetsInitializationList = stockAssetsInitializationService.selectAssetUseHolderAndWareHouseCodeListByBatchId(batchId);
        if (CollectionUtils.isEmpty(stockAssetsInitializationList)) {
            return ResponseData.createFailResult("未找到资产编号和对应的仓库");
        }


        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = new ArrayList<>();
        //准备计划资产领取单头数据
        prepareInitializationHeadDbBeanDTO(stockAssetsInitializationList, stockDeliveryPlanHeadList, user);
        stockDeliveryPlanHeadService.batchInsertStockDeliveryPlanHead(stockDeliveryPlanHeadList);
        StockAssetsInitialization stockAssetsInitialization = new StockAssetsInitialization();
        stockAssetsInitialization.setDeleteFlag(AssetsInitializationEnum.deleteFlag.NO.getValue());
        stockAssetsInitialization.setBatchId(batchId);
        stockAssetsInitialization.setType(AssetsInitializationEnum.type.USE.getValue());
        List<StockAssetsInitialization> stockAssetsInitializationList1 = stockAssetsInitializationService.selectAssetUseListByStockAssetsInitialization(stockAssetsInitialization);
        List<StockDeliveryPlanLineInfo> stockDeliveryPlanLineInfoList = new ArrayList<>();
        //准备计划资产领取行数据
        prepareInitializationLineDbBeanDTO(stockAssetsInitializationList1, stockDeliveryPlanHeadList, stockDeliveryPlanLineInfoList, user);
        stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLineInfo(stockDeliveryPlanLineInfoList);

        stockDeliveryPlanHeadService.assetPlanAssetReceiveInitialization(stockDeliveryPlanHeadList, stockDeliveryPlanLineInfoList, user);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData cancelPlanAssetsReceiveById(Long deliveryPlanHeadId) {
        return stockDeliveryPlanHeadService.cancelDeliveryPlanHeadByDeliveryPlanHeadId(deliveryPlanHeadId);
    }

    /**
     * //准备计划资产领取行数据
     *
     * @param stockAssetsInitializationList
     * @param stockDeliveryPlanHeadList
     * @param stockDeliveryPlanLineInfoList
     */
    private void prepareInitializationLineDbBeanDTO(List<StockAssetsInitialization> stockAssetsInitializationList, List<StockDeliveryPlanHead> stockDeliveryPlanHeadList, List<StockDeliveryPlanLineInfo> stockDeliveryPlanLineInfoList, JwtUser user) {
        for (StockAssetsInitialization stockAssetsInitialization : stockAssetsInitializationList) {
            StockDeliveryPlanLineInfo stockDeliveryPlanLineInfo = new StockDeliveryPlanLineInfo();
            stockDeliveryPlanHeadList.forEach(stockDeliveryPlanHead -> {
                if (stockDeliveryPlanHead.getUseUser().equals(stockAssetsInitialization.getHolder()) && stockDeliveryPlanHead.getOutWarehouseCode().equals(stockAssetsInitialization.getWarehouseCode())) {
                    stockDeliveryPlanLineInfo.setDeliveryPlanHeadId(stockDeliveryPlanHead.getDeliveryPlanHeadId());
                    stockDeliveryPlanLineInfo.setRealWarehouseCode(stockDeliveryPlanHead.getOutWarehouseCode());
                    stockDeliveryPlanLineInfo.setUseUser(stockDeliveryPlanHead.getUseUser());
                    stockDeliveryPlanLineInfo.setUserAddress(stockDeliveryPlanHead.getUseAddress());
                }
            });

            stockDeliveryPlanLineInfo.setSuppliesCode(stockAssetsInitialization.getSuppliesCode());
            stockDeliveryPlanLineInfo.setNumber(CommonConstant.NUMBER_ONE);
            stockDeliveryPlanLineInfo.setRealNumber(CommonConstant.NUMBER_ONE);
            stockDeliveryPlanLineInfo.setPlanOutTime(new Date());
            stockDeliveryPlanLineInfo.setIsSend(DeliveryPlanLineEnum.Send.NO.getCode());
            stockDeliveryPlanLineInfo.setAssetsCode(stockAssetsInitialization.getAssetsCode());
            stockDeliveryPlanLineInfo.setSnCode(stockAssetsInitialization.getSnCode());
            stockDeliveryPlanLineInfo.setCreatedBy(user.getEmployeeCode());
            stockDeliveryPlanLineInfo.setUpdatedBy(user.getEmployeeCode());
            stockDeliveryPlanLineInfo.setStatus(DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode());
            stockDeliveryPlanLineInfoList.add(stockDeliveryPlanLineInfo);
        }
    }

    /**
     * 准备初始化计划资产领取单头数据
     *
     * @param stockAssetsInitializationList
     * @param stockDeliveryPlanHeadList
     * @param user
     */
    private void prepareInitializationHeadDbBeanDTO(List<StockAssetsInitialization> stockAssetsInitializationList, List<StockDeliveryPlanHead> stockDeliveryPlanHeadList, JwtUser user) {
        List<String> wareCodeList = stockAssetsInitializationList.stream().map(stockAssetsInitialization -> stockAssetsInitialization.getWarehouseCode()).collect(Collectors.toList());
        final Map<String, StockWarehouseBase> warehouseBaseMapMap = new HashMap<>(8);
        List<StockWarehouseBase> stockWarehouseBaseList = stockWarehouseBaseService.getWarehouseBaseListByWareCodeList(wareCodeList);
        if (CollectionUtils.isNotEmpty(stockWarehouseBaseList)) {
            stockWarehouseBaseList.forEach(warehouseBase -> warehouseBaseMapMap.put(warehouseBase.getWarehouseCode(), warehouseBase));
        }
        for (StockAssetsInitialization stockAssetsInitialization : stockAssetsInitializationList) {

            StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead();
            stockDeliveryPlanHead.setDeliveryPlanNo(stockDeliveryPlanHeadService.getDeliveryPlanNo(DeliveryPlanHeadEnum.OutType.ASSET_USE.getCode()));
            stockDeliveryPlanHead.setOutWarehouseCode(stockAssetsInitialization.getWarehouseCode());
            stockDeliveryPlanHead.setOutStockType(DeliveryPlanHeadEnum.OutType.ASSET_USE.getCode());
            stockDeliveryPlanHead.setBillingUser(user.getEmployeeCode());
            stockDeliveryPlanHead.setBillingTime(new Date());
            stockDeliveryPlanHead.setPlanOutTime(new Date());
            stockDeliveryPlanHead.setUseUser(stockAssetsInitialization.getHolder());
            stockDeliveryPlanHead.setRemark(CommonConstant.DEFAULT_INITIALIZATION_ASSET_PURCHASE_REMARK);
            stockDeliveryPlanHead.setStatus(DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode());
            stockDeliveryPlanHead.setIsSend(DeliveryPlanHeadEnum.Send.NO.getCode());
            stockDeliveryPlanHead.setCreatedBy(user.getEmployeeCode());
            stockDeliveryPlanHead.setUpdatedBy(user.getEmployeeCode());
            if (null != warehouseBaseMapMap && StringUtils.isNotBlank(warehouseBaseMapMap.get(stockAssetsInitialization.getWarehouseCode()).getAddress())) {
                stockDeliveryPlanHead.setUseAddress(warehouseBaseMapMap.get(stockAssetsInitialization.getWarehouseCode()).getAddress());
            } else {
                stockDeliveryPlanHead.setUseAddress("");
            }

            stockDeliveryPlanHeadList.add(stockDeliveryPlanHead);
        }
    }

    private String checkAssetPlanAssetReceiveInitialization(Integer batchId) {
        if (null == batchId) {
            return "参数不能为空";
        }

        StockAssetsInitialization stockAssetsInitialization = new StockAssetsInitialization();
        stockAssetsInitialization.setBatchId(batchId);
        stockAssetsInitialization.setDeleteFlag(AssetsInitializationEnum.deleteFlag.NO.getValue());
        stockAssetsInitialization.setType(AssetsInitializationEnum.type.USE.getValue());
        List<StockAssetsInitialization> stockAssetsInitializationList = stockAssetsInitializationService.selectListByStockAssetsInitialization(stockAssetsInitialization);
        if (CollectionUtils.isEmpty(stockAssetsInitializationList)) {
            return "本次初始化的数据不存在";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < stockAssetsInitializationList.size(); i++) {
            StockAssetsInitialization stockAssetsInitialization1 = stockAssetsInitializationList.get(i);
            if (StringUtils.isBlank(stockAssetsInitialization1.getAssetsCode())) {
                sb.append("第" + (i + 1) + "条数据的资产编号为空；");
            }

            if (StringUtils.isBlank(stockAssetsInitialization1.getHolder())) {
                sb.append("第" + (i + 1) + "条数据的当前持有人为空；");
            }
        }

        if (sb.length() != 0) {
            return sb.toString();
        }

        List<String> assetCodes = stockAssetsInitializationList.stream().map(a -> a.getAssetsCode()).collect(Collectors.toList());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, assetCodes);
        List<String> oldAssetCodes = stockAssetsList.stream().map(a -> a.getAssetsCode()).collect(Collectors.toList());
        for (String assetCode : assetCodes) {
            if (!oldAssetCodes.contains(assetCode)) {
                sb.append("资产编号" + assetCode + "不存在；");
            }
        }

        for (StockAssets stockAssets : stockAssetsList) {
            if (!stockAssets.getStatus().equals(AssetsEnum.statusType.IDLE.getValue())) {
                sb.append("资产编号" + stockAssets.getAssetsCode() + "不在库；");
            }
        }

        if (sb.length() != 0) {
            return sb.toString();
        }


        return null;
    }
}


