package com.gz.eim.am.stock.service.wfl;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.approve.ApproveReqDTO;
import com.gz.eim.am.stock.entity.ambase.ApvTask;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.util.em.FlowCodeEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: lishuyang
 * @date: 2019/11/14
 * @description:
 */
public interface WflService {

    /**
     * 开始调用工作流
     * @param wflInfo
     * @return
     */
    Map<String, Object> beginAct(WflInfo wflInfo);

    /**
     * 查询工作流表单行集合
     * @param bizId
     * @param lobNo
     * @return
     */
    ResponseData selectWflFormLineListByBizId(String bizId, String lobNo);

    /**
     * 接受工作流审批状态
     * @param bizId
     * @param lobNo
     * @param approveStatus
     * @return
     */
    ResponseData receiveWflApproveResult(String bizId, String lobNo, Integer approveStatus) throws Exception;

    /**
     * 更新盘点详情流程单号
     * @param bizId
     * @param approveUser
     * @param formId
     * @param taskId
     * @return
     */
    ResponseData updateCheckTaskDetail(String bizId, String checkPerson, String formId, String taskId);

     /**
       * @param:
       * @description: 结束待办
       * @return:
       * @author: <EMAIL>
       * @date: 2021/11/23
       */
    String endWflBusiness(String bizId, String operateType, FlowCodeEnum flowCodeEnum, String opinion);

     /**
       * @param:
       * @description: 结束代办
       * @return:
       * @author: <EMAIL>
       * @date: 2022/1/20
       */
    Set<String> endWflBusinessReturnUserList(String bizId, FlowCodeEnum flowCodeEnum, String approveUser);

    /**
     * 业务方审批接口，用于盘点完成审批结束
     * @param operateType (Submit(同意)、Reject(驳回)、AddApprove(加签))
     * @param flowCode
     * @param formId
     * @param taskId
     * @param approveUser
     * @return
     */
    ResponseData businessApproveResult(String operateType,String flowCode,String formId,String taskId,String approveUser,String plusUser);
    /**
     * 审批回调接口，用来更新资产领用单的领用人
     * @return
     */
    ResponseData updateDemand(String bizId, String claimUser, String lobNo);
    /**
     * 审批回调接口，更新计划单的实际领用人
     * @return
     */
    ResponseData updatePlanAssetsDemandReceiveUser(String bizId, String receiveUser, String lobNo);

    /**
     * @param:
     * @description: 结束代办，并返回任务集合
     * @return:
     * @author: <EMAIL>
     * @date: 2022/1/20
     */
    List<ApvTask> endWflBusinessReturnTaskList(String bizId, FlowCodeEnum flowCodeEnum, String approveUser);

    /**
     * @param: approveReqDTO
     * @description: 结束代办操作
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/3/24
     */
    ResponseData executeApprovalByBiz(ApproveReqDTO approveReqDTO);
}
