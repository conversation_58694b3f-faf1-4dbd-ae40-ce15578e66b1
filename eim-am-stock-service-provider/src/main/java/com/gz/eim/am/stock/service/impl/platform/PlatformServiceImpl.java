package com.gz.eim.am.stock.service.impl.platform;


import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.file.FileAuthUtil;
import com.google.common.collect.Maps;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.service.platform.PlatformService;
import com.gz.eim.am.stock.util.common.OkHttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * @author: yangjifan1
 * @date: 2021/5/24
 * @description 平台交互实现
 */
@Slf4j
@Service
public class PlatformServiceImpl implements PlatformService {

    private final Logger logger = LoggerFactory.getLogger(PlatformServiceImpl.class);

    @Value("${project.file.systemCode}")
    private String systemCode;
    @Value("${project.file.filePath}")
    private String folderPath;
    @Value("${project.file.privateKey}")
    private String privateKey;
    @Value("${project.file.temporaryTokenPath}")
    private String url;

    @Autowired
    private FileAuthUtil fileAuthUtil;

    @Override
    public ResponseData getTemporaryToken() {
        Map<String,Object> param = Maps.newHashMapWithExpectedSize(5);
        param.put("systemCode",systemCode);
        param.put("privateKey",privateKey);
        param.put("method","ADD");
        param.put("filePath",folderPath);
        param.put("operationsNum", CommonConstant.NUMBER_100);
        return fileAuthUtil.getTemporaryToken(param);
    }
}
