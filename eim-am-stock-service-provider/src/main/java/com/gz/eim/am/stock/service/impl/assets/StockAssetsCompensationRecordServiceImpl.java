package com.gz.eim.am.stock.service.impl.assets;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.assets.AssetsCompensationRecordMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsCompensationRecordMapper;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecord;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecordExample;
import com.gz.eim.am.stock.service.assets.StockAssetsCompensationRecordService;
import com.gz.eim.am.stock.util.common.ListUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * @className: StockAssetsCompensationRecordServiceImpl
 * @description: 资产赔偿记录操作Service
 * @author: <EMAIL>
 * @date: 2022/2/15
 **/
@Service
public class StockAssetsCompensationRecordServiceImpl implements StockAssetsCompensationRecordService {
    @Autowired
    private AssetsCompensationRecordMapper assetsCompensationRecordMapper;
    @Autowired
    private StockAssetsCompensationRecordMapper stockAssetsCompensationRecordMapper;
    /**
     * @param: stockAssetsCompensationRecordReqDTOList
     * @description: 插入赔偿记录信息
     * @return: void
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public int batchInsert(List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList) {
        if(CollectionUtils.isEmpty(stockAssetsCompensationRecordList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsCompensationRecordList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsCompensationRecord>> stockAssetsCompensationRecordListList = ListUtil.splitList(stockAssetsCompensationRecordList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsCompensationRecord> stockAssetsCompensationRecords : stockAssetsCompensationRecordListList) {
                count += assetsCompensationRecordMapper.batchInsert(stockAssetsCompensationRecords);
            }
            return count;
        }else {
            return assetsCompensationRecordMapper.batchInsert(stockAssetsCompensationRecordList);
        }
    }

    @Override
    public int insertOne(StockAssetsCompensationRecord stockAssetsCompensationRecord) {
        if(null == stockAssetsCompensationRecord){
            return CommonConstant.NUMBER_ZERO;
        }
        return stockAssetsCompensationRecordMapper.insertSelective(stockAssetsCompensationRecord);
    }

    /**
     * @param: stockAssetsCompensationRecordReqDTOList
     * @description: 插入赔偿记录信息
     * @return: void
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public int batchUpdate(List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList) {
        if(CollectionUtils.isEmpty(stockAssetsCompensationRecordList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsCompensationRecordList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsCompensationRecord>> stockAssetsCompensationRecordListList = ListUtil.splitList(stockAssetsCompensationRecordList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsCompensationRecord> stockAssetsCompensationRecords : stockAssetsCompensationRecordListList) {
                count += assetsCompensationRecordMapper.batchUpdate(stockAssetsCompensationRecords);
            }
            return count;
        }else {
            return assetsCompensationRecordMapper.batchUpdate(stockAssetsCompensationRecordList);
        }
    }

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 查询资产赔偿记录列表
     * @return: List<StockAssetsCompensationRecordReqDTO>
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public List<StockAssetsCompensationRecord> selectStockAssetsCompensationRecordList(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        if(null == stockAssetsCompensationRecordReqDTO){
            return new ArrayList<>();
        }
        StockAssetsCompensationRecordExample stockAssetsCompensationRecordExample = new StockAssetsCompensationRecordExample();
        StockAssetsCompensationRecordExample.Criteria criteria = stockAssetsCompensationRecordExample.createCriteria();
        if(null != stockAssetsCompensationRecordReqDTO.getId()){
            criteria.andIdEqualTo(stockAssetsCompensationRecordReqDTO.getId());
        }
        if(!CollectionUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getIds())){
            criteria.andIdIn(stockAssetsCompensationRecordReqDTO.getIds());
        }
        if(!StringUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getCompensationUser())){
            criteria.andCompensationUserEqualTo(stockAssetsCompensationRecordReqDTO.getCompensationUser());
        }
        if(!CollectionUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getCompensationUserList())){
            criteria.andCompensationUserIn(stockAssetsCompensationRecordReqDTO.getCompensationUserList());
        }
        if(!CollectionUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getAssetsCodeList())){
            criteria.andAssetsCodeIn(stockAssetsCompensationRecordReqDTO.getAssetsCodeList());
        }
        if(!StringUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getLeaveTime())){
            criteria.andLeaveTimeEqualTo(stockAssetsCompensationRecordReqDTO.getLeaveTime());
        }
        if(stockAssetsCompensationRecordReqDTO.getDataSource() != null){
            criteria.andDataSourceEqualTo(stockAssetsCompensationRecordReqDTO.getDataSource());
        }
        if(stockAssetsCompensationRecordReqDTO.getIsReturn() != null){
            criteria.andIsReturnEqualTo(stockAssetsCompensationRecordReqDTO.getIsReturn());
        }
        if(stockAssetsCompensationRecordReqDTO.getDelFlag() != null){
            criteria.andDelFlagEqualTo(stockAssetsCompensationRecordReqDTO.getDelFlag());
        }
        if(stockAssetsCompensationRecordReqDTO.getCompensationMethod() != null){
            criteria.andCompensationMethodEqualTo(stockAssetsCompensationRecordReqDTO.getCompensationMethod());
        }
        if(stockAssetsCompensationRecordReqDTO.getMinCompensationMoney() != null){
            criteria.andCompensationMoneyGreaterThan(stockAssetsCompensationRecordReqDTO.getMinCompensationMoney());
        }
        if(stockAssetsCompensationRecordReqDTO.getCompensationMoney() != null){
            criteria.andCompensationMoneyEqualTo(stockAssetsCompensationRecordReqDTO.getCompensationMoney());
        }
        if(stockAssetsCompensationRecordReqDTO.getSyncPaySystemStatus() != null){
            criteria.andSyncPaySystemStatusEqualTo(stockAssetsCompensationRecordReqDTO.getSyncPaySystemStatus());
        }
        if(!StringUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getFormId())){
            criteria.andFormIdEqualTo(stockAssetsCompensationRecordReqDTO.getFormId());
        }
        if(!CollectionUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getDataSourceList())){
            criteria.andDataSourceIn(stockAssetsCompensationRecordReqDTO.getDataSourceList());
        }
        if(!CollectionUtils.isEmpty(stockAssetsCompensationRecordReqDTO.getFormIdList())){
            criteria.andFormIdIn(stockAssetsCompensationRecordReqDTO.getFormIdList());
        }
        if(stockAssetsCompensationRecordReqDTO.getOrderByIdDesc() != null && CommonConstant.NUMBER_ONE.equals(stockAssetsCompensationRecordReqDTO.getOrderByIdDesc())){
            stockAssetsCompensationRecordExample.setOrderByClause("id desc");
        }
        if(stockAssetsCompensationRecordReqDTO.getLeaveTimeStart() != null && stockAssetsCompensationRecordReqDTO.getLeaveTimeEnd() != null){
            criteria.andLeaveTimeBetween(stockAssetsCompensationRecordReqDTO.getLeaveTimeStart(), stockAssetsCompensationRecordReqDTO.getLeaveTimeEnd());
        }
        List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList = stockAssetsCompensationRecordMapper.selectByExample(stockAssetsCompensationRecordExample);
        if(CollectionUtils.isEmpty(stockAssetsCompensationRecordList)){
            return new ArrayList<>();
        }
        return stockAssetsCompensationRecordList;
    }
}
