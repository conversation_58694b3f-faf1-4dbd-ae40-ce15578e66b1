package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsCategory;
import com.gz.eim.am.stock.entity.StockAssetsCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsCategoryMapper {
    long countByExample(StockAssetsCategoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsCategory record);

    int insertSelective(StockAssetsCategory record);

    List<StockAssetsCategory> selectByExample(StockAssetsCategoryExample example);

    StockAssetsCategory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsCategory record, @Param("example") StockAssetsCategoryExample example);

    int updateByExample(@Param("record") StockAssetsCategory record, @Param("example") StockAssetsCategoryExample example);

    int updateByPrimaryKeySelective(StockAssetsCategory record);

    int updateByPrimaryKey(StockAssetsCategory record);
}