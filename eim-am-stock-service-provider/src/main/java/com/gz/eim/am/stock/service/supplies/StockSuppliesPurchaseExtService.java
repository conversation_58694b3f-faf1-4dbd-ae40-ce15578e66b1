package com.gz.eim.am.stock.service.supplies;

import com.gz.eim.am.stock.entity.StockSuppliesPurchaseExt;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 3/16/21 8:12 下午
 * @description
 */
public interface StockSuppliesPurchaseExtService {

    /**
     * 根据物料编码与仓库类型查询
     * @param supplierCodeList
     * @param warehouseType
     * @return
     */
    List<StockSuppliesPurchaseExt> selectBySuppliesCodeAndWarehouseType(List<String> supplierCodeList, Integer warehouseType);



    /**
     * 根据条件查询
     * @param supplierCode
     * @param warehouseType
     * @return
     */
    StockSuppliesPurchaseExt selectByQuery(String supplierCode, Integer warehouseType,String addPurchaseMark);

}
