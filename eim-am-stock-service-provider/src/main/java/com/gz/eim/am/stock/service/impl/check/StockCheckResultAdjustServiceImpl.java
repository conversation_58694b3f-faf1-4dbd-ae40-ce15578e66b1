package com.gz.eim.am.stock.service.impl.check;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.request.file.UploadFileReqDTO;
import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.MetaDataConstants;
import com.gz.eim.am.stock.dao.base.CheckDifferenceListHeadMapper;
import com.gz.eim.am.stock.dao.check.CheckResultAdjustMapper;
import com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.dto.response.check.*;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseBaseResp;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.CheckDifferenceLineInfo;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.entity.vo.download.DiffLineApproveEntity;
import com.gz.eim.am.stock.entity.vo.download.ProfitAndLessLineApproveEntity;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.check.*;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.service.wfl.WflService;
import com.gz.eim.am.stock.util.common.WorkFlowUtil;
import com.gz.eim.am.stock.util.em.*;
import feign.form.ContentType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;

/**
 * @author: weijunjie
 * @date: 2021/2/20
 * @description
 */
@Slf4j
@Service
public class StockCheckResultAdjustServiceImpl implements StockCheckResultAdjustService {

    @Value("${project.file.systemModule}")
    private String systemModule;
    @Value("${project.file.attachModule}")
    private String attachModule;
    @Value("${project.file.filePath}")
    private String filePath;
    @Autowired
    StockTakingPlanService stockTakingPlanService;
    @Autowired
    CheckDifferenceListHeadMapper checkDifferenceListHeadMapper;
    @Autowired
    CheckDifferenceListLineService checkDifferenceListLineService;
    @Autowired
    StockTakingProcessService stockTakingProcessService;
    @Autowired
    WflService wflService;
    @Autowired
    StockCheckResultService stockCheckResultService;
    @Autowired
    FileServiceApi fileServiceApi;
    @Autowired
    CheckResultAdjustMapper checkResultAdjustMapper;
    @Autowired
    AmbaseCommonService ambaseCommonService;
    @Autowired
    StockWarehouseService stockWarehouseService;


    @Override
    public ResponseData resultAdjustDataSelect(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {

        CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO = new CheckDifferenceListHeadRespDTO();
        //1.参数校验
        String verifyResult = verifyResultQueryParam(checkDifferenceListHeadReqDTO, checkDifferenceListHeadRespDTO);
        if (StringUtils.isNotBlank(verifyResult)) {
            return ResponseData.createFailResult(verifyResult);
        }

        //3.查询盘点计划信息
        StockTakingPlanRespDTO stockTakingPlanRespDTO = new StockTakingPlanRespDTO();
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        BeanUtils.copyProperties(stockTakingPlan, stockTakingPlanRespDTO);
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockTakingPlan.getCreatedBy());
        stockTakingPlanRespDTO.setCreatedByName(sysUserBasicInfo.getName());
        if (stockTakingPlanRespDTO.getSystemDate() != null) {
            stockTakingPlanRespDTO.setSystemDateStr(DateUtils.dateFormat(stockTakingPlanRespDTO.getSystemDate(), DateUtils.HOUR_PATTERN));
        }
        checkDifferenceListHeadRespDTO.setStockTakingPlanRespDTO(stockTakingPlanRespDTO);
        //4.查询差异清单行信息
        checkDifferenceListHeadReqDTO.setHeadId(checkDifferenceListHeadRespDTO.getHeadId());
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = getDifferenceLineRespDTO(checkDifferenceListHeadReqDTO, checkDifferenceListHeadRespDTO);
        //5.补全字段值
        List<CheckDifferenceLineInfo> checkDifferenceLineInfoList = checkDifferenceListLineService.getLineInfoGroupByHandlerMethod(checkDifferenceListHeadRespDTO.getHeadId());
        if (CollectionUtils.isNotEmpty(checkDifferenceLineInfoList)) {
            Integer otherHandlerMethodCount = 0;
            for (CheckDifferenceLineInfo dto : checkDifferenceLineInfoList) {
                if (StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue().equals(dto.getActualHandleMethod())) {
                    checkDifferenceListHeadRespDTO.setCheckMoreNumber(dto.getHandlerMethodCount());
                    checkDifferenceListHeadRespDTO.setCheckMoreNetValue(dto.getEstimatedAmountSum());
                } else if (StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue().equals(dto.getActualHandleMethod())) {
                    checkDifferenceListHeadRespDTO.setCheckLessNumber(dto.getHandlerMethodCount());
                    checkDifferenceListHeadRespDTO.setCheckLessNetValue(dto.getNetValueSum());
                } else {
                    otherHandlerMethodCount = otherHandlerMethodCount + dto.getHandlerMethodCount();
                }
            }
            checkDifferenceListHeadRespDTO.setCheckOtherAdjustNumber(otherHandlerMethodCount);
        }
        checkDifferenceListHeadRespDTO.setCheckDifferenceListLineRespDTOS(checkDifferenceListLineRespDTOList);

        //6.如果当前单据类型="审批中或已审批",从ambase获取审批附件url
        String bindPrimaryKey = null;
        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(checkDifferenceListHeadReqDTO.getType())
                && !StockCheckAdjustEnum.CheckApproveStatus.WAIT_APPROVE.getValue().equals(checkDifferenceListHeadRespDTO.getProfitLossApproveFlag())) {
            bindPrimaryKey = checkDifferenceListHeadReqDTO.getTakingPlanNo() + StockCheckAdjustEnum.ApproveCheckType.V1.getValue();
        } else if (StockCheckAdjustEnum.CheckAdjustType.ADJUST_DIFF.getValue().equals(checkDifferenceListHeadReqDTO.getType())
                && !StockCheckAdjustEnum.CheckApproveStatus.WAIT_APPROVE.getValue().equals(checkDifferenceListHeadRespDTO.getDiffApproveFlag())) {
            bindPrimaryKey = checkDifferenceListHeadReqDTO.getTakingPlanNo() + StockCheckAdjustEnum.ApproveCheckType.V2.getValue();
        }
        if (StringUtils.isNotBlank(bindPrimaryKey)) {
            // 调用ambase 根据业务主键获取下载和预览url
            QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO();
            queryFileReqDTO.setSystemModule(systemModule);
            queryFileReqDTO.setAttachModule(attachModule);
            queryFileReqDTO.setRelId(bindPrimaryKey);
            ResponseData<List<PictureMetaRespDTO>> responseData = fileServiceApi.getFileUrlListByRelId(queryFileReqDTO);
            List<PictureMetaRespDTO> pictureMetaRespDTOList = responseData.getData();
            if (CollectionUtils.isNotEmpty(pictureMetaRespDTOList)) {
                checkDifferenceListHeadRespDTO.setAttachPreviewUrl(pictureMetaRespDTOList.get(CommonConstant.NUMBER_ZERO).getShowPicUrl());
                checkDifferenceListHeadRespDTO.setAttachDownLoadUrl(pictureMetaRespDTOList.get(CommonConstant.NUMBER_ZERO).getDownLoadPicUrl());
            }
        }

        //7.返回结果
        return ResponseData.createSuccessResult(checkDifferenceListHeadRespDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData approveCheckResult(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {

        //1.保存当前页调整数据
        ResponseData responseData = stockCheckResultService.saveCheckResultRemark(checkDifferenceListHeadReqDTO);
        if (ResponseCode.SYSTEM_ERROR.getCode().equals(responseData.getCode())) {
            return responseData;
        }
        //2.参数校验
        String checkResult = checkApproveParam(checkDifferenceListHeadReqDTO);
        if (StringUtils.isNotBlank(checkResult)) {
            return ResponseData.createFailResult(checkResult);
        }

        //3.查询审批数据生成excel，上传到文件平台
        checkDifferenceListHeadReqDTO.setNoPaging(true);
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = getDifferenceLineRespDTO(checkDifferenceListHeadReqDTO, null);

        MultipartFile[] arrFiles = new MultipartFile[CommonConstant.NUMBER_ONE];
        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(checkDifferenceListHeadReqDTO.getType())) {
            List<ProfitAndLessLineApproveEntity> profitAndLessLineApproveEntities = new ArrayList<>(checkDifferenceListLineRespDTOList.size());
            checkDifferenceListLineRespDTOList.stream().forEach(dto -> {
                ProfitAndLessLineApproveEntity profitAndLessLineApproveEntity = new ProfitAndLessLineApproveEntity();
                BeanUtils.copyProperties(dto, profitAndLessLineApproveEntity);
                profitAndLessLineApproveEntities.add(profitAndLessLineApproveEntity);
            });

            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ExcelUtil.createExcelWithBuffer(profitAndLessLineApproveEntities, bos);
            byte[] barry = bos.toByteArray();
            InputStream is = new ByteArrayInputStream(barry);
            MultipartFile multipartFile = new MockMultipartFile("file", "盘点盈亏明细清单.xlsx", ContentType.MULTIPART.toString(), is);
            arrFiles[0] = multipartFile;

        } else {
            List<DiffLineApproveEntity> diffLineApproveEntities = new ArrayList<>(checkDifferenceListLineRespDTOList.size());
            checkDifferenceListLineRespDTOList.stream().forEach(dto -> {
                DiffLineApproveEntity diffLineApproveEntity = new DiffLineApproveEntity();
                BeanUtils.copyProperties(dto, diffLineApproveEntity);
                diffLineApproveEntities.add(diffLineApproveEntity);
            });

            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ExcelUtil.createExcelWithBuffer(diffLineApproveEntities, bos);
            byte[] barry = bos.toByteArray();
            InputStream is = new ByteArrayInputStream(barry);
            MultipartFile multipartFile = new MockMultipartFile("file", "盘点差异明细清单.xlsx", ContentType.MULTIPART.toString(), is);
            arrFiles[0] = multipartFile;
        }
        Map<String, Object> queryParam = new HashMap<>();
        String relId = StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(checkDifferenceListHeadReqDTO.getType()) ? checkDifferenceListHeadReqDTO.getTakingPlanNo() + StockCheckAdjustEnum.ApproveCheckType.V1.getValue() :
                checkDifferenceListHeadReqDTO.getTakingPlanNo() + StockCheckAdjustEnum.ApproveCheckType.V2.getValue();
        queryParam.put("filePath", filePath);
        queryParam.put("systemModule", systemModule);
        queryParam.put("attachModule", attachModule);
        queryParam.put("relId", relId);
        queryParam.put("replaceRelIdFlag", CommonConstant.NUMBER_ONE);
        ResponseData resp = fileServiceApi.srmUpload(arrFiles[0], queryParam);
        if (!ResponseCode.SUCCESS_CODE.equals(resp.getCode())) {
            //调用接口失败，回滚事物
            log.error("上传审批附件失败：" + resp.getMessage());
            throw new ServiceUncheckedException("上传审批附件失败");
        }

        //4.异步发起工作流审批,提交工作流参数（bizid=计划单号；lobNo流程编码）
        sendTowfl(checkDifferenceListHeadReqDTO.getType(), checkDifferenceListHeadReqDTO.getTakingPlanNo());
        //5.更新清单头表的审批状态="审批中"
        CheckDifferenceListHead checkDifferenceListHead = new CheckDifferenceListHead();
        checkDifferenceListHead.setTakingPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        CheckDifferenceListHeadExample example = getExample(checkDifferenceListHead);

        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(checkDifferenceListHeadReqDTO.getType())) {
            checkDifferenceListHead.setProfitLossApproveFlag(StockCheckAdjustEnum.CheckApproveStatus.APPROVE_ING.getValue());
        } else {
            checkDifferenceListHead.setDiffApproveFlag(StockCheckAdjustEnum.CheckApproveStatus.APPROVE_ING.getValue());
        }
        checkDifferenceListHeadMapper.updateByExampleSelective(checkDifferenceListHead, example);

        //6.更新盘点流程节点 审批盘点结果="进行中"
        StockTakingProcess stockTakingProcess = new StockTakingProcess();
        stockTakingProcess.setApprovalTakingStatus(StockTakingPlanEnum.ProcessStatus.INPROCESS.getStatus());
        stockTakingProcessService.updateTakingPlanStaus(stockTakingProcess, checkDifferenceListHeadReqDTO.getTakingPlanNo());
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData updateApproveStatus(CheckDifferenceListHead checkDifferenceListHead) {
        CheckDifferenceListHeadExample example = new CheckDifferenceListHeadExample();
        CheckDifferenceListHeadExample.Criteria criteria = example.createCriteria();
        criteria.andTakingPlanNoEqualTo(checkDifferenceListHead.getTakingPlanNo());
        checkDifferenceListHeadMapper.updateByExampleSelective(checkDifferenceListHead, example);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData queryInventoryPage(AdjustResultReqDTO adjustResultReqDTO) {
        //1.查询总数
        Map<String, Object> data = new HashMap<>(2);
        JwtUser user = SecurityUtil.getJwtUser();
        if (!superAdminFlag(user.getEmployeeCode())) {
            adjustResultReqDTO.setLogonUser(SecurityUtil.getJwtUser().getEmployeeCode());
        }
        long count = checkResultAdjustMapper.queryCheckInventoryCount(adjustResultReqDTO);
        data.put("count", count);
        if (count <= CommonConstant.NUMBER_ZERO) {
            data.put("adjustResultHeadList", new ArrayList<>());
            return ResponseData.createSuccessResult(data);
        }
        //2.分页查询数据
        adjustResultReqDTO.initPageParam();
        List<AdjustResultHeadRespDTO> adjustResultHeadRespDTOS = checkResultAdjustMapper.queryCheckInventory(adjustResultReqDTO);
        //3.赋值制单人名称
        if (CollectionUtils.isNotEmpty(adjustResultHeadRespDTOS)) {
            Set<String> userCodes = new HashSet<>();
            adjustResultHeadRespDTOS.stream().forEach(dto -> userCodes.add(dto.getBillingUser()));
            List<String> empIds = new ArrayList<>(userCodes);
            Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIds);
            adjustResultHeadRespDTOS.stream().forEach(dto -> {
                //获取单据状态名称
                dto.setBillStatusName(InventoryInPlanHeadEnum.Status.fromCode(dto.getBillStatus()).getValue());
                //获取制单人名称
                dto.setBillingUserName(sysUserMap.getOrDefault(dto.getBillingUser(), new SysUser()).getName());
            });
        }
        data.put("adjustResultHeadList", adjustResultHeadRespDTOS);
        return ResponseData.createSuccessResult(data);
    }

    @Override
    public ResponseData queryDeliveryPage(AdjustResultReqDTO adjustResultReqDTO) {
        //1.查询总数
        Map<String, Object> data = new HashMap<>(2);
        JwtUser user = SecurityUtil.getJwtUser();
        if (!superAdminFlag(user.getEmployeeCode())) {
            adjustResultReqDTO.setLogonUser(SecurityUtil.getJwtUser().getEmployeeCode());
        }
        long count = checkResultAdjustMapper.queryCheckDeliveryCount(adjustResultReqDTO);
        data.put("count", count);
        if (count <= CommonConstant.NUMBER_ZERO) {
            data.put("adjustResultHeadList", new ArrayList<>());
            return ResponseData.createSuccessResult(data);
        }
        //2.分页查询数据
        adjustResultReqDTO.initPageParam();
        List<AdjustResultHeadRespDTO> adjustResultHeadRespDTOS = checkResultAdjustMapper.queryCheckDelivery(adjustResultReqDTO);
        //3.赋值制单人名称
        settingResultResp(adjustResultHeadRespDTOS,CommonConstant.NUMBER_ZERO);
        data.put("adjustResultHeadList", adjustResultHeadRespDTOS);
        return ResponseData.createSuccessResult(data);
    }

    @Override
    public ResponseData queryAdjustResultDetail(AdjustResultReqDTO adjustResultReqDTO) {
        //1.校验参数
        String billNo = adjustResultReqDTO.getBillNo();
        Integer type = adjustResultReqDTO.getType();
        if (StringUtils.isBlank(billNo) || null == type) {
            return ResponseData.createFailResult("查询参数不能为空");
        }
        adjustResultReqDTO.setLogonUser(SecurityUtil.getJwtUser().getEmployeeCode());
        adjustResultReqDTO.setNoPaging(true);

        //2.查询返回头信息
        List<AdjustResultHeadRespDTO> adjustResultHeadRespDTOS = null;
        if (CommonConstant.NUMBER_ONE.equals(type)) {
            //盘盈入库单逻辑
            adjustResultHeadRespDTOS = checkResultAdjustMapper.queryCheckInventory(adjustResultReqDTO);
        } else {
            //盘亏出库单逻辑
            adjustResultHeadRespDTOS = checkResultAdjustMapper.queryCheckDelivery(adjustResultReqDTO);
        }
        if (CollectionUtils.isEmpty(adjustResultHeadRespDTOS)) {
            log.info("单据号没有查询到对应的出入库单据 ：" + billNo);
            return ResponseData.createFailResult("单据号没有查询到对应的出入库单据");
        }
        settingResultResp(adjustResultHeadRespDTOS,type);
        AdjustResultHeadRespDTO adjustResultHeadRespDTO = adjustResultHeadRespDTOS.get(0);
        //3.查询行总数
        long count = checkResultAdjustMapper.queryCheckAdjustDetailCount(billNo);
        adjustResultHeadRespDTO.setCount(count);

        if (count <= CommonConstant.NUMBER_ZERO) {
            return ResponseData.createSuccessResult(adjustResultHeadRespDTO);
        }

        //4.查询行数据
        adjustResultReqDTO.initPageParam();
        List<AdjustResultLineRespDTO> adjustResultLineRespDTOList = checkResultAdjustMapper.queryCheckAdjustDetail(adjustResultReqDTO);
        if (CommonConstant.NUMBER_ONE.equals(type)) {
            Map<Integer, String> assetsStatusTypeMap = AssetsEnum.assetsStatusTypeMap;
            adjustResultLineRespDTOList.forEach(dto -> {
                //处理资产状态名称
                dto.setAssetsStatusName(assetsStatusTypeMap.get(dto.getAssetsStatus()));
                //处理资产管理员 盘盈入库单的资产管理员=入库申请单的制单人
                dto.setAssetsKeeperCode(adjustResultHeadRespDTO.getBillingUser());
                dto.setAssetsKeeperName(adjustResultHeadRespDTO.getBillingUserName());
            });
        } else {
            adjustResultLineRespDTOList.forEach(dto -> {
                //处理资产状态名称 资产状态=已处置
                dto.setAssetsStatusName(AssetsEnum.statusType.HANDLE.getDesc());
            });
        }
        adjustResultHeadRespDTO.setAdjustResultLineRespDTOS(adjustResultLineRespDTOList);

        return ResponseData.createSuccessResult(adjustResultHeadRespDTO);
    }

    /**
     * 盘点出入库单据赋值信息
     *
     * @param adjustResultHeadRespDTOS
     */
    private void settingResultResp(List<AdjustResultHeadRespDTO> adjustResultHeadRespDTOS, Integer type) {
        if (CollectionUtils.isNotEmpty(adjustResultHeadRespDTOS)) {
            Set<String> userCodes = new HashSet<>();
            Set<String> warehouseCodes = new HashSet<>();
            adjustResultHeadRespDTOS.stream().forEach(dto -> {
                userCodes.add(dto.getBillingUser());
                warehouseCodes.add(dto.getWarehouseCode());
            });

            List<String> empIds = new ArrayList<>(userCodes);
            List<String> codes = new ArrayList<>(warehouseCodes);

            Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIds);
            Map<String, WarehouseRespDTO> warehouseMap = stockWarehouseService.selectWarehouseDetailMapByCode(codes);

            adjustResultHeadRespDTOS.stream().forEach(dto -> {
                //获取单据状态名称
                if (CommonConstant.NUMBER_ONE.equals(type)) {
                    dto.setBillStatusName(InventoryInPlanHeadEnum.Status.fromCode(dto.getBillStatus()).getValue());
                } else {
                    dto.setBillStatusName(DeliveryPlanHeadEnum.Status.fromCode(dto.getBillStatus()).getValue());
                }
                //获取制单人名称
                dto.setBillingUserName(sysUserMap.getOrDefault(dto.getBillingUser(), new SysUser()).getName());
                //获取仓库名称
                dto.setWarehouseCodeName(warehouseMap.getOrDefault(dto.getWarehouseCode(), new WarehouseRespDTO()).getName());
                //获取仓库地址
                WarehouseBaseResp stockWarehouseBase = warehouseMap.getOrDefault(dto.getWarehouseCode(), new WarehouseRespDTO()).getWarehouseBase();
                dto.setWarehouseAddress(stockWarehouseBase != null ? stockWarehouseBase.getAddress() : "");
            });
        }
    }


    /**
     * 校验查询参数
     *
     * @param checkDifferenceListHeadReqDTO
     * @return
     * @throws Exception
     */
    private String verifyResultQueryParam(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO, CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO) throws Exception {
        if (checkDifferenceListHeadReqDTO == null) {
            return "参数不能为空";
        }

        if (null == checkDifferenceListHeadReqDTO.getType()) {
            return "异常数据类型参数不能为空";
        }
        String checkTakingNo = checkDifferenceListHeadReqDTO.getTakingPlanNo();
        if (StringUtils.isBlank(checkTakingNo)) {
            return "查询参数不能为空";
        }
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(checkTakingNo);
        if (stockTakingPlan == null) {
            return "盘点计划单编码：" + checkTakingNo + "没有查到对应单据";
        }

        JwtUser user = SecurityUtil.getJwtUser();
        CheckDifferenceListHeadRespDTO checkDifferenceListHead = getDifferenceHeadRespDTO(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        BeanUtils.copyProperties(checkDifferenceListHead,checkDifferenceListHeadRespDTO);
        // 当审批状态 ！= 审批中时，进行权限校验
        if (!CommonConstant.NUMBER_ONE.equals(checkDifferenceListHeadReqDTO.getWorkFlowRequestFlag()) &&
                !user.getEmployeeCode().equals(stockTakingPlan.getCreatedBy()) && !superAdminFlag(user.getEmployeeCode())) {
            return "没有操作权限";
        }

        return null;
    }


    /**
     * 获取差异清单的头信息
     *
     * @return
     */
    private CheckDifferenceListHeadRespDTO getDifferenceHeadRespDTO(String checkTakingNo) {
        CheckDifferenceListHead checkDifferenceListHead = new CheckDifferenceListHead();
        checkDifferenceListHead.setTakingPlanNo(checkTakingNo);
        CheckDifferenceListHeadExample example = getExample(checkDifferenceListHead);
        List<CheckDifferenceListHead> checkDifferenceListHeads = checkDifferenceListHeadMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(checkDifferenceListHeads)) {
            throw new ServiceUncheckedException("计划单" + checkTakingNo + "没有查到异常清单结果");
        }
        if (checkDifferenceListHeads.size() > 1) {
            throw new ServiceUncheckedException("计划单" + checkTakingNo + "查到多个异常清单结果");
        }
        CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO = new CheckDifferenceListHeadRespDTO();
        BeanUtils.copyProperties(checkDifferenceListHeads.get(0), checkDifferenceListHeadRespDTO);
        return checkDifferenceListHeadRespDTO;
    }

    /**
     * 获取差异清单的头信息
     *
     * @return
     */
    private List<CheckDifferenceListLineRespDTO> getDifferenceLineRespDTO(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO, CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO) {
        AssetQueryScopeReqDTO assetQueryScopeReqDTO = new AssetQueryScopeReqDTO();
        assetQueryScopeReqDTO.setHeadId(checkDifferenceListHeadReqDTO.getHeadId());
        assetQueryScopeReqDTO.setType(checkDifferenceListHeadReqDTO.getType());
        assetQueryScopeReqDTO.setPageNum(checkDifferenceListHeadReqDTO.getPageNum());
        assetQueryScopeReqDTO.setPageSize(checkDifferenceListHeadReqDTO.getPageSize());
        assetQueryScopeReqDTO.initPageParam();
        long count = checkDifferenceListLineService.getLineCount(assetQueryScopeReqDTO);
        if (checkDifferenceListHeadRespDTO != null) {
            checkDifferenceListHeadRespDTO.setDifferenceCount(count);
            assetQueryScopeReqDTO.setTakingPlanNo(checkDifferenceListHeadRespDTO.getTakingPlanNo());
        }
        if (count <= 0) {
            return new ArrayList<>();
        }
        if (checkDifferenceListHeadReqDTO.getNoPaging() != null) {
            assetQueryScopeReqDTO.setNoPaging(true);
        }
        //4.查询行表信息
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = checkDifferenceListLineService.getLineRespDTOs(assetQueryScopeReqDTO);
        return checkDifferenceListLineRespDTOList;
    }


    private CheckDifferenceListHeadExample getExample(CheckDifferenceListHead checkDifferenceListHead) {
        CheckDifferenceListHeadExample example = new CheckDifferenceListHeadExample();
        CheckDifferenceListHeadExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(checkDifferenceListHead.getTakingPlanNo())) {
            criteria.andTakingPlanNoEqualTo(checkDifferenceListHead.getTakingPlanNo());
        }
        if (null != checkDifferenceListHead.getHeadId()) {
            criteria.andHeadIdEqualTo(checkDifferenceListHead.getHeadId());
        }
        return example;
    }

    /**
     * 异常清单审批校验传入参数
     *
     * @param checkDifferenceListHeadReqDTO
     */
    private String checkApproveParam(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {
        if (null == checkDifferenceListHeadReqDTO.getType()) {
            return "差异数据类型不能为空";
        }

        //校验盘点结果单据唯一
        List<CheckDifferenceListHead> checkDifferenceListHeads = getHeadByPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        if (checkDifferenceListHeads.size() != 1) {
            return "计划单" + checkDifferenceListHeadReqDTO.getTakingPlanNo() + "查询到的单据异常";
        }

        CheckDifferenceListHead checkDifferenceListHead = checkDifferenceListHeads.get(0);
        //返回清单头id
        checkDifferenceListHeadReqDTO.setHeadId(checkDifferenceListHead.getHeadId());
        //1.审批数据不能为空
        AssetQueryScopeReqDTO assetQueryScopeReqDTO = new AssetQueryScopeReqDTO();
        assetQueryScopeReqDTO.setHeadId(checkDifferenceListHead.getHeadId());
        assetQueryScopeReqDTO.setType(checkDifferenceListHeadReqDTO.getType());
        long count = checkDifferenceListLineService.getLineCount(assetQueryScopeReqDTO);
        if (count <= CommonConstant.NUMBER_ZERO) {
            return "审批数据不能为空";
        }
        //2.当前相关调整类型的审批状态="待审批"
        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(checkDifferenceListHeadReqDTO.getType()) &&
                !StockCheckAdjustEnum.CheckApproveStatus.WAIT_APPROVE.getValue().equals(checkDifferenceListHead.getProfitLossApproveFlag())) {
            return "当前数据状已经提交审批";
        }
        if (StockCheckAdjustEnum.CheckAdjustType.ADJUST_DIFF.getValue().equals(checkDifferenceListHeadReqDTO.getType()) &&
                !StockCheckAdjustEnum.CheckApproveStatus.WAIT_APPROVE.getValue().equals(checkDifferenceListHead.getDiffApproveFlag())) {
            return "当前数据状已经提交审批";
        }
        //3.校验所有行是否还存在备注为空的情况
        assetQueryScopeReqDTO.setRemarkIsNull(1);
        if (checkDifferenceListLineService.getLineCount(assetQueryScopeReqDTO) > 0) {
            //更新未通过数据的校验状态="校验未通过"，以方便页面靠前展示
            CheckDifferenceListLine checkDifferenceListLine = new CheckDifferenceListLine();
            checkDifferenceListLine.setApproveCheckFlag(StockCheckAdjustEnum.ApproveCheckFlag.NO.getValue());
            checkDifferenceListLineService.batchUpdateByConditions(checkDifferenceListLine, assetQueryScopeReqDTO);
            return "当前盘点差异清单还存在没有添加备注的明细，请确认";
        }

        return null;
    }

    /**
     * 推送消息到流程平台
     *
     * @param type         盘点异常审批类型
     * @param takingPlanNo 盘点计划单号
     */
    private void sendTowfl(Integer type, String takingPlanNo) {
        FlowCodeEnum flowCodeEnum = StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(type) ? FlowCodeEnum.CHECK_MORE_OR_LESS : FlowCodeEnum.CHECK_DIFFERENCE;
        WflInfo wflInfo = WorkFlowUtil.createWlfInfo(flowCodeEnum, takingPlanNo, null);
        //异步发起工作流发
        wflService.beginAct(wflInfo);
    }

    /**
     * 根据条件查询差异清单头信息
     *
     * @param takingPlanNo
     * @return
     */
    private List<CheckDifferenceListHead> getHeadByPlanNo(String takingPlanNo) {
        CheckDifferenceListHeadExample example = new CheckDifferenceListHeadExample();
        CheckDifferenceListHeadExample.Criteria criteria = example.createCriteria();
        criteria.andTakingPlanNoEqualTo(takingPlanNo);
        List<CheckDifferenceListHead> checkDifferenceListHeads = checkDifferenceListHeadMapper.selectByExample(example);
        return checkDifferenceListHeads;
    }

    /**
     * 判断员工是否为盘点功能的超级管理员
     * @param empId
     * @return true 是超级管理员  false不是超级管理员
     */
    private Boolean superAdminFlag(String empId) {
        List<String> checkAdminList = ambaseCommonService.selectMetaDataByMdKey(MetaDataConstants.STOCK_MODULE_ASSETS,MetaDataConstants.STOCK_CHECK_ADMIN_MDKEY);
        if (CollectionUtils.isNotEmpty(checkAdminList) && checkAdminList.contains(empId)) {
            return true;
        }
        return false;
    }
}
