package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetail;
import com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsCheckTaskDetailMapper {
    long countByExample(StockAssetsCheckTaskDetailExample example);

    int deleteByPrimaryKey(Long taskDetailId);

    int insert(StockAssetsCheckTaskDetail record);

    int insertSelective(StockAssetsCheckTaskDetail record);

    List<StockAssetsCheckTaskDetail> selectByExample(StockAssetsCheckTaskDetailExample example);

    StockAssetsCheckTaskDetail selectByPrimaryKey(Long taskDetailId);

    int updateByExampleSelective(@Param("record") StockAssetsCheckTaskDetail record, @Param("example") StockAssetsCheckTaskDetailExample example);

    int updateByExample(@Param("record") StockAssetsCheckTaskDetail record, @Param("example") StockAssetsCheckTaskDetailExample example);

    int updateByPrimaryKeySelective(StockAssetsCheckTaskDetail record);

    int updateByPrimaryKey(StockAssetsCheckTaskDetail record);
}