package com.gz.eim.am.stock.service.repair;

import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairLineBaseReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsRepairLine;

import java.util.List;
import java.util.Map;

/**
   * @description: 资产维修行信息操作Service
   * @author: <EMAIL>
   * @date: 2022/12/15
   */
public interface StockAssetsRepairLineService {

    /**
     * @param: stockAssetsRepairLineBaseReqDTO
     * @description: 查询资产维修列表信息
     * @return: List<StockAssetsRepairLine>
     * @author: <EMAIL>
     * @date: 2022/12/15
     */
    List<StockAssetsRepairLine> selectList(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO);


    /**
     * @param: stockAssetsRepairLineBaseReqDTO
     * @description: 查询资产维修信息
     * @return: StockAssetsRepairLine
     * @author: <EMAIL>
     * @date: 2022/12/15
     */
    StockAssetsRepairLine selectOne(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO);

    /**
     * @param: stockAssetsRepairLineBaseReqDTO
     * @description: 查询资产维修列表信息
     * @return: Map<Long, StockAssetsRepairLine>
     * @author: <EMAIL>
     * @date: 2022/12/15
     */
    Map<Long, StockAssetsRepairLine> selectMapById(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO);

    /**
     * @param: stockAssetsRepairLineList
     * @description: 批量插入逻辑
     * @return: int
     * @author: <EMAIL>
     * @date: 2022/12/16
     */
    int batchInsert(List<StockAssetsRepairLine> stockAssetsRepairLineList);

    /**
     * @param: stockAssetsRepairLineList
     * @description: 批量更新逻辑
     * @return: int
     * @author: <EMAIL>
     * @date: 2022/12/16
     */
    int batchUpdate(List<StockAssetsRepairLine> stockAssetsRepairLineList);

}
