package com.gz.eim.am.stock.constant;

/**
 * @author: yangjifan1
 * @date: 2024/11/29
 * @description 调用呱呱获取人员设备信息请求字段
 */
public class CheckCallGuaGuaConstant {


    /**
     * 请求头
     **/
    //请求token
    public static final String REQUEST_HEADER_GUAGUA_TOKEN = "guagua-token";

    /**
     * 请求参数
     **/
    //设备状态值 0.无效 1.有效
    public static final String REQUEST_PARAM_DEVICE_USE_STATUS = "deviceUseStatus";

    //设备类型（PC、Mobile）
    public static final String REQUEST_PARAM_CLIENT_TYPE = "clientType";

    //人员集合
    public static final String REQUEST_PARAM_EMP_LIST = "empList";

    //是否存在sn 0.不存在 1.存在
    public static final String REQUEST_PARAM_IS_SN = "isSn";
}
