package com.gz.eim.am.stock.service.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.supplies.*;
import com.gz.eim.am.stock.dto.response.PagerRespDto;
import com.gz.eim.am.stock.dto.response.SuppliesRespDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesResponseDTO;
import com.gz.eim.am.stock.entity.StockSupplies;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 物料基础信息
 *
 * <AUTHOR>
 * @date 2019-09-24 上午 10:17
 */
public interface StockSuppliesService {


    /**
     * 根据物料编码获取物料
     *
     * @param code
     * @param status
     * @return
     */
    StockSupplies getSuppliesByCode(String code, Integer status);

    /**
     * 物料名称和物料编码的模糊查询
     *
     * @param param
     * @param limit
     * @param warehouseCode
     * @return
     * @throws ParseException
     */
    ResponseData vagueSupplies(String param, Integer limit, String warehouseCode, Integer docType, Integer type) throws ParseException;

    /**
     * 获取启用的物料
     *
     * @param code
     * @return
     */
    List<StockSupplies> getEnableSuppliesList(String code);

    /**
     * 新增物料信息
     *
     * @param suppliesDTO
     * @return
     */
    ResponseData saveSupplies(SuppliesDTO suppliesDTO);

    /**
     * 修改物料信息
     *
     * @param suppliesDTO
     * @return
     */
    ResponseData updateSupplies(SuppliesDTO suppliesDTO);

    /**
     * 搜索物料信息
     *
     * @param suppliesReqDTO
     * @return
     */
    ResponseData selectSupplies(SuppliesReqDTO suppliesReqDTO);

    /**
     * 搜索物料列表
     *
     * @param suppliesReqDTO
     * @return
     */
    List<StockSupplies> selectSuppliesList(SuppliesReqDTO suppliesReqDTO);

    /**
     * 根据ids or codes 查询物料
     *
     * @param ids
     * @param codes
     * @return
     */
    List<StockSupplies> selectSupplies(List<Long> ids, List<String> codes);

    /**
     * 根据ids或codes获取物料map
     *
     * @param ids
     * @param codes
     * @return
     */
    Map<String, StockSupplies> getSuppliesMapByCodes(List<Long> ids, List<String> codes);

    /**
     * 采购系统根据条件查询物料
     *
     * @param suppliesPurchaseSearchDto
     * @return
     */
    PagerRespDto<SuppliesResponseDTO> pageByPurchase(SuppliesPurchaseSearchReqDto suppliesPurchaseSearchDto);

    /**
     * 判断仓库是否有物料的权限
     *
     * @param warehouseCode
     * @param stockSupplies
     * @return
     */
    Boolean judgeAuthByWarehouseCode(String warehouseCode, StockSupplies stockSupplies);

    /**
     * 根据条件搜索物料采购属性
     *
     * @param suppliesPurchaseQueryReqDTO
     * @return
     */
    ResponseData searchStockSuppliesPurchase(SuppliesPurchaseQueryReqDTO suppliesPurchaseQueryReqDTO);

    /**
     * 保存物料相关属性
     *
     * @param suppliesPropertyRepDTO
     * @return
     */
    ResponseData saveSuppliesProperty(SuppliesPropertyRepDTO suppliesPropertyRepDTO);


    /**
     * 修改物料相关属性
     *
     * @param suppliesPropertyRepDTO
     * @return
     */
    ResponseData updateSuppliesProperty(SuppliesPropertyRepDTO suppliesPropertyRepDTO);


    /**
     * 根据条件查询物料属性信息
     *
     * @param suppliesPropertyRepDTO
     * @return
     */
    ResponseData querySuppliesPropertyDetail(SuppliesPropertyRepDTO suppliesPropertyRepDTO);


    /**
     * 查询物料详情信息
     *
     * @param suppliesPropertyRepDTO
     * @return
     */
    ResponseData querySuppliesDetail(SuppliesPropertyRepDTO suppliesPropertyRepDTO);

    /**
     * @param: stockAssetsDemandHead, user
     * @description: 查询suppliesMap
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/11
     */
    List<SuppliesRespDTO> getSuppliesListByDemand(List<String> suppliesCodeList, String warehouseCode);

    /**
     * @param: suppliesReqDTO
     * @description: 根据仓库编码和物料编码集合查询物料
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/13
     */
    ResponseData querySuppliesQuantity(SuppliesReqDTO suppliesReqDTO);

    List<StockSupplies> getSuppliesList(final SuppliesReqDTO suppliesReqDTO);

    /**
     * @param: categoryCode, warehouseCode
     * @description: 查询资产分类下对应的资产数量
     * @return: Map<String, SuppliesRespDTO>
     * @author: <EMAIL>
     * @date: 2023/3/13
     */
    Map<String, BigDecimal> getCategoryNumberMapByDemand(List<String> categoryCode, String warehouseCode);

    /**
     * @param: suppliesReqDO
     * @description: 查询资产分类下的资产数量
     * @return: Map<String, BigDecimal>
     * @author: <EMAIL>
     * @date: 2023/6/29
     */
    Map<String, BigDecimal> queryCategoryQuantity(SuppliesReqDO suppliesReqDO);


    /**
     * 根据codes 查询物料
     * @param codes
     * @return
     */
    List<SuppliesDTO> selectSuppliesList(List<String> codes);
}
