package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import com.fuu.eim.tool.excel.constant.ExportType;
import java.util.Date;

/**
 * @author: weijunjie
 * @date: 2021/1/7
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportAssetsExtendEntity implements ExportModel {
    @ExportField(name = "资产状态")
    private String statusText;
    @ExportField(name = "资产使用情况")
    private String conditionsName;
    @ExportField(name = "资产编码")
    private String assetsCode;
    @ExportField(name = "资产名称")
    private String assetsName;
    @ExportField(name = "采购码")
    private String suppliesCode;
    @ExportField(name = "资产分类")
    private String category;
    @ExportField(name = "设备序列号")
    private String snCode;
    @ExportField(name = "所属公司")
    private String companyName;
    @ExportField(name = "仓库代码")
    private String warehouseCode;
    @ExportField(name = "所在仓库")
    private String warehouseName;
    @ExportField(name = "当前使用人")
    private String holderName;
    @ExportField(name = "持有人位置")
    private String holderAddress;
    @ExportField(name = "使用人所在部门")
    private String holderDeptFullName;
    @ExportField(name = "使用人所在业务线")
    private String holderBussiness;
    @ExportField(name = "购置方式")
    private String purchaseTypeText;
    @ExportField(name = "需求部门")
    private String needDeptName;
    @ExportField(name = "费用部门")
    private String costDeptName;
    @ExportField(name = "购置日期", type = ExportType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private Date purchaseTime;
    @ExportField(name = "使用年限")
    private Integer useYearLimit;
    @ExportField(name = "入库日期", type = ExportType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private Date storageTime;
    @ExportField(name = "预计处置日期", type = ExportType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private Date planHandleTime;
    @ExportField(name = "资产管理员")
    private String assetsKeeperName;
    @ExportField(name = "备注")
    private String remark;
    @ExportField(name = "图片", type = ExportType.URL)
    private String assetsPic;

    @ExportField(name = "品牌")
    private String brandExtend;
    @ExportField(name = "型号")
    private String modelExtend;
    @ExportField(name = "颜色")
    private String colourExtend;
    @ExportField(name = "处理器")
    private String processorExtend;
    @ExportField(name = "内存容量")
    private String ramMemoryExtend;
    @ExportField(name = "硬盘类型")
    private String hddTypeExtend;
    @ExportField(name = "存储容量")
    private String hardDiskCapacityExtend;
    @ExportField(name = "操作系统")
    private String operateSystemExtend;
    @ExportField(name = "显卡")
    private String videoCardExtend;
    @ExportField(name = "屏幕尺寸")
    private String screenSizeExtend;
    @ExportField(name = "屏幕分辨率")
    private String screenResolutionExtend;
    @ExportField(name = "CPU型号")
    private String cpuModelExtend;



    @Override
    public String getSheetName() {
        return "资产卡片（带配置）信息";
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(final String statusText) {
        this.statusText = statusText;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(final String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(final String assetsName) {
        this.assetsName = assetsName;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(final String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(final String category) {
        this.category = category;
    }

    public String getSnCode() {
        return snCode;
    }

    public void setSnCode(final String snCode) {
        this.snCode = snCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(final String companyName) {
        this.companyName = companyName;
    }

    public String getWarehouseCode() { return warehouseCode;}

    public void setWarehouseCode(String warehouseCode) { this.warehouseCode = warehouseCode;}

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(final String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(final String holderName) {
        this.holderName = holderName;
    }

    public String getPurchaseTypeText() {
        return purchaseTypeText;
    }

    public void setPurchaseTypeText(final String purchaseTypeText) {
        this.purchaseTypeText = purchaseTypeText;
    }

    public String getNeedDeptName() {
        return needDeptName;
    }

    public void setNeedDeptName(final String needDeptName) {
        this.needDeptName = needDeptName;
    }

    public String getCostDeptName() {
        return costDeptName;
    }

    public void setCostDeptName(final String costDeptName) {
        this.costDeptName = costDeptName;
    }

    public Date getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(final Date purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    public Integer getUseYearLimit() {
        return useYearLimit;
    }

    public void setUseYearLimit(final Integer useYearLimit) {
        this.useYearLimit = useYearLimit;
    }

    public Date getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(final Date storageTime) {
        this.storageTime = storageTime;
    }

    public Date getPlanHandleTime() {
        return planHandleTime;
    }

    public void setPlanHandleTime(final Date planHandleTime) {
        this.planHandleTime = planHandleTime;
    }

    public String getAssetsKeeperName() {
        return assetsKeeperName;
    }

    public void setAssetsKeeperName(final String assetsKeeperName) {
        this.assetsKeeperName = assetsKeeperName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(final String remark) {
        this.remark = remark;
    }

    public String getAssetsPic() {
        return assetsPic;
    }

    public void setAssetsPic(final String assetsPic) {
        this.assetsPic = assetsPic;
    }

    public String getHolderAddress() {
        return holderAddress;
    }

    public void setHolderAddress(String holderAddress) {
        this.holderAddress = holderAddress;
    }

    public String getConditionsName() {
        return conditionsName;
    }

    public void setConditionsName(String conditionsName) {
        this.conditionsName = conditionsName;
    }

    public String getHolderDeptFullName() {
        return holderDeptFullName;
    }

    public void setHolderDeptFullName(String holderDeptFullName) {
        this.holderDeptFullName = holderDeptFullName;
    }

    public String getHolderBussiness() {
        return holderBussiness;
    }

    public void setHolderBussiness(String holderBussiness) {
        this.holderBussiness = holderBussiness;
    }

    public String getBrandExtend() {
        return brandExtend;
    }

    public void setBrandExtend(String brandExtend) {
        this.brandExtend = brandExtend;
    }

    public String getModelExtend() {
        return modelExtend;
    }

    public void setModelExtend(String modelExtend) {
        this.modelExtend = modelExtend;
    }

    public String getColourExtend() {
        return colourExtend;
    }

    public void setColourExtend(String colourExtend) {
        this.colourExtend = colourExtend;
    }

    public String getProcessorExtend() {
        return processorExtend;
    }

    public void setProcessorExtend(String processorExtend) {
        this.processorExtend = processorExtend;
    }

    public String getRamMemoryExtend() {
        return ramMemoryExtend;
    }

    public void setRamMemoryExtend(String ramMemoryExtend) {
        this.ramMemoryExtend = ramMemoryExtend;
    }

    public String getHddTypeExtend() {
        return hddTypeExtend;
    }

    public void setHddTypeExtend(String hddTypeExtend) {
        this.hddTypeExtend = hddTypeExtend;
    }

    public String getHardDiskCapacityExtend() {
        return hardDiskCapacityExtend;
    }

    public void setHardDiskCapacityExtend(String hardDiskCapacityExtend) {
        this.hardDiskCapacityExtend = hardDiskCapacityExtend;
    }

    public String getOperateSystemExtend() {
        return operateSystemExtend;
    }

    public void setOperateSystemExtend(String operateSystemExtend) {
        this.operateSystemExtend = operateSystemExtend;
    }

    public String getVideoCardExtend() {
        return videoCardExtend;
    }

    public void setVideoCardExtend(String videoCardExtend) {
        this.videoCardExtend = videoCardExtend;
    }

    public String getScreenSizeExtend() {
        return screenSizeExtend;
    }

    public void setScreenSizeExtend(String screenSizeExtend) {
        this.screenSizeExtend = screenSizeExtend;
    }

    public String getScreenResolutionExtend() {
        return screenResolutionExtend;
    }

    public void setScreenResolutionExtend(String screenResolutionExtend) {
        this.screenResolutionExtend = screenResolutionExtend;
    }

    public String getCpuModelExtend() {
        return cpuModelExtend;
    }

    public void setCpuModelExtend(String cpuModelExtend) {
        this.cpuModelExtend = cpuModelExtend;
    }
}
