package com.gz.eim.am.stock.interceptor.mybatis;

import com.gz.eim.plt.permission.plugin.interceptor.mb.MybatisRowSelectInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/11 2:43 下午
 */
@Component
public class StartSysListener implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private MybatisRowSelectInterceptor mybatisRowSelectInterceptor;

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            sqlSessionFactory.getConfiguration().addInterceptor(mybatisRowSelectInterceptor);
        }
    }
}
