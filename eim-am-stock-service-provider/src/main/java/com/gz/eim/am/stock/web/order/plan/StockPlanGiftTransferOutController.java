package com.gz.eim.am.stock.web.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.api.order.plan.StockPlanGiftTransferOutApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadFastReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import com.gz.eim.am.stock.service.order.plan.StockPlanGiftTransferOutService;
import com.gz.eim.am.stock.util.em.DeliveryPlanHeadEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: lishuyang
 * @date: 2019/12/13
 * @description: 新车礼品调拨
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/planGiftTransferOut")
public class StockPlanGiftTransferOutController implements StockPlanGiftTransferOutApi {

    @Autowired
    RedisUtil redisUtil;

    @Value("${namespace.name}")
    private  String nameSpace;

    @Autowired
    private StockPlanGiftTransferOutService stockPlanGiftTransferOutService;
    @Override
    public ResponseData savePlanGiftTransferOut(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info("/api/am/stock/planGiftTransferOut/save {}", deliveryPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.savePlanGiftTransferOut (deliveryPlanHeadReqDTO, user);
        } catch (Exception e){
            log.error("计划调拨单保存", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanGiftTransferOut(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO) {
        log.info("/api/am/stock/planGiftTransferOut/search {}", deliveryPlanSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            /*JwtUser user = new JwtUser ();
            user.setEmployeeCode ("10056112");*/
            JwtUser user = SecurityUtil.getJwtUser();
            List<Integer> outStockTypeList = Stream.of(DeliveryPlanHeadEnum.OutType.TRANSFER.getCode(),DeliveryPlanHeadEnum.OutType.CARD_TRANSFER.getCode()).collect(Collectors.toList());
            deliveryPlanSearchReqDTO.setOutStockTypeList(outStockTypeList);
            res = this.stockPlanGiftTransferOutService.selectPlanGiftTransferOut (deliveryPlanSearchReqDTO, user);
        } catch (Exception e){
            log.error("计划调拨单分页查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanGiftTransferOutById(Long deliveryPlanHeadId) {
        log.info("/api/am/stock/planGiftTransferOut/search/{}", deliveryPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.selectPlanGiftTransferOutById (deliveryPlanHeadId, user);
        } catch (Exception e){
            log.error("计划调拨单详情查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData planGiftTransferOutOutBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info("/api/am/stock/planGiftTransferOut/outBound {}", deliveryPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.planGiftTransferOutBound (deliveryPlanHeadReqDTO, user);
        }catch (RuntimeException e){
            log.info("计划调拨单出库 errorMessage={}", e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e){
            log.error("计划调拨单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData urgeSendMessage(Long deliveryPlanHeadId) {
        log.info("/api/am/stock/planGiftTransferOut/urge-send-message/{}", deliveryPlanHeadId);
        JwtUser user = SecurityUtil.getJwtUser();
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_CARD_OUT + deliveryPlanHeadId;
        try {
            Long nowTime = System.currentTimeMillis();
            String str = redisUtil.getByKey(nameSpace, lockKey);
            if (StringUtils.isBlank(str)) {
                res = this.stockPlanGiftTransferOutService.urgeSendMessage(deliveryPlanHeadId, user);
            } else {
                Long beforeTime = DateUtils.dateParse(str, DateUtils.HOUR_PATTERN).getTime();
                //和当前时间比较
                if ((nowTime - beforeTime) <= CommonConstant.NUMBER_300000) {
                    res = ResponseData.createFailResult("请5分钟之后再催办");
                } else {
                    res = this.stockPlanGiftTransferOutService.urgeSendMessage(deliveryPlanHeadId, user);
                }
            }
            //发送成功时，更新redis内容
            if (res.isSuccess()) {
                String dateStr = DateUtils.dateFormat(new Date(nowTime), DateUtils.HOUR_PATTERN);
                redisUtil.setValue(nameSpace, lockKey, dateStr);
            }
        } catch (Exception e) {
            log.error("调出催办发送呱呱消息失败", e);
            redisUtil.deleteByKey(nameSpace,lockKey);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData deliveryPlanListFastOutBound(DeliveryPlanHeadFastReqDTO deliveryPlanHeadFastReqDTO) {
        log.info("/api/am/stock/planGiftTransferOut/fast {}", deliveryPlanHeadFastReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.deliveryPlanListFastOutBound (deliveryPlanHeadFastReqDTO, user);
        }catch (RuntimeException e){
            log.error("计划调拨单出库 errorMessage = {}", e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e){
            log.error("计划调拨单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData insertPlanGiftTransferUse(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info("/api/am/stock/planGiftTransferOut/insert {}", deliveryPlanHeadReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.insertPlanGiftTransferUse(deliveryPlanHeadReqDTO, user);
        }catch (RuntimeException e){
            log.info("新建计划领用出库单错误 errorMessage={}", e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("新建计划领用出库单出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanGiftTransferUse(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO) {
        log.info("/api/am/stock/planGiftTransferOut/select {}", deliveryPlanSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.selectPlanGiftTransferUse(deliveryPlanSearchReqDTO, user);
        } catch (Exception e) {
            log.error("新建计划领用出库单出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanGiftTransferUseDetail(Long deliveryPlanHeadId) {
        log.info("/api/am/stock/planGiftTransferOut/select {}", deliveryPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.selectPlanGiftTransferUseDetail(deliveryPlanHeadId, user);
        } catch (Exception e) {
            log.error("新建计划领用出库单出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectDeliveryPlanByDeliveryPlanNo(String bizId) {
        log.info("/api/am/stock/planGiftTransferOut/select {}", bizId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.stockPlanGiftTransferOutService.selectDeliveryPlanByDeliveryPlanNo(bizId, user);
        } catch (Exception e) {
            log.error("新建计划领用出库单出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
