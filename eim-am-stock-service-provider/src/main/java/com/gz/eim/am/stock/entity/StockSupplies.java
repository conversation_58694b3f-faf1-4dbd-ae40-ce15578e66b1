package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockSupplies {
    private Long suppliesId;

    private Long parentId;

    private String code;

    private String suppliesConfigCode;

    private String name;

    private String catCode;

    private String catFullCode;

    private String unitCode;

    private String purchaseUnitCode;

    private String brandCode;

    private String modelCode;

    private String specs;

    private BigDecimal purchasePrice;

    private String suppliesSource;

    private Integer type;

    private Integer status;

    private String applyBy;

    private Integer delFlag;

    private String remark;

    private String searchLabel;

    private Integer manageType;

    private String produceFactory;

    private String serviceFactory;

    private String aftersaleFactory;

    private Integer minDeliveryNum;

    private String popCode;

    private String barCode;

    private Integer canSplit;

    private Integer firstFull;

    private Integer withPackage;

    private String image;

    private String materialQuality;

    private String color;

    private String suppliesLong;

    private String suppliesWidth;

    private String suppliesHigh;

    private String costItemCode;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getSuppliesId() {
        return suppliesId;
    }

    public void setSuppliesId(Long suppliesId) {
        this.suppliesId = suppliesId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getSuppliesConfigCode() {
        return suppliesConfigCode;
    }

    public void setSuppliesConfigCode(String suppliesConfigCode) {
        this.suppliesConfigCode = suppliesConfigCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getCatCode() {
        return catCode;
    }

    public void setCatCode(String catCode) {
        this.catCode = catCode == null ? null : catCode.trim();
    }

    public String getCatFullCode() {
        return catFullCode;
    }

    public void setCatFullCode(String catFullCode) {
        this.catFullCode = catFullCode == null ? null : catFullCode.trim();
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode == null ? null : unitCode.trim();
    }

    public String getPurchaseUnitCode() {
        return purchaseUnitCode;
    }

    public void setPurchaseUnitCode(String purchaseUnitCode) {
        this.purchaseUnitCode = purchaseUnitCode == null ? null : purchaseUnitCode.trim();
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode == null ? null : brandCode.trim();
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode == null ? null : modelCode.trim();
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs == null ? null : specs.trim();
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getSuppliesSource() {
        return suppliesSource;
    }

    public void setSuppliesSource(String suppliesSource) {
        this.suppliesSource = suppliesSource == null ? null : suppliesSource.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApplyBy() {
        return applyBy;
    }

    public void setApplyBy(String applyBy) {
        this.applyBy = applyBy == null ? null : applyBy.trim();
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getSearchLabel() {
        return searchLabel;
    }

    public void setSearchLabel(String searchLabel) {
        this.searchLabel = searchLabel == null ? null : searchLabel.trim();
    }

    public Integer getManageType() {
        return manageType;
    }

    public void setManageType(Integer manageType) {
        this.manageType = manageType;
    }

    public String getProduceFactory() {
        return produceFactory;
    }

    public void setProduceFactory(String produceFactory) {
        this.produceFactory = produceFactory == null ? null : produceFactory.trim();
    }

    public String getServiceFactory() {
        return serviceFactory;
    }

    public void setServiceFactory(String serviceFactory) {
        this.serviceFactory = serviceFactory == null ? null : serviceFactory.trim();
    }

    public String getAftersaleFactory() {
        return aftersaleFactory;
    }

    public void setAftersaleFactory(String aftersaleFactory) {
        this.aftersaleFactory = aftersaleFactory == null ? null : aftersaleFactory.trim();
    }

    public Integer getMinDeliveryNum() {
        return minDeliveryNum;
    }

    public void setMinDeliveryNum(Integer minDeliveryNum) {
        this.minDeliveryNum = minDeliveryNum;
    }

    public String getPopCode() {
        return popCode;
    }

    public void setPopCode(String popCode) {
        this.popCode = popCode == null ? null : popCode.trim();
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode == null ? null : barCode.trim();
    }

    public Integer getCanSplit() {
        return canSplit;
    }

    public void setCanSplit(Integer canSplit) {
        this.canSplit = canSplit;
    }

    public Integer getFirstFull() {
        return firstFull;
    }

    public void setFirstFull(Integer firstFull) {
        this.firstFull = firstFull;
    }

    public Integer getWithPackage() {
        return withPackage;
    }

    public void setWithPackage(Integer withPackage) {
        this.withPackage = withPackage;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image == null ? null : image.trim();
    }

    public String getMaterialQuality() {
        return materialQuality;
    }

    public void setMaterialQuality(String materialQuality) {
        this.materialQuality = materialQuality == null ? null : materialQuality.trim();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    public String getSuppliesLong() {
        return suppliesLong;
    }

    public void setSuppliesLong(String suppliesLong) {
        this.suppliesLong = suppliesLong == null ? null : suppliesLong.trim();
    }

    public String getSuppliesWidth() {
        return suppliesWidth;
    }

    public void setSuppliesWidth(String suppliesWidth) {
        this.suppliesWidth = suppliesWidth == null ? null : suppliesWidth.trim();
    }

    public String getSuppliesHigh() {
        return suppliesHigh;
    }

    public void setSuppliesHigh(String suppliesHigh) {
        this.suppliesHigh = suppliesHigh == null ? null : suppliesHigh.trim();
    }

    public String getCostItemCode() {
        return costItemCode;
    }

    public void setCostItemCode(String costItemCode) {
        this.costItemCode = costItemCode == null ? null : costItemCode.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}