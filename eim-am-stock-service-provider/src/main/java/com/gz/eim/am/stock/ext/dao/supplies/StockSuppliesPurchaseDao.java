package com.gz.eim.am.stock.ext.dao.supplies;

import com.gz.eim.am.stock.entity.StockSuppliesPurchase;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/7/13
 * <p>
 *   物料采购属性Dao
 * </p>
 */
public interface StockSuppliesPurchaseDao {
    /**
     * 根据物料编码集合和仓库类别获取物料采购属性集合
     * @param suppliesCodeList
     * @param warehouseType
     * @return
     */
    List<StockSuppliesPurchase> listBySuppliesCodeListAndWarehouseType(List<String> suppliesCodeList , Integer warehouseType);
}
