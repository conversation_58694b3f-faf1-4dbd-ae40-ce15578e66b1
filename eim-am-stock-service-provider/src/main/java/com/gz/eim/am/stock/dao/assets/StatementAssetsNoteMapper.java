package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.PurchaseStatementAssetsNote;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2021/4/6
 * @description
 */
public interface StatementAssetsNoteMapper {

    /**
     * 批量插入数据
     * @param purchaseStatementAssetsNoteList
     * @return
     */
    int batchInsert(List<PurchaseStatementAssetsNote> purchaseStatementAssetsNoteList);
}
