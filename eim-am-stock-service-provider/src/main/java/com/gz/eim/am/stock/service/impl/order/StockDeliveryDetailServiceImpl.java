package com.gz.eim.am.stock.service.impl.order;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockDeliveryDetailMapper;
import com.gz.eim.am.stock.dao.order.DeliveryDetailMapper;
import com.gz.eim.am.stock.dto.response.order.InventoryOutDetailRespDTO;
import com.gz.eim.am.stock.dto.response.wfl.WflFormLineRespDTO;
import com.gz.eim.am.stock.entity.StockDeliveryDetail;
import com.gz.eim.am.stock.entity.StockDeliveryDetailExample;
import com.gz.eim.am.stock.entity.vo.StockDeliveryDetailInfo;
import com.gz.eim.am.stock.service.order.StockDeliveryDetailService;
import com.gz.eim.am.stock.util.em.DeliveryDetailEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 8:45
 */

@Service
public class StockDeliveryDetailServiceImpl implements StockDeliveryDetailService {

    @Autowired
    private StockDeliveryDetailMapper stockDeliveryDetailMapper;
    @Autowired
    private DeliveryDetailMapper deliveryDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(StockDeliveryDetail detail) {
        return this.deliveryDetailMapper.insert(detail);
    }

    @Override
    public List<StockDeliveryDetail> selectByDeliveryId(Long deliveryId) {
        StockDeliveryDetailExample example = new StockDeliveryDetailExample();
        example.createCriteria().andDeliveryIdEqualTo(deliveryId);
        return this.stockDeliveryDetailMapper.selectByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchInsertStockDeliveryDetail(List<StockDeliveryDetail> stockDeliveryDetailList) {
        if(CollectionUtils.isEmpty (stockDeliveryDetailList)){
            return null;
        }

        return deliveryDetailMapper.batchInsertStockDeliveryDetail (stockDeliveryDetailList);
    }


    @Override
    public Boolean updateStockDeliveryDetailByDeliveryId(List<StockDeliveryDetail> stockDeliveryDetailList, Long deliveryId) {
        if(deliveryId == null){
            return false;
        }
        //删除出库单详细
        List<Long> deliveryIdList = new ArrayList<> ();
        deliveryIdList.add (deliveryId);
        deliveryDetailMapper.batchDeleteByIds (deliveryIdList);

        //插入出库单详细
        if(!CollectionUtils.isEmpty (stockDeliveryDetailList)){
            Integer insertCount = this.batchInsertStockDeliveryDetail (stockDeliveryDetailList);
            if(insertCount == null || insertCount < 1){
                return false;
            }
        }

        return true;
    }

    @Override
    public StockDeliveryDetail getStockDeliveryDetailByPrimaryKey(Long deliveryDetailId) {
        return stockDeliveryDetailMapper.selectByPrimaryKey (deliveryDetailId);
    }

    @Override
    public Boolean updateStorageStockDeliveryDetailList(List<StockDeliveryDetail> stockDeliveryDetailList) {
        if(CollectionUtils.isEmpty (stockDeliveryDetailList)){
            return false;
        }

        for(StockDeliveryDetail stockDeliveryDetail : stockDeliveryDetailList){
            stockDeliveryDetailMapper.updateByPrimaryKey (stockDeliveryDetail);
        }
        return true;
    }

    @Override
    public List<InventoryOutDetailRespDTO> selectInventoryOutDetailRespDTOByDeliveryId(Long deliveryId) {
        if(deliveryId == null){
            return  new ArrayList<> ();
        }

        return  deliveryDetailMapper.selectInventoryOutDetailRespDTOByDeliveryId (deliveryId);
    }

    @Override
    public List<WflFormLineRespDTO> selectWflFormLineListByDeliveryNo(String deliveryNo) {
        if(StringUtils.isBlank(deliveryNo)){
            return new ArrayList<> ();
        }

        return deliveryDetailMapper.selectWflFormLineListByDeliveryNo (deliveryNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertStockDeliveryDetailInfo(List<StockDeliveryDetailInfo> stockDeliveryDetailInfoList) {
        if(CollectionUtils.isEmpty (stockDeliveryDetailInfoList)){
            return;
        }


        List<StockDeliveryDetailInfo> newStockDeliveryList = new ArrayList<>();
        if (stockDeliveryDetailInfoList.size() > 0) {
            int toIndex = CommonConstant.MAX_INSERT_COUNT;
            for (int i = 0; i < stockDeliveryDetailInfoList.size(); i += CommonConstant.MAX_INSERT_COUNT) {
                if (i + CommonConstant.MAX_INSERT_COUNT > stockDeliveryDetailInfoList.size()) {
                    toIndex = stockDeliveryDetailInfoList.size() - i;
                }
                List<StockDeliveryDetailInfo> newSubDetailList = stockDeliveryDetailInfoList.subList(i, i + toIndex);
                deliveryDetailMapper.batchInsertStockDeliveryDetailInfo (newSubDetailList);
                newStockDeliveryList.addAll(newSubDetailList);
            }
        }

        stockDeliveryDetailInfoList = new ArrayList<>();
        stockDeliveryDetailInfoList.addAll(newStockDeliveryList);


    }

    @Override
    public List<StockDeliveryDetail> selectNotAlreadyOutLine(Long deliveryId) {
        if(null == deliveryId){
            return new ArrayList<> ();
        }
        StockDeliveryDetailExample example = new StockDeliveryDetailExample ();
        StockDeliveryDetailExample.Criteria criteria = example.createCriteria ();
        criteria.andStatusNotEqualTo (DeliveryDetailEnum.Status.ALREADY_OUT.getCode ());
        criteria.andDeliveryIdEqualTo (deliveryId);
        return stockDeliveryDetailMapper.selectByExample (example);
    }
}
