package com.gz.eim.am.stock.constant;

/**
 * <AUTHOR>
 * @describe 流程配置相关
 * @date 2020-05-26 15:43
 */
public class WorkflowConstants {

    /**
     * lobNo
     **/
    public static final String WFL_LOB_NO = "lobNo";
    /**
     * bizId
     **/
    public static final String WFL_BIZ_ID = "bizId";
    /**
     * SystemId
     **/
    public static final String WFL_SYSTEM_ID = "SystemId";
    /**
     * CheckCode
     **/
    public static final String WFL_CHECK_CODE = "CheckCode";
    /**
     * Module
     **/
    public static final String WFL_MODULE = "Module";
    /**
     * Code
     **/
    public static final String WFL_CODE = "Code";
    /**
     * formId
     **/
    public static final String WFL_FORM_ID = "formId";
    /**
     * fromSystemId
     **/
    public static final String WFL_FROM_SYSTEM_ID = "fromSystemId";
    /**
     * applayUser
     **/
    public static final String WFL_APPLY_USER_ERR = "applayUser";
    /**
     * applyUser
     **/
    public static final String WFL_APPLY_USER = "applyUser";
    /**
     * flowCode
     **/
    public static final String WFL_FLOW_CODE = "flowCode";
    /**
     * dataItem
     **/
    public static final String WFL_DATA_ITEM = "dataItem";
    /**
     * referenceFormId
     **/
    public static final String WFL_REFERENCE_FORM_ID = "referenceFormId";
    /**
     * fileList
     **/
    public static final String WFL_FILE_LIST = "fileList";
    /**
     * RequestParam
     **/
    public static final String WFL_REQUEST_PARAM = "RequestParam";
    /**
     * wflDTO
     **/
    public static final String WFL_WFLDTO = "wflDTO";
    /**
     * flowNo
     **/
    public static final String WFL_FLOW_NO = "flowNo";
    /**
     * bizLineCode
     **/
    public static final String WFL_BIZ_LINE = "bizLineCode";

    /**
     * 审批通过
     */
    public static final int APPROVAL_SUCCESS = 1;

    /**
     * 审批拒绝
     */
    public static final int APPROVAL_REJECTION = 0;

    /**
     * approveStatus
     **/
    public static final String APPROVE_STATUS = "approveStatus";

    /**
     * 调用流程平台系统模块
     **/
    public static final String PH_FIXED_ASSET = "PHFixedAsset";

    /**
     * 自助领用-管理员工号集合
     **/
    public static final String WAREHOUSE_LINKMAN_STRING = "wareHouseLinkManString";

    /**
     * 实际使用人
     **/
    public static final String REAL_USE_USER = "realUseUser";

    /**
     * 申请人
     **/
    public static final String APPLY_USER = "applyUser";

    /**
     * 计划出库单
     **/
    public static final String DELIVERY_PLAN_NO = "deliveryPlanNo";

    /**
     * 领用方式
     **/
    public static final String DELIVERY_METHOD = "deliveryMethod";

    /**
     * 快递类型编码
     **/
    public static final String DELIVERY_TYPE_CODE = "deliveryTypeCode";
    /**
     * 资产分类编码集合
     **/
    public static final String CATEGORY_CODE_LIST = "categoryCodeList";
    /**
     * 盘点人
     **/
    public static final String CHECK_PERSON = "checkPerson";
    /**
     * 下载链接
     **/
    public static final String DOWNLOAD_URL = "downloadUrl";
    /**
     * 未盘人总数
     **/
    public static final String NOT_CHECK_PEOPLE_TOTAL = "notCheckPeopleTotal";
    /**
     * 未盘人比例
     **/
    public static final String NOT_CHECK_PEOPLE_PERCENT = "notCheckPeoplePercent";
    /**
     * 盘点计划名称
     **/
    public static final String TAKING_PLAN_NAME = "takingPlanName";
    /**
     * 提醒领导盘点详情url
     **/
    public static final String REMIND_LEADER_CHECK_DETAIL_URL = "remindLeaderCheckDetailUrl";
    /**
     * 盘点计划单号
     **/
    public static final String TAKING_PLAN_NO = "takingPlanNo";
    /**
     * 盘点结束时间
     **/
    public static final String TAKING_PLAN_END_DATE = "takingPlanEndDate";
    /**
     * 昨天的日期
     **/
    public static final String YESTERDAY_DATE = "yesterdayDate";

}
