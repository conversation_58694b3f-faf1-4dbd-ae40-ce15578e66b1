package com.gz.eim.am.stock.dao.check;

import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.entity.StockTakingSnapshot;
import org.apache.ibatis.annotations.Param;


import java.util.List;

public interface TakingSnapshotMapper {
    /**
     * 批量新增资产快照信息
     * @param stockTakingSnapshotList
     * @throws Exception
     */
     int batchInsert(List<StockTakingSnapshot> stockTakingSnapshotList) throws Exception;



    /**
     * 批量更新资产快照信息
     * @param stockTakingSnapshotList
     * @throws Exception
     */
    int batchUpdate(List<StockTakingSnapshot> stockTakingSnapshotList) throws Exception;


    /**
     * 批量更新快照信息确认
     * @param takingPlanNo 计划单号
     * @param employeeCode
     * @return
     * @throws Exception
     */
    int batchUpdateIsConfirm(@Param("takingPlanNo")String takingPlanNo, @Param("employeeCode") String employeeCode)throws Exception;

    /**
     * 根据条件查询总数量
     * @param assetQueryScopeReqDTO
     * @return
     */
    long queryCountByScopeReqDTO(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     *资产盘点查询范围(在库数量)
     * @param assetQueryScopeReqDTO
     * @return
     */
    long queryCheckPlanCountIdle(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     *资产盘点查询范围(使用中数量)
     * @param assetQueryScopeReqDTO
     * @return
     */
    long queryCheckPlanCountUsed(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 删除计划下的快照信息
     * @param takingPlanNo
     * @return
     */
    long deleteSnapshotByPlanNo(@Param("takingPlanNo") String takingPlanNo);
}
