package com.gz.eim.am.stock.web.assets;

import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.api.assets.StockAssetsBatchPrintApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.assets.*;
import com.gz.eim.am.stock.service.assets.StockAssetsBatchPrintService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-12-11 PM 5:09
 */
@RestController
@RequestMapping("/api/am/stock/assets/batchPrint")
@Slf4j
public class StockAssetsBatchPrintController implements StockAssetsBatchPrintApi {

    @Autowired
    private StockAssetsBatchPrintService stockAssetsBatchPrintService;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${namespace.name}")
    private String nameSpace;

     /**
       * @param:
       * @description: 上传批量打印资产编码
       * @return:
       * @author: <EMAIL>
       * @date: 2021/12/22
       */
    @Override
    public ResponseData upLoadBatchPrintAssetsCode(MultipartFile file) {
        log.info("/api/am/stock/assets/batchPrint/upLoadBatchPrintAssetsCode");
        JwtUser user = SecurityUtil.getJwtUser();
        if(null == user){
            return ResponseData.createFailResult(ResponseCode.NOT_LOGIN.getMessage(), ResponseCode.NOT_LOGIN.getCode());
        }
        log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
        // 获取redisKey
        String redisKey = RedisKeyConstants.BATCH_ASSETS_PRINT_UPLOAD + user.getEmployeeCode();
        ResponseData responseData = null;
        try {
            if (redisUtil.setNx (nameSpace, redisKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                // 此处延迟10s秒释放掉锁，防止10秒内有大量请求，保持幂等性
                redisUtil.expire (nameSpace, redisKey, CommonConstant.BATCH_ASSETS_PRINT_LOCK_SECOND, TimeUnit.SECONDS);
                responseData = stockAssetsBatchPrintService.upLoadBatchPrintAssetsCode(file, user);
            } else {
                responseData = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (Exception e) {
            // 直接删除相关key
            redisUtil.deleteByKey(nameSpace, redisKey);
            log.error("上传批量打印资产编码异常",e);
            responseData = ResponseData.createFailResult(e.getMessage());
        }
        return responseData;
    }

     /**
       * @param:
       * @description: 查询批量打印资产编码
       * @return:
       * @author: <EMAIL>
       * @date: 2021/12/22
       */
    @Override
    public ResponseData selectBatchPrintAssetsCode(StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO) {
        log.info("/api/am/stock/assets/batchPrint/selectBatchPrintAssetsCode,batchNo={}", JSONObject.toJSONString(stockAssetsPrintImportReqDTO));
        ResponseData responseData = null;
        try {
            responseData = stockAssetsBatchPrintService.selectBatchPrintAssetsCode(stockAssetsPrintImportReqDTO);
        } catch (Exception e) {
            log.error("查询批量打印资产编码异常",e);
            responseData = ResponseData.createFailResult(e.getMessage());
        }
        return responseData;
    }

     /**
       * @param:
       * @description: 开始批量打印资产编码
       * @return:
       * @author: <EMAIL>
       * @date: 2021/12/22
       */
    @Override
    public ResponseData submitBatchPrintAssetsCode(List<StockAssetsPrintImportReqDTO> stockAssetsPrintImportReqDTOList, String printKey) {
        log.info("/api/am/stock/assets/batchPrint/submitBatchPrintAssetsCode,stockAssetsPrintImportReqDTOList={},printKey={}", JSONObject.toJSONString(stockAssetsPrintImportReqDTOList), printKey);
        ResponseData responseData = null;
        try {
            responseData = stockAssetsBatchPrintService.submitBatchPrintAssetsCode(stockAssetsPrintImportReqDTOList, printKey);
        } catch (Exception e) {
            log.error("开始批量打印资产编码异常",e);
            responseData = ResponseData.createFailResult(e.getMessage());
        }
        return responseData;
    }
}
