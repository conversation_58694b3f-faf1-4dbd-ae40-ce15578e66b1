package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockDeliveryHistory;
import com.gz.eim.am.stock.entity.StockDeliveryHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockDeliveryHistoryMapper {
    long countByExample(StockDeliveryHistoryExample example);

    int deleteByPrimaryKey(Long deliveryId);

    int insert(StockDeliveryHistory record);

    int insertSelective(StockDeliveryHistory record);

    List<StockDeliveryHistory> selectByExample(StockDeliveryHistoryExample example);

    StockDeliveryHistory selectByPrimaryKey(Long deliveryId);

    int updateByExampleSelective(@Param("record") StockDeliveryHistory record, @Param("example") StockDeliveryHistoryExample example);

    int updateByExample(@Param("record") StockDeliveryHistory record, @Param("example") StockDeliveryHistoryExample example);

    int updateByPrimaryKeySelective(StockDeliveryHistory record);

    int updateByPrimaryKey(StockDeliveryHistory record);
}