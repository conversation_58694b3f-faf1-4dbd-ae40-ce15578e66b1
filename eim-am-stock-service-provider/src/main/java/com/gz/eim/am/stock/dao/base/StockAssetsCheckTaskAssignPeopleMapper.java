package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsCheckTaskAssignPeople;
import com.gz.eim.am.stock.entity.StockAssetsCheckTaskAssignPeopleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsCheckTaskAssignPeopleMapper {
    long countByExample(StockAssetsCheckTaskAssignPeopleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsCheckTaskAssignPeople record);

    int insertSelective(StockAssetsCheckTaskAssignPeople record);

    List<StockAssetsCheckTaskAssignPeople> selectByExample(StockAssetsCheckTaskAssignPeopleExample example);

    StockAssetsCheckTaskAssignPeople selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsCheckTaskAssignPeople record, @Param("example") StockAssetsCheckTaskAssignPeopleExample example);

    int updateByExample(@Param("record") StockAssetsCheckTaskAssignPeople record, @Param("example") StockAssetsCheckTaskAssignPeopleExample example);

    int updateByPrimaryKeySelective(StockAssetsCheckTaskAssignPeople record);

    int updateByPrimaryKey(StockAssetsCheckTaskAssignPeople record);
}