package com.gz.eim.am.stock.web.inventory.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.api.inventory.plan.StockInventoryInPlanApi;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import com.gz.eim.am.stock.service.inventory.plan.StockInventoryInPlanHeadService;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author: lishuyang
 * @date: 2019/12/11
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock")
public class StockInventoryInPlanController implements StockInventoryInPlanApi {
    @Autowired
    private StockInventoryInPlanHeadService stockInventoryInPlanHeadService;

    @Override
    public ResponseData selectInventoryInPlan(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO) {
        log.info("/api/am/stock/inventoryInPlan {}", inventoryInPlanSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockInventoryInPlanHeadService.selectInventoryInPlan (inventoryInPlanSearchReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划入库单分页查询出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectInventoryInPlanById(Long inventoryInPlanHeadId){
        log.info("/api/am/stock/inventoryInPlan/{}", inventoryInPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO = new InventoryInPlanLineReqDTO();
            inventoryInPlanLineReqDTO.setInventoryInPlanHeadId(inventoryInPlanHeadId);
            res = this.stockInventoryInPlanHeadService.selectInventoryInPlanById (inventoryInPlanLineReqDTO, user, true);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划出库单详情查询出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData inventoryInPlanStorage(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        return null;
    }

    @Override
    public ResponseData assetPurchaseFileImport(MultipartFile file) {
        log.info("/api/am/stock/inventoryInPlan/file");
        ResponseData res = null;
        try {
            /*JwtUser user = new JwtUser ();
            user.setEmployeeCode ("10056112");*/
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            //res = stockInventoryInPlanHeadService.assetPurchaseFileImport (file, user);
            return null;
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("礼品批量调拨导入失败", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
