package com.gz.eim.am.stock.service.discount;

import com.gz.eim.am.stock.entity.StockLicenseChange;
import com.gz.eim.am.stock.entity.StockLicenseChangeItems;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 4/6/21 10:42 上午
 * @description 执照变更申请行相关接口
 */
public interface StockLicenseChangeItemsService {

    /**
     * 根据变更申请单据号查询
     * @param changeNo
     * @return
     */
    List<StockLicenseChangeItems> selectByChangeNo(String changeNo);
}
