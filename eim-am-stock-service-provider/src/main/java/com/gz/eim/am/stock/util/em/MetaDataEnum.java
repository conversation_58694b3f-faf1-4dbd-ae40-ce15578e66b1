package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/2/13
 * @description 字典相关枚举
 */
public class MetaDataEnum {
    /**
     * 传入类型字典，是否为印章
     */
    public enum type{
        //类型为印章
        LICENSE(1,"类型为执照"),
        /**
         * 财务印章
         */
        FINANCE_SEAL(11, "财务印章"),
        SEAL(2, "类型为印章"),
        /**
         * 银行开户许可证
         */
        FINANCE_LICENCE(22, "银行开户许可证");
        type(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }
    public static Map<Integer, String> typeMap =
            Arrays.stream(com.gz.eim.am.stock.util.em.MetaDataEnum.type.values()).collect(
                    Collectors.toMap(MetaDataEnum.type::getValue, MetaDataEnum.type::getDesc));

    /**
     * 通用字典：是否
     */
    public enum yesOrNo{
        //否
        NO(0, "否"),
        //是
        YES(1, "是")
        ;

        yesOrNo(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

        public final static Map<Integer, String> YES_OR_NO_MAP =
                Arrays.stream(com.gz.eim.am.stock.util.em.MetaDataEnum.yesOrNo.values()).collect(
                        Collectors.toMap(com.gz.eim.am.stock.util.em.MetaDataEnum.yesOrNo::getValue, com.gz.eim.am.stock.util.em.MetaDataEnum.yesOrNo::getDesc));

        public final static Map<String,Integer> YES_OR_NO_NAME_MAP =
                Arrays.stream(com.gz.eim.am.stock.util.em.MetaDataEnum.yesOrNo.values()).collect(
                        Collectors.toMap(com.gz.eim.am.stock.util.em.MetaDataEnum.yesOrNo::getDesc, com.gz.eim.am.stock.util.em.MetaDataEnum.yesOrNo::getValue));

    }


    /**
     * 通用字典：是否是默认
     */
    public enum isDefault{
        //是
        NO(0, "否"),
        //否
        YES(1, "是")
        ;

        isDefault(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    public static Map<Integer, String> isDefaultMap =
            Arrays.stream(com.gz.eim.am.stock.util.em.MetaDataEnum.isDefault.values()).collect(
                    Collectors.toMap(com.gz.eim.am.stock.util.em.MetaDataEnum.isDefault::getValue, com.gz.eim.am.stock.util.em.MetaDataEnum.isDefault::getDesc));

    /**
     * 通用字典：status
     */
    public enum status{
        //无效
        NO(0, "作废"),
        //有效
        YES(1, "有效")
        ;

        status(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

        public static status getEnum(Integer value) {
            for (status subTypeEnum : status.values()) {
                if (subTypeEnum.value.equals(value)) {
                    return subTypeEnum;
                }
            }
            return null;
        }
    }

    public static Map<Integer, String> statusMap =
            Arrays.stream(com.gz.eim.am.stock.util.em.MetaDataEnum.status.values()).collect(
                    Collectors.toMap(com.gz.eim.am.stock.util.em.MetaDataEnum.status::getValue, com.gz.eim.am.stock.util.em.MetaDataEnum.status::getDesc));


}
