package com.gz.eim.am.stock.service.warehouse;

import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.warehouse.RoleKeeperReq;
import com.gz.eim.am.stock.dto.response.RoleKeeperResp;
import com.gz.eim.am.stock.entity.StockRoleKeeper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 9:28
 */
public interface StockRoleKeeperService {

    /**
     * 插入单条
     * @param keeper
     * @return
     */
    int insert(StockRoleKeeper keeper);

    /**
     * 查询仓管员管理的所有仓库
     * @param empId
     * @return
     */
    List<String> selectKeeperManageWarehouse(String empId);

    /**
     * 仓库编号查询仓库管理员
     * @param warehouseCode
     * @return
     */
    List<RoleKeeperResp> selectWarehouseKeeper(String warehouseCode);

    /**
     * 修改仓库管理员
     * @param warehouseCode
     * @param keepers
     * @param user
     * @return
     */
    int modifyWarehouseKeeper(String warehouseCode, List<RoleKeeperReq> keepers, JwtUser user);

    /**
     * 根据角色和人员查询人员管理的仓库
     * @param empId
     * @param roleType
     * @param warehouseCode
     * @return
     */
    List<String> selectKeepWarehouseByParam(String empId, Integer roleType, String warehouseCode);

    /**
     * 是否存在ALL权限
     * @param empId
     * @return true of false
     */
    Boolean isAll(String empId);

    /**
     * 根据单据类型查询仓库
     * @param docType
     * @return
     */
    List<String> selectKeepWarehouseByDocType(Integer docType);

}
