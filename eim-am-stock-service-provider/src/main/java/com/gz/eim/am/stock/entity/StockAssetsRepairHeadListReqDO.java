package com.gz.eim.am.stock.entity;

import com.gz.eim.am.stock.dto.request.PageReqDTO;
import lombok.Data;

/**
 * @className: StockAssetsRepairHeadListReqDO
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/12/21
 **/
@Data
public class StockAssetsRepairHeadListReqDO extends PageReqDTO {

    private String repairNo;

    private String billingUser;

    private Integer status;

    private String billingStartDate;

    private String billingEndDate;

    private Integer repairType;

    private String supplierCode;

    private String assetsCode;

}
