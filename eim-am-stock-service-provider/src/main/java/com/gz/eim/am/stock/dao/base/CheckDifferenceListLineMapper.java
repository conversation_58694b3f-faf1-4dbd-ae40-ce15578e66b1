package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.CheckDifferenceListLine;
import com.gz.eim.am.stock.entity.CheckDifferenceListLineExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CheckDifferenceListLineMapper {
    long countByExample(CheckDifferenceListLineExample example);

    int deleteByPrimaryKey(Long lineId);

    int insert(CheckDifferenceListLine record);

    int insertSelective(CheckDifferenceListLine record);

    List<CheckDifferenceListLine> selectByExample(CheckDifferenceListLineExample example);

    CheckDifferenceListLine selectByPrimaryKey(Long lineId);

    int updateByExampleSelective(@Param("record") CheckDifferenceListLine record, @Param("example") CheckDifferenceListLineExample example);

    int updateByExample(@Param("record") CheckDifferenceListLine record, @Param("example") CheckDifferenceListLineExample example);

    int updateByPrimaryKeySelective(CheckDifferenceListLine record);

    int updateByPrimaryKey(CheckDifferenceListLine record);
}