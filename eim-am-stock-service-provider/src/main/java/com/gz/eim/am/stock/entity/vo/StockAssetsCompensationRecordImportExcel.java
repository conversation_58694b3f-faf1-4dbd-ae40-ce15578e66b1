package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;

/**
 * @describe 资产赔偿记录导入
 * <AUTHOR>
 * @date 2022-04-01
 **/
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockAssetsCompensationRecordImportExcel implements ExportModel {

    @Override
    public String getSheetName() {
        return null;
    }
    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    @ExportField(name = "行政审核日期")
    private String auditTime;

    @ExportField(name = "行政审批人")
    private String auditUser;

    @ExportField(name = "员工工号")
    private String compensationUser;

    @ExportField(name = "员工姓名")
    private String userName;

    @ExportField(name = "资产编码")
    private String assetsCode;

    @ExportField(name = "损坏/丢失原因")
    private String compensationReasonName;

    @ExportField(name = "赔偿金额（元）")
    private String compensationMoney;

    @ExportField(name = "赔偿方式")
    private String compensationMethodName;

    @ExportField(name = "其他说明")
    private String remark;

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public String getCompensationUser() {
        return compensationUser;
    }

    public void setCompensationUser(String compensationUser) {
        this.compensationUser = compensationUser;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getCompensationReasonName() {
        return compensationReasonName;
    }

    public void setCompensationReasonName(String compensationReasonName) {
        this.compensationReasonName = compensationReasonName;
    }

    public String getCompensationMoney() {
        return compensationMoney;
    }

    public void setCompensationMoney(String compensationMoney) {
        this.compensationMoney = compensationMoney;
    }

    public String getCompensationMethodName() {
        return compensationMethodName;
    }

    public void setCompensationMethodName(String compensationMethodName) {
        this.compensationMethodName = compensationMethodName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }



}