package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import lombok.Data;

/**
 * @className: QueryStockAssetsDemandAssetsListReqDO
 * @description: 资产自助领用资产信息查询返回结果
 * @author: <EMAIL>
 * @date: 2023/8/15
 **/
@Data
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockAssetsDemandAssetsListExportExcel implements ExportModel {
    /**
     * 认领人工号
     */
    @ExportField(name="认领人工号")
    private String claimUser;
    /**
     * 认领人姓名
     */
    @ExportField(name="认领人姓名")
    private String claimUserName;
    /**
     * 使用人部门名称
     */
    @ExportField(name="使用人部门名称")
    private String deptFullName;
    /**
     * 运营审批人工号
     */
    @ExportField(name="运营审批人工号")
    private String operationApproveUser;
    /**
     * 运营审批人姓名
     */
    @ExportField(name="运营审批人姓名")
    private String operationApproveUserName;
    /**
     * 领用日期
     */
    @ExportField(name="领用日期")
    private String holderTime;
    /**
     * 领用人所在省名称
     */
    @ExportField(name="领用人所在省名称")
    private String receiveUserProvinceName;
    /**
     * 领用人所在城市名称
     */
    @ExportField(name="领用人所在城市名称")
    private String receiveUserCityName;
    /**
     * 领用人所在区名称
     */
    @ExportField(name="领用人所在区名称")
    private String receiveUserRegionName;
    /**
     * 资产编码
     */
    @ExportField(name="资产编码")
    private String assetsCode;
    /**
     * 资产名称
     */
    @ExportField(name="资产名称")
    private String assetsName;
    /**
     * SN编码
     */
    @ExportField(name="SN编码")
    private String snCode;
    /**
     * 使用人工号
     */
    @ExportField(name="使用人工号")
    private String realUseUser;
    /**
     * 使用人姓名
     */
    @ExportField(name="使用人姓名")
    private String realUseUserName;
    /**
     * 收货人工号
     */
    @ExportField(name="收货人工号")
    private String receiveUser;
    /**
     * 收货人姓名
     */
    @ExportField(name="收货人姓名")
    private String receiveUserName;
    /**
     * 收货人电话
     */
    @ExportField(name="收货人电话")
    private String receiveUserPhone;
    /**
     * 收货地址
     */
    @ExportField(name="收货地址")
    private String receiveUserAddress;
    /**
     * 中通快递单号
     */
    @ExportField(name="中通快递单号")
    private String trackingNumber;

    @Override
    public String getSheetName() {
        return "资产自助领用报表";
    }
}
