package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockAssetsCategory {
    private Long id;

    private String parentCategoryCode;

    private String parentCategoryName;

    private String categoryCode;

    private String categoryName;

    private Integer defaultUsedTime;

    private BigDecimal defaultUsedCost;

    private Integer status;

    private Integer syncEbsFlag;

    private BigDecimal lowestAmount;

    private Integer delFlag;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private String pictureUrl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParentCategoryCode() {
        return parentCategoryCode;
    }

    public void setParentCategoryCode(String parentCategoryCode) {
        this.parentCategoryCode = parentCategoryCode == null ? null : parentCategoryCode.trim();
    }

    public String getParentCategoryName() {
        return parentCategoryName;
    }

    public void setParentCategoryName(String parentCategoryName) {
        this.parentCategoryName = parentCategoryName == null ? null : parentCategoryName.trim();
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode == null ? null : categoryCode.trim();
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName == null ? null : categoryName.trim();
    }

    public Integer getDefaultUsedTime() {
        return defaultUsedTime;
    }

    public void setDefaultUsedTime(Integer defaultUsedTime) {
        this.defaultUsedTime = defaultUsedTime;
    }

    public BigDecimal getDefaultUsedCost() {
        return defaultUsedCost;
    }

    public void setDefaultUsedCost(BigDecimal defaultUsedCost) {
        this.defaultUsedCost = defaultUsedCost;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSyncEbsFlag() {
        return syncEbsFlag;
    }

    public void setSyncEbsFlag(Integer syncEbsFlag) {
        this.syncEbsFlag = syncEbsFlag;
    }

    public BigDecimal getLowestAmount() {
        return lowestAmount;
    }

    public void setLowestAmount(BigDecimal lowestAmount) {
        this.lowestAmount = lowestAmount;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl == null ? null : pictureUrl.trim();
    }
}