package com.gz.eim.am.stock.service.impl.warehouse;


import com.gz.eim.am.stock.dao.base.StockWarehouseCategoryMapper;
import com.gz.eim.am.stock.entity.StockWarehouseCategory;
import com.gz.eim.am.stock.entity.StockWarehouseCategoryExample;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseCategoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 11:46
 */
public class StockWarehouseCategoryServiceImpl implements StockWarehouseCategoryService {


    @Autowired
    private StockWarehouseCategoryMapper mapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(StockWarehouseCategory warehouseBusiness) {
        if(null == warehouseBusiness){
            return 0;
        }
        return this.mapper.insert(warehouseBusiness);
    }

    @Override
    public List<StockWarehouseCategory> queryByWarehouseCode(String warehouseCode) {
        if(StringUtils.isBlank(warehouseCode)){
            return null;
        }
        StockWarehouseCategoryExample example = new StockWarehouseCategoryExample();
        example.createCriteria().andWarehouseCodeEqualTo(warehouseCode);
        return this.mapper.selectByExample(example);
    }
}
