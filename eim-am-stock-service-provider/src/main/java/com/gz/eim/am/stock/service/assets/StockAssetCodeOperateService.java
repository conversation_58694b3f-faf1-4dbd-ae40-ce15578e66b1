package com.gz.eim.am.stock.service.assets;

import com.gz.eim.am.stock.entity.StockAssetCodeOperate;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2020/3/13
 * @description 资产编号操作标识
 */
public interface StockAssetCodeOperateService {

    /**
     * 插入资产编号标识
     * @param stockAssetCodeOperate
     * @return
     */
    boolean insert(StockAssetCodeOperate stockAssetCodeOperate);

    /**
     * 获取当前操作人最新的操作标识
     * @param employeeCode
     * @return
     */
    StockAssetCodeOperate selectUserNewOperating(String employeeCode);

    /**
     * 根据操作标识更新操作记录
     * @param stockAssetCodeOperate
     * @return
     */
    boolean updateSelectiveByOperating(StockAssetCodeOperate stockAssetCodeOperate);

    /**
     * 根据参数去查询
     * @param stockAssetCodeOperate
     * @return
     */
    List<StockAssetCodeOperate> selectByParam(StockAssetCodeOperate stockAssetCodeOperate);
}
