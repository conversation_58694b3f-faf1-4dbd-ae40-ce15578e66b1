package com.gz.eim.am.stock.service.impl.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.JsonUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.MetaDataConstants;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.base.CheckDifferenceListHeadMapper;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListLineReqDTO;
import com.gz.eim.am.stock.dto.response.check.CheckDifferenceListHeadRespDTO;
import com.gz.eim.am.stock.dto.response.check.CheckDifferenceListLineRespDTO;
import com.gz.eim.am.stock.dto.response.check.StockAssetsCheckTaskDetailRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.check.*;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.StockCheckMissionEnum;
import com.gz.eim.am.stock.util.em.StockTakingPlanEnum;
import com.gz.eim.am.stock.util.em.SuppliesEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/11/21
 * @description
 */
@Slf4j
@Service
public class StockCheckResultServiceImpl implements StockCheckResultService {

    @Value("${project.file.systemModule}")
    private String systemModule;
    @Value("${project.file.attachModule}")
    private String attachModule;
    @Autowired
    private FileServiceApi fileServiceApi;
    @Autowired
    StockTakingPlanService stockTakingPlanService;
    @Autowired
    StockCheckMissionService stockCheckMissionService;
    @Autowired
    StockTakingProcessService stockTakingProcessService;
    @Autowired
    StockCheckMissionDetailService stockCheckMissionDetailService;
    @Autowired
    AmbaseCommonService ambaseCommonService;
    @Autowired
    CheckDifferenceListHeadMapper checkDifferenceListHeadMapper;
    @Autowired
    CheckDifferenceListLineService checkDifferenceListLineService;
    @Autowired
    StockAssetsCheckHistoryService stockAssetsCheckHistoryService;
    @Autowired
    StockAssetsService stockAssetsService;
    @Autowired
    StockSuppliesService stockSuppliesService;
    @Autowired
    CheckDifferenceListHeadService checkDifferenceListHeadService;
    @Autowired
    private StockAssetsCheckTaskDetailService stockAssetsCheckTaskDetailService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData takingPlanEnd() throws Exception {
        //一、将过了截止日期的盘点单据，所有任务结束掉，生成差异清单节点=进行中
        //1.从盘点计划单表查询所有进行中的有效单据,过了截止日期并且流程状态录入节点！=已完成
        List<StockTakingPlan> takingPlanList = stockTakingPlanService.getValidTakingPlanNoList(StockTakingPlanEnum.PlanStatus.NOTSTART.getStatus());
        if (CollectionUtils.isEmpty(takingPlanList)){
            log.info("没有可结束盘点的盘点单据");
            return ResponseData.createSuccessResult();
        }
        List<String> takingPlanNoList = new ArrayList<>();
        for(StockTakingPlan dto : takingPlanList){
            StockTakingProcess  takingProcess = stockTakingProcessService.queryByTakingPlanNo(dto.getTakingPlanNo());
            if (DateUtils.dateCompare(dto.getTakingDeadlineDate(), new Date())<=0 && !StockTakingPlanEnum.ProcessStatus.FINISH.getStatus().equals(takingProcess.getInputTakingDataStatus())){
                takingPlanNoList.add(dto.getTakingPlanNo());
            }
        }
        if (CollectionUtils.isEmpty(takingPlanNoList)){
            log.info("没有可结束盘点的盘点单据");
            return ResponseData.createSuccessResult();
        }
        //2.所有任务都完成
        stockCheckMissionService.updateCheckTaskEnd(takingPlanNoList);
        //3.各节点完成,生成盘点差异清单节点进行中
        StringBuilder sb = new StringBuilder();
        for(String takingPlanNo : takingPlanNoList){
            sb.append(takingPlanNo+";");
            StockTakingProcess  stockTakingProcess = stockTakingProcessService.queryByTakingPlanNo(takingPlanNo);
            //组装要更新的计划状态数据
            changeTakingProcess(stockTakingProcess);
            stockTakingProcessService.updateTakingPlanStaus(stockTakingProcess,takingPlanNo);
        }
        log.info("本次结束盘点完成，单据号有："+sb.toString());

        //二、将已经完成的盘点计划数据备份到历史表
        log.info("开始备份已经调整结束的盘点计划");
        //查询所有已经结束的盘点任务
        List<StockTakingPlan> takingPlans = stockTakingPlanService.getValidTakingPlanNoList(StockTakingPlanEnum.PlanStatus.INPROCESS.getStatus());
        if (CollectionUtils.isEmpty(takingPlans)){
            log.info("没有可备份的盘点单据");
            return ResponseData.createSuccessResult();
        }
        takingPlans.forEach(dto->{
            stockAssetsCheckHistoryService.CopyToHistotyTable(dto.getTakingPlanNo());
        });
        log.info("备份已经调整结束的盘点计划完成。。");
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData init(AssetQueryScopeReqDTO assetQueryScopeReqDTO) throws Exception {
        //1.校验参数
        if (assetQueryScopeReqDTO == null || StringUtils.isBlank(assetQueryScopeReqDTO.getTakingPlanNo())){
            throw new ServiceUncheckedException("参数不能为空");
        }
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(assetQueryScopeReqDTO.getTakingPlanNo());
        if (stockTakingPlan == null){
            throw new ServiceUncheckedException("盘点计划单编码："+assetQueryScopeReqDTO.getTakingPlanNo()+"没有查到对应单据");
        }
        JwtUser user = SecurityUtil.getJwtUser();
        if (!user.getEmployeeCode().equals(stockTakingPlan.getCreatedBy()) && !superAdminFlag(user.getEmployeeCode())){
            throw new ServiceUncheckedException("没有操作权限");
        }
        //2.组织异常清单头信息
        CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO = assemHeadRespDTO(stockTakingPlan);
        //4.查询异常清单行总数
        assetQueryScopeReqDTO.setDifference(StockCheckMissionEnum.DetailFlag.NO.getValue());
        assetQueryScopeReqDTO.setNoContainCheckFlag(StockCheckMissionEnum.CheckFlag.CANCEL.getValue());
        long detailCount = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        checkDifferenceListHeadRespDTO.setDifferenceCount(detailCount);
        if (detailCount<=0){
            return ResponseData.createSuccessResult(checkDifferenceListHeadRespDTO);
        }
        //3.组织异常清单行信息
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineReqDTOList = assemLineRespDTO(assetQueryScopeReqDTO);
        //4.返回结果
        checkDifferenceListHeadRespDTO.setCheckDifferenceListLineRespDTOS(checkDifferenceListLineReqDTOList);
        return ResponseData.createSuccessResult(checkDifferenceListHeadRespDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData save(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {
        //1.校验参数
        String paramResult = checkParam(checkDifferenceListHeadReqDTO);
        if (StringUtils.isNotBlank(paramResult)){
            return ResponseData.createFailResult(paramResult);
        }

        //2.更新盘点流程节点 生成盘点差异清单="清单生成中"
        StockTakingProcess stockTakingProcess = new StockTakingProcess();
        stockTakingProcess.setGenerateTakingResultDate(new Date());
        stockTakingProcess.setGenerateTakingResultStatus(StockTakingPlanEnum.ProcessStatus.LIST_INPROCESS.getStatus());
        stockTakingProcessService.updateTakingPlanStaus(stockTakingProcess, checkDifferenceListHeadReqDTO.getTakingPlanNo());

        //3.异步生成盘点差异清单
        JwtUser user = SecurityUtil.getJwtUser();
        checkDifferenceListHeadService.createCheckDifferenceList(checkDifferenceListHeadReqDTO,user);

        return ResponseData.createSuccessResult();
    }


    @Override
    public ResponseData  queryCheckResult(AssetQueryScopeReqDTO assetQueryScopeReqDTO) throws Exception {
        //1.参数校验
        if (assetQueryScopeReqDTO == null || StringUtils.isBlank(assetQueryScopeReqDTO.getTakingPlanNo())){
            throw new ServiceUncheckedException("查询参数不能为空");
        }
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(assetQueryScopeReqDTO.getTakingPlanNo());
        if (stockTakingPlan == null){
            throw new ServiceUncheckedException("盘点计划单编码："+assetQueryScopeReqDTO.getTakingPlanNo()+"没有查到对应单据");
        }
        JwtUser user = SecurityUtil.getJwtUser();
        if (!user.getEmployeeCode().equals(stockTakingPlan.getCreatedBy()) && !superAdminFlag(user.getEmployeeCode())){
            throw new ServiceUncheckedException("没有操作权限");
        }

        //2.查询头表信息
        CheckDifferenceListHead checkDifferenceListHead = new CheckDifferenceListHead();
        checkDifferenceListHead.setTakingPlanNo(assetQueryScopeReqDTO.getTakingPlanNo());
        CheckDifferenceListHeadExample example = getExample(checkDifferenceListHead);
        List<CheckDifferenceListHead> checkDifferenceListHeads = checkDifferenceListHeadMapper.selectByExample(example);
        if (checkDifferenceListHeads.size()>1){
            throw new ServiceUncheckedException("计划单"+assetQueryScopeReqDTO.getTakingPlanNo()+"查到多个异常清单结果");
        }
        CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO = new CheckDifferenceListHeadRespDTO();
        BeanUtils.copyProperties(checkDifferenceListHeads.get(0),checkDifferenceListHeadRespDTO);
        SysUserBasicInfo sysUser = ambaseCommonService.queryUserBasicInfoByEmpId(checkDifferenceListHeadRespDTO.getCheckDuty());
        checkDifferenceListHeadRespDTO.setCheckDutyName(sysUser.getName());
        //3.查询行总数
        assetQueryScopeReqDTO.setHeadId(checkDifferenceListHeadRespDTO.getHeadId());
        assetQueryScopeReqDTO.initPageParam();
        long count = checkDifferenceListLineService.getLineCount(assetQueryScopeReqDTO);
        checkDifferenceListHeadRespDTO.setDifferenceCount(count);
        if (count<=0){
            return ResponseData.createSuccessResult(checkDifferenceListHeadRespDTO);
        }
        //4.查询行表信息
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = checkDifferenceListLineService.getLineRespDTOs(assetQueryScopeReqDTO);
        checkDifferenceListHeadRespDTO.setCheckDifferenceListLineRespDTOS(checkDifferenceListLineRespDTOList);
        //5.返回结果
        return ResponseData.createSuccessResult(checkDifferenceListHeadRespDTO);
    }


    @Override
    public ResponseData saveCheckResultRemark(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {
        //1.校验参数
        if (checkDifferenceListHeadReqDTO == null || CollectionUtils.isEmpty(checkDifferenceListHeadReqDTO.getCheckDifferenceListLineReqDTOS())) {
            return ResponseData.createFailResult("提交参数不能为空");
        }
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        if (stockTakingPlan == null) {
            return ResponseData.createFailResult("盘点计划单编码：" + checkDifferenceListHeadReqDTO.getTakingPlanNo() + "没有查到对应单据");
        }
        JwtUser user = SecurityUtil.getJwtUser();
        if (!SecurityUtil.getJwtUser().getEmployeeCode().equals(stockTakingPlan.getCreatedBy()) && !superAdminFlag(user.getEmployeeCode())) {
            return ResponseData.createFailResult("没有操作权限");
        }

        String checkResult = saveResultRemarkCheck(checkDifferenceListHeadReqDTO);

        //2.更新备注
        List<CheckDifferenceListLineReqDTO> checkDifferenceListLineReqDTOS = checkDifferenceListHeadReqDTO.getCheckDifferenceListLineReqDTOS();
        //获取校验通过的行数据
        List<CheckDifferenceListLineReqDTO> checkDifferenceListLineReqDTOList = checkDifferenceListLineReqDTOS.stream().filter(dto->!CommonConstant.NUMBER_ONE.equals(dto.getValidErrFlag())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(checkDifferenceListLineReqDTOList)) {
            List<CheckDifferenceListLine> checkDifferenceListLineList = new ArrayList<>(checkDifferenceListLineReqDTOList.size());
            checkDifferenceListLineReqDTOList.forEach(dto -> {
                CheckDifferenceListLine checkDifferenceListLine = new CheckDifferenceListLine();
                BeanUtils.copyProperties(dto, checkDifferenceListLine);
                checkDifferenceListLineList.add(checkDifferenceListLine);
            });
            checkDifferenceListLineService.batchUpdateSelective(checkDifferenceListLineList);
        }

        if (StringUtils.isNotBlank(checkResult)) {
            return ResponseData.createFailResult(checkResult);
        }

        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData getFileUrlListByRelId(String takingPlanNo, String assetsCode) {
        // 查询盘点任务行信息，如果存在图片url直接返回即可
        StockAssetsCheckTaskDetailReqDO stockAssetsCheckTaskDetailReqDO = new StockAssetsCheckTaskDetailReqDO();
        stockAssetsCheckTaskDetailReqDO.setTakingPlanNo(takingPlanNo);
        stockAssetsCheckTaskDetailReqDO.setSnapshotAssetsCode(assetsCode);
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = stockAssetsCheckTaskDetailService.selectOne(stockAssetsCheckTaskDetailReqDO);
        if(null == stockAssetsCheckTaskDetail){
            return ResponseData.createFailResult("该资产编码在盘点计划中不存在");
        }
        String pictureUrlList = stockAssetsCheckTaskDetail.getPictureUrlList();
        if(StringUtils.isNotBlank(pictureUrlList)){
            String[] pictureUrlArray = pictureUrlList.split(StringConstant.SEMI_COLON_HALF);
            List<PictureMetaRespDTO> pictureMetaRespDTOList = new ArrayList<>(pictureUrlArray.length);
            for (String pictureUrl : pictureUrlArray) {
                PictureMetaRespDTO pictureMetaRespDTO = new PictureMetaRespDTO();
                pictureMetaRespDTO.setShowPicUrl(pictureUrl);
                pictureMetaRespDTOList.add(pictureMetaRespDTO);
            }
            return ResponseData.createSuccessResult(pictureMetaRespDTOList);
        }
        QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO();
        queryFileReqDTO.setSystemModule(systemModule);
        queryFileReqDTO.setAttachModule(attachModule);
        queryFileReqDTO.setPageModule("CHECK_PICTURE");
        queryFileReqDTO.setRelId(takingPlanNo+"_"+assetsCode);
        ResponseData<List<PictureMetaRespDTO>> responseData = fileServiceApi.getFileUrlListByRelId(queryFileReqDTO);
        return responseData;
    }

    /**
     * 保存调整结果数据校验
     *
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    private String saveResultRemarkCheck(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {
        StringBuilder sb = new StringBuilder();

        List<CheckDifferenceListLineReqDTO> checkDifferenceListLineReqDTOS = checkDifferenceListHeadReqDTO.getCheckDifferenceListLineReqDTOS();

        //新录入的资产编码不能在系统存在
        List<String> adjustAssetsCodes = checkDifferenceListLineReqDTOS.stream().map(dto->dto.getAdjustAssetsCode()).collect(Collectors.toList());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, adjustAssetsCodes);
        List<String> assetsCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stockAssetsList)) {
            assetsCodeList = stockAssetsList.stream().map(dto->dto.getAssetsCode()).collect(Collectors.toList());
        }

        for (CheckDifferenceListLineReqDTO checkDifferenceListLineReqDTO : checkDifferenceListLineReqDTOS) {
            if (CollectionUtils.isNotEmpty(assetsCodeList) && assetsCodeList.contains(checkDifferenceListLineReqDTO.getAdjustAssetsCode())) {
                checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                sb.append("盘盈资产编码在系统已存在："+checkDifferenceListLineReqDTO.getAdjustAssetsCode()+";");
                continue;
            }
            if (StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue().equals(checkDifferenceListLineReqDTO.getActualHandleMethod())) {
                if (null == checkDifferenceListLineReqDTO.getEstimatedAmount() || BigDecimal.ZERO.compareTo(checkDifferenceListLineReqDTO.getEstimatedAmount()) == CommonConstant.NUMBER_ONE) {
                    sb.append("盘盈资产的预估金额不能空或小于0");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (StringUtils.isBlank(checkDifferenceListLineReqDTO.getCompanyCode())) {
                    sb.append("盘盈资产需要录入所属公司");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (null == checkDifferenceListLineReqDTO.getTimeLeft() || CommonConstant.NUMBER_ZERO > checkDifferenceListLineReqDTO.getTimeLeft()) {
                    sb.append("盘盈资产剩余使用年限不能为空或小于0");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (StringUtils.isBlank(checkDifferenceListLineReqDTO.getSuppliesCode())) {
                    sb.append("盘盈资产所属物料不能为空");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                //重点校验 ：入库条件 仓库、物料、序列号、资产编码不能为空；序列号、资产编码、资产名称不为空的校验前置到盘点提交接口

                //物料在系统存在并且为序列号管理
                StockSupplies stockSupplies = stockSuppliesService.getSuppliesByCode(checkDifferenceListLineReqDTO.getSuppliesCode(), null);
                if (stockSupplies == null) {
                    sb.append("物料:"+checkDifferenceListLineReqDTO.getSuppliesCode()+"不存在");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (null != stockSupplies.getManageType() && stockSupplies.getManageType().equals(SuppliesEnum.ManageType.SN_MANAGE.getType()) && StringUtils.isBlank(checkDifferenceListLineReqDTO.getAdjustSnCode())) {
                    sb.append("物料:"+checkDifferenceListLineReqDTO.getSuppliesCode()+"是序列号管理,调整后的序列号不能为空");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (null != stockSupplies.getManageType() && !stockSupplies.getManageType().equals(SuppliesEnum.ManageType.SN_MANAGE.getType()) && StringUtils.isNotBlank(checkDifferenceListLineReqDTO.getAdjustSnCode())) {
                    sb.append("物料:"+checkDifferenceListLineReqDTO.getSuppliesCode()+"不是序列号管理,不应提交序列号");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (null != stockSupplies.getManageType() && !stockSupplies.getManageType().equals(SuppliesEnum.ManageType.SN_MANAGE.getType())) {
                    //非序列号管理，将调整序列号更新为空字符
                    checkDifferenceListLineReqDTO.setAdjustSnCode("");
                }

                //物料与入库仓库（实际录入仓库或默认仓库）匹配
                List<CheckDifferenceListLine> checkDifferenceListLineList = checkDifferenceListLineService.getLineIdsByAdjustFlag(null,Arrays.asList(checkDifferenceListLineReqDTO.getLineId()),null,null);
                if (CollectionUtils.isEmpty(checkDifferenceListLineList)) {
                    log.error("盘点调整更新时，提交的差异清单行数据不存在，lineId："+checkDifferenceListLineReqDTO.getLineId());
                    sb.append("提交的差异清单行数据不存在，lineId："+checkDifferenceListLineReqDTO.getLineId());
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                String inWarehouseCode = StringUtils.isBlank(checkDifferenceListLineList.get(0).getRealWarehouseCode()) ? CommonConstant.DEFAULT_WAREHOUSE_CODE : checkDifferenceListLineList.get(0).getRealWarehouseCode();
                if (!stockSuppliesService.judgeAuthByWarehouseCode(inWarehouseCode, stockSupplies)) {
                    sb.append("物料"+stockSupplies.getCode()+"不属于要入库的仓库:"+inWarehouseCode);
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }

                //序列号不能为空并且不能在系统存在
                String snCode= StringUtils.isNotBlank(checkDifferenceListLineReqDTO.getAdjustSnCode()) ? checkDifferenceListLineReqDTO.getAdjustSnCode() : checkDifferenceListLineList.get(0).getSnapshotSnNo();
                if (null != stockSupplies.getManageType() && stockSupplies.getManageType().equals(SuppliesEnum.ManageType.SN_MANAGE.getType()) && StringUtils.isBlank(snCode)) {
                    sb.append("盘盈物料为序列号管理,资产序列号不能为空");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }

                if (null != stockSupplies.getManageType() && stockSupplies.getManageType().equals(SuppliesEnum.ManageType.SN_MANAGE.getType()) && CollectionUtils.isNotEmpty(stockAssetsService.selectAssets(Arrays.asList(snCode)))) {
                    sb.append("盘盈入库的序列号："+checkDifferenceListLineList.get(0).getSnapshotSnNo()+"在系统已经存在");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }

            }

            /*if (StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue().equals(checkDifferenceListLineReqDTO.getActualHandleMethod())) {
                if (Objects.isNull(checkDifferenceListLineReqDTO.getNetValue())) {
                    sb.append("盘亏资产的资产净值不能为空");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
                if (BigDecimal.ZERO.compareTo(checkDifferenceListLineReqDTO.getNetValue()) == CommonConstant.NUMBER_ONE) {
                    sb.append("盘亏资产的资产净值不能小于0");
                    checkDifferenceListLineReqDTO.setValidErrFlag(CommonConstant.NUMBER_ONE);
                    continue;
                }
            }*/
        }

        if (sb.length() != 0) {
            return sb.toString();
        }

        return null;
    }

    /**
     * 生成差异清单参数校验
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    private String checkParam(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception {
        if (checkDifferenceListHeadReqDTO == null){
            return "参数不能为空";
        }
        if (StringUtils.isBlank(checkDifferenceListHeadReqDTO.getTakingPlanNo())){
            return "盘点计划单编码";
        }
        if (StringUtils.isBlank(checkDifferenceListHeadReqDTO.getCheckTimeQuantum())){
            return "盘点时间段不能为空";
        }
        if (StringUtils.isBlank(checkDifferenceListHeadReqDTO.getCheckDuty())){
            return "盘点责任人不能为空";
        }
        if (StringUtils.isBlank(checkDifferenceListHeadReqDTO.getCheckScope())){
            return "盘点范围不能为空";
        }
        if (StringUtils.isBlank(checkDifferenceListHeadReqDTO.getCheckTaskMethod())){
            return "盘点方式不能为空";
        }
        //校验权限
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        if (stockTakingPlan == null){
            throw new ServiceUncheckedException("盘点计划单编码："+checkDifferenceListHeadReqDTO.getTakingPlanNo()+"没有查到对应单据");
        }
        JwtUser user = SecurityUtil.getJwtUser();
        if (!user.getEmployeeCode().equals(stockTakingPlan.getCreatedBy()) && !superAdminFlag(user.getEmployeeCode())){
            throw new ServiceUncheckedException("没有操作权限");
        }
        //一次盘点只能有一份结果
        CheckDifferenceListHead checkDifferenceListHead = new CheckDifferenceListHead();
        checkDifferenceListHead.setTakingPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        CheckDifferenceListHeadExample example = getExample(checkDifferenceListHead);
        if (CollectionUtils.isNotEmpty(checkDifferenceListHeadMapper.selectByExample(example))){
            return "本次盘点已经生成了盘点异常清单，不能重复生成";
        }
        return null;
    }

    /**
     * 获取当前盘点下的所有异常数据
     * @param assetQueryScopeReqDTO
     * @return
     */
    private List<CheckDifferenceListLineRespDTO> assemLineRespDTO(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = new ArrayList<>();

        List<StockAssetsCheckTaskDetailRespDTO> stockAssetsCheckTaskDetailRespDTOS = stockCheckMissionDetailService.getTaskDetailByConditions(assetQueryScopeReqDTO);
        // 获取所有的盘点人
        Set<String> checkPeopleSet = new HashSet<>();
        for (StockAssetsCheckTaskDetailRespDTO stockAssetsCheckTaskDetailRespDTO : stockAssetsCheckTaskDetailRespDTOS) {
            String checkPeople = stockAssetsCheckTaskDetailRespDTO.getCheckPeople();
            if(StringUtils.isNotBlank(checkPeople)){
                checkPeopleSet.addAll(Arrays.asList(checkPeople.split(StringConstant.SEMI_COLON_HALF)));
            }
        }
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(checkPeopleSet)){
            sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(checkPeopleSet));

        }
        if (!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailRespDTOS)){
            for(StockAssetsCheckTaskDetailRespDTO dto : stockAssetsCheckTaskDetailRespDTOS){
                CheckDifferenceListLineRespDTO checkDifferenceListLineRespDTO = new CheckDifferenceListLineRespDTO();
                BeanUtils.copyProperties(dto,checkDifferenceListLineRespDTO);
                getSystemAdvise(checkDifferenceListLineRespDTO, dto);
                //将异常状态全部置为异常
                checkDifferenceListLineRespDTO.setDifference(StockCheckMissionEnum.DetailFlag.NO.getValue());
                checkDifferenceListLineRespDTO.setDifferenceName("是");
                checkDifferenceListLineRespDTO.setLineId(dto.getTaskDetailId());
                String checkPeople = dto.getCheckPeople();
                if(StringUtils.isNotBlank(checkPeople)){
                    StringBuilder checkPeopleBuilder = new StringBuilder();
                    for (String tempCheckPeople : checkPeople.split(StringConstant.SEMI_COLON_HALF)) {
                        SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(tempCheckPeople);
                        if(sysUserBasicInfo != null){
                            checkPeopleBuilder.append(sysUserBasicInfo.getName());
                            String email = sysUserBasicInfo.getEmail();
                            if(StringUtils.isNotBlank(email)){
                                checkPeopleBuilder.append(StringConstant.ENGLISH_LEFT_PARENTHESIS);
                                checkPeopleBuilder.append(email, CommonConstant.NUMBER_ZERO, email.indexOf(StringConstant.AT));
                                checkPeopleBuilder.append(StringConstant.ENGLISH_RIGHT_PARENTHESIS);
                            }
                            checkPeopleBuilder.append(StringConstant.SEMI_COLON_HALF);
                        }
                    }
                    if(checkPeopleBuilder.length() > CommonConstant.NUMBER_ZERO){
                        checkDifferenceListLineRespDTO.setCheckPersonListStr(checkPeopleBuilder.substring(CommonConstant.NUMBER_ZERO, checkPeopleBuilder.length() - CommonConstant.NUMBER_ONE));
                    }
                }
                checkDifferenceListLineRespDTOList.add(checkDifferenceListLineRespDTO);
            }
        }
        return checkDifferenceListLineRespDTOList;
    }

    /**
     * 组织异常清单头信息
     * @param stockTakingPlan
     * @return
     */
    private CheckDifferenceListHeadRespDTO assemHeadRespDTO(StockTakingPlan stockTakingPlan) throws ParseException {
        CheckDifferenceListHeadRespDTO checkDifferenceListHeadRespDTO = new CheckDifferenceListHeadRespDTO();
        //获取盘点时间段
        String startDate = DateUtils.dateFormat(stockTakingPlan.getSystemDate(),null);
        String endDate = DateUtils.dateFormat(stockTakingPlan.getTakingDeadlineDate(),null);
        checkDifferenceListHeadRespDTO.setCheckTimeQuantum(startDate+"---"+endDate);
        //获取盘点总数和正常资产数
        AssetQueryScopeReqDTO assetQueryScopeReqDTO = new AssetQueryScopeReqDTO();
        assetQueryScopeReqDTO.setTakingPlanNo(stockTakingPlan.getTakingPlanNo());
        assetQueryScopeReqDTO.setNoContainCheckFlag(StockCheckMissionEnum.CheckFlag.CANCEL.getValue());
        long allNum = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        assetQueryScopeReqDTO.setDifference(StockCheckMissionEnum.DetailFlag.YES.getValue());
        long normalNum = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        //自动盘点数量
        assetQueryScopeReqDTO.setIsAutoCheck(StockCheckMissionEnum.isAutoCheck.YES.getValue());
        long autoCheckNum = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        checkDifferenceListHeadRespDTO.setCheckAllNumber((int) allNum);
        checkDifferenceListHeadRespDTO.setAutoCheckNumber((int)autoCheckNum);
        checkDifferenceListHeadRespDTO.setCheckNormalNumber((int) normalNum);
        checkDifferenceListHeadRespDTO.setCheckNoNormalNumber((int) (allNum-normalNum));

        //获取盘点方式
        List<Integer> checkMethods = stockCheckMissionService.allCheckMethods(stockTakingPlan.getTakingPlanNo());
        Map<Integer,String> methodMap = StockCheckMissionEnum.taskCheckMethodEnumMap;
        StringBuilder sb = new StringBuilder();
        for(Integer methodCode : checkMethods){
            sb.append(methodMap.get(methodCode)+",");
        }
        String methodNames = sb.toString();
        methodNames = methodNames.substring(0,methodNames.length() - 1);
        checkDifferenceListHeadRespDTO.setCheckTaskMethod(methodNames);
        //获取盘点人
        SysUserBasicInfo sysUser = ambaseCommonService.queryUserBasicInfoByEmpId(stockTakingPlan.getCreatedBy());
        checkDifferenceListHeadRespDTO.setCheckDuty(stockTakingPlan.getCreatedBy());
        checkDifferenceListHeadRespDTO.setCheckDutyName(sysUser.getName());
        //获取其它字段
        checkDifferenceListHeadRespDTO.setTakingPlanNo(stockTakingPlan.getTakingPlanNo());
        checkDifferenceListHeadRespDTO.setListTitle("车好多集团"+DateUtils.dateFormat(new Date(),DateUtils.YEAR_PATTERN)+"年资产盘点差异清单");
        checkDifferenceListHeadRespDTO.setCheckScope("全部在库资产");
        return checkDifferenceListHeadRespDTO;
    }

    /**
     * 修改要更新的状态数据
     * @param stockTakingProcess
     */
    private void changeTakingProcess(StockTakingProcess stockTakingProcess) {
        //创建盘点任务没有完成
        if (stockTakingProcess.getCreateTakingTaskDate() == null){
            stockTakingProcess.setCreateTakingTask(StockTakingPlanEnum.TakingPlanProcessEnum.CREATE_TAKING_TASK.getCode());
            stockTakingProcess.setCreateTakingTaskStatus(StockTakingPlanEnum.ProcessStatus.FINISH.getStatus());
            stockTakingProcess.setCreateTakingTaskDate(new Date());
        }
        //下发盘点任务没有完成
        if (stockTakingProcess.getAssignTakingTaskDate() == null){
            stockTakingProcess.setAssignTakingTask(StockTakingPlanEnum.TakingPlanProcessEnum.ASSIGN_TAKING_TASK.getCode());
            stockTakingProcess.setAssignTakingTaskStatus(StockTakingPlanEnum.ProcessStatus.FINISH.getStatus());
            stockTakingProcess.setAssignTakingTaskDate(new Date());
        }
        //录入盘点任务没有完成
        if (stockTakingProcess.getInputTakingDataDate() == null){
            stockTakingProcess.setInputTakingData(StockTakingPlanEnum.TakingPlanProcessEnum.INPUT_TAKING_DATA.getCode());
            stockTakingProcess.setInputTakingDataStatus(StockTakingPlanEnum.ProcessStatus.FINISH.getStatus());
            stockTakingProcess.setInputTakingDataDate(new Date());
        }
        //生成盘点异常清单
        if (stockTakingProcess.getGenerateTakingResultDate() == null){
            stockTakingProcess.setGenerateTakingResult(StockTakingPlanEnum.TakingPlanProcessEnum.GENERATE_TAKING_RESULT.getCode());
            stockTakingProcess.setGenerateTakingResultStatus(StockTakingPlanEnum.ProcessStatus.INPROCESS.getStatus());
        }
    }

    private CheckDifferenceListHeadExample getExample(CheckDifferenceListHead checkDifferenceListHead){
        CheckDifferenceListHeadExample example = new CheckDifferenceListHeadExample();
        CheckDifferenceListHeadExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(checkDifferenceListHead.getTakingPlanNo())){
            criteria.andTakingPlanNoEqualTo(checkDifferenceListHead.getTakingPlanNo());
        }
        //按需要继续添加字段。。
        return example;
    }

    /**
     * 赋值系统处理建议
     * @param checkDifferenceListLine
     * @param stockAssetsCheckTaskDetail
     */
    private void getSystemAdvise(CheckDifferenceListLineRespDTO checkDifferenceListLine, StockAssetsCheckTaskDetailRespDTO stockAssetsCheckTaskDetail) {
        // 如果不是已盘点，直接设置盘点结果为未盘点
        if(!StockCheckMissionEnum.CheckFlag.YES.getValue().equals(stockAssetsCheckTaskDetail.getCheckFlag())){
            checkDifferenceListLine.setCheckResult("未盘点");
            return;
        }
        if (CommonConstant.NUMBER_ONE.equals(stockAssetsCheckTaskDetail.getSnapshotNumber()) && CommonConstant.NUMBER_ZERO.equals(stockAssetsCheckTaskDetail.getRealNumber())){
            checkDifferenceListLine.setCheckResult("盘亏");
            return;
        }
        if (CommonConstant.NUMBER_ZERO.equals(stockAssetsCheckTaskDetail.getSnapshotNumber()) && CommonConstant.NUMBER_ONE.equals(stockAssetsCheckTaskDetail.getRealNumber())){
            //判断是否为真正的盘盈（系统中不存在）
            StockAssets stockAssets = stockAssetsService.selectAssetsByCode(stockAssetsCheckTaskDetail.getRealAssetsCode());
            if (!Objects.isNull(stockAssets) && stockAssets.getConditions().equals(stockAssetsCheckTaskDetail.getRealAssetsConditions())) {
                checkDifferenceListLine.setCheckResult("暂不处理");
                return;
            }
            //判断是否为线下盘点方式 处理方式=暂不处理
            StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(stockAssetsCheckTaskDetail.getCheckTaskId(),null);
            if (stockAssetsCheckTask != null && StockCheckMissionEnum.TaskCheckMethod.OFFLINE_CHECK.getValue().equals(stockAssetsCheckTask.getCheckTaskMethod())) {
                checkDifferenceListLine.setCheckResult("暂不处理");
                return;
            }
            if (!Objects.isNull(stockAssets) && !stockAssets.getConditions().equals(stockAssetsCheckTaskDetail.getRealAssetsConditions())) {
                checkDifferenceListLine.setCheckResult("更新");
                return;
            }
            checkDifferenceListLine.setCheckResult("盘盈");
            return;
        }
        if (AssetsEnum.statusType.IDLE.getValue().equals(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()) && AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getRealAssetsStatus())){
            checkDifferenceListLine.setCheckResult("领用");
            return;
        }
        if (AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()) && AssetsEnum.statusType.IDLE.getValue().equals(stockAssetsCheckTaskDetail.getRealAssetsStatus())){
            checkDifferenceListLine.setCheckResult("归还");
            return;
        }
        if (StringUtils.isNotBlank(stockAssetsCheckTaskDetail.getSnapshotWarehouseCode()) && StringUtils.isNotBlank(stockAssetsCheckTaskDetail.getRealWarehouseCode()) &&
                !stockAssetsCheckTaskDetail.getSnapshotWarehouseCode().equals(stockAssetsCheckTaskDetail.getRealWarehouseCode())){
            checkDifferenceListLine.setCheckResult("调拨");
            return;
        }
        if (AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()) && AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getRealAssetsStatus()) &&
                !stockAssetsCheckTaskDetail.getSnapshotAssetsHolder().equals(stockAssetsCheckTaskDetail.getRealAssetsHolder())){
            checkDifferenceListLine.setCheckResult("调整");
            return;
        }
        if (!stockAssetsCheckTaskDetail.getSnapshotAssetsConditions().equals(stockAssetsCheckTaskDetail.getRealAssetsConditions())){
            //判断是否为线下盘点方式 处理方式=暂不处理
            StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(stockAssetsCheckTaskDetail.getCheckTaskId(),null);
            if (stockAssetsCheckTask != null && StockCheckMissionEnum.TaskCheckMethod.OFFLINE_CHECK.getValue().equals(stockAssetsCheckTask.getCheckTaskMethod())) {
                checkDifferenceListLine.setCheckResult("暂不处理");
                return;
            }
            checkDifferenceListLine.setCheckResult("更新");
            return;
        }

        checkDifferenceListLine.setCheckResult("暂不处理");
    }

    /**
     * 判断员工是否为盘点功能的超级管理员
     * @param empId
     * @return true 是超级管理员  false不是超级管理员
     */
    private Boolean superAdminFlag(String empId) {
        List<String> checkAdminList = ambaseCommonService.selectMetaDataByMdKey(MetaDataConstants.STOCK_MODULE_ASSETS,MetaDataConstants.STOCK_CHECK_ADMIN_MDKEY);
        if (CollectionUtils.isNotEmpty(checkAdminList) && checkAdminList.contains(empId)) {
            return true;
        }
        return false;
    }
}
