package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockAssetsEbsSync {
    private Long id;

    private String assetsCode;

    private String batchNo;

    private String queryCode;

    private Integer syncStatus;

    private String errorMessage;

    private Date extractEndDate;

    private Date extractStartDate;

    private String billingTime;

    private String billingUser;

    private String businessLineNo;

    private String businessNo;

    private Integer businessType;

    private String sysCode;

    private String assetCategorySegment1;

    private String assetCategorySegment2;

    private String assetKeySegment1;

    private String assetKeySegment2;

    private String assetKeySegment3;

    private String assetType;

    private String bookTypeCode;

    private String datePlacedInService;

    private String depreciateFlag;

    private String description;

    private String deprnMethodCode;

    private BigDecimal fixedAssetsCost;

    private Integer fixedAssetsUnits;

    private String invoiceNumber;

    private String locSegment1;

    private String locSegment2;

    private String locSegment3;

    private Integer lifeInMonths;

    private String modelNumber;

    private String queueName;

    private String serialNumber;

    private String accSegment1;

    private String accSegment2;

    private String accSegment3;

    private String accSegment4;

    private String accSegment5;

    private String accSegment6;

    private String accSegment7;

    private String accSegment8;

    private String accSegment9;

    private String accSegment10;

    private String accSegment11;

    private Integer versionId;

    private String tagNumber;

    private String faCode;

    private Integer assetId;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getQueryCode() {
        return queryCode;
    }

    public void setQueryCode(String queryCode) {
        this.queryCode = queryCode == null ? null : queryCode.trim();
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage == null ? null : errorMessage.trim();
    }

    public Date getExtractEndDate() {
        return extractEndDate;
    }

    public void setExtractEndDate(Date extractEndDate) {
        this.extractEndDate = extractEndDate;
    }

    public Date getExtractStartDate() {
        return extractStartDate;
    }

    public void setExtractStartDate(Date extractStartDate) {
        this.extractStartDate = extractStartDate;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime == null ? null : billingTime.trim();
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser == null ? null : billingUser.trim();
    }

    public String getBusinessLineNo() {
        return businessLineNo;
    }

    public void setBusinessLineNo(String businessLineNo) {
        this.businessLineNo = businessLineNo == null ? null : businessLineNo.trim();
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo == null ? null : businessNo.trim();
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode == null ? null : sysCode.trim();
    }

    public String getAssetCategorySegment1() {
        return assetCategorySegment1;
    }

    public void setAssetCategorySegment1(String assetCategorySegment1) {
        this.assetCategorySegment1 = assetCategorySegment1 == null ? null : assetCategorySegment1.trim();
    }

    public String getAssetCategorySegment2() {
        return assetCategorySegment2;
    }

    public void setAssetCategorySegment2(String assetCategorySegment2) {
        this.assetCategorySegment2 = assetCategorySegment2 == null ? null : assetCategorySegment2.trim();
    }

    public String getAssetKeySegment1() {
        return assetKeySegment1;
    }

    public void setAssetKeySegment1(String assetKeySegment1) {
        this.assetKeySegment1 = assetKeySegment1 == null ? null : assetKeySegment1.trim();
    }

    public String getAssetKeySegment2() {
        return assetKeySegment2;
    }

    public void setAssetKeySegment2(String assetKeySegment2) {
        this.assetKeySegment2 = assetKeySegment2 == null ? null : assetKeySegment2.trim();
    }

    public String getAssetKeySegment3() {
        return assetKeySegment3;
    }

    public void setAssetKeySegment3(String assetKeySegment3) {
        this.assetKeySegment3 = assetKeySegment3 == null ? null : assetKeySegment3.trim();
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType == null ? null : assetType.trim();
    }

    public String getBookTypeCode() {
        return bookTypeCode;
    }

    public void setBookTypeCode(String bookTypeCode) {
        this.bookTypeCode = bookTypeCode == null ? null : bookTypeCode.trim();
    }

    public String getDatePlacedInService() {
        return datePlacedInService;
    }

    public void setDatePlacedInService(String datePlacedInService) {
        this.datePlacedInService = datePlacedInService == null ? null : datePlacedInService.trim();
    }

    public String getDepreciateFlag() {
        return depreciateFlag;
    }

    public void setDepreciateFlag(String depreciateFlag) {
        this.depreciateFlag = depreciateFlag == null ? null : depreciateFlag.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getDeprnMethodCode() {
        return deprnMethodCode;
    }

    public void setDeprnMethodCode(String deprnMethodCode) {
        this.deprnMethodCode = deprnMethodCode == null ? null : deprnMethodCode.trim();
    }

    public BigDecimal getFixedAssetsCost() {
        return fixedAssetsCost;
    }

    public void setFixedAssetsCost(BigDecimal fixedAssetsCost) {
        this.fixedAssetsCost = fixedAssetsCost;
    }

    public Integer getFixedAssetsUnits() {
        return fixedAssetsUnits;
    }

    public void setFixedAssetsUnits(Integer fixedAssetsUnits) {
        this.fixedAssetsUnits = fixedAssetsUnits;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber == null ? null : invoiceNumber.trim();
    }

    public String getLocSegment1() {
        return locSegment1;
    }

    public void setLocSegment1(String locSegment1) {
        this.locSegment1 = locSegment1 == null ? null : locSegment1.trim();
    }

    public String getLocSegment2() {
        return locSegment2;
    }

    public void setLocSegment2(String locSegment2) {
        this.locSegment2 = locSegment2 == null ? null : locSegment2.trim();
    }

    public String getLocSegment3() {
        return locSegment3;
    }

    public void setLocSegment3(String locSegment3) {
        this.locSegment3 = locSegment3 == null ? null : locSegment3.trim();
    }

    public Integer getLifeInMonths() {
        return lifeInMonths;
    }

    public void setLifeInMonths(Integer lifeInMonths) {
        this.lifeInMonths = lifeInMonths;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber == null ? null : modelNumber.trim();
    }

    public String getQueueName() {
        return queueName;
    }

    public void setQueueName(String queueName) {
        this.queueName = queueName == null ? null : queueName.trim();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber == null ? null : serialNumber.trim();
    }

    public String getAccSegment1() {
        return accSegment1;
    }

    public void setAccSegment1(String accSegment1) {
        this.accSegment1 = accSegment1 == null ? null : accSegment1.trim();
    }

    public String getAccSegment2() {
        return accSegment2;
    }

    public void setAccSegment2(String accSegment2) {
        this.accSegment2 = accSegment2 == null ? null : accSegment2.trim();
    }

    public String getAccSegment3() {
        return accSegment3;
    }

    public void setAccSegment3(String accSegment3) {
        this.accSegment3 = accSegment3 == null ? null : accSegment3.trim();
    }

    public String getAccSegment4() {
        return accSegment4;
    }

    public void setAccSegment4(String accSegment4) {
        this.accSegment4 = accSegment4 == null ? null : accSegment4.trim();
    }

    public String getAccSegment5() {
        return accSegment5;
    }

    public void setAccSegment5(String accSegment5) {
        this.accSegment5 = accSegment5 == null ? null : accSegment5.trim();
    }

    public String getAccSegment6() {
        return accSegment6;
    }

    public void setAccSegment6(String accSegment6) {
        this.accSegment6 = accSegment6 == null ? null : accSegment6.trim();
    }

    public String getAccSegment7() {
        return accSegment7;
    }

    public void setAccSegment7(String accSegment7) {
        this.accSegment7 = accSegment7 == null ? null : accSegment7.trim();
    }

    public String getAccSegment8() {
        return accSegment8;
    }

    public void setAccSegment8(String accSegment8) {
        this.accSegment8 = accSegment8 == null ? null : accSegment8.trim();
    }

    public String getAccSegment9() {
        return accSegment9;
    }

    public void setAccSegment9(String accSegment9) {
        this.accSegment9 = accSegment9 == null ? null : accSegment9.trim();
    }

    public String getAccSegment10() {
        return accSegment10;
    }

    public void setAccSegment10(String accSegment10) {
        this.accSegment10 = accSegment10 == null ? null : accSegment10.trim();
    }

    public String getAccSegment11() {
        return accSegment11;
    }

    public void setAccSegment11(String accSegment11) {
        this.accSegment11 = accSegment11 == null ? null : accSegment11.trim();
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public String getTagNumber() {
        return tagNumber;
    }

    public void setTagNumber(String tagNumber) {
        this.tagNumber = tagNumber == null ? null : tagNumber.trim();
    }

    public String getFaCode() {
        return faCode;
    }

    public void setFaCode(String faCode) {
        this.faCode = faCode == null ? null : faCode.trim();
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}