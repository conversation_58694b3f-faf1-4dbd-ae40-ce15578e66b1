package com.gz.eim.am.stock.util.common;

import com.gz.eim.am.stock.constant.StringConstant;
import org.apache.commons.lang3.StringUtils;

import java.text.NumberFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/3/17
 * @description
 */
public class CommonUtil {
    /**
     * 易盘点域名
     */
    private static String oldAssetCodeUrl  = "www.epandian.cn";

    /**
     * 替换易盘点资产编码
     * @param oldAssetCode
     * @return
     */
    public static String replaceOldAssetCode(String oldAssetCode){
        if(StringUtils.isBlank(oldAssetCode)){
            return null;
        }

        if(oldAssetCode.contains(oldAssetCodeUrl)){
           return oldAssetCode.substring(oldAssetCode.lastIndexOf(StringConstant.SLASH)+1);
        }

        return oldAssetCode;
    }

    /**
     * 生成 0000000000001 格式
     * @param value 传入的数值
     * @param miniMum 生成的最小位数 不足该为0补足
     * @return
     */
    public static String getSn(Integer value,Integer miniMum){
        NumberFormat formatter = NumberFormat.getNumberInstance();
        // 整数显示最少位数不足前面补零
        formatter.setMinimumIntegerDigits(miniMum);
        formatter.setGroupingUsed(false);
        return formatter.format(value);
    }

    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;

    }
}
