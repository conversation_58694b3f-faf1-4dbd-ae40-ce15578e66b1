package com.gz.eim.am.stock.web.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.RedisUtil;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsTimeTaskService;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wangjing67
 * @Date: 6/21/21 3:35 下午
 * @description
 */
@RestController
@RequestMapping("/api/am/stock/assets/remind")
@Slf4j
public class StockPlanAssetsTimeTaskController {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockPlanAssetsTimeTaskService stockPlanAssetsTimeTaskService;


    /**
     * 资产借用归还提醒
     * @return
     */
    @GetMapping(value = "/send-message")
    public ResponseData mtPushSendMessage() {
        log.info("/api/am/stock/assets/remind/send-message");
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsTimeTaskService.mtPushSendMessage();
        }catch(Exception e){
            log.error("资产借用逾期归还提醒发送消息出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


}
