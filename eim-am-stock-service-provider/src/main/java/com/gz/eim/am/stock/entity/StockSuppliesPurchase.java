package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StockSuppliesPurchase {
    private Long id;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer isDelFlag;

    private String suppliesCode;

    private Integer warehouseTypeCode;

    private Integer isAllowPurchase;

    private Integer inventoryManageFlag;

    private Integer isUserApprovedSupplier;

    private Integer isRequestAskQuote;

    private Integer isMergeParity;

    private Integer isRequestBidding;

    private String defaultPurchaseUser;

    private String defaultCostItemCode;

    private BigDecimal defaultPurchasePrice;

    private Integer isRequestCheck;

    private Long defaultReceiveQuantityTolerance;

    private Integer isAllowReplaceReceive;

    private Integer isAllowNotOrderedReceive;

    private String defaultReceiveWay;

    private String defaultReceiveWarehouseCode;

    private String produceFactory;

    private String serviceFactory;

    private String afterSaleFactory;

    private Integer receiveType;

    private Long baseId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getIsDelFlag() {
        return isDelFlag;
    }

    public void setIsDelFlag(Integer isDelFlag) {
        this.isDelFlag = isDelFlag;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode == null ? null : suppliesCode.trim();
    }

    public Integer getWarehouseTypeCode() {
        return warehouseTypeCode;
    }

    public void setWarehouseTypeCode(Integer warehouseTypeCode) {
        this.warehouseTypeCode = warehouseTypeCode;
    }

    public Integer getIsAllowPurchase() {
        return isAllowPurchase;
    }

    public void setIsAllowPurchase(Integer isAllowPurchase) {
        this.isAllowPurchase = isAllowPurchase;
    }

    public Integer getInventoryManageFlag() {
        return inventoryManageFlag;
    }

    public void setInventoryManageFlag(Integer inventoryManageFlag) {
        this.inventoryManageFlag = inventoryManageFlag;
    }

    public Integer getIsUserApprovedSupplier() {
        return isUserApprovedSupplier;
    }

    public void setIsUserApprovedSupplier(Integer isUserApprovedSupplier) {
        this.isUserApprovedSupplier = isUserApprovedSupplier;
    }

    public Integer getIsRequestAskQuote() {
        return isRequestAskQuote;
    }

    public void setIsRequestAskQuote(Integer isRequestAskQuote) {
        this.isRequestAskQuote = isRequestAskQuote;
    }

    public Integer getIsMergeParity() {
        return isMergeParity;
    }

    public void setIsMergeParity(Integer isMergeParity) {
        this.isMergeParity = isMergeParity;
    }

    public Integer getIsRequestBidding() {
        return isRequestBidding;
    }

    public void setIsRequestBidding(Integer isRequestBidding) {
        this.isRequestBidding = isRequestBidding;
    }

    public String getDefaultPurchaseUser() {
        return defaultPurchaseUser;
    }

    public void setDefaultPurchaseUser(String defaultPurchaseUser) {
        this.defaultPurchaseUser = defaultPurchaseUser == null ? null : defaultPurchaseUser.trim();
    }

    public String getDefaultCostItemCode() {
        return defaultCostItemCode;
    }

    public void setDefaultCostItemCode(String defaultCostItemCode) {
        this.defaultCostItemCode = defaultCostItemCode == null ? null : defaultCostItemCode.trim();
    }

    public BigDecimal getDefaultPurchasePrice() {
        return defaultPurchasePrice;
    }

    public void setDefaultPurchasePrice(BigDecimal defaultPurchasePrice) {
        this.defaultPurchasePrice = defaultPurchasePrice;
    }

    public Integer getIsRequestCheck() {
        return isRequestCheck;
    }

    public void setIsRequestCheck(Integer isRequestCheck) {
        this.isRequestCheck = isRequestCheck;
    }

    public Long getDefaultReceiveQuantityTolerance() {
        return defaultReceiveQuantityTolerance;
    }

    public void setDefaultReceiveQuantityTolerance(Long defaultReceiveQuantityTolerance) {
        this.defaultReceiveQuantityTolerance = defaultReceiveQuantityTolerance;
    }

    public Integer getIsAllowReplaceReceive() {
        return isAllowReplaceReceive;
    }

    public void setIsAllowReplaceReceive(Integer isAllowReplaceReceive) {
        this.isAllowReplaceReceive = isAllowReplaceReceive;
    }

    public Integer getIsAllowNotOrderedReceive() {
        return isAllowNotOrderedReceive;
    }

    public void setIsAllowNotOrderedReceive(Integer isAllowNotOrderedReceive) {
        this.isAllowNotOrderedReceive = isAllowNotOrderedReceive;
    }

    public String getDefaultReceiveWay() {
        return defaultReceiveWay;
    }

    public void setDefaultReceiveWay(String defaultReceiveWay) {
        this.defaultReceiveWay = defaultReceiveWay == null ? null : defaultReceiveWay.trim();
    }

    public String getDefaultReceiveWarehouseCode() {
        return defaultReceiveWarehouseCode;
    }

    public void setDefaultReceiveWarehouseCode(String defaultReceiveWarehouseCode) {
        this.defaultReceiveWarehouseCode = defaultReceiveWarehouseCode == null ? null : defaultReceiveWarehouseCode.trim();
    }

    public String getProduceFactory() {
        return produceFactory;
    }

    public void setProduceFactory(String produceFactory) {
        this.produceFactory = produceFactory == null ? null : produceFactory.trim();
    }

    public String getServiceFactory() {
        return serviceFactory;
    }

    public void setServiceFactory(String serviceFactory) {
        this.serviceFactory = serviceFactory == null ? null : serviceFactory.trim();
    }

    public String getAfterSaleFactory() {
        return afterSaleFactory;
    }

    public void setAfterSaleFactory(String afterSaleFactory) {
        this.afterSaleFactory = afterSaleFactory == null ? null : afterSaleFactory.trim();
    }

    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }

    public Long getBaseId() {
        return baseId;
    }

    public void setBaseId(Long baseId) {
        this.baseId = baseId;
    }
}