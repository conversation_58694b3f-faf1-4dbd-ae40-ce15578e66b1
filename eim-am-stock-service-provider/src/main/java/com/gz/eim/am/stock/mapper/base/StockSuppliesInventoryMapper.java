package com.gz.eim.am.stock.mapper.base;

import com.gz.eim.am.stock.entity.StockSuppliesInventory;
import com.gz.eim.am.stock.entity.StockSuppliesInventoryExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockSuppliesInventoryMapper {
    long countByExample(StockSuppliesInventoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockSuppliesInventory record);

    int insertSelective(StockSuppliesInventory record);

    List<StockSuppliesInventory> selectByExample(StockSuppliesInventoryExample example);

    StockSuppliesInventory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockSuppliesInventory record, @Param("example") StockSuppliesInventoryExample example);

    int updateByExample(@Param("record") StockSuppliesInventory record, @Param("example") StockSuppliesInventoryExample example);

    int updateByPrimaryKeySelective(StockSuppliesInventory record);

    int updateByPrimaryKey(StockSuppliesInventory record);
}
