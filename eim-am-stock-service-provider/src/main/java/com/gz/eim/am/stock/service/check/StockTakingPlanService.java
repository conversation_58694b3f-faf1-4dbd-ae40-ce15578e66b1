package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.check.StockTakingPlanQueryReqDTO;
import com.gz.eim.am.stock.dto.request.check.StockTakingPlanReqDTO;
import com.gz.eim.am.stock.entity.StockTakingPlan;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 10/30/20 5:36 下午
 * @description 盘点计划单接口
 */
public interface StockTakingPlanService {

  /**
   * 根据盘点计划单查询计划单
   * @param takingPlanNo
   * @return
   * @throws Exception
   */
  StockTakingPlan getByTakingPlanNo(String takingPlanNo)throws Exception;

  /**
   * 根据盘点计划单号集合查询计划单
   * @param takingPlanNoList
   * @return
   * @throws Exception
   */
  List<StockTakingPlan> getByTakingPlanNoList(List<String> takingPlanNoList)throws Exception;

    /**
     * 盘点计划单列表查询
     * @param stockTakingPlanQueryReqDTO 查询参数
     * @return
     * @throws Exception
     */
  ResponseData queryStockTakingPlan(StockTakingPlanQueryReqDTO stockTakingPlanQueryReqDTO)throws Exception;

    /**
     * 详情查询
     * @param bizId
     * @return
     * @throws Exception
     */
  ResponseData queryDetail(Long bizId)throws Exception;


    /**
     * 创建盘点计划单
     * @param stockTakingPlanReqDTO
     * @return
     * @throws Exception
     */
  ResponseData createTakingPlan(StockTakingPlanReqDTO stockTakingPlanReqDTO)throws Exception;

  /**
   * 获取盘点计划单表中所有的有效的计划单编码
   * @return
   */
  List<StockTakingPlan> getValidTakingPlanNoList(Integer billStatus);

  /**
   * 批量更新计划下为结束状态
   * @param takingPlanNoList
   * @return
   */
  Integer updateTakingPlanEnd(List<String> takingPlanNoList, JwtUser user);

  /**
   * 取消盘点计划
   * @param id
   * @return
   */
  ResponseData cancelTakingPlan(String takingPlanNo) throws Exception;
}
