package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockInventoryInSuppliesAsset {
    private Long id;

    private Long inSuppliesId;

    private String assetsCode;

    private String useUserDept;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public String getUseUserDept() {
        return useUserDept;
    }

    public void setUseUserDept(String useUserDept) {
        this.useUserDept = useUserDept;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInSuppliesId() {
        return inSuppliesId;
    }

    public void setInSuppliesId(Long inSuppliesId) {
        this.inSuppliesId = inSuppliesId;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}