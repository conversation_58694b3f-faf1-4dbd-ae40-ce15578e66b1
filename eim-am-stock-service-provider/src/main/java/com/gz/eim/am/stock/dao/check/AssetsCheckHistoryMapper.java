package com.gz.eim.am.stock.dao.check;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/11/25
 * @description
 */
public interface AssetsCheckHistoryMapper {

    /**
     * 将盘点的计划单和任务转移到历史表
     * @param takingPlanNo
     */
    void copyToHistotyTable(String takingPlanNo);

    /**
     * 将盘点数据从业务表删除
     * @param takingPlanNo
     */
    void deleteFromBusinessTable(String takingPlanNo);
}
