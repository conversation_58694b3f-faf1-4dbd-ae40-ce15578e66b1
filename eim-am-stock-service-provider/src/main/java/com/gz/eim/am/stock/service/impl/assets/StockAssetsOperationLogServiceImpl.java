package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsOperationLogMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsOperationLogSearchDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.assets.AssetsOperationLogDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockAssetsOperationLog;
import com.gz.eim.am.stock.entity.StockAssetsOperationLogExample;
import com.gz.eim.am.stock.entity.ambase.SysDept;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsOperationLogService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-12-11 PM 7:04
 */
@Service
public class StockAssetsOperationLogServiceImpl implements StockAssetsOperationLogService {
    /**
     * 最大值
     */
    private final static int MAX_COUNT = 10000;

    @Autowired
    private StockAssetsOperationLogMapper operationLogMapper;
    @Autowired
    private AssetsMapper assetsMapper;
    @Autowired
    private StockAssetsService assetsService;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockWarehouseService warehouseService;

    /**
     * 批量插入新增日志，可以考虑 insert into select
     */
    @Override
    public Integer insertMultipleOperationLog(final List<StockAssets> assetsList, AssetsEnum.operationType operationType) {
        Date currentDate = new Date();
        int count = 0;
        if (CollectionUtils.isNotEmpty (assetsList)) {
            final AssetsEnum.operationType optType =
                    operationType == null ? AssetsEnum.operationType.BUSINESS : operationType;
            final List<StockAssetsOperationLog> stockAssetsOperationLogLists = assetsList.stream ().map (assets -> {
                StockAssetsOperationLog operationLog = new StockAssetsOperationLog ();
                BeanUtils.copyProperties (assets, operationLog);
                operationLog.setOperationType (optType.getValue ());
                if (operationLog.getConditions() == null){
                    operationLog.setConditions(AssetsEnum.Conditions.NORMAL.getValue());
                }
                // 设置创建时间和更新时间为当前时间
                operationLog.setCreatedAt(currentDate);
                operationLog.setUpdatedAt(currentDate);
                return operationLog;
            }).collect (Collectors.toList ());


            //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
            if (stockAssetsOperationLogLists.size () > 0) {
                int toIndex = CommonConstant.MAX_INSERT_COUNT;
                for (int i = 0; i < stockAssetsOperationLogLists.size (); i += CommonConstant.MAX_INSERT_COUNT) {
                    if (i + CommonConstant.MAX_INSERT_COUNT > stockAssetsOperationLogLists.size ()) {
                        toIndex = stockAssetsOperationLogLists.size () - i;
                    }
                    List<StockAssetsOperationLog> newSubDetailList = stockAssetsOperationLogLists.subList (i, i + toIndex);
                    int insertCount = this.assetsMapper.insertMultipleOperationLog (newSubDetailList);
                    count = count + insertCount;
                }
            }
        }
        return count;
    }

    @Override
    public Integer insertMultipleOperationLogByAssetsIds(final List<Long> idList, AssetsEnum.operationType operationType) {
        return insertMultipleOperationLog (assetsService.selectAssets (idList, null), operationType);
    }

    @Override
    public ResponseData selectAssetsOperationLog(final AssetsOperationLogSearchDTO searchDTO) {
        final PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes ();
        //noPaging，Used for excel export.
        if (searchDTO.getNoPaging () != null && true == searchDTO.getNoPaging ()) {
            final Long count = this.getCount (searchDTO);
            if (count == null || count > MAX_COUNT) {
                pageRespDTO.setMessage (count == null ? "无数据导出" : "导出数量超过10000条");
                return pageRespDTO;
            }
            searchDTO.setPageSize (null);
            List<StockAssetsOperationLog> assetsList = this.getList (searchDTO);
            List<AssetsOperationLogDTO> assetsDTOList = modelToDTO (assetsList);
            assetsDTOList = settingRelationValues (assetsDTOList);
            pageRespDTO.setData (assetsDTOList);
            return pageRespDTO;
        }
        searchDTO.initPageDefaultParam ();
        searchDTO.setStartNum ((searchDTO.getPageNum () - 1) * searchDTO.getPageSize ());
        Long totalCount = this.getCount (searchDTO);
        pageRespDTO.setCount (totalCount);
        if (totalCount > 0) {
            final List<StockAssetsOperationLog> assetsList = this.getList (searchDTO);
            List<AssetsOperationLogDTO> assetsDTOList = modelToDTO (assetsList);
            assetsDTOList = settingRelationValues (assetsDTOList);
            pageRespDTO.setData (assetsDTOList);
        } else {
            pageRespDTO.setData (new ArrayList<> (1));
        }
        pageRespDTO.setPageSize (searchDTO.getPageSize ());
        pageRespDTO.setStartNum (searchDTO.getStartNum ());
        pageRespDTO.setPageNum (searchDTO.getPageNum ());
        return pageRespDTO;
    }


    private StockAssetsOperationLogExample settingExampleClause(final AssetsOperationLogSearchDTO searchDTO) {
        final StockAssetsOperationLogExample example = new StockAssetsOperationLogExample ();
        final StockAssetsOperationLogExample.Criteria criteria = example.createCriteria ();
        if (searchDTO.getAssetsId () == null) {
        } else {
            criteria.andAssetsIdEqualTo (searchDTO.getAssetsId ());
        }
        if (StringUtils.isNotBlank (searchDTO.getAssetsCode ())) {
            criteria.andAssetsCodeEqualTo (searchDTO.getAssetsCode ());
        }
        if (StringUtils.isNotBlank (searchDTO.getAssetsName ())) {
            criteria.andAssetsNameEqualTo (searchDTO.getAssetsName ());
        }
        if (StringUtils.isNotBlank (searchDTO.getAssetsKeeper ())) {
            criteria.andAssetsKeeperEqualTo (searchDTO.getAssetsKeeper ());
        }
        if (StringUtils.isNotBlank (searchDTO.getWarehouseCode ())) {
            criteria.andWarehouseCodeEqualTo (searchDTO.getWarehouseCode ());
        }
        if (null != searchDTO.getStatus ()) {
            criteria.andStatusEqualTo (searchDTO.getStatus ());
        }
        if (StringUtils.isNotBlank (searchDTO.getHolder ())) {
            criteria.andHolderEqualTo (searchDTO.getHolder ());
        }
        return example;
    }

    public List<StockAssetsOperationLog> getList(AssetsOperationLogSearchDTO searchDTO) {
        StockAssetsOperationLogExample example = settingExampleClause (searchDTO);
        example.setLimit (searchDTO.getPageSize ());
        example.setOffset (searchDTO.getStartNum ());
        String orderByClause = " id desc";
        if (StringUtils.isNotBlank (searchDTO.getSortColumns ())) {
            orderByClause = searchDTO.getSortColumns ();
        }
        example.setOrderByClause (orderByClause);
        return this.operationLogMapper.selectByExample (example);
    }

    public Long getCount(AssetsOperationLogSearchDTO assetsSearchDTO) {
        return this.operationLogMapper.countByExample (settingExampleClause (assetsSearchDTO));
    }

    @Override
    public List<AssetsOperationLogDTO> modelToDTO(final List<StockAssetsOperationLog> operationLogList) {
        if (CollectionUtils.isEmpty (operationLogList)) {
            return new ArrayList<> (1);
        }
        List<AssetsOperationLogDTO> dtoList = new ArrayList<> (operationLogList.size ());
        operationLogList.stream ().forEach (operationLog -> {
            dtoList.add (modelToDTO (operationLog));
        });
        return dtoList;
    }

    @Override
    public AssetsOperationLogDTO modelToDTO(final StockAssetsOperationLog operationLog) {
        AssetsOperationLogDTO operationLogDTO = new AssetsOperationLogDTO ();
        BeanUtils.copyProperties (operationLog, operationLogDTO);
        return operationLogDTO;
    }

    @Override
    public List<AssetsOperationLogDTO> settingRelationValues(final List<AssetsOperationLogDTO> logDTOList) {
        if (CollectionUtils.isEmpty (logDTOList)) {
            return new ArrayList<> (1);
        }
        final int collectionSize = logDTOList.size ();
        final List<String> deptIds = new ArrayList<> ();
        final List<String> userIds = new ArrayList<> ();
        final List<String> warehouseCode = new ArrayList<> (collectionSize);
        logDTOList.stream ().forEach (log -> {
            if (StringUtils.isNotBlank (log.getCostDept ())) {
                deptIds.add (log.getCostDept ());
            }
            if (StringUtils.isNotBlank (log.getNeedDept ())) {
                deptIds.add (log.getNeedDept ());
            }
            if (StringUtils.isNotBlank (log.getHolder ())) {
                userIds.add (log.getHolder ());
            }
            if (StringUtils.isNotBlank (log.getAssetsKeeper ())) {
                userIds.add (log.getAssetsKeeper ());
            }

            if (StringUtils.isNotBlank (log.getUpdatedBy ())) {
                userIds.add (log.getUpdatedBy ());
            }
            if (StringUtils.isNotBlank (log.getWarehouseCode ())) {
                warehouseCode.add (log.getWarehouseCode ());
            }
        });
        final List<SysDept> deptList = ambaseCommonService.selectDeptByIds (deptIds);
        final List<SysUser> userList = ambaseCommonService.selectUsersByIds (userIds);
        final Map<String, SysDept> deptMap = new HashMap<> (8);
        final Map<String, SysUser> userMap = new HashMap<> (8);
        if (!CollectionUtils.isEmpty (deptList)) {
            deptList.stream ().forEach (dept -> deptMap.put (dept.getDeptId (), dept));
        }
        if (!CollectionUtils.isEmpty (deptList)) {
            userList.stream ().forEach (user -> userMap.put (user.getEmpId (), user));
        }
        final Map<String, WarehouseRespDTO> warehouseMap = new HashMap<> (8);
        List<WarehouseRespDTO> warehouseRespDTOList = warehouseService.selectWarehouseDetailByCode (warehouseCode);
        if (!CollectionUtils.isEmpty (warehouseRespDTOList)) {
            warehouseRespDTOList.forEach (warehouse -> warehouseMap.put (warehouse.getCode (), warehouse));
        }
        final SysDept defaultDept = new SysDept ();
        final SysUser defaultUser = new SysUser ();
        logDTOList.stream ().forEach (logDTO -> {
            logDTO.setCostDeptName (deptMap.getOrDefault (logDTO.getCostDept (), defaultDept).getDeptName ());
            logDTO.setNeedDeptName (deptMap.getOrDefault (logDTO.getNeedDept (), defaultDept).getDeptName ());
            logDTO.setAssetsKeeperName (userMap.getOrDefault (logDTO.getAssetsKeeper (), defaultUser).getName ());
            if (StringUtils.isNotBlank (logDTO.getHolder ())) {
                if (null != userMap.getOrDefault (logDTO.getHolder (), defaultUser) && StringUtils.isNotBlank (userMap.getOrDefault (logDTO.getHolder (), defaultUser).getName ())) {
                    logDTO.setHolderName ( userMap.getOrDefault (logDTO.getHolder (), defaultUser).getName ());
                }
            }

            if (StringUtils.isNotBlank (logDTO.getUpdatedBy ())) {
                if(CommonConstant.SYSTEM.equals(logDTO.getUpdatedBy ())){
                    logDTO.setUpdatedByName(CommonConstant.SYSTEM_NAME);
                } else if (null != userMap.getOrDefault (logDTO.getUpdatedBy (), defaultUser) && StringUtils.isNotBlank (userMap.getOrDefault (logDTO.getUpdatedBy (), defaultUser).getName ())) {
                    logDTO.setUpdatedByName (userMap.getOrDefault (logDTO.getUpdatedBy (), defaultUser).getName ());
                }
            }

            logDTO.setStatusText (AssetsEnum.statusTypeMap.get (logDTO.getStatus ()));
            logDTO.setConditionsName(AssetsEnum.conditionsMap.get(logDTO.getConditions()));
            logDTO.setPurchaseTypeText (AssetsEnum.purchaseTypeEnumMap.get (logDTO.getPurchaseType ()));
            logDTO.setOperationTypeText (AssetsEnum.operationTypeMap.get (logDTO.getOperationType ()));
            WarehouseRespDTO respDTO = warehouseMap.get (logDTO.getWarehouseCode ());
            if (respDTO != null) {
                logDTO.setWarehouseName (respDTO.getName ());
                if (null != respDTO.getWarehouseBase ()) {
                    logDTO.setWarehouseAddress (respDTO.getWarehouseBase ().getAddress ());
                }
            }
        });
        return logDTOList;
    }

    @Override
    public AssetsOperationLogDTO settingRelationValues(final AssetsOperationLogDTO assetsDTO) {
        List<AssetsOperationLogDTO> dtoList = new ArrayList<> (1);
        dtoList.add (assetsDTO);
        List<AssetsOperationLogDTO> assetsDTOList = settingRelationValues (dtoList);
        return assetsDTOList.get (0);
    }

}
