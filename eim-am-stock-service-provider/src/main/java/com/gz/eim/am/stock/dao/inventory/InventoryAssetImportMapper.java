package com.gz.eim.am.stock.dao.inventory;

import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsScrapLineRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.InventoryAssetImportRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryAssetImport;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/13
 * @description:
 */
public interface InventoryAssetImportMapper {

    /**
     * 批量删除语句
     * @param idList
     * @return
     */
    Long batchDeleteStockAssetImport(List<Integer> idList);

    /**
     * 批量更新入库单资产导入数据
     * @param stockInventoryAssetImportList
     * @return
     */
    Long batchUpdateStockAssetImport(List<StockInventoryAssetImport> stockInventoryAssetImportList);


    /**
     * 批量插入入库单资产导入
     * @param stockInventoryAssetImportList
     * @return
     */
    Long batchInsertStockAssetImport(List<StockInventoryAssetImport> stockInventoryAssetImportList);

    /**
     * 查询计划入库单资产导入数量
     * @param inventoryAssetImportSearchReqDTO
     * @return
     */
    Long countInventoryAssetImport(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO);

    /**
     * 分页查询计划入库单
     * @param inventoryAssetImportSearchReqDTO
     * @return
     */
    List<InventoryAssetImportRespDTO> selectByParam(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO);

    /**
     * 查询重复的资产编码
     * @param inventoryAssetBatchCode
     * @return
     */
    List<String> selectRepeatAsset(List<String> inventoryAssetBatchCode);

    /**
     * 查询重复的sn
     * @param inventoryAssetBatchCode
     * @return
     */
    List<String> selectRepeatSn(List<String> inventoryAssetBatchCode);

    /**
     * 查询计划入库单资产导入资产编号数量
     * @param inventoryAssetImportSearchReqDTO
     * @return
     */
    Long assetCountInventoryAssetImport(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO);

    /**
     *根据参数删除
     * @param stocKInventoryAssetImport
     */
    int deleteByParam(StockInventoryAssetImport stocKInventoryAssetImport);


    /**
     *根据参数删除
     * @param inventoryAssetImportReqDTO
     */
    void deleteByParams(InventoryAssetImportReqDTO inventoryAssetImportReqDTO);

    /**
     * 查询批次号下资产的总净值
     * @param bindBatchCode
     * @return
     */
    BigDecimal selectAssetImportNetValue(@Param("bindBatchCode") String bindBatchCode);

    /**
     * 查询报废申请单缓存数据
     * @param bindBatchCode
     * @return
     */
    List<StockAssetsScrapLineRespDTO> getScrapLinesByBindCode(@Param("bindBatchCode") String bindBatchCode);
}
