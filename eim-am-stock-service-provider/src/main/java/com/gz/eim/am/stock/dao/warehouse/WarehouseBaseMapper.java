package com.gz.eim.am.stock.dao.warehouse;

import com.gz.eim.am.stock.dto.request.warehouse.WarehouseSearchReqDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseBaseResp;
import com.gz.eim.am.stock.entity.StockWarehouseBase;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-09 下午 6:24
 */
public interface WarehouseBaseMapper {

    /**
     * 仓库编码查询
     * @param warehouseCode
     * @return
     */
    StockWarehouseBase selectByWarehouseCode(String warehouseCode);

    /**
     * 仓库编码删除
     * @param warehouseCode
     * @return
     */
    int deleteByWarehouseCode(String warehouseCode);

    /**
     * 仓库详情查询
     * @param warehouseCode
     * @return
     */
    WarehouseBaseResp getWarehouseBaseRespByWarehouseCode(String warehouseCode);

    /**
     * 查询仓库配置
     * @param warehouseSearchReqDTO
     * @return
     */
    List<WarehouseBaseResp> getWarehouseBaseByEmpId(WarehouseSearchReqDTO warehouseSearchReqDTO);
}
