package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockDeliveryDetailSnHistory;
import com.gz.eim.am.stock.entity.StockDeliveryDetailSnHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockDeliveryDetailSnsHistoryMapper {
    long countByExample(StockDeliveryDetailSnHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockDeliveryDetailSnHistory record);

    int insertSelective(StockDeliveryDetailSnHistory record);

    List<StockDeliveryDetailSnHistory> selectByExample(StockDeliveryDetailSnHistoryExample example);

    StockDeliveryDetailSnHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockDeliveryDetailSnHistory record, @Param("example") StockDeliveryDetailSnHistoryExample example);

    int updateByExample(@Param("record") StockDeliveryDetailSnHistory record, @Param("example") StockDeliveryDetailSnHistoryExample example);

    int updateByPrimaryKeySelective(StockDeliveryDetailSnHistory record);

    int updateByPrimaryKey(StockDeliveryDetailSnHistory record);
}