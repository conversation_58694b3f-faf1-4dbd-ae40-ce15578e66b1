package com.gz.eim.am.stock.dao.allocate;

import com.gz.eim.am.stock.dto.request.allocate.AllocateImportSearchReqDTO;
import com.gz.eim.am.stock.dto.response.allocate.AllocateImportLineRespDTO;
import com.gz.eim.am.stock.dto.response.allocate.AllocateImportLineSumRespDTO;
import com.gz.eim.am.stock.dto.response.wfl.WflAllocateImportLineSumRespDTO;
import com.gz.eim.am.stock.entity.StockAllocateImportLine;
import com.gz.eim.am.stock.entity.StockDeliveryPlanHead;
import com.gz.eim.am.stock.entity.StockDeliveryPlanLine;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/12/6
 * @description:
 */
public interface AllocateImportLineMapper {

    /**
     * 批量插入礼品调拨导入行
     * @param stockAllocateImportLineList
     * @return
     */
    Integer batchInsertStockAllocateImportLine(@Param ("stockAllocateImportLineList") List<StockAllocateImportLine> stockAllocateImportLineList);

    /**
     * 根据headId获取礼品调拨导入行数量
     * @param headId
     * @return
     */
    Long countStockAllocateImportLineByHeadId(Long headId);

    /**
     * 根据headId获取调拨导入行
     * @param allocateImportSearchReqDTO
     * @return
     */
    List<AllocateImportLineRespDTO> selectAllocateImportLineByHeadId(AllocateImportSearchReqDTO allocateImportSearchReqDTO);

    /**
     * 根据headId获取礼品调拨导入行数量
     * @param allocateImportSearchReqDTO
     * @return
     */
    List<AllocateImportLineSumRespDTO> selectAllocateImportLineSumByHeadId(AllocateImportSearchReqDTO allocateImportSearchReqDTO);

    /**
     * 根据headI删除礼品调拨导入
     * @param headId
     */
    void deleteStockAllocateImportLineByHeadId(Long headId);

    /**
     * 根据参数获取礼品调拨导入行
     * @param stockAllocateImportLine
     * @return
     */
    List<StockAllocateImportLine>  selectAllocateImportLineBySelective(StockAllocateImportLine stockAllocateImportLine);

    /**
     * 根据headCode获取工作流显示的行
     * @param headCode
     * @return
     */
    List<WflAllocateImportLineSumRespDTO> selectWflAllocateImportLineSumRespDTOByHeadCode(String headCode);

    /**
     * 获取礼品调拨单导入总金额
     * @param headId
     * @return
     */
    BigDecimal selectAllocateImportLineSumPriceByHeadId(Long headId);

    /**
     * 获取计划调拨单头
     * @param headId
     * @return
     */
    List<StockDeliveryPlanHead> selectStockDeliveryPlanHeadListByHeadId(Long headId);

    /**
     * 获取计划调拨单行
     * @param outWarehouseCode
     * @param inWarehouseCode
     * @param headId
     * @return
     */
    List<StockDeliveryPlanLine> selectStockDeliveryPlanLineListByParam(@Param ("outWarehouseCode") String outWarehouseCode, @Param ("inWarehouseCode") String inWarehouseCode, @Param ("headId") Long headId);

    /**
     * 根据LineList获取调拨导入行
     * @param stockAllocateImportLineList
     * @return
     */
    List<AllocateImportLineRespDTO> selectLineRespDTOByList(@Param ("stockAllocateImportLineList") List<StockAllocateImportLine> stockAllocateImportLineList);

    /**
     * 根据list参数获取list
     * @param stockAllocateImportLineList
     * @return
     */
    List<StockAllocateImportLine> selectAllocateImportLineByStockAllocateImportLineList(List<StockAllocateImportLine> stockAllocateImportLineList);
}
