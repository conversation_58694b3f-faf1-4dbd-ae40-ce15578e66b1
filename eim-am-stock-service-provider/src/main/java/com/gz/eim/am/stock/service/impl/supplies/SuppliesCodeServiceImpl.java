package com.gz.eim.am.stock.service.impl.supplies;

import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockSuppliesQuantityMapper;
import com.gz.eim.am.stock.dao.base.StockSuppliesQuantityMonthEndMapper;
import com.gz.eim.am.stock.dao.manage.SuppliesQuantityMapper;
import com.gz.eim.am.stock.dao.manage.SuppliesQuantityMonthEndMapper;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.vo.SuppliesCodeRefreshImportExcel;
import com.gz.eim.am.stock.entity.vo.SuppliesCostProjectRefreshImportExcel;
import com.gz.eim.am.stock.mapper.base.StockSuppliesMapper;
import com.gz.eim.am.stock.mapper.base.StockSuppliesPurchaseMapper;
import com.gz.eim.am.stock.service.supplies.*;
import com.gz.eim.am.stock.util.common.ListUtil;
import com.gz.eim.am.stock.util.em.SuppliesEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021-09-08
 */
@Async
@Service
public class SuppliesCodeServiceImpl implements SuppliesCodeService {

    private final Logger log = LoggerFactory.getLogger(SuppliesCodeServiceImpl.class);
    @Autowired
    private StockSuppliesMapper stockSuppliesMapper;
    @Autowired
    private StockSuppliesPurchaseMapper stockSuppliesPurchaseMapper;
    @Autowired
    private StockSuppliesQuantityMapper stockSuppliesQuantityMapper;
    @Autowired
    private SuppliesQuantityMapper suppliesQuantityMapper;
    @Autowired
    private StockSuppliesQuantityMonthEndMapper stockSuppliesQuantityMonthEndMapper;
    @Autowired
    private SuppliesQuantityMonthEndMapper suppliesQuantityMonthEndMapper;
    private static Connection connection;

    @Override
    public void updateSuppliesCode(MultipartFile file, String userName, String passWord, String url) {

        log.info("SuppliesCodeServiceImpl.updateSuppliesCode 开始刷新物料编码");
        //1.校验参数
        if (StringUtils.isBlank(userName) || StringUtils.isBlank(passWord) || StringUtils.isBlank(url)) {
            log.error("参数不能为空 userName={},passWord={},url={}", userName, passWord, url);
            return;
        }
        StringBuffer returnInfo = new StringBuffer();

        try {

            List<SuppliesCodeRefreshImportExcel> suppliesCodeRefreshImportExcels = ExcelUtil.importExcel(file.getInputStream(), SuppliesCodeRefreshImportExcel.class);

            //初始化db
            try {
                initDB(userName, passWord, url);
            } catch (ClassNotFoundException e) {
                log.error("初始化DB失败", e);
            } catch (SQLException e) {
                log.error("初始化DB失败", e);
            }

            Map<String, String> suppliesCodeData = new HashMap<>(suppliesCodeRefreshImportExcels.size());
            for (SuppliesCodeRefreshImportExcel suppliesCodeRefreshImportExcel : suppliesCodeRefreshImportExcels) {
                if (!StringUtils.equalsIgnoreCase(suppliesCodeRefreshImportExcel.getSuppliesCode().trim(), suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim())) {
                    if (StringUtils.isBlank(suppliesCodeData.get(suppliesCodeRefreshImportExcel.getSuppliesCode()))) {
                        suppliesCodeData.put(suppliesCodeRefreshImportExcel.getSuppliesCode().trim(), suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim());
                    } else {
                        String refreshSuppliesCode = suppliesCodeData.get(suppliesCodeRefreshImportExcel.getSuppliesCode());
                        suppliesCodeData.put(suppliesCodeRefreshImportExcel.getSuppliesCode(), refreshSuppliesCode + "," + suppliesCodeRefreshImportExcel.getRefreshSuppliesCode());
                    }
                }
            }

            //获取数据库元数据
            DatabaseMetaData dbMetaData = null;
            ResultSet rs = null;
            try {
                dbMetaData = connection.getMetaData();
                rs = dbMetaData.getTables(null, null, null, new String[]{"TABLE"});
            } catch (SQLException e) {
                log.error("获取元数据失败", e);
            }

            while (rs.next()) {
                String table_name = rs.getString("TABLE_NAME");
//                String table_name = "stock_allocate_import_lines";

                //需要聚合数量，所以手动去跑最好
                if (StringUtils.equalsIgnoreCase(table_name, "stock_supplies_quantity")
                        || StringUtils.equalsIgnoreCase(table_name, "stock_supplies_quantity_month_end")
                        || StringUtils.equalsIgnoreCase(table_name, "stock_supplies")
                        || StringUtils.equalsIgnoreCase(table_name, "stock_supplies_purchase")
                        // 过滤掉所有的备份表
                        || table_name.contains("copy")
                        || table_name.contains("bak")){
                        continue;
                }
                String sql = "select * from " + table_name + " limit 1";
                PreparedStatement ps = connection.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ResultSet rsTable = ps.executeQuery();
                //stock库下的这两张表用的是code字段
                try {
                    String filed = "";
                    if (StringUtils.equalsIgnoreCase(table_name, "stock_supplies") || StringUtils.equalsIgnoreCase(table_name, "stock_supplies_operation_log")) {
                        rsTable.findColumn("code");
                        filed = "code";
                    } else {
                        rsTable.findColumn("supplies_code");
                        filed = "supplies_code";
                    }
                    log.info(table_name + "包含要刷新字段");


                    //执行次数
                    int executeCount = 0;
                    //更新条数
                    int updateCount = 0;

                    for (Map.Entry<String, String> entry : suppliesCodeData.entrySet()) {
                        String condition = "";
                        if (entry.getValue().contains(",")) {
                            String[] suppliesCodeArray = entry.getValue().split(",");
                            for (int i = 0; i < suppliesCodeArray.length; i++) {
                                if (i != 0) {
                                    condition += " or " + filed + " = '" + suppliesCodeArray[i] + "'";
                                } else {
                                    condition += filed + " = '" + suppliesCodeArray[i] + "'";
                                }
                            }
                        } else {
                            condition = filed + " = '" + entry.getValue() + "'";
                        }
                        //拼接sql更新
                        String updateSql = "update " + table_name + " set " + filed + "=? where " + condition;
                        ps = connection.prepareStatement(updateSql);
                        ps.setString(1, entry.getKey());
                        updateCount += ps.executeUpdate();
                        executeCount++;
                    }
                    returnInfo.append(table_name + "表执行了" + executeCount + "次,更新了" + updateCount + "条数据;");
                } catch (SQLException e) {
                    continue;
                }
            }

        } catch (IOException e) {
            log.error("IO异常", e);
        } catch (SQLException e) {
            log.error("SQL异常", e);
        } finally {

            try {
                connection.close();
                log.info("关闭" + userName + "库连接");
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        log.info("SuppliesCodeServiceImpl.updateSuppliesCode 结束刷新物料编码");
        log.info("执行结果： returnInfo={}", JSONObject.toJSONString(returnInfo));
    }


    private void initDB(String userName, String passWord, String url) throws ClassNotFoundException, SQLException {
        String driver = "com.mysql.jdbc.Driver";

        Class.forName(driver);
        connection = DriverManager.getConnection(url, userName, passWord);
        if (!connection.isClosed()) {
            log.info("数据库" + userName + "连接成功");
        }

    }
    /**
     * @param:
     * @description: 刷新物料基础表
     * @return:
     * @author: <EMAIL>
     * @date: 2021/9/17
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStockSupplies(MultipartFile file) {
        log.info("SuppliesCodeServiceImpl.updateStockSupplies 开始刷新物料编码");
        Map<String, List<String>> suppliesCodeData = null;
        try {
            suppliesCodeData = getMultipartFileData(file);
        } catch (Exception e) {
            log.error("解析文件数据失败", e);
            return;
        }
        // 更新集合
        List<StockSupplies> updateList = new ArrayList<>();
        // 插入集合
        List<StockSupplies> insertList = new ArrayList<>();
        // 获取所有的code编码一次查询数据库全部查出来
        List<String> codeList = new ArrayList<>(suppliesCodeData.size());
        suppliesCodeData.forEach((k,v) -> {
            codeList.add(k);
            v.forEach(s -> {
                codeList.add(s);
            });
        });
        // 查询数据库
        StockSuppliesExample stockSuppliesExample = new StockSuppliesExample();
        stockSuppliesExample.createCriteria().andCodeIn(codeList);
        List<StockSupplies> stockSuppliesList = stockSuppliesMapper.selectByExample(stockSuppliesExample);
        // 然后转化为Map
        Map<String, StockSupplies> stockSuppliesMap = stockSuppliesList.stream().collect(Collectors.toMap(StockSupplies::getCode, stockSupplies -> stockSupplies, (v1, v2) -> v2));

        if(CollectionUtils.isNotEmpty(stockSuppliesList)){
            for(Map.Entry<String, List<String>> entry : suppliesCodeData.entrySet()){
                String suppliesCode = entry.getKey();
                List<String> refreshSuppliesCodeList = entry.getValue();
                // 如果需要替换的物料为空，直接continue
                if(CollectionUtils.isEmpty(refreshSuppliesCodeList)){
                    continue;
                }
                // 判读是否存在替换物料，不存在就进行新增
                if(!stockSuppliesMap.containsKey(suppliesCode)){
                   // 从被替换物料中找出一个来，进行替换
                   for(String refreshSuppliesCode : refreshSuppliesCodeList){
                       if(stockSuppliesMap.containsKey(refreshSuppliesCode)){
                           StockSupplies oldStockSupplies = stockSuppliesMap.get(refreshSuppliesCode);
                           StockSupplies stockSupplies = new StockSupplies();
                           BeanUtils.copyProperties(oldStockSupplies, stockSupplies);
                           stockSupplies.setCode(suppliesCode);
                           stockSupplies.setCreatedAt(new Date());
                           stockSupplies.setCreatedBy("system");
                           stockSupplies.setUpdatedAt(new Date());
                           stockSupplies.setUpdatedBy("system");
                           // 添加新增物料
                           insertList.add(stockSupplies);
                           continue;
                       }
                   }
                }
                // 如果已经存在物料不需要进行替换，直接将被替换物料置为无效
                for(String refreshSuppliesCode : refreshSuppliesCodeList){
                    if(!stockSuppliesMap.containsKey(refreshSuppliesCode)){
                        continue;
                    }
                    StockSupplies stockSupplies = stockSuppliesMap.get(refreshSuppliesCode);
                    stockSupplies.setStatus(SuppliesEnum.Status.FORBID.getStatus());
                    stockSupplies.setUpdatedAt(new Date());
                    stockSupplies.setUpdatedBy("system");
                    // 添加需要更新的物料
                    updateList.add(stockSupplies);
                }
            }
        }
        // 新增物料
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("stock_supplies表准备插入" + insertList.size() + "条数据");
            // 新增物料
            int insertCount = stockSuppliesMapper.batchInsert(insertList);
            log.info("stock_supplies表插入" + insertCount + "条数据");
        }
        // 修改物料
        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("stock_supplies表准备更新" + updateList.size() + "条数据");
            int updateCount = stockSuppliesMapper.batchUpdate(updateList);
            log.info("stock_supplies" + (updateCount >= 1 ? "成功" : "失败"));
        }
    }

    /**
     * @param:
     * @description: 刷新物料采购配置表
     * @return:
     * @author: <EMAIL>
     * @date: 2021/9/17
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStockSuppliesPurchase(MultipartFile file) {
        log.info("SuppliesCodeServiceImpl.updateStockSuppliesPurchase 开始刷新物料编码");
        Map<String, List<String>> suppliesCodeData = null;
        try {
            suppliesCodeData = getMultipartFileData(file);
        } catch (Exception e) {
            log.error("解析文件数据失败", e);
            return;
        }
        // 更新集合
        List<StockSuppliesPurchase> updateList = new ArrayList<>();
        // 插入集合
        List<StockSuppliesPurchase> insertList = new ArrayList<>();
        // 获取所有的code编码一次查询数据库全部查出来
        List<String> codeList = new ArrayList<>(suppliesCodeData.size());
        suppliesCodeData.forEach((k,v) -> {
            codeList.add(k);
            v.forEach(s -> {
                codeList.add(s);
            });
        });
        // 查询数据库
        StockSuppliesPurchaseExample stockSuppliesPurchaseExample = new StockSuppliesPurchaseExample();
        stockSuppliesPurchaseExample.createCriteria().andSuppliesCodeIn(codeList);
        List<StockSuppliesPurchase> stockSuppliesPurchaseList = stockSuppliesPurchaseMapper.selectByExample(stockSuppliesPurchaseExample);
        // 然后转化为Map
        Map<String, StockSuppliesPurchase> stringStockSuppliesPurchaseMap = stockSuppliesPurchaseList.stream().collect(Collectors.toMap(StockSuppliesPurchase::getSuppliesCode, stockSuppliesPurchase -> stockSuppliesPurchase, (v1, v2) -> v2));

        if(CollectionUtils.isNotEmpty(stockSuppliesPurchaseList)){
            for(Map.Entry<String, List<String>> entry : suppliesCodeData.entrySet()){
                String suppliesCode = entry.getKey();
                List<String> refreshSuppliesCodeList = entry.getValue();
                // 如果需要替换的物料为空，直接continue
                if(CollectionUtils.isEmpty(refreshSuppliesCodeList)){
                    continue;
                }
                // 判读是否存在替换物料，不存在就进行新增
                if(!stringStockSuppliesPurchaseMap.containsKey(suppliesCode)){
                    // 从被替换物料中找出一个来，进行替换
                    for(String refreshSuppliesCode : refreshSuppliesCodeList){
                        if(stringStockSuppliesPurchaseMap.containsKey(refreshSuppliesCode)){
                            StockSuppliesPurchase oldStockSuppliesPurchase = stringStockSuppliesPurchaseMap.get(refreshSuppliesCode);
                            StockSuppliesPurchase stockSuppliesPurchase = new StockSuppliesPurchase();
                            BeanUtils.copyProperties(oldStockSuppliesPurchase, stockSuppliesPurchase);
                            stockSuppliesPurchase.setSuppliesCode(suppliesCode);
                            stockSuppliesPurchase.setCreatedAt(new Date());
                            stockSuppliesPurchase.setCreatedBy("system");
                            stockSuppliesPurchase.setUpdatedAt(new Date());
                            stockSuppliesPurchase.setUpdatedBy("system");
                            // 添加新增物料
                            insertList.add(stockSuppliesPurchase);
                            continue;
                        }
                    }
                }
                // 如果已经存在物料不需要进行替换，直接将被替换物料置为无效
                for(String refreshSuppliesCode : refreshSuppliesCodeList){
                    if(!stringStockSuppliesPurchaseMap.containsKey(refreshSuppliesCode)){
                        continue;
                    }
                    StockSuppliesPurchase stockSuppliesPurchase = stringStockSuppliesPurchaseMap.get(refreshSuppliesCode);
                    stockSuppliesPurchase.setIsDelFlag(1);
                    stockSuppliesPurchase.setUpdatedAt(new Date());
                    stockSuppliesPurchase.setUpdatedBy("system");
                    // 添加更新物料
                    updateList.add(stockSuppliesPurchase);
                }
            }
        }
        // 新增物料
        if (CollectionUtils.isNotEmpty(insertList)) {
            log.info("stock_supplies_purchase表准备插入" + insertList.size() + "条数据");
            // 新增物料
            int insertCount = stockSuppliesPurchaseMapper.batchInsert(insertList);
            log.info("stock_supplies_purchase表插入" + insertCount + "条数据");
        }
        // 修改物料
        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("stock_supplies_purchase表准备更新" + updateList.size() + "条数据");
            int updateCount = stockSuppliesPurchaseMapper.batchUpdate(updateList);
            log.info("stock_supplies_purchase表更新状态" + (updateCount >= 1 ? "成功" : "失败"));
        }
    }
    /**
     * @param:
     * @description: 获取上传文件中的数据
     * @return:
     * @author: <EMAIL>
     * @date: 2021/9/17
     */
    private Map<String, List<String>> getMultipartFileData(MultipartFile file) throws Exception{
        List<SuppliesCodeRefreshImportExcel> suppliesCodeRefreshImportExcels = null;

        suppliesCodeRefreshImportExcels = ExcelUtil.importExcel(file.getInputStream(), SuppliesCodeRefreshImportExcel.class);

        //查出需要所有刷新的物料
        Set<String> suppliesCodes = new HashSet<>();
//            //需要刷新的物料要删除掉，但是会有跟需要刷新的物料重复，这个用来做校验
//            Set<String> checkSuppliesCodes = new HashSet<>();
        //被替换要删除掉的物料
        Set<String> deleteSuppliesCodes = new HashSet<>();
        //需要处理的
        Map<String, List<String>> suppliesCodeData = new HashMap<>(suppliesCodeRefreshImportExcels.size());
        for (SuppliesCodeRefreshImportExcel suppliesCodeRefreshImportExcel : suppliesCodeRefreshImportExcels) {
            if (StringUtils.isBlank(suppliesCodeRefreshImportExcel.getSuppliesCode()) || StringUtils.isBlank(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode())) {
                continue;
            }

            suppliesCodes.add(suppliesCodeRefreshImportExcel.getSuppliesCode().trim());
            suppliesCodes.add(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim());


            if (!StringUtils.equalsIgnoreCase(suppliesCodeRefreshImportExcel.getSuppliesCode().trim(), suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim())) {
                if (CollectionUtils.isEmpty(suppliesCodeData.get(suppliesCodeRefreshImportExcel.getSuppliesCode().trim()))) {
                    suppliesCodeData.put(suppliesCodeRefreshImportExcel.getSuppliesCode().trim(), new ArrayList<>());
                }
                suppliesCodeData.get(suppliesCodeRefreshImportExcel.getSuppliesCode()).add(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode());
                //需要删除的物料
                deleteSuppliesCodes.add(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim());
            }
        }
        return suppliesCodeData;
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSuppliesCodeQuantity(MultipartFile file) {
        log.info("SuppliesCodeServiceImpl.updateSuppliesCodeQuantity 开始刷新物料编码数量");
        try {
            List<SuppliesCodeRefreshImportExcel> suppliesCodeRefreshImportExcels = ExcelUtil.importExcel(file.getInputStream(), SuppliesCodeRefreshImportExcel.class);
            //查出需要所有刷新的物料
            Set<String> suppliesCodes = new HashSet<>();
//            //需要刷新的物料要删除掉，但是会有跟需要刷新的物料重复，这个用来做校验
//            Set<String> checkSuppliesCodes = new HashSet<>();
            //被替换要删除掉的物料
            Set<String> deleteSuppliesCodes = new HashSet<>();
            //需要处理的
            Map<String, List<String>> suppliesCodeData = new HashMap<>(suppliesCodeRefreshImportExcels.size());
            for (SuppliesCodeRefreshImportExcel suppliesCodeRefreshImportExcel : suppliesCodeRefreshImportExcels) {
                if (StringUtils.isBlank(suppliesCodeRefreshImportExcel.getSuppliesCode()) || StringUtils.isBlank(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode())) {
                    continue;
                }

                suppliesCodes.add(suppliesCodeRefreshImportExcel.getSuppliesCode().trim());
                suppliesCodes.add(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim());


                if (!StringUtils.equalsIgnoreCase(suppliesCodeRefreshImportExcel.getSuppliesCode().trim(), suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim())) {
                    if (CollectionUtils.isEmpty(suppliesCodeData.get(suppliesCodeRefreshImportExcel.getSuppliesCode().trim()))) {
                        suppliesCodeData.put(suppliesCodeRefreshImportExcel.getSuppliesCode().trim(), new ArrayList<>());
                    }
                    suppliesCodeData.get(suppliesCodeRefreshImportExcel.getSuppliesCode()).add(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode());
                    //需要删除的物料
                    deleteSuppliesCodes.add(suppliesCodeRefreshImportExcel.getRefreshSuppliesCode().trim());
                }
            }

            Date currentDate = new Date();
            List<StockSuppliesQuantity> insertStockSuppliesQuantity = new ArrayList<>();
            List<StockSuppliesQuantity> updateStockSuppliesQuantity = new ArrayList<>();
            List<StockSuppliesQuantity> deleteStockSuppliesQuantity = new ArrayList<>();

            List<StockSuppliesQuantityMonthEnd> insertStockSuppliesQuantityMonthEnd = new ArrayList<>();
            List<StockSuppliesQuantityMonthEnd> updateStockSuppliesQuantityMonthEnd = new ArrayList<>();
            List<StockSuppliesQuantityMonthEnd> deleteStockSuppliesQuantityMonthEnd = new ArrayList<>();

            //做更新操作
            for (Map.Entry<String, List<String>> entry : suppliesCodeData.entrySet()) {
                List<String> querySuppliesCode = new ArrayList<>();
                querySuppliesCode.add(entry.getKey());
                querySuppliesCode.addAll(entry.getValue());

                //库存表的逻辑操作
                StockSuppliesQuantityExample stockSuppliesQuantityExample = new StockSuppliesQuantityExample();
                StockSuppliesQuantityExample.Criteria stockSuppliesQuantityCriteria = stockSuppliesQuantityExample.createCriteria();
                stockSuppliesQuantityCriteria.andSuppliesCodeIn(querySuppliesCode);
                List<StockSuppliesQuantity> stockSuppliesQuantityList = stockSuppliesQuantityMapper.selectByExample(stockSuppliesQuantityExample);
                if (CollectionUtils.isNotEmpty(stockSuppliesQuantityList) && !(stockSuppliesQuantityList.size() == 1 && StringUtils.equalsIgnoreCase(stockSuppliesQuantityList.get(0).getSuppliesCode(), entry.getKey()))) {
                    //stock_supplies_quantity 表的替换逻辑
                    operationSuppliesQuantity(stockSuppliesQuantityList, entry.getKey(), currentDate, insertStockSuppliesQuantity, updateStockSuppliesQuantity, deleteStockSuppliesQuantity);
                }

                //库存月结表逻辑
                StockSuppliesQuantityMonthEndExample stockSuppliesQuantityMonthEndExample = new StockSuppliesQuantityMonthEndExample();
                StockSuppliesQuantityMonthEndExample.Criteria stockSuppliesQuantityMonthEndCriteria = stockSuppliesQuantityMonthEndExample.createCriteria();
                stockSuppliesQuantityMonthEndCriteria.andSuppliesCodeIn(querySuppliesCode);
                List<StockSuppliesQuantityMonthEnd> stockSuppliesQuantityMonthEndList = stockSuppliesQuantityMonthEndMapper.selectByExample(stockSuppliesQuantityMonthEndExample);
                if (CollectionUtils.isNotEmpty(stockSuppliesQuantityMonthEndList) && !(stockSuppliesQuantityMonthEndList.size() == 1 && StringUtils.equalsIgnoreCase(stockSuppliesQuantityMonthEndList.get(0).getSuppliesCode(), entry.getKey()))) {
                    //stock_supplies_quantity_month_end 表替换逻辑
                    operationSuppliesQuantityMonthEnd(stockSuppliesQuantityMonthEndList, entry.getKey(), currentDate, insertStockSuppliesQuantityMonthEnd, updateStockSuppliesQuantityMonthEnd, deleteStockSuppliesQuantityMonthEnd);
                }

                

            }
            Long countOldStockSuppliesQuantity = stockSuppliesQuantityMapper.countByExample(null);
            log.info("stock_supplies_quantity表的初始数量总数为" + countOldStockSuppliesQuantity +"条数据");
            if (CollectionUtils.isNotEmpty(insertStockSuppliesQuantity)) {
                log.info("stock_supplies_quantity表准备插入" + insertStockSuppliesQuantity.size() + "条数据");
                int insertCount = suppliesQuantityMapper.batchInsert(insertStockSuppliesQuantity);
                log.info("stock_supplies_quantity表插入" + insertCount + "条数据");
            }
            if (CollectionUtils.isNotEmpty(updateStockSuppliesQuantity)) {
                log.info("stock_supplies_quantity表准备更新" + updateStockSuppliesQuantity.size() + "条数据");
                int updateCount = suppliesQuantityMapper.batchUpdate(updateStockSuppliesQuantity);
                log.info("stock_supplies_quantity表更新" + updateCount + "条数据");
            }
            if (CollectionUtils.isNotEmpty(deleteStockSuppliesQuantity)) {
                log.info("stock_supplies_quantity表准备删除" + deleteStockSuppliesQuantity.size() + "条数据");
                int deleteCount = suppliesQuantityMapper.batchDelete(deleteStockSuppliesQuantity);
                log.info("stock_supplies_quantity表删除" + deleteCount + "条数据");
            }
            Long countNewStockSuppliesQuantity = stockSuppliesQuantityMapper.countByExample(null);
            log.info("stock_supplies_quantity表已经处理完成，处理完成后的总数量为" + countNewStockSuppliesQuantity +"条数据");

            Long countOldStockSuppliesQuantityMonthEnd = stockSuppliesQuantityMonthEndMapper.countByExample(null);
            log.info("stock_supplies_quantity_month_end表的初始数量为" + countOldStockSuppliesQuantityMonthEnd +"条数据");
            if (CollectionUtils.isNotEmpty(insertStockSuppliesQuantityMonthEnd)) {
                log.info("stock_supplies_quantity_month_end表准备插入" + insertStockSuppliesQuantityMonthEnd.size() + "条数据");
                // 分批次更新数据每次1000条
                List<List<StockSuppliesQuantityMonthEnd>> insertStockSuppliesQuantityMonthEndList = ListUtil.splitList(insertStockSuppliesQuantityMonthEnd, CommonConstant.MAX_INSERT_COUNT);
                insertStockSuppliesQuantityMonthEndList.forEach(stockSuppliesQuantityMonthEnd -> {
                    int insertCount = suppliesQuantityMonthEndMapper.batchInsert(stockSuppliesQuantityMonthEnd);
                    log.info("stock_supplies_quantity_month_end插入条数：" + insertCount);
                });
            }
            if (CollectionUtils.isNotEmpty(updateStockSuppliesQuantityMonthEnd)) {
                log.info("stock_supplies_quantity_month_end表准备更新" + updateStockSuppliesQuantityMonthEnd.size() + "条数据");
                // 分批次插入数据每次1000条
                List<List<StockSuppliesQuantityMonthEnd>> updateStockSuppliesQuantityMonthEndList = ListUtil.splitList(updateStockSuppliesQuantityMonthEnd, CommonConstant.MAX_UPDATE_COUNT);
                updateStockSuppliesQuantityMonthEndList.forEach(stockSuppliesQuantityMonthEnds -> {
                    int updateCount = suppliesQuantityMonthEndMapper.batchUpdate(stockSuppliesQuantityMonthEnds);
                    log.info("stock_supplies_quantity_month_end表更新状态" + (updateCount >= 1 ? "成功" : "失败"));
                    log.info("stock_supplies_quantity_month_end表更新" + stockSuppliesQuantityMonthEnds.size() + "条数据");
                });
            }
            if (CollectionUtils.isNotEmpty(deleteStockSuppliesQuantityMonthEnd)) {
                log.info("stock_supplies_quantity_month_end表准备删除" + deleteStockSuppliesQuantityMonthEnd.size() + "条数据");
                // 分批次删除数据每次1000条
                List<List<StockSuppliesQuantityMonthEnd>> deleteStockSuppliesQuantityMonthEndList = ListUtil.splitList(deleteStockSuppliesQuantityMonthEnd, CommonConstant.MAX_DELETE_COUNT);
                deleteStockSuppliesQuantityMonthEndList.forEach(stockSuppliesQuantityMonthEnd -> {
                    int deleteCount = suppliesQuantityMonthEndMapper.batchDelete(stockSuppliesQuantityMonthEnd);
                    log.info("stock_supplies_quantity_month_end删除条数：" + deleteCount);
                });
            }
            Long countNewStockSuppliesQuantityMonthEnd = stockSuppliesQuantityMonthEndMapper.countByExample(null);
            log.info("stock_supplies_quantity_month_end表已经处理完成，处理完成后的总数量为" + countNewStockSuppliesQuantityMonthEnd +"条数据");

        } catch (IOException e) {
            log.error("刷新库存失败:", e);
            e.printStackTrace();
        }
        log.info("SuppliesCodeServiceImpl.updateSuppliesCodeQuantity 结束刷新物料编码数量");
        log.info("刷新库存成功!");
    }

    @Override
    public void updateSuppliesCostProject(MultipartFile file, String userName, String passWord, String url) {
        log.info("SuppliesCodeServiceImpl.updateSuppliesCostProject 开始刷新物料费用项");
        //1.校验参数
        if (StringUtils.isBlank(userName) || StringUtils.isBlank(passWord) || StringUtils.isBlank(url)) {
            log.error("参数不能为空 userName={},passWord={},url={}", userName, passWord, url);
            return;
        }
        StringBuffer returnInfo = new StringBuffer();

        try {

            List<SuppliesCostProjectRefreshImportExcel> suppliesCodeRefreshImportExcelList = ExcelUtil.importExcel(file.getInputStream(), SuppliesCostProjectRefreshImportExcel.class);

            //初始化db
            try {
                initDB(userName, passWord, url);
            } catch (ClassNotFoundException e) {
                log.error("初始化DB失败", e);
            } catch (SQLException e) {
                log.error("初始化DB失败", e);
            }

            Map<String, String> suppliesCostProjectData = new HashMap<>(suppliesCodeRefreshImportExcelList.size());
            for (SuppliesCostProjectRefreshImportExcel suppliesCostProjectRefreshImportExcel : suppliesCodeRefreshImportExcelList) {
                if (StringUtils.isBlank(suppliesCostProjectData.get(suppliesCostProjectRefreshImportExcel.getCostProject()))) {
                    suppliesCostProjectData.put(suppliesCostProjectRefreshImportExcel.getCostProject().trim(), suppliesCostProjectRefreshImportExcel.getSuppliesCode().trim());
                } else {
                    String refreshSuppliesCode = suppliesCostProjectData.get(suppliesCostProjectRefreshImportExcel.getCostProject().trim());
                    suppliesCostProjectData.put(suppliesCostProjectRefreshImportExcel.getCostProject().trim(), refreshSuppliesCode + "," + suppliesCostProjectRefreshImportExcel.getSuppliesCode().trim());
                }
            }

            //获取数据库元数据
            DatabaseMetaData dbMetaData = null;
            ResultSet rs = null;
            try {
                dbMetaData = connection.getMetaData();
                rs = dbMetaData.getTables(null, null, null, new String[]{"TABLE"});
            } catch (SQLException e) {
                log.error("获取元数据失败", e);
            }

            while (rs.next()) {
                String table_name = rs.getString("TABLE_NAME");
                // 过滤掉所有的备份表
                if (table_name.contains("copy") || table_name.contains("bak")){
                    continue;
                }
                String sql = "select * from " + table_name + " limit 1";
                PreparedStatement ps = connection.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                ResultSet rsTable = ps.executeQuery();

                try {
                    String conditionFiled = "";
                    String valueFiled = " ";
                    if(StringUtils.equalsIgnoreCase(userName,"stock")){

                        if(StringUtils.equalsIgnoreCase(table_name,"stock_supplies")){
                            rsTable.findColumn("cost_item_code");
                            valueFiled = "cost_item_code";
                            rsTable.findColumn("code");
                            conditionFiled = "code";
                        }else if (StringUtils.equalsIgnoreCase(table_name,"stock_supplies_purchase")){
                            rsTable.findColumn("default_cost_item_code");
                            valueFiled = "default_cost_item_code";
                            rsTable.findColumn("supplies_code");
                            conditionFiled = "supplies_code";
                        }else{
                            rsTable.findColumn("cost_item_code");
                            valueFiled = "cost_item_code";
                            rsTable.findColumn("supplies_code");
                            conditionFiled = "supplies_code";
                        }
                    }else {
                        rsTable.findColumn("cost_project");
                        rsTable.findColumn("supplies_code");
                        conditionFiled = "supplies_code";
                        valueFiled = "cost_project";
                    }


                    log.info(table_name + "包含要刷新字段");

                    //执行次数
                    int executeCount = 0;
                    //更新条数
                    int updateCount = 0;

                    for (Map.Entry<String, String> entry : suppliesCostProjectData.entrySet()) {
                        String condition = "";
                        if (entry.getValue().contains(",")) {
                            String[] suppliesCodeArray = entry.getValue().split(",");
                            for (int i = 0; i < suppliesCodeArray.length; i++) {
                                if (i != 0) {
                                    condition += " or " + conditionFiled + " = '" + suppliesCodeArray[i] + "'";
                                } else {
                                    condition += conditionFiled + " = '" + suppliesCodeArray[i] + "'";
                                }
                            }
                        } else {
                            condition = conditionFiled + " = '" + entry.getValue() + "'";
                        }

                        //拼接sql更新
                        String updateSql = "update " + table_name + " set " + valueFiled + "=? where " + condition;
                        ps = connection.prepareStatement(updateSql);
                        ps.setString(1, entry.getKey());
                        updateCount += ps.executeUpdate();
                        executeCount++;
                    }
                    returnInfo.append(table_name + "表执行了" + executeCount + "次,更新了" + updateCount + "条数据;");
                } catch (SQLException e) {
                    continue;
                }
            }

        } catch (IOException e) {
            log.error("IO异常", e);
        } catch (SQLException e) {
            log.error("SQL异常", e);
        } finally {

            try {
                connection.close();
                log.info("关闭" + userName + "库连接");
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        log.info("SuppliesCodeServiceImpl.updateSuppliesCostProject 结束刷新物料费用项");
        log.info("执行结果： returnInfo={}", JSONObject.toJSONString(returnInfo));

    }

    private void operationSuppliesQuantity(List<StockSuppliesQuantity> stockSuppliesQuantityList, String suppliesCode, Date currentDate, List<StockSuppliesQuantity> insertStockSuppliesQuantity,
                                           List<StockSuppliesQuantity> updateStockSuppliesQuantity, List<StockSuppliesQuantity> deleteStockSuppliesQuantity) {
        //找到同一仓库+物料+状态纬度去刷新
        Map<String, List<StockSuppliesQuantity>> warehouseSuppliesStatusMap = stockSuppliesQuantityList.stream().collect(Collectors.groupingBy(stockSuppliesQuantity -> stockSuppliesQuantity.getWarehouseCode() + stockSuppliesQuantity.getStatus()));
        for (Map.Entry<String, List<StockSuppliesQuantity>> entryData : warehouseSuppliesStatusMap.entrySet()) {
            BigDecimal quantityTotal = BigDecimal.ZERO;
            BigDecimal reservedQuantityTotal = BigDecimal.ZERO;
            Set<String> existSuppliesCode = new HashSet<>(entryData.getValue().size());
            for (StockSuppliesQuantity stockSuppliesQuantity : entryData.getValue()) {
                quantityTotal = quantityTotal.add(stockSuppliesQuantity.getQuantity());
                reservedQuantityTotal = reservedQuantityTotal.add(stockSuppliesQuantity.getReservedQuantity());
                existSuppliesCode.add(stockSuppliesQuantity.getSuppliesCode());
            }

//                    BigDecimal sumTotal = entryData.getValue().stream().map(StockSuppliesQuantity::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    List<String> existSuppliesCode = entryData.getValue().stream().map(StockSuppliesQuantity::getSuppliesCode).collect(Collectors.toList());

            //如果查询出被替换的物料不包含要替换成的物料，直接插入
            if (!existSuppliesCode.contains(suppliesCode)) {
                StockSuppliesQuantity stockSuppliesQuantity = new StockSuppliesQuantity();
                stockSuppliesQuantity.setWarehouseCode(entryData.getValue().get(0).getWarehouseCode());
                stockSuppliesQuantity.setSuppliesCode(suppliesCode);
                stockSuppliesQuantity.setQuantity(quantityTotal);
                stockSuppliesQuantity.setReservedQuantity(reservedQuantityTotal);
                stockSuppliesQuantity.setStatus(entryData.getValue().get(0).getStatus());
                stockSuppliesQuantity.setIsForst(entryData.getValue().get(0).getIsForst());
                stockSuppliesQuantity.setCreatedBy(entryData.getValue().get(0).getCreatedBy());
                stockSuppliesQuantity.setCreatedAt(currentDate);
                stockSuppliesQuantity.setUpdatedBy(entryData.getValue().get(0).getUpdatedBy());
                stockSuppliesQuantity.setUpdatedAt(currentDate);
                insertStockSuppliesQuantity.add(stockSuppliesQuantity);
                deleteStockSuppliesQuantity.addAll(entryData.getValue());

            } else {

                for (StockSuppliesQuantity stockSuppliesQuantity : entryData.getValue()) {
                    if (StringUtils.equalsIgnoreCase(stockSuppliesQuantity.getSuppliesCode(), suppliesCode)) {
                        stockSuppliesQuantity.setQuantity(quantityTotal);
                        stockSuppliesQuantity.setReservedQuantity(reservedQuantityTotal);
                        stockSuppliesQuantity.setUpdatedAt(currentDate);
                        updateStockSuppliesQuantity.add(stockSuppliesQuantity);
                    } else {
                        deleteStockSuppliesQuantity.add(stockSuppliesQuantity);
                    }
                }
            }
        }
    }


    private void operationSuppliesQuantityMonthEnd(List<StockSuppliesQuantityMonthEnd> stockSuppliesQuantityMonthEndList, String suppliesCode, Date currentDate, List<StockSuppliesQuantityMonthEnd> insertStockSuppliesQuantityMonthEnd,
                                                   List<StockSuppliesQuantityMonthEnd> updateStockSuppliesQuantityMonthEnd, List<StockSuppliesQuantityMonthEnd> deleteStockSuppliesQuantityMonthEnd) {
        //找到同一仓库+物料+状态+年月纬度去刷新
        Map<String, List<StockSuppliesQuantityMonthEnd>> warehouseSuppliesStatusYearMonthMap = stockSuppliesQuantityMonthEndList.stream().collect(Collectors.groupingBy(stockSuppliesQuantityMonthEnd -> stockSuppliesQuantityMonthEnd.getWarehouseCode() + stockSuppliesQuantityMonthEnd.getStatus() + stockSuppliesQuantityMonthEnd.getYearMonthDate()));
        for (Map.Entry<String, List<StockSuppliesQuantityMonthEnd>> entryData : warehouseSuppliesStatusYearMonthMap.entrySet()) {
            BigDecimal quantityTotal = BigDecimal.ZERO;
            BigDecimal reservedQuantityTotal = BigDecimal.ZERO;
            Set<String> existSuppliesCode = new HashSet<>(entryData.getValue().size());
            for (StockSuppliesQuantityMonthEnd stockSuppliesQuantityMonthEnd : entryData.getValue()) {
                quantityTotal = quantityTotal.add(stockSuppliesQuantityMonthEnd.getQuantity());
                reservedQuantityTotal = reservedQuantityTotal.add(stockSuppliesQuantityMonthEnd.getReservedQuantity());
                existSuppliesCode.add(stockSuppliesQuantityMonthEnd.getSuppliesCode());
            }

            //如果查询出被替换的物料不包含要替换成的物料，直接插入
            if (!existSuppliesCode.contains(suppliesCode)) {
                StockSuppliesQuantityMonthEnd stockSuppliesQuantityMonthEnd = new StockSuppliesQuantityMonthEnd();
                stockSuppliesQuantityMonthEnd.setWarehouseCode(entryData.getValue().get(0).getWarehouseCode());
                stockSuppliesQuantityMonthEnd.setSuppliesCode(suppliesCode);
                stockSuppliesQuantityMonthEnd.setQuantity(quantityTotal);
                stockSuppliesQuantityMonthEnd.setReservedQuantity(reservedQuantityTotal);
                stockSuppliesQuantityMonthEnd.setStatus(entryData.getValue().get(0).getStatus());
                stockSuppliesQuantityMonthEnd.setIsForst(entryData.getValue().get(0).getIsForst());
                stockSuppliesQuantityMonthEnd.setYearMonthDate(entryData.getValue().get(0).getYearMonthDate());
                stockSuppliesQuantityMonthEnd.setCreatedBy(entryData.getValue().get(0).getCreatedBy());
                stockSuppliesQuantityMonthEnd.setCreatedAt(currentDate);
                stockSuppliesQuantityMonthEnd.setUpdatedBy(entryData.getValue().get(0).getUpdatedBy());
                stockSuppliesQuantityMonthEnd.setUpdatedAt(currentDate);
                insertStockSuppliesQuantityMonthEnd.add(stockSuppliesQuantityMonthEnd);
                deleteStockSuppliesQuantityMonthEnd.addAll(entryData.getValue());

            } else {

                for (StockSuppliesQuantityMonthEnd stockSuppliesQuantityMonthEnd : entryData.getValue()) {
                    if (StringUtils.equalsIgnoreCase(stockSuppliesQuantityMonthEnd.getSuppliesCode(), suppliesCode)) {
                        stockSuppliesQuantityMonthEnd.setQuantity(quantityTotal);
                        stockSuppliesQuantityMonthEnd.setReservedQuantity(reservedQuantityTotal);
                        stockSuppliesQuantityMonthEnd.setUpdatedAt(currentDate);
                        updateStockSuppliesQuantityMonthEnd.add(stockSuppliesQuantityMonthEnd);
                    } else {
                        deleteStockSuppliesQuantityMonthEnd.add(stockSuppliesQuantityMonthEnd);
                    }
                }
            }

        }

    }


}

