package com.gz.eim.am.stock.web.inventory.plan;

import com.alibaba.druid.support.json.JSONUtils;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.JsonUtil;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.inventory.plan.StockPlanAssetPurchaseApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportBatchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.*;
import com.gz.eim.am.stock.service.inventory.plan.StockPlanAssetPurchaseService;

import com.netflix.discovery.converters.wrappers.CodecWrappers;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: lishuyang
 * @date: 2019/12/19
 * @description: 资产采购入库Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/planAssetPurchase")
public class StockPlanAssetPurchaseController implements StockPlanAssetPurchaseApi {

    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockPlanAssetPurchaseService stockPlanAssetPurchaseService;

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_IN)
    public ResponseData savePlanAssetPurchase(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info("/api/am/stock/planAssetPurchase/save {}", inventoryInPlanHeadReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanAssetPurchaseService.savePlanAssetPurchase(inventoryInPlanHeadReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("保存计划资产采购入库单错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_IN)
    public ResponseData selectPlanAssetPurchase(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO) {
        log.info("/api/am/stock/planAssetPurchase/search {}", inventoryInPlanSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanAssetPurchaseService.selectPlanAssetPurchase(inventoryInPlanSearchReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划资产采购入库单分页查询错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanAssetPurchaseById(Long inventoryInPlanHeadId) {
        log.info("/api/am/stock/planAssetPurchase/search/{}", inventoryInPlanHeadId.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanAssetPurchaseService.selectPlanAssetPurchaseById(inventoryInPlanHeadId, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划资产采购入库单详情错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanAssetPurchaseLineAssetByLineId(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO) {
        log.info("/api/am/stock/planAssetPurchase/line/search {}", inventoryInPlanLineReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanAssetPurchaseService.selectPlanAssetPurchaseLineAssetByLineId(inventoryInPlanLineReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划资产采购入库单行关联资产编号详情查询错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_IN)
    public ResponseData assetPlanPurchaseInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info("/api/am/stock/planAssetPurchase/inBound {}", inventoryInPlanHeadReqDTO.toString());
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_REMAND_BOUND + inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId ();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                JwtUser user = SecurityUtil.getJwtUser();
                log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
                res = this.stockPlanAssetPurchaseService.assetPlanPurchaseInBound(inventoryInPlanHeadReqDTO, user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        }catch (ServiceUncheckedException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("计划资产采购入库单入库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData assetPlanAssetFileImport(MultipartFile file) {
        log.info("/api/am/stock/planAssetPurchase/import");
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanAssetPurchaseService.assetPlanAssetFileImport(file, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("资产采购文件导入", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectAssetPlanAssetFileImportByBatchCode(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO) {
        log.info("/api/am/stock/planAssetPurchase/import/search {}", inventoryAssetImportSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanAssetPurchaseService.selectAssetPlanAssetFileImportByBatchCode(inventoryAssetImportSearchReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("资产采购文件导入", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData assetPurchaseInitialization(Integer batchId) {
        log.info("/api/am/stock/planAssetPurchase/initialization {}", batchId);
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_REMAND_BOUND + batchId;
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                JwtUser user = SecurityUtil.getJwtUser();
                log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
                res = this.stockPlanAssetPurchaseService.assetPurchaseInitialization(batchId,user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        }catch (ServiceUncheckedException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("资产采购文件导入", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_IN)
    public ResponseData cancelPlanAssetPurchaseById(Long inventoryInPlanHeadId) {
        log.info("/api/am/stock/planAssetPurchase/cancel/{}", inventoryInPlanHeadId.toString());
        ResponseData res = null;
        try {
            res = this.stockPlanAssetPurchaseService.cancelPlanAssetPurchaseById(inventoryInPlanHeadId);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划资产采购入库单取消错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData savePlanInventoryPurchase(List<PurchaseInventoryInHeadDTO> purchaseInventoryInHeadDTO) {
        log.info("/api/am/stock/planAssetPurchase/purchase {}", purchaseInventoryInHeadDTO);
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_PURCHASE_BOUND + purchaseInventoryInHeadDTO.get(0).getBizNo();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                JwtUser user = SecurityUtil.getJwtUser();
                log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
                res = this.stockPlanAssetPurchaseService.savePlanInventoryPurchase(purchaseInventoryInHeadDTO,user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        }catch (ServiceUncheckedException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("采购系统入库单据对接错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData purchaseAssetsAccountSync(List<PurchaseAssetsSyncBillReqDTO> purchaseAssetsSyncBillReqDTOs) {
        log.info("/api/am/stock/planAssetPurchase/accountant {}", purchaseAssetsSyncBillReqDTOs);
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_PURCHASE_BOUND + purchaseAssetsSyncBillReqDTOs.get(0).getAccountantCode();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                JwtUser user = SecurityUtil.getJwtUser();
                log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
                res = this.stockPlanAssetPurchaseService.purchaseAssetsAccountSync(purchaseAssetsSyncBillReqDTOs,user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        }catch (ServiceUncheckedException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("采购同步资产结算信息错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

}
