package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetCodeOperate;
import com.gz.eim.am.stock.entity.StockAssetCodeOperateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetCodeOperateMapper {
    long countByExample(StockAssetCodeOperateExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetCodeOperate record);

    int insertSelective(StockAssetCodeOperate record);

    List<StockAssetCodeOperate> selectByExample(StockAssetCodeOperateExample example);

    StockAssetCodeOperate selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetCodeOperate record, @Param("example") StockAssetCodeOperateExample example);

    int updateByExample(@Param("record") StockAssetCodeOperate record, @Param("example") StockAssetCodeOperateExample example);

    int updateByPrimaryKeySelective(StockAssetCodeOperate record);

    int updateByPrimaryKey(StockAssetCodeOperate record);
}