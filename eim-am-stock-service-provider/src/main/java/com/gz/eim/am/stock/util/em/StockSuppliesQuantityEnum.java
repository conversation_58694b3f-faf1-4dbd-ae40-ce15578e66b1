package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: ya<PERSON><PERSON><PERSON>
 * @date: 2021/06/21
 * @description 状态
 */
public class StockSuppliesQuantityEnum {
    /**
     *  状态
     */
    public enum status {

        /**
         * 在库
         */
        IN_STOCK("In Stock", "在库"),
        /**
         * 在途
         */
        IN_TRANSIT("In Transit", "实体"),
        /**
         * 质检中
         */
        IN_CHECK("In Check", "质检中"),
        /**
         * 冻结中
         */
        IN_FROST("In Frost", "冻结中"),

        ;

        status(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private String value;
        private String desc;

        public String getValue() {
            return this.value;
        }

        public void setValue(final String value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<String, String> statusMap =
            Arrays.stream(StockSuppliesQuantityEnum.status.values()).collect(
                    Collectors.toMap(StockSuppliesQuantityEnum.status::getValue, StockSuppliesQuantityEnum.status::getDesc));



}
