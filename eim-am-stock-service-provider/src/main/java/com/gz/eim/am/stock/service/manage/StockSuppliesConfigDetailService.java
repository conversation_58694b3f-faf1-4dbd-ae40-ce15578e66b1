package com.gz.eim.am.stock.service.manage;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO;
import com.gz.eim.am.stock.entity.StockSuppliesConfigDetail;
import com.gz.eim.am.stock.entity.vo.download.ExportStockConfigDetailEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 6:37
 */
public interface StockSuppliesConfigDetailService {

    /**
     * 插入单条记录
     * @param detail
     * @return
     */
    int addDetail(StockSuppliesConfigDetail detail);

    /**
     * 查询库存详情
     * @param warehouseCode
     * @param suppliesCode
     * @param version
     * @param batchNo
     * @param snNo
     * @return
     */
    StockSuppliesConfigDetail selectUseConfigDetail(String warehouseCode, String suppliesCode, String version, String batchNo, String snNo);

    /**
     * 增加库存明细数量
     * @param detail
     * @return
     */
    int addConfigDetailNumber(StockSuppliesConfigDetail detail);

    /**
     * 根据参数查询
     * @param warehouseCode
     * @param suppliesCode
     * @param version
     * @param batchNo
     * @param snNo
     * @return
     */
    List<StockSuppliesConfigDetail> selectByParam(String warehouseCode, String suppliesCode, String version, String batchNo, String snNo);

    /**
     * 库存明细查询
     * @param reqDTO
     * @return
     */
    List<SuppliesConfigRespDTO> getConfigDetail(StockSuppliesQuantityReqDTO reqDTO);

    /**
     * 查看可用的批次/序列号
     * @param reqDTO
     * @param user
     * @return
     */
    ResponseData queryUsableManageDetail(StockSuppliesQuantityReqDTO reqDTO, JwtUser user);

    /**
     * 查询库存详情
     * @param dto
     * @param user
     * @return
     */
    StockSuppliesConfigDetail getDetailByManagerNo(StockSuppliesQuantityReqDTO dto, JwtUser user);

    /**
     * 库存明细升级
     * @param oldVersion
     * @param newVersion
     * @return
     */
    int suppliesDetailStoreArchive(String oldVersion,String newVersion);


    /**
     * 批量查询库存详情
     * @param warehouseCode
     * @param suppliesCode
     * @param version
     * @param batchNoList
     * @param snNoList
     * @return
     */
    List<StockSuppliesConfigDetail> selectUseConfigDetailByBatch(String warehouseCode, String suppliesCode, String version, List<String> batchNoList, List<String> snNoList);

    /**
     * 库存明细分页查询
     * @param dto
     * @param user
     * @return
     */
    ResponseData getSuppliesConfigDetailByPage(StockSuppliesQuantityReqDTO dto, JwtUser user);

    /**
     * 库存明细导出查询
     * @param dto
     * @param user
     * @return
     */
    List<ExportStockConfigDetailEntity> exportSuppliesConfigDetail(StockSuppliesQuantityReqDTO dto, JwtUser user);

    /**
     * 发送库存整理消息
     * @return
     */
    ResponseData sendSuppliesConfigArrange();

    /**
     * 根据仓库和序列号查询物料
     * @param reqDTO
     * @return
     */
    List<SuppliesConfigRespDTO> getSuppliesBySnno(StockSuppliesQuantityReqDTO reqDTO);

    /**
     * 批量查询库存详情
     * @param warehouseCode
     * @param suppliesCode
     * @param version
     * @param snNoList
     * @return
     */
    List<String> selectExistsSnsByBatch(String warehouseCode, String suppliesCode, String version, List<String> snNoList);
}
