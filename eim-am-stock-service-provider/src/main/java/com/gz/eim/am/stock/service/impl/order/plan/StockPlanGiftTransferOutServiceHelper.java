package com.gz.eim.am.stock.service.impl.order.plan;

import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.order.DeliveryMapper;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanLineReqDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.service.manage.StockSuppliesQuantityService;
import com.gz.eim.am.stock.service.order.StockDeliveryDetailService;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.em.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 3/19/21 5:14 下午
 * @description
 */
@Component
public class StockPlanGiftTransferOutServiceHelper {


    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;

    @Autowired
    private StockSuppliesService stockSuppliesService;

    @Autowired
    private StockWarehouseBaseService stockWarehouseBaseService;
    @Autowired
    private DeliveryMapper deliveryMapper;
    @Autowired
    private StockDeliveryDetailService deliveryDetailService;
    @Autowired
    private StockSuppliesQuantityService stockSuppliesQuantityService;


    /**
     * 校验保存领用出库单参数
     */
    public String checkSaveParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) {
        if (null == deliveryPlanHeadReqDTO) {
            return "必填参数为空";
        }
        if (StringUtils.isBlank(deliveryPlanHeadReqDTO.getOutWarehouseCode())) {
            return "调出仓库不能为空";
        }
        if (null != deliveryPlanHeadReqDTO.getReasonCode() && DeliveryPlanHeadEnum.Reason.STAFF_STIMULATE.getCode().equals(deliveryPlanHeadReqDTO.getReasonCode())) {
            if (StringUtils.isBlank(deliveryPlanHeadReqDTO.getUseUser())) {
                return "领用人不能为空;";
            }
        }

        //查询仓库权限
        final List<String> wc = stockRoleKeeperService.selectKeepWarehouseByParam(user.getEmployeeCode(), null, null);
        if (CollectionUtils.isEmpty(wc)) {
            return "无操作仓库的权限";
        }
        if (!wc.get(0).equals(ManageRoleEnum.Type.ALL.getValue())) {
            if (!wc.contains(deliveryPlanHeadReqDTO.getOutWarehouseCode())) {
                return "无操作调出仓库的权限";
            }
        }
        //新建领用单详情可为空
        if (CollectionUtils.isEmpty(deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS())) {
            return null;
        }
        final StringBuilder sb = new StringBuilder();
        final Integer detailSize = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().size();
        final List<String> receiveAssets = new ArrayList<>(detailSize);
        for (int i = 0; i < detailSize; i++) {
            final DeliveryPlanLineReqDTO lineReqDTO = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS().get(i);
            if (StringUtils.isBlank(lineReqDTO.getSuppliesCode())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的物料编码不能为空;");
                continue;
            }
            List<StockSupplies> stockSuppliesList = stockSuppliesService.getEnableSuppliesList(lineReqDTO.getSuppliesCode());
            if (CollectionUtils.isEmpty(stockSuppliesList)) {
                sb.append("计划出库单行的第" + (i + 1) + "条数据的物料编码不存在或已禁用;");
                continue;
            }
            if (receiveAssets.contains(lineReqDTO.getSuppliesCode())) {
                sb.append("领用出库单详细数据的第" + (i + 1) + "条数据的物料编码重复;");
                continue;
            } else {
                receiveAssets.add(lineReqDTO.getAssetsCode());
            }
            lineReqDTO.setSuppliesCode(stockSuppliesList.get(0).getCode());
        }
        if (sb.length() > 0) {
            return sb.toString();
        }
        return null;
    }


    /**
     * 保存计划调拨单赋值
     *
     * @param deliveryPlanHeadReqDTO
     * @param stockDeliveryPlanHead
     * @param stockDeliveryPlanLineList
     * @param user
     */
    public void prepareSaveDbBeanDTO(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> stockDeliveryPlanLineList, JwtUser user) throws ParseException {
        String deliveryPlanNo = OrderUtil.getOrderNo(OrderEnum.USE_OUT_WAREHOUSE);
        stockDeliveryPlanHead.setDeliveryPlanNo(deliveryPlanNo);
        stockDeliveryPlanHead.setOutStockType(deliveryPlanHeadReqDTO.getOutStockType());
        stockDeliveryPlanHead.setOutWarehouseCode(deliveryPlanHeadReqDTO.getOutWarehouseCode());
        stockDeliveryPlanHead.setInWarehouseCode(deliveryPlanHeadReqDTO.getInWarehouseCode());
        stockDeliveryPlanHead.setDutyUser(deliveryPlanHeadReqDTO.getDutyUser());
        stockDeliveryPlanHead.setUseUser(deliveryPlanHeadReqDTO.getUseUser());
        if (null == stockDeliveryPlanHead.getDutyUser()) {
            //领用人为仓库的linkMan
            StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode(stockDeliveryPlanHead.getOutWarehouseCode());
            if (stockWarehouseBase != null) {
                stockDeliveryPlanHead.setDutyUser(stockWarehouseBase.getLinkman());
            }
        }
        stockDeliveryPlanHead.setReasonCode(deliveryPlanHeadReqDTO.getReasonCode());
        stockDeliveryPlanHead.setBillingTime(new Date());
        stockDeliveryPlanHead.setBillingUser(user.getEmployeeCode());
        stockDeliveryPlanHead.setRemark(deliveryPlanHeadReqDTO.getRemark());
        stockDeliveryPlanHead.setStatus(deliveryPlanHeadReqDTO.getStatus());
        stockDeliveryPlanHead.setPlanOutTime(new Date());
        //判断领用原因
        stockDeliveryPlanHead.setStatus(DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode());
        stockDeliveryPlanHead.setUpdatedBy(user.getEmployeeCode());
        stockDeliveryPlanHead.setCreatedBy(user.getEmployeeCode());

        for (DeliveryPlanLineReqDTO deliveryPlanLineReqDTO : deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS()) {
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine();
            stockDeliveryPlanLine.setSuppliesCode(deliveryPlanLineReqDTO.getSuppliesCode());
            stockDeliveryPlanLine.setPlanOutTime(null != deliveryPlanLineReqDTO.getPlanOutTime() ? DateUtils.dateParse(deliveryPlanLineReqDTO.getPlanOutTime(), DateUtils.DATE_PATTERN) : new Date());
            //判断物料信息是否是现金物料
            StockSupplies supplies = stockSuppliesService.getSuppliesByCode(deliveryPlanLineReqDTO.getSuppliesCode(), null);
            //如果为现金物料时 调拨数量需 乘以100 转化为分
            if (null != supplies && SuppliesEnum.SuppliesType.CASH.getType().equals(supplies.getType())) {
                if (null != deliveryPlanLineReqDTO.getNumber()) {
                    BigDecimal convertNumber = new BigDecimal(deliveryPlanLineReqDTO.getNumber()).multiply(new BigDecimal(CommonConstant.NUMBER_100));
                    stockDeliveryPlanLine.setNumber(convertNumber.intValue());
                    deliveryPlanLineReqDTO.setNumber(convertNumber.intValue());
                }
            }else {
                stockDeliveryPlanLine.setNumber(deliveryPlanLineReqDTO.getNumber());
            }

            stockDeliveryPlanLine.setRealNumber(deliveryPlanLineReqDTO.getNumber());
            stockDeliveryPlanLine.setRealWarehouseCode(deliveryPlanHeadReqDTO.getOutWarehouseCode());
            stockDeliveryPlanLine.setStatus(DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode());
            stockDeliveryPlanLine.setCreatedBy(user.getEmployeeCode());
            stockDeliveryPlanLine.setUpdatedBy(user.getEmployeeCode());
            stockDeliveryPlanLineList.add(stockDeliveryPlanLine);
        }
    }

    /**
     * 生成出库单
     */
    public void generateStockDelivery(StockDeliveryPlanHead stockDeliveryPlanHead,  List<StockDeliveryPlanLine> stockDeliveryPlanLineList,String empployeeCode) throws RuntimeException{
        StockDelivery stockDelivery = ConvertUtil.convertToType(StockDelivery.class, stockDeliveryPlanHead);
        //新增
        Date currentDate = new Date();
        String deliveryNo = OrderUtil.getOrderNo(OrderEnum.OUT_INVENTORY);
        stockDelivery.setDeliveryNo(deliveryNo);
        //出库时间
        stockDelivery.setOutStockTime(currentDate);
        stockDelivery.setPlanOutTime(currentDate);
        stockDelivery.setCreatedBy(empployeeCode);
        stockDelivery.setBillingUser(empployeeCode);
        stockDelivery.setBillingTime(currentDate);
        stockDelivery.setCreatedAt(currentDate);
        stockDelivery.setUpdatedAt(currentDate);
        stockDelivery.setUpdatedBy(empployeeCode);
        stockDelivery.setWarehouseCode(stockDeliveryPlanHead.getOutWarehouseCode());
        stockDelivery.setInWarehouseCode(stockDeliveryPlanHead.getInWarehouseCode());
        stockDelivery.setLogisticsChannel(DeliveryEnum.LogisticsChannel.DEFAULT.getChannel());
        stockDelivery.setOutStockType(stockDeliveryPlanHead.getOutStockType());
        //领用人为仓库的linkMan
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode(stockDeliveryPlanHead.getOutWarehouseCode());
        if (stockWarehouseBase != null) {
            stockDelivery.setDutyUser(stockWarehouseBase.getLinkman());
        }
        stockDelivery.setUseUser(stockDeliveryPlanHead.getUseUser());
        stockDelivery.setReasonCode(stockDeliveryPlanHead.getReasonCode());
        stockDelivery.setRemark(stockDeliveryPlanHead.getRemark());
        stockDelivery.setStatus(DeliveryEnum.Status.ALREADY_OUT.getCode());

        //插入出库单明细
        deliveryMapper.insert(stockDelivery);

        List<StockDeliveryDetail> deliveryDetailList = new ArrayList<>();
        for (StockDeliveryPlanLine stockDeliveryPlanLine : stockDeliveryPlanLineList) {
            StockDeliveryDetail stockDeliveryDetail = new StockDeliveryDetail();
            stockDeliveryDetail.setCreatedBy(empployeeCode);
            stockDeliveryDetail.setUpdatedBy(empployeeCode);
            stockDeliveryDetail.setSuppliesCode(stockDeliveryPlanLine.getSuppliesCode());
            //判断物料信息是否是现金物料
            StockSupplies supplies = stockSuppliesService.getSuppliesByCode(stockDeliveryPlanLine.getSuppliesCode(), null);
            //如果为现金物料时 调拨数量需 乘以100 转化为分
            if (null != supplies && SuppliesEnum.SuppliesType.CASH.getType().equals(supplies.getType())) {
                if (null != stockDeliveryPlanLine.getNumber()) {
                    BigDecimal convertNumber = new BigDecimal(stockDeliveryPlanLine.getNumber()).multiply(new BigDecimal(CommonConstant.NUMBER_100));
                    stockDeliveryDetail.setNumber(convertNumber.intValue());
                }
            }
            stockDeliveryDetail.setNumber(stockDeliveryPlanLine.getNumber());
            stockDeliveryDetail.setRealNumber(stockDeliveryPlanLine.getNumber());
            stockDeliveryDetail.setIsSend(stockDeliveryPlanLine.getIsSend());
            stockDeliveryDetail.setPlanOutTime(currentDate);
            stockDeliveryDetail.setInventoryOutTime(currentDate);
            stockDeliveryDetail.setStatus(DeliveryDetailEnum.Status.ALREADY_OUT.getCode());
            if (StringUtils.isNotBlank(stockDeliveryPlanLine.getRealWarehouseCode())) {
                stockDeliveryDetail.setRealWarehouseCode(stockDeliveryPlanLine.getRealWarehouseCode());
            } else {
                stockDeliveryDetail.setRealWarehouseCode("");
            }

            deliveryDetailList.add(stockDeliveryDetail);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty (deliveryDetailList)) {
//            deliveryDetailList.forEach (stockDeliveryDetail -> stockDeliveryDetail.setDeliveryId (stockDelivery.getDeliveryId ()));
            //实时扣减库存
            deliveryDetailList.forEach(stockDeliveryDetail -> {
                stockDeliveryDetail.setDeliveryId (stockDelivery.getDeliveryId ());
                StockSuppliesQuantity subtractStockSuppliesQuantity = new StockSuppliesQuantity();
                subtractStockSuppliesQuantity.setWarehouseCode(stockDeliveryDetail.getRealWarehouseCode());
                subtractStockSuppliesQuantity.setSuppliesCode(stockDeliveryDetail.getSuppliesCode());
                subtractStockSuppliesQuantity.setQuantity(new BigDecimal(stockDeliveryDetail.getNumber()));
                subtractStockSuppliesQuantity.setStatus(StockSuppliesQuantityEnum.status.IN_STOCK.getValue());
                stockSuppliesQuantityService.subtractQuantity(subtractStockSuppliesQuantity);
            });

            deliveryDetailService.batchInsertStockDeliveryDetail (deliveryDetailList);

        }
    }
}
