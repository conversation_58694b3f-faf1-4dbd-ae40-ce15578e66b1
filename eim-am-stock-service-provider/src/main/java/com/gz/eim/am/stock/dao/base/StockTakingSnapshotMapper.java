package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockTakingSnapshot;
import com.gz.eim.am.stock.entity.StockTakingSnapshotExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockTakingSnapshotMapper {
    long countByExample(StockTakingSnapshotExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockTakingSnapshot record);

    int insertSelective(StockTakingSnapshot record);

    List<StockTakingSnapshot> selectByExample(StockTakingSnapshotExample example);

    StockTakingSnapshot selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockTakingSnapshot record, @Param("example") StockTakingSnapshotExample example);

    int updateByExample(@Param("record") StockTakingSnapshot record, @Param("example") StockTakingSnapshotExample example);

    int updateByPrimaryKeySelective(StockTakingSnapshot record);

    int updateByPrimaryKey(StockTakingSnapshot record);
}