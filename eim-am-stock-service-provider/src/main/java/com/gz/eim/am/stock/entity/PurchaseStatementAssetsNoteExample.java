package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PurchaseStatementAssetsNoteExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public PurchaseStatementAssetsNoteExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeIsNull() {
            addCriterion("accountant_code is null");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeIsNotNull() {
            addCriterion("accountant_code is not null");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeEqualTo(String value) {
            addCriterion("accountant_code =", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeNotEqualTo(String value) {
            addCriterion("accountant_code <>", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeGreaterThan(String value) {
            addCriterion("accountant_code >", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeGreaterThanOrEqualTo(String value) {
            addCriterion("accountant_code >=", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeLessThan(String value) {
            addCriterion("accountant_code <", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeLessThanOrEqualTo(String value) {
            addCriterion("accountant_code <=", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeLike(String value) {
            addCriterion("accountant_code like", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeNotLike(String value) {
            addCriterion("accountant_code not like", value, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeIn(List<String> values) {
            addCriterion("accountant_code in", values, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeNotIn(List<String> values) {
            addCriterion("accountant_code not in", values, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeBetween(String value1, String value2) {
            addCriterion("accountant_code between", value1, value2, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantCodeNotBetween(String value1, String value2) {
            addCriterion("accountant_code not between", value1, value2, "accountantCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeIsNull() {
            addCriterion("accountant_line_code is null");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeIsNotNull() {
            addCriterion("accountant_line_code is not null");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeEqualTo(String value) {
            addCriterion("accountant_line_code =", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeNotEqualTo(String value) {
            addCriterion("accountant_line_code <>", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeGreaterThan(String value) {
            addCriterion("accountant_line_code >", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeGreaterThanOrEqualTo(String value) {
            addCriterion("accountant_line_code >=", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeLessThan(String value) {
            addCriterion("accountant_line_code <", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeLessThanOrEqualTo(String value) {
            addCriterion("accountant_line_code <=", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeLike(String value) {
            addCriterion("accountant_line_code like", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeNotLike(String value) {
            addCriterion("accountant_line_code not like", value, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeIn(List<String> values) {
            addCriterion("accountant_line_code in", values, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeNotIn(List<String> values) {
            addCriterion("accountant_line_code not in", values, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeBetween(String value1, String value2) {
            addCriterion("accountant_line_code between", value1, value2, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andAccountantLineCodeNotBetween(String value1, String value2) {
            addCriterion("accountant_line_code not between", value1, value2, "accountantLineCode");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoIsNull() {
            addCriterion("receive_item_no is null");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoIsNotNull() {
            addCriterion("receive_item_no is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoEqualTo(String value) {
            addCriterion("receive_item_no =", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoNotEqualTo(String value) {
            addCriterion("receive_item_no <>", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoGreaterThan(String value) {
            addCriterion("receive_item_no >", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoGreaterThanOrEqualTo(String value) {
            addCriterion("receive_item_no >=", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoLessThan(String value) {
            addCriterion("receive_item_no <", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoLessThanOrEqualTo(String value) {
            addCriterion("receive_item_no <=", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoLike(String value) {
            addCriterion("receive_item_no like", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoNotLike(String value) {
            addCriterion("receive_item_no not like", value, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoIn(List<String> values) {
            addCriterion("receive_item_no in", values, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoNotIn(List<String> values) {
            addCriterion("receive_item_no not in", values, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoBetween(String value1, String value2) {
            addCriterion("receive_item_no between", value1, value2, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andReceiveItemNoNotBetween(String value1, String value2) {
            addCriterion("receive_item_no not between", value1, value2, "receiveItemNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNull() {
            addCriterion("invoice_type is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNotNull() {
            addCriterion("invoice_type is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeEqualTo(Integer value) {
            addCriterion("invoice_type =", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotEqualTo(Integer value) {
            addCriterion("invoice_type <>", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThan(Integer value) {
            addCriterion("invoice_type >", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("invoice_type >=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThan(Integer value) {
            addCriterion("invoice_type <", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("invoice_type <=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIn(List<Integer> values) {
            addCriterion("invoice_type in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotIn(List<Integer> values) {
            addCriterion("invoice_type not in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeBetween(Integer value1, Integer value2) {
            addCriterion("invoice_type between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("invoice_type not between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceIsNull() {
            addCriterion("statement_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceIsNotNull() {
            addCriterion("statement_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceEqualTo(BigDecimal value) {
            addCriterion("statement_unit_price =", value, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("statement_unit_price <>", value, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("statement_unit_price >", value, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("statement_unit_price >=", value, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceLessThan(BigDecimal value) {
            addCriterion("statement_unit_price <", value, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("statement_unit_price <=", value, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceIn(List<BigDecimal> values) {
            addCriterion("statement_unit_price in", values, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("statement_unit_price not in", values, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("statement_unit_price between", value1, value2, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("statement_unit_price not between", value1, value2, "statementUnitPrice");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxIsNull() {
            addCriterion("statement_price_excluding_tax is null");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxIsNotNull() {
            addCriterion("statement_price_excluding_tax is not null");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxEqualTo(BigDecimal value) {
            addCriterion("statement_price_excluding_tax =", value, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxNotEqualTo(BigDecimal value) {
            addCriterion("statement_price_excluding_tax <>", value, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxGreaterThan(BigDecimal value) {
            addCriterion("statement_price_excluding_tax >", value, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("statement_price_excluding_tax >=", value, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxLessThan(BigDecimal value) {
            addCriterion("statement_price_excluding_tax <", value, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("statement_price_excluding_tax <=", value, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxIn(List<BigDecimal> values) {
            addCriterion("statement_price_excluding_tax in", values, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxNotIn(List<BigDecimal> values) {
            addCriterion("statement_price_excluding_tax not in", values, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("statement_price_excluding_tax between", value1, value2, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andStatementPriceExcludingTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("statement_price_excluding_tax not between", value1, value2, "statementPriceExcludingTax");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(BigDecimal value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(BigDecimal value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(BigDecimal value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<BigDecimal> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andPayDateIsNull() {
            addCriterion("pay_date is null");
            return (Criteria) this;
        }

        public Criteria andPayDateIsNotNull() {
            addCriterion("pay_date is not null");
            return (Criteria) this;
        }

        public Criteria andPayDateEqualTo(Date value) {
            addCriterion("pay_date =", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotEqualTo(Date value) {
            addCriterion("pay_date <>", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThan(Date value) {
            addCriterion("pay_date >", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThanOrEqualTo(Date value) {
            addCriterion("pay_date >=", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateLessThan(Date value) {
            addCriterion("pay_date <", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateLessThanOrEqualTo(Date value) {
            addCriterion("pay_date <=", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateIn(List<Date> values) {
            addCriterion("pay_date in", values, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotIn(List<Date> values) {
            addCriterion("pay_date not in", values, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateBetween(Date value1, Date value2) {
            addCriterion("pay_date between", value1, value2, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotBetween(Date value1, Date value2) {
            addCriterion("pay_date not between", value1, value2, "payDate");
            return (Criteria) this;
        }

        public Criteria andBillCodeIsNull() {
            addCriterion("bill_code is null");
            return (Criteria) this;
        }

        public Criteria andBillCodeIsNotNull() {
            addCriterion("bill_code is not null");
            return (Criteria) this;
        }

        public Criteria andBillCodeEqualTo(String value) {
            addCriterion("bill_code =", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotEqualTo(String value) {
            addCriterion("bill_code <>", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeGreaterThan(String value) {
            addCriterion("bill_code >", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_code >=", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLessThan(String value) {
            addCriterion("bill_code <", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLessThanOrEqualTo(String value) {
            addCriterion("bill_code <=", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeLike(String value) {
            addCriterion("bill_code like", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotLike(String value) {
            addCriterion("bill_code not like", value, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeIn(List<String> values) {
            addCriterion("bill_code in", values, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotIn(List<String> values) {
            addCriterion("bill_code not in", values, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeBetween(String value1, String value2) {
            addCriterion("bill_code between", value1, value2, "billCode");
            return (Criteria) this;
        }

        public Criteria andBillCodeNotBetween(String value1, String value2) {
            addCriterion("bill_code not between", value1, value2, "billCode");
            return (Criteria) this;
        }

        public Criteria andAccountNumIsNull() {
            addCriterion("account_num is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumIsNotNull() {
            addCriterion("account_num is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumEqualTo(Integer value) {
            addCriterion("account_num =", value, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumNotEqualTo(Integer value) {
            addCriterion("account_num <>", value, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumGreaterThan(Integer value) {
            addCriterion("account_num >", value, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_num >=", value, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumLessThan(Integer value) {
            addCriterion("account_num <", value, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumLessThanOrEqualTo(Integer value) {
            addCriterion("account_num <=", value, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumIn(List<Integer> values) {
            addCriterion("account_num in", values, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumNotIn(List<Integer> values) {
            addCriterion("account_num not in", values, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumBetween(Integer value1, Integer value2) {
            addCriterion("account_num between", value1, value2, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAccountNumNotBetween(Integer value1, Integer value2) {
            addCriterion("account_num not between", value1, value2, "accountNum");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNull() {
            addCriterion("assets_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNotNull() {
            addCriterion("assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeEqualTo(String value) {
            addCriterion("assets_code =", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotEqualTo(String value) {
            addCriterion("assets_code <>", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThan(String value) {
            addCriterion("assets_code >", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("assets_code >=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThan(String value) {
            addCriterion("assets_code <", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("assets_code <=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLike(String value) {
            addCriterion("assets_code like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotLike(String value) {
            addCriterion("assets_code not like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIn(List<String> values) {
            addCriterion("assets_code in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotIn(List<String> values) {
            addCriterion("assets_code not in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeBetween(String value1, String value2) {
            addCriterion("assets_code between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("assets_code not between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}