package com.gz.eim.am.stock.dao.inventory;

import com.gz.eim.am.stock.entity.StockInventoryInAssetsConditions;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/7/24
 * @description
 */
public interface InventoryInAssetsConditionsMapper {

    /**
     * 批量插入入库情况记录表靠
     * @param stockInventoryInAssetsConditionsList
     * @return
     */
    int batchInsertAssetConditions(List<StockInventoryInAssetsConditions> stockInventoryInAssetsConditionsList);
}
