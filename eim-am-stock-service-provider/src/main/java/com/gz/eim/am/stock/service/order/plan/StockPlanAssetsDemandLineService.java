package com.gz.eim.am.stock.service.order.plan;


import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandHeadReqDTO;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandLineReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsDemandLine;

import java.util.List;
import java.util.Map;

/**
 * @author: yangjifan1
 * @date: 2021/10/9
 * @description: 资产领用需求单Service
 */
public interface StockPlanAssetsDemandLineService {

    /**
     * @description: 获取资产需求单信息
     * @return:
     * @author: yangjifan1
     * @date: 2021/10/11
     */
    ResponseData queryUserStockAssetsDemandList() throws Exception;

    /**
     * 获取需求单行信息
     * @param stockAssetsDemandLineReqDTO
     * @return
     */
    List<StockAssetsDemandLine> getStockAssetsDemandLineList(StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO);
    /**
     * 获取需求单行信息
     * @param stockAssetsDemandLineReqDTO
     * @return
     */
    StockAssetsDemandLine getStockAssetsDemandLine(StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO);
    /**
     * @param: stockAssetsDemandLineReqDTOList
     * @description: 根据领用数量创建出库申请单
     * @return:
     * @author: yangjifan1
     * @date: 2021/10/12
     */
    ResponseData receiveAssetsNotify(StockAssetsDemandHeadReqDTO stockAssetsDemandHeadReqDTO);
    /**
     * @param: stockAssetsDemandLineReqDTO
     * @description: 录入资产
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/15
     */
    ResponseData enterAssets(StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO) throws Exception;
    /**
     * @param: stockAssetsDemandHeadReqDTO
     * @description: 根据资产需求单行单单号查询录入的资产信息
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/12
     */
    ResponseData queryEnterAssetsByDemandItemNo(String demandItemNo) throws Exception;
    /**
     * @param: stockAssetsDemandHeadReqDTO
     * @description: 驳回当前用户领用数量
     * @return:
     * @author: yangjifan1
     * @date: 2021/10/18
     */
    ResponseData receiveAssetsReject(StockAssetsDemandHeadReqDTO stockAssetsDemandHeadReqDTO);

    /**
     * @param: stockAssetsDemandHeadReqDTO
     * @description: 批量更新行单
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/12
     */
    int batchUpdate(List<StockAssetsDemandLine> stockAssetsDemandLineList);
    /**
     * @param: stockAssetsDemandHeadReqDTO
     * @description: 查询资产领用需求行单列表
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/12
     */
    ResponseData queryStockAssetsDemandLineList(StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO);

    /**
     * @param: stockAssetsDemandHeadReqDTO
     * @description: 通知领用或下单接口
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/3/13
     */
    ResponseData notifyReceiveOrConfirmOrder(StockAssetsDemandHeadReqDTO stockAssetsDemandHeadReqDTO);

    /**
     * @param: stockAssetsDemandLineReqDTO
     * @description: 查询资产需求行Map
     * @return: Map<String, StockAssetsDemandLine>
     * @author: <EMAIL>
     * @date: 2023/3/13
     */
    Map<String, StockAssetsDemandLine> getStockAssetsDemandLineMap(StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO);
}
