package com.gz.eim.am.stock.service.impl.discount;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.activity.MessageUtil;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.activity.ActivityRespDTO;
import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.PropertyConstants;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.vo.download.*;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.discount.StockLicenseLoanTimeTaskService;
import com.gz.eim.am.stock.service.inventory.plan.StockInventoryInPlanHeadService;
import com.gz.eim.am.stock.service.inventory.plan.StockInventoryInPlanLineAssetsService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanHeadService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanLineService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.common.ExcelUtils;
import com.gz.eim.am.stock.util.common.FileUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.gz.eim.am.stock.util.em.StockAssetsCategoryEnum.category.*;

/**
 * @Author: wangjing67
 * @Date: 4/7/21 11:22 上午
 * @description
 */
@Slf4j
@Service
public class StockLicenseLoanTimeTaskServiceImpl implements StockLicenseLoanTimeTaskService {

    @Value("${project.wfl.systemId}")
    private String systemId;

    @Value("${project.wfl.checkCode}")
    private String checkCode;

    /**
     * 员工续借跳转页面地址
     */
    @Value("${project.licenseEmployeeUrl.renew}")
    private String employeeRenewUrl;

    /**
     * 员工归还执照/印章跳转页面地址
     */
    @Value("${project.licenseEmployeeUrl.return}")
    private String employeeReturnUrl;

    /**
     * 管理员归还执照/印章跳转页面地址
     */
    @Value("${project.licenseAdminUrl.return}")
    private String adminReturnUrl;

    /**
     * 员工续借银行开户许可证/财务章跳转页面地址
     */
    @Value("${project.financeEmployeeUrl.renew}")
    private String employeeRenewFinanceUrl;

    /**
     * 员工归还银行开户许可证/财务印章跳转页面地址
     */
    @Value("${project.financeEmployeeUrl.return}")
    private String employeeReturnFinanceUrl;

    /**
     * 管理员归还银行开户许可证/财务印章跳转页面地址
     */
    @Value("${project.financeAdminUrl.return}")
    private String adminReturnFinanceUrl;


    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Autowired
    private AssetsMapper assetsMapper;

    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;

    @Autowired
    private StockDeliveryPlanHeadService stockDeliveryPlanHeadService;

    @Autowired
    private StockInventoryInPlanLineAssetsService stockInventoryInPlanLineAssetsService;


    @Autowired
    private StockInventoryInPlanHeadService stockInventoryInPlanHeadService;

    @Autowired
    private FileUtil fileUtil;

    @Override
    public ResponseData mtPushSendMessage() throws Exception {
        //获取当前时间的开始时间
        Date beginDate = DateTimeUtil.getDayStart(new Date());
        Date queryDate = DateUtils.dateAddDays(beginDate, 3);
        //1。查询使用中的执照资产信息
        List<StockAssets> stockAssetsList = assetsMapper.selectOverReturn(AssetsEnum.statusType.USED.getValue(), Arrays.asList(WarehouseEnum.Type.LICENSE_SEAL.getType()), queryDate);

        if (CollectionUtils.isEmpty(stockAssetsList)) {
            return ResponseData.createSuccessResult("未查到快过期或过期资产");
        }

        //筛选出逾期需要发消息的资产信息
//        List<StockAssets> needHandelAssets = new ArrayList<>();
//        for (StockAssets stockAssets : stockAssetsList) {
//            Date planReturnTime = stockAssets.getPlanReturnTime();
//            if (null != planReturnTime) {
//                Long days = DateTimeUtil.getDay(beginDate, planReturnTime);
//                //逾期归还前10天 即 当前时间 - 预计规划时间 = 10
//                if (CommonConstant.DEFAULT_LIMIT.intValue() == days.intValue()) {
//                    needHandelAssets.add(stockAssets);
//                }
//                //归还日期当天 即 当前时间等于预计归还时间
//                if (CommonConstant.NUMBER_ZERO.intValue() == days.intValue()) {
//                    needHandelAssets.add(stockAssets);
//                }
//                //已逾期 即 当前时间大于预计归还时间
//                if (days.intValue() > CommonConstant.NUMBER_ZERO.intValue()) {
//                    needHandelAssets.add(stockAssets);
//                }
//            }
//        }

        //根据资产持有人分组
        Map<String, List<StockAssets>> stringStockAssetsMap = new HashMap<>();
        //获取所有的资产持有人
        List<String> holderList = new ArrayList<>();

        List<StockAssets> newStockAssets = new ArrayList<>();

//        if (CollectionUtils.isNotEmpty(needHandelAssets)) {
        //根据资产持有人分组
        for (StockAssets stockAssets : stockAssetsList) {
            if (!holderList.contains(stockAssets.getHolder())) {
                holderList.add(stockAssets.getHolder());
            }
            if (!stringStockAssetsMap.containsKey(stockAssets.getHolder())) {
                stringStockAssetsMap.put(stockAssets.getHolder(), new ArrayList());
            }
            stringStockAssetsMap.get(stockAssets.getHolder()).add(stockAssets);
        }
        //判断需要发送消息的资产信息中是否已处理且在审批中的单子
        if (CollectionUtils.isNotEmpty(holderList)) {
            for (String holder : holderList) {
                List<StockAssets> dtoList = stringStockAssetsMap.get(holder);
                if (CollectionUtils.isNotEmpty(dtoList)) {
                    //判断是否有审批中的续借单 或者归还单  过滤掉已经处理的资产信息
                    List<StockAssets> assetsList = checkIsExistRenewOrReturn(holder, dtoList);
                    newStockAssets.addAll(assetsList);
                }
            }
        }
//        }


        //根据资产持有人分组
        Map<String, List<StockAssets>> newStringStockAssetsMap = new HashMap<>();
        //获取所有的资产持有人
        List<String> newHolderList = new ArrayList<>();

        //根据资产管理员分组
        Map<String, List<StockAssets>> stockAssetsMap = new HashMap<>();
        //获取所有的资产管理员
        List<String> assetsKeeperList = new ArrayList<>();

        for (StockAssets stockAssets : newStockAssets) {
            if (StringUtils.isNotBlank(stockAssets.getHolder())) {
                if (!newHolderList.contains(stockAssets.getHolder())) {
                    newHolderList.add(stockAssets.getHolder());
                }
                if (!newStringStockAssetsMap.containsKey(stockAssets.getHolder())) {
                    newStringStockAssetsMap.put(stockAssets.getHolder(), new ArrayList());
                }
                newStringStockAssetsMap.get(stockAssets.getHolder()).add(stockAssets);
            }

            if (StringUtils.isNotBlank(stockAssets.getAssetsKeeper())) {
                if (!assetsKeeperList.contains(stockAssets.getAssetsKeeper())) {
                    assetsKeeperList.add(stockAssets.getAssetsKeeper());
                }
                if (!stockAssetsMap.containsKey(stockAssets.getAssetsKeeper())) {
                    stockAssetsMap.put(stockAssets.getAssetsKeeper(), new ArrayList());
                }
                stockAssetsMap.get(stockAssets.getAssetsKeeper()).add(stockAssets);
            }
        }

        //查询sys_user人员信息
        //assetsKeeperList.addAll(newHolderList);
        //Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(assetsKeeperList);

        //资产管理员发送消息
        if (CollectionUtils.isNotEmpty(assetsKeeperList)) {
            Set<String> assetsKeeperSet = new HashSet<>(assetsKeeperList);
            Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(assetsKeeperList);
            for (String assetsKeeper : assetsKeeperSet) {
                SysUser sysUser = stringSysUserMap.get(assetsKeeper);
                //判断资产管理员状态是否是已离职
                if (null != sysUser && AssetTransferEnum.EMP_JOB.JOB.getCode().equals(sysUser.getStatus())) {
                    List<StockAssets> licenseAssetsList = new ArrayList<>();
                    List<StockAssets> sealAssetsList = new ArrayList<>();
                    List<StockAssets> financeLicenceAssetsList = new ArrayList<>();
                    List<StockAssets> financeSealAssetsList = new ArrayList<>();
                    List<StockAssets> dtoList = stockAssetsMap.get(assetsKeeper);
                    //区分执照/印章
                    if (CollectionUtils.isNotEmpty(dtoList)) {
                        dtoList.forEach(stockAssets -> {
                            if (LICENSE.getValue().equals(stockAssets.getCategoryCode())) {
                                licenseAssetsList.add(stockAssets);
                            } else if (StockAssetsCategoryEnum.category.SEAL.getValue().equals(stockAssets.getCategoryCode())) {
                                sealAssetsList.add(stockAssets);
                            } else if (FINANCE_LICENCE.getValue().equals(stockAssets.getCategoryCode())) {
                                financeLicenceAssetsList.add(stockAssets);
                            } else if (FINANCE_SEAL.getValue().equals(stockAssets.getCategoryCode())) {
                                financeSealAssetsList.add(stockAssets);
                            } else {
                                log.error("StockLicenseLoanTimeTaskServiceImpl.mtPushSendMessage 资产编码未找到资产分类,assetsCode = {}", stockAssets.getAssetsCode());
                            }
                        });
                    } else {
                        log.warn("StockLicenseLoanTimeTaskServiceImpl.mtPushSendMessage dtoList is null,assetsKeeper = {}, stockAssetsMap = {},assetsKeeperSet={},", assetsKeeper, stockAssetsMap.toString(), assetsKeeperSet.toString());
                    }

                    //调用执照信息模版
                    if (CollectionUtils.isNotEmpty(licenseAssetsList)) {
                        adminSendMessage(PropertyConstants.ADMIN_MODEL, PropertyConstants.ADMIN_EMAIL_MODEL, assetsKeeper, licenseAssetsList);
                    }
                    //调用印章信息模版
                    if (CollectionUtils.isNotEmpty(sealAssetsList)) {
                        adminSendMessage(PropertyConstants.SEAL_ADMIN_MODEL, PropertyConstants.SEAL_ADMIN_EMAIL_MODEL, assetsKeeper, sealAssetsList);
                    }
                    //调用开户许可证模版
                    if (CollectionUtils.isNotEmpty(financeLicenceAssetsList)) {
                        adminSendMessage(PropertyConstants.FINANCE_ADMIN_MODEL, PropertyConstants.FINANCE_ADMIN_EMAIL_MODEL, assetsKeeper, financeLicenceAssetsList);
                    }
                    //调用财务印章模版
                    if (CollectionUtils.isNotEmpty(financeSealAssetsList)) {
                        adminSendMessage(PropertyConstants.FINANCE_SEAL_ADMIN_MODEL, PropertyConstants.FINANCE_SEAL_ADMIN_EMAIL_MODEL, assetsKeeper, financeSealAssetsList);
                    }

                }
            }
        }

        //资产持有人发送消息
        if (CollectionUtils.isNotEmpty(newHolderList)) {
            Map<String, SysUser> holderUserMap = ambaseCommonService.selectSysUserMapByIds(newHolderList);
            for (String holder : newHolderList) {
                SysUser sysUser = holderUserMap.get(holder);
                //判断资产管理员状态是否是已离职
                if (null != sysUser && AssetTransferEnum.EMP_JOB.JOB.getCode().equals(sysUser.getStatus())) {
                    List<StockAssets> dtoList = stringStockAssetsMap.get(holder);
                    dtoList.sort(Comparator.comparing(StockAssets::getSuppliesCode));
                    if (CollectionUtils.isNotEmpty(dtoList)) {
                        employeeSendMessage(holder, dtoList);
                    }
                }
            }
        }

        //返回结果
        return ResponseData.createSuccessResult();
    }


    /**
     * 校验是否存在审批中的续借单或者归还单
     *
     * @param holder
     * @param stockAssetsList
     * @return
     */
    private List<StockAssets> checkIsExistRenewOrReturn(String holder, List<StockAssets> stockAssetsList) {
        List<StockAssets> haveHandleAssetsList = new ArrayList<>();
        List<String> assetsCodeList = stockAssetsList.stream().map(stockAssets -> stockAssets.getAssetsCode()).collect(Collectors.toList());

        //根据资产编码查询续借单
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectByAssetsCodeList(assetsCodeList);
        Map<String, StockDeliveryPlanLine> stockDeliveryPlanLineMap = new HashMap<>();
        List<Long> deliveryPlanHeadIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stockDeliveryPlanLineList)) {
            for (StockDeliveryPlanLine stockDeliveryPlanLine : stockDeliveryPlanLineList) {
                if (deliveryPlanHeadIdList.contains(stockDeliveryPlanLine.getDeliveryPlanHeadId())) {
                    deliveryPlanHeadIdList.add(stockDeliveryPlanLine.getDeliveryPlanHeadId());
                }
                stockDeliveryPlanLineMap.put(stockDeliveryPlanLine.getAssetsCode(), stockDeliveryPlanLine);
            }
        }
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = stockDeliveryPlanHeadService.selectDeliveryPlanByIds(holder, deliveryPlanHeadIdList);
        Map<Long, StockDeliveryPlanHead> stockDeliveryPlanHeadMap = stockDeliveryPlanHeadList.stream()
                .collect(Collectors.toMap(stockDeliveryPlanHead -> stockDeliveryPlanHead.getDeliveryPlanHeadId(), stockDeliveryPlanHead -> stockDeliveryPlanHead, (key1, key2) -> key1));


        //根据资产编码查询归还单
        List<StockInventoryInPlanLinesAssets> stockInventoryInPlanLinesAssetsList = stockInventoryInPlanLineAssetsService.selectByAssetsCodeList(assetsCodeList);
        List<Long> inventoryInPlanHeadIdList = new ArrayList<>();
        Map<String, StockInventoryInPlanLinesAssets> inventoryInPlanLinesAssetsMap = new HashMap<>();
        for (StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets : stockInventoryInPlanLinesAssetsList) {
            if (!inventoryInPlanHeadIdList.contains(stockInventoryInPlanLinesAssets.getInventoryInPlanHeadId())) {
                inventoryInPlanHeadIdList.add(stockInventoryInPlanLinesAssets.getInventoryInPlanHeadId());
            }
            inventoryInPlanLinesAssetsMap.put(stockInventoryInPlanLinesAssets.getAssetsCode(), stockInventoryInPlanLinesAssets);
        }

        //根据计划单集合查询
        List<StockInventoryInPlanHead> stockInventoryInPlanHeadList = stockInventoryInPlanHeadService.selectByInventoryInPlanHeadIdList(holder, inventoryInPlanHeadIdList);
        Map<Long, StockInventoryInPlanHead> stockInventoryInPlanHeadMap = stockInventoryInPlanHeadList.stream()
                .collect(Collectors.toMap(stockInventoryInPlanHead -> stockInventoryInPlanHead.getInventoryInPlanHeadId(), stockInventoryInPlanHead -> stockInventoryInPlanHead, (key1, key2) -> key1));


        for (StockAssets stockAssets : stockAssetsList) {
            //判断执照是否被续借了 正在审批中的
            StockDeliveryPlanLine stockDeliveryPlanLine = stockDeliveryPlanLineMap.get(stockAssets.getAssetsCode());
            if (null != stockDeliveryPlanLine) {
                StockDeliveryPlanHead stockDeliveryPlanHead = stockDeliveryPlanHeadMap.get(stockDeliveryPlanLine.getDeliveryPlanHeadId());
                if (null != stockDeliveryPlanHead && DeliveryPlanHeadEnum.Status.APPROVE.getCode().equals(stockDeliveryPlanHead.getStatus())) {
                    if (Objects.equals(stockDeliveryPlanHead.getOutStockType(), DeliveryPlanHeadEnum.OutType.LICENSE_RENEW.getCode())
                            || Objects.equals(stockDeliveryPlanHead.getOutStockType(), DeliveryPlanHeadEnum.OutType.FINANCE_LICENSE_RENEW.getCode())
                            || Objects.equals(stockDeliveryPlanHead.getOutStockType(), DeliveryPlanHeadEnum.OutType.FINANCE_SEAL_RENEW.getCode())) {
                        haveHandleAssetsList.add(stockAssets);
                    }
                }
            }
            //判断执照是否归还了 正在审批中
            StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets = inventoryInPlanLinesAssetsMap.get(stockAssets.getAssetsCode());
            if (null != stockInventoryInPlanLinesAssets) {
                StockInventoryInPlanHead stockInventoryInPlanHead = stockInventoryInPlanHeadMap.get(stockInventoryInPlanLinesAssets.getInventoryInPlanHeadId());
                if (null != stockInventoryInPlanHead && InventoryInPlanHeadEnum.Status.APPROVE.getCode().equals(stockInventoryInPlanHead.getStatus())) {
                    if (Objects.equals(stockInventoryInPlanHead.getInventoryInPlanType(), InventoryInPlanHeadEnum.InType.LICENSE_RETURN.getCode())
                                    || Objects.equals(stockInventoryInPlanHead.getInventoryInPlanType(), InventoryInPlanHeadEnum.InType.FINANCE_LICENSE_RETURN.getCode())
                                    || Objects.equals(stockInventoryInPlanHead.getInventoryInPlanType(), InventoryInPlanHeadEnum.InType.FINANCE_SEAL_RETURN.getCode())) {
                        haveHandleAssetsList.add(stockAssets);
                    }
                }
            }
        }
        //移除已经处理 在审批中的资产信息
        stockAssetsList.removeAll(haveHandleAssetsList);
        return stockAssetsList;
    }


    /**
     * 拼接消息内容
     *
     * @param assetsDTOList
     * @throws Exception
     */
    private String handelMessageContent(List<StockAssets> assetsDTOList, Boolean flag) throws Exception {
        StringBuffer stringBuffer = new StringBuffer();
        List<String> holderList = assetsDTOList.stream().distinct().map(stockAssets -> stockAssets.getHolder()).collect(Collectors.toList());
        Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(holderList);
        for (StockAssets assetsDTO : assetsDTOList) {
            String contentName = StockAssetsCategoryEnum.categoryMap.get(assetsDTO.getCategoryCode());
            //管理员发送消息内容
            if (flag) {
                if (StringUtils.isNotBlank(assetsDTO.getHolder())) {
                    SysUser sysUser = stringSysUserMap.get(assetsDTO.getHolder());
                    if (null != sysUser) {
                        stringBuffer.append(contentName + "名称:").append(assetsDTO.getAssetsName()).append(",")
                                .append(contentName + "编码:").append(assetsDTO.getAssetsCode()).append(",")
                                .append(contentName + "借用人:").append(assetsDTO.getHolder()).append(" ").append(sysUser.getName()).append(";")
                                .append("归还时间:").append(DateUtils.dateFormat(assetsDTO.getPlanReturnTime(), DateUtils.DATE_PATTERN))
                                .append(";").append("<br>");
                    }
                }
            } else {
                //执照使用人发送消息内容
                stringBuffer.append(contentName + "名称:").append(assetsDTO.getAssetsName()).append(",")
                        .append(contentName + "编码:").append(assetsDTO.getAssetsCode()).append(",")
                        .append("归还时间:").append(DateUtils.dateFormat(assetsDTO.getPlanReturnTime(), DateUtils.DATE_PATTERN))
                        .append(";").append("<br>");
            }
        }
        if (stringBuffer.length() > 0) {
            return stringBuffer.toString();
        }
        return null;
    }


    /**
     * 当前使用人发送呱呱消息
     *
     * @param holder
     * @param assetsDTOList
     */
    private void employeeSendMessage(String holder, List<StockAssets> assetsDTOList) throws Exception {
        //拼接发送消息内容
//        String messageContent = handelMessageContent(assetsDTOList, false);
        // 首先根据类型生成excel文件t, ExportLicenseOverdueReminderEntity.class, fileName);

        List<StockAssets> financeStockAssetsList = new ArrayList<>();
        List<StockAssets> grLegalStockAssetsList = new ArrayList<>();
        for (StockAssets stockAssets : assetsDTOList) {
            if (FINANCE_LICENCE.getValue().equals(stockAssets.getCategoryCode()) || FINANCE_SEAL.getValue().equals(stockAssets.getCategoryCode())) {
                financeStockAssetsList.add(stockAssets);
            } else if (LICENSE.getValue().equals(stockAssets.getCategoryCode()) || SEAL.getValue().equals(stockAssets.getCategoryCode())) {
                grLegalStockAssetsList.add(stockAssets);
            }
        }

        if(CollectionUtils.isNotEmpty(grLegalStockAssetsList)){
            String fileName = PropertyConstants.LICENSE_AND_SEAL_USER_EMAIL_FILE_NAME;
            String filedId = StringConstant.EMPTY;
            String messageContent = StringConstant.EMPTY;
            // 这里需要设置附件
            MockMultipartFile excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(grLegalStockAssetsList, ExportUserLicenseAndSealOverdueReminderEntity.class, fileName);
            if (excelToMockMultipartFile != null) {
                // 开始上传
                Map<String, String> returnMap = fileUtil.uploadFile(excelToMockMultipartFile);
                if (MapUtils.isEmpty(returnMap)) {
                    log.error("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，上传文件失败------，资产内容为：{}", JSON.toJSONString(assetsDTOList));
                    return;
                } else {
                    log.info("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，上传文件成功，返回内容为：{}", JSONObject.toJSONString(returnMap));
                    filedId = returnMap.get("fileId");
                    if (StringUtils.isBlank(filedId)) {
                        log.error("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，上传文件失败------，返回filedId为空，返回内容为：{}", JSONObject.toJSONString(returnMap));
                        return;
                    }
                    // 设置内容为去查看邮件
                    messageContent = fileName + "已经发送邮件，具体信息请查看附件";
                }
            }
            if (StringUtils.isBlank(filedId)) {
                log.error("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，生成附件失败，资产内容为：{}", JSON.toJSONString(assetsDTOList));
                return;
            }
            // 这里改为发送附件
            List<String> employEmailList = ambaseCommonService.selectUsersEmailByIds(Arrays.asList(holder));
            if (CollectionUtils.isNotEmpty(employEmailList)) {
                //将数组拼接为字符串
                String peopleResult = employEmailList.stream().collect(Collectors.joining(";"));
                //发送呱呱消息
                sendMessage(PropertyConstants.EMPLOYEE_MODEL, peopleResult, messageContent, false, StringConstant.EMPTY,PropertyConstants.ADMIN_EMAIL_MODEL );
                //发送邮件
                sendMessage(PropertyConstants.EMPLOYEE_EMAIL_MODEL, peopleResult, messageContent, false, filedId, PropertyConstants.ADMIN_EMAIL_MODEL);
            }

        }

        if(CollectionUtils.isNotEmpty(financeStockAssetsList)) {
            String fileName = PropertyConstants.FINANCE_LICENSE_AND_SEAL_USER_EMAIL_FILE_NAME;
            String filedId = StringConstant.EMPTY;
            String messageContent = StringConstant.EMPTY;
            // 这里需要设置附件
            MockMultipartFile excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(financeStockAssetsList, ExportUserLicenseAndSealOverdueReminderEntity.class, fileName);
            if (excelToMockMultipartFile != null) {
                // 开始上传
                Map<String, String> returnMap = fileUtil.uploadFile(excelToMockMultipartFile);
                if (MapUtils.isEmpty(returnMap)) {
                    log.error("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，上传文件失败------，资产内容为：{}", JSON.toJSONString(assetsDTOList));
                    return;
                } else {
                    log.info("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，上传文件成功，返回内容为：{}", JSONObject.toJSONString(returnMap));
                    filedId = returnMap.get("fileId");
                    if (StringUtils.isBlank(filedId)) {
                        log.error("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，上传文件失败------，返回filedId为空，返回内容为：{}", JSONObject.toJSONString(returnMap));
                        return;
                    }
                    // 设置内容为去查看邮件
                    messageContent = fileName + "已经发送邮件，具体信息请查看附件";
                }
            }
            if (StringUtils.isBlank(filedId)) {
                log.error("StockLicenseLoanTimeTaskServiceImpl.employeeSendMessage，生成附件失败，资产内容为：{}", JSON.toJSONString(assetsDTOList));
                return;
            }
            // 这里改为发送附件
            List<String> employEmailList = ambaseCommonService.selectUsersEmailByIds(Arrays.asList(holder));
            if (CollectionUtils.isNotEmpty(employEmailList)) {
                //将数组拼接为字符串
                String peopleResult = employEmailList.stream().collect(Collectors.joining(";"));
                //发送呱呱消息
                sendMessage(PropertyConstants.EMPLOYEE_MODEL, peopleResult, messageContent, false, StringConstant.EMPTY, PropertyConstants.FINANCE_ADMIN_EMAIL_MODEL);
                //发送邮件
                sendMessage(PropertyConstants.EMPLOYEE_EMAIL_MODEL, peopleResult, messageContent, false, filedId, PropertyConstants.FINANCE_ADMIN_EMAIL_MODEL);
            }

        }

    }


    /**
     * 管理员发送消息
     *
     * @param stockAssetsList
     * @param assetsKeeper
     */
    private void adminSendMessage(String guaguaTaskCode, String emailTaskCode, String assetsKeeper, List<StockAssets> stockAssetsList) throws Exception {
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            return;
        }
        List<AssetsDTO> assetsDTOList = new ArrayList<>(stockAssetsList.size());
        List<String> holderList = new ArrayList<>(stockAssetsList.size());
        // 获取资产所有的持有人
        for (StockAssets stockAssets : stockAssetsList) {
            AssetsDTO assetsDTO = ConvertUtil.convertToType(AssetsDTO.class, stockAssets);
            assetsDTOList.add(assetsDTO);
            holderList.add(assetsDTO.getHolder());
        }
        Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(holderList);
        if (MapUtils.isNotEmpty(stringSysUserMap)) {
            for (AssetsDTO assetsDTO : assetsDTOList) {
                SysUser sysUser = stringSysUserMap.get(assetsDTO.getHolder());
                if (sysUser != null) {
                    assetsDTO.setHolderName(sysUser.getName());
                }
            }
        }
        String messageContent = StringConstant.EMPTY;
        String filedId = StringConstant.EMPTY;
        String fileName = StringConstant.EMPTY;
        // 这里需要设置附件
        MockMultipartFile excelToMockMultipartFile = null;
        // 首先根据类型生成excel文件
        if (PropertyConstants.ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
            fileName = PropertyConstants.ADMIN_EMAIL_FILE_NAME;
            excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(assetsDTOList, ExportLicenseOverdueReminderEntity.class, fileName);
        } else if (PropertyConstants.SEAL_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
            fileName = PropertyConstants.SEAL_ADMIN_EMAIL_FILE_NAME;
            excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(assetsDTOList, ExportSealOverdueReminderEntity.class, fileName);
        } else if (PropertyConstants.FINANCE_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
            fileName = PropertyConstants.FINANCE_ADMIN_EMAIL_FILE_NAME;
            excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(assetsDTOList, ExportFinanceLicenseOverdueReminderEntity.class, fileName);
        } else if (PropertyConstants.FINANCE_SEAL_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
            fileName = PropertyConstants.FINANCE_SEAL_ADMIN_EMAIL_FILE_NAME;
            excelToMockMultipartFile = ExcelUtils.getExcelToMockMultipartFile(assetsDTOList, ExportFinanceSealOverdueReminderEntity.class, fileName);
        }
        if (excelToMockMultipartFile != null) {
            // 开始上传
            Map<String, String> returnMap = fileUtil.uploadFile(excelToMockMultipartFile);
            if (MapUtils.isEmpty(returnMap)) {
                log.error("StockLicenseLoanTimeTaskServiceImpl.adminSendMessage，上传文件失败------，资产内容为：{}", JSON.toJSONString(assetsDTOList));
                return;
            } else {
                log.info("StockLicenseLoanTimeTaskServiceImpl.adminSendMessage，上传文件成功，返回内容为：{}", JSONObject.toJSONString(returnMap));
                filedId = returnMap.get("fileId");
                if (StringUtils.isBlank(filedId)) {
                    log.error("StockLicenseLoanTimeTaskServiceImpl.adminSendMessage，上传文件失败------，返回filedId为空，返回内容为：{}", JSONObject.toJSONString(returnMap));
                    return;
                }
                // 设置内容为去查看邮件
                messageContent = fileName + "已经发送邮件，具体信息请查看附件";
            }
        }
        if (StringUtils.isBlank(filedId)) {
            log.error("StockLicenseLoanTimeTaskServiceImpl.adminSendMessage，生成附件失败，资产内容为：{}", JSON.toJSONString(assetsDTOList));
            return;
        }
        List<String> employEmailList = ambaseCommonService.selectUsersEmailByIds(Arrays.asList(assetsKeeper));
        if (CollectionUtils.isNotEmpty(employEmailList)) {
            //将数组拼接为字符串
            String peopleResult = employEmailList.stream().collect(Collectors.joining(";"));
            //发送呱呱消息
            sendMessage(guaguaTaskCode, peopleResult, messageContent, true, StringConstant.EMPTY, emailTaskCode);
            //发送邮件
            sendMessage(emailTaskCode, peopleResult, messageContent, true, filedId, emailTaskCode);
        }

    }

    /**
     * 发送呱呱消息
     *
     * @param taskCode
     * @param users
     */
    private void sendMessage(String taskCode, String users, String messageContent, Boolean flag, String filedId, String emailTaskCode) {
        Map<String, Object> param = new HashMap<>(4);
        try {
            List<Map<String, String>> content = new ArrayList<>(4);
            //消息内容
            Map<String, String> map1 = new HashMap<>(2);
            map1.put("key", "messageContent");
            map1.put("value", messageContent);
            content.add(map1);
            //收件人
            Map<String, String> map2 = new HashMap<>(2);
            map2.put("key", "recipients");
            map2.put("value", users);
            content.add(map2);

            //管理员发送消息
            if (flag) {
                Map<String, String> map5 = new HashMap<>(2);
                map5.put("key", "returnUrl");
                //判断是执照/印章归还还是开户许可证/财务印章归还
                if (PropertyConstants.ADMIN_EMAIL_MODEL.equals(emailTaskCode) || PropertyConstants.SEAL_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
                    map5.put("value", adminReturnUrl);
                } else if (PropertyConstants.FINANCE_ADMIN_EMAIL_MODEL.equals(emailTaskCode) || PropertyConstants.FINANCE_SEAL_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
                    map5.put("value", adminReturnFinanceUrl);
                }
                content.add(map5);
            } else {
                //使用人发送消息
                //执照&印章续借、开户许可证&财务印章续借
                Map<String, String> map3 = new HashMap<>(2);
                map3.put("key", "renewUrl");
                //执照&印章归还、开户许可证&财务印章归还
                Map<String, String> map4 = new HashMap<>(2);
                map4.put("key", "returnUrl");
                if (PropertyConstants.ADMIN_EMAIL_MODEL.equals(emailTaskCode) || PropertyConstants.SEAL_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
                    map3.put("value", employeeRenewUrl);
                    map4.put("value", employeeReturnUrl);
                } else if (PropertyConstants.FINANCE_ADMIN_EMAIL_MODEL.equals(emailTaskCode) || PropertyConstants.FINANCE_SEAL_ADMIN_EMAIL_MODEL.equals(emailTaskCode)) {
                    map3.put("value", employeeRenewFinanceUrl);
                    map4.put("value", employeeReturnFinanceUrl);
                }

                content.add(map3);
                content.add(map4);
            }
            // 判断是否存在附件
            if (StringUtils.isNotEmpty(filedId)) {
                Map<String, String> map6 = new HashMap<>(2);
                map6.put("key", "filedId");
                map6.put("value", filedId);
                content.add(map6);
            }

            //组织调用消息平台参数
            param.put("SystemId", systemId);
            param.put("CheckCode", checkCode);
            param.put("TaskCode", taskCode);
            param.put("TaskRequestJson", JsonUtil.getJsonString(content));

            log.info("send email params:{}", param);
            ActivityRespDTO respDTO = messageUtil.addTaskRequest(param);
            if (!AllocateImportLineEnum.Status.SUCCESS.getCode().equals(respDTO.getStatus())) {
                log.info("发送呱呱消息失败：{}", respDTO);
            } else {
                log.info("发送呱呱消息成功：{}", respDTO);
            }
        } catch (Exception e) {
            log.error("逾期归还提醒发送消息异常：{}", param, e);
        }
    }


}
