package com.gz.eim.am.stock.dao.discount;

import com.gz.eim.am.stock.dto.request.discount.StockLicenseChangeQueryDTO;
import com.gz.eim.am.stock.dto.response.discount.StockLicenseChangeItemsRespDTO;
import com.gz.eim.am.stock.entity.StockLicenseChange;
import com.gz.eim.am.stock.entity.StockLicenseChangeItems;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 4/1/21 2:57 下午
 * @description
 */
public interface LicenseChangeMapper {

    /**
     * 批量新增
     *
     * @param stockLicenseChangeItemsList
     * @return
     * @throws Exception
     */
    Integer batchInsert(List<StockLicenseChangeItems> stockLicenseChangeItemsList) throws Exception;

    /**
     * 新增
     *
     * @param stockLicenseChange
     * @return
     * @throws Exception
     */
    Integer insert(StockLicenseChange stockLicenseChange) throws Exception;


    /**
     * 根据条条件查询
     *
     * @param stockLicenseChangeQueryDTO
     * @return
     * @throws Exception
     */
    List<StockLicenseChangeItemsRespDTO> selectByQuery(StockLicenseChangeQueryDTO stockLicenseChangeQueryDTO) throws Exception;


    /**
     * 根据条条件计数
     *
     * @param stockLicenseChangeQueryDTO
     * @return
     * @throws Exception
     */
    Long countByQuery(StockLicenseChangeQueryDTO stockLicenseChangeQueryDTO) throws Exception;
}
