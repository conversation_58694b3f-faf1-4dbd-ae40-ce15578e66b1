package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsRepairHead;
import com.gz.eim.am.stock.entity.StockAssetsRepairHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsRepairHeadMapper {
    long countByExample(StockAssetsRepairHeadExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsRepairHead record);

    int insertSelective(StockAssetsRepairHead record);

    List<StockAssetsRepairHead> selectByExample(StockAssetsRepairHeadExample example);

    StockAssetsRepairHead selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsRepairHead record, @Param("example") StockAssetsRepairHeadExample example);

    int updateByExample(@Param("record") StockAssetsRepairHead record, @Param("example") StockAssetsRepairHeadExample example);

    int updateByPrimaryKeySelective(StockAssetsRepairHead record);

    int updateByPrimaryKey(StockAssetsRepairHead record);
}