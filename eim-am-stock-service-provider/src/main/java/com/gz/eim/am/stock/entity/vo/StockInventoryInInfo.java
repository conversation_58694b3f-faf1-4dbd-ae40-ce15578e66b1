package com.gz.eim.am.stock.entity.vo;

import com.gz.eim.am.stock.entity.StockInventoryIn;

import java.util.List;


public class StockInventoryInInfo extends StockInventoryIn {
    /**
     * 入库单行集合
     */
    private List<StockInventoryInSuppliesInfo> stockInventoryInSuppliesInfoList;
    /**
     * 需求部门
     */
    private String demandDeptCode;
    /**
     * 计划入库单编码
     */
    private String inventoryInPlanNo;

    /**
     * 采购订单号
     */
    private String purchaseOrderNo;

    /**
     * 资产编码
     */
    private String assetsCode;


    public List<StockInventoryInSuppliesInfo> getStockInventoryInSuppliesInfoList() {
        return stockInventoryInSuppliesInfoList;
    }

    public void setStockInventoryInSuppliesInfoList(List<StockInventoryInSuppliesInfo> stockInventoryInSuppliesInfoList) {
        this.stockInventoryInSuppliesInfoList = stockInventoryInSuppliesInfoList;
    }

    public String getDemandDeptCode() {
        return demandDeptCode;
    }

    public void setDemandDeptCode(String demandDeptCode) {
        this.demandDeptCode = demandDeptCode;
    }

    public String getInventoryInPlanNo() {
        return inventoryInPlanNo;
    }

    public void setInventoryInPlanNo(String inventoryInPlanNo) {
        this.inventoryInPlanNo = inventoryInPlanNo;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    @Override
    public String toString() {
        return "StockInventoryInInfo{" +
                "stockInventoryInSuppliesInfoList=" + stockInventoryInSuppliesInfoList +
                ", demandDeptCode='" + demandDeptCode + '\'' +
                ", inventoryInPlanNo='" + inventoryInPlanNo + '\'' +
                '}';
    }
}