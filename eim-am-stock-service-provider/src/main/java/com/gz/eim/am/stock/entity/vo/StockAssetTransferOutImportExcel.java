package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/5/15
 * @description: 资产
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockAssetTransferOutImportExcel implements ExportModel {
    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    @ExportField(name="资产编码")
    private String assetsCode;

    @ExportField(name="调入仓库名称")
    private String inImportWarehouseName;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }


    @Override
    public String toString() {
        return "StockAssetTransferOutImportExcel{" +
                "assetsCode='" + assetsCode + '\'' +
                ", inImportWarehouseName='" + inImportWarehouseName + '\'' +
                '}';
    }
}