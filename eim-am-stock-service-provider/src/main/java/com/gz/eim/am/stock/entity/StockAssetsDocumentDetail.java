package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockAssetsDocumentDetail {
    private Long id;

    private String documentNo;

    private String assetsCode;

    private String oldAssetsKeeper;

    private String newAssetsKeeper;

    private String oldCompanyCode;

    private String newCompanyCode;

    private String oldDeptCode;

    private String newDeptCode;

    private String oldHolder;

    private String newHolder;

    private String oldHolderAddress;

    private String newHolderAddress;

    private Integer lineStatus;

    private Integer delFlag;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDocumentNo() {
        return documentNo;
    }

    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo == null ? null : documentNo.trim();
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getOldAssetsKeeper() {
        return oldAssetsKeeper;
    }

    public void setOldAssetsKeeper(String oldAssetsKeeper) {
        this.oldAssetsKeeper = oldAssetsKeeper == null ? null : oldAssetsKeeper.trim();
    }

    public String getNewAssetsKeeper() {
        return newAssetsKeeper;
    }

    public void setNewAssetsKeeper(String newAssetsKeeper) {
        this.newAssetsKeeper = newAssetsKeeper == null ? null : newAssetsKeeper.trim();
    }

    public String getOldCompanyCode() {
        return oldCompanyCode;
    }

    public void setOldCompanyCode(String oldCompanyCode) {
        this.oldCompanyCode = oldCompanyCode == null ? null : oldCompanyCode.trim();
    }

    public String getNewCompanyCode() {
        return newCompanyCode;
    }

    public void setNewCompanyCode(String newCompanyCode) {
        this.newCompanyCode = newCompanyCode == null ? null : newCompanyCode.trim();
    }

    public String getOldDeptCode() {
        return oldDeptCode;
    }

    public void setOldDeptCode(String oldDeptCode) {
        this.oldDeptCode = oldDeptCode == null ? null : oldDeptCode.trim();
    }

    public String getNewDeptCode() {
        return newDeptCode;
    }

    public void setNewDeptCode(String newDeptCode) {
        this.newDeptCode = newDeptCode == null ? null : newDeptCode.trim();
    }

    public String getOldHolder() {
        return oldHolder;
    }

    public void setOldHolder(String oldHolder) {
        this.oldHolder = oldHolder == null ? null : oldHolder.trim();
    }

    public String getNewHolder() {
        return newHolder;
    }

    public void setNewHolder(String newHolder) {
        this.newHolder = newHolder == null ? null : newHolder.trim();
    }

    public String getOldHolderAddress() {
        return oldHolderAddress;
    }

    public void setOldHolderAddress(String oldHolderAddress) {
        this.oldHolderAddress = oldHolderAddress == null ? null : oldHolderAddress.trim();
    }

    public String getNewHolderAddress() {
        return newHolderAddress;
    }

    public void setNewHolderAddress(String newHolderAddress) {
        this.newHolderAddress = newHolderAddress == null ? null : newHolderAddress.trim();
    }

    public Integer getLineStatus() {
        return lineStatus;
    }

    public void setLineStatus(Integer lineStatus) {
        this.lineStatus = lineStatus;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}