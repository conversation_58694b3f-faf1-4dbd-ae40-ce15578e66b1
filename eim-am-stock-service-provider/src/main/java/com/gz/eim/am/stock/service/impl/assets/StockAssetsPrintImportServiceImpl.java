package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.assets.AssetsPrintImportMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsPrintImportMapper;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsPrintImportReqDTO;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockAssetsPrintImport;
import com.gz.eim.am.stock.entity.StockAssetsPrintImportExample;
import com.gz.eim.am.stock.entity.StockQr;
import com.gz.eim.am.stock.entity.vo.StockAssetCodeInfo;
import com.gz.eim.am.stock.service.assets.StockAssetCodeService;
import com.gz.eim.am.stock.service.assets.StockAssetsPrintImportService;
import com.gz.eim.am.stock.service.qr.StockQrService;
import com.gz.eim.am.stock.util.em.CommonEnum;
import com.gz.eim.am.stock.util.em.QrEnum;
import com.gz.eim.am.stock.util.em.StockAssetsPrintImportEnum;
import com.gz.eim.app.cns.dto.response.qrcode.BatchQrCodeRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @className: AssetsPrintImportServiceImpl
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2021/12/22
 **/
@Slf4j
@Service
public class StockAssetsPrintImportServiceImpl implements StockAssetsPrintImportService {

    @Autowired
    private StockAssetCodeService stockAssetCodeService;
    @Autowired
    private StockQrService stockQrService;
    @Autowired
    private AssetsPrintImportMapper assetsPrintImportMapper;
    @Autowired
    private StockAssetsPrintImportMapper stockAssetsPrintImportMapper;

    /**
     * @param:
     * @description: 批量查询
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Override
    public int batchInsert(List<StockAssetsPrintImport> stockAssetsPrintImportList) {
        int num = 0;
        if (CollectionUtils.isNotEmpty(stockAssetsPrintImportList)) {
            num = assetsPrintImportMapper.batchInsert(stockAssetsPrintImportList);
        }
        return num;
    }

    /**
     * @param:
     * @description: 批量更新,isUseNo判断是否根据batchNo和assetsCode更新
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Override
    public int batchUpdate(List<StockAssetsPrintImport> stockAssetsPrintImportList, boolean isUseNo) {
        int num = 0;
        if (CollectionUtils.isNotEmpty(stockAssetsPrintImportList)) {
            if (isUseNo) {
                num = assetsPrintImportMapper.batchUpdateByNo(stockAssetsPrintImportList);
            } else {
                num = assetsPrintImportMapper.batchUpdate(stockAssetsPrintImportList);
            }
        }

        return num;
    }

    /**
     * @param:
     * @description: 异步更新临时表数据，并生成pdf
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    /**
     * @param:
     * @description: 异步更新临时表数据
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateAssetsImportAndCreatePdf(List<StockAssetsPrintImportReqDTO> stockAssetsPrintImportReqDTOList, JwtUser user) {
        List<StockAssetsPrintImport> updateStockAssetsPrintImportList = new ArrayList<>();
        log.info("开始异步生成pdf----------------");
        // 开始时间
        long startTime = System.currentTimeMillis();
        for (StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO : stockAssetsPrintImportReqDTOList) {
            StockAssets stockAssets = new StockAssets();
            stockAssets.setAssetsCode(stockAssetsPrintImportReqDTO.getAssetsCode());
            stockAssets.setCategory(stockAssetsPrintImportReqDTO.getCategory());
            String labelUrl = null;
            try {
                labelUrl = generateAssetCodePdf(stockAssets, user);
            } catch (IOException e) {
                // 打印异常信息
                log.error(e.getMessage());
            }
            if (null == labelUrl) {
                log.error("资产编码：" + stockAssetsPrintImportReqDTO.getAssetsCode() + "，批次号：" + stockAssetsPrintImportReqDTO.getBatchNo() + ",生成pdf失败");
            } else {
                StockAssetsPrintImport stockAssetsPrintImport = ConvertUtil.convertToType(StockAssetsPrintImport.class, stockAssetsPrintImportReqDTO);
                stockAssetsPrintImport.setLabelUrl(labelUrl);
                stockAssetsPrintImport.setStatus(StockAssetsPrintImportEnum.Status.PREPARE.getCode());
                updateStockAssetsPrintImportList.add(stockAssetsPrintImport);
            }
        }
        // 结束时间
        long endTime = System.currentTimeMillis();
        log.info("异步生成pdf完成----------------，总耗时为" + (endTime - startTime) + "ms");
        if (!CollectionUtils.isEmpty(updateStockAssetsPrintImportList)) {
            log.info("开始批量更新资产批量打印临时表数据----------------");
            int num = assetsPrintImportMapper.batchUpdateByNo(updateStockAssetsPrintImportList);
            boolean flag = num > CommonConstant.NUMBER_ZERO;
            log.info("批量更新资产批量打印临时表数据完成----------------状态为：" + flag);
        }
    }

    /**
     * @param:
     * @description: 查询资产批量导入内容
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/22
     */
    @Override
    public List<StockAssetsPrintImport> selectBatchPrintAssetsCode(StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO) {
        StockAssetsPrintImportExample stockAssetsPrintImportExample = new StockAssetsPrintImportExample();
        StockAssetsPrintImportExample.Criteria criteria = stockAssetsPrintImportExample.createCriteria();
        if (StringUtils.isNotEmpty(stockAssetsPrintImportReqDTO.getBatchNo())) {
            criteria.andBatchNoEqualTo(stockAssetsPrintImportReqDTO.getBatchNo());
        }
        if (CollectionUtils.isNotEmpty(stockAssetsPrintImportReqDTO.getIdList())) {
            criteria.andIdIn(stockAssetsPrintImportReqDTO.getIdList());
        }
        if (stockAssetsPrintImportReqDTO.getPageNum() != null) {
            stockAssetsPrintImportExample.setOffset(stockAssetsPrintImportReqDTO.getStartNum());
            stockAssetsPrintImportExample.setLimit(stockAssetsPrintImportReqDTO.getPageSize());
        }
        return stockAssetsPrintImportMapper.selectByExample(stockAssetsPrintImportExample);
    }

    /**
     * @param:
     * @description: 计算总条数
     * @return:
     * @author: <EMAIL>
     * @date: 2021/12/24
     */
    @Override
    public long countBatchPrintAssetsCode(StockAssetsPrintImportReqDTO stockAssetsPrintImportReqDTO) {
        StockAssetsPrintImportExample stockAssetsPrintImportExample = new StockAssetsPrintImportExample();
        StockAssetsPrintImportExample.Criteria criteria = stockAssetsPrintImportExample.createCriteria();
        if (StringUtils.isNotEmpty(stockAssetsPrintImportReqDTO.getBatchNo())) {
            criteria.andBatchNoEqualTo(stockAssetsPrintImportReqDTO.getBatchNo());
        }
        if (CollectionUtils.isNotEmpty(stockAssetsPrintImportReqDTO.getIdList())) {
            criteria.andIdIn(stockAssetsPrintImportReqDTO.getIdList());
        }
        return stockAssetsPrintImportMapper.countByExample(stockAssetsPrintImportExample);
    }

    /**
     * 生成pdf
     *
     * @param stockAssets
     * @return
     */
    private String generateAssetCodePdf(StockAssets stockAssets, JwtUser user) throws IOException {

        //二维码校验
        String qrBatchOperating = this.generateQr(stockAssets, user, QrEnum.type.QR);
        if (StringUtils.isBlank(qrBatchOperating)) {
            log.error("生成二维码失败,assetCode:{}", stockAssets.getAssetsCode());
            throw new ServiceUncheckedException("生成二维码失败，请重试或联系技术人员");
        }

        String barBatchOperation = this.generateQr(stockAssets, user, QrEnum.type.BAR);
        if (StringUtils.isBlank(barBatchOperation)) {
            log.error("生成条形码失败,assetCode:{}", stockAssets.getAssetsCode());
            throw new ServiceUncheckedException("生成条形码失败，请重试或联系技术人员");
        }

        final Map<String, BatchQrCodeRespDTO> qrBatchQrCodeRespDTOMap = stockQrService.getBatchQrCodeRespDTOMap(qrBatchOperating, QrEnum.codeType.QR_CODE.getValue());
        if (org.springframework.util.CollectionUtils.isEmpty(qrBatchQrCodeRespDTOMap)) {
            log.error("获取二维码Map为空");
            return null;
        }

        final Map<String, BatchQrCodeRespDTO> barBatchQrCodeRespDTOMap = stockQrService.getBatchQrCodeRespDTOMap(barBatchOperation, QrEnum.codeType.BAR_CODE.getValue());
        if (org.springframework.util.CollectionUtils.isEmpty(barBatchQrCodeRespDTOMap)) {
            log.error("获取条形码Map为空");
            return null;
        }

        List<StockAssetCodeInfo> stockAssetCodeInfoList = new ArrayList<>(CommonConstant.NUMBER_ONE);
        StockAssetCodeInfo stockAssetCodeInfo = new StockAssetCodeInfo();
        stockAssetCodeInfo.setCategoryName(stockAssets.getCategory());
        stockAssetCodeInfo.setAssetCode(stockAssets.getAssetsCode());
        stockAssetCodeInfoList.add(stockAssetCodeInfo);

        return stockAssetCodeService.generateAssetCodePdf(stockAssets.getAssetsCode(), stockAssetCodeInfoList, qrBatchQrCodeRespDTOMap, barBatchQrCodeRespDTOMap);
    }

    /**
     * 生成码
     *
     * @param stockAssets
     * @param user
     * @param type
     * @return
     */
    private String generateQr(StockAssets stockAssets, JwtUser user, QrEnum.type type) {

        StockQr stockQr = new StockQr();
        stockQr.setBizNo(stockAssets.getAssetsCode());
        stockQr.setBizType(QrEnum.bizType.ASSET_CODE.getValue());
        stockQr.setDelFlag(CommonEnum.delFlag.NO.getValue());
        stockQr.setStatus(CommonEnum.status.YES.getValue());
        stockQr.setType(type.getValue());
        List<StockQr> stockQrList = stockQrService.selectParam(stockQr, null, null, null);
        if (org.springframework.util.CollectionUtils.isEmpty(stockQrList)) {
            List<String> assetCode = new ArrayList<String>() {{
                add(stockAssets.getAssetsCode());
            }};
            return stockQrService.generateQr(assetCode, QrEnum.bizType.ASSET_CODE.getValue(), type.getValue(), user);
        }

        return stockQrList.get(CommonConstant.NUMBER_ZERO).getQrBatchOperating();
    }

}
