package com.gz.eim.am.stock.util.common;


import com.gz.eim.am.stock.annotation.NameAnnotation;
import lombok.Data;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: weijunjie
 * @date: 2021/1/7
 * @description
 */
public class ObjectFieldCompareUtil {

    /**
     * 比较两个同类对象中标记有@NameAnnotation注解的字段值是否相同，将不同的字段以及差异值封装到对象后返回
     *
     * @param laseObj
     * @param nextObj
     * @return
     */
    public static List<ChangeInfo> compareDetails(Object laseObj, Object nextObj) {
        if (laseObj == null || nextObj == null) {
            return null;
        }

        // 存放新旧对象改变的内容
        List<ChangeInfo> list = new ArrayList<ChangeInfo>();
        contrastObj(laseObj, nextObj, list);
        return list;
    }

    /**
     * 循环对比两个同类型对象的差异字段值
     *
     * @param pojo1
     * @param pojo2
     * @param diffList
     */
    public static void contrastObj(Object pojo1, Object pojo2, List<ChangeInfo> diffList) {
        try {
            Class clazz = pojo1.getClass();
            // 获取obj1的所有字段
            Field[] fields = pojo1.getClass().getDeclaredFields();
            // 遍历obj1所有字段
            for (Field field : fields) {
                //获取字段上的注解，如果没有注解标记不进行比较
                NameAnnotation nameAnnotation = field.getAnnotation(NameAnnotation.class);
                if (nameAnnotation == null) {
                    continue;
                }
                //获取字段的注释名称
                String chinaName = nameAnnotation.value();
                //获取字段的get方法
                PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                Method getMethod = pd.getReadMethod();

                // 调用pojo1的对应字段的get方法
                Object o1 = getMethod.invoke(pojo1);
                // 调用pojo2的对应字段的get方法
                Object o2 = getMethod.invoke(pojo2);

                if (!Objects.equals(o1, o2)) {
                    ChangeInfo change = new ChangeInfo();
                    change.setFieldName(field.getName());
                    change.setFieldRemarkName(chinaName);
                    change.setNewInfo(o1);
                    change.setOldInfo(o2);
                    diffList.add(change);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 存放差异字段信息的类
     */
    @Data
    public static class ChangeInfo {
        /**
         * 字段名称
         */
        public String fieldName;
        /**
         * 字段中文备注名称
         */
        public String fieldRemarkName;
        /**
         * 前面对象字段值
         */
        public Object oldInfo;
        /**
         * 后面对象字段值
         */
        public Object newInfo;
    }
}
