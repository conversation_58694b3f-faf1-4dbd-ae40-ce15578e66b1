package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSuppliesCategory;
import com.gz.eim.am.stock.entity.StockSuppliesCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSuppliesCategoryMapper {
    long countByExample(StockSuppliesCategoryExample example);

    int deleteByPrimaryKey(Long suppliesCategoryId);

    int insert(StockSuppliesCategory record);

    int insertSelective(StockSuppliesCategory record);

    List<StockSuppliesCategory> selectByExample(StockSuppliesCategoryExample example);

    StockSuppliesCategory selectByPrimaryKey(Long suppliesCategoryId);

    int updateByExampleSelective(@Param("record") StockSuppliesCategory record, @Param("example") StockSuppliesCategoryExample example);

    int updateByExample(@Param("record") StockSuppliesCategory record, @Param("example") StockSuppliesCategoryExample example);

    int updateByPrimaryKeySelective(StockSuppliesCategory record);

    int updateByPrimaryKey(StockSuppliesCategory record);
}