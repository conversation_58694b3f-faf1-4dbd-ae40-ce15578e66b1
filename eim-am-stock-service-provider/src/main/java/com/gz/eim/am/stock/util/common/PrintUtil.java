package com.gz.eim.am.stock.util.common;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.util.em.PrintEnum;
import com.gz.eim.plt.print.api.PrintApi;
import com.gz.eim.plt.print.dto.request.PrintTaskReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2020/3/26
 * @description
 */
@Slf4j
@Component
public class PrintUtil {
    @Value("${spring.application.name}")
    private String source;
    /**
     * 默认缩放比例
     */
    private static int DEFAULT_SCALING_RATIO = 100;

    /**
     * 默认打印份数
     */
    private static int DEFAULT_SERVINGS=1;

    @Autowired
    private PrintApi printApi;

    /**
     * 上传打印任务
     * @param printTaskReqDTOList
     * @return
     */
    public boolean savePrintTask(List<PrintTaskReqDTO> printTaskReqDTOList){
        if(CollectionUtils.isEmpty(printTaskReqDTOList)){
            log.error("打印任务集合为空");
            return false;
        }
        for(PrintTaskReqDTO printTaskReqDTO : printTaskReqDTOList){
            if(StringUtils.isBlank(printTaskReqDTO.getPrintKey())){
                log.error("打印任务集合存在空打印机, printTaskReqDTOList:{}", printTaskReqDTOList.toString());
                return false;
            }

            if(StringUtils.isBlank(printTaskReqDTO.getFileDownloadUrl())){
                log.error("打印任务集合存在空文件, printTaskReqDTOList:{}", printTaskReqDTOList.toString());
                return false;
            }

            if(printTaskReqDTO.getWidth() <= CommonConstant.NUMBER_ZERO){
                log.error("打印任务集合存在0长度, printTaskReqDTOList:{}", printTaskReqDTOList.toString());
                return false;
            }

            if(printTaskReqDTO.getHeight() <= CommonConstant.NUMBER_ZERO){
                log.error("打印任务集合存在0高度, printTaskReqDTOList:{}", printTaskReqDTOList.toString());
                return false;
            }

            printTaskReqDTO.setSource(source);

            if(null == PrintEnum.fileTypeEnumMap.get(printTaskReqDTO.getFileType())){
                printTaskReqDTO.setFileType(PrintEnum.fileType.PDF.getValue());
            }

            if(printTaskReqDTO.getScalingRatio() <= CommonConstant.NUMBER_ZERO){
                printTaskReqDTO.setScalingRatio(DEFAULT_SCALING_RATIO);
            }

            if(null == PrintEnum.directionEnumMap.get(printTaskReqDTO.getDirection())){
                printTaskReqDTO.setDirection(PrintEnum.direction.VERTICAL.getValue());
            }

            if(printTaskReqDTO.getDownMargin() < CommonConstant.NUMBER_ONE_POINT_ONE){
                printTaskReqDTO.setDownMargin(CommonConstant.NUMBER_ONE_POINT_ONE);
            }

            if(printTaskReqDTO.getLeftMargin() < CommonConstant.NUMBER_ONE_POINT_ONE){
                printTaskReqDTO.setLeftMargin(CommonConstant.NUMBER_ONE_POINT_ONE);
            }

            if(printTaskReqDTO.getRightMargin() < CommonConstant.NUMBER_ONE_POINT_ONE){
                printTaskReqDTO.setRightMargin(CommonConstant.NUMBER_ONE_POINT_ONE);
            }

            if(printTaskReqDTO.getTopMargin() < CommonConstant.NUMBER_ONE_POINT_ONE){
                printTaskReqDTO.setTopMargin(CommonConstant.NUMBER_ONE_POINT_ONE);
            }

            if(printTaskReqDTO.getServings() <= CommonConstant.NUMBER_ZERO){
                printTaskReqDTO.setServings(DEFAULT_SERVINGS);
            }
        }
        ResponseData responseData =  printApi.savePrintTask(printTaskReqDTOList);
        if(null == responseData || !responseData.getCode().equals(ResponseCode.SUCCESS_CODE)){
            log.error("访问打印服务失败, param:{}, responseData:{}", printTaskReqDTOList.toString(), null == responseData? null:responseData.toString());
            return false;
        }

        return true;
    }
}
