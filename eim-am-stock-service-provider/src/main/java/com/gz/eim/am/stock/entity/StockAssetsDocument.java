package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockAssetsDocument {
    private Long id;

    private String documentNo;

    private Integer type;

    private Integer transferMethod;

    private Integer headStatus;

    private Integer reasonCode;

    private String billingUser;

    private Date billingTime;

    private String remark;

    private String newAssetsKeeper;

    private String newAssetsHolder;

    private String newAssetsHolderAddress;

    private Integer delFlag;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Date needTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDocumentNo() {
        return documentNo;
    }

    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo == null ? null : documentNo.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getTransferMethod() {
        return transferMethod;
    }

    public void setTransferMethod(Integer transferMethod) {
        this.transferMethod = transferMethod;
    }

    public Integer getHeadStatus() {
        return headStatus;
    }

    public void setHeadStatus(Integer headStatus) {
        this.headStatus = headStatus;
    }

    public Integer getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(Integer reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser == null ? null : billingUser.trim();
    }

    public Date getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(Date billingTime) {
        this.billingTime = billingTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getNewAssetsKeeper() {
        return newAssetsKeeper;
    }

    public void setNewAssetsKeeper(String newAssetsKeeper) {
        this.newAssetsKeeper = newAssetsKeeper == null ? null : newAssetsKeeper.trim();
    }

    public String getNewAssetsHolder() {
        return newAssetsHolder;
    }

    public void setNewAssetsHolder(String newAssetsHolder) {
        this.newAssetsHolder = newAssetsHolder == null ? null : newAssetsHolder.trim();
    }

    public String getNewAssetsHolderAddress() {
        return newAssetsHolderAddress;
    }

    public void setNewAssetsHolderAddress(String newAssetsHolderAddress) {
        this.newAssetsHolderAddress = newAssetsHolderAddress == null ? null : newAssetsHolderAddress.trim();
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getNeedTime() {
        return needTime;
    }

    public void setNeedTime(Date needTime) {
        this.needTime = needTime;
    }
}