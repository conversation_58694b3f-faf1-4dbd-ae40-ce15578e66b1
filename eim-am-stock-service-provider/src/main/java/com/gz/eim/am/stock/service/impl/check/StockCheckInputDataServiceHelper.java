package com.gz.eim.am.stock.service.impl.check;

import com.fuu.eim.support.jwt.JwtUser;
import com.guazi.gzencrypt.common.constants.CryptType;
import com.gz.eim.am.common.enums.DateFormatterEnum;
import com.gz.eim.am.common.enums.EncryptModuleEnum;
import com.gz.eim.am.common.util.DateUtils;
import com.gz.eim.am.common.util.EncryptsPlus;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.StockAssetsCheckConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.base.StockAssetsCheckTaskDetailMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsCheckTaskMapper;
import com.gz.eim.am.stock.dto.request.check.StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO;
import com.gz.eim.am.stock.dto.request.check.StockCheckAssetsEmployeeAbnormalAssetsLineReqDTO;
import com.gz.eim.am.stock.dto.request.check.StockCheckSubmitResultSuperReqDTO;
import com.gz.eim.am.stock.dto.response.check.StockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO;
import com.gz.eim.am.stock.dto.response.check.StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.ambase.SysUserReqDO;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.util.common.FiledCompareUtil;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.MetaDataEnum;
import com.gz.eim.am.stock.util.em.StockCheckMissionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @className: StockCheckInputDataServiceHelper
 * @description: 资产盘点录入数据帮助类
 * @author: <EMAIL>
 * @date: 2023/11/8
 **/
@Slf4j
@Component
public class StockCheckInputDataServiceHelper {
    @Autowired
    private StockAssetsCheckTaskDetailMapper stockAssetsCheckTaskDetailMapper;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockAssetsCheckTaskMapper stockAssetsCheckTaskMapper;

    /**
     * @param: stockCheckSubmitResultSuperReqDTO, stockAssetsCheckTaskDetail
     * @description: 校验提交盘点结果数据
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    public String checkSubmitCheckResultSuperParams(StockCheckSubmitResultSuperReqDTO stockCheckSubmitResultSuperReqDTO, StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail) {
        if (null == stockCheckSubmitResultSuperReqDTO.getTaskDetailId() ) {
            return "盘点任务详情id不能为空!";
        }
        // 是否持有该资产 2 否 1 是
        if (stockCheckSubmitResultSuperReqDTO.getOwnFlag() != null && stockCheckSubmitResultSuperReqDTO.getOwnFlag().equals(StockCheckMissionEnum.OwnFlag.NO.getValue())) {
            if (StringUtils.isBlank(stockCheckSubmitResultSuperReqDTO.getErrMeg()) || StringUtils.isBlank(stockCheckSubmitResultSuperReqDTO.getErrDesc())) {
                return "异常信息不能为空!";
            }
        }
        // 校验当前资产是否已经被盘点 是则给出提示
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        criteria.andTaskDetailIdEqualTo(stockCheckSubmitResultSuperReqDTO.getTaskDetailId());
        criteria.andCheckFlagNotEqualTo(StockCheckMissionEnum.CheckFlag.CANCEL.getValue());
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetails = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        if(CollectionUtils.isEmpty(stockAssetsCheckTaskDetails)){
            return "当前提交的盘点任务不存在";
        }
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetailExists = stockAssetsCheckTaskDetails.get(CommonConstant.NUMBER_ZERO);
        if (StockCheckMissionEnum.CheckFlag.YES.getValue().equals(stockAssetsCheckTaskDetailExists.getCheckFlag())) {
            return "当前资产已经被盘点过了！";
        }
        BeanUtils.copyProperties(stockAssetsCheckTaskDetailExists, stockAssetsCheckTaskDetail);
        return StringConstant.EMPTY;
    }

    /**
     * @param: stockCheckSubmitResultSuperReqDTO,stockAssetsCheckTaskDetail
     * @param: loginUser
     * @description: 获取需要更新的StockAssetsCheckTaskDetail
     * @return: StockAssetsCheckTaskDetail
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    public StockAssetsCheckTaskDetail getUpdateStockAssetsCheckTaskDetail(StockCheckSubmitResultSuperReqDTO stockCheckSubmitResultSuperReqDTO, StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail,
                                                                          String loginUser) {
        //判断是否拥有该资产
        if (StockCheckMissionEnum.OwnFlag.NO.getValue().equals(stockCheckSubmitResultSuperReqDTO.getOwnFlag())) {
            //该资产未被盘点且实际数量应为0
            stockAssetsCheckTaskDetail.setOwnFlag(StockCheckMissionEnum.OwnFlag.NO.getValue());
            // 如果填写的是未拥有该资产也默认为已经盘点
            stockAssetsCheckTaskDetail.setCheckFlag(MetaDataEnum.yesOrNo.YES.getValue());
            stockAssetsCheckTaskDetail.setRealNumber(CommonConstant.NUMBER_ZERO);
            stockAssetsCheckTaskDetail.setErrMessage(stockCheckSubmitResultSuperReqDTO.getErrMeg());
            stockAssetsCheckTaskDetail.setErrDesc(stockCheckSubmitResultSuperReqDTO.getErrDesc());
            //如果员工拥有该资产为否 此时认定是有差异的
            stockAssetsCheckTaskDetail.setDifference(MetaDataEnum.yesOrNo.NO.getValue());
        }else{
            // 根据资产编码查询资产详情
            StockAssets stockAssets = stockAssetsService.selectAssetsByCode(stockAssetsCheckTaskDetail.getSnapshotAssetsCode());
            stockAssetsCheckTaskDetail.setRealAssetsCode(stockAssets.getAssetsCode());
            stockAssetsCheckTaskDetail.setRealAssetsName(stockAssets.getAssetsName());
            stockAssetsCheckTaskDetail.setRealWarehouseCode(StringUtils.isNotBlank(stockAssets.getWarehouseCode()) ? stockAssets.getWarehouseCode() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealAssetsHolder(StringUtils.isNotBlank(stockAssets.getHolder()) ? stockAssets.getHolder() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealHolderTime(stockAssets.getHolderTime());
            stockAssetsCheckTaskDetail.setRealHolderAddress(StringUtils.isNotBlank(stockAssets.getHolderAddress()) ? stockAssets.getHolderAddress() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealAssetsStatus(stockAssets.getStatus());
            stockAssetsCheckTaskDetail.setRealAssetsConditions(stockAssets.getConditions());
            stockAssetsCheckTaskDetail.setRealAssetsBrand(StringUtils.isNotBlank(stockAssets.getBrand()) ? stockAssets.getBrand() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealAssetsModel(StringUtils.isNotBlank(stockAssets.getModel()) ? stockAssets.getModel() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealAssetsCpu(StringUtils.isNotBlank(stockAssets.getExtCpu()) ? stockAssets.getExtCpu() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealRamMemory(StringUtils.isNotBlank(stockAssets.getExtRamMemory()) ? stockAssets.getExtRamMemory() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealHardDisk(StringUtils.isNotBlank(stockAssets.getExtHardDisk()) ? stockAssets.getExtHardDisk() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setRealAssetsSnno(StringUtils.isNotBlank(stockAssets.getSnCode()) ? stockAssets.getSnCode() : StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setErrMessage(StringConstant.EMPTY);
            stockAssetsCheckTaskDetail.setErrDesc(StringConstant.EMPTY);
            //如果拥有该资产 设置该资产已经盘点过了
            stockAssetsCheckTaskDetail.setCheckFlag(MetaDataEnum.yesOrNo.YES.getValue());
            //账目数量为1
            stockAssetsCheckTaskDetail.setRealNumber(CommonConstant.NUMBER_ONE);
            stockAssetsCheckTaskDetail.setOwnFlag(StockCheckMissionEnum.OwnFlag.YES.getValue());
            boolean isSame = compareDifference(stockAssetsCheckTaskDetail);
            //设置是否有差异 0 有差异 1 无差异
            stockAssetsCheckTaskDetail.setDifference(compareDifference(stockAssetsCheckTaskDetail) ? MetaDataEnum.yesOrNo.YES.getValue() : MetaDataEnum.yesOrNo.NO.getValue());
        }
        stockAssetsCheckTaskDetail.setUpdatedBy(loginUser);
        stockAssetsCheckTaskDetail.setUpdatedAt(new Date());
        return stockAssetsCheckTaskDetail;
    }

    /**
     * @param: stockAssetsCheckTaskDetail
     * @description: 比较差异
     * @return: boolean
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    private boolean compareDifference(StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail) {
        // 资产名称
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsName(), stockAssetsCheckTaskDetail.getRealAssetsName())){
            return false;
        }
        // 资产编码
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsCode(), stockAssetsCheckTaskDetail.getRealAssetsCode())){
            return false;
        }
        // sn码
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotSnNo(), stockAssetsCheckTaskDetail.getRealAssetsSnno())){
            return false;
        }
        // 序列号
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotSnNo(), stockAssetsCheckTaskDetail.getRealAssetsSnno())){
            return false;
        }
        // 品牌
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotBrand(), stockAssetsCheckTaskDetail.getRealAssetsBrand())){
            return false;
        }
        // 型号
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotModel(), stockAssetsCheckTaskDetail.getRealAssetsModel())){
            return false;
        }
        // 使用人位置
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotHolderAddress(), stockAssetsCheckTaskDetail.getRealHolderAddress())){
            return false;
        }
        // 资产状态
        if(!FiledCompareUtil.compareIntegerUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus(), stockAssetsCheckTaskDetail.getRealAssetsStatus())){
            return false;
        }
        // 资产使用情况
        if(!FiledCompareUtil.compareIntegerUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsConditions(), stockAssetsCheckTaskDetail.getRealAssetsConditions())){
            return false;
        }
        // 使用人位置
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotWarehouseCode(), stockAssetsCheckTaskDetail.getRealWarehouseCode())){
            return false;
        }
        // 使用人
        if(!FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsHolder(), stockAssetsCheckTaskDetail.getRealAssetsHolder())){
            return false;
        }
        return true;
    }

    /**
     * @param: sysUserBasicInfo,stockAssetsCheckTask
     * @description: 获取库存盘点资产员工异常资产响应头
     * @return: StockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO
     * @author: <EMAIL>
     * @date: 2023/11/10
     */
    public StockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO getStockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO(SysUserBasicInfo sysUserBasicInfo, StockAssetsCheckTask stockAssetsCheckTask) {
        String checkPerson = sysUserBasicInfo.getEmpId();
        StockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO = new StockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO();
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setCheckPerson(checkPerson);
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setCheckPersonName(sysUserBasicInfo.getName());
        String checkUserEmail = sysUserBasicInfo.getEmail();
        if(StringUtils.isNotBlank(checkUserEmail)){
            stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setCheckPersonEmail(checkUserEmail.substring(CommonConstant.NUMBER_ZERO, checkUserEmail.indexOf(CommonConstant.GUA_ZI_EMAIL_SUFFIX)));
        }
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setHpsJobcdDescr(sysUserBasicInfo.getHpsJobcdDescr());
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setEntryLocationName(sysUserBasicInfo.getEntryLocationName());
        // 查询上级领导信息
        String supervisorId = sysUserBasicInfo.getSupervisorId();
        if(StringUtils.isNotBlank(supervisorId)){
            stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setSuperUser(supervisorId);
            SysUserReqDO sysUserReqDO = new SysUserReqDO();
            sysUserReqDO.setEmpId(supervisorId);
            SysUser sysUser = ambaseCommonService.selectUserOne(sysUserReqDO);
            if(sysUser != null){
                stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setSuperUserName(sysUser.getName());
                String superUserEmail = sysUserBasicInfo.getEmail();
                if(StringUtils.isNotBlank(superUserEmail)){
                    stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setSuperUserEmail(checkUserEmail.substring(CommonConstant.NUMBER_ZERO, superUserEmail.indexOf(CommonConstant.GUA_ZI_EMAIL_SUFFIX)));
                }
            }
        }
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setDeptFullName(sysUserBasicInfo.getDeptFullName());
        // 进行解密
        String phone = sysUserBasicInfo.getPhone();
        if(StringUtils.isNotBlank(phone)){
            try {
                String decryptPhone = EncryptsPlus.decrypt(EncryptModuleEnum.DATA.value, CryptType.PHONE, phone);
                if(StringUtils.isNotBlank(decryptPhone)){
                    phone = decryptPhone;
                }
            } catch (Exception e) {
                log.error("StockCheckInputDataServiceHelper.getStockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO解密电话号失败，电话号为：{}，失败信息为：{}", phone, e.getMessage());
            }
        }
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setPhone(phone);
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setFirstHireDt(sysUserBasicInfo.getFirstHireDt());
        // 查询盘点人对应的异常盘点资产并返回
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria stockAssetsCheckTaskDetailExampleCriteria = stockAssetsCheckTaskDetailExample.createCriteria();
        stockAssetsCheckTaskDetailExampleCriteria.andCheckTaskIdEqualTo(stockAssetsCheckTask.getCheckTaskId());
        stockAssetsCheckTaskDetailExampleCriteria.andCheckPeopleLike(StringConstant.PERCENT + checkPerson + StringConstant.PERCENT);
        stockAssetsCheckTaskDetailExampleCriteria.andOwnFlagNotEqualTo(StockCheckMissionEnum.OwnFlag.YES.getValue());
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        if(CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            return stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO;
        }
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = stockAssetsCheckTaskDetailList.get(CommonConstant.NUMBER_ZERO);
        Date approveTime = stockAssetsCheckTaskDetail.getApproveTime();
        if(approveTime != null){
            stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setApproveTime(DateUtils.dateString(approveTime, DateFormatterEnum.DATE_YMD_HMS));
        }
        List<StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO> stockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList = getStockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList(stockAssetsCheckTaskDetailList);
        stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO.setStockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList(stockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList);
        return stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO;
    }

    /**
     * @param: stockAssetsCheckTaskDetailList
     * @description: 获取库存盘点资产员工异常资产响应行
     * @return: List<StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO>
     * @author: <EMAIL>
     * @date: 2023/11/10
     */
    private List<StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO> getStockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList(List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList) {
        Set<String> keeperIdSet = new HashSet<>();
        List<String> assetsCodeList = new ArrayList<>(stockAssetsCheckTaskDetailList.size());
        for (StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail : stockAssetsCheckTaskDetailList) {
            keeperIdSet.add(stockAssetsCheckTaskDetail.getAssetsKeeper());
            assetsCodeList.add(stockAssetsCheckTaskDetail.getSnapshotAssetsCode());
        }
        Map<String, SysUser> sysUserMap = null;
        if(!CollectionUtils.isEmpty(keeperIdSet)){
            sysUserMap = ambaseCommonService.selectSysUserMapByIds(new ArrayList<>(keeperIdSet));
        }
        Map<String, StockAssets> assetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        List<StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO> stockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList = new ArrayList<>(stockAssetsCheckTaskDetailList.size());
        for (StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail : stockAssetsCheckTaskDetailList) {
            StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO = new StockCheckAssetsEmployeeAbnormalAssetsLineRespDTO();
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setTaskDetailId(stockAssetsCheckTaskDetail.getTaskDetailId());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setDealResult(stockAssetsCheckTaskDetail.getDealResult());
            String snapshotAssetsCode = stockAssetsCheckTaskDetail.getSnapshotAssetsCode();
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setAssetsCode(snapshotAssetsCode);
            StockAssets stockAssets = assetsMap.get(snapshotAssetsCode);
            if(stockAssets != null){
                stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setAssetsId(stockAssets.getAssetsId());
            }
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setSnCode(stockAssetsCheckTaskDetail.getSnapshotSnNo());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setAssetsName(stockAssetsCheckTaskDetail.getSnapshotAssetsName());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setCategory(stockAssetsCheckTaskDetail.getSnapshotAssetsCategory());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setStatusName(AssetsEnum.statusTypeMap.get(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()));
            Date snapshotHolderTime = stockAssetsCheckTaskDetail.getSnapshotHolderTime();
            if(snapshotHolderTime != null){
                stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setHolderTime(DateUtils.dateString(snapshotHolderTime, DateFormatterEnum.DATE_YMD_HMS));
            }
            String assetsKeeper = stockAssetsCheckTaskDetail.getAssetsKeeper();
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setAssetsKeeper(assetsKeeper);
            if(StringUtils.isNotBlank(assetsKeeper) && MapUtils.isNotEmpty(sysUserMap)){
                SysUser sysUser = sysUserMap.get(assetsKeeper);
                if(sysUser != null){
                    stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setAssetsKeeperName(sysUser.getName());
                }
            }
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setAssetsKeeper(stockAssetsCheckTaskDetail.getAssetsKeeper());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setErrDesc(stockAssetsCheckTaskDetail.getErrDesc());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO.setErrMessage(stockAssetsCheckTaskDetail.getErrMessage());
            stockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList.add(stockCheckAssetsEmployeeAbnormalAssetsLineRespDTO);
        }
        return stockCheckAssetsEmployeeAbnormalAssetsLineRespDTOList;
    }

    /**
     * @param: stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO,updateStockAssetsCheckTaskDetailList
     * @param: loginUser
     * @description: 校验参数是否合法
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/11/13
     */
    public String checkUpdateEmployeeAbnormalCheckAssetsParams(StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO, List<StockAssetsCheckTaskDetail> updateStockAssetsCheckTaskDetailList,
                                                               JwtUser loginUser) {
        if(null == stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO){
            return "请求参数不能为空";
        }
        String checkTaskNo = stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO.getCheckTaskNo();
        if(StringUtils.isBlank(checkTaskNo)){
            return "盘点任务号不能为空";
        }
        String checkPerson = stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO.getCheckPerson();
        if(StringUtils.isBlank(checkPerson)){
            return "盘点人不能为空";
        }
        StockAssetsCheckTaskExample stockAssetsCheckTaskExample = new StockAssetsCheckTaskExample();
        StockAssetsCheckTaskExample.Criteria criteria = stockAssetsCheckTaskExample.createCriteria();
        criteria.andCheckTaskNoEqualTo(checkTaskNo);
        List<StockAssetsCheckTask> stockAssetsCheckTaskList = stockAssetsCheckTaskMapper.selectByExample(stockAssetsCheckTaskExample);
        if(CollectionUtils.isEmpty(stockAssetsCheckTaskList)){
            return "盘点任务好不存在";
        }
        StockAssetsCheckTask stockAssetsCheckTask = stockAssetsCheckTaskList.get(CommonConstant.NUMBER_ZERO);
        return checkUpdateEmployeeAbnormalCheckAssetsLine(stockAssetsCheckTask, stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO, updateStockAssetsCheckTaskDetailList, loginUser);
    }

    /**
     * @param: stockAssetsCheckTask,stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO
     * @param: updateStockAssetsCheckTaskDetailList,loginUser
     * @description: 校验异常资产行信息
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/11/13
     */
    private String checkUpdateEmployeeAbnormalCheckAssetsLine(StockAssetsCheckTask stockAssetsCheckTask, StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO,
                                                              List<StockAssetsCheckTaskDetail> updateStockAssetsCheckTaskDetailList, JwtUser loginUser) {
        List<StockCheckAssetsEmployeeAbnormalAssetsLineReqDTO> stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList = stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO.getStockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList();
        if(CollectionUtils.isEmpty(stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList)){
            return "异常资产行信息不能为空";
        }
        Set<Long> idSet = new HashSet<>(stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList.size());
        for (int i = 0; i < stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList.size(); i++) {
            StockCheckAssetsEmployeeAbnormalAssetsLineReqDTO stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO = stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList.get(i);
            if(null == stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO){
                return "第" + (i + 1) + "行的信息不能为空";
            }
            Long taskDetailId = stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO.getTaskDetailId();
            if(null == taskDetailId){
                return "第" + (i + 1) + "行的id不能为空";
            }
            if(!idSet.add(taskDetailId)){
                return "第" + (i + 1) + "行的id重复";
            }
            String dealResult = stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO.getDealResult();
            if(StringUtils.isNotBlank(dealResult) && dealResult.length() > StockAssetsCheckConstant.ASSETS_CHECK_DETAIL_DEAL_RESULT){
                return "第" + (i + 1) + "行的资产处理结果长度不能大于300";
            }
        }
        // 查询资产盘点详情信息
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        criteria.andTaskDetailIdIn(new ArrayList<>(idSet));
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        if(CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            return "异常资产行信息均不存在";
        }
        Long checkTaskId = stockAssetsCheckTask.getCheckTaskId();
        String checkPerson = stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO.getCheckPerson();
        Map<Long, StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailMap = stockAssetsCheckTaskDetailList.stream().collect(Collectors.toMap(StockAssetsCheckTaskDetail::getTaskDetailId, stockAssetsCheckTaskDetail -> stockAssetsCheckTaskDetail, (k1, k2) -> k2));
        Date currentTime = new Date();
        String employeeCode = loginUser.getEmployeeCode();
        for (int i = 0; i < stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList.size(); i++) {
            StockCheckAssetsEmployeeAbnormalAssetsLineReqDTO stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO = stockCheckAssetsEmployeeAbnormalAssetsLineReqDTOList.get(i);
            Long taskDetailId = stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO.getTaskDetailId();
            StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = stockAssetsCheckTaskDetailMap.get(taskDetailId);
            if(null == stockAssetsCheckTaskDetail){
                return "第" + (i + 1) + "行的资产异常信息不存在";
            }
            if(!checkTaskId.equals(stockAssetsCheckTaskDetail.getCheckTaskId())){
                return "第" + (i + 1) + "行的盘点任务详情和盘点任务单号不匹配";
            }
            if(!stockAssetsCheckTaskDetail.getCheckPeople().contains(checkPerson)){
                return "第" + (i + 1) + "行的盘点任务详情的盘点人和当前盘点人不匹配";
            }
            StockAssetsCheckTaskDetail updateStockAssetsCheckTaskDetail = new StockAssetsCheckTaskDetail();
            updateStockAssetsCheckTaskDetail.setTaskDetailId(taskDetailId);
            updateStockAssetsCheckTaskDetail.setDealResult(stockCheckAssetsEmployeeAbnormalAssetsLineReqDTO.getDealResult());
            updateStockAssetsCheckTaskDetail.setUpdatedBy(employeeCode);
            updateStockAssetsCheckTaskDetail.setUpdatedAt(currentTime);
            updateStockAssetsCheckTaskDetailList.add(updateStockAssetsCheckTaskDetail);
        }
        return StringConstant.EMPTY;
    }
}
