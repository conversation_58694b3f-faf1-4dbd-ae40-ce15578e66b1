package com.gz.eim.am.stock.ext.dao.supplies.impl;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.ext.dao.supplies.StockSuppliesInventoryDao;
import com.gz.eim.am.stock.entity.StockSuppliesInventory;
import com.gz.eim.am.stock.entity.StockSuppliesInventoryExample;
import com.gz.eim.am.stock.mapper.base.StockSuppliesInventoryMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: lishuyang
 * @date: 2020/7/9
 * <p>
 *    物料库存属性dao层实现类
 * </p>
 */
@Repository
public class StockSuppliesInventoryDaoImpl implements StockSuppliesInventoryDao {
    @Resource
    private StockSuppliesInventoryMapper mapper;

    @Override
    public StockSuppliesInventory getBySuppliesCodeAndWarehouseType(String suppliesCode , Integer warehouseType) {
        if(StringUtils.isBlank (suppliesCode) || Objects.isNull (warehouseType)){
            return null;
        }

        StockSuppliesInventoryExample example = new StockSuppliesInventoryExample ();
        StockSuppliesInventoryExample.Criteria criteria = example.createCriteria ();
        criteria.andSuppliesCodeEqualTo (suppliesCode);
        criteria.andWarehouseTypeCodeEqualTo (warehouseType);
        criteria.andIsDelFlagEqualTo (CommonConstant.NUMBER_ZERO);
        List<StockSuppliesInventory> stockSuppliesInventoryList = mapper.selectByExample (example);
        if(CollectionUtils.isEmpty (stockSuppliesInventoryList)){
            return null;
        }

        return stockSuppliesInventoryList.get(CommonConstant.NUMBER_ZERO);
    }
}
