package com.gz.eim.am.stock.service.order.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;

import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;

/**
 * @author: weijunjie
 * @date: 2020/9/1
 * @description
 */
public interface StockPlanLicenseLoanService {

    /**
     * 保存执照借用申请单据
     * @param deliveryPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData savePlanLicenseLoad(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws ParseException;

    /**
     * 根据执照借用单编号获取工作流表单行集合
     * @param deliveryPlanNo
     * @return
     */
    ResponseData selectWflFormLineListByDeliveryPlanNo(String deliveryPlanNo);

    /**
     * 根据申请单号查询申请出库单详细信息
     * @param deliveryPlanNo
     * @param user
     * @return
     */
    ResponseData selectPlanLicenseLoadByNo(String deliveryPlanNo, JwtUser user) throws ParseException;

    /**
     * 执照借用申请单确认出库
     * @param deliveryPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData planLicenseLoadBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws InvocationTargetException, IllegalAccessException, ParseException, RuntimeException;

    /**
     * 查询执照
     * @param assetsSearchDTO
     * @return
     */
    ResponseData selectAssets(AssetsSearchDTO assetsSearchDTO);

    /**
     * 执照批量领取
     * @param batchId
     * @param user
     * @return
     */
    ResponseData assetPlanLicenseLoadInitialization(Integer batchId, JwtUser user);

    /**
     * 电子版执照借用单据保存
     * @param deliveryPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData savePlanLicenseElectron(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws ParseException;
}
