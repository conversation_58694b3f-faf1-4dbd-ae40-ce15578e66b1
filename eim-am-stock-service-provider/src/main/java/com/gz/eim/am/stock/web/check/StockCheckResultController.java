package com.gz.eim.am.stock.web.check;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.JsonUtil;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.stock.api.check.StockCheckResultControllerApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListLineReqDTO;
import com.gz.eim.am.stock.dto.response.check.CheckDifferenceListLineRespDTO;
import com.gz.eim.am.stock.entity.vo.download.CheckDifferenceListLineEntity;
import com.gz.eim.am.stock.service.check.CheckDifferenceListLineService;
import com.gz.eim.am.stock.service.check.StockCheckResultService;
import com.gz.eim.am.stock.service.impl.check.StockCheckMissionServiceImpl;
import com.fuu.eim.tool.excel.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/11/20
 * @description 盘点结果处理
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/checkResult")
public class StockCheckResultController implements StockCheckResultControllerApi {

    @Value("${namespace.name}")
    private String nameSpace;
    @Autowired
    RedisUtil redisUtil;
    @Autowired
    StockCheckResultService stockCheckResultService;
    @Autowired
    CheckDifferenceListLineService checkDifferenceListLineService;

    @Override
    public ResponseData checkResultInit(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        log.info("/api/am/stock/checkResult/init{}",assetQueryScopeReqDTO.getTakingPlanNo());
        ResponseData res = null;
        try {
            res = stockCheckResultService.init(assetQueryScopeReqDTO);
        }catch(ServiceUncheckedException e){
            return ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("盘点计划初始化异常清单报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData checkResultSave(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) {
        log.info("/api/am/stock/checkResult/save{}",JsonUtil.getJsonString(checkDifferenceListHeadReqDTO));
        ResponseData res = null;
        String lockKey = RedisKeyConstants.STOCK_CHECK_RESULT_SAVE + checkDifferenceListHeadReqDTO.getTakingPlanNo();
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                res = stockCheckResultService.save(checkDifferenceListHeadReqDTO);
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("盘点差异常清单保存报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryCheckResult(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        log.info("/api/am/stock/checkResult/query{}",assetQueryScopeReqDTO.getTakingPlanNo());
        ResponseData res = null;
        try {
            res = stockCheckResultService.queryCheckResult(assetQueryScopeReqDTO);
        } catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e) {
            log.error("盘点差异常清单查询报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData downLoadCheckResult(Long headId, HttpServletRequest request, HttpServletResponse response) {
        log.info("/api/am/stock/checkResult/downLoad{}",headId);
        //获取单据下的盘点异常资产明细
        AssetQueryScopeReqDTO assetQueryScopeReqDTO = new AssetQueryScopeReqDTO();
        assetQueryScopeReqDTO.setHeadId(headId);
        assetQueryScopeReqDTO.setNoPaging(true);
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = checkDifferenceListLineService.getLineRespDTOs(assetQueryScopeReqDTO);
        if (CollectionUtils.isNotEmpty(checkDifferenceListLineRespDTOList)){
            //根据资产类型查询配置表属性名称
            List<CheckDifferenceListLineEntity> checkDifferenceListLineEntityList = new ArrayList<>(checkDifferenceListLineRespDTOList.size());

            checkDifferenceListLineRespDTOList.stream().forEach(dto->{
                CheckDifferenceListLineEntity checkDifferenceListLineEntity = new CheckDifferenceListLineEntity();
                BeanUtils.copyProperties(dto,checkDifferenceListLineEntity);
                //人员字段拼接为名称+工号
                if (StringUtils.isNotBlank(dto.getRealAssetsHolder())){
                    checkDifferenceListLineEntity.setRealAssetsHolderName(dto.getRealAssetsHolderName()+" "+dto.getRealAssetsHolder());
//                    checkDifferenceListLineEntity.setRealAssetsHolderDept(dto.getRealAssetsHolderDeptName()+" "+dto.getRealAssetsHolderDept());
                    checkDifferenceListLineEntity.setRealAssetsHolderDept(dto.getRealAssetsHolderDeptName());
                    checkDifferenceListLineEntity.setRealAssetsHolderAddress(dto.getRealAssetsHolderAddress());
                }
                if (StringUtils.isNotBlank(dto.getSnapshotAssetsHolder())){
                    checkDifferenceListLineEntity.setSnapshotAssetsHolderName(dto.getSnapshotAssetsHolderName()+" "+dto.getSnapshotAssetsHolder());
//                    checkDifferenceListLineEntity.setSnapshotAssetsHolderDept(dto.getSnapshotAssetsHolderDeptName()+" "+dto.getSnapshotAssetsHolderDept());
                    checkDifferenceListLineEntity.setSnapshotAssetsHolderDept(dto.getSnapshotAssetsHolderDeptName());
                    checkDifferenceListLineEntity.setSnapshotAssetsHolderAddress(dto.getSnapshotAssetsHolderAddress());
                }
//                checkDifferenceListLineEntity.setCheckResult(dto.getAdviseHandleMethodName());
                checkDifferenceListLineEntityList.add(checkDifferenceListLineEntity);
            });
            //生存excel文件
            final String fileName = "盘点差异清单明细.xlsx";
            ExcelUtil.createExcelWithBuffer(checkDifferenceListLineEntityList, fileName, request, response);
        }
        return ResponseData.createFailResult("无数据导出");
    }


    @Override
    public ResponseData saveCheckResultRemark(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) {
        log.info("/api/am/stock/checkResult/remark");
        ResponseData res = null;
        try {
            res = stockCheckResultService.saveCheckResultRemark(checkDifferenceListHeadReqDTO);
        } catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e) {
            log.error("盘点差异清单行备注更新报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData getFileUrlListByRelId(String takingPlanNo, String assetsCode) {
        log.info("/api/am/stock/checkResult/file-list");
        ResponseData res = null;
        try {
            res = stockCheckResultService.getFileUrlListByRelId(takingPlanNo,assetsCode);
        } catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e) {
            log.error("盘点差异清单查询图片信息异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * 定时任务接口，根据盘点计划截止日期结束盘点任务
     * @return
     */
    @RequestMapping("/takingPlanEnd")
    public ResponseData checkEndTimer() {
        log.info("/api/am/stock/checkResult/takingPlanEnd");
        ResponseData res = null;
        try {
            res = stockCheckResultService.takingPlanEnd();
        } catch (Exception e) {
            log.error("盘点计划定时结束报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
