package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.assets.AssetTransferHeadReqDTO;
import com.gz.eim.am.stock.entity.vo.download.ExportAssetsDocumentDetailEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/5/7
 * @description 资产转移服务类
 */
public interface StockAssetTransferService {

    /**
     * 附件上传
     * @param files
     * @param user
     * @return
     */
    ResponseData upload(MultipartFile[] files,JwtUser user);

    /**
     * 资产管理员转移单新建
     * @param assetTransferHeadReqDTO
     * @param user
     * @return
     */
    ResponseData save(AssetTransferHeadReqDTO assetTransferHeadReqDTO, JwtUser user);

    /**
     * 资产持有人转移单新建
     * @param assetTransferHeadReqDTO
     * @param user
     * @return
     */
    ResponseData saveHolder(AssetTransferHeadReqDTO assetTransferHeadReqDTO, JwtUser user);


    /**
     * 资产转移分页查询
     * @param assetTransferHeadReqDTO
     * @return
     */
    ResponseData selectAssetsTransferOut(AssetTransferHeadReqDTO assetTransferHeadReqDTO);

    /**
     * 资产转移详情查询
     * @param id
     * @return
     */
    ResponseData selectAssetsDocumentById(long id, String bizId);


    /**
     * 资产转移导出
     * @param documentNo
     * @return
     */
    List<ExportAssetsDocumentDetailEntity> selectAssetsDetailEntity(String documentNo);

    /**
     * 获取资产转移单编号
     *
     * @return
     */
    String getDocumentNo();

}
