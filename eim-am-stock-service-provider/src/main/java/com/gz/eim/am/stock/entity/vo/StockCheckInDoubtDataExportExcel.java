package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import lombok.Data;

/**
 * @className: StockCheckInDoubtDataRespDO
 * @description: 库存盘点差异数据返回值
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Data
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockCheckInDoubtDataExportExcel implements ExportModel {
//    /**
//     * 盘点人姓名
//     */
//    @ExportField(name = "盘点人姓名")
//    private String checkPeopleName;
    /**
     * 盘点人
     */
    @ExportField(name = "盘点人")
    private String checkPeople;
    /**
     * 盘点时间
     */
    @ExportField(name = "盘点时间")
    private String checkTime;
    /**
     * 盘点人电话
     */
    @ExportField(name = "盘点人电话")
    private String checkPeoplePhone;
    /**
     * 账目使用人
     */
    @ExportField(name = "账目使用人")
    private String holder;
    /**
     * 资产编码
     */
    @ExportField(name = "资产编码")
    private String assetsCode;
    /**
     * 资产编码
     */
    @ExportField(name = "设备序列号")
    private String snCode;
    /**
     * 资产名称
     */
    @ExportField(name = "资产名称")
    private String assetsName;
    /**
     * 资产使用状态
     */
    @ExportField(name = "资产使用状态")
    private String statusName;
    /**
     * 领用时间
     */
    @ExportField(name = "领用时间")
    private String holderTime;
//    /**
//     * 盘点人邮箱
//     */
//    @ExportField(name = "盘点人邮箱")
//    private String checkPeopleEmail;
    /**
     * 入职地点
     */
    @ExportField(name = "入职地点")
    private String entryLocationName;
    /**
     * 上级
     */
    @ExportField(name = "上级")
    private String superUser;
//    /**
//     * 上级姓名
//     */
//    @ExportField(name = "上级姓名")
//    private String superUserName;
//    /**
//     * 上级邮箱
//     */
//    @ExportField(name = "上级邮箱")
//    private String superUserEmail;
    /**
     * 上级电话
     */
    @ExportField(name = "上级电话")
    private String superUserPhone;
    /**
     * 盘点异常类型
     */
    @ExportField(name = "盘点异常类型")
    private String errMessage;
    /**
     * 异常说明
     */
    @ExportField(name = "异常说明")
    private String errDesc;
    /**
     * 盘点计划名
     */
    private String takingPlanName;

    @Override
    public String getSheetName() {
        return "盘点存疑报表";
    }
}
