package com.gz.eim.am.stock.util.excel;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.ImportMetaData;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.constant.ExportType;
import com.gz.eim.am.stock.constant.PropertyConstants;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


public class ExcelPlusUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelPlusUtil.class);

    public ExcelPlusUtil() {
    }

    private static String[][] getExcelData(InputStream is, int ignoreRows) throws IOException, InvalidFormatException {
        List<String[]> result = new ArrayList();
        Workbook workbook = WorkbookFactory.create(is);
        int sheetNum = 1;
        int cellSize = 0;

        for (int i = 0; i < sheetNum; ++i) {
            Sheet sheet = workbook.getSheetAt(i);

            for (int rowIndex = ignoreRows; rowIndex <= sheet.getLastRowNum(); ++rowIndex) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    int tempCellSize = row.getLastCellNum() + 1;
                    if (tempCellSize > cellSize) {
                        cellSize = tempCellSize;
                    }

                    String[] cellValues = new String[cellSize];
                    Arrays.fill(cellValues, "");
                    boolean hasValue = false;

                    for (int columnIndex = 0; columnIndex <= row.getLastCellNum(); ++columnIndex) {
                        String cellValue = null;
                        Cell cell = row.getCell(columnIndex);
                        if (cell != null) {
                            cellValue = getCellValue(cell);
                        }

                        if (cellValue != null) {
                            cellValue = cellValue.trim();
                        }

                        cellValues[columnIndex] = cellValue;
                        hasValue = true;
                    }

                    if (hasValue) {
                        result.add(cellValues);
                    }
                }
            }
        }

        String[][] returnArray = new String[result.size()][cellSize];

        for (int i = 0; i < returnArray.length; ++i) {
            returnArray[i] = (String[]) ((String[]) result.get(i));
        }

        return returnArray;
    }


    /**
     * 校验Excel文件格式
     */
    public static Integer validExcelFormat(InputStream is, Class clazz, List<String> errs) {

        Integer dataTotal = 0;
        try {
            Workbook workbook = WorkbookFactory.create(is);
            ImportModelPlus annotation = (ImportModelPlus) clazz.getAnnotation(ImportModelPlus.class);
            if (annotation == null || StringUtils.isBlank(annotation.sheetName())) {
                errs.add("annotation 配置错误");
                return dataTotal;
            }

            int headerIndex = annotation.headerIndex();
            int ignoreRows = annotation.ignoreRows();
            int cellSize = 0;
            // sheet = workbook.getSheetAt(annotation.sheetIndex());
            Sheet sheet = workbook.getSheet(annotation.sheetName());
            if (sheet == null) {
                errs.add("Excel 未找到 sheet:" + annotation.sheetName());
                return dataTotal;
            }
            Row header = sheet.getRow(headerIndex);
            Map<String, ImportMetaData> allMetaDataMap = getAllMap(clazz);
            Map<Integer, ImportMetaData> headerMetaMap = new HashMap(PropertyConstants.NUMBER_10);

            int rowIndex;
            for (rowIndex = 0; rowIndex <= header.getLastCellNum(); ++rowIndex) {
                Cell cell = header.getCell(rowIndex);
                if (cell != null && allMetaDataMap.get(cell.getStringCellValue()) != null) {
                    headerMetaMap.put(rowIndex, allMetaDataMap.get(cell.getStringCellValue()));
                }
            }
            if (headerMetaMap.size() == 0) {
                errs.add("列头缺失");
                return dataTotal;
            }
            dataTotal = sheet.getLastRowNum() - (ignoreRows + headerIndex - 1);
            LOGGER.info("文件数据量:" + dataTotal);
            if (dataTotal < 1) {
                errs.add("无数据行");
            }

        } catch (Exception var21) {
            LOGGER.error("文件格式验证异常", var21);
        }
        return dataTotal;
    }

    public static <T> List<T> importExcel(InputStream is, Class<T> clazz) {
        ArrayList result = null;

        try {
            Workbook workbook = WorkbookFactory.create(is);
            ImportModelPlus annotation = (ImportModelPlus) clazz.getAnnotation(ImportModelPlus.class);
            if (annotation == null || null == annotation.sheetName()) {
                return null;
            }

            int headerIndex = annotation.headerIndex();
            int ignoreRows = annotation.ignoreRows();
            int cellSize = 0;
            // sheet = workbook.getSheetAt(annotation.sheetIndex());
            Sheet sheet = workbook.getSheet(annotation.sheetName());
            Row header = sheet.getRow(headerIndex);
            Map<String, ImportMetaData> allMetaDataMap = getAllMap(clazz);
            Map<Integer, ImportMetaData> headerMetaMap = new HashMap(PropertyConstants.NUMBER_10);

            int rowIndex;
            for (rowIndex = 0; rowIndex <= header.getLastCellNum(); ++rowIndex) {
                Cell cell = header.getCell(rowIndex);
                if (cell != null && allMetaDataMap.get(cell.getStringCellValue()) != null) {
                    headerMetaMap.put(rowIndex, allMetaDataMap.get(cell.getStringCellValue()));
                }
            }

            result = new ArrayList();

            for (rowIndex = ignoreRows; rowIndex <= sheet.getLastRowNum(); ++rowIndex) {
                T t = clazz.newInstance();
                Row row = sheet.getRow(rowIndex);
                if (Objects.nonNull(row)) {
                    for (int columnIndex = 0; columnIndex <= row.getLastCellNum(); ++columnIndex) {
                        ImportMetaData importMetaData = (ImportMetaData) headerMetaMap.get(columnIndex);
                        if (importMetaData != null) {
                            Object cellValue = null;
                            Cell cell = row.getCell(columnIndex);
                            ExportField importAnnotation = importMetaData.getImportAnnotation();
                            Field importFiled = importMetaData.getImportFiled();
                            if (cell != null) {
                                cellValue = getCellValue(cell, importAnnotation);
                            }

                            importFiled.setAccessible(true);
                            importFiled.set(t, cellValue);
                            importFiled.setAccessible(false);
                        }
                    }

                    result.add(t);
                }
            }
        } catch (Exception var21) {
            LOGGER.error("导入失败", var21);
        }

        return result;
    }

    private static Map<String, ImportMetaData> getAllMap(Class<?> clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        Map<String, ImportMetaData> map = new HashMap(PropertyConstants.NUMBER_10);
        Field[] var3 = declaredFields;
        int var4 = declaredFields.length;

        for (int var5 = 0; var5 < var4; ++var5) {
            Field declaredField = var3[var5];
            ExportField[] annotation = (ExportField[]) ((ExportField[]) declaredField.getAnnotationsByType(ExportField.class));
            if (ArrayUtils.isNotEmpty(annotation)) {
                ImportMetaData value = new ImportMetaData();
                value.setImportAnnotation(annotation[0]);
                value.setImportFiled(declaredField);
                map.put(annotation[0].name(), value);
            }
        }

        return map;
    }

    private static String getCellValue(Cell cell) {
        String cellValue = null;
        int cellType = cell.getCellType();
        String temp;
        switch (cellType) {
            case 0:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = cell.getDateCellValue();
                    if (date != null) {
                        cellValue = String.valueOf(dateFormat.format(date));
                    }
                } else {
                    cell.setCellType(1);
                    temp = cell.getRichStringCellValue().getString();
                    cellValue = getNumberStr(temp);
                }
                break;
            case 1:
                cellValue = cell.getRichStringCellValue().getString();
                break;
            case 2:
                temp = cell.getCellFormula();
                if (null != temp) {
                    cellValue = temp.replaceAll("#N/A", "").trim();
                }
                break;
            case 3:
                cellValue = null;
                break;
            case 4:
                cellValue = Boolean.toString(cell.getBooleanCellValue());
                break;
            case 5:
                cellValue = null;
                break;
            default:
                cellValue = "";
        }

        return cellValue;
    }

    private static Object getCellValue(Cell cell, ExportField exportField) {
        String cellValue = null;
        int cellType = cell.getCellType();
        switch (cellType) {
            case 0:
                ExportType importType = exportField.type();
                if (DateUtil.isCellDateFormatted(cell) && importType.equals(ExportType.DATE)) {
                    return cell.getDateCellValue();
                }

                if (importType.equals(ExportType.STRING)) {
                    cell.setCellType(1);
                    return getNumberStr(cell.getRichStringCellValue().getString());
                }

                if (importType.equals(ExportType.BIGDECIMAL)) {
                    return new BigDecimal(cell.getNumericCellValue());
                }

                return cell.getNumericCellValue();
            case 1:
                cellValue = cell.getRichStringCellValue().getString();
                break;
            case 2:
                String temp = cell.getCellFormula();
                if (temp != null) {
                    cellValue = temp.replaceAll("#N/A", "").trim();
                }
                break;
            case 3:
                cellValue = null;
                break;
            case 4:
                return cell.getBooleanCellValue();
            case 5:
                cellValue = null;
                break;
            default:
                cellValue = "";
        }

        return cellValue;
    }

    private static String getNumberStr(String temp) {
        String cellValue = "";
        if (temp.contains(PropertyConstants.SYMBOL_DOT)) {
            String temp1 = temp.substring(temp.indexOf(".") + 1, temp.length());
            if (!StringUtils.isEmpty(temp1)) {
                if (temp1.matches(PropertyConstants.SYMBOL_0)) {
                    cellValue = temp.substring(0, temp.indexOf("."));
                } else {
                    cellValue = temp;
                }
            }
        } else {
            cellValue = temp;
        }

        return cellValue;
    }

    private static void insertImage(Workbook wb, Drawing pa, byte[] data, int row, int column) {
        XSSFClientAnchor anchor = new XSSFClientAnchor();
        anchor.setCol1(column);
        anchor.setCol2(column + 1);
        anchor.setRow1(row);
        anchor.setRow2(row + 1);
        anchor.setAnchorType(2);
        pa.createPicture(anchor, wb.addPicture(data, 5));
    }

    private static byte[] getImageData(BufferedImage bi) {
        try {
            ByteArrayOutputStream bout = new ByteArrayOutputStream();
            ImageIO.write(bi, "JPG", bout);
            return bout.toByteArray();
        } catch (Exception var2) {
            LOGGER.error("IO Exception", var2);
            return null;
        }
    }

    private static XSSFCell getCell(XSSFSheet sheet, int row, int col) {
        XSSFRow sheetRow = sheet.getRow(row);
        if (sheetRow == null) {
            sheetRow = sheet.createRow(row);
        }

        XSSFCell cell = sheetRow.getCell(col);
        if (cell == null) {
            cell = sheetRow.createCell(col);
        }

        return cell;
    }

    private static Cell getCell(Sheet sheet, int row, int col) {
        Row sheetRow = sheet.getRow(row);
        if (sheetRow == null) {
            sheetRow = sheet.createRow(row);
        }

        Cell cell = sheetRow.getCell(col);
        if (cell == null) {
            cell = sheetRow.createCell(col);
        }

        return cell;
    }

    private static XSSFCellStyle getTitleStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor((short) 9);
        style.setFillPattern((short) 1);
        style.setAlignment((short) 2);
        style.setVerticalAlignment((short) 1);
        style.setBorderBottom((short) 1);
        style.setBorderLeft((short) 1);
        style.setBorderRight((short) 1);
        style.setBorderTop((short) 1);
        style.setLeftBorderColor((short) 8);
        style.setRightBorderColor((short) 8);
        style.setTopBorderColor((short) 8);
        style.setBottomBorderColor((short) 8);
        XSSFFont font = workbook.createFont();
        font.setFontName("黑体");
        font.setFontHeightInPoints((short) 14);
        font.setColor((short) 8);
        style.setFont(font);
        return style;
    }

    private static CellStyle createDefaultDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor((short) 9);
        style.setFillPattern((short) 1);
        style.setAlignment((short) 2);
        style.setBorderBottom((short) 1);
        style.setBorderLeft((short) 1);
        style.setBorderRight((short) 1);
        style.setBorderTop((short) 1);
        style.setWrapText(true);
        Font font = workbook.createFont();
        font.setFontName("黑体");
        font.setFontHeightInPoints((short) 10);
        font.setColor((short) 8);
        style.setFont(font);
        return style;
    }

    public static void createExcelWithBuffer(List<? extends ExportModel> data, OutputStream outputStream) throws Exception {
        if (data.size() != 0) {
            Workbook workbook = new SXSSFWorkbook(5000);
            Cell cell = null;
            Sheet sheet = workbook.createSheet();
            workbook.setSheetName(0, ((ExportModel) data.get(0)).getSheetName());
            List<String> fieldNames = new ArrayList();
            Field[] declaredFields = ((ExportModel) data.get(0)).getClass().getDeclaredFields();
            for (Field declaredField : declaredFields) {
                ExportField annotation = declaredField.<ExportField>getAnnotation(ExportField.class);
                if (annotation != null) {
                    fieldNames.add(annotation.name());
                }
            }

            Object extAttr = ((ExportModel) data.get(0)).getExtAttr();
            fieldNames.addAll(((LinkedHashMap) extAttr).keySet());
            int[] colWidths = new int[fieldNames.size()];
            insertHeader(workbook, sheet, fieldNames, colWidths);
            Drawing patriarch = sheet.createDrawingPatriarch();
            Integer picIndex = null;
            CellStyle defaultDataStyle = createDefaultDataStyle(workbook);
            CellStyle dateStyle = getCellDateStyle(workbook);
            CellStyle linkStyle = createLinkStyle(workbook);
            analysistype(data, workbook, sheet, fieldNames, declaredFields, colWidths, patriarch, picIndex, defaultDataStyle, dateStyle, linkStyle);
            sheet.createFreezePane(0, 1);
            workbook.write(outputStream);
        }
    }


    /**
     * MSIE
     **/
    public static final String EXCEL_MSIE = "MSIE";
    /**
     * Trident
     **/
    public static final String EXCEL_TRIDENT = "Trident";

    public static void createExcelWithBuffer(List<? extends ExportModel> data, String fileName, HttpServletRequest request, HttpServletResponse response) {
        String agent = request.getHeader("USER-AGENT");

        try {
            if (null == agent || !agent.contains(EXCEL_MSIE) && !agent.contains(EXCEL_TRIDENT)) {
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1"));
            } else {
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));
            }

            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            createExcelWithBuffer(data, response.getOutputStream());
        } catch (Exception var6) {
            LOGGER.error("导出失败", var6);
        }

    }

    private static void insertHeader(Workbook workbook, Sheet sheet, List<String> fieldNames, int[] colWidths) {
        for (int j = 0; j < colWidths.length; ++j) {
            Cell cell = getCell((Sheet) sheet, 0, j);
            cell.setCellValue((String) fieldNames.get(j));
            cell.setCellStyle(createDefaultDataStyle(workbook));
            colWidths[j] = ((String) fieldNames.get(j)).length();
        }

    }

    private static void analysistype(List<? extends ExportModel> data, Workbook workbook, Sheet sheet, List<String> fieldNames, Field[] declaredFields, int[] colWidths, Drawing patriarch, Integer picIndex, CellStyle defaultDataStyle, CellStyle dateStyle, CellStyle linkStyle) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException, IOException {
        int j;
        for (j = 0; j < data.size(); ++j) {
            ExportModel bean = (ExportModel) data.get(j);
            int normalWidth = 0;

            for (int i1 = 0; i1 < declaredFields.length; ++i1) {
                Field declaredField = declaredFields[i1];
                ExportField[] annotation = (ExportField[]) ((ExportField[]) declaredField.getAnnotationsByType(ExportField.class));
                if (annotation != null && annotation.length != 0) {
                    ++normalWidth;
                    String property = "";
                    Cell cell = getCell(sheet, j + 1, i1);
                    switch (annotation[0].type()) {
                        case PIC:
                            property = BeanUtils.getProperty(bean, declaredField.getName());
                            insertImage(workbook, patriarch, getImageData(ImageIO.read(new URL(property))), j + 1, i1);
                            picIndex = i1;
                            break;
                        case URL:
                            property = BeanUtils.getProperty(bean, declaredField.getName());
                            cell.setCellStyle(linkStyle);
                            cell.setCellValue(property);
                            break;
                        case DATE:
                            if (PropertyUtils.getProperty(bean, declaredField.getName()) == null) {
                                cell.setCellStyle(dateStyle);
                                continue;
                            }

                            if (declaredField.getType().equals(Long.class)) {
                                long timestap = (Long) PropertyUtils.getProperty(bean, declaredField.getName());
                                DateTime dateTime = new DateTime(timestap);
                                DateTime dateTime1 = DateTime.parse(dateTime.toString("yyyy-MM-dd HH:mm:ss"), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss"));
                                cell.setCellStyle(dateStyle);
                                cell.setCellValue(dateTime1.toDate());
                            } else if (declaredField.getType().equals(Date.class)) {
                                Date result = (Date) PropertyUtils.getProperty(bean, declaredField.getName());
                                if (annotation[0].format().length() > 0) {
                                    SimpleDateFormat sdf = new SimpleDateFormat(annotation[0].format());
                                    property = sdf.format(result);
                                }

                                cell.setCellValue(property);
                                cell.setCellStyle(defaultDataStyle);
                            } else {
                                ZonedDateTime result = (ZonedDateTime) PropertyUtils.getProperty(bean, declaredField.getName());
                                if (annotation[0].format().length() > 0) {
                                    property = result.format(DateTimeFormatter.ofPattern(annotation[0].format()));
                                }

                                cell.setCellValue(property);
                                cell.setCellStyle(defaultDataStyle);
                            }
                            break;
                        case NUMBER:
                            Number number = (Number) PropertyUtils.getProperty(bean, declaredField.getName());
                            if (StringUtils.isNotBlank(annotation[0].format())) {
                                property = (new DecimalFormat()).format(number);
                                cell.setCellValue(property);
                            } else {
                                double v = 0.0D;
                                if (number != null) {
                                    v = NumberUtils.toDouble(number.toString());
                                }

                                cell.setCellValue(v);
                            }

                            cell.setCellStyle(defaultDataStyle);
                            break;
                        default:
                            cell.setCellStyle(defaultDataStyle);
                            property = BeanUtils.getProperty(bean, declaredField.getName());
                            cell.setCellValue(property);
                    }

                    if (annotation[0].type().equals(ExportType.DATE)) {
                        colWidths[i1] = 8;
                    } else {
                        colWidths[i1] = Math.max(colWidths[i1], property == null ? 5 : property.length());
                    }
                }
            }

            for (Iterator var24 = bean.getExtAttr().values().iterator(); var24.hasNext(); ++normalWidth) {
                String s = (String) var24.next();
                colWidths[normalWidth] = Math.max(colWidths[normalWidth], s.length());
                Cell cell = getCell(sheet, j + 1, normalWidth);
                cell.setCellValue(s);
                cell.setCellStyle(defaultDataStyle);
            }
        }

        if (data.size() > 0) {
            for (j = 0; j < fieldNames.size(); ++j) {
                if (picIndex != null && j == picIndex) {
                    sheet.setColumnWidth(picIndex, 4096);
                } else {
                    double t = (double) ((colWidths[j] * 2 + 2) * 256);
                    if (t > 65280.0D) {
                        t = 5120.0D;
                    }

                    sheet.setColumnWidth(j, (int) t);
                }
            }

            sheet.setDefaultRowHeightInPoints(20.0F);
        }

    }

    private static CellStyle getCellDateStyle(Workbook workbook) {
        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom((short) 1);
        cellStyle.setBorderLeft((short) 1);
        cellStyle.setBorderRight((short) 1);
        cellStyle.setBorderTop((short) 1);
        cellStyle.setVerticalAlignment((short) 2);
        cellStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd HH:mm:ss"));
        return cellStyle;
    }

    private static CellStyle createLinkStyle(Workbook workbook) {
        CellStyle linkStyle = workbook.createCellStyle();
        linkStyle.setBorderBottom((short) 1);
        linkStyle.setBorderLeft((short) 1);
        linkStyle.setBorderRight((short) 1);
        linkStyle.setBorderTop((short) 1);
        linkStyle.setVerticalAlignment((short) 1);
        Font cellFont = workbook.createFont();
        cellFont.setUnderline((byte) 1);
        cellFont.setColor((short) 12);
        linkStyle.setFont(cellFont);
        return linkStyle;
    }
}
