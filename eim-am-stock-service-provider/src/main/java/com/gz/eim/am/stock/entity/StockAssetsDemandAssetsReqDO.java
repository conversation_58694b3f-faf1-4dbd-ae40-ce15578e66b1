package com.gz.eim.am.stock.entity;

import lombok.Data;

import java.util.Date;

@Data
public class StockAssetsDemandAssetsReqDO {

    private String demandNo;

    private String deliveryPlanNo;

    private Integer deliveryMethod;

    private String trackingNumber;

    private Integer deliveryStatus;

    private String assetsCode;

    private Integer notEqualsToDeliveryStatus;

    private Boolean trackingNumberIsNotNullAndIsNotEmpty;

    private Date lessThanSendDate;

}