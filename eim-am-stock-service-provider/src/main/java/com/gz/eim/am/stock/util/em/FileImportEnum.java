package com.gz.eim.am.stock.util.em;

import com.gz.eim.am.stock.util.common.CodeEnumUtil;


/**
 * @author: weijunjie
 * @date: 2020/5/10
 * @description TODO
 */
public enum FileImportEnum implements ICodeEnum{
    /**
     * 资产转移导入
     */
    ASSET_TRANSFER_FILE_IMPORT(1,"资产转移导入"),
    ASSET_BATCH_TRANSFER_OUT_IMPORT(2,  "资产批量调拨导入"),
    BATCH_ROLE_KEEPER_IMPORT(3,  "人员角色关联批量导入"),
    BATCH_ROLE_WAREHOUSE_IMPORT(4,  "仓库角色关联批量导入"),
    ASSET_CHECK_IMPORT(5,  "线下盘点数据导入"),
    BATCH_ROLE_IMPORT(6,"角色批量导入"),
    ;

    FileImportEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    private Integer code;
    private String value;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getValue() {
        return value;
    }

    /**
     * 根据code返回指定枚举类型中的相应值
     *
     * @param code 指定code
     */
    public static FileImportEnum fromCode(Integer code) {
        return CodeEnumUtil.fromCode (FileImportEnum.class, code);
    }
}
