package com.gz.eim.am.stock.service.impl.discount;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.guazi.gzencrypt.common.constants.CryptType;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.base.dto.response.file.SysAttachDTO;
import com.gz.eim.am.common.enums.EncryptModuleEnum;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.common.util.EncryptsPlus;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.constant.MetaDataConstants;
import com.gz.eim.am.stock.dao.base.StockAssetsLicenseMapper;
import com.gz.eim.am.stock.dao.base.StockLicenseChangeItemsMapper;
import com.gz.eim.am.stock.dao.base.StockLicenseChangeMapper;
import com.gz.eim.am.stock.dao.discount.LicenseChangeMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.discount.StockLicenseChangeItemsReqDTO;
import com.gz.eim.am.stock.dto.request.discount.StockLicenseChangeQueryDTO;
import com.gz.eim.am.stock.dto.request.discount.StockLicenseChangeReqDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.discount.StockLicenseChangeItemsRespDTO;
import com.gz.eim.am.stock.dto.response.discount.StockLicenseChangeRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.service.assets.StockAssetsLicenseService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.discount.StockLicenseChangeItemsService;
import com.gz.eim.am.stock.service.discount.StockLicenseChangeService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: wangjing67
 * @Date: 3/31/21 11:56 上午
 * @description
 */
@Service
@Slf4j
public class StockLicenseChangeServiceImpl implements StockLicenseChangeService {

    @Autowired
    private StockLicenseChangeMapper stockLicenseChangeMapper;

    @Autowired
    private StockLicenseChangeServiceHelper stockLicenseChangeServiceHelper;

    @Autowired
    private FileServiceApi fileServiceApi;

    @Autowired
    private StockRoleKeeperService roleKeeperService;

    @Autowired
    private LicenseChangeMapper licenseChangeMapper;

    @Autowired
    private StockLicenseChangeItemsMapper stockLicenseChangeItemsMapper;

    @Autowired
    private StockAssetsService stockAssetsService;

    @Autowired
    private StockAssetsLicenseService stockAssetsLicenseService;

    @Autowired
    private StockLicenseChangeItemsService stockLicenseChangeItemsService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData save(StockLicenseChangeReqDTO stockLicenseChangeReqDTO) throws Exception {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //1.参数校验
        String result = stockLicenseChangeServiceHelper.checkParam(stockLicenseChangeReqDTO,jwtUser);
        if (StringUtils.isNotBlank(result)) {
            return ResponseData.createFailResult(result);
        }
        //2。数据准备
        StockLicenseChange stockLicenseChange = ConvertUtil.convertToType(StockLicenseChange.class, stockLicenseChangeReqDTO);
        String changeNo = OrderUtil.getOrderNo(OrderEnum.CHANGE_NO);
        stockLicenseChange.setChangeNo(changeNo);
        stockLicenseChange.setUpdatedBy(jwtUser.getEmployeeCode());
        stockLicenseChange.setCreatedBy(jwtUser.getEmployeeCode());
        // 单据提交后 状态变为审批中
        stockLicenseChange.setChangeStatus(StockAssetsLicenseChangeEnum.ChangeStatus.IN_APPROVE.getValue());
        //需求变更时间
        if (StringUtils.isNotBlank(stockLicenseChangeReqDTO.getChangeDate())) {
            Date changeTime = DateUtils.dateParse(stockLicenseChangeReqDTO.getChangeDate(), DateUtils.DATE_PATTERN);
            stockLicenseChange.setChangeTime(changeTime);
        }
        //处理行信息
        List<StockLicenseChangeItems> stockLicenseChangeItemsList = new ArrayList<>();
        handelChangeItems(changeNo, stockLicenseChangeReqDTO, jwtUser, stockLicenseChangeItemsList);

        //3.新增数据
        licenseChangeMapper.insert(stockLicenseChange);
        //新增行信息
        licenseChangeMapper.batchInsert(stockLicenseChangeItemsList);

        //变更类型为法人信息时 上传附件
        List<SysAttachReqDTO> attachList = stockLicenseChangeReqDTO.getAttachList();
        if (CollectionUtils.isNotEmpty(attachList)) {
            attachList.forEach(attach -> {
                attach.setRelId(stockLicenseChange.getId().toString());
                attach.setSystemModule(MetaDataConstants.PROJECT_STOCK);
                attach.setAttachModule(FileConstant.ATTACH_MODULE_ASSET_LICENSE_CHANGE);
                attach.setPageModule(FileConstant.PAGE_MODE_ASSET_LICENSE_CHANGE);
            });
            fileServiceApi.updateSysAttachList(attachList);
        }

        //4。发起审批
        /**
         * 目前只会有一种变更类型  行信息中只会有一条记录
         * 可根据第一条行信息中变更类型 判断审批流程
         * 后期会有出现同一个变更申请单 存在多个变更类型的修改
         */
        Integer changeType = stockLicenseChangeReqDTO.getStockLicenseChangeItemsReqDTOList().get(0).getChangeType();
        //变更类型为法人信息时
        if (StockAssetsLicenseChangeEnum.ChangeType.LEGAL_PERSON.getValue().equals(changeType)) {
            stockLicenseChangeServiceHelper.sendToWfl(changeNo, FlowCodeEnum.LICENSE_PERSON);
        }
        //变更类型为注册地址时
        if (StockAssetsLicenseChangeEnum.ChangeType.REGISTERED_ADDRESS.getValue().equals(changeType)) {
            stockLicenseChangeServiceHelper.sendToWfl(changeNo, FlowCodeEnum.LICENSE_ADDRESS);
        }
        //5。返回结果
        return ResponseData.createSuccessResult(stockLicenseChange.getId());
    }


    /**
     * 处理行信息
     *
     * @param changeNo
     * @param stockLicenseChangeReqDTO
     * @param jwtUser
     * @param stockLicenseChangeItemsList
     */
    private void handelChangeItems(String changeNo, StockLicenseChangeReqDTO stockLicenseChangeReqDTO, JwtUser jwtUser, List<StockLicenseChangeItems> stockLicenseChangeItemsList) throws Exception {
        if (!CollectionUtils.isEmpty(stockLicenseChangeReqDTO.getStockLicenseChangeItemsReqDTOList())) {
            for (StockLicenseChangeItemsReqDTO stockLicenseChangeItemsReqDTO : stockLicenseChangeReqDTO.getStockLicenseChangeItemsReqDTOList()) {
                StockLicenseChangeItems stockLicenseChangeItems = ConvertUtil.convertToType(StockLicenseChangeItems.class, stockLicenseChangeItemsReqDTO);
                //身份证号处理
                if (StringUtils.isNotBlank(stockLicenseChangeItemsReqDTO.getBeforeIdCard())) {
                    String encrypt = EncryptsPlus.encrypt(EncryptModuleEnum.DATA.value, CryptType.ID_CARD, stockLicenseChangeItemsReqDTO.getBeforeIdCard());
                    stockLicenseChangeItems.setBeforeIdCard(encrypt);
                }
                if (StringUtils.isNotBlank(stockLicenseChangeItemsReqDTO.getAfterIdCard())) {
                    String encrypt = EncryptsPlus.encrypt(EncryptModuleEnum.DATA.value, CryptType.ID_CARD, stockLicenseChangeItemsReqDTO.getAfterIdCard());
                    stockLicenseChangeItems.setAfterIdCard(encrypt);
                }
                stockLicenseChangeItems.setChangeNo(changeNo);
                stockLicenseChangeItems.setUpdatedBy(jwtUser.getEmployeeCode());
                stockLicenseChangeItems.setCreatedBy(jwtUser.getEmployeeCode());
                stockLicenseChangeItemsList.add(stockLicenseChangeItems);
            }
        }
    }


    @Override
    public ResponseData list(StockLicenseChangeQueryDTO stockLicenseChangeQueryDTO) throws Exception {
        final PageRespDTO<StockLicenseChangeRespDTO> pageRespDTO = PageRespDTO.createSuccessRes();
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //计划变更时间处理
        if (StringUtils.isNotBlank(stockLicenseChangeQueryDTO.getBeginChangeTime())) {
            Date startDate = DateUtils.dateParse(stockLicenseChangeQueryDTO.getBeginChangeTime(), DateUtils.DATE_PATTERN);
            stockLicenseChangeQueryDTO.setBeginChangeDate(DateTimeUtil.getDayStart(startDate));
        }
        if (StringUtils.isNotBlank(stockLicenseChangeQueryDTO.getEndChangeTime())) {
            Date endDate = DateUtils.dateParse(stockLicenseChangeQueryDTO.getEndChangeTime(), DateUtils.DATE_PATTERN);
            stockLicenseChangeQueryDTO.setEndChangeDate(DateTimeUtil.getDayEnd(endDate));
        }
        //制单时间
        if (StringUtils.isNotBlank(stockLicenseChangeQueryDTO.getBeginTime())) {
            Date startDate = DateUtils.dateParse(stockLicenseChangeQueryDTO.getBeginTime(), DateUtils.DATE_PATTERN);
            stockLicenseChangeQueryDTO.setBeginDate(DateTimeUtil.getDayStart(startDate));
        }
        if (StringUtils.isNotBlank(stockLicenseChangeQueryDTO.getEndTime())) {
            Date endDate = DateUtils.dateParse(stockLicenseChangeQueryDTO.getEndTime(), DateUtils.DATE_PATTERN);
            stockLicenseChangeQueryDTO.setEndDate(DateTimeUtil.getDayEnd(endDate));
        }
        long count = 0;
//        //授权查询
//        if (!roleKeeperService.isAll(jwtUser.getEmployeeCode())) {
//            List<String> wc = this.roleKeeperService.selectKeepWarehouseByParam(jwtUser.getEmployeeCode(), null, null);
//            if (null == wc || wc.size() == 0) {
//                pageRespDTO.setCount(count);
//                pageRespDTO.setData(new ArrayList<>());
//                return ResponseData.createSuccessResult(pageRespDTO);
//            }
//            stockLicenseChangeQueryDTO.setWarehouseCodeList(wc);
//        }
        count = licenseChangeMapper.countByQuery(stockLicenseChangeQueryDTO);
        pageRespDTO.setCount(count);
        if (count > 0) {
            stockLicenseChangeQueryDTO.initPageDefaultParam();
            stockLicenseChangeQueryDTO.setStartNum((stockLicenseChangeQueryDTO.getPageNum() - CommonConstant.NUMBER_ONE) * stockLicenseChangeQueryDTO.getPageSize());
            stockLicenseChangeQueryDTO.setPageSize(stockLicenseChangeQueryDTO.getPageSize());

            List<StockLicenseChangeItemsRespDTO> stockLicenseChangeItemsRespDTOList = licenseChangeMapper.selectByQuery(stockLicenseChangeQueryDTO);
            //赋值操作
            stockLicenseChangeServiceHelper.settingListRelationValue(null,stockLicenseChangeItemsRespDTOList);
            pageRespDTO.setData(stockLicenseChangeItemsRespDTOList);
        }
        return pageRespDTO;
    }


    @Override
    public ResponseData detail(Long id) throws Exception {
        //参数校验
        if (null == id) {
            return ResponseData.createFailResult("参数不能为空");
        }
        StockLicenseChange stockLicenseChange = stockLicenseChangeMapper.selectByPrimaryKey(id);
        if (null == stockLicenseChange) {
            return ResponseData.createFailResult("查询信息不存在");
        }
        //查询变更申请单行信息
        List<StockLicenseChangeItems> stockLicenseChangeItemsList = stockLicenseChangeItemsService.selectByChangeNo(stockLicenseChange.getChangeNo());

        List<StockLicenseChange> stockLicenseChangeList = (List<StockLicenseChange>) Stream.of(stockLicenseChange).collect(Collectors.toList());
        //设置变更申请头相关信息
        List<StockLicenseChangeRespDTO> stockLicenseChangeRespDTOList = stockLicenseChangeServiceHelper.settingRelationValue(stockLicenseChangeList);

        StockLicenseChangeRespDTO stockLicenseChangeRespDTO = stockLicenseChangeRespDTOList.get(0);
        List<StockLicenseChangeItemsRespDTO> stockLicenseChangeItemsRespDTOList = ConvertUtil.convertToType(StockLicenseChangeItemsRespDTO.class, stockLicenseChangeItemsList);
        //设置变更申请单行相关信息
        stockLicenseChangeServiceHelper.settingListRelationValue(stockLicenseChangeRespDTO,stockLicenseChangeItemsRespDTOList);
        stockLicenseChangeRespDTO.setStockLicenseChangeItemsRespDTOList(stockLicenseChangeItemsRespDTOList);

        //查询附件信息
        List<SysAttachDTO> sysAttachDTOList = handleAttachList(stockLicenseChangeList.get(0));
        stockLicenseChangeRespDTO.setSysAttachDTOS(sysAttachDTOList);

        //查询附件的缩略图信息
        selectAttachInfo(stockLicenseChangeList.get(0), stockLicenseChangeRespDTO);

        return ResponseData.createSuccessResult(stockLicenseChangeRespDTO);
    }


    /**
     * 处理附件信息
     *
     * @param stockLicenseChange
     */
    private List<SysAttachDTO> handleAttachList(StockLicenseChange stockLicenseChange) {
        //查询附件信息
        com.gz.eim.am.base.dto.request.file.QueryFileReqDTO queryFileReqDTO = new com.gz.eim.am.base.dto.request.file.QueryFileReqDTO();
        queryFileReqDTO.setSystemModule(MetaDataConstants.PROJECT_STOCK);
        queryFileReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSET_LICENSE_CHANGE);
        queryFileReqDTO.setPageModule(FileConstant.PAGE_MODE_ASSET_LICENSE_CHANGE);
        queryFileReqDTO.setRelId(stockLicenseChange.getId().toString());

        com.fuu.eim.support.base.ResponseData<List<SysAttachDTO>> resultResponse = fileServiceApi.getSysAttachDTOListSelective(queryFileReqDTO);

        //附件信息  不含下载url 预览url 缩略图url
        if (resultResponse.isSuccess() && CollectionUtils.isNotEmpty(resultResponse.getData())) {
            List<SysAttachDTO> sysyAttachDTO = resultResponse.getData();
            return sysyAttachDTO;
        }
        return new ArrayList<>();
    }


    /**
     * 处理附件的缩略图信息
     *
     * @param stockLicenseChange
     * @param stockLicenseChangeRespDTO
     */
    private void selectAttachInfo(StockLicenseChange stockLicenseChange, StockLicenseChangeRespDTO stockLicenseChangeRespDTO) {
        //查询附件信息
        com.gz.eim.am.base.dto.request.file.QueryFileReqDTO queryFileReqDTO = new com.gz.eim.am.base.dto.request.file.QueryFileReqDTO();
        queryFileReqDTO.setSystemModule(MetaDataConstants.PROJECT_STOCK);
        queryFileReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSET_LICENSE_CHANGE);
        queryFileReqDTO.setPageModule(FileConstant.PAGE_MODE_ASSET_LICENSE_CHANGE);
        queryFileReqDTO.setRelId(stockLicenseChange.getId().toString());
        ResponseData<List<PictureMetaRespDTO>> listResponseData = fileServiceApi.getFileUrlListByRelId(queryFileReqDTO);
        //返回附件信息 下载url  预览url  缩略图url
        if (listResponseData.isSuccess() && CollectionUtils.isNotEmpty(listResponseData.getData())) {
            List<PictureMetaRespDTO> pictureMetaRespDTOList = listResponseData.getData();
            stockLicenseChangeRespDTO.setPictureMetaRespDTOList(pictureMetaRespDTOList);
        }
    }


    @Override
    public ResponseData selectAssets(AssetsSearchDTO assetsSearchDTO) {
        assetsSearchDTO.setStatus(AssetsEnum.statusType.IDLE.getValue());
        return stockAssetsService.selectAssets(assetsSearchDTO);
    }

    @Override
    public ResponseData selectDetail(String changeNo) throws Exception {
        if (StringUtils.isBlank(changeNo)) {
            return ResponseData.createFailResult("参数不能为空");
        }
        StockLicenseChangeExample example = new StockLicenseChangeExample();
        StockLicenseChangeExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(changeNo)) {
            criteria.andChangeNoEqualTo(changeNo);
        }
        List<StockLicenseChange> stockLicenseChangeList = stockLicenseChangeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(stockLicenseChangeList)) {
            return ResponseData.createFailResult("未查询到数据");
        }
        //目前只会存在一个变更申请对应一个变更类型
        return detail(stockLicenseChangeList.get(0).getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusByChangeNo(String changeNo, Integer status) throws Exception {
        //1。参数校验
        if (StringUtils.isBlank(changeNo) || status == null) {
            log.info("更改执照变更申请单状态参数为空,changeNo: {}, status:{}", changeNo, status);
            return false;
        }
        //2。更新单据状态
        StockLicenseChangeExample example = new StockLicenseChangeExample();
        StockLicenseChangeExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(changeNo)) {
            criteria.andChangeNoEqualTo(changeNo);
        }
        List<StockLicenseChange> stockLicenseChangeList = stockLicenseChangeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(stockLicenseChangeList)) {
            log.info("更改执照变更申请单状态查询内容为空,changeNo: {}, status:{}", changeNo, status);
            return false;
        }
        StockLicenseChange stockLicenseChange = stockLicenseChangeList.get(0);
        stockLicenseChange.setChangeStatus(status);
        stockLicenseChange.setUpdatedAt(new Date());
        stockLicenseChangeMapper.updateByPrimaryKeySelective(stockLicenseChange);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData doChange(Long id, JwtUser jwtUser) throws Exception {
        //1.参数校验
        if (null == id) {
            return ResponseData.createFailResult("参数不能为空");
        }
        StockLicenseChangeItems stockLicenseChangeItems = stockLicenseChangeItemsMapper.selectByPrimaryKey(id);
        if (null == stockLicenseChangeItems) {
            return ResponseData.createFailResult("未查到单据信息");
        }

        //2. 更新申请单状态为已完成
        //查询对应的申请单信息
        StockLicenseChange stockLicenseChange = selectByChangeNo(stockLicenseChangeItems.getChangeNo());
        //单据状态更新为已完成
        stockLicenseChange.setChangeStatus(StockAssetsLicenseChangeEnum.ChangeStatus.HAVE_FINISH.getValue());
        stockLicenseChange.setUpdatedBy(jwtUser.getEmployeeCode());
        stockLicenseChangeMapper.updateByPrimaryKeySelective(stockLicenseChange);
        //查询执照变更申请单行信息
        List<StockLicenseChangeItems> stockLicenseChangeItemsList = stockLicenseChangeItemsService.selectByChangeNo(stockLicenseChange.getChangeNo());

        //3.变更执照资产信息
        //查询物料下需要变更的所有资产信息
        List<StockAssetsLicense> stockAssetsLicenseList = stockAssetsLicenseService.selectBySuppliesCode(stockLicenseChange.getSuppliesCode());
        //定义需要新增集合
        List<StockAssetsLicense> addStockAssetsLicenseList = new ArrayList<>();
        //定义需要更新集合
        List<StockAssetsLicense> updateStockAssetsLicenseList = new ArrayList<>();
        //如果存在则更新
        if (CollectionUtils.isNotEmpty(stockAssetsLicenseList)) {
            for (StockAssetsLicense stockAssetsLicense : stockAssetsLicenseList) {
                //更新执照资产相关信息
                handelAssetsLicense(stockAssetsLicense, stockLicenseChange, stockLicenseChangeItemsList);
                updateStockAssetsLicenseList.add(stockAssetsLicense);
            }
        } else {
            //如果不存在 则需要插入信息
            List<StockAssets> stockAssetsList = stockAssetsService.selectAssetsBySuppliesCode(stockLicenseChange.getSuppliesCode());
            if (CollectionUtils.isNotEmpty(stockAssetsList)) {
                for (StockAssets stockAssets : stockAssetsList) {
                    StockAssetsLicense stockAssetsLicense = new StockAssetsLicense();
                    stockAssetsLicense.setAssetsName(stockAssets.getAssetsName());
                    stockAssetsLicense.setAssetsCode(stockAssets.getAssetsCode());
                    handelAssetsLicense(stockAssetsLicense, stockLicenseChange, stockLicenseChangeItemsList);
                    stockAssetsLicense.setSuppliesCode(stockAssets.getSuppliesCode());
                    stockAssetsLicense.setCreatedBy(jwtUser.getEmployeeCode());
                    addStockAssetsLicenseList.add(stockAssetsLicense);
                }
            }
        }
        //4.数据处理
        insertOrUpdateAssetsLicenseList(addStockAssetsLicenseList, updateStockAssetsLicenseList, jwtUser);
        //5.返回结果
        return ResponseData.createSuccessResult();
    }

    /**
     * 新增或更新执照资产信息
     *
     * @param addStockAssetsLicenseList
     * @param updateStockAssetsLicenseList
     * @throws Exception
     */
    private void insertOrUpdateAssetsLicenseList(List<StockAssetsLicense> addStockAssetsLicenseList, List<StockAssetsLicense> updateStockAssetsLicenseList, JwtUser user) throws Exception {
        //新增
        if (CollectionUtils.isNotEmpty(addStockAssetsLicenseList)) {
            stockAssetsLicenseService.batchOperate(addStockAssetsLicenseList, user);
        }
        //更新
        if (CollectionUtils.isNotEmpty(updateStockAssetsLicenseList)) {
            stockAssetsLicenseService.batchUpdate(updateStockAssetsLicenseList);
        }
    }

    /**
     * 处理执照变更信息
     *
     * @param stockAssetsLicense
     * @param stockLicenseChange
     * @param stockLicenseChangeItemsList
     * @throws Exception
     */
    private void handelAssetsLicense(StockAssetsLicense stockAssetsLicense, StockLicenseChange stockLicenseChange, List<StockLicenseChangeItems> stockLicenseChangeItemsList) throws Exception {
        //目前只会存在一个执照变更申请单 对应一个变更申请单行信息   后期可能会有多条 这期暂不支持

        for (StockLicenseChangeItems stockLicenseChangeItems : stockLicenseChangeItemsList) {
            //变更类型为注册地址
            if (StockAssetsLicenseChangeEnum.ChangeType.REGISTERED_ADDRESS.getValue().equals(stockLicenseChangeItems.getChangeType())) {
                stockAssetsLicense.setRegisteredAddress(stockLicenseChangeItems.getAfterValue());
            }
            //变更类型为法人信息
            if (StockAssetsLicenseChangeEnum.ChangeType.LEGAL_PERSON.getValue().equals(stockLicenseChangeItems.getChangeType())) {
                stockAssetsLicense.setLicenseLegalPerson(stockLicenseChangeItems.getAfterValue());
                stockAssetsLicense.setCorporateIdNumber(stockLicenseChangeItems.getAfterIdCard());

                //变更时删除之前存在的附件信息 与资产信息绑定的
                deleteAttachList(stockAssetsLicense);

                //处理法人身份证附件信息
                List<SysAttachDTO> sysAttachDTOList = handleAttachList(stockLicenseChange);

                List<SysAttachReqDTO> sysAttachReqDTOList = new ArrayList<>();
                for (SysAttachDTO attach : sysAttachDTOList) {
                    SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO();

                    sysAttachReqDTO.setAttachSaveName(attach.getAttachSaveName());
                    sysAttachReqDTO.setAttachSavePath(attach.getAttachSavePath());
                    sysAttachReqDTO.setAttachExt(attach.getAttachExt());
                    sysAttachReqDTO.setAttachSize(attach.getAttachSize());
                    sysAttachReqDTO.setPosition(attach.getPosition());
                    sysAttachReqDTO.setCreateTime(attach.getCreateTime());
                    sysAttachReqDTO.setUpdateTime(attach.getUpdateTime());
                    sysAttachReqDTO.setDelFlag(attach.getDelFlag());
                    sysAttachReqDTO.setFileUniqueId(attach.getFileUniqueId());
                    sysAttachReqDTO.setReduceFileId(attach.getReduceFileId());

                    sysAttachReqDTO.setRelId(stockAssetsLicense.getAssetsCode());
                    sysAttachReqDTO.setSystemModule(MetaDataConstants.PROJECT_STOCK);
                    sysAttachReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSET_LICENSE);
                    sysAttachReqDTO.setPageModule(FileConstant.PAGE_MODE_ASSET_LICENSE);
                    sysAttachReqDTOList.add(sysAttachReqDTO);
                }
                com.fuu.eim.support.base.ResponseData resultResponse = fileServiceApi.batchSaveAttach(sysAttachReqDTOList);
            }
        }
        stockAssetsLicense.setUpdatedBy(stockLicenseChange.getUpdatedBy());
    }

    /**
     * 删除与之前资产编码绑定的附件信息
     * @param stockAssetsLicense
     */
    private void deleteAttachList(StockAssetsLicense stockAssetsLicense){
        //查询附件信息
        com.gz.eim.am.base.dto.request.file.QueryFileReqDTO queryFileReqDTO = new com.gz.eim.am.base.dto.request.file.QueryFileReqDTO();
        queryFileReqDTO.setSystemModule(MetaDataConstants.PROJECT_STOCK);
        queryFileReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSET_LICENSE);
        queryFileReqDTO.setPageModule(FileConstant.PAGE_MODE_ASSET_LICENSE);
        queryFileReqDTO.setRelId(stockAssetsLicense.getAssetsCode());
        com.fuu.eim.support.base.ResponseData<List<SysAttachDTO>> resultResponse = fileServiceApi.getSysAttachDTOListSelective(queryFileReqDTO);

        if (resultResponse.isSuccess() && CollectionUtils.isNotEmpty(resultResponse.getData())) {
            List<SysAttachDTO> sysyAttachDTO = resultResponse.getData();
            if(CollectionUtils.isNotEmpty(sysyAttachDTO)){
                // ambase 添加删除方法
                List<String>  relIdList = sysyAttachDTO.stream().map(sysAttachDTO -> sysAttachDTO.getRelId()).collect(Collectors.toList());

                //逻辑删除之前执照绑定的信息
                com.gz.eim.am.base.dto.request.file.QueryFileReqDTO   fileReqDTO = new com.gz.eim.am.base.dto.request.file.QueryFileReqDTO();
                fileReqDTO.setSystemModule(MetaDataConstants.PROJECT_STOCK);
                fileReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSET_LICENSE);
                fileReqDTO.setPageModule(FileConstant.PAGE_MODE_ASSET_LICENSE);
                fileReqDTO.setRelIds(relIdList);
                fileServiceApi.unBindByRelIds(fileReqDTO);

                //物理删除
//                fileServiceApi.batchDeleteAttach(attachIdList);
            }
        }

    }

    @Override
    public StockLicenseChange selectByChangeNo(String changeNo) throws Exception {
        StockLicenseChangeExample example = new StockLicenseChangeExample();
        StockLicenseChangeExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(changeNo)) {
            criteria.andChangeNoEqualTo(changeNo);
        }
        return this.stockLicenseChangeMapper.selectByExample(example).get(0);
    }


    /**
     * 根据条件计数
     *
     * @param stockLicenseChangeQueryDTO
     * @return
     * @throws Exception
     */
    public Long getCount(StockLicenseChangeQueryDTO stockLicenseChangeQueryDTO) throws Exception {
        return this.stockLicenseChangeMapper.countByExample(settingExample(stockLicenseChangeQueryDTO));
    }

    /**
     * 根据调价查询列表
     *
     * @param stockLicenseChangeQueryDTO
     * @return
     * @throws Exception
     */
    public List<StockLicenseChange> getList(StockLicenseChangeQueryDTO stockLicenseChangeQueryDTO) throws Exception {
        StockLicenseChangeExample example = settingExample(stockLicenseChangeQueryDTO);
        example.setLimit(stockLicenseChangeQueryDTO.getPageSize());
        example.setOffset(stockLicenseChangeQueryDTO.getStartNum());
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        List<StockLicenseChange> stockLicenseChangeList = this.stockLicenseChangeMapper.selectByExample(example);
        return stockLicenseChangeList;
    }

    private StockLicenseChangeExample settingExample(StockLicenseChangeQueryDTO stockLicenseChangeQueryDTO) {
        StockLicenseChangeExample example = new StockLicenseChangeExample();
        StockLicenseChangeExample.Criteria criteria = example.createCriteria();
        //单据号
        if (StringUtils.isNotBlank(stockLicenseChangeQueryDTO.getChangeNo())) {
            criteria.andChangeNoEqualTo(stockLicenseChangeQueryDTO.getChangeNo());
        }
        //单据状态
        if (null != stockLicenseChangeQueryDTO.getChangeStatus()) {
            criteria.andChangeStatusEqualTo(stockLicenseChangeQueryDTO.getChangeStatus());
        }
//        //变更类型
//        if (null != stockLicenseChangeQueryDTO.getChangeType()) {
//            criteria.andChangeTypeEqualTo(stockLicenseChangeQueryDTO.getChangeType());
//        }
        //计划变更时间
        if (null != stockLicenseChangeQueryDTO.getBeginChangeDate()) {
            criteria.andChangeTimeGreaterThanOrEqualTo(stockLicenseChangeQueryDTO.getBeginChangeDate());
        }
        if (null != stockLicenseChangeQueryDTO.getEndChangeDate()) {
            criteria.andChangeTimeLessThanOrEqualTo(stockLicenseChangeQueryDTO.getEndChangeDate());
        }
        //制单时间
        if (null != stockLicenseChangeQueryDTO.getBeginDate()) {
            criteria.andCreatedAtGreaterThanOrEqualTo(stockLicenseChangeQueryDTO.getBeginDate());
        }
        if (null != stockLicenseChangeQueryDTO.getEndDate()) {
            criteria.andCreatedAtLessThanOrEqualTo(stockLicenseChangeQueryDTO.getEndDate());
        }
        //制单人
        if (StringUtils.isNotBlank(stockLicenseChangeQueryDTO.getCreatedBy())) {
            criteria.andCreatedByEqualTo(stockLicenseChangeQueryDTO.getCreatedBy());
        }

//        if (CollectionUtils.isNotEmpty(stockLicenseChangeQueryDTO.getWarehouseCodeList())) {
//            criteria.andWarehouseCodeIn(stockLicenseChangeQueryDTO.getWarehouseCodeList());
//        }
        return example;
    }
}
