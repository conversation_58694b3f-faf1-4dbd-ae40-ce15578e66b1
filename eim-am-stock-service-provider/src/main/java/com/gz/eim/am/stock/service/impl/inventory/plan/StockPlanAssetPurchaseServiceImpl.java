package com.gz.eim.am.stock.service.impl.inventory.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.google.common.collect.Lists;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.dao.inventory.InventoryInMapper;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryInSuppliesDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.*;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.PurchaseInventoryInHeadRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.PurchaseInventoryInLineRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.vo.StockAssetsInitializationInfo;
import com.gz.eim.am.stock.service.assets.PurchaseStatementAssetsNoteService;
import com.gz.eim.am.stock.service.assets.StockAssetsInitializationService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.inventory.StockInventoryAssetImportService;
import com.gz.eim.am.stock.service.inventory.StockInventoryInSuppliesAssetService;
import com.gz.eim.am.stock.service.inventory.StockInventoryInSuppliesService;
import com.gz.eim.am.stock.service.inventory.plan.*;
import com.gz.eim.am.stock.service.manage.StockSuppliesConfigDetailService;
import com.gz.eim.am.stock.service.manage.StockSuppliesConfigService;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseSuppliesCategoryService;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.em.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lishuyang
 * @date: 2019/12/20
 * @description:
 */
@Service
public class StockPlanAssetPurchaseServiceImpl implements StockPlanAssetPurchaseService {
    private final Logger logger = LoggerFactory.getLogger (StockPlanAssetPurchaseServiceImpl.class);

    /**
     * 返回值标识：count
     */
    private static final String DATA_COUNT = "count";
    /**
     * 返回值标识：inventoryInPlanLineAssetRespDTOS
     */
    private static final String DATA_INV_PLAN_LINE_ASSET_RESP_DTO_S = "inventoryInPlanLineAssetRespDTOS";

    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;

    @Autowired
    private StockWarehouseService stockWarehouseService;

    @Autowired
    private StockWarehouseBaseService stockWarehouseBaseService;

    @Autowired
    private StockWarehouseSuppliesCategoryService stockWarehouseSuppliesCategoryService;

    @Autowired
    private StockSuppliesService stockSuppliesService;

    @Autowired
    private StockInventoryInPlanHeadService stockInventoryInPlanHeadService;

    @Autowired
    private StockInventoryInPlanLineService stockInventoryInPlanLineService;

    @Autowired
    private StockInventoryInSuppliesAssetService stockInventoryInSuppliesAssetService;

    @Autowired
    private StockInventoryAssetImportService stockInventoryAssetImportService;

    @Autowired
    private StockAssetsService stockAssetsService;

    @Autowired
    private StockSuppliesConfigDetailService stockSuppliesConfigDetailService;

    @Autowired
    private StockAssetsInitializationService stockAssetsInitializationService;

    @Autowired
    private StockInventoryInPlanLineAssetsService stockInventoryInPlanLineAssetsService;

    @Autowired
    private StockInventoryInPlanLineSnsService stockInventoryInPlanLineSnsService;

    @Autowired
    private StockSuppliesService suppliesService;
    @Autowired
    private StockSuppliesConfigService suppliesConfigService;

    @Autowired
    private InventoryInMapper inventoryInMapper;

    @Autowired
    private StockInventoryInSuppliesService inventoryInSuppliesService;

    @Autowired
    private StockPlanHelper stockPlanHelper;

    @Autowired
    PurchaseStatementAssetsNoteService purchaseStatementAssetsNoteService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanAssetPurchase(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException {
        String checkReturn = checkSaveParam (inventoryInPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank (checkReturn)) {
            return ResponseData.createFailResult (checkReturn);
        }


        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead ();
        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = new ArrayList<> ();

        //准备插入参数
        prepareSaveDbBeanDTO (inventoryInPlanHeadReqDTO, stockInventoryInPlanHead, stockInventoryInPlanLineList, user);

        ///插入计划入库单
        stockInventoryInPlanHeadService.insert (stockInventoryInPlanHead);

        //导入计划入库单行
        if (!CollectionUtils.isEmpty (stockInventoryInPlanLineList)) {
            //查找入库单
            stockInventoryInPlanLineList.forEach (stockInventoryInPlanLine -> stockInventoryInPlanLine.setInventoryInPlanHeadId (stockInventoryInPlanHead.getInventoryInPlanHeadId ()));
            Integer count = stockInventoryInPlanLineService.batchInsertStockInventoryPlanLine (stockInventoryInPlanLineList);
            if (count == null || count < 1) {
                logger.error ("批量插入计划入库单单号错误，计划入库单编号：{}", stockInventoryInPlanHead.getInventoryInPlanNo ());
                throw new ServiceUncheckedException (ResponseCode.SYSTEM_ERROR.getMessage ());
            }
        }

        return ResponseData.createSuccessResult ();
    }

    /**
     * 准备入库参数
     *
     * @param inventoryInPlanHeadReqDTO
     * @param stockInventoryInPlanHead
     * @param stockInventoryInPlanLineList
     * @param user
     * @throws ParseException
     */
    private void prepareSaveDbBeanDTO(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead, List<StockInventoryInPlanLine> stockInventoryInPlanLineList, JwtUser user) throws ParseException {
        stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.WAIT_IN.getCode ());
        stockInventoryInPlanHead.setCreatedBy (user.getEmployeeCode ());
        stockInventoryInPlanHead.setInWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        stockInventoryInPlanHead.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.ASSET_PURCHASE.getCode ());
        stockInventoryInPlanHead.setVendorCode (inventoryInPlanHeadReqDTO.getVendorCode ());
        stockInventoryInPlanHead.setVendorName (inventoryInPlanHeadReqDTO.getVendorName ());
        stockInventoryInPlanHead.setBizNo (inventoryInPlanHeadReqDTO.getBizNo ());
        stockInventoryInPlanHead.setDeliveryNo (inventoryInPlanHeadReqDTO.getDeliveryNo ());
        Date planInTime;
        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getPlanInTime ())) {
            planInTime = new Date ();
        } else {
            planInTime = DateUtils.dateParse (inventoryInPlanHeadReqDTO.getPlanInTime (), DateUtils.DATE_PATTERN);
        }
        stockInventoryInPlanHead.setPlanInTime (planInTime);

        if (inventoryInPlanHeadReqDTO.getBillingUser () != null) {
            stockInventoryInPlanHead.setBillingUser (inventoryInPlanHeadReqDTO.getBillingUser ());
        } else {
            stockInventoryInPlanHead.setBillingUser (user.getEmployeeCode ());
        }
        stockInventoryInPlanHead.setRemark (inventoryInPlanHeadReqDTO.getRemark ());
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        //收货人为仓库的linkman
        if (stockWarehouseBase != null) {
            stockInventoryInPlanHead.setReceiveUser (stockWarehouseBase.getLinkman ());
        }
        stockInventoryInPlanHead.setOutWarehouseCode (inventoryInPlanHeadReqDTO.getOutWarehouseCode ());
        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getBillingTime ())) {
            stockInventoryInPlanHead.setBillingTime (new Date ());
        } else {
            stockInventoryInPlanHead.setBillingTime (DateUtils.dateParse (inventoryInPlanHeadReqDTO.getBillingTime (), DateUtils.DATE_PATTERN));
        }

        stockInventoryInPlanHead.setDutyUser (inventoryInPlanHeadReqDTO.getDutyUser ());
        stockInventoryInPlanHead.setUpdatedBy (user.getEmployeeCode ());

        stockInventoryInPlanHead.setDemandDeptCode (inventoryInPlanHeadReqDTO.getDemandDeptCode ());
        //公司编码
        stockInventoryInPlanHead.setCompanyCode(inventoryInPlanHeadReqDTO.getCompanyCode());
        //计划入库单行

        if (!CollectionUtils.isEmpty (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ())) {
            for (InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO : inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ()) {
                StockInventoryInPlanLine stockInventoryInPlanLine = new StockInventoryInPlanLine ();
                //物料编码
                stockInventoryInPlanLine.setSuppliesCode (inventoryInPlanLineReqDTO.getSuppliesCode ());

                //计划入库数量
                stockInventoryInPlanLine.setNumber (inventoryInPlanLineReqDTO.getNumber ());

                if (inventoryInPlanHeadReqDTO.getIsDirectInStock () != null && inventoryInPlanHeadReqDTO.getIsDirectInStock ().equals (InventoryInPlanHeadEnum.DirectInStock.YES.getCode ())) {
                    stockInventoryInPlanLine.setRealNumber (inventoryInPlanLineReqDTO.getNumber ());
                    stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
                    //stockInventoryInPlanLine.setInventoryInTime (new Date ());
                } else {
                    stockInventoryInPlanLine.setRealNumber (0);
                    stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.WAIT_IN.getStatus ());
                }
                stockInventoryInPlanLine.setUnitPrice(inventoryInPlanLineReqDTO.getPurchasePrice());
                stockInventoryInPlanLine.setPriceExcludingTax(inventoryInPlanLineReqDTO.getPurchasePrice());
                stockInventoryInPlanLine.setPlanInTime(planInTime);
                stockInventoryInPlanLine.setCreatedBy (user.getEmployeeCode ());
                stockInventoryInPlanLine.setUpdatedBy (user.getEmployeeCode ());

                stockInventoryInPlanLineList.add (stockInventoryInPlanLine);
            }
        }
    }

    /**
     * 校验保存计划资产采购入库单参数
     *
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) {
        if (null == inventoryInPlanHeadReqDTO) {
            return "必填参数为空";
        }

        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getInWarehouseCode ())) {
            return "调入仓库不能为空";
        }

        //仓库权限查询
        List<String> wc = this.stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        if (CollectionUtils.isEmpty (wc)) {
            return "无操作仓库的权限";
        }

        //仓库是否有效
        StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode (), WarehouseEnum.Status.NORMAL.getStatus ());
        if (null == stockWarehouse) {
            return "该仓库已禁用";
        }


        //仓库地址是否存在
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode ());
        if (null == stockWarehouseBase || StringUtils.isBlank (stockWarehouseBase.getAddress ())) {
            return "找不到调入仓库对应的地址";
        }

        if (StringUtils.isBlank (inventoryInPlanHeadReqDTO.getPlanInTime ())) {
            return "计划入库时间不能为空";
        }

        // 公司必填校验
        if (StringUtils.isBlank(inventoryInPlanHeadReqDTO.getCompanyCode())) {
            return "所属公司不能为空！";
        }

        if (CollectionUtils.isEmpty (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ())) {
            return "无计划入库的数据";
        }

        //校验计划入库单详细
        StringBuilder sb = new StringBuilder ();
        for (int i = 0; i < inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().size (); i++) {
            InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO = inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().get (i);
            if (StringUtils.isBlank (inventoryInPlanLineReqDTO.getSuppliesCode ())) {
                sb.append ("第" + (i + 1) + "行数据的采购码不能为空");
                continue;
            }

            List<StockSupplies> stockSuppliesList = stockSuppliesService.getEnableSuppliesList (inventoryInPlanLineReqDTO.getSuppliesCode ());
            if (CollectionUtils.isEmpty (stockSuppliesList)) {
                sb.append ("第" + (i + 1) + "行数据的采购码不能为空不存在或已禁用");
                continue;
            }

            if (!stockSuppliesService.judgeAuthByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode (), stockSuppliesList.get (0))) {
                sb.append ("第" + (i + 1) + "行数据的采购码不属于调入仓库");
                continue;
            }

            if (null != stockSuppliesList.get (0).getManageType () && stockSuppliesList.get (0).getManageType ().equals (SuppliesEnum.ManageType.BATCH_MANAGE.getType ())) {
                sb.append ("第" + (i + 1) + "数据采购码不能为批次控制的;");
                continue;
            }

            if (inventoryInPlanLineReqDTO.getNumber () == null || inventoryInPlanLineReqDTO.getNumber () <= 0) {
                sb.append ("计划入库单详细数据的第" + (i + 1) + "条数据的计划入库数量不能小于0");
                continue;
            }
        }
        if (sb.length () != 0) {
            return sb.toString ();
        }

        return null;

    }

    @Override
    public ResponseData selectPlanAssetPurchase(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO, JwtUser user) {
        inventoryInPlanSearchReqDTO.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.ASSET_PURCHASE.getCode ());
        return stockInventoryInPlanHeadService.selectInventoryInPlan (inventoryInPlanSearchReqDTO, user);
    }

    @Override
    public ResponseData selectPlanAssetPurchaseById(Long inventoryInPlanHeadId, JwtUser user) {
        InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO = new InventoryInPlanLineReqDTO();
        inventoryInPlanLineReqDTO.setInventoryInPlanHeadId(inventoryInPlanHeadId);
        return stockInventoryInPlanHeadService.selectInventoryInPlanById (inventoryInPlanLineReqDTO, user, true);
    }

    @Override
    public ResponseData selectPlanAssetPurchaseLineAssetByLineId(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user) {
        if (null == inventoryInPlanLineReqDTO || null == inventoryInPlanLineReqDTO.getInventoryInPlanLineId ()) {
            return ResponseData.createFailResult ("必填参数为空");
        }

        Map<String, Object> result = new HashMap<> (2);
        //授权查询
        List<String> employeeWs = this.stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, null);
        if (CollectionUtils.isEmpty (employeeWs)) {
            return ResponseData.createSuccessResult ("权限不足");
        }

        Long count = stockInventoryInPlanLineAssetsService.selectCountByParam (inventoryInPlanLineReqDTO);
        if (null == count || count <= 0) {
            result.put (DATA_COUNT, 0);
            result.put (DATA_INV_PLAN_LINE_ASSET_RESP_DTO_S, Lists.newArrayList ());
        }

        result.put (DATA_COUNT, count);
        //设置分页
        if (null == inventoryInPlanLineReqDTO.getPageSize ()
                || null == inventoryInPlanLineReqDTO.getPageNum ()) {
            inventoryInPlanLineReqDTO.initPageParam ();
        }
        inventoryInPlanLineReqDTO.setStartNum ((inventoryInPlanLineReqDTO.getPageNum () - 1) * inventoryInPlanLineReqDTO.getPageSize ());

        List<InventoryInPlanLineAssetRespDTO> inventoryInPlanLineAssetRespDTOS = stockInventoryInPlanLineAssetsService.selectByPage (inventoryInPlanLineReqDTO);
        result.put (DATA_INV_PLAN_LINE_ASSET_RESP_DTO_S, inventoryInPlanLineAssetRespDTOS);
        return ResponseData.createSuccessResult (result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData assetPlanPurchaseInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException {
        String checkInBoundParam = checkInBoundParam (inventoryInPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank (checkInBoundParam)) {
            return ResponseData.createFailResult (checkInBoundParam);
        }

        StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead ();
        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = new ArrayList<> ();
        prepareInventoryInPlanStorageDbBeanByDTO (inventoryInPlanHeadReqDTO, stockInventoryInPlanHead, stockInventoryInPlanLineList, user);
        // 更新计划采购入库单
        Boolean updateHeadResult = stockInventoryInPlanHeadService.updatePrimaryKeySelective (stockInventoryInPlanHead);
        if (!updateHeadResult) {
            logger.error ("更新计划资产入库单错误, stockInventoryInPlanHead:{}", stockInventoryInPlanHead.toString ());
            throw new ServiceUncheckedException ("系统错误");
        }

        if (CollectionUtils.isEmpty (stockInventoryInPlanLineList)) {
            logger.error ("更新计划资产入库单行错误, 集合为空");
            throw new ServiceUncheckedException ("系统错误");
        }

        for (StockInventoryInPlanLine stockInventoryInPlanLine : stockInventoryInPlanLineList) {
            Boolean updateLineResult = stockInventoryInPlanLineService.updatePrimaryKeySelective (stockInventoryInPlanLine);
            if (!updateLineResult) {
                logger.error ("更新计划资产入库单行错误, stockInventoryInPlanLine:{}", stockInventoryInPlanLine.toString ());
                throw new ServiceUncheckedException ("系统错误");
            }
        }
        //v5.0 生成计划入库单行下的资产和序列号表
        List<StockInventoryInPlanLinesAssets> stockInventoryInPlanLinesAssetsList = new ArrayList<>();
        List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsList = new ArrayList<>();
        prepareInventoryInAssetsAndSnDTO(inventoryInPlanHeadReqDTO,stockInventoryInPlanLinesAssetsList,stockInventoryInPlanLinesSnsList,user);
        stockInventoryInPlanLineAssetsService.batchInsertStockInventoryPlanLineAssets(stockInventoryInPlanLinesAssetsList);
        stockInventoryInPlanLineSnsService.batchInsertStockInventoryPlanLineSns(stockInventoryInPlanLinesSnsList);


        //生成入库单
        stockInventoryInPlanHeadService.generateStockInventory (inventoryInPlanHeadReqDTO, user);

        //调用采购接口实时更新采购入库验收单状态
        stockPlanHelper.submitPurchaseInventoryInPlan(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId(), user);

        return ResponseData.createSuccessResult ();
    }

    private void prepareInventoryInPlanStorageDbBeanByDTO(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead, List<StockInventoryInPlanLine> stockInventoryInPlanLineList, JwtUser user) {


        //入库单是否是已入库
        Boolean isInventoryInAlready = true;
        if (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS () != null && !inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().isEmpty ()) {

            for (InventoryInPlanLineReqDTO lineReqDTO : inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ()) {
                StockInventoryInPlanLine stockInventoryInPlanLine = stockInventoryInPlanLineService.selectByPrimaryKey (lineReqDTO.getInventoryInPlanLineId ());
                int thisNumber = lineReqDTO.getThisNumber () == null ? 0 : lineReqDTO.getThisNumber ();
                int realNumber = stockInventoryInPlanLine.getRealNumber () == null ? 0 : stockInventoryInPlanLine.getRealNumber ();
                //入库详细状态、入库实际数量、实际入库时间
                if (thisNumber == 0 && realNumber == 0) {
                    isInventoryInAlready = false;
                    continue;
                } else if ((realNumber + thisNumber) < stockInventoryInPlanLine.getNumber ()) {
                    stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.SECTION_IN.getStatus ());
                    stockInventoryInPlanLine.setRealNumber (realNumber + thisNumber);
                    isInventoryInAlready = false;
                } else {
                    stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
                    stockInventoryInPlanLine.setRealNumber (stockInventoryInPlanLine.getNumber ());
                }

                stockInventoryInPlanLine.setUpdatedBy (user.getEmployeeCode ());

                stockInventoryInPlanLineList.add (stockInventoryInPlanLine);
            }
        }

        if (isInventoryInAlready) {
            stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.ALREADY_IN.getCode ());
        } else {
            stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.SECTION_IN.getCode ());
        }
        stockInventoryInPlanHead.setUpdatedBy (user.getEmployeeCode ());
        stockInventoryInPlanHead.setInventoryInPlanHeadId (inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId ());
    }

    private String checkInBoundParam(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException {
        if (null == inventoryInPlanHeadReqDTO || null == inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId () || CollectionUtils.isEmpty (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ())) {
            return "必填参数为空";
        }

        StockInventoryInPlanHead stockInventoryInPlanHead = stockInventoryInPlanHeadService.selectById (inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId ());
        if (stockInventoryInPlanHead == null) {
            return "该计划入库单不存在";
        }

        //仓库权限查询
        List<String> wc = this.stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, stockInventoryInPlanHead.getInWarehouseCode ());
        if (CollectionUtils.isEmpty (wc)) {
            return "无操作仓库的权限";
        }


        //校验入库单详细
        StringBuilder sb = new StringBuilder ();
        if (CollectionUtils.isEmpty (inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ())) {
            return "无可入库的数据";
        }
        List<String> inventoryAssetBatchCode = new ArrayList<> ();
        boolean realNumberAllZero = true;
        for (int i = 0; i < inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().size (); i++) {
            InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO = inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS ().get (i);
            String checkInBoundLineParam = this.checkInBoundLineParam (inventoryInPlanHeadReqDTO, inventoryInPlanLineReqDTO, stockInventoryInPlanHead);
            if (StringUtils.isNotBlank (checkInBoundLineParam)) {
                sb.append (checkInBoundLineParam);
                continue;
            }

            if (inventoryInPlanLineReqDTO.getThisNumber () != null && inventoryInPlanLineReqDTO.getThisNumber () > 0) {
                realNumberAllZero = false;
            }

            if (inventoryInPlanLineReqDTO.getThisNumber () != null) {
                inventoryAssetBatchCode.add (inventoryInPlanLineReqDTO.getInventoryAssetBatchCode ());
            }
        }

        if (sb.length () != 0) {
            return sb.toString ();
        }

        if (realNumberAllZero) {
            return "入库单详细至少有一条数据的本次入库数量大于0";
        }
        if (!CollectionUtils.isEmpty (inventoryAssetBatchCode)) {
            //查询本次导入重复的资产编号
            List<String> assetsNoList = stockInventoryAssetImportService.selectRepeatAsset (inventoryAssetBatchCode);
            if (!CollectionUtils.isEmpty (assetsNoList)) {
                sb.append ("本次导入的资产编号" + assetsNoList.toString () + "重复");
            }
            //查询本次导入重复的sn号
            List<String> snNoList = stockInventoryAssetImportService.selectRepeatSn (inventoryAssetBatchCode);
            if (!CollectionUtils.isEmpty (snNoList)) {
                sb.append ("本次导入的sn编号" + snNoList.toString () + "重复");
            }

            if (sb.length () != 0) {
                return sb.toString ();
            }
        }

        return null;
    }

    /**
     * 校验入库操作行参数
     *
     * @param inventoryInPlanHeadReqDTO
     * @param inventoryInPlanLineReqDTO
     * @param stockInventoryInPlanHead
     * @return
     */
    private String checkInBoundLineParam(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead) throws ParseException {
       //批次号去空格
        if(StringUtils.isNotBlank(inventoryInPlanLineReqDTO.getInventoryAssetBatchCode())){
            inventoryInPlanLineReqDTO.setInventoryAssetBatchCode(inventoryInPlanLineReqDTO.getInventoryAssetBatchCode().trim());
        }

        if (null == inventoryInPlanLineReqDTO.getInventoryInPlanLineId ()) {
            return "数据标识不存在;";
        }

        StockInventoryInPlanLine stockInventoryInPlanLine = stockInventoryInPlanLineService.selectByPrimaryKey (inventoryInPlanLineReqDTO.getInventoryInPlanLineId ());
        if (null == stockInventoryInPlanLine || null == stockInventoryInPlanLine.getInventoryInPlanLineId ()) {
            return "数据不存在;";
        }

        if (!stockInventoryInPlanLine.getInventoryInPlanHeadId ().equals (inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId ())) {
            return "数据与头标识不匹配;";
        }

        List<StockSupplies> stockSuppliesList = stockSuppliesService.getEnableSuppliesList (stockInventoryInPlanLine.getSuppliesCode ());
        if (CollectionUtils.isEmpty (stockSuppliesList)) {
            return "数据的采购码不存在或已禁用;";
        }

        if (stockSuppliesService.judgeAuthByWarehouseCode (inventoryInPlanHeadReqDTO.getInWarehouseCode (), stockSuppliesList.get (0))) {
            return "数据的采购码不属于调入仓库;";
        }

        if (null != stockSuppliesList.get (0).getManageType () && stockSuppliesList.get (0).getManageType ().equals (SuppliesEnum.ManageType.BATCH_MANAGE.getType ())) {
            return "数据采购码不能为批次控制的;";
        }

        if (null == inventoryInPlanLineReqDTO.getThisNumber () && StringUtils.isNotBlank (inventoryInPlanLineReqDTO.getInventoryAssetBatchCode ())) {
            return "本次入库数量为空的数据不能导入资产编号;";
        }


        if (inventoryInPlanLineReqDTO.getThisNumber () != null) {
            if (inventoryInPlanLineReqDTO.getThisNumber () <= 0) {
                return "本次入库数量不能小于等于0;";
            }


            int realNumber = stockInventoryInPlanLine.getRealNumber () == null ? 0 : stockInventoryInPlanLine.getRealNumber ();
            if ((inventoryInPlanLineReqDTO.getThisNumber () + realNumber) > stockInventoryInPlanLine.getNumber ()) {
                return "实际入库数量不能大于计划入库数量;";
            }

            if (StringUtils.isBlank (inventoryInPlanLineReqDTO.getInventoryAssetBatchCode ())) {
                return "行资产卡片数量和本次入库数量不符;";
            }

            InventoryAssetImportSearchReqDTO stockInventoryAssetImport = new InventoryAssetImportSearchReqDTO ();
            stockInventoryAssetImport.setBindBatchCode(StringUtils.strip(inventoryInPlanLineReqDTO.getInventoryAssetBatchCode ()));
            List<StockInventoryAssetImport> stockInventoryAssetImportList = stockInventoryAssetImportService.selectInventoryAssetImportBySelective (stockInventoryAssetImport);
            if (CollectionUtils.isEmpty (stockInventoryAssetImportList) || !inventoryInPlanLineReqDTO.getThisNumber ().equals (stockInventoryAssetImportList.size ())) {
                return "资产卡片数量和本次入库数量不符;";
            }

            List<String> assetList = stockInventoryAssetImportList.stream ()
                    .filter (it -> StringUtils.isNotBlank (it.getAssetCode ()))
                    .map (it -> it.getAssetCode ()).collect (Collectors.toList ());

            if (CollectionUtils.isEmpty (assetList) || !inventoryInPlanLineReqDTO.getThisNumber ().equals (assetList.size ())) {
                return "资产卡片数量和本次入库数量不符;";
            }

            List<StockAssets> stockAssetsList = stockAssetsService.selectAssets (null, assetList);

            if (!CollectionUtils.isEmpty (stockAssetsList)) {
                return "资产编号已存在;";
            }

            String checkInBoundLineSnParamResult = this.checkInBoundLineSnParam (stockInventoryAssetImportList, stockSuppliesList, inventoryInPlanLineReqDTO, stockInventoryInPlanHead);
            if (StringUtils.isNotBlank (checkInBoundLineSnParamResult)) {
                return checkInBoundLineSnParamResult;
            }
        }
        return null;
    }

    /**
     * 校验采购入库行序列号
     *
     * @param stockInventoryAssetImportList
     * @param stockSuppliesList
     * @param inventoryInPlanLineReqDTO
     * @param stockInventoryInPlanHead
     * @return
     */
    private String checkInBoundLineSnParam(List<StockInventoryAssetImport> stockInventoryAssetImportList, List<StockSupplies> stockSuppliesList, InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, StockInventoryInPlanHead stockInventoryInPlanHead) throws ParseException {


        List<String> snNoList = stockInventoryAssetImportList.stream ()
                .filter (it -> StringUtils.isNotBlank (it.getSnNo ()))
                .map (it -> it.getSnNo ()).collect (Collectors.toList ());


        if (null != stockSuppliesList.get (0).getManageType () && stockSuppliesList.get (0).getManageType ().equals (SuppliesEnum.ManageType.SN_MANAGE.getType ())) {
            //判断sn号
            if (CollectionUtils.isEmpty (snNoList)) {
                return "设备序列号不能为空";
            }

            if (!inventoryInPlanLineReqDTO.getThisNumber ().equals (snNoList.size ())) {
                return "设备序列号数量和本次入库数量不符;";
            }

            //批量查询库存
            if (!CollectionUtils.isEmpty (snNoList)) {
                List<StockSuppliesConfigDetail> suppliesConfigDetailList = this.stockSuppliesConfigDetailService.selectUseConfigDetailByBatch (stockInventoryInPlanHead.getInWarehouseCode (), inventoryInPlanLineReqDTO.getSuppliesCode (), DateUtils.dateFormat (new Date (), DateUtils.MONTH_PATTERN), null, snNoList);
                if (!CollectionUtils.isEmpty (suppliesConfigDetailList)) {
                    for (StockSuppliesConfigDetail stockSuppliesConfigDetail : suppliesConfigDetailList) {
                        return "序列号" + stockSuppliesConfigDetail.getSnNo () + "已存在;";
                    }
                }
            }

        } else {
            //判断sn号
            if (!CollectionUtils.isEmpty (snNoList)) {
                return "采购码不支持sn号控制;";
            }
        }

        return null;
    }

    @Override
    public ResponseData assetPlanAssetFileImport(MultipartFile file, JwtUser user) throws IOException {
      //  return stockInventoryAssetImportService.assetFileImport (file, null, user);
          return null;
    }

    @Override
    public ResponseData selectAssetPlanAssetFileImportByBatchCode(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO, JwtUser user) {
        return stockInventoryAssetImportService.selectInventoryAssetImport (inventoryAssetImportSearchReqDTO, user);
    }

    @Override
    public ResponseData assetPurchaseInitialization(Integer batchId, JwtUser user) {

        //生成资产采购计划入库单
        ///插入计划入库单
        StockAssetsInitialization stockAssetsInitialization = new StockAssetsInitialization ();

        stockAssetsInitialization.setBatchId (batchId);
        stockAssetsInitialization.setType (AssetsInitializationEnum.type.PURCHASE.getValue ());
        stockAssetsInitialization.setDeleteFlag (AssetsInitializationEnum.deleteFlag.NO.getValue ());
        List<StockAssetsInitializationInfo> warehouseCodeList = stockAssetsInitializationService.selectWarehouseCodeByBatchId (stockAssetsInitialization);
        if (CollectionUtils.isEmpty (warehouseCodeList)) {
            return ResponseData.createFailResult ("未找到本地需要导入的仓库数据");
        }
        for (StockAssetsInitializationInfo stockAssetsInitializationInfo : warehouseCodeList) {

            stockAssetsInitialization.setWarehouseCode (stockAssetsInitializationInfo.getWarehouseCode ());
            logger.info ("开始初始仓库:{} 的数据", stockAssetsInitializationInfo.getWarehouseCode ());
            String assetPurchaseInitializationResult = stockInventoryInPlanHeadService.assetPurchaseInitialization (batchId, user, stockAssetsInitialization);
            if (StringUtils.isNotBlank (assetPurchaseInitializationResult)) {
                logger.error ("初始仓库:{} 的数据错误:", stockAssetsInitializationInfo);
                throw new RuntimeException(assetPurchaseInitializationResult);
            }

            stockAssetsInitializationService.updateDelFlagByParam (stockAssetsInitializationInfo);
            logger.info ("初始仓库:{} 的数据结束", stockAssetsInitializationInfo.getWarehouseCode ());
        }
        return ResponseData.createSuccessResult ();
    }


    /**
     * 组织行资产表和行序列号表数据
     * @param inventoryInPlanHeadReqDTO
     * @param stockInventoryInPlanLinesAssetsList
     * @param stockInventoryInPlanLinesSnsList
     * @param user
     */
    private void prepareInventoryInAssetsAndSnDTO(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO,List<StockInventoryInPlanLinesAssets> stockInventoryInPlanLinesAssetsList,
                                                  List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsList,JwtUser user){
        List<InventoryInPlanLineReqDTO> inventoryInPlanLineReqDTOS = inventoryInPlanHeadReqDTO.getInventoryInPlanLineReqDTOS();
        InventoryAssetImportSearchReqDTO stockInventoryAssetImport = new InventoryAssetImportSearchReqDTO ();
        for(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO : inventoryInPlanLineReqDTOS){
            if(inventoryInPlanLineReqDTO.getThisNumber() == null || inventoryInPlanLineReqDTO.getThisNumber()==0){
                continue;
            }
            stockInventoryAssetImport.setBindBatchCode(StringUtils.strip(inventoryInPlanLineReqDTO.getInventoryAssetBatchCode()));
            List<StockInventoryAssetImport> stockInventoryAssetImportList = stockInventoryAssetImportService.selectInventoryAssetImportBySelective (stockInventoryAssetImport);
            //入库单与资产关联 资产
            if(!CollectionUtils.isEmpty(stockInventoryAssetImportList)){
                stockInventoryAssetImportList.stream().forEach(assets->{
                    StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets = new StockInventoryInPlanLinesAssets();
                    stockInventoryInPlanLinesAssets.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
                    stockInventoryInPlanLinesAssets.setAssetsCode(assets.getAssetCode());
                    stockInventoryInPlanLinesAssets.setInventoryInPlanLineId(inventoryInPlanLineReqDTO.getInventoryInPlanLineId());
                    stockInventoryInPlanLinesAssets.setDataStatus(CommonEnum.status.YES.getValue());
                    stockInventoryInPlanLinesAssets.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
                    stockInventoryInPlanLinesAssets.setDelFlag(CommonEnum.status.YES.getValue());
                    stockInventoryInPlanLinesAssets.setCreatedAt(new Date());
                    stockInventoryInPlanLinesAssets.setCreatedBy(user.getEmployeeCode());
                    stockInventoryInPlanLinesAssets.setUpdatedAt(new Date());
                    stockInventoryInPlanLinesAssets.setUpdatedBy(user.getEmployeeCode());
                    stockInventoryInPlanLinesAssetsList.add(stockInventoryInPlanLinesAssets);

                    if (StringUtils.isNotBlank(assets.getSnNo())){
                        StockInventoryInPlanLinesSns stockinventoryinplanlinessns = new StockInventoryInPlanLinesSns();
                        stockinventoryinplanlinessns.setInventoryInPlanHeadId(inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId());
                        stockinventoryinplanlinessns.setSnNo(assets.getSnNo());
                        stockinventoryinplanlinessns.setInventoryInPlanLineId(inventoryInPlanLineReqDTO.getInventoryInPlanLineId());
                        stockinventoryinplanlinessns.setDataStatus(CommonEnum.status.YES.getValue());
                        stockinventoryinplanlinessns.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
                        stockinventoryinplanlinessns.setDelFlag(CommonEnum.status.YES.getValue());
                        stockinventoryinplanlinessns.setCreatedAt(new Date());
                        stockinventoryinplanlinessns.setCreatedBy(user.getEmployeeCode());
                        stockinventoryinplanlinessns.setUpdatedAt(new Date());
                        stockinventoryinplanlinessns.setUpdatedBy(user.getEmployeeCode());
                        stockInventoryInPlanLinesSnsList.add(stockinventoryinplanlinessns);
                    }
                });
            }

        }
    }


    @Override
    public ResponseData cancelPlanAssetPurchaseById(Long inventoryInPlanHeadId) {
        return stockInventoryInPlanHeadService.cancelInventoryInPlanById(inventoryInPlanHeadId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanInventoryPurchase(List<PurchaseInventoryInHeadDTO> purchaseInventoryInHeadDTOs, JwtUser user) {
        String checkReturn = checkSavePurchaseParam (purchaseInventoryInHeadDTOs, user);
        if (StringUtils.isNotBlank (checkReturn)) {
            return ResponseData.createFailResult (checkReturn);
        }

        //存储所有的入库申请单行
        List<StockInventoryInPlanLine> stockInventoryInPlanLineList = new ArrayList<> ();

        //存储所有的入库单行
        List<StockInventoryInSupplies> stockInventoryInSuppliesList = new ArrayList<>();

        //保存返回结果
        List<PurchaseInventoryInHeadRespDTO> purchaseInventoryInHeadRespDTOs = new ArrayList<>(purchaseInventoryInHeadDTOs.size());
        Map<Long,List<PurchaseInventoryInLineRespDTO>> listMap = new HashMap<>(purchaseInventoryInHeadDTOs.size());

        for (PurchaseInventoryInHeadDTO purchaseInventoryInHeadDTO : purchaseInventoryInHeadDTOs){

            StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead ();
            //存储一个单据的入库申请单行
            List<StockInventoryInPlanLine> stockInventoryInPlanLines = new ArrayList<> ();

            //准备插入参数
            prepareSavePurchaseDbBeanDTO (purchaseInventoryInHeadDTO, stockInventoryInPlanHead, stockInventoryInPlanLines, user);

            ///插入计划入库单
            stockInventoryInPlanHeadService.insert (stockInventoryInPlanHead);

            //导入计划入库单行
            if (!CollectionUtils.isEmpty (stockInventoryInPlanLines)) {
                //查找入库单
                stockInventoryInPlanLines.forEach (stockInventoryInPlanLine -> stockInventoryInPlanLine.setInventoryInPlanHeadId (stockInventoryInPlanHead.getInventoryInPlanHeadId ()));
                stockInventoryInPlanLineList.addAll(stockInventoryInPlanLines);
            }
            //生成返回数据头表
            PurchaseInventoryInHeadRespDTO purchaseInventoryInHeadRespDTO = new PurchaseInventoryInHeadRespDTO();
            purchaseInventoryInHeadRespDTO.setBizNo(purchaseInventoryInHeadDTO.getBizNo());
            purchaseInventoryInHeadRespDTO.setPurchaseOrderNo(purchaseInventoryInHeadDTO.getPurchaseOrderNo());
            purchaseInventoryInHeadRespDTO.setInventoryInHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());
            purchaseInventoryInHeadRespDTO.setInventoryInPlanNo(stockInventoryInPlanHead.getInventoryInPlanNo());
            purchaseInventoryInHeadRespDTOs.add(purchaseInventoryInHeadRespDTO);

            //如果单据类型为 purchaseInventoryInHeadDTO.getDocType()为14 则同时生成入库单据
            if(InventoryEnum.InType.CARD_PURCHASE.getCode().equals(purchaseInventoryInHeadDTO.getDocType())){
                StockInventoryIn stockInventoryIn = new StockInventoryIn ();
                //存储一个单据的入库申请单行
                List<StockInventoryInSupplies> stockInventoryInSupplies = new ArrayList<> (purchaseInventoryInHeadDTOs.size());
                List<InventoryInSuppliesDTO> inventoryInSuppliesDTOList = new ArrayList<>(purchaseInventoryInHeadDTOs.size());

                prepareGenerateInventoryIn(stockInventoryInPlanHead,stockInventoryIn,stockInventoryInSupplies,user,stockInventoryInPlanLineList,inventoryInSuppliesDTOList);

                stockInventoryIn.setInventoryInPlanHeadId(stockInventoryInPlanHead.getInventoryInPlanHeadId());

                //插入入库单
                inventoryInMapper.insert (stockInventoryIn);

                //入库单行
                if (!CollectionUtils.isEmpty (stockInventoryInSupplies)) {
                    //查找入库单
                    stockInventoryInSupplies.forEach (inventoryInSupplies -> inventoryInSupplies.setInventoryInId (stockInventoryIn.getInventoryInId()));
                    stockInventoryInSuppliesList.addAll(stockInventoryInSupplies);
                }
                //增加库存记录
                addStockSuppliesConfig (stockInventoryIn, inventoryInSuppliesDTOList, user);
            }

        }

        //插入计划入库申请单行
        Integer count = stockInventoryInPlanLineService.batchInsertStockInventoryPlanLine (stockInventoryInPlanLineList);
        if (count == null || count < 1) {
            logger.error ("采购对接生成申请入库单错误，验收单编号：{}", purchaseInventoryInHeadDTOs.get(0).getBizNo());
            throw new ServiceUncheckedException (ResponseCode.SYSTEM_ERROR.getMessage ());
        }

        if(!CollectionUtils.isEmpty(stockInventoryInSuppliesList)){
            Integer insertSuppliesCount = inventoryInSuppliesService.insertStockInventoryInSuppliesList (stockInventoryInSuppliesList);
            if (insertSuppliesCount == null || insertSuppliesCount < 1) {
                logger.error ("批量插入入库单详细失败，入库单编号：{}", purchaseInventoryInHeadDTOs.get(0).getBizNo());
                throw new ServiceUncheckedException (ResponseCode.SYSTEM_ERROR.getMessage ());
            }
        }

        //生成返回数据行表
        stockInventoryInPlanLineList.stream().forEach(dto->{
            PurchaseInventoryInLineRespDTO purchaseInventoryInLineRespDTO = new PurchaseInventoryInLineRespDTO();
            purchaseInventoryInLineRespDTO.setInventoryInPlanLineId(dto.getInventoryInPlanLineId());
            purchaseInventoryInLineRespDTO.setReceiveItemNo(dto.getReceiveItemNo());
            if (listMap.get(dto.getInventoryInPlanHeadId()) != null){
                listMap.get(dto.getInventoryInPlanHeadId()).add(purchaseInventoryInLineRespDTO);
            }else{
                List<PurchaseInventoryInLineRespDTO> purchaseInventoryInLineRespDTOList = new ArrayList<>();
                purchaseInventoryInLineRespDTOList.add(purchaseInventoryInLineRespDTO);
                listMap.put(dto.getInventoryInPlanHeadId(),purchaseInventoryInLineRespDTOList);
            }
        });
        purchaseInventoryInHeadRespDTOs.stream().forEach(head->{
            head.setPurchaseInventoryInLineRespDTOs(listMap.get(head.getInventoryInHeadId()));
        });

        return ResponseData.createSuccessResult (purchaseInventoryInHeadRespDTOs);
    }


    private void prepareGenerateInventoryIn(StockInventoryInPlanHead stockInventoryInPlanHead,StockInventoryIn stockInventoryIn,List<StockInventoryInSupplies> stockInventoryInSuppliesList,JwtUser user,
                                            List<StockInventoryInPlanLine> stockInventoryInPlanLines,List<InventoryInSuppliesDTO> inventoryInSuppliesDTOList){
        String employeeCode = user.getEmployeeCode();
        String poNo = OrderUtil.getOrderNo (OrderEnum.IN_INVENTORY);
        stockInventoryIn.setInventoryInNo (poNo);
        stockInventoryIn.setStatus (InventoryEnum.Status.ALREADY_IN.getCode ());
        stockInventoryIn.setCreatedBy (user.getEmployeeCode ());
        stockInventoryIn.setInWarehouseCode (stockInventoryInPlanHead.getInWarehouseCode());
        stockInventoryIn.setInventoryInType (InventoryEnum.InType.CARD_PURCHASE.getCode());
        stockInventoryIn.setVendorCode (stockInventoryInPlanHead.getVendorCode ());
        stockInventoryIn.setVendorName (stockInventoryInPlanHead.getVendorName ());
        stockInventoryIn.setBizNo (stockInventoryInPlanHead.getBizNo ());
        stockInventoryIn.setDeliveryNo ("");
        stockInventoryIn.setPlanInTime (new Date ());
        stockInventoryIn.setBillingUser (employeeCode);
        stockInventoryIn.setRemark ("");
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode (stockInventoryInPlanHead.getInWarehouseCode());
        //收货人为仓库的linkman
        if (stockWarehouseBase != null) {
            stockInventoryIn.setReceiveUser (stockWarehouseBase.getLinkman ());
        }
        stockInventoryIn.setOutWarehouseCode ("");
        stockInventoryIn.setBillingTime (new Date ());

        stockInventoryIn.setDutyUser (employeeCode);
        stockInventoryIn.setUpdatedBy (employeeCode);
        //获取仓库信息
        StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode (stockInventoryInPlanHead.getInWarehouseCode(), WarehouseEnum.Status.NORMAL.getStatus ());
        stockInventoryIn.setDeptCode (stockWarehouse.getCostCenterCode ());
        stockInventoryIn.setInventoryInUser (employeeCode);

        //入库单详细信息
        if (!CollectionUtils.isEmpty(stockInventoryInPlanLines)) {
            for (StockInventoryInPlanLine stockInventoryInPlanLine : stockInventoryInPlanLines) {
                StockInventoryInSupplies stockInventoryInSupplies = new StockInventoryInSupplies ();
                //物料编码
                stockInventoryInSupplies.setSuppliesCode (stockInventoryInPlanLine.getSuppliesCode ());
                stockInventoryInSupplies.setPrintLabelType (InventoryInSuppliesEnum.PrintLabelType.NOT_PRINT.getCode ());
                //计划入库数量
                stockInventoryInSupplies.setRealNumber (stockInventoryInPlanLine.getNumber ());
                stockInventoryInSupplies.setNumber(stockInventoryInPlanLine.getNumber ());
                stockInventoryInSupplies.setStatus (InventoryInSuppliesEnum.Status.ALREADY_IN.getStatus ());
                stockInventoryInSupplies.setInventoryInTime (new Date ());

                stockInventoryInSupplies.setCreatedBy (user.getEmployeeCode ());
                stockInventoryInSupplies.setUpdatedBy (user.getEmployeeCode ());
                stockInventoryInSupplies.setQuality (InventoryInSuppliesEnum.Quality.GOOD.getType ());
                stockInventoryInSuppliesList.add (stockInventoryInSupplies);

                InventoryInSuppliesDTO suppliesDTO = ConvertUtil.convertToType(InventoryInSuppliesDTO.class,stockInventoryInSupplies);
                suppliesDTO.setThisNumber (stockInventoryInPlanLine.getNumber ());
                inventoryInSuppliesDTOList.add(suppliesDTO);
            }
        }
    }


    /**
     * 增加库存记录
     *
     * @param stockInventoryIn
     * @param inventoryInSuppliesDTOList
     * @param user
     */
    private void addStockSuppliesConfig(StockInventoryIn stockInventoryIn, List<InventoryInSuppliesDTO> inventoryInSuppliesDTOList, JwtUser user) {
        if (stockInventoryIn == null || CollectionUtils.isEmpty (inventoryInSuppliesDTOList)) {
            return;
        }
        for (InventoryInSuppliesDTO inventoryInSuppliesDTO : inventoryInSuppliesDTOList) {
            if (inventoryInSuppliesDTO.getThisNumber () == null || inventoryInSuppliesDTO.getThisNumber () == 0) {
                continue;
            }
//            StockSuppliesConfig config = new StockSuppliesConfig ();
//            config.setWarehouseCode (stockInventoryIn.getInWarehouseCode ());
//            config.setSuppliesCode (inventoryInSuppliesDTO.getSuppliesCode ());
//            config.setAllNumber (inventoryInSuppliesDTO.getThisNumber ().longValue ());
//            config.setUpdatedBy (user.getEmployeeCode ());
//            config.setCreatedBy (user.getEmployeeCode ());

            //改造后
            StockSuppliesQuantity addStockSuppliesQuantity = new StockSuppliesQuantity();
            addStockSuppliesQuantity.setWarehouseCode(stockInventoryIn.getInWarehouseCode());
            addStockSuppliesQuantity.setSuppliesCode(inventoryInSuppliesDTO.getSuppliesCode());
            addStockSuppliesQuantity.setQuantity(new BigDecimal(inventoryInSuppliesDTO.getThisNumber()));
            addStockSuppliesQuantity.setStatus(StockSuppliesQuantityEnum.status.IN_STOCK.getValue());
            addStockSuppliesQuantity.setUpdatedBy(user.getEmployeeCode());
            addStockSuppliesQuantity.setCreatedBy(user.getEmployeeCode());

            //库存明细
            StockSupplies stockSupplies = suppliesService.getSuppliesByCode (inventoryInSuppliesDTO.getSuppliesCode (), null);
            List<StockSuppliesConfigDetail> configDetails = null;
            if (!stockSupplies.getManageType ().equals (SuppliesEnum.ManageType.NOT_MANAGE.getType ())) {
                configDetails = new ArrayList<> ();
                StockSuppliesConfigDetail detail = new StockSuppliesConfigDetail ();
                detail.setWarehouseCode (stockInventoryIn.getInWarehouseCode ());
                detail.setSuppliesCode (stockSupplies.getCode ());
                detail.setConfigType (stockSupplies.getManageType ());
                if (stockSupplies.getManageType ().equals (SuppliesEnum.ManageType.BATCH_MANAGE.getType ())) {
                    detail.setBatchNo (inventoryInSuppliesDTO.getBatchNo ());
                } else {
                    detail.setSnNo (inventoryInSuppliesDTO.getBatchNo ());
                }
                detail.setAllNumber (inventoryInSuppliesDTO.getThisNumber ().longValue ());
                detail.setUpdatedBy (user.getEmployeeCode ());
                detail.setCreatedBy (user.getEmployeeCode ());
                configDetails.add (detail);
            }
            this.suppliesConfigService.addTotalInventory (addStockSuppliesQuantity, configDetails);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData purchaseAssetsAccountSync(List<PurchaseAssetsSyncBillReqDTO> purchaseAssetsSyncBillReqDTOs, JwtUser user) {
        //数据校验
        String checkReturn = checkPurchaseAccountParam (purchaseAssetsSyncBillReqDTOs);
        if (StringUtils.isNotBlank (checkReturn)) {
            return ResponseData.createFailResult (checkReturn);
        }

        List<PurchaseStatementAssetsNote> purchaseStatementAssetsNotes = new ArrayList<>();
        Date date = new Date();
        //更新数据
        for(PurchaseAssetsSyncBillReqDTO purchaseAssetsSyncBillReqDTO : purchaseAssetsSyncBillReqDTOs){
            //
            List<StockAssets> stockAssetsList = stockAssetsService.selectAssetsByReceiveItemNo(purchaseAssetsSyncBillReqDTO.getReceiveItemNo(),purchaseAssetsSyncBillReqDTO.getAccountNum());
            if (CollectionUtils.isEmpty(stockAssetsList) || !purchaseAssetsSyncBillReqDTO.getAccountNum().equals(stockAssetsList.size())){
                logger.error("结算单结算数量和待结算资产数量不匹配,验收单号{}，结算单号{},结算数量{}",purchaseAssetsSyncBillReqDTO.getReceiveItemNo(),purchaseAssetsSyncBillReqDTO.getAccountantCode(),purchaseAssetsSyncBillReqDTO.getAccountNum());
            }else{

                /**
                 * 思路：
                 * 1、计算出新的总的结算不含税价（通过结算单价 除税率 乘结算数量）
                 * 2、计算出旧的总的结算不含税价 （结算不含税价 乘 结算数量）
                 * 3、通过新 旧 算出差额
                 * 4、将差额分摊到最后一个资产上
                 * 5、更新资产的结算不含税价
                 */
                // 计算税率小数形式
                BigDecimal taxRateDecimal = purchaseAssetsSyncBillReqDTO.getTaxRate().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                // 计算不含税单价
                BigDecimal statementUnitPriceExcludingTax = purchaseAssetsSyncBillReqDTO.getStatementUnitPrice().divide(BigDecimal.ONE.add(taxRateDecimal), 4, RoundingMode.HALF_UP);
                // 1、计算新的不含税总额
                BigDecimal newTotalExcludingTax = statementUnitPriceExcludingTax.multiply(new BigDecimal(purchaseAssetsSyncBillReqDTO.getAccountNum())).setScale(2, RoundingMode.HALF_UP);
                // 2、计算旧的不含税总额
                BigDecimal oldTotalExcludingTax = purchaseAssetsSyncBillReqDTO.getStatementPriceExcludingTax().multiply(new BigDecimal(purchaseAssetsSyncBillReqDTO.getAccountNum())).setScale(2, RoundingMode.HALF_UP);
                // 3、计算差额
                BigDecimal difference = newTotalExcludingTax.subtract(oldTotalExcludingTax).setScale(2, RoundingMode.HALF_UP);
                // 4
                StockAssets lastAsset = stockAssetsList.get(stockAssetsList.size() - 1);
                stockAssetsList.stream().forEach(dto->{
                    // 判断是否是最后一个资产
                    if(dto.equals(lastAsset)){
                        purchaseAssetsSyncBillReqDTO.setStatementPriceExcludingTax(purchaseAssetsSyncBillReqDTO.getStatementPriceExcludingTax().add(difference));
                    }
                    dto.setBillCode(purchaseAssetsSyncBillReqDTO.getBillCode());
                    dto.setPayDate(purchaseAssetsSyncBillReqDTO.getPayDate());
                    dto.setAccountantLineCode(purchaseAssetsSyncBillReqDTO.getAccountantLineCode());
                    dto.setAccountantCode(purchaseAssetsSyncBillReqDTO.getAccountantCode());
                    //dto.setExtractStatus(AssetsEnum.ExtractStatus.WAIT_EXTRACT.getValue());
                    dto.setInvoiceType(purchaseAssetsSyncBillReqDTO.getInvoiceType());
                    dto.setStatementUnitPrice(purchaseAssetsSyncBillReqDTO.getStatementUnitPrice());
                    dto.setStatementPriceExcludingTax(purchaseAssetsSyncBillReqDTO.getStatementPriceExcludingTax());
                    if (AssetsEnum.invoiceType.VAT_INVOICE.getValue().equals(purchaseAssetsSyncBillReqDTO.getInvoiceType())) {
                        dto.setInitialValue(purchaseAssetsSyncBillReqDTO.getStatementPriceExcludingTax());
                    }else{
                        dto.setInitialValue(purchaseAssetsSyncBillReqDTO.getStatementUnitPrice());
                    }

                    //生成对接记录信息
                    PurchaseStatementAssetsNote purchaseStatementAssetsNote = new PurchaseStatementAssetsNote();
                    purchaseStatementAssetsNote.setAccountantCode(purchaseAssetsSyncBillReqDTO.getAccountantCode());
                    purchaseStatementAssetsNote.setAccountantLineCode(purchaseAssetsSyncBillReqDTO.getAccountantLineCode());
                    purchaseStatementAssetsNote.setAccountNum(purchaseAssetsSyncBillReqDTO.getAccountNum());
                    purchaseStatementAssetsNote.setAssetsCode(dto.getAssetsCode());
                    purchaseStatementAssetsNote.setInvoiceType(purchaseAssetsSyncBillReqDTO.getInvoiceType());
                    purchaseStatementAssetsNote.setBillCode(purchaseAssetsSyncBillReqDTO.getBillCode());
                    purchaseStatementAssetsNote.setPayDate(purchaseAssetsSyncBillReqDTO.getPayDate());
                    purchaseStatementAssetsNote.setStatementUnitPrice(purchaseAssetsSyncBillReqDTO.getStatementUnitPrice());
                    purchaseStatementAssetsNote.setStatementPriceExcludingTax(purchaseAssetsSyncBillReqDTO.getStatementPriceExcludingTax());
                    purchaseStatementAssetsNote.setTaxRate(purchaseAssetsSyncBillReqDTO.getTaxRate());
                    purchaseStatementAssetsNote.setReceiveItemNo(purchaseAssetsSyncBillReqDTO.getReceiveItemNo());
                    purchaseStatementAssetsNote.setCreatedBy(user.getEmployeeCode());
                    purchaseStatementAssetsNote.setUpdatedBy(user.getEmployeeCode());
                    purchaseStatementAssetsNote.setCreatedAt(date);
                    purchaseStatementAssetsNote.setUpdatedAt(date);
                    purchaseStatementAssetsNotes.add(purchaseStatementAssetsNote);
                });
                stockAssetsService.updateMultipleSelective(stockAssetsList);
            }
        }

        //保存对接记录信息
        if (!CollectionUtils.isEmpty(purchaseStatementAssetsNotes)) {
            purchaseStatementAssetsNoteService.batchInsert(purchaseStatementAssetsNotes);
        }
        return ResponseData.createSuccessResult();
    }

    /**
     * 校验费用信息参数
     * @param purchaseAssetsSyncBillReqDTOs
     * @return
     */
    private String checkPurchaseAccountParam(List<PurchaseAssetsSyncBillReqDTO> purchaseAssetsSyncBillReqDTOs) {
        if (CollectionUtils.isEmpty(purchaseAssetsSyncBillReqDTOs)){
            return "参数不能为空";
        }

        StringBuilder stringBuilder = new StringBuilder();
        for(int i = 0;i<purchaseAssetsSyncBillReqDTOs.size();i++){
            //验收单行编码不能为空
            if (StringUtils.isBlank(purchaseAssetsSyncBillReqDTOs.get(i).getReceiveItemNo())) {
                stringBuilder.append("参数列表中，第" + i + "行数据的验收单行为空;");
            }
            //结算单编码不能为空
            if (StringUtils.isBlank(purchaseAssetsSyncBillReqDTOs.get(i).getAccountantCode())){
                stringBuilder.append("参数列表中，第"+i+"行数据的结算单编码为空;");
            }
            //结算单行编码不能为空
            if (StringUtils.isBlank(purchaseAssetsSyncBillReqDTOs.get(i).getAccountantLineCode())){
                stringBuilder.append("参数列表中，第"+i+"行数据的结算单行编码为空;");
            }
            //结算数量大于0
            if (purchaseAssetsSyncBillReqDTOs.get(i).getAccountNum() == null || purchaseAssetsSyncBillReqDTOs.get(i).getAccountNum()<=0){
                stringBuilder.append("参数列表中，第"+i+"行数据的结算数量为空或小于0;");
            }
            //结算日期不能为空
            if (purchaseAssetsSyncBillReqDTOs.get(i).getPayDate() == null){
                stringBuilder.append("参数列表中，第"+i+"行数据的结算日期为空;");
            }
            //发票单据号不能为空
            if (StringUtils.isBlank(purchaseAssetsSyncBillReqDTOs.get(i).getBillCode())){
                stringBuilder.append("参数列表中，第"+i+"行数据的发票单据号为空;");
            }
        }
        if (stringBuilder.length() != 0){
            return stringBuilder.toString();
        }

        return null;
    }

    /**
     * 根据验收单下的采购当单，生成多张采购申请入库单
     * @param purchaseInventoryInHeadDTO
     * @param stockInventoryInPlanHead
     * @param stockInventoryInPlanLineList
     * @param user
     */
    private void prepareSavePurchaseDbBeanDTO(PurchaseInventoryInHeadDTO purchaseInventoryInHeadDTO, StockInventoryInPlanHead stockInventoryInPlanHead, List<StockInventoryInPlanLine> stockInventoryInPlanLineList, JwtUser user) {
        if(InventoryInPlanHeadEnum.InType.CARD_PURCHASE.getCode().equals(purchaseInventoryInHeadDTO.getDocType())){
            stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.ALREADY_IN.getCode ());
        }else{
            stockInventoryInPlanHead.setStatus (InventoryInPlanHeadEnum.Status.WAIT_IN.getCode ());
        }
        stockInventoryInPlanHead.setCreatedBy (user.getEmployeeCode ());
        stockInventoryInPlanHead.setInWarehouseCode (purchaseInventoryInHeadDTO.getWarehouseCode ());
        stockInventoryInPlanHead.setWarehouseType(purchaseInventoryInHeadDTO.getWarehouseType());
        if (InventoryInPlanHeadEnum.InType.GPS_PURCHASE.getCode().equals(purchaseInventoryInHeadDTO.getDocType())){
            stockInventoryInPlanHead.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.GPS_PURCHASE.getCode ());
        }else if(InventoryInPlanHeadEnum.InType.CARD_PURCHASE.getCode().equals(purchaseInventoryInHeadDTO.getDocType())){
            stockInventoryInPlanHead.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.CARD_PURCHASE.getCode());
        }else{
            stockInventoryInPlanHead.setInventoryInPlanType (InventoryInPlanHeadEnum.InType.ASSET_PURCHASE.getCode ());
        }
        stockInventoryInPlanHead.setVendorCode (purchaseInventoryInHeadDTO.getVendorCode ());
        stockInventoryInPlanHead.setVendorName(purchaseInventoryInHeadDTO.getVendorName());
        stockInventoryInPlanHead.setBizNo (purchaseInventoryInHeadDTO.getBizNo ());
        stockInventoryInPlanHead.setDeliveryNo (purchaseInventoryInHeadDTO.getDeliveryNo ());
        stockInventoryInPlanHead.setPlanInTime (purchaseInventoryInHeadDTO.getPlanInTime());
        stockInventoryInPlanHead.setBillingUser (purchaseInventoryInHeadDTO.getBillingUser ());
        stockInventoryInPlanHead.setCompanyCode(purchaseInventoryInHeadDTO.getCompanyCode());
        stockInventoryInPlanHead.setSystemCode(purchaseInventoryInHeadDTO.getSystemCode());
        stockInventoryInPlanHead.setPurchaseOrderNo(purchaseInventoryInHeadDTO.getPurchaseOrderNo());
        stockInventoryInPlanHead.setDemandDeptCode(purchaseInventoryInHeadDTO.getDemandDeptCode());
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode (purchaseInventoryInHeadDTO.getWarehouseCode ());
        //收货人为仓库的linkman
        if (stockWarehouseBase != null) {
            stockInventoryInPlanHead.setReceiveUser (stockWarehouseBase.getLinkman ());
        }
        stockInventoryInPlanHead.setBillingTime (new Date ());
        stockInventoryInPlanHead.setDutyUser (purchaseInventoryInHeadDTO.getBillingUser ());
        stockInventoryInPlanHead.setCreatedAt(new Date());
        stockInventoryInPlanHead.setCreatedBy(user.getEmployeeCode());
        stockInventoryInPlanHead.setUpdatedAt(new Date());
        stockInventoryInPlanHead.setUpdatedBy (user.getEmployeeCode ());
        //计划入库单行

        if (!CollectionUtils.isEmpty (purchaseInventoryInHeadDTO.getPurchaseInventoryInLineDTOs())) {
            for (PurchaseInventoryInLineDTO purchaseInventoryInLineDTO : purchaseInventoryInHeadDTO.getPurchaseInventoryInLineDTOs()) {
                StockInventoryInPlanLine stockInventoryInPlanLine = new StockInventoryInPlanLine ();
                //物料编码
                stockInventoryInPlanLine.setSuppliesCode (purchaseInventoryInLineDTO.getSuppliesCode ());
                if(InventoryInPlanHeadEnum.InType.CARD_PURCHASE.getCode().equals(purchaseInventoryInHeadDTO.getDocType())){
                    stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
                    stockInventoryInPlanLine.setRealNumber (purchaseInventoryInLineDTO.getReceiveNum());
                }else{
                    stockInventoryInPlanLine.setStatus (InventoryInPlanLineEnum.Status.WAIT_IN.getStatus ());
                    stockInventoryInPlanLine.setRealNumber (0);
                }
                //计划入库数量
                stockInventoryInPlanLine.setNumber (purchaseInventoryInLineDTO.getReceiveNum());
                BeanUtils.copyProperties(purchaseInventoryInLineDTO,stockInventoryInPlanLine);
//                stockInventoryInPlanLine.setReceiveItemNo(purchaseInventoryInLineDTO.getReceiveItemNo());
//                stockInventoryInPlanLine.setPurchaseOrderLineId(purchaseInventoryInLineDTO.getPurchaseOrderLineId());
//                stockInventoryInPlanLine.setUnitPrice(purchaseInventoryInLineDTO.getUnitPrice());
//                stockInventoryInPlanLine.setPriceExcludingTax(purchaseInventoryInLineDTO.getPriceExcludingTax());
//                stockInventoryInPlanLine.setUnitTaxRate(purchaseInventoryInLineDTO.getUnitTaxRate());
//                stockInventoryInPlanLine.setSimUnitPrice(purchaseInventoryInLineDTO.getSimUnitPrice());
//                stockInventoryInPlanLine.setSimTaxRate(purchaseInventoryInLineDTO.getSimTaxRate());
//                stockInventoryInPlanLine.setServeUnitPrice(purchaseInventoryInLineDTO.getServeUnitPrice());
//                stockInventoryInPlanLine.setServeTaxRate(purchaseInventoryInLineDTO.getServeTaxRate());
//                stockInventoryInPlanLine.setSimCompanyCode(purchaseInventoryInLineDTO.getSimCompanyCode());
//                stockInventoryInPlanLine.setServeCompanyCode(purchaseInventoryInLineDTO.getServeCompanyCode());
//                stockInventoryInPlanLine.setSimReNewUnitPrice(purchaseInventoryInLineDTO.getSimReNewUnitPrice());
//                stockInventoryInPlanLine.setSimReNewTaxRate(purchaseInventoryInLineDTO.getSimReNewTaxRate());
//                stockInventoryInPlanLine.setServeReNewUnitPrice(purchaseInventoryInLineDTO.getServeReNewUnitPrice());
//                stockInventoryInPlanLine.setServeReNewTaxRate(purchaseInventoryInLineDTO.getServeReNewTaxRate());
//                stockInventoryInPlanLine.setSimGroupReNewUnitPrice(purchaseInventoryInLineDTO.getSimGroupReNewUnitPrice());
//                stockInventoryInPlanLine.setSimGroupReNewTaxRate(purchaseInventoryInLineDTO.getSimGroupReNewTaxRate());
//                stockInventoryInPlanLine.setServeGroupReNewUnitPrice(purchaseInventoryInLineDTO.getServeGroupReNewUnitPrice());
//                stockInventoryInPlanLine.setServeGroupReNewTaxRate(purchaseInventoryInLineDTO.getServeGroupReNewTaxRate());
                stockInventoryInPlanLine.setTotalPrice(purchaseInventoryInLineDTO.getUnitPrice().multiply(new BigDecimal(purchaseInventoryInLineDTO.getReceiveNum())));
                stockInventoryInPlanLine.setCurrency(purchaseInventoryInLineDTO.getCurrency());
                stockInventoryInPlanLine.setCreatedBy (user.getEmployeeCode ());
                stockInventoryInPlanLine.setUpdatedBy (user.getEmployeeCode ());
                stockInventoryInPlanLine.setPlanInTime(purchaseInventoryInHeadDTO.getPlanInTime());

                stockInventoryInPlanLineList.add (stockInventoryInPlanLine);
            }
        }
    }

    /**
     * 校验采购系统传入参数
     * @param purchaseInventoryInHeadDTOs
     * @param user
     * @return
     */
    private String checkSavePurchaseParam(List<PurchaseInventoryInHeadDTO> purchaseInventoryInHeadDTOs, JwtUser user) {
        if (CollectionUtils.isEmpty(purchaseInventoryInHeadDTOs)){
            return "参数的参数为空";
        }
        for(PurchaseInventoryInHeadDTO purchaseInventoryInHeadDTO : purchaseInventoryInHeadDTOs){
            //校验当前验收单据没有重复提交
            String bizNo = purchaseInventoryInHeadDTO.getBizNo();
            if (StringUtils.isBlank(bizNo)){
                return "验收单号不能为空";
            }
            StockInventoryInPlanHead stockInventoryInPlanHead = new StockInventoryInPlanHead();
            stockInventoryInPlanHead.setBizNo(purchaseInventoryInHeadDTO.getBizNo());
            stockInventoryInPlanHead.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.ASSET_PURCHASE.getCode());
            if (!CollectionUtils.isEmpty(stockInventoryInPlanHeadService.selectByParam(stockInventoryInPlanHead))){
                return "该验收单据不能重复推送";
            }
            if (StringUtils.isBlank(purchaseInventoryInHeadDTO.getBillingUser())){
                return "制单人不能为空";
            }
            if (StringUtils.isBlank(purchaseInventoryInHeadDTO.getCompanyCode())){
                return "公司编码不能为空";
            }
            if (StringUtils.isBlank(purchaseInventoryInHeadDTO.getDemandDeptCode())){
                return "需求部门编码不能为空";
            }
            if (StringUtils.isBlank(purchaseInventoryInHeadDTO.getPurchaseOrderNo())){
                return "采购订单号不能为空";
            }
            if (StringUtils.isBlank(purchaseInventoryInHeadDTO.getVendorCode())){
                return "供应商编码不能为空";
            }
            if (StringUtils.isBlank(purchaseInventoryInHeadDTO.getWarehouseCode())){
                return "入库仓库编码不能为空";
            }
            if (purchaseInventoryInHeadDTO.getPlanInTime() == null){
                return "计划入库时间不能为空";
            }
            if (purchaseInventoryInHeadDTO.getWarehouseType() == null){
                return "入库仓库所属类型不能为空";
            }
            if (CollectionUtils.isEmpty(purchaseInventoryInHeadDTO.getPurchaseInventoryInLineDTOs())){
                return "订单行不能为空";
            }

            List<PurchaseInventoryInLineDTO> purchaseInventoryInLineDTOs = purchaseInventoryInHeadDTO.getPurchaseInventoryInLineDTOs();
            String paramResult = checkInventoryInLineParam(purchaseInventoryInLineDTOs);
            if (StringUtils.isNotBlank(paramResult)){
                return paramResult;
            }
        }
        return null;
    }

    private String checkInventoryInLineParam(List<PurchaseInventoryInLineDTO> purchaseInventoryInLineDTOs){
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<purchaseInventoryInLineDTOs.size();i++){
            if (purchaseInventoryInLineDTOs.get(i).getPurchaseOrderLineId() == null){
                sb.append("订单下第："+i+"行，订单行id为空");
            }
            if (StringUtils.isBlank(purchaseInventoryInLineDTOs.get(i).getReceiveItemNo())){
                sb.append("订单下第："+i+"行，验收单行编码为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getReceiveNum() == null){
                sb.append("订单下第："+i+"行，验收数量为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getUnitPrice() == null){
                sb.append("订单下第："+i+"行，物料单价为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getPriceExcludingTax() == null){
                sb.append("订单下第："+i+"行，物料不含税价为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getUnitTaxRate() == null){
                sb.append("订单下第："+i+"行，物料单价税率为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getSimUnitPrice() == null){
                sb.append("订单下第："+i+"行，物料sim卡单价为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getSimTaxRate() == null){
                sb.append("订单下第："+i+"行，物料sim卡税率为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getServeUnitPrice() == null){
                sb.append("订单下第："+i+"行，物料平台服务费单价为空");
            }
            if (purchaseInventoryInLineDTOs.get(i).getServeTaxRate() == null){
                sb.append("订单下第："+i+"行，物料平台服务费税率为空");
            }
            if (StringUtils.isBlank(purchaseInventoryInLineDTOs.get(i).getCurrency())){
                sb.append("订单下第："+i+"行，币种不能为空");
            }
        }
        if (sb.length() != 0){
            return sb.toString();
        }else{
            return null;
        }
    }
}
