package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * @author: weijunjie
 * @date: 2020/5/18
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockRoleKeeperImportExcel implements ExportModel {
    @ExportField(name = "员工工号")
    private String empId;

    @ExportField(name = "角色名称")
    private String roleName;

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        StockRoleKeeperImportExcel that = (StockRoleKeeperImportExcel) o;
        return Objects.equals(empId, that.empId) &&
                Objects.equals(roleName, that.roleName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(empId, roleName);
    }
}
