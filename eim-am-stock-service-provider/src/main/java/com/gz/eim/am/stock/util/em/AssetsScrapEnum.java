package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2021/3/29
 * @description 资产报废单据枚举类
 */
public class AssetsScrapEnum {

    /**
     * 报废单据状态
     */
    public enum scrapStatus {

        /**
         * 单据已保存
         */
        SAVE(0, "单据已保存"),
        /**
         * 已提交审批
         */
        SUBMIT(1, "已提交审批"),
        /**
         * 审批通过
         */
        PASS(2, "审批通过"),
        /**
         * 审批拒绝
         */
        REFUSE(3, "审批拒绝"),
        /**
         * 单据作废
         */
        CANCEL(4, "单据作废")
        ;

        scrapStatus(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }
    /**
     * 报废行单状态
     */
    public enum scrapFlag {

        /**
         * 待报废
         */
        WAIT_SCRAP(0, "待报废"),
        /**
         * 已报废
         */
        YES_SCRAP(1, "已报废");

        scrapFlag(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    /**
     * 报废单据状态
     */
    public enum ExistAttach {

        /**
         * 不存在附件
         */
        NO(0, "不存在附件"),
        /**
         * 存在附件
         */
        YES(1, "存在附件")
        ;

        ExistAttach(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<Integer, String> scrapStatusMap =
            Arrays.stream(AssetsScrapEnum.scrapStatus.values()).collect(
                    Collectors.toMap(AssetsScrapEnum.scrapStatus::getValue, AssetsScrapEnum.scrapStatus::getDesc));
}
