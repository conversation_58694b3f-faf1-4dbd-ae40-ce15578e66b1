package com.gz.eim.am.stock.entity;

import lombok.Data;

/**
 * @className: StockCheckNotCheckPeopleSendToLeaderDataRespDO
 * @description: 未盘点的人发送给leader数据类
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Data
public class StockCheckNotCheckPeopleSendToLeaderDataRespDO {
    /**
     * 上级领导工号
     */
    private String supervisorId;
    /**
     * 上级领导email
     */
    private String supervisorEmail;
    /**
     * 计划名称
     */
    private String takingPlanName;
    /**
     * 计划单号
     */
    private String takingPlanNo;
    /**
     * 未盘点总人数
     */
    private Integer notCheckPeopleTotal;

}
