package com.gz.eim.am.stock.web.platform;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.platform.PlatformApi;
import com.gz.eim.am.stock.service.platform.PlatformService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * describe 平台交互
 * <AUTHOR>
 * @date 2021-05-24
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/platform")
public class PlatformController implements PlatformApi {

    private final Logger logger = LoggerFactory.getLogger(PlatformController.class);

    @Autowired
    private PlatformService platformService;

    @Override
    public ResponseData getTemporaryToken() {
        logger.info("/api/am/stock/platform/getTemporaryToken");
        return platformService.getTemporaryToken();
    }
}
