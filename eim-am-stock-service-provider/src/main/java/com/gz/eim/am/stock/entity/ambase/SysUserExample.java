package com.gz.eim.am.stock.entity.ambase;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class SysUserExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SysUserExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNull() {
            addCriterion("first_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNotNull() {
            addCriterion("first_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstNameEqualTo(String value) {
            addCriterion("first_name =", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotEqualTo(String value) {
            addCriterion("first_name <>", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThan(String value) {
            addCriterion("first_name >", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_name >=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThan(String value) {
            addCriterion("first_name <", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThanOrEqualTo(String value) {
            addCriterion("first_name <=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLike(String value) {
            addCriterion("first_name like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotLike(String value) {
            addCriterion("first_name not like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIn(List<String> values) {
            addCriterion("first_name in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotIn(List<String> values) {
            addCriterion("first_name not in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameBetween(String value1, String value2) {
            addCriterion("first_name between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotBetween(String value1, String value2) {
            addCriterion("first_name not between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNull() {
            addCriterion("last_name is null");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNotNull() {
            addCriterion("last_name is not null");
            return (Criteria) this;
        }

        public Criteria andLastNameEqualTo(String value) {
            addCriterion("last_name =", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotEqualTo(String value) {
            addCriterion("last_name <>", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThan(String value) {
            addCriterion("last_name >", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("last_name >=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThan(String value) {
            addCriterion("last_name <", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThanOrEqualTo(String value) {
            addCriterion("last_name <=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLike(String value) {
            addCriterion("last_name like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotLike(String value) {
            addCriterion("last_name not like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameIn(List<String> values) {
            addCriterion("last_name in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotIn(List<String> values) {
            addCriterion("last_name not in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameBetween(String value1, String value2) {
            addCriterion("last_name between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotBetween(String value1, String value2) {
            addCriterion("last_name not between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("sex is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("sex is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(String value) {
            addCriterion("sex =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(String value) {
            addCriterion("sex <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(String value) {
            addCriterion("sex >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(String value) {
            addCriterion("sex >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(String value) {
            addCriterion("sex <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(String value) {
            addCriterion("sex <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLike(String value) {
            addCriterion("sex like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotLike(String value) {
            addCriterion("sex not like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<String> values) {
            addCriterion("sex in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<String> values) {
            addCriterion("sex not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(String value1, String value2) {
            addCriterion("sex between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(String value1, String value2) {
            addCriterion("sex not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andPinyinIsNull() {
            addCriterion("pinyin is null");
            return (Criteria) this;
        }

        public Criteria andPinyinIsNotNull() {
            addCriterion("pinyin is not null");
            return (Criteria) this;
        }

        public Criteria andPinyinEqualTo(String value) {
            addCriterion("pinyin =", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinNotEqualTo(String value) {
            addCriterion("pinyin <>", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinGreaterThan(String value) {
            addCriterion("pinyin >", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinGreaterThanOrEqualTo(String value) {
            addCriterion("pinyin >=", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinLessThan(String value) {
            addCriterion("pinyin <", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinLessThanOrEqualTo(String value) {
            addCriterion("pinyin <=", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinLike(String value) {
            addCriterion("pinyin like", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinNotLike(String value) {
            addCriterion("pinyin not like", value, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinIn(List<String> values) {
            addCriterion("pinyin in", values, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinNotIn(List<String> values) {
            addCriterion("pinyin not in", values, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinBetween(String value1, String value2) {
            addCriterion("pinyin between", value1, value2, "pinyin");
            return (Criteria) this;
        }

        public Criteria andPinyinNotBetween(String value1, String value2) {
            addCriterion("pinyin not between", value1, value2, "pinyin");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeIsNull() {
            addCriterion("id_card_type is null");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeIsNotNull() {
            addCriterion("id_card_type is not null");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeEqualTo(Byte value) {
            addCriterion("id_card_type =", value, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeNotEqualTo(Byte value) {
            addCriterion("id_card_type <>", value, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeGreaterThan(Byte value) {
            addCriterion("id_card_type >", value, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("id_card_type >=", value, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeLessThan(Byte value) {
            addCriterion("id_card_type <", value, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeLessThanOrEqualTo(Byte value) {
            addCriterion("id_card_type <=", value, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeIn(List<Byte> values) {
            addCriterion("id_card_type in", values, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeNotIn(List<Byte> values) {
            addCriterion("id_card_type not in", values, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeBetween(Byte value1, Byte value2) {
            addCriterion("id_card_type between", value1, value2, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("id_card_type not between", value1, value2, "idCardType");
            return (Criteria) this;
        }

        public Criteria andIdCardIsNull() {
            addCriterion("id_card is null");
            return (Criteria) this;
        }

        public Criteria andIdCardIsNotNull() {
            addCriterion("id_card is not null");
            return (Criteria) this;
        }

        public Criteria andIdCardEqualTo(String value) {
            addCriterion("id_card =", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardNotEqualTo(String value) {
            addCriterion("id_card <>", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardGreaterThan(String value) {
            addCriterion("id_card >", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardGreaterThanOrEqualTo(String value) {
            addCriterion("id_card >=", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardLessThan(String value) {
            addCriterion("id_card <", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardLessThanOrEqualTo(String value) {
            addCriterion("id_card <=", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardLike(String value) {
            addCriterion("id_card like", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardNotLike(String value) {
            addCriterion("id_card not like", value, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardIn(List<String> values) {
            addCriterion("id_card in", values, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardNotIn(List<String> values) {
            addCriterion("id_card not in", values, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardBetween(String value1, String value2) {
            addCriterion("id_card between", value1, value2, "idCard");
            return (Criteria) this;
        }

        public Criteria andIdCardNotBetween(String value1, String value2) {
            addCriterion("id_card not between", value1, value2, "idCard");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andEmpTypeIsNull() {
            addCriterion("emp_type is null");
            return (Criteria) this;
        }

        public Criteria andEmpTypeIsNotNull() {
            addCriterion("emp_type is not null");
            return (Criteria) this;
        }

        public Criteria andEmpTypeEqualTo(String value) {
            addCriterion("emp_type =", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeNotEqualTo(String value) {
            addCriterion("emp_type <>", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeGreaterThan(String value) {
            addCriterion("emp_type >", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeGreaterThanOrEqualTo(String value) {
            addCriterion("emp_type >=", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeLessThan(String value) {
            addCriterion("emp_type <", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeLessThanOrEqualTo(String value) {
            addCriterion("emp_type <=", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeLike(String value) {
            addCriterion("emp_type like", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeNotLike(String value) {
            addCriterion("emp_type not like", value, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeIn(List<String> values) {
            addCriterion("emp_type in", values, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeNotIn(List<String> values) {
            addCriterion("emp_type not in", values, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeBetween(String value1, String value2) {
            addCriterion("emp_type between", value1, value2, "empType");
            return (Criteria) this;
        }

        public Criteria andEmpTypeNotBetween(String value1, String value2) {
            addCriterion("emp_type not between", value1, value2, "empType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeIsNull() {
            addCriterion("employ_type is null");
            return (Criteria) this;
        }

        public Criteria andEmployTypeIsNotNull() {
            addCriterion("employ_type is not null");
            return (Criteria) this;
        }

        public Criteria andEmployTypeEqualTo(Byte value) {
            addCriterion("employ_type =", value, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeNotEqualTo(Byte value) {
            addCriterion("employ_type <>", value, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeGreaterThan(Byte value) {
            addCriterion("employ_type >", value, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("employ_type >=", value, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeLessThan(Byte value) {
            addCriterion("employ_type <", value, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeLessThanOrEqualTo(Byte value) {
            addCriterion("employ_type <=", value, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeIn(List<Byte> values) {
            addCriterion("employ_type in", values, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeNotIn(List<Byte> values) {
            addCriterion("employ_type not in", values, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeBetween(Byte value1, Byte value2) {
            addCriterion("employ_type between", value1, value2, "employType");
            return (Criteria) this;
        }

        public Criteria andEmployTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("employ_type not between", value1, value2, "employType");
            return (Criteria) this;
        }

        public Criteria andEmpIdIsNull() {
            addCriterion("emp_id is null");
            return (Criteria) this;
        }

        public Criteria andEmpIdIsNotNull() {
            addCriterion("emp_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmpIdEqualTo(String value) {
            addCriterion("emp_id =", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdNotEqualTo(String value) {
            addCriterion("emp_id <>", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdGreaterThan(String value) {
            addCriterion("emp_id >", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdGreaterThanOrEqualTo(String value) {
            addCriterion("emp_id >=", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdLessThan(String value) {
            addCriterion("emp_id <", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdLessThanOrEqualTo(String value) {
            addCriterion("emp_id <=", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdLike(String value) {
            addCriterion("emp_id like", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdNotLike(String value) {
            addCriterion("emp_id not like", value, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdIn(List<String> values) {
            addCriterion("emp_id in", values, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdNotIn(List<String> values) {
            addCriterion("emp_id not in", values, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdBetween(String value1, String value2) {
            addCriterion("emp_id between", value1, value2, "empId");
            return (Criteria) this;
        }

        public Criteria andEmpIdNotBetween(String value1, String value2) {
            addCriterion("emp_id not between", value1, value2, "empId");
            return (Criteria) this;
        }

        public Criteria andJobIdIsNull() {
            addCriterion("job_id is null");
            return (Criteria) this;
        }

        public Criteria andJobIdIsNotNull() {
            addCriterion("job_id is not null");
            return (Criteria) this;
        }

        public Criteria andJobIdEqualTo(String value) {
            addCriterion("job_id =", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotEqualTo(String value) {
            addCriterion("job_id <>", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdGreaterThan(String value) {
            addCriterion("job_id >", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdGreaterThanOrEqualTo(String value) {
            addCriterion("job_id >=", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdLessThan(String value) {
            addCriterion("job_id <", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdLessThanOrEqualTo(String value) {
            addCriterion("job_id <=", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdLike(String value) {
            addCriterion("job_id like", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotLike(String value) {
            addCriterion("job_id not like", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdIn(List<String> values) {
            addCriterion("job_id in", values, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotIn(List<String> values) {
            addCriterion("job_id not in", values, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdBetween(String value1, String value2) {
            addCriterion("job_id between", value1, value2, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotBetween(String value1, String value2) {
            addCriterion("job_id not between", value1, value2, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobLevelIsNull() {
            addCriterion("job_level is null");
            return (Criteria) this;
        }

        public Criteria andJobLevelIsNotNull() {
            addCriterion("job_level is not null");
            return (Criteria) this;
        }

        public Criteria andJobLevelEqualTo(String value) {
            addCriterion("job_level =", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotEqualTo(String value) {
            addCriterion("job_level <>", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelGreaterThan(String value) {
            addCriterion("job_level >", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelGreaterThanOrEqualTo(String value) {
            addCriterion("job_level >=", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLessThan(String value) {
            addCriterion("job_level <", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLessThanOrEqualTo(String value) {
            addCriterion("job_level <=", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelLike(String value) {
            addCriterion("job_level like", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotLike(String value) {
            addCriterion("job_level not like", value, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelIn(List<String> values) {
            addCriterion("job_level in", values, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotIn(List<String> values) {
            addCriterion("job_level not in", values, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelBetween(String value1, String value2) {
            addCriterion("job_level between", value1, value2, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andJobLevelNotBetween(String value1, String value2) {
            addCriterion("job_level not between", value1, value2, "jobLevel");
            return (Criteria) this;
        }

        public Criteria andDeptIdIsNull() {
            addCriterion("dept_id is null");
            return (Criteria) this;
        }

        public Criteria andDeptIdIsNotNull() {
            addCriterion("dept_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeptIdEqualTo(String value) {
            addCriterion("dept_id =", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotEqualTo(String value) {
            addCriterion("dept_id <>", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdGreaterThan(String value) {
            addCriterion("dept_id >", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdGreaterThanOrEqualTo(String value) {
            addCriterion("dept_id >=", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdLessThan(String value) {
            addCriterion("dept_id <", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdLessThanOrEqualTo(String value) {
            addCriterion("dept_id <=", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdLike(String value) {
            addCriterion("dept_id like", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotLike(String value) {
            addCriterion("dept_id not like", value, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdIn(List<String> values) {
            addCriterion("dept_id in", values, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotIn(List<String> values) {
            addCriterion("dept_id not in", values, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdBetween(String value1, String value2) {
            addCriterion("dept_id between", value1, value2, "deptId");
            return (Criteria) this;
        }

        public Criteria andDeptIdNotBetween(String value1, String value2) {
            addCriterion("dept_id not between", value1, value2, "deptId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andEntryLocationIsNull() {
            addCriterion("entry_location is null");
            return (Criteria) this;
        }

        public Criteria andEntryLocationIsNotNull() {
            addCriterion("entry_location is not null");
            return (Criteria) this;
        }

        public Criteria andEntryLocationEqualTo(String value) {
            addCriterion("entry_location =", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationNotEqualTo(String value) {
            addCriterion("entry_location <>", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationGreaterThan(String value) {
            addCriterion("entry_location >", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationGreaterThanOrEqualTo(String value) {
            addCriterion("entry_location >=", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationLessThan(String value) {
            addCriterion("entry_location <", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationLessThanOrEqualTo(String value) {
            addCriterion("entry_location <=", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationLike(String value) {
            addCriterion("entry_location like", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationNotLike(String value) {
            addCriterion("entry_location not like", value, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationIn(List<String> values) {
            addCriterion("entry_location in", values, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationNotIn(List<String> values) {
            addCriterion("entry_location not in", values, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationBetween(String value1, String value2) {
            addCriterion("entry_location between", value1, value2, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andEntryLocationNotBetween(String value1, String value2) {
            addCriterion("entry_location not between", value1, value2, "entryLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationIsNull() {
            addCriterion("work_location is null");
            return (Criteria) this;
        }

        public Criteria andWorkLocationIsNotNull() {
            addCriterion("work_location is not null");
            return (Criteria) this;
        }

        public Criteria andWorkLocationEqualTo(String value) {
            addCriterion("work_location =", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationNotEqualTo(String value) {
            addCriterion("work_location <>", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationGreaterThan(String value) {
            addCriterion("work_location >", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationGreaterThanOrEqualTo(String value) {
            addCriterion("work_location >=", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationLessThan(String value) {
            addCriterion("work_location <", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationLessThanOrEqualTo(String value) {
            addCriterion("work_location <=", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationLike(String value) {
            addCriterion("work_location like", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationNotLike(String value) {
            addCriterion("work_location not like", value, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationIn(List<String> values) {
            addCriterion("work_location in", values, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationNotIn(List<String> values) {
            addCriterion("work_location not in", values, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationBetween(String value1, String value2) {
            addCriterion("work_location between", value1, value2, "workLocation");
            return (Criteria) this;
        }

        public Criteria andWorkLocationNotBetween(String value1, String value2) {
            addCriterion("work_location not between", value1, value2, "workLocation");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNull() {
            addCriterion("supervisor_id is null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNotNull() {
            addCriterion("supervisor_id is not null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdEqualTo(String value) {
            addCriterion("supervisor_id =", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotEqualTo(String value) {
            addCriterion("supervisor_id <>", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThan(String value) {
            addCriterion("supervisor_id >", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThanOrEqualTo(String value) {
            addCriterion("supervisor_id >=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThan(String value) {
            addCriterion("supervisor_id <", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThanOrEqualTo(String value) {
            addCriterion("supervisor_id <=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLike(String value) {
            addCriterion("supervisor_id like", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotLike(String value) {
            addCriterion("supervisor_id not like", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIn(List<String> values) {
            addCriterion("supervisor_id in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotIn(List<String> values) {
            addCriterion("supervisor_id not in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdBetween(String value1, String value2) {
            addCriterion("supervisor_id between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotBetween(String value1, String value2) {
            addCriterion("supervisor_id not between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andBirthdayIsNull() {
            addCriterion("birthday is null");
            return (Criteria) this;
        }

        public Criteria andBirthdayIsNotNull() {
            addCriterion("birthday is not null");
            return (Criteria) this;
        }

        public Criteria andBirthdayEqualTo(Date value) {
            addCriterionForJDBCDate("birthday =", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotEqualTo(Date value) {
            addCriterionForJDBCDate("birthday <>", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayGreaterThan(Date value) {
            addCriterionForJDBCDate("birthday >", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("birthday >=", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLessThan(Date value) {
            addCriterionForJDBCDate("birthday <", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("birthday <=", value, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayIn(List<Date> values) {
            addCriterionForJDBCDate("birthday in", values, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotIn(List<Date> values) {
            addCriterionForJDBCDate("birthday not in", values, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("birthday between", value1, value2, "birthday");
            return (Criteria) this;
        }

        public Criteria andBirthdayNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("birthday not between", value1, value2, "birthday");
            return (Criteria) this;
        }

        public Criteria andHpsCountryIsNull() {
            addCriterion("hps_country is null");
            return (Criteria) this;
        }

        public Criteria andHpsCountryIsNotNull() {
            addCriterion("hps_country is not null");
            return (Criteria) this;
        }

        public Criteria andHpsCountryEqualTo(String value) {
            addCriterion("hps_country =", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryNotEqualTo(String value) {
            addCriterion("hps_country <>", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryGreaterThan(String value) {
            addCriterion("hps_country >", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryGreaterThanOrEqualTo(String value) {
            addCriterion("hps_country >=", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryLessThan(String value) {
            addCriterion("hps_country <", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryLessThanOrEqualTo(String value) {
            addCriterion("hps_country <=", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryLike(String value) {
            addCriterion("hps_country like", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryNotLike(String value) {
            addCriterion("hps_country not like", value, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryIn(List<String> values) {
            addCriterion("hps_country in", values, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryNotIn(List<String> values) {
            addCriterion("hps_country not in", values, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryBetween(String value1, String value2) {
            addCriterion("hps_country between", value1, value2, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andHpsCountryNotBetween(String value1, String value2) {
            addCriterion("hps_country not between", value1, value2, "hpsCountry");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnIsNull() {
            addCriterion("native_place_chn is null");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnIsNotNull() {
            addCriterion("native_place_chn is not null");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnEqualTo(String value) {
            addCriterion("native_place_chn =", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnNotEqualTo(String value) {
            addCriterion("native_place_chn <>", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnGreaterThan(String value) {
            addCriterion("native_place_chn >", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnGreaterThanOrEqualTo(String value) {
            addCriterion("native_place_chn >=", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnLessThan(String value) {
            addCriterion("native_place_chn <", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnLessThanOrEqualTo(String value) {
            addCriterion("native_place_chn <=", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnLike(String value) {
            addCriterion("native_place_chn like", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnNotLike(String value) {
            addCriterion("native_place_chn not like", value, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnIn(List<String> values) {
            addCriterion("native_place_chn in", values, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnNotIn(List<String> values) {
            addCriterion("native_place_chn not in", values, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnBetween(String value1, String value2) {
            addCriterion("native_place_chn between", value1, value2, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andNativePlaceChnNotBetween(String value1, String value2) {
            addCriterion("native_place_chn not between", value1, value2, "nativePlaceChn");
            return (Criteria) this;
        }

        public Criteria andBirthplaceIsNull() {
            addCriterion("birthplace is null");
            return (Criteria) this;
        }

        public Criteria andBirthplaceIsNotNull() {
            addCriterion("birthplace is not null");
            return (Criteria) this;
        }

        public Criteria andBirthplaceEqualTo(String value) {
            addCriterion("birthplace =", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceNotEqualTo(String value) {
            addCriterion("birthplace <>", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceGreaterThan(String value) {
            addCriterion("birthplace >", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceGreaterThanOrEqualTo(String value) {
            addCriterion("birthplace >=", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceLessThan(String value) {
            addCriterion("birthplace <", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceLessThanOrEqualTo(String value) {
            addCriterion("birthplace <=", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceLike(String value) {
            addCriterion("birthplace like", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceNotLike(String value) {
            addCriterion("birthplace not like", value, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceIn(List<String> values) {
            addCriterion("birthplace in", values, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceNotIn(List<String> values) {
            addCriterion("birthplace not in", values, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceBetween(String value1, String value2) {
            addCriterion("birthplace between", value1, value2, "birthplace");
            return (Criteria) this;
        }

        public Criteria andBirthplaceNotBetween(String value1, String value2) {
            addCriterion("birthplace not between", value1, value2, "birthplace");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnIsNull() {
            addCriterion("hukou_type_chn is null");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnIsNotNull() {
            addCriterion("hukou_type_chn is not null");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnEqualTo(String value) {
            addCriterion("hukou_type_chn =", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnNotEqualTo(String value) {
            addCriterion("hukou_type_chn <>", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnGreaterThan(String value) {
            addCriterion("hukou_type_chn >", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnGreaterThanOrEqualTo(String value) {
            addCriterion("hukou_type_chn >=", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnLessThan(String value) {
            addCriterion("hukou_type_chn <", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnLessThanOrEqualTo(String value) {
            addCriterion("hukou_type_chn <=", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnLike(String value) {
            addCriterion("hukou_type_chn like", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnNotLike(String value) {
            addCriterion("hukou_type_chn not like", value, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnIn(List<String> values) {
            addCriterion("hukou_type_chn in", values, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnNotIn(List<String> values) {
            addCriterion("hukou_type_chn not in", values, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnBetween(String value1, String value2) {
            addCriterion("hukou_type_chn between", value1, value2, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andHukouTypeChnNotBetween(String value1, String value2) {
            addCriterion("hukou_type_chn not between", value1, value2, "hukouTypeChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnIsNull() {
            addCriterion("contrib_area_chn is null");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnIsNotNull() {
            addCriterion("contrib_area_chn is not null");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnEqualTo(String value) {
            addCriterion("contrib_area_chn =", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnNotEqualTo(String value) {
            addCriterion("contrib_area_chn <>", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnGreaterThan(String value) {
            addCriterion("contrib_area_chn >", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnGreaterThanOrEqualTo(String value) {
            addCriterion("contrib_area_chn >=", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnLessThan(String value) {
            addCriterion("contrib_area_chn <", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnLessThanOrEqualTo(String value) {
            addCriterion("contrib_area_chn <=", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnLike(String value) {
            addCriterion("contrib_area_chn like", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnNotLike(String value) {
            addCriterion("contrib_area_chn not like", value, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnIn(List<String> values) {
            addCriterion("contrib_area_chn in", values, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnNotIn(List<String> values) {
            addCriterion("contrib_area_chn not in", values, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnBetween(String value1, String value2) {
            addCriterion("contrib_area_chn between", value1, value2, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andContribAreaChnNotBetween(String value1, String value2) {
            addCriterion("contrib_area_chn not between", value1, value2, "contribAreaChn");
            return (Criteria) this;
        }

        public Criteria andMarStatusIsNull() {
            addCriterion("mar_status is null");
            return (Criteria) this;
        }

        public Criteria andMarStatusIsNotNull() {
            addCriterion("mar_status is not null");
            return (Criteria) this;
        }

        public Criteria andMarStatusEqualTo(String value) {
            addCriterion("mar_status =", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusNotEqualTo(String value) {
            addCriterion("mar_status <>", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusGreaterThan(String value) {
            addCriterion("mar_status >", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusGreaterThanOrEqualTo(String value) {
            addCriterion("mar_status >=", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusLessThan(String value) {
            addCriterion("mar_status <", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusLessThanOrEqualTo(String value) {
            addCriterion("mar_status <=", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusLike(String value) {
            addCriterion("mar_status like", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusNotLike(String value) {
            addCriterion("mar_status not like", value, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusIn(List<String> values) {
            addCriterion("mar_status in", values, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusNotIn(List<String> values) {
            addCriterion("mar_status not in", values, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusBetween(String value1, String value2) {
            addCriterion("mar_status between", value1, value2, "marStatus");
            return (Criteria) this;
        }

        public Criteria andMarStatusNotBetween(String value1, String value2) {
            addCriterion("mar_status not between", value1, value2, "marStatus");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdIsNull() {
            addCriterion("ethnic_grp_cd is null");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdIsNotNull() {
            addCriterion("ethnic_grp_cd is not null");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdEqualTo(String value) {
            addCriterion("ethnic_grp_cd =", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdNotEqualTo(String value) {
            addCriterion("ethnic_grp_cd <>", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdGreaterThan(String value) {
            addCriterion("ethnic_grp_cd >", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdGreaterThanOrEqualTo(String value) {
            addCriterion("ethnic_grp_cd >=", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdLessThan(String value) {
            addCriterion("ethnic_grp_cd <", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdLessThanOrEqualTo(String value) {
            addCriterion("ethnic_grp_cd <=", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdLike(String value) {
            addCriterion("ethnic_grp_cd like", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdNotLike(String value) {
            addCriterion("ethnic_grp_cd not like", value, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdIn(List<String> values) {
            addCriterion("ethnic_grp_cd in", values, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdNotIn(List<String> values) {
            addCriterion("ethnic_grp_cd not in", values, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdBetween(String value1, String value2) {
            addCriterion("ethnic_grp_cd between", value1, value2, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andEthnicGrpCdNotBetween(String value1, String value2) {
            addCriterion("ethnic_grp_cd not between", value1, value2, "ethnicGrpCd");
            return (Criteria) this;
        }

        public Criteria andStartDtChnIsNull() {
            addCriterion("start_dt_chn is null");
            return (Criteria) this;
        }

        public Criteria andStartDtChnIsNotNull() {
            addCriterion("start_dt_chn is not null");
            return (Criteria) this;
        }

        public Criteria andStartDtChnEqualTo(String value) {
            addCriterion("start_dt_chn =", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotEqualTo(String value) {
            addCriterion("start_dt_chn <>", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnGreaterThan(String value) {
            addCriterion("start_dt_chn >", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnGreaterThanOrEqualTo(String value) {
            addCriterion("start_dt_chn >=", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnLessThan(String value) {
            addCriterion("start_dt_chn <", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnLessThanOrEqualTo(String value) {
            addCriterion("start_dt_chn <=", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnLike(String value) {
            addCriterion("start_dt_chn like", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotLike(String value) {
            addCriterion("start_dt_chn not like", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnIn(List<String> values) {
            addCriterion("start_dt_chn in", values, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotIn(List<String> values) {
            addCriterion("start_dt_chn not in", values, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnBetween(String value1, String value2) {
            addCriterion("start_dt_chn between", value1, value2, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotBetween(String value1, String value2) {
            addCriterion("start_dt_chn not between", value1, value2, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andEntryDateIsNull() {
            addCriterion("entry_date is null");
            return (Criteria) this;
        }

        public Criteria andEntryDateIsNotNull() {
            addCriterion("entry_date is not null");
            return (Criteria) this;
        }

        public Criteria andEntryDateEqualTo(Date value) {
            addCriterionForJDBCDate("entry_date =", value, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("entry_date <>", value, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateGreaterThan(Date value) {
            addCriterionForJDBCDate("entry_date >", value, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("entry_date >=", value, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateLessThan(Date value) {
            addCriterionForJDBCDate("entry_date <", value, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("entry_date <=", value, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateIn(List<Date> values) {
            addCriterionForJDBCDate("entry_date in", values, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("entry_date not in", values, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("entry_date between", value1, value2, "entryDate");
            return (Criteria) this;
        }

        public Criteria andEntryDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("entry_date not between", value1, value2, "entryDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateIsNull() {
            addCriterion("dms_date is null");
            return (Criteria) this;
        }

        public Criteria andDmsDateIsNotNull() {
            addCriterion("dms_date is not null");
            return (Criteria) this;
        }

        public Criteria andDmsDateEqualTo(Date value) {
            addCriterionForJDBCDate("dms_date =", value, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("dms_date <>", value, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateGreaterThan(Date value) {
            addCriterionForJDBCDate("dms_date >", value, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dms_date >=", value, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateLessThan(Date value) {
            addCriterionForJDBCDate("dms_date <", value, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("dms_date <=", value, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateIn(List<Date> values) {
            addCriterionForJDBCDate("dms_date in", values, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("dms_date not in", values, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dms_date between", value1, value2, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDmsDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("dms_date not between", value1, value2, "dmsDate");
            return (Criteria) this;
        }

        public Criteria andDelFlagIsNull() {
            addCriterion("del_flag is null");
            return (Criteria) this;
        }

        public Criteria andDelFlagIsNotNull() {
            addCriterion("del_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDelFlagEqualTo(Byte value) {
            addCriterion("del_flag =", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotEqualTo(Byte value) {
            addCriterion("del_flag <>", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagGreaterThan(Byte value) {
            addCriterion("del_flag >", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("del_flag >=", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagLessThan(Byte value) {
            addCriterion("del_flag <", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagLessThanOrEqualTo(Byte value) {
            addCriterion("del_flag <=", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagIn(List<Byte> values) {
            addCriterion("del_flag in", values, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotIn(List<Byte> values) {
            addCriterion("del_flag not in", values, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagBetween(Byte value1, Byte value2) {
            addCriterion("del_flag between", value1, value2, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("del_flag not between", value1, value2, "delFlag");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}