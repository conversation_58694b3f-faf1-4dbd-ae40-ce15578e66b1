package com.gz.eim.am.stock.service.impl.ambase;

import com.gz.eim.am.stock.dao.ambase.FinDemCostItemMapper;
import com.gz.eim.am.stock.entity.ambase.FinDemCostItem;
import com.gz.eim.am.stock.entity.ambase.FinDemCostItemExample;
import com.gz.eim.am.stock.service.ambase.FinDemCostItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wangjing67
 * @Date: 3/17/21 2:44 下午
 * @description
 */
@Slf4j
@Service
public class FinDemCostItemServiceImpl  implements FinDemCostItemService {

    @Autowired
    private FinDemCostItemMapper finDemCostItemMapper;

    @Override
    public List<FinDemCostItem> selectByItemCodeList(List<String> itemCodeList) {
        FinDemCostItemExample example = new FinDemCostItemExample();
        FinDemCostItemExample.Criteria criteria = example.createCriteria();
        if(!CollectionUtils.isEmpty(itemCodeList)){
            criteria.andItemCodeIn(itemCodeList);
        }
        return this.finDemCostItemMapper.selectByExample(example);
    }

    @Override
    public Map<String, FinDemCostItem> selectMapByItemCodeList(List<String> itemCodeList) {
        List<FinDemCostItem> finDemCostItemList = selectByItemCodeList(itemCodeList);
        Map<String, FinDemCostItem> finDemCostItemMap = finDemCostItemList.stream().collect(Collectors.toMap(finDemCostItem -> finDemCostItem.getItemCode(),finDemCostItem -> finDemCostItem,(key1,key2)->key1));
        return finDemCostItemMap;
    }
}
