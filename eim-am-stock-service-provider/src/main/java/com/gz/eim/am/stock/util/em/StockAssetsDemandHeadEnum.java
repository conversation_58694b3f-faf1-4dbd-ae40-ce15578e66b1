package com.gz.eim.am.stock.util.em;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
   * @param:
   * @description: 资产领用头单ENUM
   * @return:
   * @author: <EMAIL>
   * @date: 2021/10/9
   */
public class StockAssetsDemandHeadEnum {

    /**
     *  单据状态
     */
    public enum Status{
        /**
         * 未完成
         */
        RUNNING(1, "未完成"),
        /**
         * 已完成
         */
        DONE(2, "已完成")
        ;

        private Integer code;
        private String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
    public final static Map<Integer, String> statusMap =
            Arrays.stream(StockAssetsDemandHeadEnum.Status.values()).collect(
                    Collectors.toMap(StockAssetsDemandHeadEnum.Status::getCode, StockAssetsDemandHeadEnum.Status::getDesc));
    /**
     *  领用原因
     */
    public enum Reason{
        /**
         * 入职领用
         */
        ENTRY_USE(2, "入职领用"),
        /**
         * 业务领用
         */
        BUSINESS_USE(3, "业务领用")
        ;
        private Integer code;
        private String desc;

        Reason(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
    public final static Map<Integer, String> reasonMap =
            Arrays.stream(StockAssetsDemandHeadEnum.Reason.values()).collect(
                    Collectors.toMap(StockAssetsDemandHeadEnum.Reason::getCode, StockAssetsDemandHeadEnum.Reason::getDesc));
}
