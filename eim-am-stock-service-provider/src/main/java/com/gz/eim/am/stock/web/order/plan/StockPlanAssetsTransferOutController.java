package com.gz.eim.am.stock.web.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.order.plan.StockPlanAssetsTransferOutApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadFastReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsTransferOutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * @author: hedahong
 * @date: 2019-12-20 PM 5:47
 * @description: 资产调拨
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/planAssetsTransferOut")
public class StockPlanAssetsTransferOutController implements StockPlanAssetsTransferOutApi {
    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockPlanAssetsTransferOutService stockPlanAssetsTransferOutService;

    @Override
    @DocTypeAnnotation(DocTypeConstant.TRANS_PLAN_OUT)
    public ResponseData savePlanAssetsTransferOut(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info ("/api/am/stock/planAssetsTransferOut/save {}", deliveryPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsTransferOutService.savePlanAssetsTransferOut (deliveryPlanHeadReqDTO, user);
        } catch (ServiceUncheckedException e) {
            log.info ("计划资产调拨单保存失败{}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("计划资产调拨单保存报错{}", e.getMessage ());
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.TRANS_PLAN_OUT)
    public ResponseData selectPlanAssetsTransferOut(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO) {
        log.info ("/api/am/stock/planAssetsTransferOut/search {}", deliveryPlanSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsTransferOutService.selectPlanAssetsTransferOut (deliveryPlanSearchReqDTO, user);
        } catch (ServiceUncheckedException e) {
            log.info ("计划调拨单分页查询失败{}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("计划调拨单分页查询报错{}", e.getMessage ());
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanAssetsTransferOutById(Long deliveryPlanHeadId) {
        log.info ("/api/am/stock/planAssetsTransferOut/search/{}", deliveryPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsTransferOutService.selectPlanAssetsTransferOutById (deliveryPlanHeadId, user);
        } catch (Exception e) {
            log.error ("计划资产调拨单详情查询", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }


    @Override
    @DocTypeAnnotation(DocTypeConstant.TRANS_PLAN_OUT)
    public ResponseData planAssetsTransferOutOutBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info ("/api/am/stock/planAssetsTransferOut/outBound {}", deliveryPlanHeadReqDTO.toString ());
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_TRANSFER_OUT_BOUND + deliveryPlanHeadReqDTO.getDeliveryPlanHeadId();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                JwtUser user = SecurityUtil.getJwtUser ();
                res = this.stockPlanAssetsTransferOutService.planAssetsTransferOutBound (deliveryPlanHeadReqDTO, user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        }catch (RuntimeException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info ("计划资产调拨单出库", e);
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error ("计划资产调拨单出库", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData planAssetsTransferOutFastOutBound(DeliveryPlanHeadFastReqDTO deliveryPlanHeadFastReqDTO) {
        log.info ("/api/am/stock/planAssetsTransferOut/fast {}", deliveryPlanHeadFastReqDTO.toString ());
        ResponseData res = null;
        JwtUser user = SecurityUtil.getJwtUser ();
        String lockKey = RedisKeyConstants.BATCH_ASSETS_TRANSFER_OUT_IMPORT_SAVE + user.getEmployeeCode ();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                res = this.stockPlanAssetsTransferOutService.planAssetsTransferOutFastOutBound (deliveryPlanHeadFastReqDTO, user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            } else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info ("计划资产调拨单快速出库失败 errorMessage = {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (RuntimeException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info("计划资产调拨单快速出库失败 errorMessage = {}",e.getMessage());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error ("计划资产调拨单快速出库报错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.TRANS_PLAN_OUT)
    public ResponseData cancelPlanAssetsTransferOutById(Long deliveryPlanHeadId) {
        log.info ("/api/am/stock/planAssetsTransferOut/cancel/{}", deliveryPlanHeadId);
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsTransferOutService.cancelPlanAssetsTransferOutById (deliveryPlanHeadId);
        } catch (Exception e) {
            log.error ("计划资产调拨单取消错误", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

}
