package com.gz.eim.am.stock.dao.ambase;

import com.gz.eim.am.stock.dto.response.address.AddressProvinceRespDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: weijunjie
 * @date: 2021/4/28
 * @description
 */
public interface SysAddressMapper {

    /**
     * 查询地址表全量的地址信息
     * @return
     */
    List<AddressProvinceRespDTO> queryAllAddress();

    /**
     * 查询员工的地址信息
     * @param empId
     * @return
     */
    Map<String,String> queryEmployAddress(@Param("empId") String empId);

    /**
     * 根据编码查询记录
     * @param locations
     * @return
     */
    List<Map<String, String>> queryAddressName(@Param("locations") List<String> locations);
}
