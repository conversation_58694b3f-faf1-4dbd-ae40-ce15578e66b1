package com.gz.eim.am.stock.util.em;

/**
 * @author: we<PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/10/10
 * @description 数据状态（0:抽取成功 1:审批中 2:审批拒绝 3:审批通过 4:财务处理中 5:财务入库失败 6:同步成功 7:同步失败,10:抽取的数据校验异常）',
 */
public class StockAssetsSyncEnum {

    /**
     * 记录表数据状态
     */
    public enum SyncStatus {

        /**
         * 抽取成功
         */
        EXTRACT_SUCCESS(0, "抽取成功"),
        /**
         * 审批中
         */
        APPROVE(1, "审批中"),
        /**
         * 审批拒绝
         */
        REFUSE(2, "审批拒绝"),
        /**
         * 审批通过
         */
        APPROVESUCCESS(3, "审批通过"),
        /**
         * 财务处理中
         */
        WAIT_IN(4, "财务处理中"),
        /**
         * 财务入库失败
         */
        IN_DEFAULT(5, "财务入库有异常数据"),
        /**
         * 同步成功
         */
        SYNC_SUCCESS(6, "同步成功"),
        /**
         * 同步失败
         */
        SYNC_DEFAULT(7, "同步失败"),
        /**
         * 数据校验异常
         */
        EXTRACT_ERROR(10, "数据校验异常");

        SyncStatus(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    /**
     * 模块标识
     */
    public enum ExtractFlag {

        /**
         * 新增资产模块
         */
        NEW(1, "新增资产模块"),
        /**
         * 报废资产模块
         */
        SCRAP(2, "新增资产模块"),
        /**
         * 变更资产模块
         */
        CHANGE(3, "新增资产模块");

        ExtractFlag(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

}
