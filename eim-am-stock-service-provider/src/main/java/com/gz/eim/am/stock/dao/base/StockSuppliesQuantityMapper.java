package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSuppliesQuantity;
import com.gz.eim.am.stock.entity.StockSuppliesQuantityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSuppliesQuantityMapper {
    long countByExample(StockSuppliesQuantityExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockSuppliesQuantity record);

    int insertSelective(StockSuppliesQuantity record);

    List<StockSuppliesQuantity> selectByExample(StockSuppliesQuantityExample example);

    StockSuppliesQuantity selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockSuppliesQuantity record, @Param("example") StockSuppliesQuantityExample example);

    int updateByExample(@Param("record") StockSuppliesQuantity record, @Param("example") StockSuppliesQuantityExample example);

    int updateByPrimaryKeySelective(StockSuppliesQuantity record);

    int updateByPrimaryKey(StockSuppliesQuantity record);


}