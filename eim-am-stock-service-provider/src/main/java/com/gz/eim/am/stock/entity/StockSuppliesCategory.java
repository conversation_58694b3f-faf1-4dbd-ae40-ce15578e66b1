package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockSuppliesCategory {
    private Long suppliesCategoryId;

    private Long parentId;

    private String code;

    private String name;

    private Integer level;

    private Integer type;

    private Integer status;

    private String allCode;

    private String allName;

    private String bussinessLine;

    private Integer vendorType;

    private Integer delFlag;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getSuppliesCategoryId() {
        return suppliesCategoryId;
    }

    public void setSuppliesCategoryId(Long suppliesCategoryId) {
        this.suppliesCategoryId = suppliesCategoryId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAllCode() {
        return allCode;
    }

    public void setAllCode(String allCode) {
        this.allCode = allCode == null ? null : allCode.trim();
    }

    public String getAllName() {
        return allName;
    }

    public void setAllName(String allName) {
        this.allName = allName == null ? null : allName.trim();
    }

    public String getBussinessLine() {
        return bussinessLine;
    }

    public void setBussinessLine(String bussinessLine) {
        this.bussinessLine = bussinessLine == null ? null : bussinessLine.trim();
    }

    public Integer getVendorType() {
        return vendorType;
    }

    public void setVendorType(Integer vendorType) {
        this.vendorType = vendorType;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}