package com.gz.eim.am.stock.service.impl.assets;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.assets.AssetsExtendDesktopPcMapper;
import com.gz.eim.am.stock.dao.assets.AssetsExtendDiskMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsExtendDiskMapper;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockAssetsExtendDisk;
import com.gz.eim.am.stock.entity.StockAssetsExtendDiskExample;
import com.gz.eim.am.stock.entity.StockInventoryAssetImport;
import com.gz.eim.am.stock.service.assets.StockAssetsExtendDiskService;
import com.gz.eim.am.stock.util.common.ListUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @author: weijunjie
 * @date: 2021/1/6
 * @description 硬盘尾表处理服务类
 */
@Service
public class StockAssetsExtendDiskServiceImpl implements StockAssetsExtendDiskService {

    @Autowired
    AssetsExtendDiskMapper assetsExtendDiskMapper;
    @Autowired
    private StockAssetsExtendDiskMapper stockAssetsExtendDiskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertAssetsTail(List<StockInventoryAssetImport> stockInventoryAssetImports) {
        if(CollectionUtils.isEmpty (stockInventoryAssetImports)){
            return false;
        }

        List<List<StockInventoryAssetImport>> stockInventoryAssetImportList = ListUtil.splitList(stockInventoryAssetImports, CommonConstant.MAX_INSERT_COUNT);
        for (List<StockInventoryAssetImport> subStockInventoryAssetImportList : stockInventoryAssetImportList){
            assetsExtendDiskMapper.batchInsertAssetsDisk(subStockInventoryAssetImportList);
        }

        return true;
    }

    @Override
    public Map<String, String> selectTailDataByCode(String assetsCode) {
        if (StringUtils.isEmpty(assetsCode)){
            return null;
        }
        return assetsExtendDiskMapper.selectTailDataByCode(assetsCode);
    }

    @Override
    public void settingAssetExtFiled(StockAssets stockAssets, StockInventoryAssetImport stockInventoryAssetImport1) {
        if (stockAssets == null || stockInventoryAssetImport1 == null) {
            return;
        }

        //型号
        if (!StringUtils.isEmpty(stockInventoryAssetImport1.getAttr2())) {
            stockAssets.setModel(stockInventoryAssetImport1.getAttr2());
        }
        //硬盘容量
        if (!StringUtils.isEmpty(stockInventoryAssetImport1.getAttr3())) {
            stockAssets.setExtRamMemory(stockInventoryAssetImport1.getAttr3());
        }
    }

    @Override
    public int batchUpdateAssetsExtendListByAssetsList(List<StockAssets> updateAssetsList) {
        return 0;
    }

    @Override
    public StockAssetsExtendDisk selectByAssetsCode(String assetsCode) {
        StockAssetsExtendDiskExample extendDiskExample = new StockAssetsExtendDiskExample();
        StockAssetsExtendDiskExample.Criteria criteria = extendDiskExample.createCriteria();
        if (!StringUtils.isEmpty(assetsCode)) {
            criteria.andAssetsCodeEqualTo(assetsCode);
        }
        List<StockAssetsExtendDisk> stockAssetsExtendDiskList = stockAssetsExtendDiskMapper.selectByExample(extendDiskExample);
        return !CollectionUtils.isEmpty(stockAssetsExtendDiskList) ? stockAssetsExtendDiskList.get(0) : null;
    }
}
