package com.gz.eim.am.stock.dao.order;

import com.gz.eim.am.stock.dto.response.order.InventoryOutRespDTO;
import com.gz.eim.am.stock.entity.StockDeliveryHistory;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-11 下午 6:04
 */
public interface DeliveryHistoryMapper {

    /**
     * 批量插入
     * @param histories
     * @return
     */
    int insertList(List<StockDeliveryHistory> histories);
    /**
     * 根据出库单id获取出库单
     * @param deliveryId
     * @return
     */
    InventoryOutRespDTO selectInventoryOutRespDTOByDeliveryId(Long deliveryId);
}
