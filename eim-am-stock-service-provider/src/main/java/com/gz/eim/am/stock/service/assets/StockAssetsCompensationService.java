package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsCompensationRecordRespDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
   * @description: 资产赔偿Service
   * @author: <EMAIL>
   * @date: 2022/2/15
   */
public interface StockAssetsCompensationService {

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 查询资产赔偿记录表
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/3/30
     */
    ResponseData queryAssetsCompensationRecordPage(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) throws Exception;

    /**
     * 导入资产赔偿临时表
     * @param file
     * @return
     * @throws IOException
     * @throws ParseException
     */
    ResponseData importAssetsCompensationRecord(MultipartFile file) throws IOException, ParseException;
    /**
     * @param: file
     * @description: 导入在职资产赔偿记录
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/4/1
     */
    ResponseData confirmImportAssetsCompensationRecord(String batchCode) throws IOException;

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 导出资产赔偿记录表
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/3/30
     */
    ResponseData exportAssetsCompensationRecord(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * @param: stockAssetsCompensationRecordReqDTOList
     * @description: update资产赔偿记录
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/3/31
     */
    ResponseData updateAssetsCompensationRecord(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) throws Exception;
    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 离职查询人员名下资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
     ResponseData<StockAssetsCompensationRecordRespDTO> queryCompensationAssets(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) throws Exception;

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 提交资产赔款信息
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    ResponseData submitAssetsCompensationRecord(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) throws Exception;

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 审批流回查资产赔偿情况
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    ResponseData queryWflAssetsCompensationRecordList(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO);

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 处理hr下放的离职人员
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    ResponseData dealHrDevolveLeaveUser(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) throws Exception;

    /**
     * @param: id
     * @description: 逻辑删除资产赔偿
     * @return: ResponseData
     */
    ResponseData deleteAssetsCompensationRecord(Long id);

    /**
     * @param: leaveUser
     * @description: 审批流查询离职人对应的审批人
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/4/11
     */
    ResponseData queryLeaveApproveUserByWfl(String leaveUser);

    /**
     * 手动推送hr扣款数据记录
     * @param stockAssetsCompensationRecordReqDTO
     * @return
     */
    ResponseData manualPushHrCompensationData(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO);

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 离职查询人员名下资产
     * @return: ResponseData<StockAssetsCompensationRecordRespDTO>
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    ResponseData<List<StockAssetsCompensationRecordRespDTO>> queryStockAssetsCompensationRecordRespDTOList(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) throws Exception;
}
