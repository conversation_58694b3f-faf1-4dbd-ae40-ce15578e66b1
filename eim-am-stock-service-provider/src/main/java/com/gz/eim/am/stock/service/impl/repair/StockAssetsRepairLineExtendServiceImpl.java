package com.gz.eim.am.stock.service.impl.repair;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockAssetsRepairLineExtendMapper;
import com.gz.eim.am.stock.dao.repair.AssetsRepairLineExtendMapper;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairLineBaseReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsRepairLine;
import com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend;
import com.gz.eim.am.stock.entity.StockAssetsRepairLineExtendExample;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairLineExtendService;
import com.gz.eim.am.stock.util.common.ListUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: StockAssetsRepairLineExtendServiceImpl
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/12/15
 **/
@Service
public class StockAssetsRepairLineExtendServiceImpl implements StockAssetsRepairLineExtendService {

    @Autowired
    private StockAssetsRepairLineExtendMapper stockAssetsRepairLineExtendMapper;
    @Autowired
    private AssetsRepairLineExtendMapper assetsRepairLineExtendMapper;

    @Override
    public List<StockAssetsRepairLineExtend> selectList(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO) {
        if(null == stockAssetsRepairLineBaseReqDTO){
            return new ArrayList<>();
        }
        StockAssetsRepairLineExtendExample stockAssetsRepairLineExtendExample = new StockAssetsRepairLineExtendExample();
        StockAssetsRepairLineExtendExample.Criteria criteria = stockAssetsRepairLineExtendExample.createCriteria();
        if(CollectionUtils.isNotEmpty(stockAssetsRepairLineBaseReqDTO.getRepairItemNoList())){
            criteria.andRepairItemNoIn(stockAssetsRepairLineBaseReqDTO.getRepairItemNoList());
        }
        return stockAssetsRepairLineExtendMapper.selectByExample(stockAssetsRepairLineExtendExample);
    }

    @Override
    public StockAssetsRepairLineExtend selectOne(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO) {
        List<StockAssetsRepairLineExtend> stockAssetsRepairLineExtendList = selectList(stockAssetsRepairLineBaseReqDTO);
        if(CollectionUtils.isNotEmpty(stockAssetsRepairLineExtendList)){
            return null;
        }
        return stockAssetsRepairLineExtendList.get(CommonConstant.NUMBER_ZERO);
    }

    @Override
    public Map<String, StockAssetsRepairLineExtend> selectMapByItemNo(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO) {
        List<StockAssetsRepairLineExtend> stockAssetsRepairLineExtendList = selectList(stockAssetsRepairLineBaseReqDTO);
        if(CollectionUtils.isEmpty(stockAssetsRepairLineExtendList)){
            return new HashMap<>();
        }
        return stockAssetsRepairLineExtendList.stream().collect(Collectors.toMap(StockAssetsRepairLineExtend :: getRepairItemNo, stockAssetsRepairLineExtend -> stockAssetsRepairLineExtend, (k1, k2) -> k2));
    }

    @Override
    public int batchInsert(List<StockAssetsRepairLineExtend> stockAssetsRepairLineExtendList) {
        if(org.springframework.util.CollectionUtils.isEmpty(stockAssetsRepairLineExtendList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsRepairLineExtendList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsRepairLineExtend>> StockAssetsRepairLineExtendListList = ListUtil.splitList(stockAssetsRepairLineExtendList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsRepairLineExtend> stockAssetsRepairLineExtends : StockAssetsRepairLineExtendListList) {
                count += assetsRepairLineExtendMapper.batchInsert(stockAssetsRepairLineExtends);
            }
            return count;
        }else {
            return assetsRepairLineExtendMapper.batchInsert(stockAssetsRepairLineExtendList);
        }
    }

    @Override
    public int batchUpdate(List<StockAssetsRepairLineExtend> stockAssetsRepairLineExtendList) {
        if(org.springframework.util.CollectionUtils.isEmpty(stockAssetsRepairLineExtendList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsRepairLineExtendList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsRepairLineExtend>> StockAssetsRepairLineExtendListList = ListUtil.splitList(stockAssetsRepairLineExtendList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsRepairLineExtend> stockAssetsRepairLineExtends : StockAssetsRepairLineExtendListList) {
                count += assetsRepairLineExtendMapper.batchUpdate(stockAssetsRepairLineExtends);
            }
            return count;
        }else {
            return assetsRepairLineExtendMapper.batchUpdate(stockAssetsRepairLineExtendList);
        }
    }
}
