package com.gz.eim.am.stock.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @className: StockDeliveryPlanHeadDo
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2023/3/17
 **/
@Data
public class StockDeliveryPlanHeadDO {

    private Integer deliveryMethod;

    private Integer status;

    private Date lessThanSendDate;

    private String deliveryPlanNo;

    private List<String> deliveryPlanNoList;
}
