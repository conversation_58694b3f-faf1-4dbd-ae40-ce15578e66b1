package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wangjing67
 * @Date: 1/28/21 4:14 下午
 * @description 折现申请单 枚举
 */
public class DiscountEnum {

    /**
     * 单据状态
     */
    public enum Status {
        /**
         * 审批中
         */
        IN_APPROVAL(0, "审批中"),
        /**
         * 正常
         */
        PASS(1, "审批通过"),
        /**
         * 不可修改
         */
        FAIL(2, "审批拒绝"),
        /**
         * 已完成
         */
        FINISH(3, "完成");


        Status(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        private Integer status;
        private String desc;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    public static Map<Integer, String> statusMap =
            Arrays.stream(DiscountEnum.Status.values()).collect(
                    Collectors.toMap(DiscountEnum.Status::getStatus, DiscountEnum.Status::getDesc));


    /**
     * 单据状态
     */
    public enum DiscountType {
        /**
         * 物料转换单
         */
        SUPPLIES_CONVERSION_SHEET(0, "物料转换单");

        DiscountType(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        private Integer status;
        private String desc;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    public static Map<Integer, String> DiscountTypeMap =
            Arrays.stream(DiscountEnum.DiscountType.values()).collect(
                    Collectors.toMap(DiscountEnum.DiscountType::getStatus, DiscountEnum.DiscountType::getDesc));

}
