package com.gz.eim.am.stock.dao.manage;


import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO;
import com.gz.eim.am.stock.entity.StockSuppliesConfigResult;
import com.gz.eim.am.stock.entity.StockSuppliesQuantity;

import java.util.List;

public interface SuppliesQuantityMapper {


   /**
    * 增加库存
    * @param stockSuppliesQuantity
    * @return
    */
   int addQuantity(StockSuppliesQuantity stockSuppliesQuantity);

   /**
    * 扣减库存
    * @param stockSuppliesQuantity
    * @return
    */
   int subtractQuantity(StockSuppliesQuantity stockSuppliesQuantity);

   /**
    * 增加预留库存
    * @param stockSuppliesQuantity
    * @return
    */
   int addReservedQuantity(StockSuppliesQuantity stockSuppliesQuantity);

   /**
    * 扣减预留和实际库存
    * @param stockSuppliesQuantity
    * @return
    */
   int subtractReservedQuantity(StockSuppliesQuantity stockSuppliesQuantity);

   /**
    * 查询具体的可用库存
    * @param dto
    * @return
    */
   StockSuppliesConfigResult getSingleSuppliesWarehouse(StockSuppliesQuantityReqDTO dto);


   /**
    * 库存数据查询(不过滤库存量为0的数据)
    * @param reqDTO
    * @return
    */
   List<SuppliesConfigRespDTO> getStockSuppliesQuantity(StockSuppliesQuantityReqDTO reqDTO);

   /**
    * 库存数据计数（不过滤库存为0的物料）
    * @param reqDTO
    * @return
    */
   Long getStockSuppliesQuantityCount(StockSuppliesQuantityReqDTO reqDTO);

   int batchInsert(List<StockSuppliesQuantity> stockSuppliesQuantityList);

   int batchUpdate(List<StockSuppliesQuantity> stockSuppliesQuantityList);

   int batchDelete(List<StockSuppliesQuantity> stockSuppliesQuantityList);

//   /**
//    * 页面库存查询
//    * @param reqDTO
//    * @return
//    */
//   List<SuppliesConfigRespDTO> getWarehouseSuppliesQuantity(StockSuppliesQuantityReqDTO reqDTO);
//
//
//   /**
//    * 页面库存条数查询
//    * @param reqDTO
//    * @return
//    */
//   Long getStockSuppliesQuantityCount(StockSuppliesQuantityReqDTO reqDTO);

}