package com.gz.eim.am.stock.service.assets;

import com.gz.eim.am.stock.entity.StockAssetsDocumentDetail;

import java.util.List;

/**
 * @className: StockAssetsDocumentDetailService
 * @description: 资产转移单行单service
 * @author: <EMAIL>
 * @date: 2023/2/24
 **/
public interface StockAssetsDocumentDetailService {

    /**
     * @param: stockAssetsDocumentDetailList
     * @description: 批量插入行信息
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/2/24
     */
    int batchInsert(List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList);
}
