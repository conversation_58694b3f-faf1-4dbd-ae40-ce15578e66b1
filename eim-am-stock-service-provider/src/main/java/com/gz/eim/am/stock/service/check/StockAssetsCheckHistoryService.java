package com.gz.eim.am.stock.service.check;

/**
 * @author: we<PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/11/25
 * @description 盘点历史表处理
 */
public interface StockAssetsCheckHistoryService {

    /**
     * 将盘点的计划单和任务转移到历史表
     * @param takingPlanNo
     */
    void CopyToHistotyTable(String takingPlanNo);

    /**
     * 将盘点数据从业务表删除
     * @param takingPlanNo
     */
    void deleteFromBusinessTable(String takingPlanNo);
}
