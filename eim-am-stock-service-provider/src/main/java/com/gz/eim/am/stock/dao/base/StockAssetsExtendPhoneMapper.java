package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsExtendPhone;
import com.gz.eim.am.stock.entity.StockAssetsExtendPhoneExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsExtendPhoneMapper {
    long countByExample(StockAssetsExtendPhoneExample example);

    int deleteByPrimaryKey(Long assetsPhoneId);

    int insert(StockAssetsExtendPhone record);

    int insertSelective(StockAssetsExtendPhone record);

    List<StockAssetsExtendPhone> selectByExample(StockAssetsExtendPhoneExample example);

    StockAssetsExtendPhone selectByPrimaryKey(Long assetsPhoneId);

    int updateByExampleSelective(@Param("record") StockAssetsExtendPhone record, @Param("example") StockAssetsExtendPhoneExample example);

    int updateByExample(@Param("record") StockAssetsExtendPhone record, @Param("example") StockAssetsExtendPhoneExample example);

    int updateByPrimaryKeySelective(StockAssetsExtendPhone record);

    int updateByPrimaryKey(StockAssetsExtendPhone record);
}