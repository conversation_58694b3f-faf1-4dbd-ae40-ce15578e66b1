package com.gz.eim.am.stock.service.assets;


import com.fuu.eim.support.base.ResponseData;

import java.text.ParseException;

/**
   * @description: 定时任务抓取没有审批的数据
   * @author: <EMAIL>
   * @date: 2022/3/29
   */
public interface StockAssetsLeaveNoReturnService {

     ResponseData getLeaveNoReturnAssets(String leaveDateStr) throws ParseException;

    /**
     * 检索职日期为上个月1号到xx号范围内的员工名下未归还资产且赔偿金额为0的数据，并发送邮件提醒
     * @return
     */
     ResponseData queryLastMonthNoReturnAssetsAndSendEmail() throws Exception;
}
