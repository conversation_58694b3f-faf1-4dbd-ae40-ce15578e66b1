package com.gz.eim.am.stock.web.inventory.plan;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.inventory.plan.StockPlanLicenseInApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import com.gz.eim.am.stock.service.inventory.plan.StockPlanLicenseInService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.spring.web.json.Json;

import java.util.concurrent.TimeUnit;

/**
 * @Author: wangjing67
 * @Date: 3/25/21 2:26 下午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/planLicenseIn")
public class StockPlanLicenseInController implements StockPlanLicenseInApi{

    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockPlanLicenseInService stockPlanLicenseInService;

    @Override
    @DocTypeAnnotation(DocTypeConstant.LICENSE_PLAN_IN)
    public ResponseData save(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info ("/api/am/stock/planLicenseIn/save {}", JSON.toJSONString(inventoryInPlanHeadReqDTO));
        JwtUser user = SecurityUtil.getJwtUser ();
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_REMAND_BOUND + inventoryInPlanHeadReqDTO.getDutyUser();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
                res = this.stockPlanLicenseInService.save (inventoryInPlanHeadReqDTO, user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            } else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info ("执照新增入库操作失败返回消息：{}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error ("执照新增入库", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.LICENSE_PLAN_IN)
    public ResponseData selectByQuery(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO) {
        log.info ("/api/am/stock/planLicenseIn/list {} ", JSON.toJSONString(inventoryInPlanSearchReqDTO));
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanLicenseInService.selectByQuery (inventoryInPlanSearchReqDTO,user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("执照入库查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData detail(Long inventoryInPlanHeadId) {
        log.info ("/api/am/stock/planLicenseIn/detail {} ", JSON.toJSONString(inventoryInPlanHeadId));
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanLicenseInService.queryDetail (inventoryInPlanHeadId,user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("执照入库详情查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData selectLicenseLineAssetByLineId(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO) {
        log.info ("/api/am/stock/planLicenseIn/line/search {} ", JSON.toJSONString(inventoryInPlanLineReqDTO));
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanLicenseInService.selectLicenseLineAssetByLineId (inventoryInPlanLineReqDTO,user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("查询执照行下资产明细出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }
}
