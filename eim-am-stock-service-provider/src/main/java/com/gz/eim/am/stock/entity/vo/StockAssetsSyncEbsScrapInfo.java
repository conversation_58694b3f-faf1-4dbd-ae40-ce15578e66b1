package com.gz.eim.am.stock.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

/**
 * @author: weijunjie
 * @date: 2021/4/7
 * @description
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StockAssetsSyncEbsScrapInfo {

    @JsonProperty("tag_number")
    private String assetsCode;
    @JsonProperty("business_type")
    private Integer businessType;
    @JsonProperty("business_doc_num")
    private String businessNo;
    @JsonProperty("business_doc_row_num")
    private String businessLineNo;
    @JsonProperty("doc_created_by")
    private String billingUser;
    @JsonProperty("doc_creation_date")
    private String billingTime;
    @JsonProperty("retire_date")
    private String scrapTime;
    @JsonProperty("description")
    private String assetsName;
    @JsonProperty("book_type_code")
    private String bookTypeCode;
    @JsonProperty("qty_retired")
    private Integer scrapNum;
    @JsonProperty("version_id")
    private Integer versionId;
    @JsonProperty("net_book_value")
    private BigDecimal lastCost;
    @JsonProperty("retire_reason")
    private String businessReason;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getBusinessLineNo() {
        return businessLineNo;
    }

    public void setBusinessLineNo(String businessLineNo) {
        this.businessLineNo = businessLineNo;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public Integer getScrapNum() {
        return scrapNum;
    }

    public void setScrapNum(Integer scrapNum) {
        this.scrapNum = scrapNum;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public BigDecimal getLastCost() {
        return lastCost;
    }

    public void setLastCost(BigDecimal lastCost) {
        this.lastCost = lastCost;
    }

    public String getBusinessReason() {
        return businessReason;
    }

    public void setBusinessReason(String businessReason) {
        this.businessReason = businessReason;
    }

    public String getScrapTime() {
        return scrapTime;
    }

    public void setScrapTime(String scrapTime) {
        this.scrapTime = scrapTime;
    }

    public String getBookTypeCode() {
        return bookTypeCode;
    }

    public void setBookTypeCode(String bookTypeCode) {
        this.bookTypeCode = bookTypeCode;
    }
}
