package com.gz.eim.am.stock.dao.manage;

import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.dto.response.manage.StockSuppliesConfigDetailRespDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO;
import com.gz.eim.am.stock.entity.StockSuppliesConfigDetail;
import com.gz.eim.am.stock.entity.vo.download.ExportStockConfigDetailEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-11 上午 10:58
 */
public interface SuppliesConfigDetailMapper {


    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertList(List<StockSuppliesConfigDetail> list);

    /**
     * 增加库存明细数量
     * @param detail
     * @return
     */
    int updateConfigDetailNumber(StockSuppliesConfigDetail detail);

    /**
     * 查询物料明细可用库存
     * @param dto
     * @return
     */
    StockSuppliesConfigDetail selectUseConfigDetail(StockSuppliesQuantityReqDTO dto);

    /**
     * 仓库-物料 下的可用批次
     * @param dto
     * @return
     */
    List<StockSuppliesConfigDetail> selectUseManageDetail(StockSuppliesQuantityReqDTO dto);

    /**
     * 库存明细查询
     * @param reqDTO
     * @return
     */
    List<SuppliesConfigRespDTO> getConfigDetail(StockSuppliesQuantityReqDTO reqDTO);

    /**
     * 库存明细批量查询
     * @param dto
     * @return
     */
    List<StockSuppliesConfigDetail> selectUseConfigDetailByBatch(StockSuppliesQuantityReqDTO dto);

    /**
     * 单个库存明细的数量查询
     * @param dto
     * @return
     */
    Long countSuppliesConfigDetail(StockSuppliesQuantityReqDTO dto);

    /**
     * 单个库存明细分页查询
     * @param dto
     * @return
     */
    List<StockSuppliesConfigDetailRespDTO> getSuppliesConfigDetailByPage(StockSuppliesQuantityReqDTO dto);

    /**
     * 单个库存明细导出查询
     * @param dto
     * @return
     */
    List<ExportStockConfigDetailEntity> exportSuppliesConfigDetail(StockSuppliesQuantityReqDTO dto);

    /**
     * 库存详细
     * @param stockSuppliesQuantityReqDTO
     * @return
     */
    List<StockSuppliesConfigDetailRespDTO> getConfigDetailArrange(StockSuppliesQuantityReqDTO stockSuppliesQuantityReqDTO);

    /**
     * 根据序列号查询物料
     * @param reqDTO
     * @return
     */
    List<SuppliesConfigRespDTO> getSuppliesBySnnoAndWareCode(StockSuppliesQuantityReqDTO reqDTO);

    /**
     * 查询已经存在的库存序列号
     * @param dto
     * @return
     */
    List<String> selectExistsSnsByBatch(StockSuppliesQuantityReqDTO dto);
}
