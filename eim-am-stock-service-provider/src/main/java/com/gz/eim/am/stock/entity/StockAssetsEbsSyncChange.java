package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockAssetsEbsSyncChange {
    private Long id;

    private String assetsCode;

    private String batchNo;

    private String queryCode;

    private Integer syncStatus;

    private String errorMessage;

    private Date extractStartDate;

    private Date extractEndDate;

    private Integer businessType;

    private String businessNo;

    private String businessLineNo;

    private String billingUser;

    private String billingTime;

    private String assetsName;

    private String lastBookTypeCode;

    private String lastCompanyCode;

    private String lastDeptCode;

    private String lastAddress;

    private String newBookTypeCode;

    private String newCompanyCode;

    private String newDeptCode;

    private String newAddress;

    private String lastPrice;

    private String newPrice;

    private String billCode;

    private Integer versionId;

    private String changeReason;

    private String faCode;

    private Integer assetId;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getQueryCode() {
        return queryCode;
    }

    public void setQueryCode(String queryCode) {
        this.queryCode = queryCode == null ? null : queryCode.trim();
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage == null ? null : errorMessage.trim();
    }

    public Date getExtractStartDate() {
        return extractStartDate;
    }

    public void setExtractStartDate(Date extractStartDate) {
        this.extractStartDate = extractStartDate;
    }

    public Date getExtractEndDate() {
        return extractEndDate;
    }

    public void setExtractEndDate(Date extractEndDate) {
        this.extractEndDate = extractEndDate;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo == null ? null : businessNo.trim();
    }

    public String getBusinessLineNo() {
        return businessLineNo;
    }

    public void setBusinessLineNo(String businessLineNo) {
        this.businessLineNo = businessLineNo == null ? null : businessLineNo.trim();
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser == null ? null : billingUser.trim();
    }

    public String getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(String billingTime) {
        this.billingTime = billingTime == null ? null : billingTime.trim();
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName == null ? null : assetsName.trim();
    }

    public String getLastBookTypeCode() {
        return lastBookTypeCode;
    }

    public void setLastBookTypeCode(String lastBookTypeCode) {
        this.lastBookTypeCode = lastBookTypeCode == null ? null : lastBookTypeCode.trim();
    }

    public String getLastCompanyCode() {
        return lastCompanyCode;
    }

    public void setLastCompanyCode(String lastCompanyCode) {
        this.lastCompanyCode = lastCompanyCode == null ? null : lastCompanyCode.trim();
    }

    public String getLastDeptCode() {
        return lastDeptCode;
    }

    public void setLastDeptCode(String lastDeptCode) {
        this.lastDeptCode = lastDeptCode == null ? null : lastDeptCode.trim();
    }

    public String getLastAddress() {
        return lastAddress;
    }

    public void setLastAddress(String lastAddress) {
        this.lastAddress = lastAddress == null ? null : lastAddress.trim();
    }

    public String getNewBookTypeCode() {
        return newBookTypeCode;
    }

    public void setNewBookTypeCode(String newBookTypeCode) {
        this.newBookTypeCode = newBookTypeCode == null ? null : newBookTypeCode.trim();
    }

    public String getNewCompanyCode() {
        return newCompanyCode;
    }

    public void setNewCompanyCode(String newCompanyCode) {
        this.newCompanyCode = newCompanyCode == null ? null : newCompanyCode.trim();
    }

    public String getNewDeptCode() {
        return newDeptCode;
    }

    public void setNewDeptCode(String newDeptCode) {
        this.newDeptCode = newDeptCode == null ? null : newDeptCode.trim();
    }

    public String getNewAddress() {
        return newAddress;
    }

    public void setNewAddress(String newAddress) {
        this.newAddress = newAddress == null ? null : newAddress.trim();
    }

    public String getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(String lastPrice) {
        this.lastPrice = lastPrice == null ? null : lastPrice.trim();
    }

    public String getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(String newPrice) {
        this.newPrice = newPrice == null ? null : newPrice.trim();
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode == null ? null : billCode.trim();
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason == null ? null : changeReason.trim();
    }

    public String getFaCode() {
        return faCode;
    }

    public void setFaCode(String faCode) {
        this.faCode = faCode == null ? null : faCode.trim();
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}