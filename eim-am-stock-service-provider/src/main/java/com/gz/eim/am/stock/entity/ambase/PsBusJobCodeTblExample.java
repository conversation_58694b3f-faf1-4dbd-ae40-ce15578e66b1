package com.gz.eim.am.stock.entity.ambase;

import java.util.ArrayList;
import java.util.List;

public class PsBusJobCodeTblExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public PsBusJobCodeTblExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andJobcodeIsNull() {
            addCriterion("JOBCODE is null");
            return (Criteria) this;
        }

        public Criteria andJobcodeIsNotNull() {
            addCriterion("JOBCODE is not null");
            return (Criteria) this;
        }

        public Criteria andJobcodeEqualTo(String value) {
            addCriterion("JOBCODE =", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotEqualTo(String value) {
            addCriterion("JOBCODE <>", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeGreaterThan(String value) {
            addCriterion("JOBCODE >", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeGreaterThanOrEqualTo(String value) {
            addCriterion("JOBCODE >=", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeLessThan(String value) {
            addCriterion("JOBCODE <", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeLessThanOrEqualTo(String value) {
            addCriterion("JOBCODE <=", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeLike(String value) {
            addCriterion("JOBCODE like", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotLike(String value) {
            addCriterion("JOBCODE not like", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeIn(List<String> values) {
            addCriterion("JOBCODE in", values, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotIn(List<String> values) {
            addCriterion("JOBCODE not in", values, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeBetween(String value1, String value2) {
            addCriterion("JOBCODE between", value1, value2, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotBetween(String value1, String value2) {
            addCriterion("JOBCODE not between", value1, value2, "jobcode");
            return (Criteria) this;
        }

        public Criteria andEffStatusIsNull() {
            addCriterion("EFF_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andEffStatusIsNotNull() {
            addCriterion("EFF_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andEffStatusEqualTo(String value) {
            addCriterion("EFF_STATUS =", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusNotEqualTo(String value) {
            addCriterion("EFF_STATUS <>", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusGreaterThan(String value) {
            addCriterion("EFF_STATUS >", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusGreaterThanOrEqualTo(String value) {
            addCriterion("EFF_STATUS >=", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusLessThan(String value) {
            addCriterion("EFF_STATUS <", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusLessThanOrEqualTo(String value) {
            addCriterion("EFF_STATUS <=", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusLike(String value) {
            addCriterion("EFF_STATUS like", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusNotLike(String value) {
            addCriterion("EFF_STATUS not like", value, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusIn(List<String> values) {
            addCriterion("EFF_STATUS in", values, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusNotIn(List<String> values) {
            addCriterion("EFF_STATUS not in", values, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusBetween(String value1, String value2) {
            addCriterion("EFF_STATUS between", value1, value2, "effStatus");
            return (Criteria) this;
        }

        public Criteria andEffStatusNotBetween(String value1, String value2) {
            addCriterion("EFF_STATUS not between", value1, value2, "effStatus");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrIsNull() {
            addCriterion("HPS_JOBCD_DESCR is null");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrIsNotNull() {
            addCriterion("HPS_JOBCD_DESCR is not null");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrEqualTo(String value) {
            addCriterion("HPS_JOBCD_DESCR =", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrNotEqualTo(String value) {
            addCriterion("HPS_JOBCD_DESCR <>", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrGreaterThan(String value) {
            addCriterion("HPS_JOBCD_DESCR >", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrGreaterThanOrEqualTo(String value) {
            addCriterion("HPS_JOBCD_DESCR >=", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrLessThan(String value) {
            addCriterion("HPS_JOBCD_DESCR <", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrLessThanOrEqualTo(String value) {
            addCriterion("HPS_JOBCD_DESCR <=", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrLike(String value) {
            addCriterion("HPS_JOBCD_DESCR like", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrNotLike(String value) {
            addCriterion("HPS_JOBCD_DESCR not like", value, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrIn(List<String> values) {
            addCriterion("HPS_JOBCD_DESCR in", values, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrNotIn(List<String> values) {
            addCriterion("HPS_JOBCD_DESCR not in", values, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrBetween(String value1, String value2) {
            addCriterion("HPS_JOBCD_DESCR between", value1, value2, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andHpsJobcdDescrNotBetween(String value1, String value2) {
            addCriterion("HPS_JOBCD_DESCR not between", value1, value2, "hpsJobcdDescr");
            return (Criteria) this;
        }

        public Criteria andDescrshortIsNull() {
            addCriterion("DESCRSHORT is null");
            return (Criteria) this;
        }

        public Criteria andDescrshortIsNotNull() {
            addCriterion("DESCRSHORT is not null");
            return (Criteria) this;
        }

        public Criteria andDescrshortEqualTo(String value) {
            addCriterion("DESCRSHORT =", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortNotEqualTo(String value) {
            addCriterion("DESCRSHORT <>", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortGreaterThan(String value) {
            addCriterion("DESCRSHORT >", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortGreaterThanOrEqualTo(String value) {
            addCriterion("DESCRSHORT >=", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortLessThan(String value) {
            addCriterion("DESCRSHORT <", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortLessThanOrEqualTo(String value) {
            addCriterion("DESCRSHORT <=", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortLike(String value) {
            addCriterion("DESCRSHORT like", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortNotLike(String value) {
            addCriterion("DESCRSHORT not like", value, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortIn(List<String> values) {
            addCriterion("DESCRSHORT in", values, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortNotIn(List<String> values) {
            addCriterion("DESCRSHORT not in", values, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortBetween(String value1, String value2) {
            addCriterion("DESCRSHORT between", value1, value2, "descrshort");
            return (Criteria) this;
        }

        public Criteria andDescrshortNotBetween(String value1, String value2) {
            addCriterion("DESCRSHORT not between", value1, value2, "descrshort");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}