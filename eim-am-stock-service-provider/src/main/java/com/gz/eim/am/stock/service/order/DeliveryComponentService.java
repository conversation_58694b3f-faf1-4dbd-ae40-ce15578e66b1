package com.gz.eim.am.stock.service.order;

import com.fuu.eim.support.base.ResponseData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-12 上午 11:48
 */
public interface DeliveryComponentService {

    /**
     * 归档发货单
     * @param limit
     * @param version
     * @return
     */
    ResponseData<List<Long>> fileDelivery(Integer limit, String version);

    /**
     * 批量修改发货单状态
     * @param deliveryIds
     * @param status
     * @return
     */
    int batchUpdateStatus(List<Long> deliveryIds,Integer status);
}
