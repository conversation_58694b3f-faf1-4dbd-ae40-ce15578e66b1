package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockSuppliesPurchaseExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockSuppliesPurchaseExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CREATED_BY like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CREATED_BY not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("CREATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("CREATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("CREATED_AT =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("CREATED_AT <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("CREATED_AT >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("CREATED_AT <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("CREATED_AT in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("CREATED_AT not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("UPDATED_BY =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("UPDATED_BY <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("UPDATED_BY >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("UPDATED_BY <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("UPDATED_BY like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("UPDATED_BY not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("UPDATED_BY in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("UPDATED_BY not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("UPDATED_BY between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("UPDATED_BY not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("UPDATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("UPDATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("UPDATED_AT =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("UPDATED_AT <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("UPDATED_AT >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("UPDATED_AT <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("UPDATED_AT in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("UPDATED_AT not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagIsNull() {
            addCriterion("IS_DEL_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagIsNotNull() {
            addCriterion("IS_DEL_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagEqualTo(Integer value) {
            addCriterion("IS_DEL_FLAG =", value, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagNotEqualTo(Integer value) {
            addCriterion("IS_DEL_FLAG <>", value, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagGreaterThan(Integer value) {
            addCriterion("IS_DEL_FLAG >", value, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DEL_FLAG >=", value, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagLessThan(Integer value) {
            addCriterion("IS_DEL_FLAG <", value, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DEL_FLAG <=", value, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagIn(List<Integer> values) {
            addCriterion("IS_DEL_FLAG in", values, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagNotIn(List<Integer> values) {
            addCriterion("IS_DEL_FLAG not in", values, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagBetween(Integer value1, Integer value2) {
            addCriterion("IS_DEL_FLAG between", value1, value2, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andIsDelFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DEL_FLAG not between", value1, value2, "isDelFlag");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeIsNull() {
            addCriterion("supplies_code is null");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeIsNotNull() {
            addCriterion("supplies_code is not null");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeEqualTo(String value) {
            addCriterion("supplies_code =", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotEqualTo(String value) {
            addCriterion("supplies_code <>", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeGreaterThan(String value) {
            addCriterion("supplies_code >", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeGreaterThanOrEqualTo(String value) {
            addCriterion("supplies_code >=", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeLessThan(String value) {
            addCriterion("supplies_code <", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeLessThanOrEqualTo(String value) {
            addCriterion("supplies_code <=", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeLike(String value) {
            addCriterion("supplies_code like", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotLike(String value) {
            addCriterion("supplies_code not like", value, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeIn(List<String> values) {
            addCriterion("supplies_code in", values, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotIn(List<String> values) {
            addCriterion("supplies_code not in", values, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeBetween(String value1, String value2) {
            addCriterion("supplies_code between", value1, value2, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andSuppliesCodeNotBetween(String value1, String value2) {
            addCriterion("supplies_code not between", value1, value2, "suppliesCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeIsNull() {
            addCriterion("warehouse_type_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeIsNotNull() {
            addCriterion("warehouse_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeEqualTo(Integer value) {
            addCriterion("warehouse_type_code =", value, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeNotEqualTo(Integer value) {
            addCriterion("warehouse_type_code <>", value, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeGreaterThan(Integer value) {
            addCriterion("warehouse_type_code >", value, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("warehouse_type_code >=", value, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeLessThan(Integer value) {
            addCriterion("warehouse_type_code <", value, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeLessThanOrEqualTo(Integer value) {
            addCriterion("warehouse_type_code <=", value, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeIn(List<Integer> values) {
            addCriterion("warehouse_type_code in", values, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeNotIn(List<Integer> values) {
            addCriterion("warehouse_type_code not in", values, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeBetween(Integer value1, Integer value2) {
            addCriterion("warehouse_type_code between", value1, value2, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseTypeCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("warehouse_type_code not between", value1, value2, "warehouseTypeCode");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseIsNull() {
            addCriterion("is_allow_purchase is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseIsNotNull() {
            addCriterion("is_allow_purchase is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseEqualTo(Integer value) {
            addCriterion("is_allow_purchase =", value, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseNotEqualTo(Integer value) {
            addCriterion("is_allow_purchase <>", value, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseGreaterThan(Integer value) {
            addCriterion("is_allow_purchase >", value, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_allow_purchase >=", value, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseLessThan(Integer value) {
            addCriterion("is_allow_purchase <", value, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseLessThanOrEqualTo(Integer value) {
            addCriterion("is_allow_purchase <=", value, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseIn(List<Integer> values) {
            addCriterion("is_allow_purchase in", values, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseNotIn(List<Integer> values) {
            addCriterion("is_allow_purchase not in", values, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_purchase between", value1, value2, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andIsAllowPurchaseNotBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_purchase not between", value1, value2, "isAllowPurchase");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagIsNull() {
            addCriterion("inventory_manage_flag is null");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagIsNotNull() {
            addCriterion("inventory_manage_flag is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagEqualTo(Integer value) {
            addCriterion("inventory_manage_flag =", value, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagNotEqualTo(Integer value) {
            addCriterion("inventory_manage_flag <>", value, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagGreaterThan(Integer value) {
            addCriterion("inventory_manage_flag >", value, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_manage_flag >=", value, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagLessThan(Integer value) {
            addCriterion("inventory_manage_flag <", value, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_manage_flag <=", value, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagIn(List<Integer> values) {
            addCriterion("inventory_manage_flag in", values, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagNotIn(List<Integer> values) {
            addCriterion("inventory_manage_flag not in", values, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagBetween(Integer value1, Integer value2) {
            addCriterion("inventory_manage_flag between", value1, value2, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andInventoryManageFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_manage_flag not between", value1, value2, "inventoryManageFlag");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierIsNull() {
            addCriterion("is_user_approved_supplier is null");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierIsNotNull() {
            addCriterion("is_user_approved_supplier is not null");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierEqualTo(Integer value) {
            addCriterion("is_user_approved_supplier =", value, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierNotEqualTo(Integer value) {
            addCriterion("is_user_approved_supplier <>", value, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierGreaterThan(Integer value) {
            addCriterion("is_user_approved_supplier >", value, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_user_approved_supplier >=", value, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierLessThan(Integer value) {
            addCriterion("is_user_approved_supplier <", value, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierLessThanOrEqualTo(Integer value) {
            addCriterion("is_user_approved_supplier <=", value, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierIn(List<Integer> values) {
            addCriterion("is_user_approved_supplier in", values, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierNotIn(List<Integer> values) {
            addCriterion("is_user_approved_supplier not in", values, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierBetween(Integer value1, Integer value2) {
            addCriterion("is_user_approved_supplier between", value1, value2, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsUserApprovedSupplierNotBetween(Integer value1, Integer value2) {
            addCriterion("is_user_approved_supplier not between", value1, value2, "isUserApprovedSupplier");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteIsNull() {
            addCriterion("is_request_ask_quote is null");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteIsNotNull() {
            addCriterion("is_request_ask_quote is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteEqualTo(Integer value) {
            addCriterion("is_request_ask_quote =", value, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteNotEqualTo(Integer value) {
            addCriterion("is_request_ask_quote <>", value, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteGreaterThan(Integer value) {
            addCriterion("is_request_ask_quote >", value, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_request_ask_quote >=", value, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteLessThan(Integer value) {
            addCriterion("is_request_ask_quote <", value, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteLessThanOrEqualTo(Integer value) {
            addCriterion("is_request_ask_quote <=", value, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteIn(List<Integer> values) {
            addCriterion("is_request_ask_quote in", values, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteNotIn(List<Integer> values) {
            addCriterion("is_request_ask_quote not in", values, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteBetween(Integer value1, Integer value2) {
            addCriterion("is_request_ask_quote between", value1, value2, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsRequestAskQuoteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_request_ask_quote not between", value1, value2, "isRequestAskQuote");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityIsNull() {
            addCriterion("is_merge_parity is null");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityIsNotNull() {
            addCriterion("is_merge_parity is not null");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityEqualTo(Integer value) {
            addCriterion("is_merge_parity =", value, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityNotEqualTo(Integer value) {
            addCriterion("is_merge_parity <>", value, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityGreaterThan(Integer value) {
            addCriterion("is_merge_parity >", value, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_merge_parity >=", value, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityLessThan(Integer value) {
            addCriterion("is_merge_parity <", value, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityLessThanOrEqualTo(Integer value) {
            addCriterion("is_merge_parity <=", value, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityIn(List<Integer> values) {
            addCriterion("is_merge_parity in", values, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityNotIn(List<Integer> values) {
            addCriterion("is_merge_parity not in", values, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityBetween(Integer value1, Integer value2) {
            addCriterion("is_merge_parity between", value1, value2, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsMergeParityNotBetween(Integer value1, Integer value2) {
            addCriterion("is_merge_parity not between", value1, value2, "isMergeParity");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingIsNull() {
            addCriterion("is_request_bidding is null");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingIsNotNull() {
            addCriterion("is_request_bidding is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingEqualTo(Integer value) {
            addCriterion("is_request_bidding =", value, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingNotEqualTo(Integer value) {
            addCriterion("is_request_bidding <>", value, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingGreaterThan(Integer value) {
            addCriterion("is_request_bidding >", value, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_request_bidding >=", value, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingLessThan(Integer value) {
            addCriterion("is_request_bidding <", value, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingLessThanOrEqualTo(Integer value) {
            addCriterion("is_request_bidding <=", value, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingIn(List<Integer> values) {
            addCriterion("is_request_bidding in", values, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingNotIn(List<Integer> values) {
            addCriterion("is_request_bidding not in", values, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingBetween(Integer value1, Integer value2) {
            addCriterion("is_request_bidding between", value1, value2, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andIsRequestBiddingNotBetween(Integer value1, Integer value2) {
            addCriterion("is_request_bidding not between", value1, value2, "isRequestBidding");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserIsNull() {
            addCriterion("default_purchase_user is null");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserIsNotNull() {
            addCriterion("default_purchase_user is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserEqualTo(String value) {
            addCriterion("default_purchase_user =", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserNotEqualTo(String value) {
            addCriterion("default_purchase_user <>", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserGreaterThan(String value) {
            addCriterion("default_purchase_user >", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserGreaterThanOrEqualTo(String value) {
            addCriterion("default_purchase_user >=", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserLessThan(String value) {
            addCriterion("default_purchase_user <", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserLessThanOrEqualTo(String value) {
            addCriterion("default_purchase_user <=", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserLike(String value) {
            addCriterion("default_purchase_user like", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserNotLike(String value) {
            addCriterion("default_purchase_user not like", value, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserIn(List<String> values) {
            addCriterion("default_purchase_user in", values, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserNotIn(List<String> values) {
            addCriterion("default_purchase_user not in", values, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserBetween(String value1, String value2) {
            addCriterion("default_purchase_user between", value1, value2, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchaseUserNotBetween(String value1, String value2) {
            addCriterion("default_purchase_user not between", value1, value2, "defaultPurchaseUser");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeIsNull() {
            addCriterion("default_cost_item_code is null");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeIsNotNull() {
            addCriterion("default_cost_item_code is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeEqualTo(String value) {
            addCriterion("default_cost_item_code =", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeNotEqualTo(String value) {
            addCriterion("default_cost_item_code <>", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeGreaterThan(String value) {
            addCriterion("default_cost_item_code >", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("default_cost_item_code >=", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeLessThan(String value) {
            addCriterion("default_cost_item_code <", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeLessThanOrEqualTo(String value) {
            addCriterion("default_cost_item_code <=", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeLike(String value) {
            addCriterion("default_cost_item_code like", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeNotLike(String value) {
            addCriterion("default_cost_item_code not like", value, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeIn(List<String> values) {
            addCriterion("default_cost_item_code in", values, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeNotIn(List<String> values) {
            addCriterion("default_cost_item_code not in", values, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeBetween(String value1, String value2) {
            addCriterion("default_cost_item_code between", value1, value2, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultCostItemCodeNotBetween(String value1, String value2) {
            addCriterion("default_cost_item_code not between", value1, value2, "defaultCostItemCode");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceIsNull() {
            addCriterion("default_purchase_price is null");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceIsNotNull() {
            addCriterion("default_purchase_price is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceEqualTo(BigDecimal value) {
            addCriterion("default_purchase_price =", value, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceNotEqualTo(BigDecimal value) {
            addCriterion("default_purchase_price <>", value, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceGreaterThan(BigDecimal value) {
            addCriterion("default_purchase_price >", value, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("default_purchase_price >=", value, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceLessThan(BigDecimal value) {
            addCriterion("default_purchase_price <", value, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("default_purchase_price <=", value, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceIn(List<BigDecimal> values) {
            addCriterion("default_purchase_price in", values, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceNotIn(List<BigDecimal> values) {
            addCriterion("default_purchase_price not in", values, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("default_purchase_price between", value1, value2, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andDefaultPurchasePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("default_purchase_price not between", value1, value2, "defaultPurchasePrice");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckIsNull() {
            addCriterion("is_request_check is null");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckIsNotNull() {
            addCriterion("is_request_check is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckEqualTo(Integer value) {
            addCriterion("is_request_check =", value, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckNotEqualTo(Integer value) {
            addCriterion("is_request_check <>", value, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckGreaterThan(Integer value) {
            addCriterion("is_request_check >", value, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_request_check >=", value, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckLessThan(Integer value) {
            addCriterion("is_request_check <", value, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckLessThanOrEqualTo(Integer value) {
            addCriterion("is_request_check <=", value, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckIn(List<Integer> values) {
            addCriterion("is_request_check in", values, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckNotIn(List<Integer> values) {
            addCriterion("is_request_check not in", values, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckBetween(Integer value1, Integer value2) {
            addCriterion("is_request_check between", value1, value2, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andIsRequestCheckNotBetween(Integer value1, Integer value2) {
            addCriterion("is_request_check not between", value1, value2, "isRequestCheck");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceIsNull() {
            addCriterion("default_receive_quantity_tolerance is null");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceIsNotNull() {
            addCriterion("default_receive_quantity_tolerance is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceEqualTo(Long value) {
            addCriterion("default_receive_quantity_tolerance =", value, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceNotEqualTo(Long value) {
            addCriterion("default_receive_quantity_tolerance <>", value, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceGreaterThan(Long value) {
            addCriterion("default_receive_quantity_tolerance >", value, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceGreaterThanOrEqualTo(Long value) {
            addCriterion("default_receive_quantity_tolerance >=", value, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceLessThan(Long value) {
            addCriterion("default_receive_quantity_tolerance <", value, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceLessThanOrEqualTo(Long value) {
            addCriterion("default_receive_quantity_tolerance <=", value, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceIn(List<Long> values) {
            addCriterion("default_receive_quantity_tolerance in", values, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceNotIn(List<Long> values) {
            addCriterion("default_receive_quantity_tolerance not in", values, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceBetween(Long value1, Long value2) {
            addCriterion("default_receive_quantity_tolerance between", value1, value2, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveQuantityToleranceNotBetween(Long value1, Long value2) {
            addCriterion("default_receive_quantity_tolerance not between", value1, value2, "defaultReceiveQuantityTolerance");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveIsNull() {
            addCriterion("is_allow_replace_receive is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveIsNotNull() {
            addCriterion("is_allow_replace_receive is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveEqualTo(Integer value) {
            addCriterion("is_allow_replace_receive =", value, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveNotEqualTo(Integer value) {
            addCriterion("is_allow_replace_receive <>", value, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveGreaterThan(Integer value) {
            addCriterion("is_allow_replace_receive >", value, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_allow_replace_receive >=", value, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveLessThan(Integer value) {
            addCriterion("is_allow_replace_receive <", value, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveLessThanOrEqualTo(Integer value) {
            addCriterion("is_allow_replace_receive <=", value, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveIn(List<Integer> values) {
            addCriterion("is_allow_replace_receive in", values, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveNotIn(List<Integer> values) {
            addCriterion("is_allow_replace_receive not in", values, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_replace_receive between", value1, value2, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowReplaceReceiveNotBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_replace_receive not between", value1, value2, "isAllowReplaceReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveIsNull() {
            addCriterion("is_allow_not_ordered_receive is null");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveIsNotNull() {
            addCriterion("is_allow_not_ordered_receive is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveEqualTo(Integer value) {
            addCriterion("is_allow_not_ordered_receive =", value, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveNotEqualTo(Integer value) {
            addCriterion("is_allow_not_ordered_receive <>", value, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveGreaterThan(Integer value) {
            addCriterion("is_allow_not_ordered_receive >", value, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_allow_not_ordered_receive >=", value, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveLessThan(Integer value) {
            addCriterion("is_allow_not_ordered_receive <", value, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveLessThanOrEqualTo(Integer value) {
            addCriterion("is_allow_not_ordered_receive <=", value, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveIn(List<Integer> values) {
            addCriterion("is_allow_not_ordered_receive in", values, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveNotIn(List<Integer> values) {
            addCriterion("is_allow_not_ordered_receive not in", values, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_not_ordered_receive between", value1, value2, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andIsAllowNotOrderedReceiveNotBetween(Integer value1, Integer value2) {
            addCriterion("is_allow_not_ordered_receive not between", value1, value2, "isAllowNotOrderedReceive");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayIsNull() {
            addCriterion("default_receive_way is null");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayIsNotNull() {
            addCriterion("default_receive_way is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayEqualTo(String value) {
            addCriterion("default_receive_way =", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayNotEqualTo(String value) {
            addCriterion("default_receive_way <>", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayGreaterThan(String value) {
            addCriterion("default_receive_way >", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayGreaterThanOrEqualTo(String value) {
            addCriterion("default_receive_way >=", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayLessThan(String value) {
            addCriterion("default_receive_way <", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayLessThanOrEqualTo(String value) {
            addCriterion("default_receive_way <=", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayLike(String value) {
            addCriterion("default_receive_way like", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayNotLike(String value) {
            addCriterion("default_receive_way not like", value, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayIn(List<String> values) {
            addCriterion("default_receive_way in", values, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayNotIn(List<String> values) {
            addCriterion("default_receive_way not in", values, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayBetween(String value1, String value2) {
            addCriterion("default_receive_way between", value1, value2, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWayNotBetween(String value1, String value2) {
            addCriterion("default_receive_way not between", value1, value2, "defaultReceiveWay");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeIsNull() {
            addCriterion("default_receive_warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeIsNotNull() {
            addCriterion("default_receive_warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeEqualTo(String value) {
            addCriterion("default_receive_warehouse_code =", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeNotEqualTo(String value) {
            addCriterion("default_receive_warehouse_code <>", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeGreaterThan(String value) {
            addCriterion("default_receive_warehouse_code >", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("default_receive_warehouse_code >=", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeLessThan(String value) {
            addCriterion("default_receive_warehouse_code <", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("default_receive_warehouse_code <=", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeLike(String value) {
            addCriterion("default_receive_warehouse_code like", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeNotLike(String value) {
            addCriterion("default_receive_warehouse_code not like", value, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeIn(List<String> values) {
            addCriterion("default_receive_warehouse_code in", values, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeNotIn(List<String> values) {
            addCriterion("default_receive_warehouse_code not in", values, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeBetween(String value1, String value2) {
            addCriterion("default_receive_warehouse_code between", value1, value2, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andDefaultReceiveWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("default_receive_warehouse_code not between", value1, value2, "defaultReceiveWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryIsNull() {
            addCriterion("produce_factory is null");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryIsNotNull() {
            addCriterion("produce_factory is not null");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryEqualTo(String value) {
            addCriterion("produce_factory =", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryNotEqualTo(String value) {
            addCriterion("produce_factory <>", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryGreaterThan(String value) {
            addCriterion("produce_factory >", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryGreaterThanOrEqualTo(String value) {
            addCriterion("produce_factory >=", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryLessThan(String value) {
            addCriterion("produce_factory <", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryLessThanOrEqualTo(String value) {
            addCriterion("produce_factory <=", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryLike(String value) {
            addCriterion("produce_factory like", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryNotLike(String value) {
            addCriterion("produce_factory not like", value, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryIn(List<String> values) {
            addCriterion("produce_factory in", values, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryNotIn(List<String> values) {
            addCriterion("produce_factory not in", values, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryBetween(String value1, String value2) {
            addCriterion("produce_factory between", value1, value2, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andProduceFactoryNotBetween(String value1, String value2) {
            addCriterion("produce_factory not between", value1, value2, "produceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryIsNull() {
            addCriterion("service_factory is null");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryIsNotNull() {
            addCriterion("service_factory is not null");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryEqualTo(String value) {
            addCriterion("service_factory =", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryNotEqualTo(String value) {
            addCriterion("service_factory <>", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryGreaterThan(String value) {
            addCriterion("service_factory >", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryGreaterThanOrEqualTo(String value) {
            addCriterion("service_factory >=", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryLessThan(String value) {
            addCriterion("service_factory <", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryLessThanOrEqualTo(String value) {
            addCriterion("service_factory <=", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryLike(String value) {
            addCriterion("service_factory like", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryNotLike(String value) {
            addCriterion("service_factory not like", value, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryIn(List<String> values) {
            addCriterion("service_factory in", values, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryNotIn(List<String> values) {
            addCriterion("service_factory not in", values, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryBetween(String value1, String value2) {
            addCriterion("service_factory between", value1, value2, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andServiceFactoryNotBetween(String value1, String value2) {
            addCriterion("service_factory not between", value1, value2, "serviceFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryIsNull() {
            addCriterion("after_sale_factory is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryIsNotNull() {
            addCriterion("after_sale_factory is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryEqualTo(String value) {
            addCriterion("after_sale_factory =", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryNotEqualTo(String value) {
            addCriterion("after_sale_factory <>", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryGreaterThan(String value) {
            addCriterion("after_sale_factory >", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_factory >=", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryLessThan(String value) {
            addCriterion("after_sale_factory <", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryLessThanOrEqualTo(String value) {
            addCriterion("after_sale_factory <=", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryLike(String value) {
            addCriterion("after_sale_factory like", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryNotLike(String value) {
            addCriterion("after_sale_factory not like", value, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryIn(List<String> values) {
            addCriterion("after_sale_factory in", values, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryNotIn(List<String> values) {
            addCriterion("after_sale_factory not in", values, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryBetween(String value1, String value2) {
            addCriterion("after_sale_factory between", value1, value2, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andAfterSaleFactoryNotBetween(String value1, String value2) {
            addCriterion("after_sale_factory not between", value1, value2, "afterSaleFactory");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeIsNull() {
            addCriterion("receive_type is null");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeIsNotNull() {
            addCriterion("receive_type is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeEqualTo(Integer value) {
            addCriterion("receive_type =", value, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeNotEqualTo(Integer value) {
            addCriterion("receive_type <>", value, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeGreaterThan(Integer value) {
            addCriterion("receive_type >", value, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("receive_type >=", value, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeLessThan(Integer value) {
            addCriterion("receive_type <", value, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeLessThanOrEqualTo(Integer value) {
            addCriterion("receive_type <=", value, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeIn(List<Integer> values) {
            addCriterion("receive_type in", values, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeNotIn(List<Integer> values) {
            addCriterion("receive_type not in", values, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeBetween(Integer value1, Integer value2) {
            addCriterion("receive_type between", value1, value2, "receiveType");
            return (Criteria) this;
        }

        public Criteria andReceiveTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("receive_type not between", value1, value2, "receiveType");
            return (Criteria) this;
        }

        public Criteria andBaseIdIsNull() {
            addCriterion("base_id is null");
            return (Criteria) this;
        }

        public Criteria andBaseIdIsNotNull() {
            addCriterion("base_id is not null");
            return (Criteria) this;
        }

        public Criteria andBaseIdEqualTo(Long value) {
            addCriterion("base_id =", value, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdNotEqualTo(Long value) {
            addCriterion("base_id <>", value, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdGreaterThan(Long value) {
            addCriterion("base_id >", value, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("base_id >=", value, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdLessThan(Long value) {
            addCriterion("base_id <", value, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("base_id <=", value, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdIn(List<Long> values) {
            addCriterion("base_id in", values, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdNotIn(List<Long> values) {
            addCriterion("base_id not in", values, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdBetween(Long value1, Long value2) {
            addCriterion("base_id between", value1, value2, "baseId");
            return (Criteria) this;
        }

        public Criteria andBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("base_id not between", value1, value2, "baseId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}