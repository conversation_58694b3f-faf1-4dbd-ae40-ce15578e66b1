package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;

/**
 * @author: yangjifan1
 * @date: 2021/11/10
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class SuppliesCodePrintExcel implements ExportModel {
    @ExportField(name = "资产编码")
    private String assetsCode;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }


}
