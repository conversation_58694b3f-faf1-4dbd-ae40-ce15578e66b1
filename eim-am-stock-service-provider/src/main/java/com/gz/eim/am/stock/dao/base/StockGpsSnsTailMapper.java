package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockGpsSnsTail;
import com.gz.eim.am.stock.entity.StockGpsSnsTailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockGpsSnsTailMapper {
    long countByExample(StockGpsSnsTailExample example);

    int deleteByPrimaryKey(Long snId);

    int insert(StockGpsSnsTail record);

    int insertSelective(StockGpsSnsTail record);

    List<StockGpsSnsTail> selectByExample(StockGpsSnsTailExample example);

    StockGpsSnsTail selectByPrimaryKey(Long snId);

    int updateByExampleSelective(@Param("record") StockGpsSnsTail record, @Param("example") StockGpsSnsTailExample example);

    int updateByExample(@Param("record") StockGpsSnsTail record, @Param("example") StockGpsSnsTailExample example);

    int updateByPrimaryKeySelective(StockGpsSnsTail record);

    int updateByPrimaryKey(StockGpsSnsTail record);
}