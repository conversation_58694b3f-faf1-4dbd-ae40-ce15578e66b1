package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsEbsSyncScrap;
import com.gz.eim.am.stock.entity.StockAssetsEbsSyncScrapExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsEbsSyncScrapMapper {
    long countByExample(StockAssetsEbsSyncScrapExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsEbsSyncScrap record);

    int insertSelective(StockAssetsEbsSyncScrap record);

    List<StockAssetsEbsSyncScrap> selectByExample(StockAssetsEbsSyncScrapExample example);

    StockAssetsEbsSyncScrap selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsEbsSyncScrap record, @Param("example") StockAssetsEbsSyncScrapExample example);

    int updateByExample(@Param("record") StockAssetsEbsSyncScrap record, @Param("example") StockAssetsEbsSyncScrapExample example);

    int updateByPrimaryKeySelective(StockAssetsEbsSyncScrap record);

    int updateByPrimaryKey(StockAssetsEbsSyncScrap record);
}