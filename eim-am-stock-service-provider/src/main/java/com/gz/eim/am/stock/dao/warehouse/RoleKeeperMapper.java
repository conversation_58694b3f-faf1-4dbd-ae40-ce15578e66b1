package com.gz.eim.am.stock.dao.warehouse;

import com.gz.eim.am.stock.dto.response.RoleKeeperResp;
import com.gz.eim.am.stock.entity.vo.StockKeeperWareSearchVo;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * <AUTHOR>
 */
public interface RoleKeeperMapper {

    /**
     * 校验用户是否超级管理员
     * @param keeperCode
     * @return
     */
    int checkSuperRole(String keeperCode);

    /**
     * 查出管理员管理的仓库
     * @param keeperCode
     * @return
     */
    List<String> selectKeepWarehouse(String keeperCode);

    /**
     * 查询仓库下的管理员
     * @param warehouseCode
     * @return
     */
    List<RoleKeeperResp> selectWarehouseKeeper(String warehouseCode);

    /**
     * 删除角色下的管理员
     * @param roleId
     * @return
     */
    int deleteByRoleId(Long roleId);

    /**
     * 根据角色和人员和仓库查询仓库权限
     * @param keeperCode
     * @param roleType
     * @param warehouseCode
     * @return
     */
    List<String> selectKeepWarehouseByParam(@Param("keeperCode") String keeperCode, @Param ("roleType")Integer roleType, @Param ("warehouseCode") String warehouseCode);

    /**
     * 根据角色和人员和仓库查询仓库权限
     * @param stockKeeperWareSearchVo
     * @return
     */
    List<String> selectKeepWarehouseByParamVo(StockKeeperWareSearchVo stockKeeperWareSearchVo);

    /**
     * 判断权限是否为ALL权限
     * @param keeperCode
     * @return
     */
    Long isAll(String keeperCode);
}