package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.StockAssetsEbsSync;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wei<PERSON><PERSON><PERSON>
 * @date: 2020/10/9
 * @description
 */
public interface StockAssetsSyncToEbsMapper {

    /**
     * 抽取待同步到ebs的资产数据
     * @return
     */
    List<StockAssetsEbsSync> extractAssets(@Param("limitNum") Integer limitNum);

    /**
     * 批量插入记录表数据
     * @param newSubDetailList
     */
    void batchInsert(List<StockAssetsEbsSync> newSubDetailList);

    /**
     * 查询所有待查询结果的查询码
     * @param syncStatus
     * @return
     */
    List<String> getQueryCodeList(List<Integer> syncStatus);

    /**
     * 批量更新资产
     * @param stockAssetsEbsSyncList
     * @return
     */
    Integer updateMultipleSelective(final List<StockAssetsEbsSync> stockAssetsEbsSyncList);


    ///////////////////ebs同步二期///////////////////
    /**
     * 提取新增资产数据
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSync> extractNewAssets(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
