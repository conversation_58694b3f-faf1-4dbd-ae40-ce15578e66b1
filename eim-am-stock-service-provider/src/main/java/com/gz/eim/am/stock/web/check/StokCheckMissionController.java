package com.gz.eim.am.stock.web.check;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.JsonUtil;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.api.check.StockCheckMissionApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.check.*;
import com.gz.eim.am.stock.service.check.StockCheckMissionDetailService;
import com.gz.eim.am.stock.dto.response.check.AssetQueryScopeRespDTO;
import com.gz.eim.am.stock.dto.response.check.StockAssetsCheckTaskDetailRespDTO;
import com.gz.eim.am.stock.dto.response.check.StockAssetsCheckTaskRespDTO;
import com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfig;
import com.gz.eim.am.stock.service.check.StockCheckMissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: weijunjie
 * @date: 2020/11/4
 * @description 盘点任务管理接口类
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/checkTask")
public class StokCheckMissionController implements StockCheckMissionApi {

    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private StockCheckMissionService stockCheckMissionService;

    @Autowired
    private StockCheckMissionDetailService stockCheckMissionDetailService;

    @Autowired
    RedisUtil redisUtil;

    @Override
    public ResponseData queryCheckMethodConfig(Integer checkTaskMethodCode) {
        log.info("/api/am/stock/checkTask/checkMethodConfig {}", checkTaskMethodCode);
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.queryCheckMethodConfig(checkTaskMethodCode);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("查询盘点配置信息报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData saveCheckTask(StockCheckMissionReqDTO stockCheckMissionReqDTO) {
        log.info("/api/am/stock/checkTask/save {}", JsonUtil.getJsonString(stockCheckMissionReqDTO));
        ResponseData res = null;
        String lockKey = RedisKeyConstants.CREATE_STOCK_ASSETS_CHECK_TASK + stockCheckMissionReqDTO.getTakingPlanNo();
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                JwtUser user = SecurityUtil.getJwtUser();
                log.info("---------------------login-user--{}---------------------", user.getEmployeeCode());
                res = this.stockCheckMissionService.saveCheckTask(stockCheckMissionReqDTO, user);
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("保存盘点任务单据错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData updateCheckPeople(StockCheckCommonReqDTO stockCheckCommonReqDTO) {
        log.info("/api/am/stock/checkTask/updateCheckPeople {}", stockCheckCommonReqDTO.getCheckTaskId());
        ResponseData res = null;
        String lockKey = RedisKeyConstants.UPDATE_ASSETS_CHECK_PEOPLES + stockCheckCommonReqDTO.getCheckTaskId();
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                res = this.stockCheckMissionService.updateCheckPeople(stockCheckCommonReqDTO.getCheckTaskId(),stockCheckCommonReqDTO.getCheckPeoples());
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("更新任务盘点人错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData cancel(Long checkTaskId) {
        log.info("/api/am/stock/checkTask/cancel {}", checkTaskId);
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.cancel(checkTaskId);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("盘点任务取消错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData finish(Long checkTaskId, String takingPlanNo) {
        log.info("/api/am/stock/checkTask/finish/{}/{}", checkTaskId, takingPlanNo);
        ResponseData res;
        String lockKey = RedisKeyConstants.ASSETS_CHECK_TASK_FINISH + takingPlanNo;
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                res = this.stockCheckMissionService.finish(checkTaskId);
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("盘点任务结束错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData taskAssign(Long checkTaskId, String takingPlanNo) {
        log.info("/api/am/stock/checkTask/assign/{}/{} ", checkTaskId, takingPlanNo);
        ResponseData res;
        String lockKey = RedisKeyConstants.CHECK_TASK_ASSIGN_TO_PEOPLE + takingPlanNo;
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                res = this.stockCheckMissionService.taskAssign(checkTaskId);
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("存在未下放完成的盘点任务请稍后重试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("更新任务盘点人错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryCheckTaskList(StockTakingSnapshotQueryDTO stockTakingSnapshotQueryDTO) {
        log.info("/api/am/stock/checkTask/list {}", stockTakingSnapshotQueryDTO);
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.queryCheckTaskList(stockTakingSnapshotQueryDTO);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("分页查询盘点任务错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryDetailById(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        log.info("/api/am/stock/checkTask/detail {}", JsonUtil.getJsonString(assetQueryScopeReqDTO));
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.queryDetailById(assetQueryScopeReqDTO);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("盘点任务明细查询错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData updateToDoStatus() {
        log.info("/api/am/stock/checkTask/update-to-do-status {}");
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.updateToDoStatus();
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("更新统一代办状态", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData closeCheck(String takingPlanNo, Long checkTaskId) {
        log.info("/api/am/stock/checkTask/close-check {}");
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.closeCheck(takingPlanNo,checkTaskId);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("员工结束盘点", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * @param:
     * @description: 定时任务调用：对于盘点任务中没有盘完资产的同学，并且距下发时间已经超过了一天，进行呱呱消息提醒
     * @return:
     * @author: <EMAIL>
     * @date: 2022/1/19
     */
    @Override
    public ResponseData urgeCheck() {
        log.info("/api/am/stock/checkTask/sendGuaGuaMessage/urgeCheck");
        ResponseData res = null;
        try {
            res = this.stockCheckMissionService.urgeCheck();
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("定时任务执行出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

}
