package com.gz.eim.am.stock.web.check;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.util.JsonUtil;
import com.fuu.eim.support.util.RedisUtil;
import com.gz.eim.am.stock.api.check.StockCheckResultExecuteControllerApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.service.check.StockCheckResultExecuteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: weijunjie
 * @date: 2021/2/20
 * @description 盘点结果执行接口
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/check-result-execute")
public class StockCheckResultExecuteController implements StockCheckResultExecuteControllerApi {

    @Value("${namespace.name}")
    private String nameSpace;
    @Autowired
    RedisUtil redisUtil;

    @Autowired
    StockCheckResultExecuteService stockCheckResultExecuteService;

    @Override
    public ResponseData profitAndLossExecute(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) {
        log.info("/api/am/stock/check-result-execute/adjust/profit  checkDifferenceListHeadReqDTO={}", JsonUtil.getJsonString(checkDifferenceListHeadReqDTO));
        ResponseData res = null;
        String lockKey = RedisKeyConstants.STOCK_CHECK_ADJUST_PROFIT + checkDifferenceListHeadReqDTO.getTakingPlanNo();
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                res = this.stockCheckResultExecuteService.profitAndLossExecute(checkDifferenceListHeadReqDTO);
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (RuntimeException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("执行盘盈亏调整错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData checkDiffAdjustExecute(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) {
        log.info("/api/am/stock/check-result-execute/adjust/difference checkDifferenceListHeadReqDTO={}", checkDifferenceListHeadReqDTO.toString());
        ResponseData res = null;
        String lockKey = RedisKeyConstants.STOCK_CHECK_ADJUST_UPDATE +checkDifferenceListHeadReqDTO.getTakingPlanNo()+checkDifferenceListHeadReqDTO.getLineId();
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)){
                res = this.stockCheckResultExecuteService.checkDiffAdjustExecute(checkDifferenceListHeadReqDTO.getTakingPlanNo(),checkDifferenceListHeadReqDTO.getLineId());
                redisUtil.deleteByKey (nameSpace, lockKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        }catch (RuntimeException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info("执行盘点调整更新资产状态错误", e);
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error("执行盘点调整更新资产状态错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
