package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockAssetsCheckTaskAssignPeople {
    private Long id;

    private String takingPlanNo;

    private Long checkTaskId;

    private String checkPerson;

    private String createdBy;

    private Date createdAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTakingPlanNo() {
        return takingPlanNo;
    }

    public void setTakingPlanNo(String takingPlanNo) {
        this.takingPlanNo = takingPlanNo == null ? null : takingPlanNo.trim();
    }

    public Long getCheckTaskId() {
        return checkTaskId;
    }

    public void setCheckTaskId(Long checkTaskId) {
        this.checkTaskId = checkTaskId;
    }

    public String getCheckPerson() {
        return checkPerson;
    }

    public void setCheckPerson(String checkPerson) {
        this.checkPerson = checkPerson == null ? null : checkPerson.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}