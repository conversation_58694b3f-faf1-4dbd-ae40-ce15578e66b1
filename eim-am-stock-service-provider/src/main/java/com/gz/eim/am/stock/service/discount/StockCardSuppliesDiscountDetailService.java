package com.gz.eim.am.stock.service.discount;

import com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 1/27/21 6:49 下午
 * @description
 */
public interface StockCardSuppliesDiscountDetailService {

    /**
     * 根据申请单号查询明细信息
     * @param discountNo
     * @return
     * @throws Exception
     */
    List<StockCardSuppliesDiscountDetail> getByDiscountNo(String discountNo)throws Exception;
}
