package com.gz.eim.am.stock.entity.ambase;

import java.util.Date;

public class SysUser {
    private Long id;

    private String name;

    private String firstName;

    private String lastName;

    private String sex;

    private String pinyin;

    private String email;

    private String phone;

    private Byte idCardType;

    private String idCard;

    private String status;

    private String empType;

    private Byte employType;

    private String empId;

    private String jobId;

    private String jobLevel;

    private String deptId;

    private String companyId;

    private String entryLocation;

    private String workLocation;

    private String supervisorId;

    private Date birthday;

    private String hpsCountry;

    private String nativePlaceChn;

    private String birthplace;

    private String hukouTypeChn;

    private String contribAreaChn;

    private String marStatus;

    private String ethnicGrpCd;

    private String startDtChn;

    private Date entryDate;

    private Date dmsDate;

    private Byte delFlag;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName == null ? null : firstName.trim();
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName == null ? null : lastName.trim();
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin == null ? null : pinyin.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public Byte getIdCardType() {
        return idCardType;
    }

    public void setIdCardType(Byte idCardType) {
        this.idCardType = idCardType;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard == null ? null : idCard.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getEmpType() {
        return empType;
    }

    public void setEmpType(String empType) {
        this.empType = empType == null ? null : empType.trim();
    }

    public Byte getEmployType() {
        return employType;
    }

    public void setEmployType(Byte employType) {
        this.employType = employType;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId == null ? null : empId.trim();
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId == null ? null : jobId.trim();
    }

    public String getJobLevel() {
        return jobLevel;
    }

    public void setJobLevel(String jobLevel) {
        this.jobLevel = jobLevel == null ? null : jobLevel.trim();
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId == null ? null : deptId.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getEntryLocation() {
        return entryLocation;
    }

    public void setEntryLocation(String entryLocation) {
        this.entryLocation = entryLocation == null ? null : entryLocation.trim();
    }

    public String getWorkLocation() {
        return workLocation;
    }

    public void setWorkLocation(String workLocation) {
        this.workLocation = workLocation == null ? null : workLocation.trim();
    }

    public String getSupervisorId() {
        return supervisorId;
    }

    public void setSupervisorId(String supervisorId) {
        this.supervisorId = supervisorId == null ? null : supervisorId.trim();
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getHpsCountry() {
        return hpsCountry;
    }

    public void setHpsCountry(String hpsCountry) {
        this.hpsCountry = hpsCountry == null ? null : hpsCountry.trim();
    }

    public String getNativePlaceChn() {
        return nativePlaceChn;
    }

    public void setNativePlaceChn(String nativePlaceChn) {
        this.nativePlaceChn = nativePlaceChn == null ? null : nativePlaceChn.trim();
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace == null ? null : birthplace.trim();
    }

    public String getHukouTypeChn() {
        return hukouTypeChn;
    }

    public void setHukouTypeChn(String hukouTypeChn) {
        this.hukouTypeChn = hukouTypeChn == null ? null : hukouTypeChn.trim();
    }

    public String getContribAreaChn() {
        return contribAreaChn;
    }

    public void setContribAreaChn(String contribAreaChn) {
        this.contribAreaChn = contribAreaChn == null ? null : contribAreaChn.trim();
    }

    public String getMarStatus() {
        return marStatus;
    }

    public void setMarStatus(String marStatus) {
        this.marStatus = marStatus == null ? null : marStatus.trim();
    }

    public String getEthnicGrpCd() {
        return ethnicGrpCd;
    }

    public void setEthnicGrpCd(String ethnicGrpCd) {
        this.ethnicGrpCd = ethnicGrpCd == null ? null : ethnicGrpCd.trim();
    }

    public String getStartDtChn() {
        return startDtChn;
    }

    public void setStartDtChn(String startDtChn) {
        this.startDtChn = startDtChn == null ? null : startDtChn.trim();
    }

    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    public Date getDmsDate() {
        return dmsDate;
    }

    public void setDmsDate(Date dmsDate) {
        this.dmsDate = dmsDate;
    }

    public Byte getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Byte delFlag) {
        this.delFlag = delFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}