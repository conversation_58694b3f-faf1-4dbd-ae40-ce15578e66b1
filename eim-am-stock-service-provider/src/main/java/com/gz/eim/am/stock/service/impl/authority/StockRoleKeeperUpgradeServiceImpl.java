package com.gz.eim.am.stock.service.impl.authority;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.authority.StockRoleKeeperExtendMapper;
import com.gz.eim.am.stock.dao.base.StockRoleKeeperMapper;
import com.gz.eim.am.stock.dto.request.PageReqDTO;
import com.gz.eim.am.stock.dto.request.authority.StockRoleKeeperReqDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.authority.StockRoleKeeperRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysDept;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.StockRoleKeeperImportExcel;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.authority.StockManageRoleUpgradeService;
import com.gz.eim.am.stock.service.authority.StockRoleKeeperUpgradeService;
import com.gz.eim.am.stock.util.em.StockAuthorityEnum;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/5/18
 * @description
 */
@Service
@Slf4j
public class StockRoleKeeperUpgradeServiceImpl implements StockRoleKeeperUpgradeService {


    @Autowired
    private StockRoleKeeperMapper stockRoleKeeperMapper;

    @Autowired
    private StockManageRoleUpgradeService stockManageRoleService;

    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Autowired
    private StockRoleKeeperExtendMapper stockRoleKeeperExtendMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData save(StockRoleKeeperReqDTO stockRoleKeeperReqDTO, JwtUser user) {
        //校验参数
        String checkSaveParam = checkSaveParam (stockRoleKeeperReqDTO);
        if (StringUtils.isNotBlank (checkSaveParam)) {
            return ResponseData.createFailResult (checkSaveParam);
        }
        log.info("资产转移保存校验参数已通过》》");

        //插入数据库
        StockRoleKeeper stockRoleKeeper = new StockRoleKeeper();
        BeanUtils.copyProperties(stockRoleKeeperReqDTO,stockRoleKeeper);
        if(StringUtils.isNotBlank(stockRoleKeeperReqDTO.getKeeperCode())){
            SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockRoleKeeperReqDTO.getKeeperCode());
            stockRoleKeeper.setDeptId(sysUserBasicInfo != null ? sysUserBasicInfo.getDeptId() : "");
        }
        stockRoleKeeper.setCreatedAt(new Date());
        stockRoleKeeper.setCreatedBy(user.getEmployeeCode());
        stockRoleKeeper.setUpdatedAt(new Date());
        stockRoleKeeper.setUpdatedBy(user.getEmployeeCode());
        stockRoleKeeper.setStatus(StockAuthorityEnum.Status.VALID.getCode());
        stockRoleKeeper.setDelFlag(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
        //获取员工联系方式
        if(StringUtils.isNotBlank(stockRoleKeeperReqDTO.getKeeperCode())){
            List<String> empId = new ArrayList<>();
            empId.add(stockRoleKeeperReqDTO.getKeeperCode());
            List<SysUser> users = ambaseCommonService.selectUsersByIds(empId);
            stockRoleKeeper.setContactWay(CollectionUtils.isNotEmpty(users) ? users.get(0).getPhone() : "");
        }else{
            stockRoleKeeper.setContactWay("");
        }
        stockRoleKeeperMapper.insert(stockRoleKeeper);

        return ResponseData.createSuccessResult ();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData delete(Long roleKeeperId) {
        //校验当前id存在
        if(roleKeeperId == null){
            return ResponseData.createFailResult ("传入的参数为空");
        }
        if(stockRoleKeeperMapper.selectByPrimaryKey(roleKeeperId) == null){
            return ResponseData.createFailResult ("该条数据在系统不存在");
        }

        //逻辑删除，将del_flag设置为1
        StockRoleKeeperExample stockRoleKeeperExample = new StockRoleKeeperExample();
        StockRoleKeeperExample.Criteria criteria = stockRoleKeeperExample.createCriteria();
        criteria.andRoleKeeperIdEqualTo(roleKeeperId);

        StockRoleKeeper stockRoleKeeper = new StockRoleKeeper();
        stockRoleKeeper.setDelFlag(StockAuthorityEnum.DelFlag.DELETE.getCode());
        stockRoleKeeper.setStatus(StockAuthorityEnum.Status.NO_VALID.getCode());
        stockRoleKeeperMapper.updateByExampleSelective(stockRoleKeeper,stockRoleKeeperExample);

        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData selectRoleKeepersOut(StockRoleKeeperReqDTO stockRoleKeeperReqDTO) {
        StockRoleKeeperExample stockRoleKeeperExample = new StockRoleKeeperExample();
        StockRoleKeeperExample.Criteria criteria = stockRoleKeeperExample.createCriteria();
        criteria.andStatusEqualTo(StockAuthorityEnum.Status.VALID.getCode()).andDelFlagEqualTo(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
        if(stockRoleKeeperReqDTO.getRoleId() != null){
            criteria.andRoleIdEqualTo(stockRoleKeeperReqDTO.getRoleId());
        }
        //查询总数
        long count = stockRoleKeeperMapper.countByExample(stockRoleKeeperExample);
        //分页查询数据
        //设置分页数据
        stockRoleKeeperReqDTO.initPageParam();
        stockRoleKeeperExample.setLimit(stockRoleKeeperReqDTO.getPageSize());
        stockRoleKeeperExample.setOffset(stockRoleKeeperReqDTO.getStartNum());
        String orderByClause = " role_keeper_id desc";
        if (StringUtils.isNotBlank(stockRoleKeeperReqDTO.getSortColumns())) {
            orderByClause = stockRoleKeeperReqDTO.getSortColumns();
        }
        stockRoleKeeperExample.setOrderByClause(orderByClause);

        //分页查询获取数据列表
        List<StockRoleKeeper> stockRoleKeepers = stockRoleKeeperMapper.selectByExample(stockRoleKeeperExample);
        HashSet<String> empIds = new HashSet<>();
        HashSet<String> deptIds = new HashSet<>();
        stockRoleKeepers.stream().forEach(dto->{
            empIds.add(dto.getKeeperCode());
            empIds.add(dto.getCreatedBy());
            deptIds.add(dto.getDeptId());
        });

        List<String> empIdList = new ArrayList<>();
        empIdList.addAll(empIds);
        List<String> deptIdList = new ArrayList<>();
        deptIdList.addAll(deptIds);

        Map<String,SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        Map<String, SysDept> deptMap = ambaseCommonService.selectSysDeptMapByIds(deptIdList);
        List<StockRoleKeeperRespDTO> stockRoleKeeperRespDTOS = new ArrayList<>();
        stockRoleKeepers.stream().forEach(dto->{
            StockRoleKeeperRespDTO stockRoleKeeperRespDTO = new StockRoleKeeperRespDTO();
            BeanUtils.copyProperties(dto,stockRoleKeeperRespDTO);
            stockRoleKeeperRespDTO.setKeeperName(sysUserMap.get(dto.getKeeperCode()) != null ? sysUserMap.get(dto.getKeeperCode()).getName() : null);
            stockRoleKeeperRespDTO.setCreatedByName(sysUserMap.get(dto.getCreatedBy()) != null ? sysUserMap.get(dto.getCreatedBy()).getName() : null);
            stockRoleKeeperRespDTO.setDeptName(deptMap.get(dto.getDeptId()) !=null ? deptMap.get(dto.getDeptId()).getDeptName() : null);
            try {
                stockRoleKeeperRespDTO.setCreatedAt(DateUtils.dateFormat(dto.getCreatedAt(),null));
            } catch (Exception e) {
                e.printStackTrace();
            }
            stockRoleKeeperRespDTOS.add(stockRoleKeeperRespDTO);
        });

        //返回结果
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        pageRespDTO.setCount(count);
        pageRespDTO.setPageNum(stockRoleKeeperReqDTO.getPageNum());
        pageRespDTO.setPageSize(stockRoleKeeperReqDTO.getPageSize());
        pageRespDTO.setStartNum(stockRoleKeeperReqDTO.getStartNum());
        pageRespDTO.setData(stockRoleKeeperRespDTOS);

        return pageRespDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData batchSaveRoleKeepers(List<StockRoleKeeperImportExcel> importExcelList,JwtUser user) {

        //文件数据不能为空
        if (CollectionUtils.isEmpty(importExcelList)) {
            return ResponseData.createFailResult("文件内数据为空");
        }

        //清单中不能有空的列
        final List<String> empIds = importExcelList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getEmpId()))
                .map(item -> item.getEmpId())
                .collect(Collectors.toList());
        final List<String> roleNames = importExcelList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getRoleName()))
                .map(item -> item.getRoleName())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(empIds) || importExcelList.size() != empIds.size()) {
            return ResponseData.createFailResult("文件内数据量与解析的员工工号数量对应不上，员工工号存在为空的列");
        }
        if (CollectionUtils.isEmpty(roleNames) || importExcelList.size() != roleNames.size()) {
            return ResponseData.createFailResult("文件内数据量与解析的角色名称数量对应不上，角色名称存在为空的列");
        }

        Map<String,SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIds);
        List<StockManageRole> stockManageRoles = stockManageRoleService.selectManageRolesByRoleNames(roleNames);

        //对清单中的数据进行去重
        importExcelList = importExcelList.stream().distinct().collect(Collectors.toList());

        //校验参数
        String checkSaveParam = checkBatchSaveParam (importExcelList,sysUserMap,empIds,stockManageRoles,roleNames);
        if (StringUtils.isNotBlank (checkSaveParam)) {
            return ResponseData.createFailResult (checkSaveParam);
        }
        log.info("资产转移保存校验参数已通过》》");

        //准备数据进行批量插入
        Map<String,StockManageRole> stockManageRoleMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        stockManageRoles.stream().forEach(dto->{
            stockManageRoleMap.put(dto.getRoleName(),dto);
        });
        List<StockRoleKeeper> stockRoleKeepers = new ArrayList<>();
        importExcelList.stream().forEach(dto->{
            StockRoleKeeper stockRoleKeeper = new StockRoleKeeper();
            stockRoleKeeper.setDeptId(sysUserMap.get(dto.getEmpId()).getDeptId());
            stockRoleKeeper.setKeeperCode(dto.getEmpId());
            stockRoleKeeper.setContactWay(StringUtils.isNotBlank(sysUserMap.get(dto.getEmpId()).getPhone()) ? sysUserMap.get(dto.getEmpId()).getPhone() : "");
            stockRoleKeeper.setRoleId(stockManageRoleMap.get(dto.getRoleName()).getRoleId());
            stockRoleKeeper.setStatus(StockAuthorityEnum.Status.VALID.getCode());
            stockRoleKeeper.setDelFlag(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
            stockRoleKeeper.setCreatedBy(user.getEmployeeCode());
            stockRoleKeeper.setCreatedAt(new Date());
            stockRoleKeeper.setUpdatedBy(user.getEmployeeCode());
            stockRoleKeeper.setUpdatedAt(new Date());
            stockRoleKeepers.add(stockRoleKeeper);
        });

        stockRoleKeeperExtendMapper.batchInsertStockRoleKeeper(stockRoleKeepers);

        return ResponseData.createSuccessResult();
    }



    @Override
    public List<StockRoleKeeper> selectRoleKeeperListByEmpIds(List<String> empIds) {
        List<StockRoleKeeper> stockRoleKeepers = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empIds)) {
            //去重
            HashSet<String> empIdHashSet = new HashSet<>(empIds);
            List<String> empidList = new ArrayList<>(empIdHashSet);

            int toIndex = CommonConstant.MAX_QUERY_COUNT;
            for (int i = 0; i < empidList.size(); i += CommonConstant.MAX_QUERY_COUNT) {
                if (i + CommonConstant.MAX_QUERY_COUNT > empidList.size()) {
                    toIndex = empidList.size() - i;
                }

                List<String> subempIdList = empidList.subList(i, i + toIndex);
                StockRoleKeeperExample example = new StockRoleKeeperExample();
                StockRoleKeeperExample.Criteria criteria = example.createCriteria();
                criteria.andKeeperCodeIn(subempIdList);
                List<StockRoleKeeper> stockManageRoleList = stockRoleKeeperMapper.selectByExample(example);
                stockRoleKeepers.addAll(stockManageRoleList);
            }
        }
        return stockRoleKeepers;
    }

    @Override
    public Map<String, List<String>> selectRoleKeepersMapByEmpIds(List<String> empIds) {
        List<StockRoleKeeper> stockRoleKeepers = this.selectRoleKeeperListByEmpIds(empIds);
        HashSet<Long> roleIdHashSet = new HashSet<>();
        stockRoleKeepers.stream().forEach(dto->{
            roleIdHashSet.add(dto.getRoleId());
        });
        List<Long> roleIdList = new ArrayList<>(roleIdHashSet);
        List<StockManageRole> stockManageRoles = stockManageRoleService.selectManageRolesByRoleIds(roleIdList);

        //key：roleid  value：rolename
        Map<Long, String> roleMap = new HashMap<Long, String>(CommonConstant.DEFAULT_MAP_SIZE);
        stockManageRoles.stream().forEach(dto->{
            roleMap.put(dto.getRoleId(),dto.getRoleName());
        });

        //key: empid    value:rolenameList
        Map<String, List<String>> stringListMap = new HashMap<String, List<String>>(CommonConstant.DEFAULT_MAP_SIZE);
        stockRoleKeepers.stream().forEach(dto->{
            if(stringListMap.get(dto.getKeeperCode()) == null){
                List<String> roleNames = new ArrayList<>();
                roleNames.add(roleMap.get(dto.getRoleId()));
                stringListMap.put(dto.getKeeperCode(),roleNames);
            }else{
                List<String> roleNames = stringListMap.get(dto.getKeeperCode());
                roleNames.add(roleMap.get(dto.getRoleId()));
            }
        });

        return stringListMap;
    }

    @Override
    public List<StockRoleKeeperImportExcel> selectExistEmpIdRoleList(List<StockRoleKeeperImportExcel> stockRoleKeeperImportExcels,Integer type) {
        List<StockRoleKeeperImportExcel> stockRoleKeeperImportExcelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stockRoleKeeperImportExcels)) {
            int toIndex = CommonConstant.MAX_QUERY_COUNT;
            for (int i = 0; i < stockRoleKeeperImportExcels.size(); i += CommonConstant.MAX_QUERY_COUNT) {
                if (i + CommonConstant.MAX_QUERY_COUNT > stockRoleKeeperImportExcels.size()) {
                    toIndex = stockRoleKeeperImportExcels.size() - i;
                }

                List<StockRoleKeeperImportExcel> subRoleKeeperList = stockRoleKeeperImportExcels.subList(i, i + toIndex);
                if(type.equals(StockAuthorityEnum.ValidDeptRole.KeeperCode.getCode())){
                    List<StockRoleKeeperImportExcel> existList = stockRoleKeeperExtendMapper.selectExistEmpIdRoleList(subRoleKeeperList);
                    stockRoleKeeperImportExcelList.addAll(existList);
                }else{
                    List<StockRoleKeeperImportExcel> existList = stockRoleKeeperExtendMapper.selectExistDeptRoleList(subRoleKeeperList);
                    stockRoleKeeperImportExcelList.addAll(existList);
                }
            }
        }
        return stockRoleKeeperImportExcelList;
    }


    /**
     * 新建时校验前端传入的参数
     * @param stockRoleKeeperReqDTO
     * @return
     */
    private String checkSaveParam(StockRoleKeeperReqDTO stockRoleKeeperReqDTO) {
        if(stockRoleKeeperReqDTO.getRoleId() == null){
            return "传入的角色不能为空";
        }

        StockManageRole stockManageRole = stockManageRoleService.selectManageRoleById(stockRoleKeeperReqDTO.getRoleId());
        if(stockManageRole == null){
            return "传入的角色id："+stockRoleKeeperReqDTO.getRoleId()+"不存在;";
        }else{
            if(DateUtils.dateCompare(stockManageRole.getStartDate(),new Date()) > 0 || DateUtils.dateCompare(new Date(),stockManageRole.getEndDate())>0){
                return "角色已经过了有效期期间";
            }
            if(StockAuthorityEnum.Status.NO_VALID.getCode().equals(stockManageRole.getStatus()) || StockAuthorityEnum.DelFlag.DELETE.getCode().equals(stockManageRole.getDelFlag())){
                return "角色已经失效或被删除";
            }
        }

        if(StringUtils.isBlank(stockRoleKeeperReqDTO.getKeeperCode()) && StringUtils.isBlank(stockRoleKeeperReqDTO.getDeptId())){
            return "部门id和员工号不能同时为空";
        }

        if(StringUtils.isNotBlank(stockRoleKeeperReqDTO.getKeeperCode()) && StringUtils.isNotBlank(stockRoleKeeperReqDTO.getDeptId())){
            SysUserBasicInfo userBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockRoleKeeperReqDTO.getKeeperCode());
            if(!stockRoleKeeperReqDTO.getDeptId().equals(userBasicInfo.getDeptId())){
                return "录入的部门id和员工id不匹配";
            }
        }

        StockRoleKeeperExample stockRoleKeeperExample = new StockRoleKeeperExample();
        StockRoleKeeperExample.Criteria criteria = stockRoleKeeperExample.createCriteria();
        criteria.andRoleIdEqualTo(stockRoleKeeperReqDTO.getRoleId()).andStatusEqualTo(StockAuthorityEnum.Status.VALID.getCode()).andDelFlagEqualTo(StockAuthorityEnum.DelFlag.NO_DELETE.getCode());
        if(StringUtils.isNotBlank(stockRoleKeeperReqDTO.getKeeperCode())){
            criteria.andKeeperCodeEqualTo(stockRoleKeeperReqDTO.getKeeperCode());
            List<StockRoleKeeper> stockRoleKeeperList = stockRoleKeeperMapper.selectByExample(stockRoleKeeperExample);
            if(stockRoleKeeperList != null && stockRoleKeeperList.size()>0){
                return "系统中已存在roleId："+stockRoleKeeperReqDTO.getRoleId()+"和员工:"+stockRoleKeeperReqDTO.getKeeperCode()+"的关联关系;";
            }
            if(StringUtils.isNotBlank(stockRoleKeeperExtendMapper.selectExistDeptRole(stockRoleKeeperReqDTO.getKeeperCode(),stockRoleKeeperReqDTO.getRoleId()))){
                return "系统中已存在roleId："+stockRoleKeeperReqDTO.getRoleId()+"和员工:"+stockRoleKeeperReqDTO.getKeeperCode()+"所在部门的关联关系;";
            }
        }else{
            criteria.andDeptIdEqualTo(stockRoleKeeperReqDTO.getDeptId());
            List<StockRoleKeeper> stockRoleKeeperList = stockRoleKeeperMapper.selectByExample(stockRoleKeeperExample);
            if(stockRoleKeeperList != null && stockRoleKeeperList.size()>0){
                return "系统中已存在roleId："+stockRoleKeeperReqDTO.getRoleId()+"和部门:"+stockRoleKeeperReqDTO.getDeptId()+"的关联关系;";
            }
        }

        return null;
    }

    /**
     * 批量导入参数校验
     * @param importExcelList
     * @return
     */
    private String checkBatchSaveParam(List<StockRoleKeeperImportExcel> importExcelList,Map<String,SysUser> sysUserMap,List<String> empIds,
                                       List<StockManageRole> stockManageRoles,List<String> roleNames) {

        //校验文件中不存在的员工和角色
        StringBuilder sb = new StringBuilder ();
        Set<String> existsEmpIds = sysUserMap.keySet();
        HashSet<String> empIdsHashSet = new HashSet<>(empIds);
        empIdsHashSet.removeAll(existsEmpIds);
        if (empIdsHashSet.size()>0){
            empIdsHashSet.stream().forEach(code ->{
                sb.append("系统中不存在员工工号："+code+";");
            });
        }

        HashSet<String> stockManageRoleSet = new HashSet<>();
        stockManageRoles.stream().forEach(dto->{
            stockManageRoleSet.add(dto.getRoleName());
        });
        HashSet<String> empIdsHashHashSet = new HashSet<>(roleNames);
        empIdsHashHashSet.removeAll(stockManageRoleSet);
        if (empIdsHashHashSet.size()>0){
            empIdsHashHashSet.stream().forEach(code ->{
                sb.append("系统中不存在或者已失效的角色："+code+";");
            });
        }

        if(StringUtils.isNotBlank(sb.toString())){
            return sb.toString();
        }

        //去除员工和角色已存在关联的数据
        List<StockRoleKeeperImportExcel> importExcels = this.selectExistEmpIdRoleList(importExcelList,StockAuthorityEnum.ValidDeptRole.KeeperCode.getCode());
        if(CollectionUtils.isNotEmpty(importExcels)){
            importExcelList.removeAll(importExcels);
        }
        //去除员工所属部门和角色已存在关联的数据
        List<StockRoleKeeperImportExcel> keeperImportExcels = this.selectExistEmpIdRoleList(importExcelList,StockAuthorityEnum.ValidDeptRole.KeeperDept.getCode());
        if(CollectionUtils.isNotEmpty(keeperImportExcels)){
            importExcelList.removeAll(keeperImportExcels);
        }

        return null;
    }
}
