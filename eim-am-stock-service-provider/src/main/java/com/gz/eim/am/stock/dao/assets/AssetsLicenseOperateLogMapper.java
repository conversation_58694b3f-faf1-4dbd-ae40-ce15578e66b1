package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.StockAssetsLicense;
import com.gz.eim.am.stock.entity.StockAssetsLicenseOperateLog;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 4/9/21 11:10 上午
 * @description
 */
public interface AssetsLicenseOperateLogMapper {

    /**
     * 批量新增
     * @param stockAssetsLicenseOperateLogList
     * @return
     */
    Integer batchInsert(List<StockAssetsLicenseOperateLog> stockAssetsLicenseOperateLogList);
}
