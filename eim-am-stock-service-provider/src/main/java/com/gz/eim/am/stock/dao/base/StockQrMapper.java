package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockQr;
import com.gz.eim.am.stock.entity.StockQrExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockQrMapper {
    long countByExample(StockQrExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockQr record);

    int insertSelective(StockQr record);

    List<StockQr> selectByExample(StockQrExample example);

    StockQr selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockQr record, @Param("example") StockQrExample example);

    int updateByExample(@Param("record") StockQr record, @Param("example") StockQrExample example);

    int updateByPrimaryKeySelective(StockQr record);

    int updateByPrimaryKey(StockQr record);
}