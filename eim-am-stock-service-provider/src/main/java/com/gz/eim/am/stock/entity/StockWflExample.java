package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockWflExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockWflExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("flow_id like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("flow_id not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNull() {
            addCriterion("biz_id is null");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNotNull() {
            addCriterion("biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizIdEqualTo(String value) {
            addCriterion("biz_id =", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotEqualTo(String value) {
            addCriterion("biz_id <>", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThan(String value) {
            addCriterion("biz_id >", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("biz_id >=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThan(String value) {
            addCriterion("biz_id <", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanOrEqualTo(String value) {
            addCriterion("biz_id <=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLike(String value) {
            addCriterion("biz_id like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotLike(String value) {
            addCriterion("biz_id not like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdIn(List<String> values) {
            addCriterion("biz_id in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotIn(List<String> values) {
            addCriterion("biz_id not in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdBetween(String value1, String value2) {
            addCriterion("biz_id between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotBetween(String value1, String value2) {
            addCriterion("biz_id not between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andLobNoIsNull() {
            addCriterion("lob_no is null");
            return (Criteria) this;
        }

        public Criteria andLobNoIsNotNull() {
            addCriterion("lob_no is not null");
            return (Criteria) this;
        }

        public Criteria andLobNoEqualTo(String value) {
            addCriterion("lob_no =", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoNotEqualTo(String value) {
            addCriterion("lob_no <>", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoGreaterThan(String value) {
            addCriterion("lob_no >", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoGreaterThanOrEqualTo(String value) {
            addCriterion("lob_no >=", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoLessThan(String value) {
            addCriterion("lob_no <", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoLessThanOrEqualTo(String value) {
            addCriterion("lob_no <=", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoLike(String value) {
            addCriterion("lob_no like", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoNotLike(String value) {
            addCriterion("lob_no not like", value, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoIn(List<String> values) {
            addCriterion("lob_no in", values, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoNotIn(List<String> values) {
            addCriterion("lob_no not in", values, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoBetween(String value1, String value2) {
            addCriterion("lob_no between", value1, value2, "lobNo");
            return (Criteria) this;
        }

        public Criteria andLobNoNotBetween(String value1, String value2) {
            addCriterion("lob_no not between", value1, value2, "lobNo");
            return (Criteria) this;
        }

        public Criteria andWflParamIsNull() {
            addCriterion("wfl_param is null");
            return (Criteria) this;
        }

        public Criteria andWflParamIsNotNull() {
            addCriterion("wfl_param is not null");
            return (Criteria) this;
        }

        public Criteria andWflParamEqualTo(String value) {
            addCriterion("wfl_param =", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamNotEqualTo(String value) {
            addCriterion("wfl_param <>", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamGreaterThan(String value) {
            addCriterion("wfl_param >", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamGreaterThanOrEqualTo(String value) {
            addCriterion("wfl_param >=", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamLessThan(String value) {
            addCriterion("wfl_param <", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamLessThanOrEqualTo(String value) {
            addCriterion("wfl_param <=", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamLike(String value) {
            addCriterion("wfl_param like", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamNotLike(String value) {
            addCriterion("wfl_param not like", value, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamIn(List<String> values) {
            addCriterion("wfl_param in", values, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamNotIn(List<String> values) {
            addCriterion("wfl_param not in", values, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamBetween(String value1, String value2) {
            addCriterion("wfl_param between", value1, value2, "wflParam");
            return (Criteria) this;
        }

        public Criteria andWflParamNotBetween(String value1, String value2) {
            addCriterion("wfl_param not between", value1, value2, "wflParam");
            return (Criteria) this;
        }

        public Criteria andInitiateResultIsNull() {
            addCriterion("initiate_result is null");
            return (Criteria) this;
        }

        public Criteria andInitiateResultIsNotNull() {
            addCriterion("initiate_result is not null");
            return (Criteria) this;
        }

        public Criteria andInitiateResultEqualTo(Integer value) {
            addCriterion("initiate_result =", value, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultNotEqualTo(Integer value) {
            addCriterion("initiate_result <>", value, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultGreaterThan(Integer value) {
            addCriterion("initiate_result >", value, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("initiate_result >=", value, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultLessThan(Integer value) {
            addCriterion("initiate_result <", value, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultLessThanOrEqualTo(Integer value) {
            addCriterion("initiate_result <=", value, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultIn(List<Integer> values) {
            addCriterion("initiate_result in", values, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultNotIn(List<Integer> values) {
            addCriterion("initiate_result not in", values, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultBetween(Integer value1, Integer value2) {
            addCriterion("initiate_result between", value1, value2, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andInitiateResultNotBetween(Integer value1, Integer value2) {
            addCriterion("initiate_result not between", value1, value2, "initiateResult");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIsNull() {
            addCriterion("error_message is null");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIsNotNull() {
            addCriterion("error_message is not null");
            return (Criteria) this;
        }

        public Criteria andErrorMessageEqualTo(String value) {
            addCriterion("error_message =", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotEqualTo(String value) {
            addCriterion("error_message <>", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageGreaterThan(String value) {
            addCriterion("error_message >", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageGreaterThanOrEqualTo(String value) {
            addCriterion("error_message >=", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLessThan(String value) {
            addCriterion("error_message <", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLessThanOrEqualTo(String value) {
            addCriterion("error_message <=", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLike(String value) {
            addCriterion("error_message like", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotLike(String value) {
            addCriterion("error_message not like", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIn(List<String> values) {
            addCriterion("error_message in", values, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotIn(List<String> values) {
            addCriterion("error_message not in", values, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageBetween(String value1, String value2) {
            addCriterion("error_message between", value1, value2, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotBetween(String value1, String value2) {
            addCriterion("error_message not between", value1, value2, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andApprovedResultIsNull() {
            addCriterion("approved_result is null");
            return (Criteria) this;
        }

        public Criteria andApprovedResultIsNotNull() {
            addCriterion("approved_result is not null");
            return (Criteria) this;
        }

        public Criteria andApprovedResultEqualTo(Integer value) {
            addCriterion("approved_result =", value, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultNotEqualTo(Integer value) {
            addCriterion("approved_result <>", value, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultGreaterThan(Integer value) {
            addCriterion("approved_result >", value, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultGreaterThanOrEqualTo(Integer value) {
            addCriterion("approved_result >=", value, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultLessThan(Integer value) {
            addCriterion("approved_result <", value, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultLessThanOrEqualTo(Integer value) {
            addCriterion("approved_result <=", value, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultIn(List<Integer> values) {
            addCriterion("approved_result in", values, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultNotIn(List<Integer> values) {
            addCriterion("approved_result not in", values, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultBetween(Integer value1, Integer value2) {
            addCriterion("approved_result between", value1, value2, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedResultNotBetween(Integer value1, Integer value2) {
            addCriterion("approved_result not between", value1, value2, "approvedResult");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeIsNull() {
            addCriterion("approved_time is null");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeIsNotNull() {
            addCriterion("approved_time is not null");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeEqualTo(Date value) {
            addCriterion("approved_time =", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeNotEqualTo(Date value) {
            addCriterion("approved_time <>", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeGreaterThan(Date value) {
            addCriterion("approved_time >", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approved_time >=", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeLessThan(Date value) {
            addCriterion("approved_time <", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeLessThanOrEqualTo(Date value) {
            addCriterion("approved_time <=", value, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeIn(List<Date> values) {
            addCriterion("approved_time in", values, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeNotIn(List<Date> values) {
            addCriterion("approved_time not in", values, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeBetween(Date value1, Date value2) {
            addCriterion("approved_time between", value1, value2, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andApprovedTimeNotBetween(Date value1, Date value2) {
            addCriterion("approved_time not between", value1, value2, "approvedTime");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}