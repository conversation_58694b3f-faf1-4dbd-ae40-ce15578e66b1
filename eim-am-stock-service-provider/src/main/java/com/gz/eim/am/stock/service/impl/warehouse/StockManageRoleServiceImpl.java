package com.gz.eim.am.stock.service.impl.warehouse;

import com.gz.eim.am.stock.dao.base.StockManageRoleMapper;
import com.gz.eim.am.stock.dao.warehouse.ManageRoleMapper;
import com.gz.eim.am.stock.entity.StockManageRole;
import com.gz.eim.am.stock.service.warehouse.StockManageRoleService;
import com.gz.eim.am.stock.util.em.ManageRoleEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 9:28
 */
@Service
public class StockManageRoleServiceImpl implements StockManageRoleService {

    @Autowired
    private StockManageRoleMapper stockManageRoleMapper;

    @Autowired
    private ManageRoleMapper manageRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long initWarehouseManageRole(String warehouseCode,String warehouseName,String createUser) {
        if(StringUtils.isBlank(warehouseCode)){
            return null;
        }
        StockManageRole manageRole = new StockManageRole();
        manageRole.setRoleName(warehouseName + "(" + warehouseCode + ")" + "-RW管理员");
        manageRole.setRoleType(ManageRoleEnum.Type.MANAGER.getCode ());
        manageRole.setStatus(ManageRoleEnum.Status.NORMAL.getStatus());
        manageRole.setWarehouseCode(warehouseCode);
        manageRole.setCreatedBy(createUser);
        manageRole.setCreatedAt(new Date());
        manageRole.setUpdatedBy(manageRole.getCreatedBy());
        manageRole.setUpdatedAt(manageRole.getCreatedAt());

        this.manageRoleMapper.insert(manageRole);
        return manageRole.getRoleId();
    }
}
