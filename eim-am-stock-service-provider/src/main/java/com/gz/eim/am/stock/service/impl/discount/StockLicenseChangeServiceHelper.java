package com.gz.eim.am.stock.service.impl.discount;

import com.fuu.eim.support.jwt.JwtUser;
import com.guazi.gzencrypt.common.constants.CryptType;
import com.gz.eim.am.common.enums.EncryptModuleEnum;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.common.util.EncryptsPlus;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.MetaDataConstants;
import com.gz.eim.am.stock.dto.request.discount.StockLicenseChangeItemsReqDTO;
import com.gz.eim.am.stock.dto.request.discount.StockLicenseChangeReqDTO;
import com.gz.eim.am.stock.dto.response.discount.StockLicenseChangeItemsRespDTO;
import com.gz.eim.am.stock.dto.response.discount.StockLicenseChangeRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.service.wfl.WflService;
import com.gz.eim.am.stock.util.common.WorkFlowUtil;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.FlowCodeEnum;
import com.gz.eim.am.stock.util.em.StockAssetsLicenseChangeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.jar.JarEntry;

/**
 * @Author: wangjing67
 * @Date: 3/31/21 2:20 下午
 * @description
 */
@Component
public class StockLicenseChangeServiceHelper {

    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Lazy
    @Autowired
    private WflService wflService;

    @Autowired
    private StockSuppliesService stockSuppliesService;

    @Autowired
    private StockAssetsService stockAssetsService;



    /**
     * 推送消息到流程平台
     *
     * @param changeNo
     */
    public void sendToWfl(String changeNo,FlowCodeEnum flowCodeEnum) {
        Map<String, Object> extendInfoMap = new HashMap<>(CommonConstant.NUMBER_ONE);

        extendInfoMap.put("changeNo",changeNo);
        List<Map<String, Object>> dataItemListExtendList = new ArrayList<>(CommonConstant.NUMBER_ONE);
        dataItemListExtendList.add(extendInfoMap);
        WflInfo wflInfo = WorkFlowUtil.createWlfInfo(flowCodeEnum, changeNo, null);
        //异步发起工作流发
        wflService.beginAct(wflInfo);
    }


    /**
     * 设置行相关信息
     *
     * @param stockLicenseChangeItemsRespDTOList
     * @return
     * @throws Exception
     */
    protected void   settingListRelationValue(StockLicenseChangeRespDTO stockLicenseChangeRespDTO,List<StockLicenseChangeItemsRespDTO> stockLicenseChangeItemsRespDTOList) throws Exception {
        List<String> createdByList = new ArrayList<>();
        stockLicenseChangeItemsRespDTOList.forEach(stockLicenseChangeItemsRespDTO -> {
            createdByList.add(stockLicenseChangeItemsRespDTO.getCreatedBy());
            //变更类型为法人信息时
            if (StockAssetsLicenseChangeEnum.ChangeType.LEGAL_PERSON.getValue().equals(stockLicenseChangeItemsRespDTO.getChangeType())) {
                if (StringUtils.isNotBlank(stockLicenseChangeItemsRespDTO.getBeforeValue())) {
                    createdByList.add(stockLicenseChangeItemsRespDTO.getBeforeValue());
                }
                if (StringUtils.isNotBlank(stockLicenseChangeItemsRespDTO.getAfterValue())) {
                    createdByList.add(stockLicenseChangeItemsRespDTO.getAfterValue());
                }
            }
        });
        Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(createdByList);
        Map<Integer, String> changeTypeMap = StockAssetsLicenseChangeEnum.changeTypeMap;
        Map<Integer, String> changeStatusMap = StockAssetsLicenseChangeEnum.changeStatusMap;

        for (StockLicenseChangeItemsRespDTO stockLicenseChangeItemsRespDTO : stockLicenseChangeItemsRespDTOList) {
            stockLicenseChangeItemsRespDTO.setChangeStatusName(changeStatusMap.get(stockLicenseChangeItemsRespDTO.getChangeStatus()));
            stockLicenseChangeItemsRespDTO.setChangeTypeName(changeTypeMap.get(stockLicenseChangeItemsRespDTO.getChangeType()));
            //创建人
            SysUser sysUser = stringSysUserMap.get(stockLicenseChangeItemsRespDTO.getCreatedBy());
            if (null != sysUser) {
                stockLicenseChangeItemsRespDTO.setCreatedByName(sysUser.getName());
            }
            //变更类型为法人信息时
            if (StockAssetsLicenseChangeEnum.ChangeType.LEGAL_PERSON.getValue().equals(stockLicenseChangeItemsRespDTO.getChangeType())) {
                if (StringUtils.isNotBlank(stockLicenseChangeItemsRespDTO.getBeforeValue())) {
                    SysUser beforeSysUser = stringSysUserMap.get(stockLicenseChangeItemsRespDTO.getBeforeValue());
                    if (null != beforeSysUser) {
                        stockLicenseChangeItemsRespDTO.setBeforeValueName(beforeSysUser.getName());
                    }
                }
                if (StringUtils.isNotBlank(stockLicenseChangeItemsRespDTO.getAfterValue())) {
                    SysUser afterSysUser = stringSysUserMap.get(stockLicenseChangeItemsRespDTO.getAfterValue());
                    if (null != afterSysUser) {
                        stockLicenseChangeItemsRespDTO.setAfterValueName(afterSysUser.getName());
                    }
                }
                //处理法人身份证
                if (StringUtils.isNotBlank(stockLicenseChangeItemsRespDTO.getBeforeIdCard())) {
                    String encrypt = EncryptsPlus.decrypt(EncryptModuleEnum.DATA.value, CryptType.ID_CARD, stockLicenseChangeItemsRespDTO.getBeforeIdCard());
                    stockLicenseChangeItemsRespDTO.setBeforeIdCard(encrypt);
                }
                if (StringUtils.isNotBlank(stockLicenseChangeItemsRespDTO.getAfterIdCard())) {
                    String encrypt = EncryptsPlus.decrypt(EncryptModuleEnum.DATA.value, CryptType.ID_CARD, stockLicenseChangeItemsRespDTO.getAfterIdCard());
                    stockLicenseChangeItemsRespDTO.setAfterIdCard(encrypt);
                }
            }
            if (null != stockLicenseChangeRespDTO) {
                //物料编码
                stockLicenseChangeItemsRespDTO.setSuppliesCode(stockLicenseChangeRespDTO.getSuppliesCode());
                //物料名称
                stockLicenseChangeItemsRespDTO.setSuppliesName(stockLicenseChangeRespDTO.getSuppliesName());
            }
        }
    }

    /**
     * 设置相关参数值
     *
     * @param stockLicenseChangeList
     * @return
     * @throws Exception
     */
    protected List<StockLicenseChangeRespDTO> settingRelationValue(List<StockLicenseChange> stockLicenseChangeList) throws Exception {
        List<StockLicenseChangeRespDTO> stockLicenseChangeRespDTOList = new ArrayList<>();

        //数据准备
        List<String> createdByList = new ArrayList<>();
//        List<String> warehouseCodeList = new ArrayList<>();

        stockLicenseChangeList.stream().forEach(stockLicenseChange -> {
            createdByList.add(stockLicenseChange.getCreatedBy());
//            warehouseCodeList.add(stockLicenseChange.getWarehouseCode());

        });

//        Map<String, StockAssets> stringStockAssetsMap = stockAssetsService.selectAssetsMapByCodes(warehouseCodeList);
//        Map<String, WarehouseRespDTO> stringStockWarehouseMap = stockWarehouseService.selectWarehouseDetailMapByCode(warehouseCodeList);
//        List<StockWarehouseBase> stockWarehouseBaseList = stockWarehouseBaseService.getWarehouseBaseListByWareCodeList(warehouseCodeList);
//        Map<String, StockWarehouseBase> stringStockWarehouseBaseMap = new HashMap<>(16);
//        List<String> linkmanList = new ArrayList<>();
//        stockWarehouseBaseList.stream().forEach(stockWarehouseBase -> {
//            stringStockWarehouseBaseMap.put(stockWarehouseBase.getWarehouseCode(), stockWarehouseBase);
//            linkmanList.add(stockWarehouseBase.getLinkman());
//        });

//        createdByList.addAll(linkmanList);
        Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(createdByList);
        Map<Integer, String> changeStatusMap = StockAssetsLicenseChangeEnum.changeStatusMap;
        Map<Integer, String> assetsStatusTypeMap = AssetsEnum.assetsStatusTypeMap;

        for (StockLicenseChange stockLicenseChange : stockLicenseChangeList) {
            StockLicenseChangeRespDTO stockLicenseChangeRespDTO = ConvertUtil.convertToType(StockLicenseChangeRespDTO.class, stockLicenseChange);
            //创建人
            SysUser sysUser = stringSysUserMap.get(stockLicenseChangeRespDTO.getCreatedBy());
            if (null != sysUser) {
                stockLicenseChangeRespDTO.setCreatedByName(sysUser.getName());
            }
            //单据状态
            stockLicenseChangeRespDTO.setChangeStatusName(changeStatusMap.get(stockLicenseChangeRespDTO.getChangeStatus()));

//            //仓库名称
//            WarehouseRespDTO warehouseRespDTO = stringStockWarehouseMap.get(stockLicenseChangeRespDTO.getWarehouseCode());
//            if (null != warehouseRespDTO) {
//                stockLicenseChangeRespDTO.setWarehouseCodeName(warehouseRespDTO.getName());
//            }
//            //仓库管理员
//            String linkman = stringStockWarehouseBaseMap.get(stockLicenseChangeRespDTO.getWarehouseCode()).getLinkman();
//            if (StringUtils.isNotBlank(linkman)) {
//                SysUser linkSysUser = stringSysUserMap.get(linkman);
//                if (null != sysUser) {
//                    stockLicenseChangeRespDTO.setLinkmanName(linkSysUser.getName());
//                }
//            }
//            //执照状态
//            StockAssets stockAssets = stringStockAssetsMap.get(stockLicenseChangeRespDTO.getAssetsCode());
//            if (null != stockAssets) {
//                stockLicenseChangeRespDTO.setAssetsStatus(stockAssets.getStatus());
//                stockLicenseChangeRespDTO.setAssetsStatusName(assetsStatusTypeMap.get(stockAssets.getStatus()));
//            }
            stockLicenseChangeRespDTOList.add(stockLicenseChangeRespDTO);
        }
        return stockLicenseChangeRespDTOList;
    }


    /**
     * 参数校验
     *
     * @param stockLicenseChangeReqDTO
     * @return
     */
    protected String checkParam(StockLicenseChangeReqDTO stockLicenseChangeReqDTO, JwtUser jwtUser) throws Exception {
        //校验当前请求人工号是否在配置可执行变更申请人工号中
        List<String> employeeCodes = ambaseCommonService.selectMetaDataByMdKey(MetaDataConstants.STOCK_LICENSE_Module, MetaDataConstants.STOCK_LICENSE_MDKEY);
        if (!CollectionUtils.isEmpty(employeeCodes)) {
            if (!employeeCodes.contains(jwtUser.getEmployeeCode())) {
                return "您没有执照变更申请的权限";
            }
        }
        if (StringUtils.isBlank(stockLicenseChangeReqDTO.getSuppliesCode())) {
            return "执照物料编码不能为空";
        }
        if (StringUtils.isBlank(stockLicenseChangeReqDTO.getSuppliesName())) {
            return "执照名称不能为空";
        }
        //增加校验 判断提交的执照变更申请物料下是否有资产信息  如果没有则给出提示
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssetsBySuppliesCode(stockLicenseChangeReqDTO.getSuppliesCode());
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            return "没有可被变更的执照信息";
        }
        if (null == stockLicenseChangeReqDTO.getChangeDate()) {
            return "需求变更时间不能为空";
        }
        if (StringUtils.isBlank(stockLicenseChangeReqDTO.getChangeReason()) || stockLicenseChangeReqDTO.getChangeReason().length() > CommonConstant.NUMBER_500) {
            return "变更原因不能为空且长度不能超过500个字符";
        }
        List<StockSupplies> stockSuppliesList = stockSuppliesService.getEnableSuppliesList(stockLicenseChangeReqDTO.getSuppliesCode());
        if (org.springframework.util.CollectionUtils.isEmpty(stockSuppliesList)) {
            return  "需要变更物料编码不存在或已禁用;";
        }
//        //查询执照信息
//        StockAssets stockAssets = stockAssetsService.selectAssetsByCode(stockLicenseChangeReqDTO.getAssetsCode());
//        if (null == stockAssets) {
//            return "需要变更的执照信息不存在，不允许变更";
//        }
//        if (null != stockAssets.getStatus() && !AssetsEnum.statusType.IDLE.getValue().equals(stockAssets.getStatus())) {
//            return "需要变更的执照信息当前状态不在库，不允许变更";
//        }
//        //设置仓库信息
//        stockLicenseChangeReqDTO.setWarehouseCode(stockAssets.getWarehouseCode());

        if (CollectionUtils.isEmpty(stockLicenseChangeReqDTO.getStockLicenseChangeItemsReqDTOList())) {
            return "应至少包含一行变更行信息";
        }
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < stockLicenseChangeReqDTO.getStockLicenseChangeItemsReqDTOList().size(); i++) {
            StockLicenseChangeItemsReqDTO stockLicenseChangeItemsReqDTO = stockLicenseChangeReqDTO.getStockLicenseChangeItemsReqDTOList().get(i);

            if (null == stockLicenseChangeItemsReqDTO.getChangeType()) {
                stringBuffer.append("第" + (i + 1) + "变更类型不能为空");
            }
            if (null != stockLicenseChangeItemsReqDTO.getChangeType()) {
                if (StockAssetsLicenseChangeEnum.ChangeType.LEGAL_PERSON.getValue().equals(stockLicenseChangeItemsReqDTO.getChangeType())) {
                    if (StringUtils.isBlank(stockLicenseChangeItemsReqDTO.getAfterIdCard())) {
                        stringBuffer.append("第" + (i + 1) + "变更类型为法人信息时，变更后的法人身份证信息不能为空");
                    }
                    if (CollectionUtils.isEmpty(stockLicenseChangeReqDTO.getAttachList())) {
                        stringBuffer.append("第" + (i + 1) + "变更类型为法人信息时，变更后的法人身份证附件信息不能为空");
                    }
                }
            }
            if (StringUtils.isBlank(stockLicenseChangeItemsReqDTO.getAfterValue())) {
                stringBuffer.append("第" + (i + 1) + "变更后的值不能为空");
            }
            if (StringUtils.isNotBlank(stockLicenseChangeItemsReqDTO.getAfterValue())) {
                if (StockAssetsLicenseChangeEnum.ChangeType.LEGAL_PERSON.getValue().equals(stockLicenseChangeItemsReqDTO.getChangeType())) {
                    //查询变更后法人信息是否存在
                    List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(Arrays.asList(stockLicenseChangeItemsReqDTO.getAfterValue()));
                    if (CollectionUtils.isEmpty(sysUserList)) {
                        stringBuffer.append("第" + (i + 1) + "变更后的法人信息不存在");
                    }
                }
            }
        }
        if (stringBuffer.length() > 0) {
            return stringBuffer.toString();
        }
        return null;
    }

}
