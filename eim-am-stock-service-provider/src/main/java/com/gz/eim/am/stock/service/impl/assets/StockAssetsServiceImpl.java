package com.gz.eim.am.stock.service.impl.assets;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.activity.MessageUtil;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.common.constant.MetaDataKeyConstants;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.constant.PropertyConstants;
import com.gz.eim.am.stock.constant.SqlConstant;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.dao.base.*;
import com.gz.eim.am.stock.dto.request.PageReqDTO;
import com.gz.eim.am.stock.dto.request.ambase.CuxGetfadetailTblReqDTO;
import com.gz.eim.am.stock.dto.request.assets.*;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.assets.*;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.*;
import com.gz.eim.am.stock.entity.vo.*;
import com.gz.eim.am.stock.entity.vo.download.ExportAssetsEntity;
import com.gz.eim.am.stock.entity.vo.download.ExportAssetsInventoryRecordEntity;
import com.gz.eim.am.stock.entity.vo.download.ExportAssetsScrapRecordEntity;
import com.gz.eim.am.stock.interceptor.DocTypeAspect;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.*;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanLineService;
import com.gz.eim.am.stock.service.qr.StockQrService;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.common.*;
import com.gz.eim.am.stock.util.em.*;
import com.gz.eim.am.stock.util.task.StockThreadPool;
import com.gz.eim.app.cns.dto.response.qrcode.BatchQrCodeRespDTO;
import com.gz.eim.plt.print.api.PrintApi;
import com.gz.eim.plt.print.dto.request.PrintTaskReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-12-11 PM 7:04
 */
@Slf4j
@Service
public class StockAssetsServiceImpl implements StockAssetsService {

    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;
    @Autowired
    private StockAssetsMapper stockAssetsMapper;
    @Autowired
    private StockAssetsOperationLogMapper operationLogMapper;
    @Autowired
    private AssetsMapper assetsMapper;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockWarehouseService warehouseService;
    @Lazy
    @Autowired
    private StockAssetsOperationLogService operationLogService;
    @Autowired
    private StockWarehouseBaseService warehouseBaseService;
    @Autowired
    private FileUtil fileUtil;
    @Autowired
    private PrintApi printApi;
    @Autowired
    private PrintUtil printUtil;
    @Autowired
    private StockQrService stockQrService;
    @Autowired
    private StockAssetCodeService stockAssetCodeService;
    @Autowired
    private StockAssetsLicenseService stockAssetsLicenseService;
    @Autowired
    private StockSuppliesService stockSuppliesService;
    @Autowired
    private StockAssetsDocumentMapper stockAssetsDocumentMapper;
    @Autowired
    private StockAssetsDocumentDetailMapper stockAssetsDocumentDetailMapper;



    /**
     * 导出Excel：根据资产报废单查询资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    @Override
    public ResponseData downLoadAssetsByScrapExcel(AssetsSearchDTO assetsSearchDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<StockAssetsInfo> stockAssetsList = assetsMapper.selectAssetsByScrap(assetsSearchDTO);
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            return ResponseData.createFailResult("导出数量为空");
        }
        if (stockAssetsList.size() > CommonConstant.EXCEL_EXPORT_MAX_COUNT) {
            return ResponseData.createFailResult("导出数量超过10000条, 无法导出");
        }
        // 赋值资产状态和使用状态
        stockAssetsList.forEach(stockAssetsInfo -> {
            stockAssetsInfo.setStatusName(AssetsEnum.assetsStatusTypeMap.get(stockAssetsInfo.getStatus()));
            stockAssetsInfo.setConditionsName(AssetsEnum.conditionsMap.get(stockAssetsInfo.getConditions()));
            stockAssetsInfo.setScrapReasonName(AssetsEnum.scrapReasonMap.get(stockAssetsInfo.getScrapReason()));
        });
        List<ExportAssetsScrapRecordEntity> modelList = ConvertUtil.convertToType(ExportAssetsScrapRecordEntity.class, stockAssetsList);
        final String fileName = "资产报废报表_" + DateUtils.dateFormat(new Date(), DateUtils.HOUR_PATTERN) + ".xlsx";
        ExcelUtil.createExcelWithBuffer(modelList, fileName, request, response);
        return ResponseData.createSuccessResult();
    }

    /**
     * 根据资产报废单查询资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    @Override
    public ResponseData selectAssetsByScrap(AssetsSearchDTO assetsSearchDTO) {
        Long count = assetsMapper.countAssetsByScrap(assetsSearchDTO);
        // 初始化分页信息
        assetsSearchDTO.initPageParam();
        List<StockAssetsInfo> stockAssetsList = assetsMapper.selectAssetsByScrap(assetsSearchDTO);
        // 赋值资产状态和使用状态
        stockAssetsList.forEach(stockAssetsInfo -> {
            stockAssetsInfo.setStatusName(AssetsEnum.assetsStatusTypeMap.get(stockAssetsInfo.getStatus()));
            stockAssetsInfo.setConditionsName(AssetsEnum.conditionsMap.get(stockAssetsInfo.getConditions()));
            stockAssetsInfo.setScrapReasonName(AssetsEnum.scrapReasonMap.get(stockAssetsInfo.getScrapReason()));
        });
        // 更换实体类
        List<AssetsDTO> assetsDTOList = ConvertUtil.convertToType(AssetsDTO.class, stockAssetsList);
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        pageRespDTO.setPageNum(assetsSearchDTO.getPageNum());
        pageRespDTO.setPageSize(assetsSearchDTO.getPageSize());
        pageRespDTO.setCount(count);
        pageRespDTO.setData(assetsDTOList);
        return pageRespDTO;
    }

    /**
     * 导出Excel：根据资产入库申请单查询资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    @Override
    public ResponseData downLoadAssetsByInventoryPlanExcel(AssetsSearchDTO assetsSearchDTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 赋予初始值（此处规定是固定资产入库，且为已经入库的资产）
        assetsSearchDTO.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.ASSET_PURCHASE.getCode());
        assetsSearchDTO.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
        List<StockAssetsInfo> stockAssetsList = assetsMapper.selectAssetsByInventoryPlan(assetsSearchDTO);
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            return ResponseData.createFailResult("导出数量为空");
        }
        if (stockAssetsList.size() > CommonConstant.EXCEL_EXPORT_MAX_COUNT) {
            return ResponseData.createFailResult("导出数量超过10000条, 无法导出");
        }
        // 赋值资产状态
        stockAssetsList.forEach(stockAssetsInfo -> {
            stockAssetsInfo.setStatusName(AssetsEnum.assetsStatusTypeMap.get(stockAssetsInfo.getStatus()));
        });
        List<ExportAssetsInventoryRecordEntity> modelList = ConvertUtil.convertToType(ExportAssetsInventoryRecordEntity.class, stockAssetsList);
        final String fileName = "资产新增报表_" + DateUtils.dateFormat(new Date(), DateUtils.HOUR_PATTERN) + ".xlsx";
        ExcelUtil.createExcelWithBuffer(modelList, fileName, request, response);
        return ResponseData.createSuccessResult();
    }

    /**
     * 根据资产入库申请单查询资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    @Override
    public ResponseData selectAssetsByInventoryPlan(AssetsSearchDTO assetsSearchDTO) {
        // 赋予初始值（此处规定是固定资产入库，且为已经入库的资产）
        assetsSearchDTO.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.ASSET_PURCHASE.getCode());
        assetsSearchDTO.setInStockStatus(InventoryInPlanLineAssetsEnum.Status.ALREADY_IN.getCode());
        // 初始化分页信息
        assetsSearchDTO.initPageParam();
        Long count = assetsMapper.countAssetsByInventoryPlan(assetsSearchDTO);
        List<StockAssetsInfo> stockAssetsList = assetsMapper.selectAssetsByInventoryPlan(assetsSearchDTO);
        // 赋值资产状态
        stockAssetsList.forEach(stockAssetsInfo -> {
            stockAssetsInfo.setStatusName(AssetsEnum.assetsStatusTypeMap.get(stockAssetsInfo.getStatus()));
        });
        // 更换实体类
        List<AssetsDTO> assetsDTOList = ConvertUtil.convertToType(AssetsDTO.class, stockAssetsList);

        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        pageRespDTO.setData(assetsDTOList);
        pageRespDTO.setCount(count);
        pageRespDTO.setPageNum(assetsSearchDTO.getStartNum());
        pageRespDTO.setPageSize(assetsSearchDTO.getPageSize());
        return pageRespDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData saveAssets(final AssetsDTO assetsDTO) {

        final List<String> errs = checkInputAssetsParams(assetsDTO);
        if (errs.size() > 0) {
            return ResponseData.createFailResult(StringUtils.join(errs, ","));
        }
        final StockAssets assets = this.selectAssetsByCode(assetsDTO.getAssetsCode());
        if (assets == null) {
            return ResponseData.createFailResult("资产编码重复");
        }
        BeanUtils.copyProperties(assetsDTO, assets);
        //待定字段保留处理
        //暂时默认没有子资产
        assets.setHasSub(0);
        // 暂时公司编码
        assets.setCompanyCode("-");
        //是否定时维护 暂时默认
        assets.setRegularMaintain(0);
        //assets.setPurchaseType(1); //购置方式：1.采购(默认) 2.租赁 3.代管
        stockAssetsMapper.insertSelective(assets);

        saveOperationLog(assets, assetsDTO.getOperationType());

        return ResponseData.createSuccessResult();
    }

    private void saveOperationLog(final StockAssets assets, final Integer operationType) {
        final StockAssetsOperationLog assetsOperationLog = new StockAssetsOperationLog();
        BeanUtils.copyProperties(assets, assetsOperationLog);
        assetsOperationLog.setOperationType(operationType);
        operationLogMapper.insertSelective(assetsOperationLog);
    }

    /**
     * 可更新业务字段：新资产持有人、资产管理员、资产类别、资产年限
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateAssets(final AssetsDTO assetsDTO) {
        String errMsg = checkUpdateAssetsParams(assetsDTO);
        if (StringUtils.isNotBlank(errMsg)) {
            return ResponseData.createFailResult(errMsg);
        }
        final StockAssets assets = stockAssetsMapper.selectByPrimaryKey(assetsDTO.getAssetsId());
        if (assets == null) {
            return ResponseData.createFailResult("更新资产信息不存在");
        }
        //如果资产领用人或者领用人位置发生了变更，那么自动生成转移单据
        if (!compare(assets.getHolder(), assetsDTO.getHolder()) || !compare(assets.getHolderAddress(), assetsDTO.getHolderAddress())) {
            //准备入库数据
            StockAssetsDocument stockAssetsDocument = new StockAssetsDocument();
            StockAssetsDocumentDetail stockAssetsDocumentDetail = new StockAssetsDocumentDetail();
            prepareSaveDbBeanDTO(assetsDTO, assets, stockAssetsDocument, stockAssetsDocumentDetail);
            //保存转移头信息
            stockAssetsDocumentMapper.insert(stockAssetsDocument);
            //保存转移行信息
            stockAssetsDocumentDetailMapper.insert(stockAssetsDocumentDetail);
        }

        //持有者
        assets.setHolder(assetsDTO.getHolder());
        if (StringUtils.isNotEmpty(assetsDTO.getHolder())) {
            SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(assetsDTO.getHolder());
            if (null != sysUserBasicInfo) {
                assets.setCostDept(sysUserBasicInfo.getDeptId());
                assets.setHolderDept(sysUserBasicInfo.getDeptId());
            }
        }

        //资产管理员
        assets.setAssetsKeeper(assetsDTO.getAssetsKeeper());
        //分类
        assets.setCategory(assetsDTO.getCategory());
        //使用年限
        if (null != assetsDTO.getUseYearLimit()) {
            assets.setUseYearLimit(assetsDTO.getUseYearLimit());
        } else {
            assets.setUseYearLimit(CommonConstant.NUMBER_ZERO);
        }

        if (null != assetsDTO.getRemark()) {
            assets.setRemark(assetsDTO.getRemark());
        } else {
            assets.setRemark("");
        }
        //assets.setStatus(assetsDTO.getStatus()); //状态

        assets.setUpdatedAt(assetsDTO.getUpdatedAt());
        assets.setUpdatedBy(assetsDTO.getUpdatedBy());
        assets.setHolderAddress(assetsDTO.getHolderAddress());
        stockAssetsMapper.updateByPrimaryKeySelective(assets);

        final StockAssetsOperationLog assetsOperationLog = new StockAssetsOperationLog();
        BeanUtils.copyProperties(assets, assetsOperationLog);
        assetsOperationLog.setOperationType(assetsDTO.getOperationType());
        operationLogMapper.insertSelective(assetsOperationLog);

        final AssetsDTO newAssetsDTO = new AssetsDTO();
        BeanUtils.copyProperties(assets, newAssetsDTO);
        final ResponseData<AssetsDTO> responseData = ResponseData.createSuccessResult(newAssetsDTO);
        return responseData;
    }

    public static String checkUpdateAssetsParams(final AssetsDTO assetsDTO) {
        if (assetsDTO.getAssetsId() == null) {
            return "资产id不能为空";
        }
        if (assetsDTO.getOperationType() == null) {
            return "修改类型不能为空";
        }
        return null;
    }

    public static List<String> checkInputAssetsParams(final AssetsDTO assetsDTO) {
        List<String> errList = new ArrayList<>();
        if (null == assetsDTO) {
            errList.add("无参数输入");
            return errList;
        }
        if (null == assetsDTO.getOperationType()) {
            errList.add("修改类型不能为空");
        }
        if (StringUtils.isBlank(assetsDTO.getAssetsCode()) || assetsDTO.getAssetsCode().length() > SqlConstant.VARCHAR_LENGTH_SIXTH_FOUR) {
            errList.add("资产编码不能为空，长度不能超过64个字符");
        }
        if (StringUtils.isBlank(assetsDTO.getAssetsName()) || assetsDTO.getAssetsName().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("资产名称不能为空，长度不能超过255个字符");
        }
        if (StringUtils.isBlank(assetsDTO.getSuppliesCode()) || assetsDTO.getSuppliesCode().length() > SqlConstant.VARCHAR_LENGTH_SIXTH_FOUR) {
            errList.add("物料编码不能为空，长度不能超过64个字符");
        }
        if (StringUtils.isBlank(assetsDTO.getWarehouseCode()) || assetsDTO.getWarehouseCode().length() > SqlConstant.VARCHAR_LENGTH_SIXTH_FOUR) {
            errList.add("仓库编码不能为空，长度不能超过64个字符");
        }
        if (StringUtils.isBlank(assetsDTO.getCategory()) || assetsDTO.getCategory().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("资产分类不能为空，长度不能超过255个字符");
        }
        if (StringUtils.isBlank(assetsDTO.getCostDept()) || assetsDTO.getCostDept().length() > SqlConstant.VARCHAR_LENGTH_SIXTH_FOUR) {
            errList.add("费用部门不能为空，长度不能超过64个字符");
        }
        if (null == assetsDTO.getStatus()) {
            errList.add("资产状态不能为空");
        }
        if (null != assetsDTO.getUseYearLimit() && assetsDTO.getUseYearLimit() <= CommonConstant.NUMBER_ZERO) {
            errList.add("使用年限不能小于等于0个月");
        }
        if (null == assetsDTO.getPurchaseType()) {
            errList.add("购置方式不能为空");
        }
        if (StringUtils.isNotBlank(assetsDTO.getAssetsPic()) && assetsDTO.getAssetsPic().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("图片URL长度不能超过255个字符");
        }
        if (null == assetsDTO.getStorageTime()) {
            errList.add("入库日期不能为空");
        }
        if (StringUtils.isBlank(assetsDTO.getAssetsKeeper())) {
            errList.add("资产管理员不能为空");
        }
        if (StringUtils.isNotBlank(assetsDTO.getRemark()) && assetsDTO.getRemark().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("备注长度不能超过255个字符");
        }
        if (null == assetsDTO.getCreatedAt() || null == assetsDTO.getUpdatedAt()
                || StringUtils.isBlank(assetsDTO.getCreatedBy()) || StringUtils.isBlank(assetsDTO.getUpdatedBy())) {
            errList.add("创建人、创建时间、最近修改人、最近修改时间不能为空");
        }
        return errList;
    }

    @Override
    public List<StockAssets> selectAssetsList(final AssetsSearchDTO assetsSearchDTO) {
        return this.getList(assetsSearchDTO);
    }

    @Override
    public ResponseData selectAssets(final AssetsSearchDTO assetsSearchDTO) {
        final PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        //根据单据获取仓库类型
        Integer docType = DocTypeAspect.threadLocal.get();
        if (docType != null) {
            List<Integer> warehouseTypes = ambaseCommonService.selectMetaDataByKey(MetaDataKeyConstants.DOCUMENT_WAREHOUSE, docType);
            if (CollectionUtils.isEmpty(warehouseTypes)) {
                return ResponseData.createFailResult("当前单据没有找到对应的仓库类型");
            }
            assetsSearchDTO.setWarehouseTypes(warehouseTypes);
        }
        //noPaging，Used for excel export.
        if (assetsSearchDTO.getNoPaging() != null && true == assetsSearchDTO.getNoPaging()) {
            //  final Long count = this.getCount (assetsSearchDTO);
            final Long count = assetsMapper.selectCountNewBySearchDTO(assetsSearchDTO);
            if (count == null) {
                pageRespDTO.setMessage(count == null || count.equals(CommonConstant.NUMBER_LONG_ZERO) ? "无数据导出" : "导出数量超过10000条");
                return pageRespDTO;
            }

            List<StockAssets> assetsList = new ArrayList<>();
            assetsSearchDTO.setPageNum(CommonConstant.NUMBER_ONE);
            assetsSearchDTO.setPageSize(CommonConstant.MAX_QUERY_COUNT);
            for (int i = CommonConstant.NUMBER_ZERO; i < count; i += CommonConstant.MAX_QUERY_COUNT) {
                assetsSearchDTO.setStartNum((assetsSearchDTO.getPageNum() - CommonConstant.NUMBER_ONE) * assetsSearchDTO.getPageSize());
                //  List<StockAssets> assetSubList = this.getList (assetsSearchDTO);
                List<StockAssets> assetSubList = assetsMapper.selectAssetsNewBySearchDTO(assetsSearchDTO);
                assetsList.addAll(assetSubList);
                assetsSearchDTO.setPageNum(assetsSearchDTO.getPageNum() + CommonConstant.NUMBER_ONE);
            }
            List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);

            assetsDTOList = settingRelationValues(assetsDTOList);
            pageRespDTO.setData(assetsDTOList);
            return pageRespDTO;
        }
        assetsSearchDTO.initPageDefaultParam();
        assetsSearchDTO.setStartNum((assetsSearchDTO.getPageNum() - 1) * assetsSearchDTO.getPageSize());
        //  Long totalCount = this.getCount (assetsSearchDTO);
        Long totalCount = assetsMapper.selectCountNewBySearchDTO(assetsSearchDTO);
        pageRespDTO.setCount(totalCount);
        if (totalCount > 0) {
            //  final List<StockAssets> assetsList = this.getList (assetsSearchDTO);
            final List<StockAssets> assetsList = assetsMapper.selectAssetsNewBySearchDTO(assetsSearchDTO);
            List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
            assetsDTOList = settingRelationValues(assetsDTOList);
            pageRespDTO.setData(assetsDTOList);
        } else {
            pageRespDTO.setData(new ArrayList<>(1));
        }
        pageRespDTO.setPageSize(assetsSearchDTO.getPageSize());
        pageRespDTO.setStartNum(assetsSearchDTO.getStartNum());
        pageRespDTO.setPageNum(assetsSearchDTO.getPageNum());
        return pageRespDTO;
    }


     /**
       * @param:
       * @description: service调用查询全量资产
       * @return:
       * @author: <EMAIL>
       * @date: 2022/2/18
       */
    @Override
    public List<AssetsDTO> selectAssetsByService(final AssetsSearchDTO assetsSearchDTO) {
        log.info("StockAssetsServiceImpl.selectAssetsByService,开始查询资产当前时间为：" + new Date());
        //根据单据获取仓库类型
        Integer docType = DocTypeAspect.threadLocal.get();
        if (docType != null) {
            List<Integer> warehouseTypes = ambaseCommonService.selectMetaDataByKey(MetaDataKeyConstants.DOCUMENT_WAREHOUSE, docType);
            if (CollectionUtils.isEmpty(warehouseTypes)) {
                return new ArrayList<>();
            }
            assetsSearchDTO.setWarehouseTypes(warehouseTypes);
        }

        final Long count = assetsMapper.selectCountNewBySearchDTO(assetsSearchDTO);
        if (count == null || count <= CommonConstant.NUMBER_ZERO) {
            return new ArrayList<>();
        }
        int intCount;
        try {
            intCount = Integer.parseInt(String.valueOf(count));
        }catch (Exception e){
            intCount = Integer.MAX_VALUE;
        }
        List<AssetsDTO> assetsDTOList = new ArrayList<>(intCount);
        assetsSearchDTO.setPageNum(CommonConstant.NUMBER_ONE);
        assetsSearchDTO.setPageSize(CommonConstant.MAX_QUERY_COUNT);
        for (int i = CommonConstant.NUMBER_ZERO; i < count; i += CommonConstant.MAX_QUERY_COUNT) {
            assetsSearchDTO.setStartNum((assetsSearchDTO.getPageNum() - CommonConstant.NUMBER_ONE) * assetsSearchDTO.getPageSize());
            List<AssetsDTO> assetSubList = assetsMapper.selectAllAssetsBySearchDTO(assetsSearchDTO);
            assetsDTOList.addAll(assetSubList);
            assetsSearchDTO.setPageNum(assetsSearchDTO.getPageNum() + CommonConstant.NUMBER_ONE);
            log.info("StockAssetsServiceImpl.selectAssetsByService,已经查询出来：" + (i + (CommonConstant.MAX_QUERY_COUNT.compareTo(assetSubList.size()) > CommonConstant.NUMBER_ZERO ? assetSubList.size() : CommonConstant.MAX_QUERY_COUNT) + "条数据"));
        }
        log.info("StockAssetsServiceImpl.selectAssetsByService,查询资产结束当前时间为：" + new Date());
        settingSendAssetsEmailValues(assetsDTOList);
        return assetsDTOList;
    }


    /**
     * 报废搜索资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    @Override
    public ResponseData selectAssetsByAssetsScrap(final AssetsSearchDTO assetsSearchDTO) {
        final PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        //根据单据获取仓库类型
        Integer docType = DocTypeAspect.threadLocal.get();
        if (docType != null) {
            List<Integer> warehouseTypes = ambaseCommonService.selectMetaDataByKey(MetaDataKeyConstants.DOCUMENT_WAREHOUSE, docType);
            if (CollectionUtils.isEmpty(warehouseTypes)) {
                return ResponseData.createFailResult("当前单据没有找到对应的仓库类型");
            }
            assetsSearchDTO.setWarehouseTypes(warehouseTypes);
        }
        //noPaging，Used for excel export.
        if (assetsSearchDTO.getNoPaging() != null && true == assetsSearchDTO.getNoPaging()) {
            //  final Long count = this.getCount (assetsSearchDTO);
            final Long count = assetsMapper.selectCountByAssetsScrap(assetsSearchDTO);
            if (null == count || count.equals(CommonConstant.NUMBER_LONG_ZERO)) {
                pageRespDTO.setMessage("无资产信息");
                return pageRespDTO;
            }

            List<StockAssets> assetsList = new ArrayList<>();
            assetsSearchDTO.setPageNum(CommonConstant.NUMBER_ONE);
            assetsSearchDTO.setPageSize(CommonConstant.MAX_QUERY_COUNT);
            for (int i = CommonConstant.NUMBER_ZERO; i < count; i += CommonConstant.MAX_QUERY_COUNT) {
                assetsSearchDTO.setStartNum((assetsSearchDTO.getPageNum() - CommonConstant.NUMBER_ONE) * assetsSearchDTO.getPageSize());
                //  List<StockAssets> assetSubList = this.getList (assetsSearchDTO);
                List<StockAssets> assetSubList = assetsMapper.selectAssetsByAssetsScrap(assetsSearchDTO);
                assetsList.addAll(assetSubList);
                assetsSearchDTO.setPageNum(assetsSearchDTO.getPageNum() + CommonConstant.NUMBER_ONE);
            }
            List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);

            assetsDTOList = settingRelationValues(assetsDTOList);
            pageRespDTO.setData(assetsDTOList);
            return pageRespDTO;
        }
        assetsSearchDTO.initPageDefaultParam();
        assetsSearchDTO.setStartNum((assetsSearchDTO.getPageNum() - 1) * assetsSearchDTO.getPageSize());
        //  Long totalCount = this.getCount (assetsSearchDTO);
        Long totalCount = assetsMapper.selectCountByAssetsScrap(assetsSearchDTO);
        pageRespDTO.setCount(totalCount);
        if (totalCount > 0) {
            //  final List<StockAssets> assetsList = this.getList (assetsSearchDTO);
            final List<StockAssets> assetsList = assetsMapper.selectAssetsByAssetsScrap(assetsSearchDTO);
            List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
            assetsDTOList = settingRelationValues(assetsDTOList);
            pageRespDTO.setData(assetsDTOList);
        } else {
            pageRespDTO.setData(new ArrayList<>(1));
        }
        pageRespDTO.setPageSize(assetsSearchDTO.getPageSize());
        pageRespDTO.setStartNum(assetsSearchDTO.getStartNum());
        pageRespDTO.setPageNum(assetsSearchDTO.getPageNum());
        return pageRespDTO;
    }

    /**
     * 搜索资产信息
     *
     * @param assetsSearchDTO
     * @return
     */
    @Deprecated
    public ResponseData selectAssetsByDTOOld(AssetsSearchDTO assetsSearchDTO) {
        if (StringUtils.isEmpty(assetsSearchDTO.getCode())) {
            return ResponseData.createFailResult("请输入资产编码");
        }
        if (CollectionUtils.isEmpty(assetsSearchDTO.getDemandItemNoList())) {
            return ResponseData.createFailResult("行单编码不能为空");
        }
        assetsSearchDTO.setStatus(AssetsEnum.statusType.IDLE.getValue());
        assetsSearchDTO.setConditionList(new ArrayList<Integer>() {{
            add(AssetsEnum.Conditions.NORMAL.getValue());
        }});
        // warehouseCode不再从前端获取
        assetsSearchDTO.setWarehouseCode(null);
        // 根据行单获取编码集合
        StockDeliveryPlanLineInfo stockDeliveryPlanLineInfo = new StockDeliveryPlanLineInfo();
        stockDeliveryPlanLineInfo.setReceiveItemNos(assetsSearchDTO.getDemandItemNoList());
        stockDeliveryPlanLineInfo.setStatus(DeliveryPlanLineEnum.Status.WAIT_OUT.getCode());
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectByInfoParam(stockDeliveryPlanLineInfo);
        if (CollectionUtils.isEmpty(stockDeliveryPlanLineList)) {
            return ResponseData.createFailResult("无效需求单");
        }
        Map<String, Set<String>> stringListMap = new HashMap<>();
        for (StockDeliveryPlanLine stockDeliveryPlanLine : stockDeliveryPlanLineList) {
            Set<String> suppliesCodeSet = stringListMap.getOrDefault(stockDeliveryPlanLine.getSuppliesCode(), new HashSet<>());
            suppliesCodeSet.add(stockDeliveryPlanLine.getRealWarehouseCode());
            stringListMap.put(stockDeliveryPlanLine.getSuppliesCode(), suppliesCodeSet);
        }
        List<StockAssets> stockAssets = assetsMapper.selectAssetsNewBySearchDTO(assetsSearchDTO);
        // 如果物料编码每一行的仓库不包含新增资产的领用仓库，那么是查询不出来的
        if (CollectionUtils.isEmpty(stockAssets) || stringListMap.get(stockAssets.get(0).getSuppliesCode()) == null || !stringListMap.get(stockAssets.get(0).getSuppliesCode()).contains(stockAssets.get(0).getWarehouseCode())) {
            return ResponseData.createFailResult("该仓库下没有资产编码对应的资产");
        }
        AssetsDTO assetsDTO = new AssetsDTO();
        BeanUtils.copyProperties(stockAssets.get(0), assetsDTO);
        StockWarehouse stockWarehouse = warehouseService.selectByWarehouseCode(assetsDTO.getWarehouseCode(), null);
        if (stockWarehouse != null) {
            assetsDTO.setWarehouseName(stockWarehouse.getName());
        }
        return ResponseData.createSuccessResult(assetsDTO);
    }

    @Override
    public ResponseData selectAssetsByDTO(AssetsSearchDTO assetsSearchDTO) {
        if (StringUtils.isEmpty(assetsSearchDTO.getCode())) {
            return ResponseData.createFailResult("请输入资产编码");
        }
        if (StringUtils.isEmpty(assetsSearchDTO.getWarehouseCode())) {
            return ResponseData.createFailResult("仓库编码不能为空");
        }
        // 将物料编码集合变为空
        assetsSearchDTO.setSuppliesCodeList(null);
        List<StockAssets> stockAssets = assetsMapper.selectAssetsNewBySearchDTO(assetsSearchDTO);
        if (CollectionUtils.isEmpty(stockAssets)) {
            return ResponseData.createFailResult("该仓库下没有资产编码对应的资产");
        }
        if(!AssetsEnum.Conditions.NORMAL.getValue().equals(stockAssets.get(0).getConditions())){
            return ResponseData.createFailResult("该资产不是正常状态，不能领用");
        }
        if(!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAssets.get(0).getApproveStatus())){
            return ResponseData.createFailResult("该资产已经报废或者在报废/IT工单审批中，不能领用");
        }
        AssetsDTO assetsDTO = new AssetsDTO();
        BeanUtils.copyProperties(stockAssets.get(0), assetsDTO);
        StockWarehouse stockWarehouse = warehouseService.selectByWarehouseCode(assetsDTO.getWarehouseCode(), null);
        if (stockWarehouse != null) {
            assetsDTO.setWarehouseName(stockWarehouse.getName());
        }
        return ResponseData.createSuccessResult(assetsDTO);
    }

    @Override
    public List<AssetsDTO> modelToDTO(final List<StockAssets> assetsList) {
        if (CollectionUtils.isEmpty(assetsList)) {
            return new ArrayList<>(1);
        }
        List<AssetsDTO> dtoList = new ArrayList<>(assetsList.size());
        assetsList.stream().forEach(assets -> {
            dtoList.add(modelToDTO(assets));
        });
        return dtoList;
    }

    @Override
    public AssetsDTO modelToDTO(final StockAssets assets) {
        AssetsDTO assetsDTO = new AssetsDTO();
        BeanUtils.copyProperties(assets, assetsDTO);
        if (StringUtils.isBlank(assets.getHolder())) {
            assetsDTO.setHolderTime(null);
        }
        return assetsDTO;
    }

    @Override
    public List<AssetsDTO> selectAssetByHolder(String holder, String remandWarehouseCode) {
        if (StringUtils.isBlank(holder)) {
            return new ArrayList<>();
        }
        final List<StockAssets> assetsList = this.getListByHolder(holder, remandWarehouseCode);
        List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
        assetsDTOList = settingRelationValues(assetsDTOList);
        return assetsDTOList;
    }

    @Override
    public List<AssetsDTO> selectAssetByHolder(String holder, String remandWarehouseCode, String assetsCode, Integer pageNum, Integer pageSize) {
        if (StringUtils.isBlank(holder)) {
            return new ArrayList<>();
        }
        if (null == pageNum && null == pageSize) {
            pageNum = 1;
            pageSize = 20;
        }

        final List<StockAssets> assetsList = assetsMapper.selectAssetByHolderLimit(holder, remandWarehouseCode, assetsCode, (pageNum - 1) * pageSize, pageSize);
        List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
        assetsDTOList = settingRelationValues(assetsDTOList);
        return assetsDTOList;
    }

    @Override
    public Map<String, AssetsDTO> selectAssetDTOMapByAssetsDTO(AssetsSearchDTO assetsSearchDTO) {
        List<AssetsDTO> assetsDTOList = selectAssetDTOListByAssetsDTO(assetsSearchDTO);
        if(CollectionUtils.isEmpty(assetsDTOList)){
            return new HashMap<>();
        }
        return assetsDTOList.stream().collect(Collectors.toMap(AssetsDTO :: getAssetsCode, assetsDTO -> assetsDTO, (k1, k2) -> k2));
    }

    @Override
    public List<AssetsDTO> selectAssetDTOListByAssetsDTO(AssetsSearchDTO assetsSearchDTO) {
        if(null == assetsSearchDTO){
            return new ArrayList<>();
        }
        List<StockAssets> assetsList = selectAssetsList(assetsSearchDTO);
        if(CollectionUtils.isEmpty(assetsList)){
            return new ArrayList<>();
        }
        List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
        settingRelationValues(assetsDTOList);
        return assetsDTOList;
    }

    @Override
    public Long countAssetByHolder(String holder, String remandWarehouseCode) {
        if (StringUtils.isBlank(holder)) {
            return 0L;
        }
        final Long count = assetsMapper.countAssetByHolder(holder, remandWarehouseCode);
        return count;
    }

    @Override
    public List<AssetsDTO> selectAssetByHolder(String holder, String remandWarehouseCode, List<String> assetsCodeList) {
        if (StringUtils.isBlank(holder)) {
            return new ArrayList<>();
        }
        final List<StockAssets> assetsList = assetsMapper.selectAssetByHolderAndAssetsCodeList(holder, remandWarehouseCode, assetsCodeList);
        List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
        assetsDTOList = settingRelationValues(assetsDTOList);
        return assetsDTOList;
    }

    @Override
    public ResponseData selectAssetsBusinessRecord(AssetsSearchDTO assetsSearchDTO) {
        if (null == assetsSearchDTO || StringUtils.isBlank(assetsSearchDTO.getAssetsCode())) {
            return ResponseData.createFailResult("必填参数为空");
        }
        List<StockAssets> assetsList = this.getList(assetsSearchDTO);
        if (CollectionUtils.isEmpty(assetsList)) {
            return ResponseData.createSuccessResult();
        }
        List<AssetsDTO> assetsDTOList = modelToDTO(assetsList);
        assetsDTOList = settingRelationValues(assetsDTOList);

        List<AssetsBusinessRecordRespDTO> assetsBusinessRecordRespDTOList = assetsMapper.selectAssetsBusinessRecordByParam(assetsSearchDTO);
        assetsDTOList.get(0).setAssetsBusinessRecordRespDTOList(assetsBusinessRecordRespDTOList);
        return ResponseData.createSuccessResult(assetsDTOList.get(0));
    }

    @Override
    public Map<String, StockAssets> selectAssetsMapByCodes(List<String> assetCodes) {
        final Map<String, StockAssets> assetsMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        if (!CollectionUtils.isEmpty(assetCodes)) {
            List<StockAssets> stockAssets = assetsMapper.selectAssetsByCodes(assetCodes);
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(stockAssets)) {
                stockAssets.forEach(asset -> assetsMap.put(asset.getAssetsCode(), asset));
            }
        }

        return assetsMap;
    }

    @Override
    public Map<String, StockAssets> selectAssetsMapByAssetsSearchDTO(AssetsSearchDTO assetsSearchDTO) {
        final Map<String, StockAssets> assetsMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(assetsSearchDTO.getAssetsCodeList())) {
            criteria.andAssetsCodeIn(assetsSearchDTO.getAssetsCodeList());
        }
        if (assetsSearchDTO.getApproveStatus() != null) {
            criteria.andApproveStatusEqualTo(assetsSearchDTO.getApproveStatus());
        }
        List<StockAssets> stockAssets = stockAssetsMapper.selectByExample(example);
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(stockAssets)) {
            stockAssets.forEach(asset -> assetsMap.put(asset.getAssetsCode(), asset));
        }
        return assetsMap;
    }

    @Override
    public List<StockAssets> selectAssetsListByAssetsSearchDTO(AssetsSearchDTO assetsSearchDTO) {
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(assetsSearchDTO.getAssetsCodeList())) {
            criteria.andAssetsCodeIn(assetsSearchDTO.getAssetsCodeList());
        }
        if(!CollectionUtils.isEmpty(assetsSearchDTO.getHolderList())){
            criteria.andHolderIn(assetsSearchDTO.getHolderList());
        }
        if(null != assetsSearchDTO.getStatus()){
            criteria.andStatusEqualTo(assetsSearchDTO.getStatus());
        }
        if(null != assetsSearchDTO.getConditions()){
            criteria.andConditionsEqualTo(assetsSearchDTO.getConditions());
        }
        if (assetsSearchDTO.getApproveStatus() != null) {
            criteria.andApproveStatusEqualTo(assetsSearchDTO.getApproveStatus());
        }
        if(!CollectionUtils.isEmpty(assetsSearchDTO.getNoCategoryCodeList())){
            criteria.andCategoryCodeNotIn(assetsSearchDTO.getNoCategoryCodeList());
        }
        List<StockAssets> stockAssetsList = stockAssetsMapper.selectByExample(example);
        return stockAssetsList;
    }

    @Deprecated
    @Override
    public ResponseData serverPrintAssetCode(AssetCodeReqDTO assetCodeReqDTO, JwtUser user) throws IOException {
        String checkServerPrintAssetCodeParam = checkServerPrintAssetCodeParamResult(assetCodeReqDTO);
        if (StringUtils.isNotBlank(checkServerPrintAssetCodeParam)) {
            return ResponseData.createFailResult(checkServerPrintAssetCodeParam);
        }

        StockAssets stockAssets = this.selectAssetsByCode(assetCodeReqDTO.getAssetCode());
        if (StringUtils.isBlank(stockAssets.getLabelUrl())) {
            //生成pdf
            String labelUrl = generateAssetCodePdf(stockAssets, user);
            if (StringUtils.isBlank(labelUrl)) {
                return ResponseData.createFailResult("生成pdf失败，请稍后重试或联系相关人员");
            }
            stockAssets.setLabelUrl(labelUrl);
            this.updateAssets(stockAssets);
        }
        //调用打印
        List<PrintTaskReqDTO> printTaskReqDTOList = new ArrayList<>();
        PrintTaskReqDTO printTaskReqDTO = new PrintTaskReqDTO();
        printTaskReqDTO.setFileDownloadUrl(stockAssets.getLabelUrl());
        printTaskReqDTO.setPrintKey(assetCodeReqDTO.getPrintKey());
        printTaskReqDTO.setWidth((int) FileConstant.ASSET_CODE_FILE_Y);
        printTaskReqDTO.setHeight((int) FileConstant.ASSET_CODE_FILE_X);
        printTaskReqDTOList.add(printTaskReqDTO);
        boolean printResult = printUtil.savePrintTask(printTaskReqDTOList);
        if (!printResult) {
            return ResponseData.createFailResult("打印失败，请稍后重试或联系相关人员");
        }

        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData selectCostByAssetsCode(String assetsCode) {
        if (StringUtils.isBlank(assetsCode)) {
            return ResponseData.createFailResult("参数不能为空");
        }
        List<String> assetsCodes = Arrays.asList(assetsCode);
        List<AssetsCostRespDTO> assetsCostRespDTOList = ambaseCommonService.selectCostByAssetsCode(assetsCodes);
        if (CollectionUtils.isEmpty(assetsCostRespDTOList)) {
            AssetsCostRespDTO assetsCostRespDTO = new AssetsCostRespDTO();
            assetsCostRespDTO.setAssetsCode(assetsCode);
            assetsCostRespDTO.setLastCost(new BigDecimal("0.0000"));
            return ResponseData.createSuccessResult(assetsCostRespDTO);
        }
        return ResponseData.createSuccessResult(assetsCostRespDTOList.get(CommonConstant.NUMBER_ZERO));
    }

    @Override
    public List<AssetsDTO> selectAssetByHolderAndWareTypes(String holder, List<String> categoryCodes, List<String> assetsCodeList) {
        if (holder == null) {
            return new ArrayList<>();
        }
        return assetsMapper.selectAssetByHolderAndWareTypes(holder, categoryCodes, assetsCodeList);
    }

    @Override
    public List<StockAssets> selectAssetsByReceiveItemNo(String receiveItemNo, Integer pageCount) {
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        criteria.andReceiveItemNoEqualTo(receiveItemNo);
        criteria.andAccountantCodeIsNull();
        example.setLimit(pageCount);
        return stockAssetsMapper.selectByExample(example);
    }

    @Override
    public ResponseData selectAssetsDetailById(Long assetsId) {
        if (null == assetsId) {
            return ResponseData.createFailResult("参数不能为空");
        }
        //1.获取资产信息
        StockAssets assets = selectAssetsById(assetsId);
        if (null == assets) {
            return ResponseData.createFailResult("没有查到资产数据");
        }
        AssetsDTO assetsDTO = settingRelationValues(modelToDTO(assets));
        //2.获取资产尾表信息
        Map<String, String> tailMap = getAssetsExtendAttr(assetsDTO.getCategory(), assetsDTO.getAssetsCode());
        assetsDTO.setAssetsExtendAttr(tailMap);

        //3.获取资产变更记录
        AssetsOperationLogSearchDTO searchDTO = new AssetsOperationLogSearchDTO();
        searchDTO.setAssetsId(assetsId);
        searchDTO.setNoPaging(true);
        ResponseData data = operationLogService.selectAssetsOperationLog(searchDTO);
        List<AssetsOperationLogDTO> assetsDTOList = (List<AssetsOperationLogDTO>) data.getData();
        if (!CollectionUtils.isEmpty(assetsDTOList)) {
            List<AssetsOperationLogRespDTO> resultLogResp = new ArrayList<>(assetsDTOList.size());
            for (int i = CommonConstant.NUMBER_ONE; i <= assetsDTOList.size(); i++) {
                if (i == assetsDTOList.size()) {
                    AssetsOperationLogRespDTO assetsOperationLogRespDTO = new AssetsOperationLogRespDTO();
                    assetsOperationLogRespDTO.setAssetsCode(assetsDTOList.get(i - 1).getAssetsCode());
                    assetsOperationLogRespDTO.setOperationTypeText(AssetsEnum.operationType.NEWSAVE.getDesc());
                    assetsOperationLogRespDTO.setUpdatedAt(assetsDTOList.get(i - 1).getUpdatedAt());
                    assetsOperationLogRespDTO.setUpdatedByName(assetsDTOList.get(i - 1).getUpdatedByName() + " " + assetsDTOList.get(i - 1).getUpdatedBy());
                    resultLogResp.add(assetsOperationLogRespDTO);
                } else {
                    AssetsOperationLogRespDTO assetsOperationLogRespDTONext = compareLogData(assetsDTOList.get(i), assetsDTOList.get(i - 1));
                    resultLogResp.add(assetsOperationLogRespDTONext);
                }
            }
            assetsDTO.setAssetsOperationLogRespDTOList(resultLogResp);
        }
        //4.获取资产操作事件记录
        AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
        assetsSearchDTO.setAssetsCode(assetsDTO.getAssetsCode());
        List<AssetsBusinessRecordRespDTO> assetsBusinessRecordRespDTOList = assetsMapper.selectAssetsBusinessRecordByParam(assetsSearchDTO);
        if (!CollectionUtils.isEmpty(assetsBusinessRecordRespDTOList)) {
            assetsDTO.setAssetsBusinessRecordRespDTOList(assetsBusinessRecordRespDTOList);
        }
        if (StringUtils.isBlank(assetsDTO.getHolder())) {
            assetsDTO.setHolderAddress("");
        }
        //5.返回结果
        return ResponseData.createSuccessResult(assetsDTO);
    }

    @Override
    public List<StockAssetsExtendCommonVo> selectAssetsExtend(AssetsSearchDTO assetsSearchDTO) {
        //根据单据获取仓库类型
        Integer docType = DocTypeAspect.threadLocal.get();
        if (docType != null) {
            List<Integer> warehouseTypes = ambaseCommonService.selectMetaDataByKey(MetaDataKeyConstants.DOCUMENT_WAREHOUSE, docType);
            if (CollectionUtils.isEmpty(warehouseTypes)) {
                throw new ServiceUncheckedException("当前单据没有找到对应的仓库类型");
            }
            assetsSearchDTO.setWarehouseTypes(warehouseTypes);
        }

        //查询总数
        final Long count = assetsMapper.queryAssetsExtendCount(assetsSearchDTO);
        if (count == null) {
            return null;
        }

        List<StockAssetsExtendCommonVo> assetsList = new ArrayList<>();
        assetsSearchDTO.setPageNum(CommonConstant.NUMBER_ONE);
        assetsSearchDTO.setPageSize(CommonConstant.MAX_QUERY_COUNT);
        for (int i = CommonConstant.NUMBER_ZERO; i < count; i += CommonConstant.MAX_QUERY_COUNT) {
            assetsSearchDTO.setStartNum((assetsSearchDTO.getPageNum() - CommonConstant.NUMBER_ONE) * assetsSearchDTO.getPageSize());
            List<StockAssetsExtendCommonVo> assetSubList = assetsMapper.queryAssetsExtendBySearchDTO(assetsSearchDTO);
            assetsList.addAll(assetSubList);
            assetsSearchDTO.setPageNum(assetsSearchDTO.getPageNum() + CommonConstant.NUMBER_ONE);
        }

        assetsList = settingRelationExtendValues(assetsList);
        return assetsList;
    }

    @Override
    public ResponseData selectAssetsByHolder(PageReqDTO pageReqDTO) {
        JwtUser user = SecurityUtil.getJwtUser();
        Map<String, Object> resultMap = new HashMap<>(2);
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        criteria.andHolderEqualTo(user.getEmployeeCode());
        criteria.andSuppliesCodeNotLike("ZZ%");

        //查询总数
        long count = stockAssetsMapper.countByExample(example);
        resultMap.put("count", count);
        if (count <= 0) {
            resultMap.put("assetList", new ArrayList<>());
        } else {
            example.setLimit(pageReqDTO.getPageSize());
            example.setOffset((pageReqDTO.getPageNum() - 1) * pageReqDTO.getPageSize());
            example.setOrderByClause(" assets_id ");
            List<StockAssets> stockAssetsList = stockAssetsMapper.selectByExample(example);
            resultMap.put("assetList", stockAssetsList);
        }
        return ResponseData.createSuccessResult(resultMap);
    }

    @Override
    public Map<String, String> getAssetsExtendAttr(String category, String assetsCode) {
        if (StringUtils.isBlank(category)) {
            log.error("资产：" + assetsCode + "所属资产类别为空，请排查！");
            return null;
        }
        String serviceName = AssetsEnum.assetsTypeServiceEnumMap.get(category);
        if (StringUtils.isNotBlank(serviceName)) {
            StockCommonAssetService stockCommonAssetService = (StockCommonAssetService) SpringUtils.getBean(serviceName);
            Map<String, String> tailMap = stockCommonAssetService.selectTailDataByCode(assetsCode);
            return tailMap;
        }
        return null;
    }

    @Override
    public List<StockAssets> selectAssets(List<String> snCodes) {
        if (CollectionUtils.isEmpty(snCodes)) {
            return null;
        }
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        criteria.andSnCodeIn(snCodes);
        return this.stockAssetsMapper.selectByExample(example);
    }

    @Override
    public List<StockAssets> selectAssetsBySuppliesCode(String suppliesCode) {
        if (StringUtils.isBlank(suppliesCode)) {
            return null;
        }
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        criteria.andSuppliesCodeEqualTo(suppliesCode);
        return this.stockAssetsMapper.selectByExample(example);
    }

    @Override
    public ResponseData selectAssetsExtendInfo(AssetsSearchDTO assetsSearchDTO) {
        if (StringUtils.isBlank(assetsSearchDTO.getCategory())) {
            return ResponseData.createSuccessResult("资产分类不能为空");
        }
        if (StringUtils.isBlank(assetsSearchDTO.getAssetsCode())) {
            return ResponseData.createSuccessResult("资产编码不能为空");
        }
        Map<String, String> tailMap = getAssetsExtendAttr(assetsSearchDTO.getCategory(), assetsSearchDTO.getAssetsCode());
        return ResponseData.createSuccessResult(tailMap);
    }

    /**
     * 为对象赋值
     *
     * @param assetsDTOList
     * @return
     */
    private List<StockAssetsExtendCommonVo> settingRelationExtendValues(List<StockAssetsExtendCommonVo> assetsDTOList) {
        if (CollectionUtils.isEmpty(assetsDTOList)) {
            return new ArrayList<>(1);
        }
        final int collectionSize = assetsDTOList.size();
        final List<String> deptIds = new ArrayList<>();
        final List<String> userIds = new ArrayList<>();
        final List<String> warehouseCode = new ArrayList<>(collectionSize);
        final List<String> companyCodes = new ArrayList<>();
        assetsDTOList.stream().forEach(asset -> {
            if (StringUtils.isNotBlank(asset.getCostDept())) {
                deptIds.add(asset.getCostDept());
            }
            if (StringUtils.isNotBlank(asset.getNeedDept())) {
                deptIds.add(asset.getNeedDept());
            }
            if (StringUtils.isNotBlank(asset.getHolder())) {
                userIds.add(asset.getHolder());
            }
            if (StringUtils.isNotBlank(asset.getAssetsKeeper())) {
                userIds.add(asset.getAssetsKeeper());
            }
            if (StringUtils.isNotBlank(asset.getWarehouseCode())) {
                warehouseCode.add(asset.getWarehouseCode());
            }
            if (StringUtils.isNotBlank(asset.getCompanyCode())) {
                companyCodes.add(asset.getCompanyCode());
            }
        });

        final Map<String, SysDept> deptMap = ambaseCommonService.selectSysDeptMapByIds(deptIds);
        final Map<String, SysUser> userMap = ambaseCommonService.selectSysUserMapByIds(userIds);
        final Map<String, SysCompanyInfo> companyInfoMap = ambaseCommonService.getCompanysByCodes(companyCodes);
        final List<String> userDeptIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userMap)) {
            userMap.forEach((userId, user) -> {
                if (StringUtils.isNotBlank(user.getDeptId())) {
                    userDeptIds.add(user.getDeptId());
                }
            });
        }

        //使用人的部门
        final Map<String, SysDept> userDeptMap = ambaseCommonService.selectSysDeptMapByIds(userDeptIds);

        final List<String> userBussiness = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userDeptMap)) {
            userDeptMap.forEach((deptId, dept) -> {
                if (StringUtils.isNotBlank(dept.getCstBusinessLine()) && !userBussiness.contains(dept.getCstBusinessLine())) {
                    userBussiness.add(dept.getCstBusinessLine());
                }
            });
        }
        final Map<String, SysDict> sysDictMap = ambaseCommonService.selectDictListByValue("CST_BUSINESS_LINE", userBussiness);

        final Map<String, WarehouseRespDTO> warehouseMap = new HashMap<>(8);
        List<WarehouseRespDTO> warehouseRespDTOList = warehouseService.selectWarehouseDetailByCode(warehouseCode);
        if (!CollectionUtils.isEmpty(warehouseRespDTOList)) {
            warehouseRespDTOList.forEach(warehouse -> warehouseMap.put(warehouse.getCode(), warehouse));
        }
        final SysDept defaultDept = new SysDept();
        final SysUser defaultUser = new SysUser();
        final SysDict defaultSysDict = new SysDict();
        final SysCompanyInfo defaultSysCompanyInfo = new SysCompanyInfo();
        assetsDTOList.stream().forEach(assetsDTO -> {
            assetsDTO.setCostDeptName(deptMap.getOrDefault(assetsDTO.getCostDept(), defaultDept).getDeptFullName());
            assetsDTO.setNeedDeptName(deptMap.getOrDefault(assetsDTO.getNeedDept(), defaultDept).getDeptFullName());
            assetsDTO.setAssetsKeeperName(userMap.getOrDefault(assetsDTO.getAssetsKeeper(), defaultUser).getName());
            if (StringUtils.isNotBlank(assetsDTO.getHolder())) {
                if (StringUtils.isNotBlank(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getName())) {
                    assetsDTO.setHolderName(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getName());
                }
                if (StringUtils.isNotBlank(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId())) {
                    assetsDTO.setHolderDeptName(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultDept).getDeptName());
                }
                if (StringUtils.isNotBlank(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId())) {
                    assetsDTO.setHolderDeptFullName(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultDept).getDeptFullName());
                }
                if (StringUtils.isNotBlank(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultDept).getCstBusinessLine())) {
                    assetsDTO.setHolderBussiness(sysDictMap.getOrDefault(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultDept).getCstBusinessLine(), defaultSysDict).getName());
                }
            }
            assetsDTO.setCompanyName(companyInfoMap.getOrDefault(assetsDTO.getCompanyCode(), defaultSysCompanyInfo).getCompanyFullName());
            assetsDTO.setStatusText(AssetsEnum.statusTypeMap.get(assetsDTO.getStatus()));
            assetsDTO.setConditionsName(AssetsEnum.conditionsMap.get(assetsDTO.getConditions()));
            assetsDTO.setPurchaseTypeText(AssetsEnum.purchaseTypeEnumMap.get(assetsDTO.getPurchaseType()));
            WarehouseRespDTO respDTO = warehouseMap.get(assetsDTO.getWarehouseCode());
            if (respDTO != null) {
                assetsDTO.setWarehouseName(respDTO.getName());
                if (null != respDTO.getWarehouseBase()) {
                    assetsDTO.setWarehouseAddress(respDTO.getWarehouseBase().getAddress());
                }
            }
        });
        return assetsDTOList;
    }

    /**
     * 比较两个对象不同的属性值
     *
     * @param logDTO
     * @param nextLogDTO
     * @return
     */
    private AssetsOperationLogRespDTO compareLogData(AssetsOperationLogDTO logDTO, AssetsOperationLogDTO nextLogDTO) {
        AssetsOperationLogRespDTO assetsOperationLogRespDTO = new AssetsOperationLogRespDTO();
        assetsOperationLogRespDTO.setUpdatedAt(nextLogDTO.getUpdatedAt());
        assetsOperationLogRespDTO.setUpdatedByName(nextLogDTO.getUpdatedByName() + " " + nextLogDTO.getUpdatedBy());
        assetsOperationLogRespDTO.setOperationTypeText(nextLogDTO.getOperationTypeText());
        StringBuilder sb = new StringBuilder(500);
        sb.append(compareFieldValue("资产名称", logDTO.getAssetsName(), nextLogDTO.getAssetsName()))
                .append(compareFieldValue("资产型号", logDTO.getModel(), nextLogDTO.getModel()))
                .append(compareFieldValue("资产序列号", logDTO.getSnCode(), nextLogDTO.getSnCode()))
                .append(compareFieldValue("资产品牌", logDTO.getBrand(), nextLogDTO.getBrand()))
                .append(compareFieldValue("资产管理员", logDTO.getAssetsKeeperName(), nextLogDTO.getAssetsKeeperName()))
                .append(compareFieldValue("资产使用人", logDTO.getHolderName(), nextLogDTO.getHolderName()))
                .append(compareFieldValue("资产费用部门", logDTO.getCostDeptName(), nextLogDTO.getCostDeptName()))
                .append(compareFieldValue("资产使用人位置", logDTO.getHolderAddress(), nextLogDTO.getHolderAddress()))
                .append(compareFieldValue("资产状态", logDTO.getStatusText(), nextLogDTO.getStatusText()))
                .append(compareFieldValue("资产使用状态", logDTO.getConditionsName(), nextLogDTO.getConditionsName()))
                .append(compareFieldValue("资产所在仓库", logDTO.getWarehouseName(), nextLogDTO.getWarehouseName()))
                .append(compareFieldValue("资产使用年限", logDTO.getUseYearLimit(), nextLogDTO.getUseYearLimit()));
        if (sb.length() > 0) {
            assetsOperationLogRespDTO.setContent(sb.toString());
        }

        if (!Objects.equals(logDTO.getAssetsPic(), nextLogDTO.getAssetsPic())) {
            assetsOperationLogRespDTO.setAssetPicDiff(CommonConstant.NUMBER_ONE);
            if (StringUtils.isNotBlank(logDTO.getAssetsPic())) {
                assetsOperationLogRespDTO.setLastPicList(Arrays.asList(logDTO.getAssetsPic().split(";")));
            }
            if (StringUtils.isNotBlank(nextLogDTO.getAssetsPic())) {
                assetsOperationLogRespDTO.setNextPicList(Arrays.asList(nextLogDTO.getAssetsPic().split(";")));
            }
        } else {
            assetsOperationLogRespDTO.setAssetPicDiff(CommonConstant.NUMBER_ZERO);
        }

        return assetsOperationLogRespDTO;
    }

    /**
     * 比较字段是否相等
     *
     * @param preDes
     * @param laseDto
     * @param nextDto
     * @return
     */
    private String compareFieldValue(String preDes, String laseDto, String nextDto) {
        if (!Objects.equals(laseDto, nextDto)) {
            return preDes + " 变更前:" + (StringUtils.isNotBlank(laseDto) ? laseDto : "无") + " 变更后:" + (StringUtils.isNotBlank(nextDto) ? nextDto : "无") + ";";
        }
        return "";
    }

    /**
     * 比较字段是否相等
     *
     * @param preDes
     * @param laseDto
     * @param nextDto
     * @return
     */
    private String compareFieldValue(String preDes, Integer laseDto, Integer nextDto) {
        if (!Objects.equals(laseDto, nextDto)) {
            return preDes + " 变更前:" + (null != laseDto ? laseDto : "无") + " 变更后:" + (null != laseDto ? nextDto : "无") + ";";
        }
        return "";
    }

    /**
     * 生成pdf
     *
     * @param stockAssets
     * @return
     */
    private String generateAssetCodePdf(StockAssets stockAssets, JwtUser user) throws IOException {

        //二维码校验
        String qrBatchOperating = this.generateQr(stockAssets, user, QrEnum.type.QR);
        if (StringUtils.isBlank(qrBatchOperating)) {
            log.error("生成二维码失败,assetCode:{}", stockAssets.getAssetsCode());
            throw new ServiceUncheckedException("生成二维码失败，请重试或联系技术人员");
        }

        String barBatchOperation = this.generateQr(stockAssets, user, QrEnum.type.BAR);
        if (StringUtils.isBlank(barBatchOperation)) {
            log.error("生成条形码失败,assetCode:{}", stockAssets.getAssetsCode());
            throw new ServiceUncheckedException("生成条形码失败，请重试或联系技术人员");
        }

        final Map<String, BatchQrCodeRespDTO> qrBatchQrCodeRespDTOMap = stockQrService.getBatchQrCodeRespDTOMap(qrBatchOperating, QrEnum.codeType.QR_CODE.getValue());
        if (CollectionUtils.isEmpty(qrBatchQrCodeRespDTOMap)) {
            log.error("获取二维码Map为空");
            return null;
        }

        final Map<String, BatchQrCodeRespDTO> barBatchQrCodeRespDTOMap = stockQrService.getBatchQrCodeRespDTOMap(barBatchOperation, QrEnum.codeType.BAR_CODE.getValue());
        if (CollectionUtils.isEmpty(barBatchQrCodeRespDTOMap)) {
            log.error("获取条形码Map为空");
            return null;
        }

        List<StockAssetCodeInfo> stockAssetCodeInfoList = new ArrayList<>(CommonConstant.NUMBER_ONE);
        StockAssetCodeInfo stockAssetCodeInfo = new StockAssetCodeInfo();
        stockAssetCodeInfo.setCategoryName(stockAssets.getCategory());
        stockAssetCodeInfo.setAssetCode(stockAssets.getAssetsCode());
        stockAssetCodeInfoList.add(stockAssetCodeInfo);

        return stockAssetCodeService.generateAssetCodePdf(stockAssets.getAssetsCode(), stockAssetCodeInfoList, qrBatchQrCodeRespDTOMap, barBatchQrCodeRespDTOMap);
    }

    /**
     * 生成码
     *
     * @param stockAssets
     * @param user
     * @param type
     * @return
     */
    private String generateQr(StockAssets stockAssets, JwtUser user, QrEnum.type type) {

        StockQr stockQr = new StockQr();
        stockQr.setBizNo(stockAssets.getAssetsCode());
        stockQr.setBizType(QrEnum.bizType.ASSET_CODE.getValue());
        stockQr.setDelFlag(CommonEnum.delFlag.NO.getValue());
        stockQr.setStatus(CommonEnum.status.YES.getValue());
        stockQr.setType(type.getValue());
        List<StockQr> stockQrList = stockQrService.selectParam(stockQr, null, null, null);
        if (CollectionUtils.isEmpty(stockQrList)) {
            List<String> assetCode = new ArrayList<String>() {{
                add(stockAssets.getAssetsCode());
            }};
            return stockQrService.generateQr(assetCode, QrEnum.bizType.ASSET_CODE.getValue(), type.getValue(), user);
        }

        return stockQrList.get(CommonConstant.NUMBER_ZERO).getQrBatchOperating();
    }

    private String checkServerPrintAssetCodeParamResult(AssetCodeReqDTO assetCodeReqDTO) {
        if (null == assetCodeReqDTO || StringUtils.isBlank(assetCodeReqDTO.getAssetCode()) || StringUtils.isBlank(assetCodeReqDTO.getPrintKey())) {
            return "必填参数为空";
        }

        StockAssets stockAssets = this.selectAssetsByCode(assetCodeReqDTO.getAssetCode());
        if (null == stockAssets) {
            return "资产不存在";
        }

        //校验打印机
        ResponseData responseData = printApi.selectPrintInfoByPrintKey(assetCodeReqDTO.getPrintKey());
        if (null == responseData || !ResponseCode.SUCCESS_CODE.equals(responseData.getCode())) {
            log.info("当前打印机无效，printKey:{}, 错误：{}", assetCodeReqDTO.getPrintKey(), null == responseData ? null : responseData.getMessage());
            return "当前打印机无效";
        }
        return null;
    }

    private List<StockAssets> getListByHolder(String holder, String remandWarehouseCode) {
        if (StringUtils.isBlank(holder)) {
            return new ArrayList<>();
        }

        return assetsMapper.selectAssetByHolder(holder, remandWarehouseCode);
    }

    @Override
    public List<AssetsDTO> settingRelationValues(final List<AssetsDTO> assetsDTOList) {
        if (CollectionUtils.isEmpty(assetsDTOList)) {
            return new ArrayList<>(1);
        }
        final int collectionSize = assetsDTOList.size();
        final List<String> deptIds = new ArrayList<>();
        final List<String> userIds = new ArrayList<>();
        final List<String> warehouseCode = new ArrayList<>(collectionSize);
        final List<String> companyCodes = new ArrayList<>();
        //GR执照修改时添加
        final List<String> assetsCodes = new ArrayList<>();
        final List<String> suppliesCodes = new ArrayList<>();

        assetsDTOList.stream().forEach(asset -> {
            if (StringUtils.isNotBlank(asset.getCostDept())) {
                deptIds.add(asset.getCostDept());
            }
            if (StringUtils.isNotBlank(asset.getNeedDept())) {
                deptIds.add(asset.getNeedDept());
            }
            if (StringUtils.isNotBlank(asset.getHolder())) {
                userIds.add(asset.getHolder());
            }
            if (StringUtils.isNotBlank(asset.getAssetsKeeper())) {
                userIds.add(asset.getAssetsKeeper());
            }
            if (StringUtils.isNotBlank(asset.getWarehouseCode())) {
                warehouseCode.add(asset.getWarehouseCode());
            }
            if (StringUtils.isNotBlank(asset.getCompanyCode())) {
                companyCodes.add(asset.getCompanyCode());
            }

            if (StringUtils.isNotBlank(asset.getAssetsCode())) {
                assetsCodes.add(asset.getAssetsCode());
            }
            if (StringUtils.isNotBlank(asset.getSuppliesCode())) {
                suppliesCodes.add(asset.getSuppliesCode());
            }
        });

        //查询资产执照相关信息
        final Map<String, StockAssetsLicenseRespDTO> stockAssetsLicenseMap = stockAssetsLicenseService.selectMapByAssetsCodeList(assetsCodes);
        final Map<String, StockSupplies> suppliesMap = stockSuppliesService.getSuppliesMapByCodes(null, suppliesCodes);

        final Map<String, SysDept> deptMap = ambaseCommonService.selectSysDeptMapByIds(deptIds);
        final Map<String, SysUser> userMap = ambaseCommonService.selectSysUserMapByIds(userIds);
        final Map<String, SysCompanyInfo> companyInfoMap = ambaseCommonService.getCompanysByCodes(companyCodes);
        final List<String> userDeptIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userMap)) {
            userMap.forEach((userId, user) -> {
                if (StringUtils.isNotBlank(user.getDeptId())) {
                    userDeptIds.add(user.getDeptId());
                }
            });
        }

        //使用人的部门
        final Map<String, SysDept> userDeptMap = ambaseCommonService.selectSysDeptMapByIds(userDeptIds);

        final List<String> userBussiness = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userDeptMap)) {
            userDeptMap.forEach((deptId, dept) -> {
                if (StringUtils.isNotBlank(dept.getCstBusinessLine()) && !userBussiness.contains(dept.getCstBusinessLine())) {
                    userBussiness.add(dept.getCstBusinessLine());
                }
            });
        }
        final Map<String, SysDict> sysDictMap = ambaseCommonService.selectDictListByValue("CST_BUSINESS_LINE", userBussiness);

        final Map<String, WarehouseRespDTO> warehouseMap = new HashMap<>(8);
        List<WarehouseRespDTO> warehouseRespDTOList = warehouseService.selectWarehouseDetailByCode(warehouseCode);
        if (!CollectionUtils.isEmpty(warehouseRespDTOList)) {
            warehouseRespDTOList.forEach(warehouse -> warehouseMap.put(warehouse.getCode(), warehouse));
        }
        // 查询资产净值相关信息
        CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO = new CuxGetfadetailTblReqDTO();
        cuxGetfadetailTblReqDTO.setTagNumberList(assetsCodes);
        Map<String, CuxGetfadetailTbl> cuxGetfadetailTblMap = ambaseCommonService.selectCuxGetfadetailTblMap(cuxGetfadetailTblReqDTO);

        final SysDept defaultDept = new SysDept();
        final SysUser defaultUser = new SysUser();
        final SysDept defaultUserDept = new SysDept();
        final SysDict defaultSysDict = new SysDict();
        final SysCompanyInfo defaultSysCompanyInfo = new SysCompanyInfo();
        assetsDTOList.stream().forEach(assetsDTO -> {
            assetsDTO.setCostDeptName(deptMap.getOrDefault(assetsDTO.getCostDept(), defaultDept).getDeptFullName());
            assetsDTO.setNeedDeptName(deptMap.getOrDefault(assetsDTO.getNeedDept(), defaultDept).getDeptFullName());
            assetsDTO.setAssetsKeeperName(userMap.getOrDefault(assetsDTO.getAssetsKeeper(), defaultUser).getName());
            if (StringUtils.isNotBlank(assetsDTO.getHolder())) {
                assetsDTO.setCompensationUser(assetsDTO.getHolder());
                if (null != userMap.getOrDefault(assetsDTO.getHolder(), defaultUser) && StringUtils.isNotBlank(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getName())) {
                    assetsDTO.setHolderName(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getName());
                    assetsDTO.setCompensationUserName(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getName());
                }
                if (null != userMap.getOrDefault(assetsDTO.getHolder(), defaultUser) && StringUtils.isNotBlank(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId())) {
                    assetsDTO.setHolderDeptName(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultUserDept).getDeptName());
                }
                if (null != userMap.getOrDefault(assetsDTO.getHolder(), defaultUser) && StringUtils.isNotBlank(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId())) {
                    assetsDTO.setHolderDeptFullName(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultUserDept).getDeptFullName());
                }
                if (StringUtils.isNotBlank(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultUserDept).getCstBusinessLine())) {
                    assetsDTO.setHolderBussiness(sysDictMap.getOrDefault(userDeptMap.getOrDefault(userMap.getOrDefault(assetsDTO.getHolder(), defaultUser).getDeptId(), defaultUserDept).getCstBusinessLine(), defaultSysDict).getName());
                }
            }
            assetsDTO.setCompanyName(companyInfoMap.getOrDefault(assetsDTO.getCompanyCode(), defaultSysCompanyInfo).getCompanyFullName());
            assetsDTO.setStatusText(AssetsEnum.statusTypeMap.get(assetsDTO.getStatus()));
            assetsDTO.setConditionsName(AssetsEnum.conditionsMap.get(assetsDTO.getConditions()));
            assetsDTO.setPurchaseTypeText(AssetsEnum.purchaseTypeEnumMap.get(assetsDTO.getPurchaseType()));
            WarehouseRespDTO respDTO = warehouseMap.get(assetsDTO.getWarehouseCode());
            if (respDTO != null) {
                assetsDTO.setWarehouseName(respDTO.getName());
                if (null != respDTO.getWarehouseBase()) {
                    assetsDTO.setWarehouseAddress(respDTO.getWarehouseBase().getAddress());
                }
            }
            StockSupplies supplies = suppliesMap.get(assetsDTO.getSuppliesCode());
            if (null != supplies) {
                assetsDTO.setSuppliesName(supplies.getName());
            }
            //资产为执照时增加返回的参数信息
            StockAssetsLicenseRespDTO stockAssetsLicenseRespDTO = stockAssetsLicenseMap.get(assetsDTO.getAssetsCode());
            if (null != stockAssetsLicenseRespDTO) {
                settingLicenseValue(assetsDTO, stockAssetsLicenseRespDTO);
            }
            //固定资产标识
            if (null != assetsDTO.getExtractStatus()) {
                assetsDTO.setExtractStatusName(MetaDataEnum.yesOrNo.YES.getValue().equals(assetsDTO.getExtractStatus()) ? "固定资产" : "低值资产");
            }
            // 添加资产净值
            CuxGetfadetailTbl cuxGetfadetailTbl = cuxGetfadetailTblMap.get(assetsDTO.getAssetsCode());
            if(cuxGetfadetailTbl != null){
                assetsDTO.setLastCost(cuxGetfadetailTbl.getLastCost().setScale(CommonConstant.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
            }else {
                assetsDTO.setLastCost(BigDecimal.ZERO.setScale(CommonConstant.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
            }
            Date purchaseTime = assetsDTO.getPurchaseTime();
            if(purchaseTime != null){
                try {
                    assetsDTO.setPurchaseTimeStr(DateUtils.dateFormat(purchaseTime, DateUtils.DATE_PATTERN));
                    assetsDTO.setUsedYearLimitByMonth(DateTimeUtil.dateBetweenMonth(purchaseTime, new Date()));
                } catch (ParseException e) {
                    log.error("StockAssetsServiceImpl.settingRelationValues转化购置时间失败，assetsDTO:{}, 错误信息为：{}", JSON.toJSONString(assetsDTO), e.getMessage());
                }
            }
            Date holderTime = assetsDTO.getHolderTime();
            if(holderTime != null){
                try {
                    assetsDTO.setHolderTimeStr(DateUtils.dateFormat(holderTime, DateUtils.DATE_PATTERN));
                    assetsDTO.setHoldYearLimitByMonth(DateTimeUtil.dateBetweenMonth(holderTime, new Date()));
                } catch (ParseException e) {
                    log.error("StockAssetsServiceImpl.settingRelationValues转化持有时间失败，assetsDTO:{}, 错误信息为：{}", JSON.toJSONString(assetsDTO), e.getMessage());
                }
            }
        });
        return assetsDTOList;
    }

     /**
       * @param:
       * @description: 设置资产导出的基础值
       * @return:
       * @author: <EMAIL>
       * @date: 2022/4/1
       */
    public List<AssetsDTO> settingSendAssetsEmailValues(final List<AssetsDTO> assetsDTOList) {
        if (CollectionUtils.isEmpty(assetsDTOList)) {
            return new ArrayList<>();
        }
         int collectionSize = assetsDTOList.size();
         Set<String> deptIds = new HashSet<>(collectionSize);
         Set<String> userIds = new HashSet<>(collectionSize);
         Set<String> warehouseCodes = new HashSet<>(collectionSize);
         Set<String> companyCodes = new HashSet<>(collectionSize);

        for (AssetsDTO asset : assetsDTOList) {

            if (StringUtils.isNotBlank(asset.getCostDept())) {
                deptIds.add(asset.getCostDept());
            }
            if (StringUtils.isNotBlank(asset.getNeedDept())) {
                deptIds.add(asset.getNeedDept());
            }
            if (StringUtils.isNotBlank(asset.getHolder())) {
                userIds.add(asset.getHolder());
            }
            if (StringUtils.isNotBlank(asset.getAssetsKeeper())) {
                userIds.add(asset.getAssetsKeeper());
            }
            if (StringUtils.isNotBlank(asset.getWarehouseCode())) {
                warehouseCodes.add(asset.getWarehouseCode());
            }
            if (StringUtils.isNotBlank(asset.getCompanyCode())) {
                companyCodes.add(asset.getCompanyCode());
            }
        }

        final Map<String, SysUser> userMap = ambaseCommonService.selectSysUserMapByIds(new ArrayList<>(userIds));
        if (!CollectionUtils.isEmpty(userMap)) {
            userMap.forEach((userId, user) -> {
                if (StringUtils.isNotBlank(user.getDeptId())) {
                    deptIds.add(user.getDeptId());
                }
            });
        }
        // 部门信息
        final Map<String, SysDept> deptMap = ambaseCommonService.selectSysDeptMapByIds(new ArrayList<>(deptIds));
        // 公司信息
        final Map<String, SysCompanyInfo> companyInfoMap = ambaseCommonService.getCompanysByCodes(new ArrayList<>(companyCodes));
        // 业务线信息
        final Set<String> userBusinessSet = new HashSet<>(CommonConstant.DEFAULT_MAP_SIZE);
        if (!CollectionUtils.isEmpty(deptMap)) {
            deptMap.forEach((deptId, dept) -> {
                if (StringUtils.isNotBlank(dept.getCstBusinessLine())) {
                    userBusinessSet.add(dept.getCstBusinessLine());
                }
            });
        }
        // 部门业务线信息
        final Map<String, SysDict> sysDictMap = ambaseCommonService.selectDictListByValue("CST_BUSINESS_LINE", new ArrayList<>(userBusinessSet));
        // 仓库信息
        final Map<String, WarehouseRespDTO> warehouseMap = new HashMap<>(warehouseCodes.size());
        List<WarehouseRespDTO> warehouseRespDTOList = warehouseService.selectWarehouseDetailByCode(new ArrayList<>(warehouseCodes));
        if (!CollectionUtils.isEmpty(warehouseRespDTOList)) {
            warehouseRespDTOList.forEach(warehouse -> warehouseMap.put(warehouse.getCode(), warehouse));
        }

        for (AssetsDTO assetsDTO : assetsDTOList) {
            if(userMap != null){
                SysUser sysUser = userMap.get(assetsDTO.getAssetsKeeper());
                if(sysUser != null){
                    assetsDTO.setAssetsKeeperName(sysUser.getName());
                }
                sysUser = userMap.get(assetsDTO.getHolder());
                if(sysUser != null){
                    assetsDTO.setHolderName(sysUser.getName());
                }
            }
            if(deptMap != null){
                SysDept sysDept = deptMap.get(assetsDTO.getCostDept());
                if(sysDept != null){
                    assetsDTO.setCostDeptName(sysDept.getDeptFullName());
                }
                sysDept = deptMap.get(assetsDTO.getNeedDept());
                if(sysDept != null){
                    assetsDTO.setNeedDeptName(sysDept.getDeptFullName());
                }
                sysDept = deptMap.get(assetsDTO.getHolderDept());
                if(sysDept != null){
                    assetsDTO.setHolderDeptName(sysDept.getDeptName());
                    assetsDTO.setHolderDeptFullName(sysDept.getDeptFullName());
                    if(sysDictMap != null && sysDictMap.get(sysDept.getCstBusinessLine()) != null){
                        assetsDTO.setHolderBussiness(sysDictMap.get(sysDept.getCstBusinessLine()).getName());
                    }
                }
            }
            if(MapUtils.isNotEmpty(companyInfoMap) && companyInfoMap.get(assetsDTO.getCompanyCode()) != null){
                assetsDTO.setCompanyName(companyInfoMap.get(assetsDTO.getCompanyCode()).getCompanyFullName());
            }
            assetsDTO.setStatusText(AssetsEnum.statusTypeMap.get(assetsDTO.getStatus()));
            assetsDTO.setConditionsName(AssetsEnum.conditionsMap.get(assetsDTO.getConditions()));
            assetsDTO.setPurchaseTypeText(AssetsEnum.purchaseTypeEnumMap.get(assetsDTO.getPurchaseType()));
            WarehouseRespDTO respDTO = warehouseMap.get(assetsDTO.getWarehouseCode());
            if (respDTO != null) {
                assetsDTO.setWarehouseName(respDTO.getName());
                if (null != respDTO.getWarehouseBase()) {
                    assetsDTO.setWarehouseAddress(respDTO.getWarehouseBase().getAddress());
                }
            }
            //固定资产标识
            if (null != assetsDTO.getExtractStatus()) {
                assetsDTO.setExtractStatusName(MetaDataEnum.yesOrNo.YES.getValue().equals(assetsDTO.getExtractStatus()) ? "固定资产" : "低值资产");
            }
        }
        return assetsDTOList;
    }

    /**
     * 设置执照资产相关信息
     *
     * @param assetsDTO
     * @param stockAssetsLicenseRespDTO
     */
    private void settingLicenseValue(AssetsDTO assetsDTO, StockAssetsLicenseRespDTO stockAssetsLicenseRespDTO) {
        //资产为执照时增加返回的参数信息
        if (null != stockAssetsLicenseRespDTO) {
            //法人信息
            assetsDTO.setLicenseLegalPerson(stockAssetsLicenseRespDTO.getLicenseLegalPerson());
            assetsDTO.setLicenseLegalPersonName(stockAssetsLicenseRespDTO.getLicenseLegalPersonName());
            //法人身份证信息
            assetsDTO.setCorporateIdNumber(stockAssetsLicenseRespDTO.getCorporateIdNumber());
            //注册地址
            assetsDTO.setRegisteredAddress(stockAssetsLicenseRespDTO.getRegisteredAddress());
            //执照类型
            assetsDTO.setLicenseType(stockAssetsLicenseRespDTO.getLicenseType());
            //注册资本
            assetsDTO.setRegisteredCapital(stockAssetsLicenseRespDTO.getRegisteredCapital());
            //经营范围
            assetsDTO.setBusinessScope(stockAssetsLicenseRespDTO.getBusinessScope());
            //身份证复印件信息
            assetsDTO.setPictureMetaRespDTOList(stockAssetsLicenseRespDTO.getPictureMetaRespDTOList());
        }
    }


    @Override
    public AssetsDTO settingRelationValues(final AssetsDTO assetsDTO) {
        List<AssetsDTO> dtoList = new ArrayList<>(1);
        dtoList.add(assetsDTO);
        List<AssetsDTO> assetsDTOList = settingRelationValues(dtoList);
        return assetsDTOList.get(0);
    }

    /**
     * 根据资产id or 资产code 批量查询资产信息
     */
    @Override
    public List<StockAssets> selectAssets(final List<Long> ids, final List<String> codes) {
        StockAssetsExample example = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(ids)) {
            criteria.andAssetsIdIn(ids);
            return this.stockAssetsMapper.selectByExample(example);
        } else if (!CollectionUtils.isEmpty(codes)) {
            criteria.andAssetsCodeIn(codes);
            return this.stockAssetsMapper.selectByExample(example);
        }
        return null;
    }

    /**
     * 更新资产信息,记录修改日志
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateAssets(final StockAssets assets) {
        assets.setUpdatedAt(new Date());
        Integer ret = stockAssetsMapper.updateByPrimaryKeySelective(assets);
        StockAssets newAssets = stockAssetsMapper.selectByPrimaryKey(assets.getAssetsId());
        saveOperationLog(newAssets, AssetsEnum.operationType.BUSINESS.getValue());
        return ret;
    }

    @Override
    public Integer updateOne(StockAssets stockAssets) {
        if(null == stockAssets){
            return CommonConstant.NUMBER_ZERO;
        }
        return stockAssetsMapper.updateByPrimaryKeySelective(stockAssets);
    }


    /**
     * 根据资产编码查询资产信息
     */
    @Override
    public StockAssets selectAssetsByCode(final String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        final StockAssetsExample example = new StockAssetsExample();
        final StockAssetsExample.Criteria criteria = example.createCriteria();
        criteria.andAssetsCodeEqualTo(code);
        final List<StockAssets> assetsList = this.stockAssetsMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(assetsList)) {
            return assetsList.get(CommonConstant.NUMBER_ZERO);
        }

        return null;
    }

    @Override
    public StockAssets selectAssetsById(final Long assetsId) {
        final StockAssets assets = this.stockAssetsMapper.selectByPrimaryKey(assetsId);
        return assets;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchOperating(final List<StockAssets> stockAssetsList, final JwtUser user) {
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            log.info("资产集合为空");
            return false;
        }
        final List<StockAssets> newStockAssetsList = new ArrayList<>(stockAssetsList.size());
        stockAssetsList.forEach(stockAssets -> {
            stockAssets.setHasSub(0);//暂时默认没有子资产
            if (StringUtils.isBlank(stockAssets.getCompanyCode())) {
                stockAssets.setCompanyCode("-"); // 暂时公司编码
            }
            stockAssets.setRegularMaintain(0); //是否定时维护 暂时默认
            stockAssets.setCreatedBy(user.getEmployeeCode());
            stockAssets.setUpdatedBy(user.getEmployeeCode());
            if (StringUtils.isBlank(stockAssets.getLabelUrl())) {
                stockAssets.setLabelUrl("");
            }
            newStockAssetsList.add(stockAssets);
        });
        List<StockAssets> stockAssetsList1 = new ArrayList<>();
        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
        if (!CollectionUtils.isEmpty(newStockAssetsList)) {
            if (newStockAssetsList.size() > 0) {
                int toIndex = CommonConstant.MAX_INSERT_COUNT;
                for (int i = 0; i < newStockAssetsList.size(); i += CommonConstant.MAX_INSERT_COUNT) {
                    if (i + CommonConstant.MAX_INSERT_COUNT > newStockAssetsList.size()) {
                        toIndex = newStockAssetsList.size() - i;
                    }
                    List<StockAssets> newSubDetailList = newStockAssetsList.subList(i, i + toIndex);
                    assetsMapper.batchInsert(newSubDetailList);
                    stockAssetsList1.addAll(newSubDetailList);
                }
            }

        }

        final List<Long> idList = stockAssetsList1.stream().map(a -> a.getAssetsId()).collect(Collectors.toList());
        this.operationLogService.insertMultipleOperationLogByAssetsIds(idList, null);
        return true;
    }

    /**
     * 通过资产编码批量更新资产
     * @param stockAssetsList
     * @return
     */
    @Override
    public Integer batchUpdateByAssetsCodeList(List<StockAssets> stockAssetsList) {
        if(org.springframework.util.CollectionUtils.isEmpty(stockAssetsList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssets>> stockAssetsListList = ListUtil.splitList(stockAssetsList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssets> stockAssetsListTemp : stockAssetsListList) {
                count += assetsMapper.batchUpdateByAssetsCodeList(stockAssetsListTemp);
            }
            return count;
        }else {
            return assetsMapper.batchUpdateByAssetsCodeList(stockAssetsList);
        }
    }

    /**
     * 批量更新资产
     * @param stockAssetsList
     * @return
     */
    @Override
    public Integer batchUpdate(List<StockAssets> stockAssetsList) {
        if(org.springframework.util.CollectionUtils.isEmpty(stockAssetsList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssets>> stockAssetsListList = ListUtil.splitList(stockAssetsList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssets> stockAssetsListTemp : stockAssetsListList) {
                count += assetsMapper.batchUpdate(stockAssetsListTemp);
            }
            return count;
        }else {
            return assetsMapper.batchUpdate(stockAssetsList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateMultipleSelective(final List<StockAssets> assetsList) {
        if (CollectionUtils.isEmpty(assetsList)) {
            return CommonConstant.NUMBER_ZERO;
        }


        int toIndex = CommonConstant.MAX_INSERT_COUNT;

        Integer ret = CommonConstant.NUMBER_ZERO;
        for (int i = CommonConstant.NUMBER_ZERO; i < assetsList.size(); i += CommonConstant.MAX_INSERT_COUNT) {
            if (i + CommonConstant.MAX_INSERT_COUNT > assetsList.size()) {
                toIndex = assetsList.size() - i;
            }
            List<StockAssets> newAssetsList = assetsList.subList(i, i + toIndex);
            Integer subRet = assetsMapper.updateMultipleSelective(newAssetsList);
            ret = ret + subRet;
        }
        this.operationLogService.insertMultipleOperationLog(assetsList, AssetsEnum.operationType.BUSINESS);
        return ret;
    }

    /**
     * 根据参数赋值Example
     *
     * @param assetsSearchDTO
     * @return
     */
    private StockAssetsExample settingExampleClause(AssetsSearchDTO assetsSearchDTO) {
        final StockAssetsExample example = new StockAssetsExample();
        final StockAssetsExample.Criteria criteria = example.createCriteria();
        final StockAssetsExample.Criteria criteria1 = example.createCriteria();
        if (null != assetsSearchDTO.getAssetsId()) {
            criteria.andAssetsIdEqualTo(assetsSearchDTO.getAssetsId());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getAssetsCode())) {
            if(assetsSearchDTO.getRightLike() != null && assetsSearchDTO.getRightLike()){
                criteria.andAssetsCodeLike(assetsSearchDTO.getAssetsCode() + "%");
            }else {
                criteria.andAssetsCodeLike("%" + assetsSearchDTO.getAssetsCode() + "%");
            }
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getAssetsName())) {
            criteria.andAssetsNameEqualTo(assetsSearchDTO.getAssetsName());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getSuppliesCode())) {
            criteria.andSuppliesCodeEqualTo(assetsSearchDTO.getSuppliesCode());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getSnCode())) {
            criteria.andSnCodeEqualTo(assetsSearchDTO.getSnCode());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getBrand())) {
            criteria.andBrandEqualTo(assetsSearchDTO.getBrand());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getModel())) {
            criteria.andModelEqualTo(assetsSearchDTO.getModel());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getUnit())) {
            criteria.andUnitEqualTo(assetsSearchDTO.getUnit());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getCategory())) {
            criteria.andCategoryEqualTo(assetsSearchDTO.getCategory());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getCompanyCode())) {
            criteria.andCompanyCodeEqualTo(assetsSearchDTO.getCompanyCode());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getCompanyName())) {
            criteria.andCompanyNameEqualTo(assetsSearchDTO.getCompanyName());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getCostDept())) {
            criteria.andCostDeptEqualTo(assetsSearchDTO.getCompanyCode());
        }
        if (null != assetsSearchDTO.getStatus()) {
            criteria.andStatusEqualTo(assetsSearchDTO.getStatus());
        }
        if (null != assetsSearchDTO.getPurchaseType()) {
            criteria.andPurchaseTypeEqualTo(assetsSearchDTO.getPurchaseType());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getAssetsKeeper())) {
            criteria.andAssetsKeeperEqualTo(assetsSearchDTO.getAssetsKeeper());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getPurchaseNo())) {
            criteria.andPurchaseNoEqualTo(assetsSearchDTO.getPurchaseNo());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getHolder())) {
            criteria.andHolderEqualTo(assetsSearchDTO.getHolder());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getNeedDept())) {
            criteria.andNeedDeptEqualTo(assetsSearchDTO.getNeedDept());
        }
        if (StringUtils.isNotBlank(assetsSearchDTO.getCreatedBy())) {
            criteria.andCreatedByEqualTo(assetsSearchDTO.getCreatedBy());
        }
        // 目前remark 未维护值，这里搜索资产名称=物料remark
        if (StringUtils.isNotBlank(assetsSearchDTO.getRemark())) {
            criteria.andAssetsNameLike("%" + assetsSearchDTO.getRemark() + "%");
        }
        //同时支持备注搜索
        if (StringUtils.isNotBlank(assetsSearchDTO.getRemark())) {
            criteria1.andRemarkLike("%" + assetsSearchDTO.getRemark() + "%");
            example.or(criteria1);
        }

        if (StringUtils.isNotBlank(assetsSearchDTO.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(assetsSearchDTO.getUpdatedBy());
        }

        settingExampleWarehouseCodeClause(criteria, assetsSearchDTO);
        if (!CollectionUtils.isEmpty(assetsSearchDTO.getNoGetStatus())) {
            criteria.andStatusNotIn(assetsSearchDTO.getNoGetStatus());
        }
        //根据资产使用情况筛选资产
        if (!CollectionUtils.isEmpty(assetsSearchDTO.getConditionList())) {
            criteria.andConditionsIn(assetsSearchDTO.getConditionList());
        }

        if (!CollectionUtils.isEmpty(assetsSearchDTO.getNoGetConditionList())) {
            criteria.andConditionsNotIn(assetsSearchDTO.getNoGetConditionList());
        }
        if (!CollectionUtils.isEmpty(assetsSearchDTO.getNoCategoryCodeList())) {
            criteria.andCategoryCodeNotIn(assetsSearchDTO.getNoCategoryCodeList());
        }
        if(!CollectionUtils.isEmpty(assetsSearchDTO.getAssetsCodeList())){
            criteria.andAssetsCodeIn(assetsSearchDTO.getAssetsCodeList());
        }
        if(!CollectionUtils.isEmpty(assetsSearchDTO.getSuppliesCodeList())){
            criteria.andSuppliesCodeIn(assetsSearchDTO.getSuppliesCodeList());
        }
        if(assetsSearchDTO.getApproveStatus() != null){
            criteria.andApproveStatusEqualTo(assetsSearchDTO.getApproveStatus());
        }
        if(StringUtils.isNotBlank(assetsSearchDTO.getCategoryCode())){
            criteria.andCategoryCodeEqualTo(assetsSearchDTO.getCategoryCode());
        }
        return example;
    }

    /**
     * 根据参数赋值资产仓库
     *
     * @param criteria
     * @param assetsSearchDTO
     */
    private void settingExampleWarehouseCodeClause(StockAssetsExample.Criteria criteria, AssetsSearchDTO assetsSearchDTO) {
        if (StringUtils.isNotBlank(assetsSearchDTO.getWarehouseCode())) {
            criteria.andWarehouseCodeEqualTo(assetsSearchDTO.getWarehouseCode());
        } else {
            if (StringUtils.isNotBlank(assetsSearchDTO.getBusLargeRegion()) ||
                    StringUtils.isNotBlank(assetsSearchDTO.getBusCity())) {
                List<StockWarehouseBase> warehouses =
                        this.warehouseBaseService.getWarehouseBaseList(assetsSearchDTO.getBusLargeRegion(), assetsSearchDTO.getBusCity());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(warehouses)) {
                    criteria.andWarehouseCodeIn(warehouses.stream().map(w -> w.getWarehouseCode()).collect(Collectors.toList()));
                }
            }
        }
    }


    public List<StockAssets> getList(AssetsSearchDTO assetsSearchDTO) {
        StockAssetsExample example = settingExampleClause(assetsSearchDTO);
        example.setLimit(assetsSearchDTO.getPageSize());
        example.setOffset(assetsSearchDTO.getStartNum());
        String orderByClause = " assets_id desc ";
        if (StringUtils.isNotBlank(assetsSearchDTO.getSortColumns())) {
            orderByClause = assetsSearchDTO.getSortColumns();
        }
        if (assetsSearchDTO.getPageSize() != null) {
            example.setLimit(assetsSearchDTO.getPageSize());
        }
        example.setOrderByClause(orderByClause);
        return this.stockAssetsMapper.selectByExample(example);
    }

    public Long getCount(AssetsSearchDTO assetsSearchDTO) {
        return this.stockAssetsMapper.countByExample(settingExampleClause(assetsSearchDTO));
    }

    /**
     * 获取资产转移单编号
     *
     * @return
     */
    private String getDocumentNo() {
        String trNo = OrderUtil.getOrderNo(OrderEnum.DOCUMENT_NO);
        if (net.logstash.logback.encoder.org.apache.commons.lang.StringUtils.isBlank(trNo)) {
            log.error("调用公用生成编号出错，将使用系统默认生成的资产编号》》");
            return "TR" + System.currentTimeMillis();
        }
        return trNo;
    }

    /**
     * 比较两个字符串是否相同
     *
     * @return 相同返回true
     */
    private boolean compare(String str1, String str2) {
        if (Objects.isNull(str1)) {
            if (Objects.isNull(str2)) {
                return true;
            } else {
                return false;
            }
        } else {
            return str1.equals(str2);
        }
    }

    /**
     * 保存资产转移单赋值
     *
     * @param assetsDTO
     * @param stockAssets
     * @param stockAssetsDocument
     * @param stockAssetsDocumentDetail
     */
    private void prepareSaveDbBeanDTO(AssetsDTO assetsDTO, StockAssets stockAssets, StockAssetsDocument stockAssetsDocument, StockAssetsDocumentDetail stockAssetsDocumentDetail) {

        JwtUser user = SecurityUtil.getJwtUser();

        //获取单据编号
        String documentNo = getDocumentNo();

        //转移头赋值
        stockAssetsDocument.setDocumentNo(documentNo);
        stockAssetsDocument.setBillingTime(new Date());
        stockAssetsDocument.setBillingUser(user.getEmployeeCode());
        stockAssetsDocument.setCreatedAt(new Date());
        stockAssetsDocument.setCreatedBy(user.getEmployeeCode());
        stockAssetsDocument.setDelFlag(AssetTransferEnum.DelFlag.NO_DELETE.getCode());
        stockAssetsDocument.setReasonCode(AssetTransferEnum.Reason.OTHER_TRANSFER.getCode());
        stockAssetsDocument.setRemark("");
        stockAssetsDocument.setHeadStatus(AssetTransferEnum.Status.APPROVE.getCode());
        stockAssetsDocument.setType(AssetTransferEnum.Type.AUTO_ASSET_HOLDER_RANSFER.getCode());
        stockAssetsDocument.setUpdatedAt(new Date());
        stockAssetsDocument.setUpdatedBy(user.getEmployeeCode());
        stockAssetsDocument.setNewAssetsKeeper(StringUtils.isBlank(assetsDTO.getAssetsKeeper()) ? "" : assetsDTO.getAssetsKeeper());


        //转移行赋值
        stockAssetsDocumentDetail.setDocumentNo(documentNo);
        stockAssetsDocumentDetail.setAssetsCode(stockAssets.getAssetsCode());
        stockAssetsDocumentDetail.setCreatedAt(new Date());
        stockAssetsDocumentDetail.setCreatedBy(user.getEmployeeCode());
        stockAssetsDocumentDetail.setDelFlag(AssetTransferEnum.DelFlag.NO_DELETE.getCode());
        stockAssetsDocumentDetail.setOldAssetsKeeper(StringUtils.isBlank(stockAssets.getAssetsKeeper()) ? "" : stockAssets.getAssetsKeeper());
        stockAssetsDocumentDetail.setNewAssetsKeeper(StringUtils.isBlank(assetsDTO.getAssetsKeeper()) ? "" : assetsDTO.getAssetsKeeper());
        stockAssetsDocumentDetail.setOldCompanyCode(stockAssets.getCompanyCode());
        stockAssetsDocumentDetail.setNewCompanyCode(stockAssets.getCompanyCode());
        stockAssetsDocumentDetail.setOldHolder(stockAssets.getHolder());
        stockAssetsDocumentDetail.setNewHolder(assetsDTO.getHolder());
        stockAssetsDocumentDetail.setOldHolderAddress(stockAssets.getHolderAddress());
        stockAssetsDocumentDetail.setNewHolderAddress(assetsDTO.getHolderAddress());
        //获取人员所在部门
        List<String> empIdList = new ArrayList<>();
        empIdList.add(stockAssets.getHolder());
        empIdList.add(assetsDTO.getHolder());
        if (!CollectionUtils.isEmpty(empIdList)) {
            Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
            stockAssetsDocumentDetail.setOldDeptCode(sysUserMap.getOrDefault(stockAssets.getHolder(), new SysUser()).getDeptId());
            stockAssetsDocumentDetail.setNewDeptCode(sysUserMap.getOrDefault(assetsDTO.getHolder(), new SysUser()).getDeptId());
        }
        stockAssetsDocumentDetail.setLineStatus(AssetTransferEnum.LineStatus.NO_FINISH.getCode());
        stockAssetsDocumentDetail.setUpdatedAt(new Date());
        stockAssetsDocumentDetail.setUpdatedBy(user.getEmployeeCode());
    }

    /**
     * @param:
     * @description: 根据资产类型查询资产
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/9
     */
    @Override
    public ResponseData selectAssetByType(String companyName, Integer type, JwtUser user, Integer pageNum, Integer pageSize) {
        // 判断传入参数是否正确
        StringBuilder stringBuilder = new StringBuilder();
        if (type == null) {
            stringBuilder.append("类型是传项，请填入类型重试！！！");
        }
        if (stringBuilder.length() != 0) {
            return ResponseData.createFailResult(stringBuilder.toString());
        }
        // 如果为法务印章则查询印章类型的资产
        AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
        if (StockAssetsCategoryEnum.Type.LEGAL_SEAL_TYPE.getValue().equals(type)) {
            assetsSearchDTO.setCategoryCode(StockAssetsCategoryEnum.category.SEAL.getValue());
        }
        // 如果为执照查询执照类型的资产
        if (StockAssetsCategoryEnum.Type.LICENSE_TYPE.getValue().equals(type)) {
            assetsSearchDTO.setCategoryCode(StockAssetsCategoryEnum.category.LICENSE.getValue());
        }
        //财务印章类资产
        if (StockAssetsCategoryEnum.Type.FINANCE_SEAL_TYPE.getValue().equals(type)) {
            assetsSearchDTO.setCategoryCode(StockAssetsCategoryEnum.category.FINANCE_SEAL.getValue());
        }
        //银行开户许可证
        if (StockAssetsCategoryEnum.Type.FINANCE_LINCENCE_TYPE.getValue().equals(type)) {
            assetsSearchDTO.setCategoryCode(StockAssetsCategoryEnum.category.FINANCE_LICENCE.getValue());
        }

        assetsSearchDTO.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
        // 初始化分页值
        assetsSearchDTO.setPageNum(pageNum);
        assetsSearchDTO.setPageSize(pageSize);
        assetsSearchDTO.initPageDefaultParam();
        assetsSearchDTO.setAssetsCode(companyName.trim());
        assetsSearchDTO.setAssetsName(companyName.trim());
        assetsSearchDTO.setStatus(AssetsEnum.statusType.IDLE.getValue());
        // 查询资产集合
        Map<String, Object> returnMap = new HashMap<String, Object>();
        returnMap.put("stockAsset", assetsMapper.selectAssetsBySearchDTO(assetsSearchDTO));
        returnMap.put("count", assetsMapper.selectCountBySearchDTO(assetsSearchDTO));
        return ResponseData.createSuccessResult(returnMap);
    }

    /**
     * @param:
     * @description: 根据资产类型和资产持有人以及根据资产编码和名称查询资产
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/9
     */
    @Override
    public ResponseData selectAssetByTypeAndHolder(String holder, String assetsCodeAndName, Integer type, JwtUser user, Integer pageNum, Integer pageSize, Integer isPC, Integer isSearch, Integer approveStatus) {
        // 判断传入参数是否正确
        StringBuilder stringBuilder = new StringBuilder();
        if (type == null) {
            stringBuilder.append("类型是传项，请填入类型重试！！！");
        }
        if (stringBuilder.length() != 0) {
            return ResponseData.createFailResult(stringBuilder.toString());
        }
        AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
        // 设置是否排除报废和报废中的资产逻辑
        assetsSearchDTO.setApproveStatus(approveStatus);
        // 如果是筛选固定资产不包括执照和印章
        if (StockAssetsCategoryEnum.Type.FIXED_ASSETS_TYPE.getValue().equals(type)) {
            List<String> noCategoryCodeList = new ArrayList<String>(2);
            noCategoryCodeList.add(StockAssetsCategoryEnum.category.LICENSE.getValue());
            noCategoryCodeList.add(StockAssetsCategoryEnum.category.SEAL.getValue());
            assetsSearchDTO.setNoCategoryCodeList(noCategoryCodeList);
        }
        // 初始化分页值
        assetsSearchDTO.setPageNum(pageNum);
        assetsSearchDTO.setPageSize(pageSize);
        assetsSearchDTO.initPageDefaultParam();
        // 如果是PC端，按资产编码查询
        if (CommonConstant.YES_PC.equals(isPC)) {
            assetsSearchDTO.setAssetsCode(assetsCodeAndName);
        }
        // 如果不是PC端，按资产编码和资产名字查询，而且需要给holder赋默认值，还需要判断是否分页
        if (CommonConstant.NO_PC.equals(isPC)) {
            assetsSearchDTO.setAssetsCodeAndName(assetsCodeAndName);
            if (StringUtils.isBlank(holder)) {
                holder = user.getEmployeeCode();
            }
            // 判断是否需要分页
            if (CommonConstant.NO_SEARCH.equals(isSearch)) {
                assetsSearchDTO.setStartNum(null);
            }
        }
        // 防止数据库中的空串带来的影响
        if ("".equals(holder)) {
            holder = null;
        }
        assetsSearchDTO.setHolder(holder);
        // 查询资产集合
        List<StockAssetsInfo> stockAssetsInfos = assetsMapper.selectAssetByTypeAndHolder(assetsSearchDTO);
        // 将集合转化成DTO
        List<AssetsDTO> assetsDTOList = new ArrayList<>();
        stockAssetsInfos.forEach(stockAssetsInfo -> {
            AssetsDTO assetsDTO = new AssetsDTO();
            BeanUtils.copyProperties(stockAssetsInfo, assetsDTO);
            // 添加资产状态信息
            assetsDTO.setStatusText(AssetsEnum.assetsStatusTypeMap.get(assetsDTO.getStatus()));
            assetsDTOList.add(assetsDTO);
        });
        // 获取当前登陆人已经在审批中的资产编码列表
        StockAssetsDocumentLineRespDTO stockAssetsDocumentLineRespDTO = new StockAssetsDocumentLineRespDTO();
        stockAssetsDocumentLineRespDTO.setOldHolder(user.getEmployeeCode());
        stockAssetsDocumentLineRespDTO.setHeadStatus(AssetTransferEnum.Status.APPROVE.getCode());
        List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = stockAssetsDocumentDetailMapper.selectByStockAssetsDocumentLineReq(stockAssetsDocumentLineRespDTO);
        // 获取AssetsCodeList
        List<String> stockAssetsCodeIsApproval = stockAssetsDocumentDetailList.stream().map(stockAssetsDocumentDetail -> stockAssetsDocumentDetail.getAssetsCode()).collect(Collectors.toList());
        // 拼装是否在审核中的状态
        assetsDTOList.forEach(assetsDTO -> {
            // 添加拥有人姓名
            assetsDTO.setHolderName(user.getEmployeeName());
            // 如果包含在审核中，前端不能选中
            if (stockAssetsCodeIsApproval.contains(assetsDTO.getAssetsCode())) {
                assetsDTO.setIsApproval(CommonConstant.YES_APPROVAL);
            } else {
                assetsDTO.setIsApproval(CommonConstant.NO_APPROVAL);
            }
        });
        Map<String, Object> returnMap = new HashMap<String, Object>();
        returnMap.put("stockAsset", assetsDTOList);
        returnMap.put("count", assetsMapper.countAssetByTypeAndHolder(assetsSearchDTO));
        return ResponseData.createSuccessResult(returnMap);
    }

    /**
     * @param:
     * @description: TODO
     * @return: 设置判断该资产是否为资产所有人
     * @author: <EMAIL>
     * @date: 2021/7/27
     */
    @Override
    public void setJudgeHolderHaveAssets(AssetsDTO assetsDTO) {
        // 获取用户信息
        JwtUser user = SecurityUtil.getJwtUser();
        final StockAssetsExample example = new StockAssetsExample();
        final StockAssetsExample.Criteria criteria = example.createCriteria();
        criteria.andAssetsCodeEqualTo(assetsDTO.getAssetsCode());
        criteria.andHolderEqualTo(user.getEmployeeCode());
        long count = this.stockAssetsMapper.countByExample(example);
        if (count <= 0) {
            assetsDTO.setIsMyself(CommonConstant.NO_MYSELF);
        } else {
            assetsDTO.setIsMyself(CommonConstant.YES_MYSELF);
        }
    }

    /**
     * @param:
     * @description: TODO
     * @return: 设置判断该资产是否已经在审批中了
     * @author: <EMAIL>
     * @date: 2021/7/27
     */
    @Override
    public void setJudgeHolderAssetsIsApproval(AssetsDTO assetsDTO) {
        // 获取用户信息
        JwtUser user = SecurityUtil.getJwtUser();
        // 获取当前登陆人已经在审批中的资产编码列表
        StockAssetsDocumentLineRespDTO stockAssetsDocumentLineRespDTO = new StockAssetsDocumentLineRespDTO();
        stockAssetsDocumentLineRespDTO.setOldHolder(user.getEmployeeCode());
        stockAssetsDocumentLineRespDTO.setHeadStatus(AssetTransferEnum.Status.APPROVE.getCode());
        stockAssetsDocumentLineRespDTO.setAssetsCode(assetsDTO.getAssetsCode());
        List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = stockAssetsDocumentDetailMapper.selectByStockAssetsDocumentLineReq(stockAssetsDocumentLineRespDTO);
        if (stockAssetsDocumentDetailList.isEmpty()) {
            assetsDTO.setIsApproval(CommonConstant.NO_APPROVAL);
        } else {
            assetsDTO.setIsApproval(CommonConstant.YES_APPROVAL);
        }
    }

    @Override
    public void batchPrintAssetsCode(MultipartFile file, String printKey) throws IOException {
        JwtUser user = SecurityUtil.getJwtUser();
        if (StringUtils.isBlank(printKey)) {
            log.error("printKey不能为空");
        }

        List<SuppliesCodePrintExcel> suppliesCodePrintExcels = ExcelUtil.importExcel(file.getInputStream(), SuppliesCodePrintExcel.class);
        if (CollectionUtils.isEmpty(suppliesCodePrintExcels)) {
            log.error("excel不能为空");
        }

        if (suppliesCodePrintExcels.size() > CommonConstant.NUMBER_100) {
            log.error("最多打印100条资产");
        }

        //校验资产编码
        Set<String> repeatAssetsCode = new HashSet<>();
        for (SuppliesCodePrintExcel suppliesCodePrintExcel : suppliesCodePrintExcels) {
            if (repeatAssetsCode.contains(suppliesCodePrintExcel.getAssetsCode())) {
                log.error("资产编码" + suppliesCodePrintExcel.getAssetsCode() + "重复");
            }
            repeatAssetsCode.add(suppliesCodePrintExcel.getAssetsCode());
        }

        //校验资产编码是否存在
        List<StockAssets> stockAssetsList = this.selectAssets(null, new ArrayList<>(repeatAssetsCode));
        if (CollectionUtils.isEmpty(stockAssetsList)) {
            log.error("未获取到资产信息");
        }
        Set<String> existAssetsCode = stockAssetsList.stream().map(stockAssets -> stockAssets.getAssetsCode()).collect(Collectors.toSet());
        for (SuppliesCodePrintExcel suppliesCodePrintExcel : suppliesCodePrintExcels) {
            if (!existAssetsCode.contains(suppliesCodePrintExcel.getAssetsCode())) {
                log.error(suppliesCodePrintExcel.getAssetsCode() + "不存在");
            }
        }

        //生成pdf
        List<String> labeUrlList = new ArrayList<>();
        for (StockAssets stockAssets : stockAssetsList) {
            if (StringUtils.isBlank(stockAssets.getLabelUrl())) {
                String labelUrl = generateAssetCodePdf(stockAssets, user);
                if (StringUtils.isBlank(labelUrl)) {
                    log.error("生成pdf失败，请稍后重试或联系相关人员");
                }
                labeUrlList.add(labelUrl);
            } else {
                labeUrlList.add(stockAssets.getLabelUrl());
            }
        }

        List<PrintTaskReqDTO> printTaskReqDTOList = new ArrayList<>();
        for (String labeUrl : labeUrlList) {
            PrintTaskReqDTO printTaskReqDTO = new PrintTaskReqDTO();
            printTaskReqDTO.setFileDownloadUrl(labeUrl);
            printTaskReqDTO.setPrintKey(printKey);
            printTaskReqDTO.setWidth((int) FileConstant.ASSET_CODE_FILE_Y);
            printTaskReqDTO.setHeight((int) FileConstant.ASSET_CODE_FILE_X);
            printTaskReqDTOList.add(printTaskReqDTO);
        }

        //调用打印
        boolean printResult = printUtil.savePrintTask(printTaskReqDTOList);
        if (!printResult) {
            log.error("打印失败，请稍后重试或联系相关人员");
        } else {
            log.info("资产编码打印成功");
        }


    }

    /**
     * 定时发送资产全量数据文件
     *
     * @param
     * @return
     */
    @Override
    public ResponseData sendAssetsEmail() {
        AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
        //根据单据获取仓库类型
        Integer docType = DocTypeAspect.threadLocal.get();
        if (docType != null) {
            List<Integer> warehouseTypes = ambaseCommonService.selectMetaDataByKey(MetaDataKeyConstants.DOCUMENT_WAREHOUSE, docType);
            if (CollectionUtils.isEmpty(warehouseTypes)) {
                return ResponseData.createFailResult("当前单据没有找到对应的仓库类型");
            }
            assetsSearchDTO.setWarehouseTypes(warehouseTypes);
        }
        // 放到线程池中，进行异步执行
        StockThreadPool.getStockExecutor().execute(() -> sendAssetsEmailAsync(assetsSearchDTO));
        return ResponseData.createSuccessResult();
    }

    /**
     * 批量修改资产信息
     *
     * @param
     * @return
     */
    @Override
    public int batchUpdateAssetsSameMessage(AssetsSearchDTO assetsSearchDTO, List<String> assetsCodeList) {
        if(CollectionUtils.isEmpty(assetsCodeList)){
            return CommonConstant.NUMBER_ZERO;
        }
        StockAssetsExample stockAssetsExample = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = stockAssetsExample.createCriteria();
        criteria.andAssetsCodeIn(assetsCodeList);
        StockAssets stockAssets = new StockAssets();
        if(assetsSearchDTO.getConditions() != null){
            stockAssets.setConditions(assetsSearchDTO.getConditions());
        }
        if(assetsSearchDTO.getUpdatedBy() != null){
            stockAssets.setUpdatedBy(assetsSearchDTO.getUpdatedBy());
        }
        return stockAssetsMapper.updateByExampleSelective(stockAssets, stockAssetsExample);
    }

    @Override
    public ResponseData searchAssetsByReceive(AssetsSearchDTO assetsSearchDTO) {
        if(StringUtils.isBlank(assetsSearchDTO.getAssetsCode())){
            return ResponseData.createSuccessResult(new ArrayList<>());
        }
        // 设置分页为1000条
        assetsSearchDTO.setPageSize(CommonConstant.MAX_LIMIT_COUNT);
        assetsSearchDTO.setStatus(AssetsEnum.statusType.IDLE.getValue());
        assetsSearchDTO.setConditions(AssetsEnum.Conditions.NORMAL.getValue());
        assetsSearchDTO.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
        return selectAssets(assetsSearchDTO);
    }

    @Override
    public ResponseData searchAssetsByRemand(AssetsSearchDTO assetsSearchDTO) {
        if(StringUtils.isBlank(assetsSearchDTO.getAssetsCode())){
            return ResponseData.createSuccessResult(new ArrayList<>());
        }
        // 设置分页为1000条
        assetsSearchDTO.setPageSize(CommonConstant.MAX_LIMIT_COUNT);
        assetsSearchDTO.setStatus(AssetsEnum.statusType.USED.getValue());
        assetsSearchDTO.setConditions(AssetsEnum.Conditions.NORMAL.getValue());
        assetsSearchDTO.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
        return selectAssets(assetsSearchDTO);
    }

    @Override
    public ResponseData queryAssetsNumberGroupByCategoryCode(StockAssetsNumberGroupByCategoryCodeReqDTO stockAssetsNumberGroupByCategoryCodeReqDTO) {
        if(null == stockAssetsNumberGroupByCategoryCodeReqDTO){
            return ResponseData.createFailResult("请求参数不能为空");
        }
        String holder = stockAssetsNumberGroupByCategoryCodeReqDTO.getHolder();
        if(StringUtils.isEmpty(holder)){
            return ResponseData.createFailResult("资产持有人不能为空");
        }
        StockAssetsNumberGroupByCategoryCodeReqDO stockAssetsNumberGroupByCategoryCodeReqDO = new StockAssetsNumberGroupByCategoryCodeReqDO();
        stockAssetsNumberGroupByCategoryCodeReqDO.setHolder(stockAssetsNumberGroupByCategoryCodeReqDTO.getHolder());
        List<String> notInCategoryCodeList = new ArrayList<>(CommonConstant.NUMBER_TWO);
        notInCategoryCodeList.add(AssetsEnum.AssetsCategory.LICENSE.getValue());
        notInCategoryCodeList.add(AssetsEnum.AssetsCategory.SEAL.getValue());
        stockAssetsNumberGroupByCategoryCodeReqDO.setNotInCategoryCodeList(notInCategoryCodeList);
        List<StockAssetsNumberGroupByCategoryCodeRespDO> stockAssetsNumberGroupByCategoryCodeRespDOList = assetsMapper.selectAssetsNumberGroupByCategoryCode(stockAssetsNumberGroupByCategoryCodeReqDO);
        if(CollectionUtils.isEmpty(stockAssetsNumberGroupByCategoryCodeRespDOList)){
            return ResponseData.createSuccessResult(stockAssetsNumberGroupByCategoryCodeRespDOList);
        }
        List<StockAssetsNumberGroupByCategoryCodeRespDTO> stockAssetsNumberGroupByCategoryCodeRespDTOList = new ArrayList<>(stockAssetsNumberGroupByCategoryCodeRespDOList.size());
        for (StockAssetsNumberGroupByCategoryCodeRespDO stockAssetsNumberGroupByCategoryCodeRespDO : stockAssetsNumberGroupByCategoryCodeRespDOList) {
            StockAssetsNumberGroupByCategoryCodeRespDTO stockAssetsNumberGroupByCategoryCodeRespDTO = new StockAssetsNumberGroupByCategoryCodeRespDTO();
            BeanUtils.copyProperties(stockAssetsNumberGroupByCategoryCodeRespDO, stockAssetsNumberGroupByCategoryCodeRespDTO);
            stockAssetsNumberGroupByCategoryCodeRespDTOList.add(stockAssetsNumberGroupByCategoryCodeRespDTO);
        }
        return ResponseData.createSuccessResult(stockAssetsNumberGroupByCategoryCodeRespDTOList);
    }

    @Override
    public StockAssets selectOne(AssetsSearchDTO assetsSearchDTO) {
        List<StockAssets> stockAssetsList = selectList(assetsSearchDTO);
        if(CollectionUtils.isEmpty(stockAssetsList)){
            return null;
        }
        return stockAssetsList.get(CommonConstant.NUMBER_ZERO);
    }

    @Override
    public List<StockAssets> selectList(AssetsSearchDTO assetsSearchDTO) {
        if(null == assetsSearchDTO){
            return null;
        }
        StockAssetsExample stockAssetsExample = new StockAssetsExample();
        StockAssetsExample.Criteria criteria = stockAssetsExample.createCriteria();
        if(StringUtils.isNotBlank(assetsSearchDTO.getAssetsCode())){
            criteria.andAssetsCodeEqualTo(assetsSearchDTO.getAssetsCode());
        }
        if(StringUtils.isNotBlank(assetsSearchDTO.getSnCode())){
            criteria.andSnCodeEqualTo(assetsSearchDTO.getSnCode());
        }
        return stockAssetsMapper.selectByExample(stockAssetsExample);
    }

    /**
     * 定时发送资产全量数据文件的异步任务
     *
     * @param
     * @return
     */
    private void sendAssetsEmailAsync(AssetsSearchDTO assetsSearchDTO) {
        log.info("StockAssetsServiceImpl.sendAssetsEmailAsync,excel导出开始查询资产编码------,param：{}", JSON.toJSONString(assetsSearchDTO));
        List<AssetsDTO> dtoList = selectAssetsByService(assetsSearchDTO);
        log.info("StockAssetsServiceImpl.sendAssetsEmailAsync,excel导出查询资产编码完成------,数据总数为：{}", dtoList.size());
        //因重载方法导致注解解析错误。临时解决线上bug，过滤掉执照
        if ((org.apache.commons.collections.CollectionUtils.isEmpty(dtoList))) {
            log.error("StockAssetsServiceImpl.sendAssetsEmailAsync,文件内容是空，无法生成excel");
            return;
        }
        final List<ExportAssetsEntity> modelList = new ArrayList<>(dtoList.size());
        dtoList.stream().forEach(dto -> {
            final ExportAssetsEntity entity = new ExportAssetsEntity();
            BeanUtils.copyProperties(dto, entity);
            if (StringUtils.isNotBlank(dto.getHolder())) {
                if (StringUtils.isNotBlank(dto.getHolderName())) {
                    entity.setHolderName(dto.getHolder() + " " + dto.getHolderName());
                } else {
                    entity.setHolderName(dto.getHolder());
                }
            } else {
                dto.setHolderName("");
            }
            modelList.add(entity);
        });
        final LocalDateTime dateTime = LocalDateTime.now();
        final String fileName = "资产卡片信息_" + dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        MultipartFile multipartFile = null;
        try {
            log.info("StockAssetsServiceImpl.sendAssetsEmailAsync,开始生成excel---------");
            // 生成输出流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
            ExcelUtil.createExcelWithBuffer(modelList, bufferedOutputStream);
            bufferedOutputStream.close();
            // 转化为MultipartFile
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            BufferedInputStream bufferedInputStream = new BufferedInputStream(byteArrayInputStream);
            multipartFile = new MockMultipartFile("file", fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), bufferedInputStream);
            bufferedInputStream.close();
        } catch (Exception e) {
            log.error("StockAssetsServiceImpl.sendAssetsEmailAsync,生成excel失败---------，异常信息为:" + e.getMessage());
            return;
        }
        log.info("StockAssetsServiceImpl.sendAssetsEmailAsync,excel生成完成----------");
        // 上传文件
        log.info("StockAssetsServiceImpl.sendAssetsEmailAsync,开始上传文件------");
        Map<String, String> returnMap = fileUtil.uploadFile(multipartFile);
        if(MapUtils.isEmpty(returnMap)){
            log.error("StockAssetsServiceImpl.sendAssetsEmailAsync,上传文件失败------");
            return;
        }
        log.info("StockAssetsServiceImpl.sendAssetsEmailAsync,开始上传文件------成功，返回值为：{}", returnMap);
        // 发送email
        String filedId = returnMap.get("fileId");
        // 发送email
        List<Map<String, String>> content = new ArrayList<>(CommonConstant.NUMBER_ONE);
        Map<String, String> map1 = new HashMap<>();
        map1.put("key", "filedId");
        map1.put("value", filedId);
        content.add(map1);
        SendGuaGuaMessageUtil.sendMessage(PropertyConstants.SENT_ASSETS, content);
    }
}
