package com.gz.eim.am.stock.service.impl.assets;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockAssetsDocumentMapper;
import com.gz.eim.am.stock.entity.StockAssetsDocument;
import com.gz.eim.am.stock.service.assets.StockAssetsDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StockAssetsDocumentServiceImpl implements StockAssetsDocumentService {
    @Autowired
    private StockAssetsDocumentMapper stockAssetsDocumentMapper;

    @Override
    public int insertOne(StockAssetsDocument stockAssetsDocument) {
        if(null == stockAssetsDocument){
            return CommonConstant.NUMBER_ZERO;
        }
        return stockAssetsDocumentMapper.insertSelective(stockAssetsDocument);
    }
}
