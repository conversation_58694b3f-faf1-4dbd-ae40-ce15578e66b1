package com.gz.eim.am.stock.service.discount;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.discount.StockCardSuppliesDiscountQueryDTO;
import com.gz.eim.am.stock.dto.request.discount.StockCardSuppliesDiscountReqDTO;
import com.gz.eim.am.stock.entity.StockCardSuppliesDiscount;

/**
 * @Author: wangjing67
 * @Date: 1/27/21 6:47 下午
 * @description
 */
public interface StockCardSuppliesDiscountService {

    /**
     * 新建折现申请单
     *
     * @param stockCardSuppliesDiscountReqDTO
     * @return
     * @throws Exception
     */
    ResponseData save(StockCardSuppliesDiscountReqDTO stockCardSuppliesDiscountReqDTO) throws RuntimeException, Exception;


    /**
     * 查询折现申请单列表
     *
     * @param stockCardSuppliesDiscountQueryDTO
     * @return
     * @throws Exception
     */
    ResponseData queryByPage(StockCardSuppliesDiscountQueryDTO stockCardSuppliesDiscountQueryDTO) throws Exception;

    /**
     * 查询折现申请单详情
     *
     * @param bizId
     * @return
     * @throws Exception
     */
    ResponseData queryDetail(String bizId) throws Exception;

    /**
     * 根据申请单号更新状态
     * @param discountNo
     * @param status
     * @return
     * @throws Exception
     */
    Boolean updateStatusByDiscountNo(String discountNo,Integer status)throws Exception;

    /**
     * 根据申请单号查询
     * @param discountNo
     * @return
     * @throws Exception
     */
    StockCardSuppliesDiscount getByDiscountNo(String discountNo)throws Exception;

}
