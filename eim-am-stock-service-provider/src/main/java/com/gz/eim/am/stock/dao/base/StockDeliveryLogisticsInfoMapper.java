package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockDeliveryLogisticsInfo;
import com.gz.eim.am.stock.entity.StockDeliveryLogisticsInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockDeliveryLogisticsInfoMapper {
    long countByExample(StockDeliveryLogisticsInfoExample example);

    int deleteByPrimaryKey(Integer logisticsId);

    int insert(StockDeliveryLogisticsInfo record);

    int insertSelective(StockDeliveryLogisticsInfo record);

    List<StockDeliveryLogisticsInfo> selectByExample(StockDeliveryLogisticsInfoExample example);

    StockDeliveryLogisticsInfo selectByPrimaryKey(Integer logisticsId);

    int updateByExampleSelective(@Param("record") StockDeliveryLogisticsInfo record, @Param("example") StockDeliveryLogisticsInfoExample example);

    int updateByExample(@Param("record") StockDeliveryLogisticsInfo record, @Param("example") StockDeliveryLogisticsInfoExample example);

    int updateByPrimaryKeySelective(StockDeliveryLogisticsInfo record);

    int updateByPrimaryKey(StockDeliveryLogisticsInfo record);
}