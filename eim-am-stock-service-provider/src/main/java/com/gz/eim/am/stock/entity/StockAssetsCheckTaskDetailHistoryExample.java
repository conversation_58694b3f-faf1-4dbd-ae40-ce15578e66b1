package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockAssetsCheckTaskDetailHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockAssetsCheckTaskDetailHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdIsNull() {
            addCriterion("task_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdIsNotNull() {
            addCriterion("task_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdEqualTo(Long value) {
            addCriterion("task_detail_id =", value, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdNotEqualTo(Long value) {
            addCriterion("task_detail_id <>", value, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdGreaterThan(Long value) {
            addCriterion("task_detail_id >", value, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_detail_id >=", value, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdLessThan(Long value) {
            addCriterion("task_detail_id <", value, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("task_detail_id <=", value, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdIn(List<Long> values) {
            addCriterion("task_detail_id in", values, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdNotIn(List<Long> values) {
            addCriterion("task_detail_id not in", values, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdBetween(Long value1, Long value2) {
            addCriterion("task_detail_id between", value1, value2, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andTaskDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("task_detail_id not between", value1, value2, "taskDetailId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdIsNull() {
            addCriterion("check_task_id is null");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdIsNotNull() {
            addCriterion("check_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdEqualTo(Long value) {
            addCriterion("check_task_id =", value, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdNotEqualTo(Long value) {
            addCriterion("check_task_id <>", value, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdGreaterThan(Long value) {
            addCriterion("check_task_id >", value, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("check_task_id >=", value, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdLessThan(Long value) {
            addCriterion("check_task_id <", value, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("check_task_id <=", value, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdIn(List<Long> values) {
            addCriterion("check_task_id in", values, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdNotIn(List<Long> values) {
            addCriterion("check_task_id not in", values, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdBetween(Long value1, Long value2) {
            addCriterion("check_task_id between", value1, value2, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andCheckTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("check_task_id not between", value1, value2, "checkTaskId");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIsNull() {
            addCriterion("taking_plan_no is null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIsNotNull() {
            addCriterion("taking_plan_no is not null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoEqualTo(String value) {
            addCriterion("taking_plan_no =", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotEqualTo(String value) {
            addCriterion("taking_plan_no <>", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoGreaterThan(String value) {
            addCriterion("taking_plan_no >", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoGreaterThanOrEqualTo(String value) {
            addCriterion("taking_plan_no >=", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLessThan(String value) {
            addCriterion("taking_plan_no <", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLessThanOrEqualTo(String value) {
            addCriterion("taking_plan_no <=", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLike(String value) {
            addCriterion("taking_plan_no like", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotLike(String value) {
            addCriterion("taking_plan_no not like", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIn(List<String> values) {
            addCriterion("taking_plan_no in", values, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotIn(List<String> values) {
            addCriterion("taking_plan_no not in", values, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoBetween(String value1, String value2) {
            addCriterion("taking_plan_no between", value1, value2, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotBetween(String value1, String value2) {
            addCriterion("taking_plan_no not between", value1, value2, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleIsNull() {
            addCriterion("check_people is null");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleIsNotNull() {
            addCriterion("check_people is not null");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleEqualTo(String value) {
            addCriterion("check_people =", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleNotEqualTo(String value) {
            addCriterion("check_people <>", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleGreaterThan(String value) {
            addCriterion("check_people >", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleGreaterThanOrEqualTo(String value) {
            addCriterion("check_people >=", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleLessThan(String value) {
            addCriterion("check_people <", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleLessThanOrEqualTo(String value) {
            addCriterion("check_people <=", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleLike(String value) {
            addCriterion("check_people like", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleNotLike(String value) {
            addCriterion("check_people not like", value, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleIn(List<String> values) {
            addCriterion("check_people in", values, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleNotIn(List<String> values) {
            addCriterion("check_people not in", values, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleBetween(String value1, String value2) {
            addCriterion("check_people between", value1, value2, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andCheckPeopleNotBetween(String value1, String value2) {
            addCriterion("check_people not between", value1, value2, "checkPeople");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionIsNull() {
            addCriterion("bus_large_region is null");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionIsNotNull() {
            addCriterion("bus_large_region is not null");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionEqualTo(String value) {
            addCriterion("bus_large_region =", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotEqualTo(String value) {
            addCriterion("bus_large_region <>", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionGreaterThan(String value) {
            addCriterion("bus_large_region >", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionGreaterThanOrEqualTo(String value) {
            addCriterion("bus_large_region >=", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionLessThan(String value) {
            addCriterion("bus_large_region <", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionLessThanOrEqualTo(String value) {
            addCriterion("bus_large_region <=", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionLike(String value) {
            addCriterion("bus_large_region like", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotLike(String value) {
            addCriterion("bus_large_region not like", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionIn(List<String> values) {
            addCriterion("bus_large_region in", values, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotIn(List<String> values) {
            addCriterion("bus_large_region not in", values, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionBetween(String value1, String value2) {
            addCriterion("bus_large_region between", value1, value2, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotBetween(String value1, String value2) {
            addCriterion("bus_large_region not between", value1, value2, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusCityIsNull() {
            addCriterion("bus_city is null");
            return (Criteria) this;
        }

        public Criteria andBusCityIsNotNull() {
            addCriterion("bus_city is not null");
            return (Criteria) this;
        }

        public Criteria andBusCityEqualTo(String value) {
            addCriterion("bus_city =", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotEqualTo(String value) {
            addCriterion("bus_city <>", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityGreaterThan(String value) {
            addCriterion("bus_city >", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityGreaterThanOrEqualTo(String value) {
            addCriterion("bus_city >=", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityLessThan(String value) {
            addCriterion("bus_city <", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityLessThanOrEqualTo(String value) {
            addCriterion("bus_city <=", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityLike(String value) {
            addCriterion("bus_city like", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotLike(String value) {
            addCriterion("bus_city not like", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityIn(List<String> values) {
            addCriterion("bus_city in", values, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotIn(List<String> values) {
            addCriterion("bus_city not in", values, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityBetween(String value1, String value2) {
            addCriterion("bus_city between", value1, value2, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotBetween(String value1, String value2) {
            addCriterion("bus_city not between", value1, value2, "busCity");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIsNull() {
            addCriterion("assets_keeper is null");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIsNotNull() {
            addCriterion("assets_keeper is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperEqualTo(String value) {
            addCriterion("assets_keeper =", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotEqualTo(String value) {
            addCriterion("assets_keeper <>", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperGreaterThan(String value) {
            addCriterion("assets_keeper >", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperGreaterThanOrEqualTo(String value) {
            addCriterion("assets_keeper >=", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLessThan(String value) {
            addCriterion("assets_keeper <", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLessThanOrEqualTo(String value) {
            addCriterion("assets_keeper <=", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLike(String value) {
            addCriterion("assets_keeper like", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotLike(String value) {
            addCriterion("assets_keeper not like", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIn(List<String> values) {
            addCriterion("assets_keeper in", values, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotIn(List<String> values) {
            addCriterion("assets_keeper not in", values, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperBetween(String value1, String value2) {
            addCriterion("assets_keeper between", value1, value2, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotBetween(String value1, String value2) {
            addCriterion("assets_keeper not between", value1, value2, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceIsNull() {
            addCriterion("holder_address_province is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceIsNotNull() {
            addCriterion("holder_address_province is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceEqualTo(String value) {
            addCriterion("holder_address_province =", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotEqualTo(String value) {
            addCriterion("holder_address_province <>", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceGreaterThan(String value) {
            addCriterion("holder_address_province >", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address_province >=", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceLessThan(String value) {
            addCriterion("holder_address_province <", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceLessThanOrEqualTo(String value) {
            addCriterion("holder_address_province <=", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceLike(String value) {
            addCriterion("holder_address_province like", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotLike(String value) {
            addCriterion("holder_address_province not like", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceIn(List<String> values) {
            addCriterion("holder_address_province in", values, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotIn(List<String> values) {
            addCriterion("holder_address_province not in", values, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceBetween(String value1, String value2) {
            addCriterion("holder_address_province between", value1, value2, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotBetween(String value1, String value2) {
            addCriterion("holder_address_province not between", value1, value2, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityIsNull() {
            addCriterion("holder_address_city is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityIsNotNull() {
            addCriterion("holder_address_city is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityEqualTo(String value) {
            addCriterion("holder_address_city =", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotEqualTo(String value) {
            addCriterion("holder_address_city <>", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityGreaterThan(String value) {
            addCriterion("holder_address_city >", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address_city >=", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityLessThan(String value) {
            addCriterion("holder_address_city <", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityLessThanOrEqualTo(String value) {
            addCriterion("holder_address_city <=", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityLike(String value) {
            addCriterion("holder_address_city like", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotLike(String value) {
            addCriterion("holder_address_city not like", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityIn(List<String> values) {
            addCriterion("holder_address_city in", values, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotIn(List<String> values) {
            addCriterion("holder_address_city not in", values, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityBetween(String value1, String value2) {
            addCriterion("holder_address_city between", value1, value2, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotBetween(String value1, String value2) {
            addCriterion("holder_address_city not between", value1, value2, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyIsNull() {
            addCriterion("holder_address_county is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyIsNotNull() {
            addCriterion("holder_address_county is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyEqualTo(String value) {
            addCriterion("holder_address_county =", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotEqualTo(String value) {
            addCriterion("holder_address_county <>", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyGreaterThan(String value) {
            addCriterion("holder_address_county >", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address_county >=", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyLessThan(String value) {
            addCriterion("holder_address_county <", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyLessThanOrEqualTo(String value) {
            addCriterion("holder_address_county <=", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyLike(String value) {
            addCriterion("holder_address_county like", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotLike(String value) {
            addCriterion("holder_address_county not like", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyIn(List<String> values) {
            addCriterion("holder_address_county in", values, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotIn(List<String> values) {
            addCriterion("holder_address_county not in", values, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyBetween(String value1, String value2) {
            addCriterion("holder_address_county between", value1, value2, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotBetween(String value1, String value2) {
            addCriterion("holder_address_county not between", value1, value2, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeIsNull() {
            addCriterion("snapshot_assets_code is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeIsNotNull() {
            addCriterion("snapshot_assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeEqualTo(String value) {
            addCriterion("snapshot_assets_code =", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeNotEqualTo(String value) {
            addCriterion("snapshot_assets_code <>", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeGreaterThan(String value) {
            addCriterion("snapshot_assets_code >", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_code >=", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeLessThan(String value) {
            addCriterion("snapshot_assets_code <", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_code <=", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeLike(String value) {
            addCriterion("snapshot_assets_code like", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeNotLike(String value) {
            addCriterion("snapshot_assets_code not like", value, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeIn(List<String> values) {
            addCriterion("snapshot_assets_code in", values, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeNotIn(List<String> values) {
            addCriterion("snapshot_assets_code not in", values, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeBetween(String value1, String value2) {
            addCriterion("snapshot_assets_code between", value1, value2, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("snapshot_assets_code not between", value1, value2, "snapshotAssetsCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameIsNull() {
            addCriterion("snapshot_assets_name is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameIsNotNull() {
            addCriterion("snapshot_assets_name is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameEqualTo(String value) {
            addCriterion("snapshot_assets_name =", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameNotEqualTo(String value) {
            addCriterion("snapshot_assets_name <>", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameGreaterThan(String value) {
            addCriterion("snapshot_assets_name >", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_name >=", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameLessThan(String value) {
            addCriterion("snapshot_assets_name <", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameLessThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_name <=", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameLike(String value) {
            addCriterion("snapshot_assets_name like", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameNotLike(String value) {
            addCriterion("snapshot_assets_name not like", value, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameIn(List<String> values) {
            addCriterion("snapshot_assets_name in", values, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameNotIn(List<String> values) {
            addCriterion("snapshot_assets_name not in", values, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameBetween(String value1, String value2) {
            addCriterion("snapshot_assets_name between", value1, value2, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsNameNotBetween(String value1, String value2) {
            addCriterion("snapshot_assets_name not between", value1, value2, "snapshotAssetsName");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoIsNull() {
            addCriterion("snapshot_sn_no is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoIsNotNull() {
            addCriterion("snapshot_sn_no is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoEqualTo(String value) {
            addCriterion("snapshot_sn_no =", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoNotEqualTo(String value) {
            addCriterion("snapshot_sn_no <>", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoGreaterThan(String value) {
            addCriterion("snapshot_sn_no >", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_sn_no >=", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoLessThan(String value) {
            addCriterion("snapshot_sn_no <", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoLessThanOrEqualTo(String value) {
            addCriterion("snapshot_sn_no <=", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoLike(String value) {
            addCriterion("snapshot_sn_no like", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoNotLike(String value) {
            addCriterion("snapshot_sn_no not like", value, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoIn(List<String> values) {
            addCriterion("snapshot_sn_no in", values, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoNotIn(List<String> values) {
            addCriterion("snapshot_sn_no not in", values, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoBetween(String value1, String value2) {
            addCriterion("snapshot_sn_no between", value1, value2, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotSnNoNotBetween(String value1, String value2) {
            addCriterion("snapshot_sn_no not between", value1, value2, "snapshotSnNo");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryIsNull() {
            addCriterion("snapshot_assets_category is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryIsNotNull() {
            addCriterion("snapshot_assets_category is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryEqualTo(String value) {
            addCriterion("snapshot_assets_category =", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryNotEqualTo(String value) {
            addCriterion("snapshot_assets_category <>", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryGreaterThan(String value) {
            addCriterion("snapshot_assets_category >", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_category >=", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryLessThan(String value) {
            addCriterion("snapshot_assets_category <", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryLessThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_category <=", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryLike(String value) {
            addCriterion("snapshot_assets_category like", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryNotLike(String value) {
            addCriterion("snapshot_assets_category not like", value, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryIn(List<String> values) {
            addCriterion("snapshot_assets_category in", values, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryNotIn(List<String> values) {
            addCriterion("snapshot_assets_category not in", values, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryBetween(String value1, String value2) {
            addCriterion("snapshot_assets_category between", value1, value2, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCategoryNotBetween(String value1, String value2) {
            addCriterion("snapshot_assets_category not between", value1, value2, "snapshotAssetsCategory");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusIsNull() {
            addCriterion("snapshot_assets_status is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusIsNotNull() {
            addCriterion("snapshot_assets_status is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusEqualTo(Integer value) {
            addCriterion("snapshot_assets_status =", value, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusNotEqualTo(Integer value) {
            addCriterion("snapshot_assets_status <>", value, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusGreaterThan(Integer value) {
            addCriterion("snapshot_assets_status >", value, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("snapshot_assets_status >=", value, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusLessThan(Integer value) {
            addCriterion("snapshot_assets_status <", value, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusLessThanOrEqualTo(Integer value) {
            addCriterion("snapshot_assets_status <=", value, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusIn(List<Integer> values) {
            addCriterion("snapshot_assets_status in", values, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusNotIn(List<Integer> values) {
            addCriterion("snapshot_assets_status not in", values, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusBetween(Integer value1, Integer value2) {
            addCriterion("snapshot_assets_status between", value1, value2, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("snapshot_assets_status not between", value1, value2, "snapshotAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsIsNull() {
            addCriterion("snapshot_assets_conditions is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsIsNotNull() {
            addCriterion("snapshot_assets_conditions is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsEqualTo(Integer value) {
            addCriterion("snapshot_assets_conditions =", value, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsNotEqualTo(Integer value) {
            addCriterion("snapshot_assets_conditions <>", value, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsGreaterThan(Integer value) {
            addCriterion("snapshot_assets_conditions >", value, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsGreaterThanOrEqualTo(Integer value) {
            addCriterion("snapshot_assets_conditions >=", value, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsLessThan(Integer value) {
            addCriterion("snapshot_assets_conditions <", value, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsLessThanOrEqualTo(Integer value) {
            addCriterion("snapshot_assets_conditions <=", value, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsIn(List<Integer> values) {
            addCriterion("snapshot_assets_conditions in", values, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsNotIn(List<Integer> values) {
            addCriterion("snapshot_assets_conditions not in", values, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsBetween(Integer value1, Integer value2) {
            addCriterion("snapshot_assets_conditions between", value1, value2, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsConditionsNotBetween(Integer value1, Integer value2) {
            addCriterion("snapshot_assets_conditions not between", value1, value2, "snapshotAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderIsNull() {
            addCriterion("snapshot_assets_holder is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderIsNotNull() {
            addCriterion("snapshot_assets_holder is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderEqualTo(String value) {
            addCriterion("snapshot_assets_holder =", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderNotEqualTo(String value) {
            addCriterion("snapshot_assets_holder <>", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderGreaterThan(String value) {
            addCriterion("snapshot_assets_holder >", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_holder >=", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderLessThan(String value) {
            addCriterion("snapshot_assets_holder <", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderLessThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_holder <=", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderLike(String value) {
            addCriterion("snapshot_assets_holder like", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderNotLike(String value) {
            addCriterion("snapshot_assets_holder not like", value, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderIn(List<String> values) {
            addCriterion("snapshot_assets_holder in", values, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderNotIn(List<String> values) {
            addCriterion("snapshot_assets_holder not in", values, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderBetween(String value1, String value2) {
            addCriterion("snapshot_assets_holder between", value1, value2, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsHolderNotBetween(String value1, String value2) {
            addCriterion("snapshot_assets_holder not between", value1, value2, "snapshotAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeIsNull() {
            addCriterion("snapshot_holder_time is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeIsNotNull() {
            addCriterion("snapshot_holder_time is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeEqualTo(Date value) {
            addCriterion("snapshot_holder_time =", value, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeNotEqualTo(Date value) {
            addCriterion("snapshot_holder_time <>", value, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeGreaterThan(Date value) {
            addCriterion("snapshot_holder_time >", value, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("snapshot_holder_time >=", value, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeLessThan(Date value) {
            addCriterion("snapshot_holder_time <", value, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeLessThanOrEqualTo(Date value) {
            addCriterion("snapshot_holder_time <=", value, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeIn(List<Date> values) {
            addCriterion("snapshot_holder_time in", values, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeNotIn(List<Date> values) {
            addCriterion("snapshot_holder_time not in", values, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeBetween(Date value1, Date value2) {
            addCriterion("snapshot_holder_time between", value1, value2, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderTimeNotBetween(Date value1, Date value2) {
            addCriterion("snapshot_holder_time not between", value1, value2, "snapshotHolderTime");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressIsNull() {
            addCriterion("snapshot_holder_address is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressIsNotNull() {
            addCriterion("snapshot_holder_address is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressEqualTo(String value) {
            addCriterion("snapshot_holder_address =", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressNotEqualTo(String value) {
            addCriterion("snapshot_holder_address <>", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressGreaterThan(String value) {
            addCriterion("snapshot_holder_address >", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_holder_address >=", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressLessThan(String value) {
            addCriterion("snapshot_holder_address <", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressLessThanOrEqualTo(String value) {
            addCriterion("snapshot_holder_address <=", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressLike(String value) {
            addCriterion("snapshot_holder_address like", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressNotLike(String value) {
            addCriterion("snapshot_holder_address not like", value, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressIn(List<String> values) {
            addCriterion("snapshot_holder_address in", values, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressNotIn(List<String> values) {
            addCriterion("snapshot_holder_address not in", values, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressBetween(String value1, String value2) {
            addCriterion("snapshot_holder_address between", value1, value2, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotHolderAddressNotBetween(String value1, String value2) {
            addCriterion("snapshot_holder_address not between", value1, value2, "snapshotHolderAddress");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeIsNull() {
            addCriterion("snapshot_warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeIsNotNull() {
            addCriterion("snapshot_warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeEqualTo(String value) {
            addCriterion("snapshot_warehouse_code =", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeNotEqualTo(String value) {
            addCriterion("snapshot_warehouse_code <>", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeGreaterThan(String value) {
            addCriterion("snapshot_warehouse_code >", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_warehouse_code >=", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeLessThan(String value) {
            addCriterion("snapshot_warehouse_code <", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("snapshot_warehouse_code <=", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeLike(String value) {
            addCriterion("snapshot_warehouse_code like", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeNotLike(String value) {
            addCriterion("snapshot_warehouse_code not like", value, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeIn(List<String> values) {
            addCriterion("snapshot_warehouse_code in", values, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeNotIn(List<String> values) {
            addCriterion("snapshot_warehouse_code not in", values, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeBetween(String value1, String value2) {
            addCriterion("snapshot_warehouse_code between", value1, value2, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("snapshot_warehouse_code not between", value1, value2, "snapshotWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelIsNull() {
            addCriterion("snapshot_model is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelIsNotNull() {
            addCriterion("snapshot_model is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelEqualTo(String value) {
            addCriterion("snapshot_model =", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelNotEqualTo(String value) {
            addCriterion("snapshot_model <>", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelGreaterThan(String value) {
            addCriterion("snapshot_model >", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_model >=", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelLessThan(String value) {
            addCriterion("snapshot_model <", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelLessThanOrEqualTo(String value) {
            addCriterion("snapshot_model <=", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelLike(String value) {
            addCriterion("snapshot_model like", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelNotLike(String value) {
            addCriterion("snapshot_model not like", value, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelIn(List<String> values) {
            addCriterion("snapshot_model in", values, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelNotIn(List<String> values) {
            addCriterion("snapshot_model not in", values, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelBetween(String value1, String value2) {
            addCriterion("snapshot_model between", value1, value2, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotModelNotBetween(String value1, String value2) {
            addCriterion("snapshot_model not between", value1, value2, "snapshotModel");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandIsNull() {
            addCriterion("snapshot_brand is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandIsNotNull() {
            addCriterion("snapshot_brand is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandEqualTo(String value) {
            addCriterion("snapshot_brand =", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandNotEqualTo(String value) {
            addCriterion("snapshot_brand <>", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandGreaterThan(String value) {
            addCriterion("snapshot_brand >", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_brand >=", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandLessThan(String value) {
            addCriterion("snapshot_brand <", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandLessThanOrEqualTo(String value) {
            addCriterion("snapshot_brand <=", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandLike(String value) {
            addCriterion("snapshot_brand like", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandNotLike(String value) {
            addCriterion("snapshot_brand not like", value, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandIn(List<String> values) {
            addCriterion("snapshot_brand in", values, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandNotIn(List<String> values) {
            addCriterion("snapshot_brand not in", values, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandBetween(String value1, String value2) {
            addCriterion("snapshot_brand between", value1, value2, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotBrandNotBetween(String value1, String value2) {
            addCriterion("snapshot_brand not between", value1, value2, "snapshotBrand");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuIsNull() {
            addCriterion("snapshot_assets_cpu is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuIsNotNull() {
            addCriterion("snapshot_assets_cpu is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuEqualTo(String value) {
            addCriterion("snapshot_assets_cpu =", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuNotEqualTo(String value) {
            addCriterion("snapshot_assets_cpu <>", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuGreaterThan(String value) {
            addCriterion("snapshot_assets_cpu >", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_cpu >=", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuLessThan(String value) {
            addCriterion("snapshot_assets_cpu <", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuLessThanOrEqualTo(String value) {
            addCriterion("snapshot_assets_cpu <=", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuLike(String value) {
            addCriterion("snapshot_assets_cpu like", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuNotLike(String value) {
            addCriterion("snapshot_assets_cpu not like", value, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuIn(List<String> values) {
            addCriterion("snapshot_assets_cpu in", values, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuNotIn(List<String> values) {
            addCriterion("snapshot_assets_cpu not in", values, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuBetween(String value1, String value2) {
            addCriterion("snapshot_assets_cpu between", value1, value2, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotAssetsCpuNotBetween(String value1, String value2) {
            addCriterion("snapshot_assets_cpu not between", value1, value2, "snapshotAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskIsNull() {
            addCriterion("snapshot_hard_disk is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskIsNotNull() {
            addCriterion("snapshot_hard_disk is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskEqualTo(String value) {
            addCriterion("snapshot_hard_disk =", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskNotEqualTo(String value) {
            addCriterion("snapshot_hard_disk <>", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskGreaterThan(String value) {
            addCriterion("snapshot_hard_disk >", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_hard_disk >=", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskLessThan(String value) {
            addCriterion("snapshot_hard_disk <", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskLessThanOrEqualTo(String value) {
            addCriterion("snapshot_hard_disk <=", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskLike(String value) {
            addCriterion("snapshot_hard_disk like", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskNotLike(String value) {
            addCriterion("snapshot_hard_disk not like", value, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskIn(List<String> values) {
            addCriterion("snapshot_hard_disk in", values, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskNotIn(List<String> values) {
            addCriterion("snapshot_hard_disk not in", values, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskBetween(String value1, String value2) {
            addCriterion("snapshot_hard_disk between", value1, value2, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotHardDiskNotBetween(String value1, String value2) {
            addCriterion("snapshot_hard_disk not between", value1, value2, "snapshotHardDisk");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryIsNull() {
            addCriterion("snapshot_ram_memory is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryIsNotNull() {
            addCriterion("snapshot_ram_memory is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryEqualTo(String value) {
            addCriterion("snapshot_ram_memory =", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryNotEqualTo(String value) {
            addCriterion("snapshot_ram_memory <>", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryGreaterThan(String value) {
            addCriterion("snapshot_ram_memory >", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryGreaterThanOrEqualTo(String value) {
            addCriterion("snapshot_ram_memory >=", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryLessThan(String value) {
            addCriterion("snapshot_ram_memory <", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryLessThanOrEqualTo(String value) {
            addCriterion("snapshot_ram_memory <=", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryLike(String value) {
            addCriterion("snapshot_ram_memory like", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryNotLike(String value) {
            addCriterion("snapshot_ram_memory not like", value, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryIn(List<String> values) {
            addCriterion("snapshot_ram_memory in", values, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryNotIn(List<String> values) {
            addCriterion("snapshot_ram_memory not in", values, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryBetween(String value1, String value2) {
            addCriterion("snapshot_ram_memory between", value1, value2, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andSnapshotRamMemoryNotBetween(String value1, String value2) {
            addCriterion("snapshot_ram_memory not between", value1, value2, "snapshotRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuIsNull() {
            addCriterion("real_assets_cpu is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuIsNotNull() {
            addCriterion("real_assets_cpu is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuEqualTo(String value) {
            addCriterion("real_assets_cpu =", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuNotEqualTo(String value) {
            addCriterion("real_assets_cpu <>", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuGreaterThan(String value) {
            addCriterion("real_assets_cpu >", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_cpu >=", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuLessThan(String value) {
            addCriterion("real_assets_cpu <", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuLessThanOrEqualTo(String value) {
            addCriterion("real_assets_cpu <=", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuLike(String value) {
            addCriterion("real_assets_cpu like", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuNotLike(String value) {
            addCriterion("real_assets_cpu not like", value, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuIn(List<String> values) {
            addCriterion("real_assets_cpu in", values, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuNotIn(List<String> values) {
            addCriterion("real_assets_cpu not in", values, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuBetween(String value1, String value2) {
            addCriterion("real_assets_cpu between", value1, value2, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCpuNotBetween(String value1, String value2) {
            addCriterion("real_assets_cpu not between", value1, value2, "realAssetsCpu");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskIsNull() {
            addCriterion("real_hard_disk is null");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskIsNotNull() {
            addCriterion("real_hard_disk is not null");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskEqualTo(String value) {
            addCriterion("real_hard_disk =", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskNotEqualTo(String value) {
            addCriterion("real_hard_disk <>", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskGreaterThan(String value) {
            addCriterion("real_hard_disk >", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskGreaterThanOrEqualTo(String value) {
            addCriterion("real_hard_disk >=", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskLessThan(String value) {
            addCriterion("real_hard_disk <", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskLessThanOrEqualTo(String value) {
            addCriterion("real_hard_disk <=", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskLike(String value) {
            addCriterion("real_hard_disk like", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskNotLike(String value) {
            addCriterion("real_hard_disk not like", value, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskIn(List<String> values) {
            addCriterion("real_hard_disk in", values, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskNotIn(List<String> values) {
            addCriterion("real_hard_disk not in", values, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskBetween(String value1, String value2) {
            addCriterion("real_hard_disk between", value1, value2, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealHardDiskNotBetween(String value1, String value2) {
            addCriterion("real_hard_disk not between", value1, value2, "realHardDisk");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryIsNull() {
            addCriterion("real_ram_memory is null");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryIsNotNull() {
            addCriterion("real_ram_memory is not null");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryEqualTo(String value) {
            addCriterion("real_ram_memory =", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryNotEqualTo(String value) {
            addCriterion("real_ram_memory <>", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryGreaterThan(String value) {
            addCriterion("real_ram_memory >", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryGreaterThanOrEqualTo(String value) {
            addCriterion("real_ram_memory >=", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryLessThan(String value) {
            addCriterion("real_ram_memory <", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryLessThanOrEqualTo(String value) {
            addCriterion("real_ram_memory <=", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryLike(String value) {
            addCriterion("real_ram_memory like", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryNotLike(String value) {
            addCriterion("real_ram_memory not like", value, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryIn(List<String> values) {
            addCriterion("real_ram_memory in", values, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryNotIn(List<String> values) {
            addCriterion("real_ram_memory not in", values, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryBetween(String value1, String value2) {
            addCriterion("real_ram_memory between", value1, value2, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealRamMemoryNotBetween(String value1, String value2) {
            addCriterion("real_ram_memory not between", value1, value2, "realRamMemory");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeIsNull() {
            addCriterion("real_assets_code is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeIsNotNull() {
            addCriterion("real_assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeEqualTo(String value) {
            addCriterion("real_assets_code =", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeNotEqualTo(String value) {
            addCriterion("real_assets_code <>", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeGreaterThan(String value) {
            addCriterion("real_assets_code >", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_code >=", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeLessThan(String value) {
            addCriterion("real_assets_code <", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("real_assets_code <=", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeLike(String value) {
            addCriterion("real_assets_code like", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeNotLike(String value) {
            addCriterion("real_assets_code not like", value, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeIn(List<String> values) {
            addCriterion("real_assets_code in", values, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeNotIn(List<String> values) {
            addCriterion("real_assets_code not in", values, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeBetween(String value1, String value2) {
            addCriterion("real_assets_code between", value1, value2, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("real_assets_code not between", value1, value2, "realAssetsCode");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameIsNull() {
            addCriterion("real_assets_name is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameIsNotNull() {
            addCriterion("real_assets_name is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameEqualTo(String value) {
            addCriterion("real_assets_name =", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameNotEqualTo(String value) {
            addCriterion("real_assets_name <>", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameGreaterThan(String value) {
            addCriterion("real_assets_name >", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_name >=", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameLessThan(String value) {
            addCriterion("real_assets_name <", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameLessThanOrEqualTo(String value) {
            addCriterion("real_assets_name <=", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameLike(String value) {
            addCriterion("real_assets_name like", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameNotLike(String value) {
            addCriterion("real_assets_name not like", value, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameIn(List<String> values) {
            addCriterion("real_assets_name in", values, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameNotIn(List<String> values) {
            addCriterion("real_assets_name not in", values, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameBetween(String value1, String value2) {
            addCriterion("real_assets_name between", value1, value2, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsNameNotBetween(String value1, String value2) {
            addCriterion("real_assets_name not between", value1, value2, "realAssetsName");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusIsNull() {
            addCriterion("real_assets_status is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusIsNotNull() {
            addCriterion("real_assets_status is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusEqualTo(Integer value) {
            addCriterion("real_assets_status =", value, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusNotEqualTo(Integer value) {
            addCriterion("real_assets_status <>", value, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusGreaterThan(Integer value) {
            addCriterion("real_assets_status >", value, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("real_assets_status >=", value, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusLessThan(Integer value) {
            addCriterion("real_assets_status <", value, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusLessThanOrEqualTo(Integer value) {
            addCriterion("real_assets_status <=", value, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusIn(List<Integer> values) {
            addCriterion("real_assets_status in", values, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusNotIn(List<Integer> values) {
            addCriterion("real_assets_status not in", values, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusBetween(Integer value1, Integer value2) {
            addCriterion("real_assets_status between", value1, value2, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("real_assets_status not between", value1, value2, "realAssetsStatus");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsIsNull() {
            addCriterion("real_assets_conditions is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsIsNotNull() {
            addCriterion("real_assets_conditions is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsEqualTo(Integer value) {
            addCriterion("real_assets_conditions =", value, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsNotEqualTo(Integer value) {
            addCriterion("real_assets_conditions <>", value, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsGreaterThan(Integer value) {
            addCriterion("real_assets_conditions >", value, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsGreaterThanOrEqualTo(Integer value) {
            addCriterion("real_assets_conditions >=", value, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsLessThan(Integer value) {
            addCriterion("real_assets_conditions <", value, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsLessThanOrEqualTo(Integer value) {
            addCriterion("real_assets_conditions <=", value, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsIn(List<Integer> values) {
            addCriterion("real_assets_conditions in", values, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsNotIn(List<Integer> values) {
            addCriterion("real_assets_conditions not in", values, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsBetween(Integer value1, Integer value2) {
            addCriterion("real_assets_conditions between", value1, value2, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsConditionsNotBetween(Integer value1, Integer value2) {
            addCriterion("real_assets_conditions not between", value1, value2, "realAssetsConditions");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoIsNull() {
            addCriterion("real_assets_snno is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoIsNotNull() {
            addCriterion("real_assets_snno is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoEqualTo(String value) {
            addCriterion("real_assets_snno =", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoNotEqualTo(String value) {
            addCriterion("real_assets_snno <>", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoGreaterThan(String value) {
            addCriterion("real_assets_snno >", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_snno >=", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoLessThan(String value) {
            addCriterion("real_assets_snno <", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoLessThanOrEqualTo(String value) {
            addCriterion("real_assets_snno <=", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoLike(String value) {
            addCriterion("real_assets_snno like", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoNotLike(String value) {
            addCriterion("real_assets_snno not like", value, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoIn(List<String> values) {
            addCriterion("real_assets_snno in", values, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoNotIn(List<String> values) {
            addCriterion("real_assets_snno not in", values, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoBetween(String value1, String value2) {
            addCriterion("real_assets_snno between", value1, value2, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsSnnoNotBetween(String value1, String value2) {
            addCriterion("real_assets_snno not between", value1, value2, "realAssetsSnno");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelIsNull() {
            addCriterion("real_assets_model is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelIsNotNull() {
            addCriterion("real_assets_model is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelEqualTo(String value) {
            addCriterion("real_assets_model =", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelNotEqualTo(String value) {
            addCriterion("real_assets_model <>", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelGreaterThan(String value) {
            addCriterion("real_assets_model >", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_model >=", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelLessThan(String value) {
            addCriterion("real_assets_model <", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelLessThanOrEqualTo(String value) {
            addCriterion("real_assets_model <=", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelLike(String value) {
            addCriterion("real_assets_model like", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelNotLike(String value) {
            addCriterion("real_assets_model not like", value, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelIn(List<String> values) {
            addCriterion("real_assets_model in", values, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelNotIn(List<String> values) {
            addCriterion("real_assets_model not in", values, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelBetween(String value1, String value2) {
            addCriterion("real_assets_model between", value1, value2, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsModelNotBetween(String value1, String value2) {
            addCriterion("real_assets_model not between", value1, value2, "realAssetsModel");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandIsNull() {
            addCriterion("real_assets_brand is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandIsNotNull() {
            addCriterion("real_assets_brand is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandEqualTo(String value) {
            addCriterion("real_assets_brand =", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandNotEqualTo(String value) {
            addCriterion("real_assets_brand <>", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandGreaterThan(String value) {
            addCriterion("real_assets_brand >", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_brand >=", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandLessThan(String value) {
            addCriterion("real_assets_brand <", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandLessThanOrEqualTo(String value) {
            addCriterion("real_assets_brand <=", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandLike(String value) {
            addCriterion("real_assets_brand like", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandNotLike(String value) {
            addCriterion("real_assets_brand not like", value, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandIn(List<String> values) {
            addCriterion("real_assets_brand in", values, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandNotIn(List<String> values) {
            addCriterion("real_assets_brand not in", values, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandBetween(String value1, String value2) {
            addCriterion("real_assets_brand between", value1, value2, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsBrandNotBetween(String value1, String value2) {
            addCriterion("real_assets_brand not between", value1, value2, "realAssetsBrand");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderIsNull() {
            addCriterion("real_assets_holder is null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderIsNotNull() {
            addCriterion("real_assets_holder is not null");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderEqualTo(String value) {
            addCriterion("real_assets_holder =", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderNotEqualTo(String value) {
            addCriterion("real_assets_holder <>", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderGreaterThan(String value) {
            addCriterion("real_assets_holder >", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderGreaterThanOrEqualTo(String value) {
            addCriterion("real_assets_holder >=", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderLessThan(String value) {
            addCriterion("real_assets_holder <", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderLessThanOrEqualTo(String value) {
            addCriterion("real_assets_holder <=", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderLike(String value) {
            addCriterion("real_assets_holder like", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderNotLike(String value) {
            addCriterion("real_assets_holder not like", value, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderIn(List<String> values) {
            addCriterion("real_assets_holder in", values, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderNotIn(List<String> values) {
            addCriterion("real_assets_holder not in", values, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderBetween(String value1, String value2) {
            addCriterion("real_assets_holder between", value1, value2, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealAssetsHolderNotBetween(String value1, String value2) {
            addCriterion("real_assets_holder not between", value1, value2, "realAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeIsNull() {
            addCriterion("real_holder_time is null");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeIsNotNull() {
            addCriterion("real_holder_time is not null");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeEqualTo(Date value) {
            addCriterion("real_holder_time =", value, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeNotEqualTo(Date value) {
            addCriterion("real_holder_time <>", value, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeGreaterThan(Date value) {
            addCriterion("real_holder_time >", value, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("real_holder_time >=", value, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeLessThan(Date value) {
            addCriterion("real_holder_time <", value, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeLessThanOrEqualTo(Date value) {
            addCriterion("real_holder_time <=", value, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeIn(List<Date> values) {
            addCriterion("real_holder_time in", values, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeNotIn(List<Date> values) {
            addCriterion("real_holder_time not in", values, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeBetween(Date value1, Date value2) {
            addCriterion("real_holder_time between", value1, value2, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderTimeNotBetween(Date value1, Date value2) {
            addCriterion("real_holder_time not between", value1, value2, "realHolderTime");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressIsNull() {
            addCriterion("real_holder_address is null");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressIsNotNull() {
            addCriterion("real_holder_address is not null");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressEqualTo(String value) {
            addCriterion("real_holder_address =", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressNotEqualTo(String value) {
            addCriterion("real_holder_address <>", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressGreaterThan(String value) {
            addCriterion("real_holder_address >", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("real_holder_address >=", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressLessThan(String value) {
            addCriterion("real_holder_address <", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressLessThanOrEqualTo(String value) {
            addCriterion("real_holder_address <=", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressLike(String value) {
            addCriterion("real_holder_address like", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressNotLike(String value) {
            addCriterion("real_holder_address not like", value, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressIn(List<String> values) {
            addCriterion("real_holder_address in", values, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressNotIn(List<String> values) {
            addCriterion("real_holder_address not in", values, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressBetween(String value1, String value2) {
            addCriterion("real_holder_address between", value1, value2, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealHolderAddressNotBetween(String value1, String value2) {
            addCriterion("real_holder_address not between", value1, value2, "realHolderAddress");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeIsNull() {
            addCriterion("real_warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeIsNotNull() {
            addCriterion("real_warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeEqualTo(String value) {
            addCriterion("real_warehouse_code =", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotEqualTo(String value) {
            addCriterion("real_warehouse_code <>", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeGreaterThan(String value) {
            addCriterion("real_warehouse_code >", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("real_warehouse_code >=", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeLessThan(String value) {
            addCriterion("real_warehouse_code <", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("real_warehouse_code <=", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeLike(String value) {
            addCriterion("real_warehouse_code like", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotLike(String value) {
            addCriterion("real_warehouse_code not like", value, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeIn(List<String> values) {
            addCriterion("real_warehouse_code in", values, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotIn(List<String> values) {
            addCriterion("real_warehouse_code not in", values, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeBetween(String value1, String value2) {
            addCriterion("real_warehouse_code between", value1, value2, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("real_warehouse_code not between", value1, value2, "realWarehouseCode");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlIsNull() {
            addCriterion("real_pictures_url is null");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlIsNotNull() {
            addCriterion("real_pictures_url is not null");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlEqualTo(String value) {
            addCriterion("real_pictures_url =", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlNotEqualTo(String value) {
            addCriterion("real_pictures_url <>", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlGreaterThan(String value) {
            addCriterion("real_pictures_url >", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlGreaterThanOrEqualTo(String value) {
            addCriterion("real_pictures_url >=", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlLessThan(String value) {
            addCriterion("real_pictures_url <", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlLessThanOrEqualTo(String value) {
            addCriterion("real_pictures_url <=", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlLike(String value) {
            addCriterion("real_pictures_url like", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlNotLike(String value) {
            addCriterion("real_pictures_url not like", value, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlIn(List<String> values) {
            addCriterion("real_pictures_url in", values, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlNotIn(List<String> values) {
            addCriterion("real_pictures_url not in", values, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlBetween(String value1, String value2) {
            addCriterion("real_pictures_url between", value1, value2, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andRealPicturesUrlNotBetween(String value1, String value2) {
            addCriterion("real_pictures_url not between", value1, value2, "realPicturesUrl");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIsNull() {
            addCriterion("need_dept is null");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIsNotNull() {
            addCriterion("need_dept is not null");
            return (Criteria) this;
        }

        public Criteria andNeedDeptEqualTo(String value) {
            addCriterion("need_dept =", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotEqualTo(String value) {
            addCriterion("need_dept <>", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptGreaterThan(String value) {
            addCriterion("need_dept >", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptGreaterThanOrEqualTo(String value) {
            addCriterion("need_dept >=", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLessThan(String value) {
            addCriterion("need_dept <", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLessThanOrEqualTo(String value) {
            addCriterion("need_dept <=", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLike(String value) {
            addCriterion("need_dept like", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotLike(String value) {
            addCriterion("need_dept not like", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIn(List<String> values) {
            addCriterion("need_dept in", values, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotIn(List<String> values) {
            addCriterion("need_dept not in", values, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptBetween(String value1, String value2) {
            addCriterion("need_dept between", value1, value2, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotBetween(String value1, String value2) {
            addCriterion("need_dept not between", value1, value2, "needDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptIsNull() {
            addCriterion("cost_dept is null");
            return (Criteria) this;
        }

        public Criteria andCostDeptIsNotNull() {
            addCriterion("cost_dept is not null");
            return (Criteria) this;
        }

        public Criteria andCostDeptEqualTo(String value) {
            addCriterion("cost_dept =", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotEqualTo(String value) {
            addCriterion("cost_dept <>", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptGreaterThan(String value) {
            addCriterion("cost_dept >", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptGreaterThanOrEqualTo(String value) {
            addCriterion("cost_dept >=", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptLessThan(String value) {
            addCriterion("cost_dept <", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptLessThanOrEqualTo(String value) {
            addCriterion("cost_dept <=", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptLike(String value) {
            addCriterion("cost_dept like", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotLike(String value) {
            addCriterion("cost_dept not like", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptIn(List<String> values) {
            addCriterion("cost_dept in", values, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotIn(List<String> values) {
            addCriterion("cost_dept not in", values, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptBetween(String value1, String value2) {
            addCriterion("cost_dept between", value1, value2, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotBetween(String value1, String value2) {
            addCriterion("cost_dept not between", value1, value2, "costDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptIsNull() {
            addCriterion("holder_dept is null");
            return (Criteria) this;
        }

        public Criteria andHolderDeptIsNotNull() {
            addCriterion("holder_dept is not null");
            return (Criteria) this;
        }

        public Criteria andHolderDeptEqualTo(String value) {
            addCriterion("holder_dept =", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotEqualTo(String value) {
            addCriterion("holder_dept <>", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptGreaterThan(String value) {
            addCriterion("holder_dept >", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptGreaterThanOrEqualTo(String value) {
            addCriterion("holder_dept >=", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptLessThan(String value) {
            addCriterion("holder_dept <", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptLessThanOrEqualTo(String value) {
            addCriterion("holder_dept <=", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptLike(String value) {
            addCriterion("holder_dept like", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotLike(String value) {
            addCriterion("holder_dept not like", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptIn(List<String> values) {
            addCriterion("holder_dept in", values, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotIn(List<String> values) {
            addCriterion("holder_dept not in", values, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptBetween(String value1, String value2) {
            addCriterion("holder_dept between", value1, value2, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotBetween(String value1, String value2) {
            addCriterion("holder_dept not between", value1, value2, "holderDept");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberIsNull() {
            addCriterion("snapshot_number is null");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberIsNotNull() {
            addCriterion("snapshot_number is not null");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberEqualTo(Integer value) {
            addCriterion("snapshot_number =", value, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberNotEqualTo(Integer value) {
            addCriterion("snapshot_number <>", value, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberGreaterThan(Integer value) {
            addCriterion("snapshot_number >", value, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("snapshot_number >=", value, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberLessThan(Integer value) {
            addCriterion("snapshot_number <", value, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberLessThanOrEqualTo(Integer value) {
            addCriterion("snapshot_number <=", value, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberIn(List<Integer> values) {
            addCriterion("snapshot_number in", values, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberNotIn(List<Integer> values) {
            addCriterion("snapshot_number not in", values, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberBetween(Integer value1, Integer value2) {
            addCriterion("snapshot_number between", value1, value2, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andSnapshotNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("snapshot_number not between", value1, value2, "snapshotNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberIsNull() {
            addCriterion("real_number is null");
            return (Criteria) this;
        }

        public Criteria andRealNumberIsNotNull() {
            addCriterion("real_number is not null");
            return (Criteria) this;
        }

        public Criteria andRealNumberEqualTo(Integer value) {
            addCriterion("real_number =", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberNotEqualTo(Integer value) {
            addCriterion("real_number <>", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberGreaterThan(Integer value) {
            addCriterion("real_number >", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("real_number >=", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberLessThan(Integer value) {
            addCriterion("real_number <", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberLessThanOrEqualTo(Integer value) {
            addCriterion("real_number <=", value, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberIn(List<Integer> values) {
            addCriterion("real_number in", values, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberNotIn(List<Integer> values) {
            addCriterion("real_number not in", values, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberBetween(Integer value1, Integer value2) {
            addCriterion("real_number between", value1, value2, "realNumber");
            return (Criteria) this;
        }

        public Criteria andRealNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("real_number not between", value1, value2, "realNumber");
            return (Criteria) this;
        }

        public Criteria andDifferenceIsNull() {
            addCriterion("difference is null");
            return (Criteria) this;
        }

        public Criteria andDifferenceIsNotNull() {
            addCriterion("difference is not null");
            return (Criteria) this;
        }

        public Criteria andDifferenceEqualTo(Integer value) {
            addCriterion("difference =", value, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceNotEqualTo(Integer value) {
            addCriterion("difference <>", value, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceGreaterThan(Integer value) {
            addCriterion("difference >", value, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceGreaterThanOrEqualTo(Integer value) {
            addCriterion("difference >=", value, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceLessThan(Integer value) {
            addCriterion("difference <", value, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceLessThanOrEqualTo(Integer value) {
            addCriterion("difference <=", value, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceIn(List<Integer> values) {
            addCriterion("difference in", values, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceNotIn(List<Integer> values) {
            addCriterion("difference not in", values, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceBetween(Integer value1, Integer value2) {
            addCriterion("difference between", value1, value2, "difference");
            return (Criteria) this;
        }

        public Criteria andDifferenceNotBetween(Integer value1, Integer value2) {
            addCriterion("difference not between", value1, value2, "difference");
            return (Criteria) this;
        }

        public Criteria andOwnFlagIsNull() {
            addCriterion("own_flag is null");
            return (Criteria) this;
        }

        public Criteria andOwnFlagIsNotNull() {
            addCriterion("own_flag is not null");
            return (Criteria) this;
        }

        public Criteria andOwnFlagEqualTo(Integer value) {
            addCriterion("own_flag =", value, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagNotEqualTo(Integer value) {
            addCriterion("own_flag <>", value, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagGreaterThan(Integer value) {
            addCriterion("own_flag >", value, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("own_flag >=", value, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagLessThan(Integer value) {
            addCriterion("own_flag <", value, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagLessThanOrEqualTo(Integer value) {
            addCriterion("own_flag <=", value, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagIn(List<Integer> values) {
            addCriterion("own_flag in", values, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagNotIn(List<Integer> values) {
            addCriterion("own_flag not in", values, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagBetween(Integer value1, Integer value2) {
            addCriterion("own_flag between", value1, value2, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andOwnFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("own_flag not between", value1, value2, "ownFlag");
            return (Criteria) this;
        }

        public Criteria andErrMessageIsNull() {
            addCriterion("err_message is null");
            return (Criteria) this;
        }

        public Criteria andErrMessageIsNotNull() {
            addCriterion("err_message is not null");
            return (Criteria) this;
        }

        public Criteria andErrMessageEqualTo(String value) {
            addCriterion("err_message =", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageNotEqualTo(String value) {
            addCriterion("err_message <>", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageGreaterThan(String value) {
            addCriterion("err_message >", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageGreaterThanOrEqualTo(String value) {
            addCriterion("err_message >=", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageLessThan(String value) {
            addCriterion("err_message <", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageLessThanOrEqualTo(String value) {
            addCriterion("err_message <=", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageLike(String value) {
            addCriterion("err_message like", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageNotLike(String value) {
            addCriterion("err_message not like", value, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageIn(List<String> values) {
            addCriterion("err_message in", values, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageNotIn(List<String> values) {
            addCriterion("err_message not in", values, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageBetween(String value1, String value2) {
            addCriterion("err_message between", value1, value2, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrMessageNotBetween(String value1, String value2) {
            addCriterion("err_message not between", value1, value2, "errMessage");
            return (Criteria) this;
        }

        public Criteria andErrDescIsNull() {
            addCriterion("err_desc is null");
            return (Criteria) this;
        }

        public Criteria andErrDescIsNotNull() {
            addCriterion("err_desc is not null");
            return (Criteria) this;
        }

        public Criteria andErrDescEqualTo(String value) {
            addCriterion("err_desc =", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescNotEqualTo(String value) {
            addCriterion("err_desc <>", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescGreaterThan(String value) {
            addCriterion("err_desc >", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescGreaterThanOrEqualTo(String value) {
            addCriterion("err_desc >=", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescLessThan(String value) {
            addCriterion("err_desc <", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescLessThanOrEqualTo(String value) {
            addCriterion("err_desc <=", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescLike(String value) {
            addCriterion("err_desc like", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescNotLike(String value) {
            addCriterion("err_desc not like", value, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescIn(List<String> values) {
            addCriterion("err_desc in", values, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescNotIn(List<String> values) {
            addCriterion("err_desc not in", values, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescBetween(String value1, String value2) {
            addCriterion("err_desc between", value1, value2, "errDesc");
            return (Criteria) this;
        }

        public Criteria andErrDescNotBetween(String value1, String value2) {
            addCriterion("err_desc not between", value1, value2, "errDesc");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagIsNull() {
            addCriterion("new_insert_flag is null");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagIsNotNull() {
            addCriterion("new_insert_flag is not null");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagEqualTo(Integer value) {
            addCriterion("new_insert_flag =", value, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagNotEqualTo(Integer value) {
            addCriterion("new_insert_flag <>", value, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagGreaterThan(Integer value) {
            addCriterion("new_insert_flag >", value, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_insert_flag >=", value, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagLessThan(Integer value) {
            addCriterion("new_insert_flag <", value, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagLessThanOrEqualTo(Integer value) {
            addCriterion("new_insert_flag <=", value, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagIn(List<Integer> values) {
            addCriterion("new_insert_flag in", values, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagNotIn(List<Integer> values) {
            addCriterion("new_insert_flag not in", values, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagBetween(Integer value1, Integer value2) {
            addCriterion("new_insert_flag between", value1, value2, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andNewInsertFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("new_insert_flag not between", value1, value2, "newInsertFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagIsNull() {
            addCriterion("check_flag is null");
            return (Criteria) this;
        }

        public Criteria andCheckFlagIsNotNull() {
            addCriterion("check_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCheckFlagEqualTo(Integer value) {
            addCriterion("check_flag =", value, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagNotEqualTo(Integer value) {
            addCriterion("check_flag <>", value, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagGreaterThan(Integer value) {
            addCriterion("check_flag >", value, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("check_flag >=", value, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagLessThan(Integer value) {
            addCriterion("check_flag <", value, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagLessThanOrEqualTo(Integer value) {
            addCriterion("check_flag <=", value, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagIn(List<Integer> values) {
            addCriterion("check_flag in", values, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagNotIn(List<Integer> values) {
            addCriterion("check_flag not in", values, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagBetween(Integer value1, Integer value2) {
            addCriterion("check_flag between", value1, value2, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andCheckFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("check_flag not between", value1, value2, "checkFlag");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtIsNull() {
            addCriterion("copy_at is null");
            return (Criteria) this;
        }

        public Criteria andCopyAtIsNotNull() {
            addCriterion("copy_at is not null");
            return (Criteria) this;
        }

        public Criteria andCopyAtEqualTo(Date value) {
            addCriterion("copy_at =", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtNotEqualTo(Date value) {
            addCriterion("copy_at <>", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtGreaterThan(Date value) {
            addCriterion("copy_at >", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtGreaterThanOrEqualTo(Date value) {
            addCriterion("copy_at >=", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtLessThan(Date value) {
            addCriterion("copy_at <", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtLessThanOrEqualTo(Date value) {
            addCriterion("copy_at <=", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtIn(List<Date> values) {
            addCriterion("copy_at in", values, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtNotIn(List<Date> values) {
            addCriterion("copy_at not in", values, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtBetween(Date value1, Date value2) {
            addCriterion("copy_at between", value1, value2, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtNotBetween(Date value1, Date value2) {
            addCriterion("copy_at not between", value1, value2, "copyAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}