package com.gz.eim.am.stock.web.inventory.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.inventory.plan.StockPlanAssetRemandApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import com.gz.eim.am.stock.service.inventory.plan.StockPlanAssetRemandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * @author: lishuyang
 * @date: 2019/12/26
 * @description: 资产归还入库controller
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/planAssetRemand")
public class StockPlanAssetRemandController implements StockPlanAssetRemandApi {
    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockPlanAssetRemandService stockPlanAssetRemandService;

    @Override
    @DocTypeAnnotation(DocTypeConstant.REMAND_PLAN_IN)
    public ResponseData savePlanAssetRemand(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info ("/api/am/stock/planAssetRemand/save {}", inventoryInPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanAssetRemandService.savePlanAssetRemand (inventoryInPlanHeadReqDTO, user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("保存资产归还单出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.REMAND_PLAN_IN)
    public ResponseData selectPlanAssetRemand(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO) {
        log.info ("/api/am/stock/planAssetRemand/search {}", inventoryInPlanSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanAssetRemandService.selectPlanAssetRemand (inventoryInPlanSearchReqDTO, user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("资产归还单分页查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanAssetRemandById(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO) {
        log.info ("/api/am/stock/planAssetRemand/search/detail{}", inventoryInPlanLineReqDTO);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanAssetRemandService.selectPlanAssetRemandById (inventoryInPlanLineReqDTO, user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("资产归还单详情查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.REMAND_PLAN_IN)
    public ResponseData assetPlanAssetRemandInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info ("/api/am/stock/planAssetRemand/inBound {}", inventoryInPlanHeadReqDTO);
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_REMAND_BOUND + inventoryInPlanHeadReqDTO.getInventoryInPlanHeadId ();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                JwtUser user = SecurityUtil.getJwtUser ();
                log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
                res = this.stockPlanAssetRemandService.assetPlanAssetRemandInBound (inventoryInPlanHeadReqDTO, user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            } else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info ("资产归还单归还操作失败返回消息：{}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error ("资产归还单详情归还", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData assetPlanAssetRemandPrint(Long inventoryInPlanHeadId) {
        log.info ("/api/am/stock/planAssetRemand/print/{}", inventoryInPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanAssetRemandService.assetPlanAssetRemandPrint (inventoryInPlanHeadId, user);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("资产归还单详情查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData selectAssetByRemand(String holder, String remandWarehouseCode,String assetsCode,Integer pageNum,Integer pageSize) {
        log.info ("/api/am/stock/planAssetRemand/assets/search, holder:{}, remandWarehouseCode:{} ", holder, remandWarehouseCode);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
            res = this.stockPlanAssetRemandService.selectAssetByRemand (holder, remandWarehouseCode,assetsCode,pageNum,pageSize);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("资产归还单详情查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData assetRemandInitialization(Integer batchId) {
        log.info ("/api/am/stock/planAssetRemand/initialization, batchId:{}", batchId);
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_REMAND_INITIALIZATION + batchId;
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                JwtUser user = SecurityUtil.getJwtUser ();
                log.info ("---------------------login-user--{}---------------------", user.getEmployeeCode ());
                res = this.stockPlanAssetRemandService.assetRemandInitialization (batchId, user);
                redisUtil.deleteByKey (nameSpace, lockKey);
            } else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error ("资产归还单批量导入失败", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.REMAND_PLAN_IN)
    public ResponseData cancelPlanAssetRemandById(Long inventoryInPlanHeadId) {
        log.info ("/api/am/stock/planAssetRemand/cancel/{}", inventoryInPlanHeadId);
        ResponseData res = null;
        try {
            res = this.stockPlanAssetRemandService.cancelPlanAssetRemandById (inventoryInPlanHeadId);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("资产归还单详情查询出错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

}
