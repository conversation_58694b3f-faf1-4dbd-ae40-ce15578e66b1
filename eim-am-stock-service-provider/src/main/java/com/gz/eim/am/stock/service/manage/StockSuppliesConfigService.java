package com.gz.eim.am.stock.service.manage;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.entity.StockSuppliesConfig;
import com.gz.eim.am.stock.entity.StockSuppliesConfigDetail;
import com.gz.eim.am.stock.entity.StockSuppliesConfigResult;
import com.gz.eim.am.stock.entity.StockSuppliesQuantity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 5:01
 */
public interface StockSuppliesConfigService {

    /**
     * 库存记录查询
     * @param warehouseCode
     * @param suppliesCode
     * @param version
     * @param batchNo
     * @param snNo
     * @return
     */
    StockSuppliesConfigResult queryByParam(String warehouseCode, String suppliesCode, String version, String batchNo, String snNo);

    /**
     * 增加库存记录
     * @param stockSuppliesQuantity
     * @param details
     * @return
     */
    void addTotalInventory(StockSuppliesQuantity stockSuppliesQuantity, List<StockSuppliesConfigDetail> details);

    /**
     * 扣减库存记录
     * @param config
     * @param details
     * @return
     */
    int subTotalInventory(StockSuppliesConfig config, List<StockSuppliesConfigDetail> details);

    /**
     * 库存数据查询
     * @param dto
     * @param user
     * @return
     */
    ResponseData getSuppliesWarehouse(StockSuppliesQuantityReqDTO dto, JwtUser user, Boolean isSkipStockAuthority);

    /**
     * 库存结算
     * @param user
     * @return
     */
    int suppliesStoreArchive(JwtUser user);

    /**
     * 库存记录查询
     * @param dto
     * @param user
     * @return
     */
    StockSuppliesConfigResult queryByParam(StockSuppliesQuantityReqDTO dto, JwtUser user);

    /**
     * 根据仓库和序列号查询物料
     * @param dto
     * @return
     */
    ResponseData getSuppliesBySnnoAndWareCode(StockSuppliesQuantityReqDTO dto, JwtUser user);

    /**
     * 金融gps明细查询
     * @param dto
     * @return
     */
    ResponseData getGpsDetail(StockSuppliesQuantityReqDTO dto);
}
