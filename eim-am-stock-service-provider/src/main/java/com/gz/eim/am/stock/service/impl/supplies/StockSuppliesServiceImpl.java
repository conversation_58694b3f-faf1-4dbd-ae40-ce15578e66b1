package com.gz.eim.am.stock.service.impl.supplies;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.common.constant.MetaDataKeyConstants;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.*;
import com.gz.eim.am.stock.dao.base.StockSuppliesOperationLogMapper;
import com.gz.eim.am.stock.dao.supplies.SuppliesMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.supplies.*;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.PagerRespDto;
import com.gz.eim.am.stock.dto.response.SuppliesRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsLicenseRespDTO;
import com.gz.eim.am.stock.dto.response.supplies.*;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.FinDemCostItem;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.supplies.SuppliesPurchaseSearchVo;
import com.gz.eim.am.stock.ext.dao.supplies.StockSuppliesBusinessBaseInfoDao;
import com.gz.eim.am.stock.ext.dao.supplies.StockSuppliesDao;
import com.gz.eim.am.stock.ext.dao.supplies.StockSuppliesPurchaseDao;
import com.gz.eim.am.stock.mapper.base.StockSuppliesBusinessBaseInfoMapper;
import com.gz.eim.am.stock.mapper.base.StockSuppliesInventoryMapper;
import com.gz.eim.am.stock.mapper.base.StockSuppliesMapper;
import com.gz.eim.am.stock.mapper.base.StockSuppliesPurchaseMapper;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.ambase.FinDemCostItemService;
import com.gz.eim.am.stock.service.assets.StockAssetsCategoryService;
import com.gz.eim.am.stock.service.assets.StockAssetsLicenseService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.supplies.*;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseSuppliesCategoryService;
import com.gz.eim.am.stock.util.em.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 10:17
 */
@Service
public class StockSuppliesServiceImpl implements StockSuppliesService {

    private final Logger logger = LoggerFactory.getLogger(StockSuppliesServiceImpl.class);

    @Resource
    private StockSuppliesMapper stockSuppliesMapper;

    @Resource
    private SuppliesMapper suppliesMapper;

    @Resource
    private StockSuppliesUnitService unitService;

    @Resource
    private StockSuppliesOperationLogMapper suppliesOperationLogMapper;

    @Resource
    private StockSuppliesCategoryService categoryService;

    @Resource
    private StockSuppliesDao stockSuppliesDao;

    @Resource
    private StockSuppliesPurchaseService stockSuppliesPurchaseService;

    @Resource
    private StockWarehouseSuppliesCategoryService stockWarehouseSuppliesCategoryService;

    @Resource
    private StockSuppliesBusinessBaseInfoService stockSuppliesBusinessBaseInfoService;

    @Resource
    private StockWarehouseService stockWarehouseService;

    @Resource
    private StockSuppliesPurchaseMapper stockSuppliesPurchaseMapper;

    @Resource
    private AmbaseCommonService ambaseCommonService;

    @Resource
    private StockSuppliesPurchaseDao stockSuppliesPurchaseDao;

    @Resource
    private StockSuppliesBusinessBaseInfoDao stockSuppliesBusinessBaseInfoDao;
    @Resource
    private StockSuppliesBusinessBaseInfoMapper stockSuppliesBusinessBaseInfoMapper;
    @Resource
    private StockSuppliesPurchaseExtService stockSuppliesPurchaseExtService;
    @Resource
    private FinDemCostItemService finDemCostItemService;
    @Resource
    private StockAssetsLicenseService stockAssetsLicenseService;
    @Resource
    private StockSuppliesInventoryService stockSuppliesInventoryService;

    @Resource
    private StockSuppliesInventoryMapper stockSuppliesInventoryMapper;
    @Resource
    private StockAssetsCategoryService stockAssetsCategoryService;

    @Resource
    private StockSuppliesPurchaseExtService StockSuppliesPurchaseExtService;

    @Autowired
    private StockAssetsService stockAssetsService;


    @Override
    public StockSupplies getSuppliesByCode(String code, Integer status) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        StockSuppliesExample example = new StockSuppliesExample();
        if (null == status) {
            example.createCriteria().andCodeEqualTo(code);
        } else {
            example.createCriteria().andCodeEqualTo(code).andStatusEqualTo(status);
        }
        List<StockSupplies> supplies = this.stockSuppliesMapper.selectByExample(example);
        if (supplies.size() == 1) {
            return supplies.get(0);
        }
        return null;
    }

    @Override
    public ResponseData vagueSupplies(String param, Integer limit, String warehouseCode, Integer docType, Integer suppliesType) throws ParseException {
        if (StringUtils.isBlank(param)) {
            return ResponseData.createResult(ResponseCode.PARAMETER_ERROR);
        }
        List<Integer> warehouseTypes = null;
        if (docType != null) {
            warehouseTypes = ambaseCommonService.selectMetaDataByKey(MetaDataKeyConstants.DOCUMENT_WAREHOUSE, docType);
            if (CollectionUtils.isEmpty(warehouseTypes)) {
                return ResponseData.createFailResult("当前单据没有查询到对应的仓库类型");
            }
        }
        if (null == limit || limit <= 0) {
            limit = 10;
        }
        final List<SuppliesRespDTO> respDTOS = this.suppliesMapper.vagueSupplies(param, limit, DateUtils.dateFormat(new Date(),
                DateUtils.MONTH_PATTERN), warehouseCode, warehouseTypes, suppliesType);
        // 如果是执照借用，就查询执照物料下的资产信息
        if(docType != null && docType == DocTypeConstant.LICENSE_PLAN_OUT){
            selectBySuppliesCode(respDTOS);
        }
        return ResponseData.createSuccessResult(respDTOS);
    }

    /**
     * 查询物料下的资产信息
     *
     * @param respDTOS
     */
    private void selectBySuppliesCode(List<SuppliesRespDTO> respDTOS) {
        if (!CollectionUtils.isEmpty(respDTOS)) {
            List<String> codeList = respDTOS.stream().map(suppliesRespDTO -> suppliesRespDTO.getCode()).collect(Collectors.toList());
            // 然后查询物料是否存在在库的而且不是审批中的资产，如果没有就直接remove
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setSuppliesCodeList(new ArrayList<>(codeList));
            assetsSearchDTO.setStatus(AssetsEnum.statusType.IDLE.getValue());
            assetsSearchDTO.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
            assetsSearchDTO.setCategoryCode(AssetsEnum.AssetsCategory.LICENSE.getValue());
            List<StockAssets> stockAssetsList = stockAssetsService.selectAssetsList(assetsSearchDTO);
            Set<String> haveAssetsSuppliesCodeSet = stockAssetsList.stream().map(StockAssets::getSuppliesCode).collect(Collectors.toSet());
            // 如果不存在直接都清空
            if(CollectionUtils.isEmpty(haveAssetsSuppliesCodeSet)){
                respDTOS.clear();
                return;
            }
            Iterator<SuppliesRespDTO> suppliesRespDTOIterator = respDTOS.iterator();
            while (suppliesRespDTOIterator.hasNext()) {
                SuppliesRespDTO suppliesRespDTO = suppliesRespDTOIterator.next();
                String code = suppliesRespDTO.getCode();
                if (!haveAssetsSuppliesCodeSet.contains(code)) {
                    suppliesRespDTOIterator.remove();
                }
            }
            //根据物料编码集合查询 物料下的执照资产信息
            List<StockAssetsLicenseRespDTO> stockAssetsLicenseRespDTOList = stockAssetsLicenseService.selectBySuppliesCodeList(new ArrayList<>(haveAssetsSuppliesCodeSet));
            Map<String, List<StockAssetsLicenseRespDTO>> stringListMap = stockAssetsLicenseRespDTOList.stream().
                    collect(Collectors.groupingBy(stockAssetsLicenseRespDTO -> stockAssetsLicenseRespDTO.getSuppliesCode()));
            respDTOS.stream().forEach(suppliesRespDTO -> {
                //如果当前物料为执照物料 即当前物料下的 资产信息 法人信息 法人身份证号 注册地址都是一样的
                List<StockAssetsLicenseRespDTO> assetsLicenseRespDTOList = stringListMap.get(suppliesRespDTO.getCode());
                if (!CollectionUtils.isEmpty(assetsLicenseRespDTOList)) {
                    StockAssetsLicenseRespDTO stockAssetsLicenseRespDTO = assetsLicenseRespDTOList.get(0);
                    suppliesRespDTO.setLicenseLegalPerson(stockAssetsLicenseRespDTO.getLicenseLegalPerson());
                    suppliesRespDTO.setLicenseLegalPersonName(stockAssetsLicenseRespDTO.getLicenseLegalPersonName());
                    suppliesRespDTO.setCorporateIdNumber(stockAssetsLicenseRespDTO.getCorporateIdNumber());
                    suppliesRespDTO.setRegisteredAddress(stockAssetsLicenseRespDTO.getRegisteredAddress());
                }
            });
        }

    }

    @Override
    public List<StockSupplies> getEnableSuppliesList(String code) {

        StockSuppliesExample example = new StockSuppliesExample();
        if (StringUtils.isBlank(code)) {
            example.createCriteria().andStatusNotEqualTo(SuppliesEnum.Status.FORBID.getStatus());
        } else {
            example.createCriteria().andCodeEqualTo(code).andStatusNotEqualTo(SuppliesEnum.Status.FORBID.getStatus());
        }
        List<StockSupplies> supplies = this.stockSuppliesMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(supplies)) {
            return new ArrayList<>();
        }

        return supplies;
    }


    public StockSupplies selectSuppliesDetails(final Long id, final String code) {

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData saveSupplies(final SuppliesDTO suppliesDTO) {
        final List<String> errList = checkInputSuppliesParams(suppliesDTO);
        if (errList.size() > 0) {
            return ResponseData.createFailResult(StringUtils.join(errList, ","));
        }
        final StockSuppliesCategory category = categoryService.selectSuppliesCategoryById(suppliesDTO.getCategoryId());
        if (null == category || !category.getLevel().equals(SuppliesCategoryEnum.level.THREE.getValue())) {
            return ResponseData.createFailResult("物料分类设置错误");
        }
        final StockSupplies stockSupplies = new StockSupplies();
        BeanUtils.copyProperties(suppliesDTO, stockSupplies);
        //设置物料分类
        stockSupplies.setCatCode(category.getCode());
        stockSupplies.setCatFullCode(category.getAllCode());
        stockSupplies.setCode(suppliesMapper.selectNextSerialNo(category.getCode()));
        stockSupplies.setRemark(suppliesDTO.getName() + "；" + suppliesDTO.getBrandCode() + "；" + suppliesDTO.getModelCode());
        stockSupplies.setApplyBy(stockSupplies.getCreatedBy());
        stockSupplies.setUpdatedBy(stockSupplies.getCreatedBy());
        stockSupplies.setUpdatedAt(new Date());
        stockSupplies.setCreatedAt(stockSupplies.getUpdatedAt());
        // 物料来源默认手工
        if (StringUtils.isBlank(stockSupplies.getSuppliesSource())) {
            stockSupplies.setSuppliesSource("1");
        }
        // 默认不需要特需管理
        if (stockSupplies.getManageType() == null) {
            stockSupplies.setManageType(0);
        }
        // 默认费用项目字段
        if (StringUtils.isBlank(stockSupplies.getCostItemCode())) {
            stockSupplies.setCostItemCode("");
        }
        this.stockSuppliesMapper.insertSelective(stockSupplies);
        final StockSuppliesOperationLog suppliesOperationLog = new StockSuppliesOperationLog();

        BeanUtils.copyProperties(stockSupplies, suppliesOperationLog);
        if (StringUtils.isBlank(suppliesOperationLog.getCostItemCode())) {
            suppliesOperationLog.setCostItemCode("");
        }
        this.suppliesOperationLogMapper.insertSelective(suppliesOperationLog);

        ResponseData resp = ResponseData.createSuccessResult(stockSupplies);
        return resp;
    }

    /**
     * 新增物料校验
     */
    public static List<String> checkInputSuppliesParams(final SuppliesDTO suppliesDTO) {
        final List<String> errList = new ArrayList<>(6);
        if (suppliesDTO.getCategoryId() == null) {
            errList.add("物料分类id为空");
        }
        if (StringUtils.isBlank(suppliesDTO.getName())) {
            errList.add("物料名称必填");
        }
        if (StringUtils.isNotBlank(suppliesDTO.getName()) && suppliesDTO.getName().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("物料名称长度过长");
        }
        if (StringUtils.isBlank(suppliesDTO.getModelCode())) {
            errList.add("物料型号必填");
        }
        if (StringUtils.isNotBlank(suppliesDTO.getModelCode()) && suppliesDTO.getModelCode().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("物料型号长度过长");
        }
        if (StringUtils.isBlank(suppliesDTO.getBrandCode())) {
            errList.add("物料品牌必填");
        }
        if (StringUtils.isNotBlank(suppliesDTO.getBrandCode()) && suppliesDTO.getBrandCode().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("物料品牌长度过长");
        }
        if (StringUtils.isBlank(suppliesDTO.getUnitCode())) {
            errList.add("物料单位必填");
        }
        if (StringUtils.isBlank(suppliesDTO.getPurchaseUnitCode())) {
            errList.add("物料采购单位必填");
        }
        if (StringUtils.isNotBlank(suppliesDTO.getProduceFactory()) && suppliesDTO.getProduceFactory().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("生产工厂长度过长");
        }
        if (StringUtils.isNotBlank(suppliesDTO.getServiceFactory()) && suppliesDTO.getServiceFactory().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("服务工厂长度过长");
        }
        if (StringUtils.isNotBlank(suppliesDTO.getAftersaleFactory()) && suppliesDTO.getAftersaleFactory().length() > SqlConstant.VARCHAR_LENGTH_TWO_FIVE_FIVE) {
            errList.add("售后工厂长度过长");
        }
        return errList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateSupplies(final SuppliesDTO suppliesDTO) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        /**
         * 12月24号修改
         */
        final List<String> errList = checkInputSuppliesParams(suppliesDTO);
        if (errList.size() > 0) {
            return ResponseData.createFailResult(StringUtils.join(errList, ","));
        }
        StockSuppliesCategory category = categoryService.selectSuppliesCategoryById(suppliesDTO.getCategoryId());
        if (null == category || !category.getLevel().equals(SuppliesCategoryEnum.level.THREE.getValue())) {
            return ResponseData.createFailResult("物料分类设置错误");
        }
        StockSupplies stockSupplies = getSuppliesByCode(suppliesDTO.getCode(), null);
        if (null == stockSupplies) {
            return ResponseData.createFailResult("当前物料不存在,不能修改!");
        }
        BeanUtils.copyProperties(suppliesDTO, stockSupplies, new String[]{"suppliesId"});
        //设置物料分类
        stockSupplies.setCatCode(category.getCode());
        stockSupplies.setCatFullCode(category.getAllCode());
//        stockSupplies.setCode(suppliesMapper.selectNextSerialNo(category.getCode()));
        stockSupplies.setRemark(suppliesDTO.getName() + "；" + suppliesDTO.getBrandCode() + "；" + suppliesDTO.getModelCode());
        stockSupplies.setUpdatedBy(jwtUser.getEmployeeCode());
        stockSupplies.setUpdatedAt(new Date());
        // 物料来源默认手工
        if (StringUtils.isBlank(stockSupplies.getSuppliesSource())) {
            stockSupplies.setSuppliesSource("1");
        }
        this.stockSuppliesMapper.updateByPrimaryKeySelective(stockSupplies);
        final StockSuppliesOperationLog suppliesOperationLog = new StockSuppliesOperationLog();

        BeanUtils.copyProperties(stockSupplies, suppliesOperationLog);
        suppliesOperationLog.setSuppliesId(stockSupplies.getSuppliesId());
        suppliesOperationLog.setSuppliesSource(stockSupplies.getSuppliesSource());
        suppliesOperationLog.setCreatedBy(jwtUser.getEmployeeCode());
        suppliesOperationLog.setUpdatedBy(jwtUser.getEmployeeCode());
        if (StringUtils.isBlank(suppliesOperationLog.getCostItemCode())) {
            suppliesOperationLog.setCostItemCode("");
        }
        this.suppliesOperationLogMapper.insertSelective(suppliesOperationLog);

        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData selectSupplies(final SuppliesReqDTO suppliesReqDTO) {
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        //校验物料小类下是否存在费用项目
        if (null != suppliesReqDTO.getCategoryId()) {
            List<String> costItemList = suppliesMapper.selectItemCodeByCategoryId(String.valueOf(suppliesReqDTO.getCategoryId()));
            if (CollectionUtils.isEmpty(costItemList)) {
                pageRespDTO.setMessage("该物料小类没有查询到对应的费用项目");
                return pageRespDTO;
            } else {
                suppliesReqDTO.setCostItemList(costItemList);
            }
        }
        if (suppliesReqDTO.getNoPaging() != null && true == suppliesReqDTO.getNoPaging()) {
            final Long count = this.getCount(suppliesReqDTO);
            if (count == null || count > CommonConstant.EXCEL_EXPORT_MAX_COUNT) {
                pageRespDTO.setMessage(count == null ? "无数据导出" : "导出数量超过10000条");
                return pageRespDTO;
            }
            suppliesReqDTO.setPageSize(null);
            List<StockSupplies> suppliesList = this.getList(suppliesReqDTO);
            pageRespDTO.setData(convertToDTO(suppliesList));
            return pageRespDTO;
        }
        suppliesReqDTO.initPageDefaultParam();
        suppliesReqDTO.setStartNum((suppliesReqDTO.getPageNum() - 1) * suppliesReqDTO.getPageSize());
        Long totalCount = this.getCount(suppliesReqDTO);
        pageRespDTO.setCount(totalCount);
        if (totalCount > 0) {
            List<StockSupplies> suppliesList = this.getList(suppliesReqDTO);
            pageRespDTO.setData(convertToDTO(suppliesList));
        } else {
            pageRespDTO.setData(new ArrayList<>(0));
        }
        pageRespDTO.setPageSize(suppliesReqDTO.getPageSize());
        pageRespDTO.setStartNum(suppliesReqDTO.getStartNum());
        pageRespDTO.setPageNum(suppliesReqDTO.getPageNum());
        return pageRespDTO;
    }

    public List<SuppliesResponseDTO> convertToDTO(final List<StockSupplies> suppliesList) {
        final List<String> catCodes = suppliesList.stream().map(s -> s.getCatCode()).distinct().collect(Collectors.toList());
        final List<StockSuppliesCategory> categoryList = categoryService.selectSuppliesCategory(null, catCodes);
        final List<String> unitCodes = suppliesList.stream().map(StockSupplies::getUnitCode).collect(Collectors.toList());
        final List<StockSuppliesUnit> unitList = unitService.selectSuppliesUnit(null, unitCodes);
        final Map<String, String> unitMap =
                unitList.stream().collect(Collectors.toMap(StockSuppliesUnit::getUnitCode, e -> e.getUnitName()));
        final Map<String, StockSuppliesCategory> categoryMap =
                categoryList.stream().collect(Collectors.toMap(StockSuppliesCategory::getCode, e -> e));

        final List<SuppliesResponseDTO> dtoList = new ArrayList<>(suppliesList.size());
        suppliesList.stream().forEach(s -> {
            final SuppliesResponseDTO dto = new SuppliesResponseDTO();
            BeanUtils.copyProperties(s, dto);
            dto.setUnitName(unitMap.get(dto.getUnitCode()));
            if (null != categoryMap.get(dto.getCatCode()) && StringUtils.isNotBlank(categoryMap.get(dto.getCatCode()).getAllName())) {
                String[] nameArr = categoryMap.get(dto.getCatCode()).getAllName().split("/");
                dto.setCatName(nameArr[1]);
                dto.setCatName2(nameArr[2]);
                dto.setCatName3(nameArr[3]);
            }
            dtoList.add(dto);
        });
        return dtoList;
    }

    private StockSuppliesExample settingExampleClause(final SuppliesReqDTO suppliesReqDTO) {
        final StockSuppliesExample suppliesExample = new StockSuppliesExample();
        final StockSuppliesExample.Criteria criteria = suppliesExample.createCriteria();
        if (StringUtils.isNotBlank(suppliesReqDTO.getCatCode())) {
            criteria.andCatCodeLike(suppliesReqDTO.getCatCode() + "%");
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getRemark())) {
            criteria.andRemarkLike("%" + suppliesReqDTO.getRemark().trim() + "%");
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getCatFullCode())) {
            criteria.andCatFullCodeLike(suppliesReqDTO.getCatFullCode().trim() + "%");
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getName())) {
            criteria.andNameEqualTo(suppliesReqDTO.getName());
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getCode())) {
            criteria.andCodeEqualTo(suppliesReqDTO.getCode());
        }
        //多个物料编码查询
        if (!CollectionUtils.isEmpty(suppliesReqDTO.getCodeList())) {
            criteria.andCodeIn(suppliesReqDTO.getCodeList());
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getBrandCode())) {
            criteria.andBrandCodeEqualTo(suppliesReqDTO.getBrandCode());
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getModelCode())) {
            criteria.andModelCodeEqualTo(suppliesReqDTO.getModelCode());
        }
        if (suppliesReqDTO.getStatus() != null) {
            criteria.andStatusEqualTo(suppliesReqDTO.getStatus());
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getCreatedBy())) {
            criteria.andCreatedByEqualTo(suppliesReqDTO.getCreatedBy());
        }
        if (StringUtils.isNotBlank(suppliesReqDTO.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(suppliesReqDTO.getUpdatedBy());
        }
        if (!CollectionUtils.isEmpty(suppliesReqDTO.getCostItemList())) {
            criteria.andCostItemCodeIn(suppliesReqDTO.getCostItemList());
        }
        if (!CollectionUtils.isEmpty(suppliesReqDTO.getStatusList())) {
            criteria.andStatusIn(suppliesReqDTO.getStatusList());
        }
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        return suppliesExample;
    }

    public List<StockSupplies> getList(SuppliesReqDTO suppliesReqDTO) {
        StockSuppliesExample suppliesExample = settingExampleClause(suppliesReqDTO);
        suppliesExample.setLimit(suppliesReqDTO.getPageSize());
        suppliesExample.setOffset(suppliesReqDTO.getStartNum());
        String orderByClause = " supplies_id desc ";
        if (StringUtils.isNotBlank(suppliesReqDTO.getSortColumns())) {
            orderByClause = suppliesReqDTO.getSortColumns();
        }
        suppliesExample.setDistinct(true);
        suppliesExample.setOrderByClause(orderByClause);
        return this.stockSuppliesMapper.selectByExample(suppliesExample);
    }

    public Long getCount(SuppliesReqDTO suppliesReqDTO) {
        StockSuppliesExample suppliesExample = settingExampleClause(suppliesReqDTO);
        return this.stockSuppliesMapper.countByExample(suppliesExample);
    }

    @Override
    public List<StockSupplies> selectSupplies(final List<Long> ids, final List<String> codes) {
        StockSuppliesExample suppliesExample = new StockSuppliesExample();
        StockSuppliesExample.Criteria criteria = suppliesExample.createCriteria();
        if (ids != null && ids.size() > 0) {
            criteria.andSuppliesIdIn(ids);
            return this.stockSuppliesMapper.selectByExample(suppliesExample);
        } else if (codes != null && codes.size() > 0) {
            criteria.andCodeIn(codes);
            return this.stockSuppliesMapper.selectByExample(suppliesExample);
        }
        return null;
    }

    @Override
    public Map<String, StockSupplies> getSuppliesMapByCodes(List<Long> ids, List<String> codes) {
        Map<String, StockSupplies> stockSuppliesMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        List<StockSupplies> stockSuppliesList = this.selectSupplies(ids, codes);
        if (!CollectionUtils.isEmpty(stockSuppliesList)) {
            stockSuppliesList.forEach(stockSupplies -> {
                stockSuppliesMap.put(stockSupplies.getCode(), stockSupplies);
            });
        }
        return stockSuppliesMap;
    }

    @Override
    public PagerRespDto<SuppliesResponseDTO> pageByPurchase(SuppliesPurchaseSearchReqDto suppliesPurchaseSearchReqDto) {
        //校验参数
        String checkPageByPurchaseParamResult = this.checkPageByPurchaseParam(suppliesPurchaseSearchReqDto);
        if (StringUtils.isNotBlank(checkPageByPurchaseParamResult)) {
            throw new ServiceUncheckedException(checkPageByPurchaseParamResult);
        }

        SuppliesPurchaseSearchVo suppliesPurchaseSearchVo = new SuppliesPurchaseSearchVo();
        BeanUtils.copyProperties(suppliesPurchaseSearchReqDto, suppliesPurchaseSearchVo);

        List<String> costItemList = suppliesMapper.selectItemCodeByCategoryId(suppliesPurchaseSearchVo.getCategoryId());
        if (CollectionUtils.isEmpty(costItemList)) {
            return new PagerRespDto<>(CommonConstant.NUMBER_LONG_ZERO, Collections.emptyList());
        } else {
            suppliesPurchaseSearchVo.setCostItemList(costItemList);
        }

        Long count = stockSuppliesDao.countBySuppliesPurchaseSearchVo(suppliesPurchaseSearchVo);
        if (Objects.isNull(count) || Long.compare(count, CommonConstant.NUMBER_LONG_ZERO) != CommonConstant.NUMBER_ONE) {
            return new PagerRespDto<>(CommonConstant.NUMBER_LONG_ZERO, Collections.emptyList());
        }

        PagerRespDto<SuppliesResponseDTO> pagerRespDto = new PagerRespDto<>();
        pagerRespDto.setCount(count);

        suppliesPurchaseSearchVo.initPageDefaultParam();
        List<StockSupplies> stockSuppliesList = stockSuppliesDao.pageBySuppliesPurchaseSearchVo(suppliesPurchaseSearchVo);
        if (CollectionUtils.isEmpty(stockSuppliesList)) {
            pagerRespDto.setList(Collections.emptyList());
        } else {
            List<SuppliesResponseDTO> suppliesRespDtoList = this.convertToDTO(stockSuppliesList);
            //获取物料的采购属性
            List<String> suppliesCodeList = suppliesRespDtoList.stream().filter(suppliesResponseDTO -> StringUtils.isNotBlank(suppliesResponseDTO.getCode()))
                    .map(SuppliesResponseDTO::getCode).collect(Collectors.toList());

            List<SuppliesPurchaseRespDto> suppliesPurchaseRespDtoList = stockSuppliesPurchaseService.listRespDtoBySuppliesCodeListAndWarehouseType(suppliesCodeList, suppliesPurchaseSearchVo.getWarehouseType());
            Map<String, SuppliesPurchaseRespDto> suppliesPurchaseRespDtoMap = suppliesPurchaseRespDtoList.stream().collect(Collectors.toMap(suppliesPurchaseRespDto -> suppliesPurchaseRespDto.getSuppliesCode(), suppliesPurchaseRespDto -> suppliesPurchaseRespDto, (key1, key2) -> key1));

            //查询物料采购属性尾表信息
            List<StockSuppliesPurchaseExt> stockSuppliesPurchaseExtList = stockSuppliesPurchaseExtService.selectBySuppliesCodeAndWarehouseType(suppliesCodeList, suppliesPurchaseSearchVo.getWarehouseType());
            Map<String, List<StockSuppliesPurchaseExtRespDTO>> stringListMap = selectCostItem(suppliesPurchaseRespDtoList, stockSuppliesPurchaseExtList);

//            suppliesPurchaseRespDtoList.forEach(suppliesPurchaseRespDto -> suppliesRespDtoList.forEach(suppliesResponseDTO -> {
//                if (suppliesResponseDTO.getCode().equals(suppliesPurchaseRespDto.getSuppliesCode())) {
//                    suppliesResponseDTO.setSuppliesPurchaseRespDto(suppliesPurchaseRespDto);
//                }
//            }));
//
            suppliesRespDtoList.stream().forEach(suppliesResponseDTO -> {
                SuppliesPurchaseRespDto suppliesPurchaseRespDto = suppliesPurchaseRespDtoMap.get(suppliesResponseDTO.getCode());
                List<StockSuppliesPurchaseExtRespDTO> purchaseExtList = stringListMap.get(suppliesPurchaseRespDto.getSuppliesCode());
                suppliesPurchaseRespDto.setSuppliesPurchaseExtRespDTOList(purchaseExtList);
                suppliesResponseDTO.setSuppliesPurchaseRespDto(suppliesPurchaseRespDto);

            });

            pagerRespDto.setList(suppliesRespDtoList);
        }
        pagerRespDto.setPageSize(suppliesPurchaseSearchVo.getPageSize());
        pagerRespDto.setPageNum(suppliesPurchaseSearchVo.getPageNum());
        return pagerRespDto;
    }

    /**
     * 查询费用项名称信息
     *
     * @param suppliesPurchaseRespDtoList
     * @param stockSuppliesPurchaseExtList
     * @throws Exception
     */
    private Map<String, List<StockSuppliesPurchaseExtRespDTO>> selectCostItem(List<SuppliesPurchaseRespDto> suppliesPurchaseRespDtoList, List<StockSuppliesPurchaseExt> stockSuppliesPurchaseExtList) {
        Map<String, List<StockSuppliesPurchaseExtRespDTO>> stringListMap = new HashMap<>();
        List<String> costItemCodeList = suppliesPurchaseRespDtoList.stream().map(suppliesPurchaseRespDto -> suppliesPurchaseRespDto.getDefaultCostItemCode()).collect(Collectors.toList());
        for (StockSuppliesPurchaseExt stockSuppliesPurchaseExt : stockSuppliesPurchaseExtList) {
            costItemCodeList.add(stockSuppliesPurchaseExt.getCostItemCode());
        }
        //查询费用项信息
        Map<String, FinDemCostItem> finDemCostItemMap = finDemCostItemService.selectMapByItemCodeList(costItemCodeList);
        suppliesPurchaseRespDtoList.stream().forEach(suppliesPurchaseRespDto -> {
            FinDemCostItem finDemCostItem = finDemCostItemMap.get(suppliesPurchaseRespDto.getDefaultCostItemCode());
            if (null != finDemCostItem) {
                suppliesPurchaseRespDto.setDefaultCostItemName(finDemCostItem.getItemName());
            }
        });

        for (StockSuppliesPurchaseExt stockSuppliesPurchaseExt : stockSuppliesPurchaseExtList) {
            StockSuppliesPurchaseExtRespDTO stockSuppliesPurchaseExtRespDTO = ConvertUtil.convertToType(StockSuppliesPurchaseExtRespDTO.class, stockSuppliesPurchaseExt);
            FinDemCostItem finDemCostItem = finDemCostItemMap.get(stockSuppliesPurchaseExtRespDTO.getCostItemCode());
            if (null != finDemCostItem) {
                stockSuppliesPurchaseExtRespDTO.setCostItemName(finDemCostItem.getItemName());
            }
            if (!stringListMap.containsKey(stockSuppliesPurchaseExt.getSuppliesCode())) {
                stringListMap.put(stockSuppliesPurchaseExt.getSuppliesCode(), new ArrayList());
            }
            stringListMap.get(stockSuppliesPurchaseExtRespDTO.getSuppliesCode()).add(stockSuppliesPurchaseExtRespDTO);
        }
        return stringListMap;
    }


    @Override
    public Boolean judgeAuthByWarehouseCode(String warehouseCode, StockSupplies stockSupplies) {
        //a. 该物料必须已经分配给了当前单据上的仓库所对应的仓库类型；
        //b. 该物料所在的分类属于当前单据上的仓库下面可以看到的物料分类。

        StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode(warehouseCode, WarehouseEnum.Status.NORMAL.getStatus());
        if (Objects.isNull(stockWarehouse) || Objects.isNull(stockWarehouse.getType())) {
            return false;
        }

        return stockWarehouseSuppliesCategoryService.judgeWareHouseSuppliesCategory(warehouseCode, stockSupplies.getCatFullCode())
                && stockSuppliesBusinessBaseInfoService.judgeAuthByWarehouseType(stockSupplies.getCode(), stockWarehouse.getType());
    }

    @Override
    public ResponseData searchStockSuppliesPurchase(SuppliesPurchaseQueryReqDTO suppliesPurchaseQueryReqDTO) {
//        List<String> concatResult=new ArrayList<>();
//        if(!CollectionUtils.isEmpty(suppliesPurchaseQueryReqDTO.getTypeCodeDTOList())){
//           for(SuppliesCodeAndWarehouseTypeCodeDTO suppliesCodeAndWarehouseTypeCodeDTO: suppliesPurchaseQueryReqDTO.getTypeCodeDTOList()){
//               concatResult.add(suppliesCodeAndWarehouseTypeCodeDTO.getSuppliesCode()+"_"+suppliesCodeAndWarehouseTypeCodeDTO.getWarehouseTypeCode());
//           }
//        }

        List<StockSuppliesPurchase> stockSuppliesPurchaseList = suppliesMapper.queryBySuppliesPurchaseQueryReqDTO(suppliesPurchaseQueryReqDTO);
        //类型转换
        List<SuppliesPurchaseRespDto> suppliesRespDtoList = ConvertUtil.convertToType(SuppliesPurchaseRespDto.class, stockSuppliesPurchaseList);

        return ResponseData.createSuccessResult(suppliesRespDtoList);
    }

    private String checkPageByPurchaseParam(SuppliesPurchaseSearchReqDto suppliesPurchaseSearchReqDto) {
        if (Objects.isNull(suppliesPurchaseSearchReqDto) || StringUtils.isBlank(suppliesPurchaseSearchReqDto.getCategoryId())
                || Objects.isNull(suppliesPurchaseSearchReqDto.getWarehouseType())) {
            return "必填参数不能为空";
        }

        return null;
    }

    @Override
    public List<StockSupplies> selectSuppliesList(final SuppliesReqDTO suppliesReqDTO) {
        return this.getList(suppliesReqDTO);
    }

    @Override
    public List<StockSupplies> getSuppliesList(final SuppliesReqDTO suppliesReqDTO) {
       return suppliesMapper.selectSuppliesListBySuppliesReqDTO(suppliesReqDTO);
    }

    @Override
    public ResponseData saveSuppliesProperty(SuppliesPropertyRepDTO suppliesPropertyRepDTO) {
        logger.info("物料属性扩展参数请求参数:" + JSON.toJSONString(suppliesPropertyRepDTO));
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //1。参数校验
        String resultCheck = checkParam(suppliesPropertyRepDTO, true);
        if (StringUtils.isNotBlank(resultCheck)) {
            return ResponseData.createFailResult(resultCheck);
        }
        //2。信息保存
        //物料采购属性不为空时
        if (null != suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO()) {
            SuppliesPurchaseReqDTO stockSuppliesPurchaseReqDTO = suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO();
            StockSuppliesPurchase stockSuppliesPurchase = new StockSuppliesPurchase();
            stockSuppliesPurchase.setSuppliesCode(stockSuppliesPurchaseReqDTO.getSuppliesCode());
            stockSuppliesPurchase.setDefaultCostItemCode(stockSuppliesPurchaseReqDTO.getDefaultCostItemCode());
            stockSuppliesPurchase.setReceiveType(stockSuppliesPurchaseReqDTO.getReceiveType());
            stockSuppliesPurchase.setWarehouseTypeCode(stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
            stockSuppliesPurchase.setInventoryManageFlag(stockSuppliesPurchaseReqDTO.getInventoryManageFlag());
            stockSuppliesPurchase.setCreatedBy(jwtUser.getEmployeeCode());
            stockSuppliesPurchase.setUpdatedBy(jwtUser.getEmployeeCode());
            //判断物料基本业务属性是否存在
            StockSuppliesBusinessBaseInfo stockSuppliesBusinessBaseInfo = stockSuppliesBusinessBaseInfoDao.getBySuppliesCodeAndWarehouseType(stockSuppliesPurchaseReqDTO.getSuppliesCode(), stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
            if (stockSuppliesBusinessBaseInfo != null) {
                stockSuppliesPurchase.setBaseId(stockSuppliesBusinessBaseInfo.getId());
            } else {
                StockSuppliesBusinessBaseInfo suppliesBusinessBaseInfo = new StockSuppliesBusinessBaseInfo();
                suppliesBusinessBaseInfo.setSuppliesCode(stockSuppliesPurchaseReqDTO.getSuppliesCode());
                suppliesBusinessBaseInfo.setWarehouseTypeCode(stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
                suppliesBusinessBaseInfo.setCreatedBy(jwtUser.getEmployeeCode());
                suppliesBusinessBaseInfo.setUpdatedBy(jwtUser.getEmployeeCode());
                suppliesBusinessBaseInfo.setIsDelFlag(MetaDataEnum.yesOrNo.NO.getValue());
                stockSuppliesBusinessBaseInfoMapper.insertSelective(suppliesBusinessBaseInfo);

                stockSuppliesPurchase.setBaseId(suppliesBusinessBaseInfo.getId());
            }
            stockSuppliesPurchaseMapper.insertSelective(stockSuppliesPurchase);

            //采购附加标示尾表信息
            List<StockSuppliesPurchaseExt> stockSuppliesPurchaseExtList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(stockSuppliesPurchaseReqDTO.getSuppliesPurchaseExtReqDTOList())) {
                for (StockSuppliesPurchaseExtReqDTO stockSuppliesPurchaseExtReqDTO : stockSuppliesPurchaseReqDTO.getSuppliesPurchaseExtReqDTOList()) {
                    StockSuppliesPurchaseExt stockSuppliesPurchaseExt = new StockSuppliesPurchaseExt();
                    stockSuppliesPurchaseExt.setSuppliesCode(stockSuppliesPurchaseReqDTO.getSuppliesCode());
                    stockSuppliesPurchaseExt.setCostItemCode(stockSuppliesPurchaseExtReqDTO.getCostItemCode());
                    stockSuppliesPurchaseExt.setWarehouseType(stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
                    stockSuppliesPurchaseExt.setCreatedBy(jwtUser.getEmployeeCode());
                    stockSuppliesPurchaseExt.setUpdatedBy(jwtUser.getEmployeeCode());
                    stockSuppliesPurchaseExt.setAddPurchaseMark(stockSuppliesPurchaseExtReqDTO.getAddPurchaseMark());
                    stockSuppliesPurchaseExt.setIsValid(MetaDataEnum.yesOrNo.YES.getValue());
                    stockSuppliesPurchaseExtList.add(stockSuppliesPurchaseExt);
                }
            }
            if (!CollectionUtils.isEmpty(stockSuppliesPurchaseExtList)) {
                suppliesMapper.batchInsertPurchaseExt(stockSuppliesPurchaseExtList);
            }

        }

        //物料库存属性不为空时
        if (null != suppliesPropertyRepDTO.getSuppliesInventoryReqDTO()) {
            SuppliesInventoryReqDTO suppliesInventoryReqDTO = suppliesPropertyRepDTO.getSuppliesInventoryReqDTO();

            StockSuppliesInventory stockSuppliesInventory = new StockSuppliesInventory();
            stockSuppliesInventory.setWarehouseTypeCode(suppliesInventoryReqDTO.getWarehouseTypeCode());
            stockSuppliesInventory.setDefaultAssetCategoryCode(suppliesInventoryReqDTO.getDefaultAssetCategoryCode());
            stockSuppliesInventory.setIsDelFlag(MetaDataEnum.yesOrNo.NO.getValue());
            stockSuppliesInventory.setSuppliesCode(suppliesInventoryReqDTO.getSuppliesCode());
            //根据资产分类code 查询资产分类信息
            StockAssetsCategory stockAssetsCategory = stockAssetsCategoryService.selectByCategoryCode(suppliesInventoryReqDTO.getDefaultAssetCategoryCode());
            if (null != stockAssetsCategory) {
                stockSuppliesInventory.setDefaultAssetCategory(stockAssetsCategory.getCategoryName());
            } else {
                stockSuppliesInventory.setDefaultAssetCategory("");
            }
            stockSuppliesInventoryMapper.insertSelective(stockSuppliesInventory);
        }
        return ResponseData.createSuccessResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateSuppliesProperty(SuppliesPropertyRepDTO suppliesPropertyRepDTO) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //1。参数校验
        String resultCheck = checkParam(suppliesPropertyRepDTO, false);
        if (StringUtils.isNotBlank(resultCheck)) {
            return ResponseData.createFailResult(resultCheck);
        }
        //2。查询信息判断

        //更新物料采购属性
        if (null != suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO()) {
            SuppliesPurchaseReqDTO stockSuppliesPurchaseReqDTO = suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO();
            List<StockSuppliesPurchase> stockSuppliesPurchaseList = stockSuppliesPurchaseDao
                    .listBySuppliesCodeListAndWarehouseType(Arrays.asList(stockSuppliesPurchaseReqDTO.getSuppliesCode()), stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
            if (CollectionUtils.isEmpty(stockSuppliesPurchaseList)) {
                return ResponseData.createFailResult("当前物料采购属性不存在,不能进行修改!");
            }
            stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO).setSuppliesCode(stockSuppliesPurchaseReqDTO.getSuppliesCode());
            stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO).setDefaultCostItemCode(stockSuppliesPurchaseReqDTO.getDefaultCostItemCode());
            stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO).setReceiveType(stockSuppliesPurchaseReqDTO.getReceiveType());
            stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO).setInventoryManageFlag(stockSuppliesPurchaseReqDTO.getInventoryManageFlag());
            stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO).setUpdatedBy(jwtUser.getEmployeeCode());
            stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO).setWarehouseTypeCode(stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
            stockSuppliesPurchaseMapper.updateByPrimaryKeySelective(stockSuppliesPurchaseList.get(CommonConstant.NUMBER_ZERO));

            List<StockSuppliesPurchaseExt> stockSuppliesPurchaseExtList = stockSuppliesPurchaseExtService.selectBySuppliesCodeAndWarehouseType(Arrays.asList(stockSuppliesPurchaseReqDTO.getSuppliesCode()), stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
            Map<String, StockSuppliesPurchaseExt> purchaseExtMap = stockSuppliesPurchaseExtList.stream().collect(Collectors.toMap(stockSuppliesPurchaseExt ->
                    stockSuppliesPurchaseExt.getSuppliesCode() + stockSuppliesPurchaseExt.getWarehouseType() + stockSuppliesPurchaseExt.getAddPurchaseMark(), stockSuppliesPurchaseExt -> stockSuppliesPurchaseExt, (key1, key2) -> key1));

            //更新采购附加信息
            List<StockSuppliesPurchaseExt> addStockSuppliesPurchaseExtList = new ArrayList<>();
            List<StockSuppliesPurchaseExt> updateStockSuppliesPurchaseExtList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(stockSuppliesPurchaseReqDTO.getSuppliesPurchaseExtReqDTOList())) {
                for (StockSuppliesPurchaseExtReqDTO stockSuppliesPurchaseExtReqDTO : stockSuppliesPurchaseReqDTO.getSuppliesPurchaseExtReqDTOList()) {
                    String key = stockSuppliesPurchaseExtReqDTO.getSuppliesCode() + stockSuppliesPurchaseExtReqDTO.getWarehouseType() + stockSuppliesPurchaseExtReqDTO.getAddPurchaseMark();
                    StockSuppliesPurchaseExt oldStockSuppliesPurchaseExt = purchaseExtMap.get(key);
                    if (null != oldStockSuppliesPurchaseExt) {
                        StockSuppliesPurchaseExt stockSuppliesPurchaseExt = ConvertUtil.convertToType(StockSuppliesPurchaseExt.class, stockSuppliesPurchaseExtReqDTO);
                        stockSuppliesPurchaseExt.setCreatedBy(oldStockSuppliesPurchaseExt.getCreatedBy());
                        stockSuppliesPurchaseExt.setUpdatedBy(oldStockSuppliesPurchaseExt.getUpdatedBy());
                        stockSuppliesPurchaseExt.setIsValid(oldStockSuppliesPurchaseExt.getIsValid());
                        updateStockSuppliesPurchaseExtList.add(stockSuppliesPurchaseExt);
                    } else {
                        StockSuppliesPurchaseExt stockSuppliesPurchaseExt = new StockSuppliesPurchaseExt();
                        stockSuppliesPurchaseExt.setSuppliesCode(stockSuppliesPurchaseReqDTO.getSuppliesCode());
                        stockSuppliesPurchaseExt.setCostItemCode(stockSuppliesPurchaseExtReqDTO.getCostItemCode());
                        stockSuppliesPurchaseExt.setWarehouseType(stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
                        stockSuppliesPurchaseExt.setCreatedBy(jwtUser.getEmployeeCode());
                        stockSuppliesPurchaseExt.setUpdatedBy(jwtUser.getEmployeeCode());
                        stockSuppliesPurchaseExt.setAddPurchaseMark(stockSuppliesPurchaseExtReqDTO.getAddPurchaseMark());
                        stockSuppliesPurchaseExt.setIsValid(MetaDataEnum.yesOrNo.YES.getValue());
                        addStockSuppliesPurchaseExtList.add(stockSuppliesPurchaseExt);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(addStockSuppliesPurchaseExtList)) {
                suppliesMapper.batchInsertPurchaseExt(addStockSuppliesPurchaseExtList);
            }
            if (!CollectionUtils.isEmpty(updateStockSuppliesPurchaseExtList)) {
                suppliesMapper.batchUpdatePurchaseExt(updateStockSuppliesPurchaseExtList);
            }
        }

        //更新物料库存属性
        if (null != suppliesPropertyRepDTO.getSuppliesInventoryReqDTO()) {
            SuppliesInventoryReqDTO stockSuppliesInventoryReqDTO = suppliesPropertyRepDTO.getSuppliesInventoryReqDTO();
            //根据仓库类型与物料编码 查询 当前仓库下 该物料的库存属性
            StockSuppliesInventory stockSuppliesInventory = stockSuppliesInventoryService.getBySuppliesCodeAndWarehouseType(stockSuppliesInventoryReqDTO.getSuppliesCode(), stockSuppliesInventoryReqDTO.getWarehouseTypeCode());
            stockSuppliesInventory.setWarehouseTypeCode(stockSuppliesInventoryReqDTO.getWarehouseTypeCode());
            stockSuppliesInventory.setDefaultAssetCategoryCode(stockSuppliesInventoryReqDTO.getDefaultAssetCategoryCode());
            stockSuppliesInventory.setIsDelFlag(MetaDataEnum.yesOrNo.NO.getValue());
            stockSuppliesInventory.setSuppliesCode(stockSuppliesInventoryReqDTO.getSuppliesCode());
            //根据资产分类code 查询资产分类信息
            StockAssetsCategory stockAssetsCategory = stockAssetsCategoryService.selectByCategoryCode(stockSuppliesInventoryReqDTO.getDefaultAssetCategoryCode());
            if (null != stockAssetsCategory) {
                stockSuppliesInventory.setDefaultAssetCategory(stockAssetsCategory.getCategoryName());
            } else {
                stockSuppliesInventory.setDefaultAssetCategory("");
            }
            stockSuppliesInventoryMapper.updateByPrimaryKeySelective(stockSuppliesInventory);
        }

        //修改物料基本属性
        if (null != suppliesPropertyRepDTO.getSuppliesDTO()) {
            updateSupplies(suppliesPropertyRepDTO.getSuppliesDTO());

        }
        return ResponseData.createSuccessResult("数据更新成功");
    }


    @Override
    public ResponseData querySuppliesPropertyDetail(SuppliesPropertyRepDTO suppliesPropertyRepDTO) {
        //1。参数校验
        if (StringUtils.isBlank(suppliesPropertyRepDTO.getSuppliesCode())) {
            return ResponseData.createFailResult("物料编码不能为空!");
        }
        if (null != suppliesPropertyRepDTO.getPropertyValue()) {
            if (!SuppliesEnum.PropertyType.BASE.getType().equals(suppliesPropertyRepDTO.getPropertyValue())) {
                if (null == suppliesPropertyRepDTO.getWarehouseTypeCode()) {
                    return ResponseData.createFailResult("仓库类型不能为空！");
                }
            }
        }

        //2。根据参数查询
        StockSupplies stockSupplies = getSuppliesByCode(suppliesPropertyRepDTO.getSuppliesCode(), null);
        if (null == stockSupplies) {
            return ResponseData.createFailResult("当前物料不存在！");
        }

        //定义返回结果
        SuppliesPropertyRespDTO suppliesPropertyRespDTO = new SuppliesPropertyRespDTO();

        //根据物料信息 仓库类型 查询物料采购属性
        if (null != suppliesPropertyRepDTO.getWarehouseTypeCode()) {
            List<SuppliesPurchaseRespDto> suppliesPurchaseRespDtoList = stockSuppliesPurchaseService
                    .listRespDtoBySuppliesCodeListAndWarehouseType(Arrays.asList(suppliesPropertyRepDTO.getSuppliesCode()), suppliesPropertyRepDTO.getWarehouseTypeCode());
            if (!CollectionUtils.isEmpty(suppliesPurchaseRespDtoList)) {
                //添加判断
                SuppliesPurchaseRespDto suppliesPurchaseRespDto = suppliesPurchaseRespDtoList.get(0);
                suppliesPurchaseRespDto.setRemark(StringUtils.isNotBlank(stockSupplies.getRemark()) ? stockSupplies.getRemark() : "");
                //根据物料加仓库类型查询采购属性尾表信息
                List<StockSuppliesPurchaseExt> stockSuppliesPurchaseExtList = stockSuppliesPurchaseExtService.selectBySuppliesCodeAndWarehouseType(Arrays.asList(suppliesPropertyRepDTO.getSuppliesCode()), suppliesPropertyRepDTO.getWarehouseTypeCode());
                if (!CollectionUtils.isEmpty(stockSuppliesPurchaseExtList)) {
                    List<StockSuppliesPurchaseExtRespDTO> stockSuppliesPurchaseExtRespDTOList = ConvertUtil.convertToType(StockSuppliesPurchaseExtRespDTO.class, stockSuppliesPurchaseExtList);
                    suppliesPurchaseRespDto.setSuppliesPurchaseExtRespDTOList(stockSuppliesPurchaseExtRespDTOList);
                }
                suppliesPropertyRespDTO.setSuppliesPurchaseRespDTO(suppliesPurchaseRespDto);
            }

            //根据物料信息 仓库类型 查询仓库属性
            StockSuppliesInventory stockSuppliesInventory = stockSuppliesInventoryService.getBySuppliesCodeAndWarehouseType(suppliesPropertyRepDTO.getSuppliesCode(), suppliesPropertyRepDTO.getWarehouseTypeCode());
            SuppliesInventoryRespDTO suppliesInventoryRespDTO = ConvertUtil.convertToType(SuppliesInventoryRespDTO.class, stockSuppliesInventory);

            suppliesPropertyRespDTO.setSuppliesInventoryRespDTO(suppliesInventoryRespDTO);
        }

        //查询物料基本属性
        ResponseData<SuppliesResponseDTO> responseData = querySuppliesDetail(suppliesPropertyRepDTO);
        if (responseData.isSuccess()) {
            SuppliesResponseDTO suppliesResponseDTO = responseData.getData();
            suppliesPropertyRespDTO.setSuppliesResponseDTO(suppliesResponseDTO);
        }

        return ResponseData.createSuccessResult(suppliesPropertyRespDTO);
    }


    @Override
    public ResponseData querySuppliesDetail(SuppliesPropertyRepDTO suppliesPropertyRepDTO) {
        //1。参数校验
        if (StringUtils.isBlank(suppliesPropertyRepDTO.getSuppliesCode())) {
            return ResponseData.createFailResult("请求参数不能为空！");
        }
        //2。根据条件查询数据
        List<StockSupplies> stockSupplies = selectSupplies(null, Arrays.asList(suppliesPropertyRepDTO.getSuppliesCode()));
        if (CollectionUtils.isEmpty(stockSupplies)) {
            return ResponseData.createFailResult("查询的当前物料信息不存在！");
        }
        //3。类型转换
        //详情查询根据物料编码查询 唯一的 所以取出第一个即是返回结果
        StockSupplies supplies = stockSupplies.get(0);
        SuppliesResponseDTO suppliesResponseDTO = ConvertUtil.convertToType(SuppliesResponseDTO.class, supplies);
        return ResponseData.createSuccessResult(suppliesResponseDTO);
    }

    /**
     * 参数校验
     *
     * @param suppliesPropertyRepDTO
     * @return
     */
    private String checkParam(SuppliesPropertyRepDTO suppliesPropertyRepDTO, Boolean flag) {
        //判断物料采购属性参数
        if (null != suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO()) {
            SuppliesPurchaseReqDTO stockSuppliesPurchaseReqDTO = suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO();
            if (StringUtils.isBlank(stockSuppliesPurchaseReqDTO.getSuppliesCode())) {
                return "物料编码不能为空！";
            }
            if (StringUtils.isBlank(stockSuppliesPurchaseReqDTO.getDefaultCostItemCode())) {
                return "费用项不能为空！";
            }
            if (stockSuppliesPurchaseReqDTO.getWarehouseTypeCode() == null) {
                return "仓库类型不能为空！";
            }
            if (stockSuppliesPurchaseReqDTO.getInventoryManageFlag() == null) {
                return "是否启用仓储管理不能为空";
            }
            if (stockSuppliesPurchaseReqDTO.getReceiveType() == null) {
                return "收货类型不能为空！";
            }
//            if (stockSuppliesPurchaseReqDTO.getPropertyType() == null) {
//                return "物料属性类型不能为空！";
//            }
            StringBuffer stringBuffer = new StringBuffer();
            if (flag) {
                if (!CollectionUtils.isEmpty(stockSuppliesPurchaseReqDTO.getSuppliesPurchaseExtReqDTOList())) {
                    for (int i = 0; i < stockSuppliesPurchaseReqDTO.getSuppliesPurchaseExtReqDTOList().size(); i++) {
                        StockSuppliesPurchaseExtReqDTO stockSuppliesPurchaseExtReqDTO = suppliesPropertyRepDTO.getSuppliesPurchaseReqDTO().getSuppliesPurchaseExtReqDTOList().get(i);
                        StockSuppliesPurchaseExt stockSuppliesPurchaseExt = stockSuppliesPurchaseExtService.selectByQuery(stockSuppliesPurchaseReqDTO.getSuppliesCode(), stockSuppliesPurchaseReqDTO.getWarehouseTypeCode(), stockSuppliesPurchaseExtReqDTO.getAddPurchaseMark());
                        if (null != stockSuppliesPurchaseExt) {
                            stringBuffer.append("第" + (i + 1) + "附加采购标示已经存在不允许重复添加");
                        }
                    }
                }
            }

            if (stringBuffer.toString().length() > 0) {
                return stringBuffer.toString();
            }
            StockSupplies stockSupplies = getSuppliesByCode(stockSuppliesPurchaseReqDTO.getSuppliesCode(), null);
            if (null == stockSupplies) {
                return "当前物料不存在！";
            }
            //新增时判断属性信息是否已经扩展
            if (flag) {
                List<StockSuppliesPurchase> stockSuppliesPurchaseList = stockSuppliesPurchaseDao
                        .listBySuppliesCodeListAndWarehouseType(Arrays.asList(stockSuppliesPurchaseReqDTO.getSuppliesCode()), stockSuppliesPurchaseReqDTO.getWarehouseTypeCode());
                if (!CollectionUtils.isEmpty(stockSuppliesPurchaseList)) {
                    return "物料在当前勾选的仓库类型下已扩展采购属性！";
                }
            }
        }

        //判断物料库存属性
        if (null != suppliesPropertyRepDTO.getSuppliesInventoryReqDTO()) {
            SuppliesInventoryReqDTO suppliesInventoryReqDTO = suppliesPropertyRepDTO.getSuppliesInventoryReqDTO();
            if (StringUtils.isBlank(suppliesInventoryReqDTO.getSuppliesCode())) {
                return "物料编码不能为空！";
            }
            if (suppliesInventoryReqDTO.getWarehouseTypeCode() == null) {
                return "仓库类型不能为空！";
            }
            if (StringUtils.isBlank(suppliesInventoryReqDTO.getDefaultAssetCategoryCode())) {
                return "资产类别不能为空！";
            }
            StockSupplies stockSupplies = getSuppliesByCode(suppliesInventoryReqDTO.getSuppliesCode(), null);
            if (null == stockSupplies) {
                return "当前物料不存在！";
            }
            //新增时判断属性信息是否已经扩展
            if (flag) {
                StockSuppliesInventory stockSuppliesInventory = stockSuppliesInventoryService.getBySuppliesCodeAndWarehouseType(suppliesPropertyRepDTO.getSuppliesCode(), suppliesPropertyRepDTO.getWarehouseTypeCode());
                if (null != stockSuppliesInventory) {
                    return "物料在当前勾选的仓库类型下已扩展库存属性！";
                }
            }
        }
        return null;
    }
    /**
     * @param: stockAssetsDemandHead,user
     * @description: 查询suppliesMap
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/11
     */
    @Override
    public List<SuppliesRespDTO> getSuppliesListByDemand(List<String> categoryCode, String warehouseCode){
        List<SuppliesRespDTO> suppliesRespDTOList = suppliesMapper.selectSuppliesByDemand(categoryCode, warehouseCode);
        return suppliesRespDTOList;
    }

    @Override
    public Map<String, BigDecimal> getCategoryNumberMapByDemand(List<String> categoryCode, String warehouseCode){
        List<SuppliesRespDTO> suppliesRespDTOList = suppliesMapper.selectSuppliesByDemand(categoryCode, warehouseCode);
        if(CollectionUtils.isEmpty(suppliesRespDTOList)){
            return new HashMap<>();
        }
        // 按照资产类别统计每个资产类别下的物料编码数量集合
        Map<String, BigDecimal> assetsCategoryAndActualQuantityMap = new HashMap<>();
        for (SuppliesRespDTO suppliesRespDTO : suppliesRespDTOList) {
            BigDecimal suppliesNum = assetsCategoryAndActualQuantityMap.getOrDefault(suppliesRespDTO.getCategoryCode(), BigDecimal.ZERO);
            assetsCategoryAndActualQuantityMap.put(suppliesRespDTO.getCategoryCode(), suppliesNum.add(suppliesRespDTO.getActualQuantity()));
        }
        return assetsCategoryAndActualQuantityMap;
    }
    /**
     * @param: suppliesReqDTO
     * @description: 根据仓库编码和物料编码集合查询物料
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/13
     */
    @Override
    public ResponseData querySuppliesQuantity(SuppliesReqDTO suppliesReqDTO) {
        String warehouseCode = suppliesReqDTO.getWarehouseCode();
        List<String> codeList = suppliesReqDTO.getCodeList();
        if(StringUtils.isEmpty(warehouseCode)){
            return ResponseData.createFailResult("仓库编码不能为空");
        }
        if(CollectionUtils.isEmpty(codeList)){
            return ResponseData.createFailResult("资产分类编码不能为空");
        }
        SuppliesReqDO suppliesReqDO = new SuppliesReqDO();
        suppliesReqDO.setWarehouseCode(warehouseCode);
        suppliesReqDO.setCategoryCodeList(codeList);
        Map<String, BigDecimal> assetsCategoryAndActualQuantityMap = queryCategoryQuantity(suppliesReqDO);
        List<SuppliesRespDTO> suppliesRespDTOList = new ArrayList<>();
        for(Map.Entry<String, BigDecimal> entry : assetsCategoryAndActualQuantityMap.entrySet()){
            SuppliesRespDTO suppliesRespDTO = new SuppliesRespDTO();
            suppliesRespDTO.setCategoryCode(entry.getKey());
            suppliesRespDTO.setActualQuantity(entry.getValue());
            suppliesRespDTOList.add(suppliesRespDTO);
        }
        return ResponseData.createSuccessResult(suppliesRespDTOList);

    }

    @Override
    public Map<String, BigDecimal> queryCategoryQuantity(SuppliesReqDO suppliesReqDO){
        List<String> categoryCodeList = suppliesReqDO.getCategoryCodeList();
        List<SuppliesRespDTO> suppliesRespDTOList = suppliesMapper.selectSuppliesByDemand(suppliesReqDO.getCategoryCodeList(), suppliesReqDO.getWarehouseCode());
        // 按照资产类别统计每个资产类别下的物料编码数量集合
        Map<String, BigDecimal> assetsCategoryAndActualQuantityMap = new HashMap<>();
        for (SuppliesRespDTO suppliesRespDTO : suppliesRespDTOList) {
            BigDecimal suppliesNum = assetsCategoryAndActualQuantityMap.getOrDefault(suppliesRespDTO.getCategoryCode(), BigDecimal.ZERO);
            assetsCategoryAndActualQuantityMap.put(suppliesRespDTO.getCategoryCode(), suppliesNum.add(suppliesRespDTO.getActualQuantity()));
        }
        // 如果没有的赋予值为0
        categoryCodeList.forEach(code -> {
            if(!assetsCategoryAndActualQuantityMap.containsKey(code)){
                assetsCategoryAndActualQuantityMap.put(code, BigDecimal.ZERO);
            }
        });
        return assetsCategoryAndActualQuantityMap;
    }

    @Override
    public List<SuppliesDTO> selectSuppliesList(List<String> codes) {
        return suppliesMapper.selectSuppliesInfoBySuppliesCodeList(codes);
    }

}
