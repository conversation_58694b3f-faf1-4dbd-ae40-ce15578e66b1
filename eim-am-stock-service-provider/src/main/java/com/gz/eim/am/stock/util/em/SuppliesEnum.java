package com.gz.eim.am.stock.util.em;

/**
 * <AUTHOR>
 * @date 2019-09-24 下午 2:51
 */
public class SuppliesEnum {

    /**
     * 物料状态
     */
    public enum Status {
        /**
         * 禁用
         */
        FORBID(0, "禁用"),
        /**
         * 正常
         */
        NORMAL(1, "启用"),
        /**
         * 不可修改
         */
        READ_ONLY(2, "不可修改");

        Status(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        private Integer status;
        private String desc;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    /**
     * 物料管理类型
     */
    public enum ManageType {
        /**
         * 无需管理
         */
        NOT_MANAGE(0),
        /**
         * 生产批次管理
         */
        BATCH_MANAGE(1),
        /**
         * 序列号管理
         */
        SN_MANAGE(2),
        ;

        ManageType(Integer type) {
            this.type = type;
        }

        private Integer type;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }

    /**
     * 物料属性
     */
    public enum PropertyType {
        /**
         * 基础属性
         */
        BASE(0,"基础属性"),
        /**
         * 计划属性
         */
        PLAN(1,"计划属性"),
        /**
         * 生产属性
         */
        PRODUCT(2,"生产属性"),
        /**
         * 采购属性
         */
        PURCHASE(3,"采购属性"),
        /**
         * 销售属性
         */
        SALE(4,"销售属性"),
        /**
         * 库存属性
         */
        STOCK(5,"库存属性"),
        /**
         * 财务属性
         */
        FINANCE(6,"财务属性")
        ;

        PropertyType(Integer type, String des) {
            this.type = type;
            this.des = des;
        }

        private Integer type;

        private String des;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getDes() {
            return des;
        }

        public void setDes(String des) {
            this.des = des;
        }
    }

    /**
     * 物料类型
     */
    public enum SuppliesType {

        //  1：普通物料 、2 ：固定资产、3：低值资产、4：低值易耗、5：父物料 、6：虚拟物料'、7：现金物料'
        /**
         * 普通物料
         */
        NORMAL(1,"普通物料"),
        /**
         * 固定资产
         */
        SOLD(2,"固定资产"),
        /**
         * 低值资产
         */
        LOW_VALIUE(3,"低值资产"),
        /**
         * 低值易耗
         */
        LOW_CONSUME(4,"低值易耗"),
        /**
         * 父物料
         */
        PARENT(5,"父物料"),
        /**
         * 虚拟物料
         */
        INVENTED(6,"虚拟物料"),
        /**
         * 现金物料
         */
        CASH(7,"现金物料")
        ;

        SuppliesType(Integer type, String des) {
            this.type = type;
            this.des = des;
        }

        private Integer type;

        private String des;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getDes() {
            return des;
        }

        public void setDes(String des) {
            this.des = des;
        }
    }
}
