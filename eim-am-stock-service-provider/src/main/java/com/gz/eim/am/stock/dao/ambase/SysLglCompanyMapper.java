package com.gz.eim.am.stock.dao.ambase;


import com.gz.eim.am.stock.entity.ambase.SysLglCompany;
import com.gz.eim.am.stock.entity.ambase.SysLglCompanyExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysLglCompanyMapper {
    long countByExample(SysLglCompanyExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SysLglCompany record);

    int insertSelective(SysLglCompany record);

    List<SysLglCompany> selectByExample(SysLglCompanyExample example);

    SysLglCompany selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SysLglCompany record, @Param("example") SysLglCompanyExample example);

    int updateByExample(@Param("record") SysLglCompany record, @Param("example") SysLglCompanyExample example);

    int updateByPrimaryKeySelective(SysLglCompany record);

    int updateByPrimaryKey(SysLglCompany record);
}