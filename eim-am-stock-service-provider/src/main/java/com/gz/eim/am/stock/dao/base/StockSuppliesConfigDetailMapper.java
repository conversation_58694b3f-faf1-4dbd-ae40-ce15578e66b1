package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSuppliesConfigDetail;
import com.gz.eim.am.stock.entity.StockSuppliesConfigDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSuppliesConfigDetailMapper {
    long countByExample(StockSuppliesConfigDetailExample example);

    int deleteByPrimaryKey(Long configDetailId);

    int insert(StockSuppliesConfigDetail record);

    int insertSelective(StockSuppliesConfigDetail record);

    List<StockSuppliesConfigDetail> selectByExample(StockSuppliesConfigDetailExample example);

    StockSuppliesConfigDetail selectByPrimaryKey(Long configDetailId);

    int updateByExampleSelective(@Param("record") StockSuppliesConfigDetail record, @Param("example") StockSuppliesConfigDetailExample example);

    int updateByExample(@Param("record") StockSuppliesConfigDetail record, @Param("example") StockSuppliesConfigDetailExample example);

    int updateByPrimaryKeySelective(StockSuppliesConfigDetail record);

    int updateByPrimaryKey(StockSuppliesConfigDetail record);
}