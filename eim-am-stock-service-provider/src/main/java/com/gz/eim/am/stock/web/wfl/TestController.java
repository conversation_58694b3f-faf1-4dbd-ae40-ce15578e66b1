package com.gz.eim.am.stock.web.wfl;

import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.common.util.OKHttpClientUtils;
import com.gz.eim.am.stock.api.wfl.StockWflApi;
import com.gz.eim.am.stock.api.wfl.TestApi;

import com.gz.eim.am.stock.dto.response.AppDeviceRespDTO;
import com.lop.open.api.sdk.internal.fastjson.JSON;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: l<PERSON>uy<PERSON>
 * @date: 2019/11/14
 * @description: 工作流
 */
@RestController
@RequestMapping("/api/am/test")
public class TestController implements TestApi {



    @Override
    public ResponseData test1() {
        Map headMap = new HashMap();
        headMap.put("guagua-token", SecurityUtil.getSsoToken());
        String responseResult = OKHttpClientUtils.get("http://testhome-app.guazi-cloud.com/sys/device/listUserDevice", MapUtils.EMPTY_MAP, headMap);
        ResponseData responseData = JSONObject.parseObject(responseResult, ResponseData.class);

        List<AppDeviceRespDTO> appDeviceRespDTOS = JSON.parseArray(JSONObject.toJSONString(responseData.getData()), AppDeviceRespDTO.class);

        return null;
    }
}
