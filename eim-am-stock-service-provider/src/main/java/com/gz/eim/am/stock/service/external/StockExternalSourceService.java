package com.gz.eim.am.stock.service.external;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.external.order.StockSuppliesQuantityReduceHeadReqDTO;

import java.text.ParseException;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/2
 * @description:
 */
public interface StockExternalSourceService {

    /**
     * 根据序列号获取在领用出库-
     * @param snNo
     * @return
     * @throws ParseException
     */
    ResponseData selectUseDeliverySuppliesBySnNo(String snNo) throws ParseException;



    /**
     * 根据类型查询当前人是否是卡仓管理员 或执照的法人或执照的领用人
     * @param empId
     * @param type
     * @return
     * @throws ParseException
     */
    ResponseData selectCardStockQuantity(String empId,Integer type) throws ParseException;


    /**
     * 兑换卡
     */
    ResponseData convertInventorySupplies(StockSuppliesQuantityReduceHeadReqDTO dto) throws RuntimeException;

    /**
     * 获取当前人员名下未归还印章数据
     * @param empId
     * @return
     */
    ResponseData queryUserBorrowSeal(String empId,Boolean sendMsg) throws ParseException;

    /**
     * 获取当前人员名下未归还执照数据
     * @param empId
     * @return
     */
    ResponseData queryUserBorrowLicense(String empId,Boolean sendMsg,Integer isFlowPlatform) throws ParseException;
}
