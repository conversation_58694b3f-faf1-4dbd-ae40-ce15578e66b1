package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.dao.assets.AssetsScrapLineMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsScrapHeadMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsScrapLineMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsScrapHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportBatchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.assets.AssetsCostRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsScrapHeadRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsScrapLineRespDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysDept;
import com.gz.eim.am.stock.entity.ambase.SysDict;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.vo.StockAssetsScrapImportExcel;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsOperationLogService;
import com.gz.eim.am.stock.service.assets.StockAssetsScrapService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.inventory.StockInventoryAssetImportService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanHeadService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanLineService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.service.wfl.WflService;
import com.gz.eim.am.stock.util.common.ListUtil;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.common.SetDefaultValueUtils;
import com.gz.eim.am.stock.util.common.WorkFlowUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2021/3/24
 * @description
 */
@Slf4j
@Service
public class StockAssetsScrapServiceImpl implements StockAssetsScrapService {

    @Value("${project.file.systemModule}")
    private String systemModule;
    @Value("${project.file.attachModule}")
    private String attachModule;
    @Value("${project.file.filePath}")
    private String filePath;

    @Autowired
    private AssetsMapper assetsMapper;
    @Autowired
    private StockAssetsService stockAssetsService;

    @Autowired
    private StockInventoryAssetImportService stockInventoryAssetImportService;

    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Autowired
    private StockWarehouseService stockWarehouseService;

    @Autowired
    private StockAssetsScrapHeadMapper stockAssetsScrapHeadMapper;

    @Autowired
    private StockAssetsScrapLineMapper stockAssetsScrapLineMapper;

    @Autowired
    private AssetsScrapLineMapper assetsScrapLineMapper;

    @Autowired
    private FileServiceApi fileServiceApi;

    @Autowired
    private WflService wflService;
    @Autowired
    StockDeliveryPlanHeadService stockDeliveryPlanHeadService;
    @Autowired
    StockDeliveryPlanLineService stockDeliveryPlanLineService;

    @Override
    public ResponseData manualAddAssets(InventoryAssetImportBatchReqDTO inventoryAssetImportBatchReqDTO) {
        //1.参数校验
        String checkResult = checkManualAddParam(inventoryAssetImportBatchReqDTO);
        if (StringUtils.isNotBlank(checkResult)) {
            return ResponseData.createFailResult(checkResult);
        }

        List<InventoryAssetImportReqDTO> inventoryAssetImportReqDTOS = inventoryAssetImportBatchReqDTO.getInventoryAssetImportBatchReqDTOList();
        if (CollectionUtils.isEmpty(inventoryAssetImportReqDTOS)) {
            log.info("当前保存数据已经添加过或者已经报废");
            return ResponseData.createSuccessResult();
        }
        //2.批量插入
        List<StockInventoryAssetImport> stockInventoryAssetImportList = new ArrayList<>(inventoryAssetImportReqDTOS.size());
        JwtUser user = SecurityUtil.getJwtUser();
        inventoryAssetImportReqDTOS.forEach(dto -> {
            StockInventoryAssetImport stockInventoryAssetImport = new StockInventoryAssetImport();
            stockInventoryAssetImport.setInventoryAssetBatchCode(inventoryAssetImportBatchReqDTO.getBindBatchCode());
            stockInventoryAssetImport.setAssetCode(dto.getAssetCode());
            stockInventoryAssetImport.setStatus(dto.getScrapStatus());
            stockInventoryAssetImport.setCreatedBy(user.getEmployeeCode());
            stockInventoryAssetImport.setUpdatedBy(user.getEmployeeCode());
            stockInventoryAssetImport.setCreatedAt(new Date());
            stockInventoryAssetImport.setUpdatedAt(new Date());
            try {
                SetDefaultValueUtils.defaultValue(stockInventoryAssetImport);
            } catch (Exception e) {
                e.printStackTrace();
            }
            stockInventoryAssetImportList.add(stockInventoryAssetImport);
        });

        return stockInventoryAssetImportService.saveAssetPlanAssetImport(stockInventoryAssetImportList);
    }

    @Override
    public ResponseData excelAddAssets(MultipartFile file, String batchCode) throws IOException {
        // 1.校验参数
        if (file == null || StringUtils.isBlank(batchCode)) {
            return ResponseData.createFailResult("参数不能为空");
        }

        // 批次号去空格
        batchCode = batchCode.trim();

        List<StockAssetsScrapImportExcel> stockAssetsScrapImportExcelList = ExcelUtil.importExcel(file.getInputStream(), StockAssetsScrapImportExcel.class);



//        HashSet<StockAssetsScrapImportExcel> stockAssetsScrapImportExcelHashSet = new HashSet<>(stockAssetsScrapImportExcelList);
//        List<StockAssetsScrapImportExcel> stockAssetsScrapImportExcels = new ArrayList<>(stockAssetsScrapImportExcelHashSet);

        List<String> assetsCodeList = stockAssetsScrapImportExcelList.stream().map(dto -> dto.getAssetCode()).collect(Collectors.toList());

        // 2.过滤掉已经保存的资产
        filterAlreadySaveAssets(assetsCodeList, batchCode);
        if (CollectionUtils.isEmpty(assetsCodeList)) {
            return ResponseData.createFailResult("导入的资产已经添加");
        }
        stockAssetsScrapImportExcelList = stockAssetsScrapImportExcelList.stream().filter(dto -> assetsCodeList.contains(dto.getAssetCode())).collect(Collectors.toList());

        //查询报废申请单行信息
        Map<String,StockAssetsScrapLine> scrapLineMap = selectByAssetsCodeList(assetsCodeList);
        Set<String> assetsCodeSet = new HashSet<>();
//        filterAlreadyScrapAssets(assetsCodeList);
//        if (CollectionUtils.isEmpty(assetsCodeList)) {
//            return ResponseData.createFailResult("导入的资产都是已经报废的资产");
//        }

        AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
        assetsSearchDTO.setAssetsCodeList(assetsCodeList);
        //assetsSearchDTO.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
        Map<String,StockAssets> stringStockAssetsMap = stockAssetsService.selectAssetsMapByAssetsSearchDTO(assetsSearchDTO);
        //批量插入
        List<StockInventoryAssetImport> stockInventoryAssetImportList = new ArrayList<>();
//        List<StockAssetsScrapImportExcel> newStockAssetsScrapImportExcels = stockAssetsScrapImportExcels.stream().filter(dto -> assetsCodeList.contains(dto.getAssetCode())).collect(Collectors.toList());
        JwtUser user = SecurityUtil.getJwtUser();
        for (int i = 0; i < stockAssetsScrapImportExcelList.size(); i++) {
            StockAssetsScrapImportExcel stockAssetsScrapImportExcel = stockAssetsScrapImportExcelList.get(i);
            //校验上传资产信息
            String resultCheck = checkStockAssetsInfo(stockAssetsScrapImportExcel.getAssetCode(), stringStockAssetsMap, scrapLineMap, assetsCodeSet,i);
            StockInventoryAssetImport stockInventoryAssetImport = new StockInventoryAssetImport();
            stockInventoryAssetImport.setInventoryAssetBatchCode(batchCode);
            stockInventoryAssetImport.setAssetCode(stockAssetsScrapImportExcel.getAssetCode());
            stockInventoryAssetImport.setAttr1(stockAssetsScrapImportExcel.getAttr1());
            stockInventoryAssetImport.setStatus(StringUtils.isNotBlank(stockAssetsScrapImportExcel.getStatus()) ? Integer.valueOf(stockAssetsScrapImportExcel.getStatus()) : CommonConstant.NUMBER_ZERO);
            //资产校验信息
            stockInventoryAssetImport.setAttr2(resultCheck);
            stockInventoryAssetImport.setCreatedBy(user.getEmployeeCode());
            stockInventoryAssetImport.setUpdatedBy(user.getEmployeeCode());
            stockInventoryAssetImport.setCreatedAt(new Date());
            stockInventoryAssetImport.setUpdatedAt(new Date());
            try {
                SetDefaultValueUtils.defaultValue(stockInventoryAssetImport);
            } catch (Exception e) {
                e.printStackTrace();
            }
            stockInventoryAssetImportList.add(stockInventoryAssetImport);
        }

        return stockInventoryAssetImportService.saveAssetPlanAssetImport(stockInventoryAssetImportList);
    }


    /**
     * 资产信息校验
     *
     * @param assetsCode
     * @param stringStockAssetsMap
     * @param scrapLineMap
     * @param assetsCodeSet
     * @return
     */
    public String checkStockAssetsInfo(String assetsCode, Map<String, StockAssets> stringStockAssetsMap, Map<String, StockAssetsScrapLine> scrapLineMap, Set<String> assetsCodeSet, int i) {
        StockAssets stockAssets = stringStockAssetsMap.get(assetsCode);
        if (null == stockAssets) {
            return "资产编码不存在";
        }
        //集合中第二个元素开始比较是否有重复数据
        if (assetsCodeSet.contains(assetsCode)) {
            return "存在重复上载的资产";
        }
        if (null != scrapLineMap.get(assetsCode)) {
            return "该资产存在相同的报废申请记录";
        }
        Boolean flag = null != stockAssets && (AssetsEnum.statusType.IDLE.getValue().equals(stockAssets.getStatus()) || AssetsEnum.statusType.USED.getValue().equals(stockAssets.getStatus())) && !AssetsEnum.Conditions.DISCAERD.getValue().equals(stockAssets.getConditions()) && AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAssets.getApproveStatus());
        if (!flag) {
            return "该资产状态不对，不允许报废";
        }
        if(AssetsEnum.Conditions.TRANSFER.getValue().equals(stockAssets.getConditions())){
            return "该资产在转移审批中，不允许报废";
        }
        assetsCodeSet.add(assetsCode);
        return "";
    }


    /**
     * 根据资产编码查询报废申请单行信息
     * @param assetsCodeList
     * @return
     */
    public Map<String,StockAssetsScrapLine> selectByAssetsCodeList(List<String> assetsCodeList){
        Map<String,StockAssetsScrapLine> scrapLineMap = new HashMap<>(assetsCodeList.size());
        List<StockAssetsScrapLine> stockAssetsScrapLineList = assetsScrapLineMapper.queryLinesByAssetsCodeList(assetsCodeList);
        if(CollectionUtils.isNotEmpty(stockAssetsScrapLineList)){
            for(StockAssetsScrapLine stockAssetsScrapLine:stockAssetsScrapLineList){
                scrapLineMap.put(stockAssetsScrapLine.getAssetsCode(),stockAssetsScrapLine);
            }
        }
        return scrapLineMap;
    }


    @Override
    public ResponseData queryTempAddAssets(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO) {
        //1.数据校验
        if (inventoryAssetImportSearchReqDTO == null || StringUtils.isBlank(inventoryAssetImportSearchReqDTO.getBindBatchCode())) {
            return ResponseData.createFailResult("页面参数不能为空");
        }

        //批次号去空格
        inventoryAssetImportSearchReqDTO.setBindBatchCode(inventoryAssetImportSearchReqDTO.getBindBatchCode().trim());

        //定义返回数据
        StockAssetsScrapHeadRespDTO stockAssetsScrapHeadRespDTO = new StockAssetsScrapHeadRespDTO();

        inventoryAssetImportSearchReqDTO.initPageParam();
        //查询总数量
        Long count = stockInventoryAssetImportService.selectInventoryAssetImportCount(inventoryAssetImportSearchReqDTO.getBindBatchCode());
        stockAssetsScrapHeadRespDTO.setRealScrapCount(count.intValue());
        if (count <= CommonConstant.NUMBER_ZERO) {
            stockAssetsScrapHeadRespDTO.setRealScrapValue(new BigDecimal("0"));
            return ResponseData.createSuccessResult(stockAssetsScrapHeadRespDTO);
        }
        //查询资产总净值
        BigDecimal netValue = stockInventoryAssetImportService.selectAssetImportNetValue(inventoryAssetImportSearchReqDTO.getBindBatchCode());
        stockAssetsScrapHeadRespDTO.setRealScrapValue(netValue);

        //2.分页获取报废资产
        inventoryAssetImportSearchReqDTO.initPageParam();
        List<StockInventoryAssetImport> stockInventoryAssetImportList = stockInventoryAssetImportService.selectInventoryAssetImportBySelective(inventoryAssetImportSearchReqDTO, inventoryAssetImportSearchReqDTO.getPageSize(), inventoryAssetImportSearchReqDTO.getStartNum(), "inventory_asset_imp_id");

        //3.补全返回信息
        List<StockAssetsScrapLineRespDTO> stockAssetsScrapLineRespDTOList = new ArrayList<>(stockInventoryAssetImportList.size());
        settingExtendField(stockInventoryAssetImportList, stockAssetsScrapLineRespDTOList);

        stockAssetsScrapHeadRespDTO.setStockAssetsScrapLineRespDTOList(stockAssetsScrapLineRespDTOList);
        return ResponseData.createSuccessResult(stockAssetsScrapHeadRespDTO);
    }



    @Override
    public ResponseData deleteTempAssets(Integer lineId) {
        if (lineId == null) {
            return ResponseData.createFailResult("参数不能为空");
        }
        stockInventoryAssetImportService.deleteAssetsById(lineId);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData updateTempAssets(Integer lineId, Integer scrapReason) {
        if (lineId == null) {
            return ResponseData.createFailResult("参数不能为空");
        }
        stockInventoryAssetImportService.updateAssetsById(lineId, scrapReason);
        return ResponseData.createSuccessResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData createScrap(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException {
        //1.参数校验
        String checkResult = checkCreateScrapParam(stockAssetsScrapHeadReqDTO);
        if (StringUtils.isNotBlank(checkResult)) {
            return ResponseData.createFailResult(checkResult);
        }
        JwtUser user = SecurityUtil.getJwtUser();
        //2.headid不存在则保存头表信息,状态=已保存 ，headid存在则进行更新操作
        if (null != stockAssetsScrapHeadReqDTO.getHeadId()) {
            StockAssetsScrapHead stockAssetsScrapHead = stockAssetsScrapHeadMapper.selectByPrimaryKey(stockAssetsScrapHeadReqDTO.getHeadId());
            if (stockAssetsScrapHead == null) {
                return ResponseData.createFailResult("根据id没有查到对应的单据信息:" + stockAssetsScrapHeadReqDTO.getHeadId());
            }
            //获取总数量
            Long count = stockInventoryAssetImportService.selectInventoryAssetImportCount(stockAssetsScrapHead.getBindBatchCode());
            if (count <= CommonConstant.NUMBER_ZERO) {
                return ResponseData.createFailResult("更新后的报废资产数量不能为0");
            }
            stockAssetsScrapHead.setPlanScrapCount(count.intValue());
            stockAssetsScrapHead.setRealScrapCount(count.intValue());
            //查询资产总净值
            BigDecimal netValue = stockInventoryAssetImportService.selectAssetImportNetValue(stockAssetsScrapHead.getBindBatchCode());
            stockAssetsScrapHead.setPlanScrapValue(netValue);
            stockAssetsScrapHead.setRealScrapValue(netValue);
            //备注字段更新
            stockAssetsScrapHead.setRemark(stockAssetsScrapHeadReqDTO.getRemark());

            stockAssetsScrapHeadMapper.updateByPrimaryKey(stockAssetsScrapHead);
            return ResponseData.createSuccessResult(stockAssetsScrapHead);
        } else {

            StockAssetsScrapHead stockAssetsScrapHead = new StockAssetsScrapHead();
            saveScrapHead(stockAssetsScrapHeadReqDTO, stockAssetsScrapHead, user);

            //3.组织行表数据
            //saveScrapLines(stockAssetsScrapHeadReqDTO,stockAssetsScrapHead,user);

            //5.调用ambase，绑定附件的业务主键
            if (CollectionUtils.isNotEmpty(stockAssetsScrapHeadReqDTO.getAttachIds())) {
                List<SysAttachReqDTO> sysAttachReqDTOList = new ArrayList<>(stockAssetsScrapHeadReqDTO.getAttachIds().size());
                for(Long attachId:stockAssetsScrapHeadReqDTO.getAttachIds()){
                    SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO();
                    sysAttachReqDTO.setRelId(stockAssetsScrapHead.getScrapNo());
                    sysAttachReqDTO.setAttachId(attachId);
                    sysAttachReqDTO.setPageModule(FileConstant.PAGE_MODE_CARD_ASSET_SCRAP);
                    sysAttachReqDTOList.add(sysAttachReqDTO);
                }
                ResponseData responseData = fileServiceApi.updateSysAttachList(sysAttachReqDTOList);
                if (!ResponseCode.SUCCESS_CODE.equals(responseData.getCode())) {
                    //调用接口失败，回滚事物
                    log.error("调用ambase绑定附件信息失败：" + responseData.getMessage());
                    throw new ServiceUncheckedException("报废单绑定附件信息失败");
                }
            }

            return ResponseData.createSuccessResult(stockAssetsScrapHead);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData submitScrap(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException {
        //1.状态校验
        JwtUser user = SecurityUtil.getJwtUser();
        Long headId = stockAssetsScrapHeadReqDTO.getHeadId();
        StockAssetsScrapHead stockAssetsScrapHead = null;

        if (headId == null) {
            //参数校验
            String checkResult = checkCreateScrapParam(stockAssetsScrapHeadReqDTO);
            if (StringUtils.isNotBlank(checkResult)) {
                return ResponseData.createFailResult(checkResult);
            }
            //保存提交一块处理
            stockAssetsScrapHead = new StockAssetsScrapHead();
            stockAssetsScrapHeadReqDTO.setScrapStatus(AssetsScrapEnum.scrapStatus.SUBMIT.getValue());
            saveScrapHead(stockAssetsScrapHeadReqDTO, stockAssetsScrapHead, user);

            if (CollectionUtils.isNotEmpty(stockAssetsScrapHeadReqDTO.getAttachIds())) {
                List<SysAttachReqDTO> sysAttachReqDTOList = new ArrayList<>(stockAssetsScrapHeadReqDTO.getAttachIds().size());
                for(Long attachId:stockAssetsScrapHeadReqDTO.getAttachIds()){
                    SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO();
                    sysAttachReqDTO.setRelId(stockAssetsScrapHead.getScrapNo());
                    sysAttachReqDTO.setAttachId(attachId);
                    sysAttachReqDTO.setPageModule(FileConstant.PAGE_MODE_CARD_ASSET_SCRAP);
                    sysAttachReqDTOList.add(sysAttachReqDTO);
                }
                ResponseData responseData = fileServiceApi.updateSysAttachList(sysAttachReqDTOList);
                if (!ResponseCode.SUCCESS_CODE.equals(responseData.getCode())) {
                    //调用接口失败，回滚事物
                    log.error("调用ambase绑定附件信息失败：" + responseData.getMessage());
                    throw new ServiceUncheckedException("报废单绑定附件信息失败");
                }
            }
        } else {
            //增加判断存入临时表中资产信息
            InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO = new InventoryAssetImportSearchReqDTO();
            inventoryAssetImportSearchReqDTO.setBindBatchCode(stockAssetsScrapHeadReqDTO.getBindBatchCode().trim());
            List<StockInventoryAssetImport> stockInventoryAssetImports = stockInventoryAssetImportService.selectInventoryAssetImportBySelective(inventoryAssetImportSearchReqDTO);
            //过滤出说明信息不为空的数据信息
            StringBuilder errorAssetsCodeListString = new StringBuilder();
            List<String> assetsCodeList = new ArrayList<>();
            for (StockInventoryAssetImport stockInventoryAssetImport : stockInventoryAssetImports) {
                if(StringUtils.isNotBlank(stockInventoryAssetImport.getAttr2())){
                    errorAssetsCodeListString.append(stockInventoryAssetImport.getAssetCode()).append(StringConstant.SEMI_COLON_HALF);
                }
                assetsCodeList.add(stockInventoryAssetImport.getAssetCode());
            }

            if(errorAssetsCodeListString.length() > CommonConstant.NUMBER_ZERO){
                return ResponseData.createFailResult("废申请单不允许保存/提交，请检查”说明“信息，存在问题的资产编码为：" + errorAssetsCodeListString);
            }
            // 查询所有的资产判断资产状态是否合法
            stockAssetsScrapHead = stockAssetsScrapHeadMapper.selectByPrimaryKey(headId);
            if (stockAssetsScrapHead == null) {
                return ResponseData.createFailResult("没有查询到相应的报废单据信息");
            }
            if (!AssetsScrapEnum.scrapStatus.SAVE.getValue().equals(stockAssetsScrapHead.getScrapStatus())) {
                return ResponseData.createFailResult("单据当前状态不是待审批");
            }

            Long count = stockInventoryAssetImportService.selectInventoryAssetImportCount(stockAssetsScrapHeadReqDTO.getBindBatchCode());
            if (count <= CommonConstant.NUMBER_ZERO) {
                throw new ServiceUncheckedException("报废资产数量不能为0");
            }
            List<StockAssets> stockAssets = stockAssetsService.selectAssets(null, assetsCodeList);
            StringBuilder errorMessage = new StringBuilder();
            for (StockAssets stockAsset : stockAssets) {
                if(AssetsEnum.Conditions.DISCAERD.getValue().equals(stockAsset.getConditions())){
                    errorMessage.append("资产编码：" + stockAsset.getAssetsCode() + "已报废，不能提交报废申请");
                }
                if(AssetsEnum.Conditions.TRANSFER.getValue().equals(stockAsset.getConditions())){
                    errorMessage.append("资产编码：" + stockAsset.getAssetsCode() + "在转移审批中，不能提交报废申请");
                }
                if(!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAsset.getApproveStatus())){
                    errorMessage.append("资产编码：" + stockAsset.getAssetsCode() + "在报废/IT工单审批中或者已经报废，不能提交报废申请");
                }
            }
            if (errorMessage.length() > CommonConstant.NUMBER_ZERO){
                return ResponseData.createFailResult(errorMessage.toString());
            }
            stockAssetsScrapHead.setPlanScrapCount(count.intValue());
            stockAssetsScrapHead.setRealScrapCount(count.intValue());
            //查询资产总净值
            BigDecimal netValue = stockInventoryAssetImportService.selectAssetImportNetValue(stockAssetsScrapHeadReqDTO.getBindBatchCode());
            stockAssetsScrapHead.setPlanScrapValue(netValue);
            stockAssetsScrapHead.setRealScrapValue(netValue);
            stockAssetsScrapHead.setScrapStatus(AssetsScrapEnum.scrapStatus.SUBMIT.getValue());
            stockAssetsScrapHead.setRemark(stockAssetsScrapHeadReqDTO.getRemark());

            stockAssetsScrapHeadMapper.updateByPrimaryKey(stockAssetsScrapHead);
        }

        List<StockAssets> updateAssetsApproveStatus = new ArrayList<>();
        //2.生成报废单行数据
        saveScrapLines(stockAssetsScrapHead.getBindBatchCode(), stockAssetsScrapHead, user,updateAssetsApproveStatus);
        //更新资产为审批中
        if(CollectionUtils.isNotEmpty(updateAssetsApproveStatus)){
            int toIndex = CommonConstant.MAX_INSERT_COUNT;

            Integer ret = CommonConstant.NUMBER_ZERO;
            for (int i = CommonConstant.NUMBER_ZERO; i < updateAssetsApproveStatus.size (); i += CommonConstant.MAX_INSERT_COUNT) {
                if (i + CommonConstant.MAX_INSERT_COUNT > updateAssetsApproveStatus.size ()) {
                    toIndex = updateAssetsApproveStatus.size () - i;
                }
                List<StockAssets> newAssetsList = updateAssetsApproveStatus.subList (i, i + toIndex);
                Integer subRet = assetsMapper.updateMultipleSelective (newAssetsList);
                ret = ret + subRet;
            }
        }

        //4.发起审批流程
        List<Map<String, Object>> dateItemListExtend = new ArrayList<>(2);
        Map<String, Object> map1 = new HashMap<>(CommonConstant.NUMBER_ONE);
        map1.put("scrapValue", stockAssetsScrapHead.getRealScrapValue());
        dateItemListExtend.add(map1);
        Map<String, Object> map2 = new HashMap<>(CommonConstant.NUMBER_ONE);
        map2.put("billingUser", stockAssetsScrapHead.getBillingUser());
        dateItemListExtend.add(map2);
        WflInfo wflInfo = WorkFlowUtil.createWlfInfo(FlowCodeEnum.STOCK_ASSETS_SCRAP, stockAssetsScrapHead.getScrapNo(), dateItemListExtend);


        //异步发起工作流发
        wflService.beginAct(wflInfo);

        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData queryScrap(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException {
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        List<StockAssetsScrapHeadRespDTO> stockAssetsScrapHeadRespDTOS = new ArrayList<>();
        //1.获取总数
        StockAssetsScrapHeadExample example = getStockAssetsScrapHeadExample(stockAssetsScrapHeadReqDTO);
        long count = stockAssetsScrapHeadMapper.countByExample(example);
        pageRespDTO.setCount(count);
        if (count <= CommonConstant.NUMBER_ZERO) {
            pageRespDTO.setData(stockAssetsScrapHeadRespDTOS);
            return pageRespDTO;
        }

        //2.获取行数据
        stockAssetsScrapHeadReqDTO.initPageParam();
        example.setOffset(stockAssetsScrapHeadReqDTO.getStartNum());
        example.setLimit(stockAssetsScrapHeadReqDTO.getPageSize());
        example.setOrderByClause("head_id desc");
        List<StockAssetsScrapHead> stockAssetsScrapHeadList = stockAssetsScrapHeadMapper.selectByExample(example);
        List<StockAssetsScrapHeadRespDTO> stockAssetsScrapHeadReqDTOs = new ArrayList<>(stockAssetsScrapHeadList.size());
        //3.补全返回字段
        settingHeadRespField(stockAssetsScrapHeadList, stockAssetsScrapHeadReqDTOs);
        pageRespDTO.setData(stockAssetsScrapHeadReqDTOs);

        return pageRespDTO;
    }

    @Override
    public ResponseData queryScrapDetail(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException {
        //1.校验参数
        if (null == stockAssetsScrapHeadReqDTO.getScrapNo()) {
            return ResponseData.createFailResult("参数不能为空");
        }
        //2.查询报废单头信息
        StockAssetsScrapHeadExample example = getStockAssetsScrapHeadExample(stockAssetsScrapHeadReqDTO);
        List<StockAssetsScrapHead> stockAssetsScrapHeadList = stockAssetsScrapHeadMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(stockAssetsScrapHeadList)) {
            return ResponseData.createFailResult("没有查到报废单信息");
        }
        List<StockAssetsScrapHeadRespDTO> stockAssetsScrapHeadReqDTOs = new ArrayList<>(CommonConstant.NUMBER_ONE);
        settingHeadRespField(stockAssetsScrapHeadList, stockAssetsScrapHeadReqDTOs);
        StockAssetsScrapHeadRespDTO stockAssetsScrapHeadRespDTO = stockAssetsScrapHeadReqDTOs.get(CommonConstant.NUMBER_ZERO);
        StockAssetsScrapHead stockAssetsScrapHead = stockAssetsScrapHeadList.get(CommonConstant.NUMBER_ZERO);

        //3.查询附件url
        QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO();
        queryFileReqDTO.setSystemModule(systemModule);
        queryFileReqDTO.setAttachModule(attachModule);
        queryFileReqDTO.setRelId(stockAssetsScrapHead.getScrapNo());
        ResponseData<List<PictureMetaRespDTO>> responseData = fileServiceApi.getFileUrlListByRelId(queryFileReqDTO);
        List<PictureMetaRespDTO> pictureMetaRespDTOList = responseData.getData();
        if (CollectionUtils.isNotEmpty(pictureMetaRespDTOList)) {
            stockAssetsScrapHeadRespDTO.setPictureMetaRespDTOList(pictureMetaRespDTOList);
//            stockAssetsScrapHeadRespDTO.setAttachDownLoadUrl(pictureMetaRespDTOList.get(CommonConstant.NUMBER_ZERO).getDownLoadPicUrl());
//            stockAssetsScrapHeadRespDTO.setAttachName(pictureMetaRespDTOList.get(CommonConstant.NUMBER_ZERO).getShowPicName());
        }

        //4.查询报废单行信息 保存和作废状态查询临时表；其它状态查询行表
        settingLineDetailData(stockAssetsScrapHeadRespDTO, stockAssetsScrapHead, stockAssetsScrapHeadReqDTO);

        return ResponseData.createSuccessResult(stockAssetsScrapHeadRespDTO);
    }


    @Override
    public ResponseData cancelScrap(Long headId) {
        //1.校验数据
        if (null == headId) {
            return ResponseData.createFailResult("参数不能为空");
        }
        StockAssetsScrapHead stockAssetsScrapHead = stockAssetsScrapHeadMapper.selectByPrimaryKey(headId);
        if (null == stockAssetsScrapHead) {
            return ResponseData.createFailResult("没有查到报废单信息");
        }
        if (!AssetsScrapEnum.scrapStatus.SAVE.getValue().equals(stockAssetsScrapHead.getScrapStatus())) {
            return ResponseData.createFailResult("当前状态不允许作废");
        }
        //2.更新单据状态
        stockAssetsScrapHead.setScrapStatus(AssetsScrapEnum.scrapStatus.CANCEL.getValue());
        stockAssetsScrapHeadMapper.updateByPrimaryKey(stockAssetsScrapHead);

        return ResponseData.createSuccessResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleApproveResult(String bizId, Integer status) {
        //1.查询报废单单据
        StockAssetsScrapHeadExample example = new StockAssetsScrapHeadExample();
        StockAssetsScrapHeadExample.Criteria criteria = example.createCriteria();
        criteria.andScrapNoEqualTo(bizId);
        List<StockAssetsScrapHead> stockAssetsScrapHeadList = stockAssetsScrapHeadMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(stockAssetsScrapHeadList)) {
            throw new ServiceUncheckedException("没有查到对应单据信息");
        }
        StockAssetsScrapHead stockAssetsScrapHead = stockAssetsScrapHeadList.get(CommonConstant.NUMBER_ZERO);
        if (!AssetsScrapEnum.scrapStatus.SUBMIT.getValue().equals(stockAssetsScrapHead.getScrapStatus())) {
            throw new ServiceUncheckedException("单据当前状态不是审批中");
        }

        //2.更新单据状态
        stockAssetsScrapHead.setScrapStatus(status);
        stockAssetsScrapHead.setUpdatedAt(new Date());
        stockAssetsScrapHeadMapper.updateByPrimaryKey(stockAssetsScrapHead);

        //3.如果status=审批通过，更新资产使用状态=已报废
        List<String> assetsCodeList = assetsScrapLineMapper.queryAssetsCodesByHeadId(stockAssetsScrapHead.getHeadId());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, assetsCodeList);


        String billingUser = stockAssetsScrapHead.getBillingUser();
        if (AssetsScrapEnum.scrapStatus.PASS.getValue().equals(status)) {
            //生成资产出置出库单
            generateStockDelivery(stockAssetsScrapHead);
            stockAssetsList.forEach(dto -> {
                dto.setConditions(AssetsEnum.Conditions.DISCAERD.getValue());
                dto.setApproveStatus(AssetsEnum.ApproveStatus.APPROVE.getValue());
                dto.setUpdatedBy(billingUser);
            });
            stockAssetsService.updateMultipleSelective(stockAssetsList);
        // 如果是审批驳回，只修改资产状态，不插入资产变更记录
        }else {
            stockAssetsList.forEach(dto -> {
                dto.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
                dto.setUpdatedBy(billingUser);
            });
            stockAssetsService.batchUpdate(stockAssetsList);
        }

    }

    /**
     * 生成资产出置出库单
     */
    private void generateStockDelivery(StockAssetsScrapHead stockAssetsScrapHead) {
        JwtUser user = SecurityUtil.getJwtUser();
        List<StockAssetsScrapLine> stockAssetsScrapLineList = assetsScrapLineMapper.queryLinesByHeadId(stockAssetsScrapHead.getHeadId());
        // 查询资产信息
        List<String> assetsCodeList = stockAssetsScrapLineList.stream().map(StockAssetsScrapLine :: getAssetsCode).collect(Collectors.toList());
        Map<String,StockAssets> assetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        List<String> warehouseCodeList = new ArrayList<>();
        Map<String, List<StockAssetsScrapLine>> stringListMap = new HashMap<>();
        for (StockAssetsScrapLine stockAssetsScrapLine : stockAssetsScrapLineList) {
            stockAssetsScrapLine.setScrapFlag(AssetsScrapEnum.scrapFlag.YES_SCRAP.getValue());
            stockAssetsScrapLine.setUpdatedAt(new Date());
            stockAssetsScrapLine.setUpdatedBy(user.getEmployeeCode());
            // 修改报废状态
            StockAssets stockAssets = assetsMap.get(stockAssetsScrapLine.getAssetsCode());
            if(stockAssets != null){
                stockAssetsScrapLine.setCostDept(stockAssets.getCostDept());
            }
            if (!warehouseCodeList.contains(stockAssetsScrapLine.getWarehouseCode())) {
                warehouseCodeList.add(stockAssetsScrapLine.getWarehouseCode());
            }
            if (!stringListMap.containsKey(stockAssetsScrapLine.getWarehouseCode())) {
                stringListMap.put(stockAssetsScrapLine.getWarehouseCode(), new ArrayList());
            }
            stringListMap.get(stockAssetsScrapLine.getWarehouseCode()).add(stockAssetsScrapLine);
        }
        // 修改行单状态
        assetsScrapLineMapper.batchUpdateScrapLines(stockAssetsScrapLineList);
        if (CollectionUtils.isNotEmpty(warehouseCodeList)) {
            for (String warehouseCode : warehouseCodeList) {
                List<StockAssetsScrapLine> scrapLineList = stringListMap.get(warehouseCode);
                StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead();
                List<StockDeliveryPlanLine> stockDeliveryPlanLineList = new ArrayList<>();
                //资产处置出库单赋值
                prepareSaveDisposeDbBeanDTO(warehouseCode, scrapLineList, stockDeliveryPlanHead, stockDeliveryPlanLineList, stockAssetsScrapHead.getCreatedBy());
                stockDeliveryPlanHeadService.insertStockDeliveryPlanHead(stockDeliveryPlanHead);
                if (!CollectionUtils.isEmpty(stockDeliveryPlanLineList)) {
                    stockDeliveryPlanLineList.forEach(stockDeliveryPlanLine -> stockDeliveryPlanLine.setDeliveryPlanHeadId(stockDeliveryPlanHead.getDeliveryPlanHeadId()));
                    stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLine(stockDeliveryPlanLineList);
                }
            }
        }
    }

    /**
     * 赋值处置计划出库单参数
     *
     * @param warehouseCode
     * @param scrapLineList
     * @param stockDeliveryPlanHead
     * @param stockDeliveryPlanLineList
     * @param employeeCode
     */
    private void prepareSaveDisposeDbBeanDTO(String warehouseCode,List<StockAssetsScrapLine> scrapLineList, StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> stockDeliveryPlanLineList, String employeeCode) {
        stockDeliveryPlanHead.setDeliveryPlanNo (stockDeliveryPlanHeadService.getDeliveryPlanNo (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ()));
        stockDeliveryPlanHead.setOutStockType (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ());
        stockDeliveryPlanHead.setOutWarehouseCode (warehouseCode);
        stockDeliveryPlanHead.setReasonCode (DeliveryPlanHeadEnum.Reason.DEFAULT.getCode ());
        stockDeliveryPlanHead.setBillingTime (new Date ());
        stockDeliveryPlanHead.setBillingUser (employeeCode);
        stockDeliveryPlanHead.setRemark ("");
        stockDeliveryPlanHead.setPlanOutTime (new Date ());
        stockDeliveryPlanHead.setStatus (DeliveryPlanHeadEnum.Status.WAIT_OUT.getCode ());

        stockDeliveryPlanHead.setUpdatedBy (employeeCode);
        stockDeliveryPlanHead.setCreatedBy (employeeCode);

        stockDeliveryPlanHead.setUseAddress ("");

        stockDeliveryPlanHead.setBusinessType (DeliveryPlanHeadEnum.BusinessType.SELL.getCode ());

        List<String> assetCodeList = scrapLineList.stream ().map (a -> a.getAssetsCode ()).collect (Collectors.toList ());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets (null, assetCodeList);
        Map<String, StockAssets> stockAssetsMap = new HashMap<> (CommonConstant.DEFAULT_MAP_SIZE);
        if (!CollectionUtils.isEmpty (stockAssetsList)) {
            stockAssetsList.forEach (stockAssets -> {
                stockAssetsMap.put (stockAssets.getAssetsCode (), stockAssets);
            });
        }

        for (StockAssetsScrapLine stockAssetsScrapLine : scrapLineList) {
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine ();
            if (MapUtils.isNotEmpty (stockAssetsMap)) {
                stockDeliveryPlanLine.setSuppliesCode (stockAssetsMap.get (stockAssetsScrapLine.getAssetsCode ()).getSuppliesCode ());
            }
            stockDeliveryPlanLine.setAssetsCode (stockAssetsScrapLine.getAssetsCode ());
            stockDeliveryPlanLine.setPlanOutTime (stockDeliveryPlanHead.getPlanOutTime ());
            //默认1个
            stockDeliveryPlanLine.setNumber (1);
            stockDeliveryPlanLine.setRealNumber (0);
            stockDeliveryPlanLine.setStatus (DeliveryPlanLineEnum.Status.WAIT_OUT.getCode ());
            stockDeliveryPlanLine.setCreatedBy (employeeCode);
            stockDeliveryPlanLine.setUpdatedBy (employeeCode);
            stockDeliveryPlanLineList.add (stockDeliveryPlanLine);
        }
    }



    /**
     * 保存报废单据参数校验
     *
     * @param stockAssetsScrapHeadReqDTO
     */
    private String checkCreateScrapParam(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) {
        if (StringUtils.isBlank(stockAssetsScrapHeadReqDTO.getBindBatchCode())) {
            return "批次号不能为空";
        }

        stockAssetsScrapHeadReqDTO.setBindBatchCode(stockAssetsScrapHeadReqDTO.getBindBatchCode().trim());

        Long count = stockInventoryAssetImportService.selectInventoryAssetImportCount(stockAssetsScrapHeadReqDTO.getBindBatchCode());
        if (count <= 0) {
            return "请添加要报废的资产";
        }

        if (StringUtils.isBlank(stockAssetsScrapHeadReqDTO.getApplyDept())) {
            return "申请部门不能为空";
        }
        if (StringUtils.isBlank(stockAssetsScrapHeadReqDTO.getBillingUser())) {
            return "制单人不能为空";
        }
        if (StringUtils.isBlank(stockAssetsScrapHeadReqDTO.getBillingTime())) {
            return "制单日期不能为空";
        }
        if (StringUtils.isBlank(stockAssetsScrapHeadReqDTO.getRemark())) {
            return "备注不能为空";
        }

        //增加判断存入临时表中资产信息
        InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO = new InventoryAssetImportSearchReqDTO();
        inventoryAssetImportSearchReqDTO.setBindBatchCode(stockAssetsScrapHeadReqDTO.getBindBatchCode().trim());
        List<StockInventoryAssetImport> stockInventoryAssetImports = stockInventoryAssetImportService.selectInventoryAssetImportBySelective(inventoryAssetImportSearchReqDTO);
        //过滤出说明信息不为空的数据信息
        List<String> assetsCodeList = new ArrayList<>();
        StringBuilder errorAssetsCodeListString = new StringBuilder();
        for (StockInventoryAssetImport stockInventoryAssetImport : stockInventoryAssetImports) {
            if(StringUtils.isNotBlank(stockInventoryAssetImport.getAttr2())){
                errorAssetsCodeListString.append(stockInventoryAssetImport.getAssetCode()).append(StringConstant.SEMI_COLON_HALF);
            }
            assetsCodeList.add(stockInventoryAssetImport.getAssetCode());
        }
        if(errorAssetsCodeListString.length() > CommonConstant.NUMBER_ZERO){
            return "废申请单不允许保存/提交，请检查”说明“信息，存在问题的资产编码为：" + errorAssetsCodeListString;
        }
        // 查询所有的资产判断资产状态是否合法
        List<StockAssets> stockAssets = stockAssetsService.selectAssets(null, assetsCodeList);
        for (StockAssets stockAsset : stockAssets) {
            if(AssetsEnum.Conditions.DISCAERD.getValue().equals(stockAsset.getConditions())){
                return "资产编码：" + stockAsset.getAssetsCode() + "已报废，不能提交报废申请";
            }
            if(AssetsEnum.Conditions.TRANSFER.getValue().equals(stockAsset.getConditions())){
                return "资产编码：" + stockAsset.getAssetsCode() + "在转移审批中，不能提交报废申请";
            }
            if(!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAsset.getApproveStatus())){
                return "资产编码：" + stockAsset.getAssetsCode() + "在报废/IT工单审批中或者已经报废，不能提交报废申请";
            }
        }
        return null;
    }


    /**
     * 生成报废单头表信息
     *
     * @param stockAssetsScrapHeadReqDTO
     */
    private void saveScrapHead(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO, StockAssetsScrapHead stockAssetsScrapHead, JwtUser user) throws ParseException {
        stockAssetsScrapHeadReqDTO.setBindBatchCode(stockAssetsScrapHeadReqDTO.getBindBatchCode().trim());
        BeanUtils.copyProperties(stockAssetsScrapHeadReqDTO, stockAssetsScrapHead);
        //获取单据号
        stockAssetsScrapHead.setScrapNo(getScrapNo());
        //获取总数量
        Long count = stockInventoryAssetImportService.selectInventoryAssetImportCount(stockAssetsScrapHeadReqDTO.getBindBatchCode());
        if (count <= CommonConstant.NUMBER_ZERO) {
            throw new ServiceUncheckedException("报废资产数量不能为0");
        }
        stockAssetsScrapHead.setRealScrapCount(count.intValue());
        //查询资产总净值
        BigDecimal netValue = stockInventoryAssetImportService.selectAssetImportNetValue(stockAssetsScrapHeadReqDTO.getBindBatchCode());
        stockAssetsScrapHead.setBindBatchCode(stockAssetsScrapHeadReqDTO.getBindBatchCode());
        stockAssetsScrapHead.setRealScrapValue(netValue);
        stockAssetsScrapHead.setPlanScrapCount(stockAssetsScrapHead.getRealScrapCount());
        stockAssetsScrapHead.setPlanScrapValue(stockAssetsScrapHead.getRealScrapValue());
        stockAssetsScrapHead.setBillingTime(StringUtils.isNotBlank(stockAssetsScrapHeadReqDTO.getBillingTime()) ? DateUtils.dateParse(stockAssetsScrapHeadReqDTO.getBillingTime(), null) : new Date());
        if (stockAssetsScrapHead.getScrapStatus() == null) {
            stockAssetsScrapHead.setScrapStatus(AssetsScrapEnum.scrapStatus.SAVE.getValue());
        }
        stockAssetsScrapHead.setExistAttachFlag(CollectionUtils.isEmpty(stockAssetsScrapHeadReqDTO.getAttachIds()) ? AssetsScrapEnum.ExistAttach.NO.getValue() : AssetsScrapEnum.ExistAttach.YES.getValue());
        Date date = new Date();
        stockAssetsScrapHead.setCreatedAt(date);
        stockAssetsScrapHead.setCreatedBy(user.getEmployeeCode());
        stockAssetsScrapHead.setUpdatedAt(date);
        stockAssetsScrapHead.setUpdatedBy(user.getEmployeeCode());
        stockAssetsScrapHeadMapper.insert(stockAssetsScrapHead);
    }

    /**
     * 组织报废单行数据
     *
     * @param bingBatchCode
     * @param stockAssetsScrapHead
     * @param user
     */
    private void saveScrapLines(String bingBatchCode, StockAssetsScrapHead stockAssetsScrapHead, JwtUser user,List<StockAssets> updateAssetsApproveStatus) {
        List<StockAssetsScrapLineRespDTO> stockAssetsScrapLineRespDTOList = stockInventoryAssetImportService.getScrapLinesByBindCode(bingBatchCode);
        if (CollectionUtils.isEmpty(stockAssetsScrapLineRespDTOList)) {
            throw new ServiceUncheckedException("报废资产不能为空");
        }
        List<StockAssetsScrapLine> stockAssetsScrapLineList = new ArrayList<>(stockAssetsScrapLineRespDTOList.size());

        Date date = new Date();
        stockAssetsScrapLineRespDTOList.forEach(dto -> {
            StockAssetsScrapLine stockAssetsScrapLine = new StockAssetsScrapLine();
            BeanUtils.copyProperties(dto, stockAssetsScrapLine);
            //处理剩余使用年限
            if (StringUtils.isNotBlank(dto.getAssetsSurplusLifeStr())) {
                stockAssetsScrapLine.setAssetsSurplusLife(changeSurplusLife(dto.getAssetsSurplusLifeStr()));
            }
            stockAssetsScrapLine.setHeadId(stockAssetsScrapHead.getHeadId());
            stockAssetsScrapLine.setScrapNo(stockAssetsScrapHead.getScrapNo());
            stockAssetsScrapLine.setCreatedAt(date);
            stockAssetsScrapLine.setCreatedBy(user.getEmployeeCode());
            stockAssetsScrapLine.setUpdatedAt(date);
            stockAssetsScrapLine.setUpdatedBy(user.getEmployeeCode());
            stockAssetsScrapLineList.add(stockAssetsScrapLine);

            //需要更新资产为审批中 不能去做任何处理
            StockAssets stockAssets = new StockAssets();
            stockAssets.setAssetsCode(dto.getAssetsCode());
            stockAssets.setApproveStatus(AssetsEnum.ApproveStatus.SUBMIT.getValue());
            updateAssetsApproveStatus.add(stockAssets);
        });

        //批量保存数据
        List<List<StockAssetsScrapLine>> lineAllList = ListUtil.splitList(stockAssetsScrapLineList, CommonConstant.MAX_INSERT_COUNT);
        for (List<StockAssetsScrapLine> lineList : lineAllList) {
            assetsScrapLineMapper.batchInsertScrapLines(lineList);
        }

        //删除临时表数据
        stockInventoryAssetImportService.deleteAssetsByBatchCode(bingBatchCode);
    }


    /**
     * 参数校验
     *
     * @param inventoryAssetImportBatchReqDTO
     * @return
     */
    private String checkManualAddParam(InventoryAssetImportBatchReqDTO inventoryAssetImportBatchReqDTO) {
        //1.批次号不能为空
        if (StringUtils.isBlank(inventoryAssetImportBatchReqDTO.getBindBatchCode())) {
            return "批次号不能为空";
        }
        //2.提交的数据不能为空
        List<InventoryAssetImportReqDTO> inventoryAssetImportBatchReqDTOList = inventoryAssetImportBatchReqDTO.getInventoryAssetImportBatchReqDTOList();
        if (CollectionUtils.isEmpty(inventoryAssetImportBatchReqDTOList)) {
            return "提交的资产不能为空";
        }

        //参数格式化
        inventoryAssetImportBatchReqDTO.setBindBatchCode(inventoryAssetImportBatchReqDTO.getBindBatchCode().trim());
        inventoryAssetImportBatchReqDTOList.forEach(dto -> dto.setAssetCode(dto.getAssetsCode()));

        //3.过滤调状态=已报废的资产或者是报废/IT工单审批中的资产或转移审批中的资产
        List<String> assetsCodeList = inventoryAssetImportBatchReqDTOList.stream().map(dto -> dto.getAssetCode()).distinct().collect(Collectors.toList());
        filterAlreadyScrapAssets(assetsCodeList);
        if (CollectionUtils.isEmpty(assetsCodeList)) {
            inventoryAssetImportBatchReqDTO.setInventoryAssetImportBatchReqDTOList(null);
            return null;
        }

        //4.过滤掉已经保存的资产
        filterAlreadySaveAssets(assetsCodeList, inventoryAssetImportBatchReqDTO.getBindBatchCode());
        if (CollectionUtils.isEmpty(assetsCodeList)) {
            inventoryAssetImportBatchReqDTO.setInventoryAssetImportBatchReqDTOList(null);
            return null;
        }

        List<InventoryAssetImportReqDTO> newInventoryAssetImportBatchReqDTOList = inventoryAssetImportBatchReqDTOList.stream().filter(dto -> assetsCodeList.contains(dto.getAssetCode())).collect(Collectors.toList());
        inventoryAssetImportBatchReqDTO.setInventoryAssetImportBatchReqDTOList(newInventoryAssetImportBatchReqDTOList);

        return null;
    }

    /**
     * 过滤掉已经报废的资产或资产卡片不存在的资产
     *
     * @param assetsCodeList
     */
    private void filterAlreadyScrapAssets(List<String> assetsCodeList) {
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, assetsCodeList);
        List<String> noContainDiscardAssets = stockAssetsList.stream().filter(dto -> !AssetsEnum.Conditions.DISCAERD.getValue().equals(dto.getConditions()) && (AssetsEnum.statusType.IDLE.getValue().equals(dto.getStatus()) || AssetsEnum.statusType.USED.getValue().equals(dto.getStatus()))
                && AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(dto.getApproveStatus()) && !AssetsEnum.Conditions.TRANSFER.getValue().equals(dto.getConditions()))
                .map(dto -> dto.getAssetsCode()).collect(Collectors.toList());

        assetsCodeList.clear();
        if (CollectionUtils.isNotEmpty(noContainDiscardAssets)) {
            assetsCodeList.addAll(noContainDiscardAssets);
        }
    }

    /**
     * 过滤已经添加临时表的资产
     *
     * @param assetsCodeList
     * @param bindBatchCode
     */
    private void filterAlreadySaveAssets(List<String> assetsCodeList, String bindBatchCode) {
        InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO = new InventoryAssetImportSearchReqDTO();
        inventoryAssetImportSearchReqDTO.setBindBatchCode(bindBatchCode);
        inventoryAssetImportSearchReqDTO.setAssetsCodeList(assetsCodeList);
        List<StockInventoryAssetImport> stockInventoryAssetImports = stockInventoryAssetImportService.selectInventoryAssetImportBySelective(inventoryAssetImportSearchReqDTO);
        if (CollectionUtils.isNotEmpty(stockInventoryAssetImports)) {
            List<String> alreadySaveAssets = stockInventoryAssetImports.stream().map(dto -> dto.getAssetCode()).collect(Collectors.toList());
            assetsCodeList.removeAll(alreadySaveAssets);
        }
    }

    /**
     * 获取人员，部门信息等字段值
     *
     * @param stockInventoryAssetImportList
     * @param stockAssetsScrapLineRespDTOList
     */
    private void settingExtendField(List<StockInventoryAssetImport> stockInventoryAssetImportList, List<StockAssetsScrapLineRespDTO> stockAssetsScrapLineRespDTOList) {
        if (CollectionUtils.isEmpty(stockInventoryAssetImportList)) {
            return;
        }
        Map<String, AssetsCostRespDTO> costMap = new HashMap<>(stockInventoryAssetImportList.size());
        Map<String, StockAssets> assetsMap = new HashMap<>(stockInventoryAssetImportList.size());
        Map<String, SysUser> userMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, WarehouseRespDTO> warehouseRespDTOMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, SysDept> deptMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, SysDict> dictMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<Integer, String> scrapReasonMap = AssetsEnum.scrapReasonMap;
        Map<Integer, String> assetsStatusMap = AssetsEnum.assetsStatusTypeMap;
        SysUser defaultUser = new SysUser();
        SysDept defaultDept = new SysDept();
        SysDict defaultDict = new SysDict();
        //1.获取资产原值、资产净值、规定使用年限、已使用年限
        List<String> assetsCodes = stockInventoryAssetImportList.stream().map(dto -> dto.getAssetCode()).collect(Collectors.toList());
        List<AssetsCostRespDTO> assetsCostRespDTOList = ambaseCommonService.selectCostByAssetsCode(assetsCodes);
        if (CollectionUtils.isNotEmpty(assetsCostRespDTOList)) {
            costMap = assetsCostRespDTOList.stream().collect(Collectors.toMap(dto -> dto.getAssetsCode(), asset -> asset, (k1, k2) -> k1));
        }
        //2.获取资产名称、序列号、状态、资产管理员、资产持有人、所在仓库
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, assetsCodes);
        assetsMap = stockAssetsList.stream().collect(Collectors.toMap(dto -> dto.getAssetsCode(), asset -> asset, (k1, k2) -> k1));
        //3.获取状态名称、人员名称、费用部门、业务线
        Set<String> empIds = new HashSet<>();
        Set<String> warehouseCodes = new HashSet<>();
        Set<String> costDepts = new HashSet<>();
        Set<String> businessCodes = new HashSet<>();
        stockAssetsList.forEach(dto -> {
            empIds.add(dto.getAssetsKeeper());
            empIds.add(dto.getHolder());
            warehouseCodes.add(dto.getWarehouseCode());
            costDepts.add(dto.getCostDept());
        });
        if (CollectionUtils.isNotEmpty(empIds)) {
            List<String> empIdList = new ArrayList<>(empIds);
            userMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        }
        if (CollectionUtils.isNotEmpty(costDepts)) {
            List<String> costDeptList = new ArrayList<>(costDepts);
            List<SysDept> sysDepts = ambaseCommonService.selectDeptByIds(costDeptList);
            if (CollectionUtils.isNotEmpty(sysDepts)) {
                deptMap = sysDepts.stream().collect(Collectors.toMap(dto->dto.getDeptId(),dto->dto,(k1,k2)->k1));
                sysDepts.forEach(dto->businessCodes.add(dto.getCstBusinessLine()));
            }
        }
        if (CollectionUtils.isNotEmpty(businessCodes)) {
            List<String> businessList = new ArrayList<>(businessCodes);
            dictMap = ambaseCommonService.selectDictListByValue("CST_BUSINESS_LINE",businessList);
        }
        //4.获取仓库名称
        if (CollectionUtils.isNotEmpty(warehouseCodes)) {
            List<String> warehouseCodeList = new ArrayList<>(warehouseCodes);
            warehouseRespDTOMap = stockWarehouseService.selectWarehouseDetailMapByCode(warehouseCodeList);
        }
        for (StockInventoryAssetImport stockInventoryAssetImport : stockInventoryAssetImportList) {
            String assetsCode = stockInventoryAssetImport.getAssetCode();
            StockAssetsScrapLineRespDTO stockAssetsScrapLineRespDTO = new StockAssetsScrapLineRespDTO();
            stockAssetsScrapLineRespDTO.setAssetsCode(assetsCode);
            stockAssetsScrapLineRespDTO.setLineId(new Long(stockInventoryAssetImport.getInventoryAssetImpId()));
            //资产信息
            StockAssets stockAssets = assetsMap.get(assetsCode);
            if (null != stockAssets) {
                stockAssetsScrapLineRespDTO.setAssetsName(stockAssets.getAssetsName());
                stockAssetsScrapLineRespDTO.setSnCode(stockAssets.getSnCode());
                stockAssetsScrapLineRespDTO.setAssetsStatus(stockAssets.getStatus());
                stockAssetsScrapLineRespDTO.setAssetsStatusName(assetsStatusMap.get(stockAssets.getStatus()));
                stockAssetsScrapLineRespDTO.setCategory(stockAssets.getCategory());
                stockAssetsScrapLineRespDTO.setAssetsKeeper(stockAssets.getAssetsKeeper());
                stockAssetsScrapLineRespDTO.setAssetsKeeperName(userMap.getOrDefault(stockAssets.getAssetsKeeper(), defaultUser).getName());
                stockAssetsScrapLineRespDTO.setHolder(stockAssets.getHolder());
                stockAssetsScrapLineRespDTO.setExplain(stockInventoryAssetImport.getAttr2());
                if (StringUtils.isNotBlank(stockAssets.getHolder())) {
                    stockAssetsScrapLineRespDTO.setHolderName(userMap.getOrDefault(stockAssets.getHolder(), defaultUser).getName());
                }
                stockAssetsScrapLineRespDTO.setWarehouseCode(stockAssets.getWarehouseCode());
                //仓库信息
                if (StringUtils.isNotBlank(stockAssets.getWarehouseCode())) {
                    WarehouseRespDTO warehouseResp = warehouseRespDTOMap.get(stockAssets.getWarehouseCode());
                    stockAssetsScrapLineRespDTO.setWarehouseCodeName(warehouseResp.getName());
                }
                //报废原因
                stockAssetsScrapLineRespDTO.setScrapReason(stockInventoryAssetImport.getStatus());
                stockAssetsScrapLineRespDTO.setScrapReasonName(scrapReasonMap.get(stockInventoryAssetImport.getStatus()));
                //费用信息
                AssetsCostRespDTO assetsCostRespDTO = costMap.get(assetsCode);
                if (assetsCostRespDTO != null) {
                    stockAssetsScrapLineRespDTO.setAssetsCost(assetsCostRespDTO.getCost());
                    stockAssetsScrapLineRespDTO.setAssetsLastCost(assetsCostRespDTO.getLastCost());
                    Integer lifeYear = assetsCostRespDTO.getLifeYear();
                    if (lifeYear > CommonConstant.NUMBER_ZERO) {
                        //年转月
                        stockAssetsScrapLineRespDTO.setAssetsLife(lifeYear * CommonConstant.MONTH_NUM);
                    }
                    if (StringUtils.isNotBlank(assetsCostRespDTO.getSurplusYear())) {
                        //获取到的数据格式 2年1个月
                        stockAssetsScrapLineRespDTO.setAssetsSurplusLife(changeSurplusLife(assetsCostRespDTO.getSurplusYear()));
                    }
                }
                //费用部门
                stockAssetsScrapLineRespDTO.setCostDept(stockAssets.getCostDept());
                SysDept sysDept = deptMap.getOrDefault(stockAssets.getCostDept(), defaultDept);
                stockAssetsScrapLineRespDTO.setCostDeptName(sysDept.getDeptName());
                //业务线
                stockAssetsScrapLineRespDTO.setBusinessCode(sysDept.getCstBusinessLine());
                stockAssetsScrapLineRespDTO.setBusinessName(dictMap.getOrDefault(sysDept.getCstBusinessLine(), defaultDict).getName());
                stockAssetsScrapLineRespDTO.setExtractStatus(null != assetsMap.get(stockAssetsScrapLineRespDTO.getAssetsCode()) ? assetsMap.get(stockAssetsScrapLineRespDTO.getAssetsCode()).getExtractStatus() : MetaDataEnum.yesOrNo.NO.getValue());
                stockAssetsScrapLineRespDTO.setExtractStatusName(MetaDataEnum.yesOrNo.YES.getValue().equals(stockAssetsScrapLineRespDTO.getExtractStatus()) ? "是" : "否");
            } else {
                stockAssetsScrapLineRespDTO.setExplain(stockInventoryAssetImport.getAttr2());
                stockAssetsScrapLineRespDTO.setAssetsName(stockInventoryAssetImport.getAttr1());
            }
            stockAssetsScrapLineRespDTOList.add(stockAssetsScrapLineRespDTO);
        }
    }

    /**
     * 补全报废单头字段
     *
     * @param stockAssetsScrapHeadList
     * @param stockAssetsScrapHeadReqDTOs
     */
    private void settingHeadRespField(List<StockAssetsScrapHead> stockAssetsScrapHeadList, List<StockAssetsScrapHeadRespDTO> stockAssetsScrapHeadReqDTOs) throws ParseException {
        if (CollectionUtils.isEmpty(stockAssetsScrapHeadList)) {
            return;
        }
        Map<String, SysUser> userMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, SysDept> deptMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<Integer, String> scrapStatusMap = AssetsScrapEnum.scrapStatusMap;
        SysUser defaultUser = new SysUser();
        SysDept defaultDept = new SysDept();

        //3.获取状态名称、人员名称
        Set<String> empIds = new HashSet<>();
        Set<String> deptIds = new HashSet<>();
        stockAssetsScrapHeadList.forEach(dto -> {
            empIds.add(dto.getBillingUser());
            deptIds.add(dto.getApplyDept());
        });

        if (CollectionUtils.isNotEmpty(empIds)) {
            List<String> empIdList = new ArrayList<>(empIds);
            userMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        }
        if (CollectionUtils.isNotEmpty(empIds)) {
            List<String> deptIdList = new ArrayList<>(deptIds);
            deptMap = ambaseCommonService.selectSysDeptMapByIds(deptIdList);
        }

        for (StockAssetsScrapHead stockAssetsScrapHead : stockAssetsScrapHeadList) {
            StockAssetsScrapHeadRespDTO stockAssetsScrapHeadRespDTO = new StockAssetsScrapHeadRespDTO();
            BeanUtils.copyProperties(stockAssetsScrapHead, stockAssetsScrapHeadRespDTO);
            stockAssetsScrapHeadRespDTO.setBillingTime(DateUtils.dateFormat(stockAssetsScrapHead.getBillingTime(), null));
            stockAssetsScrapHeadRespDTO.setApplyDeptName(deptMap.getOrDefault(stockAssetsScrapHeadRespDTO.getApplyDept(), defaultDept).getDeptName());
            stockAssetsScrapHeadRespDTO.setBillingUserName(userMap.getOrDefault(stockAssetsScrapHeadRespDTO.getBillingUser(), defaultUser).getName());
            stockAssetsScrapHeadRespDTO.setScrapStatusName(scrapStatusMap.get(stockAssetsScrapHeadRespDTO.getScrapStatus()));
            stockAssetsScrapHeadReqDTOs.add(stockAssetsScrapHeadRespDTO);
        }
    }

    /**
     * 获取人员，部门信息等字段值
     *
     * @param stockAssetsScrapLineList
     * @param stockAssetsScrapLineRespDTOList
     */
    private void settingLineRespField(List<StockAssetsScrapLine> stockAssetsScrapLineList, List<StockAssetsScrapLineRespDTO> stockAssetsScrapLineRespDTOList) {
        if (CollectionUtils.isEmpty(stockAssetsScrapLineList)) {
            return;
        }
        Map<String, SysUser> userMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, WarehouseRespDTO> warehouseRespDTOMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, StockAssets> assetsMap = new HashMap<>(stockAssetsScrapLineList.size());
        Map<String, SysDept> deptMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<String, SysDict> dictMap = new HashMap<>(CommonConstant.NUMBER_100);
        Map<Integer, String> scrapReasonMap = AssetsEnum.scrapReasonMap;
        Map<Integer, String> assetsStatusTypeMap = AssetsEnum.assetsStatusTypeMap;
        SysUser defaultUser = new SysUser();
        SysDept defaultDept = new SysDept();
        SysDict defaultDict = new SysDict();
        WarehouseRespDTO defaultWarehouseRespDTO = new WarehouseRespDTO();

        //获取费用部门、业务线
        List<String> assetsCodes = stockAssetsScrapLineList.stream().map(dto -> dto.getAssetsCode()).collect(Collectors.toList());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets(null, assetsCodes);
        assetsMap = stockAssetsList.stream().collect(Collectors.toMap(dto -> dto.getAssetsCode(), asset -> asset, (k1, k2) -> k1));
        Set<String> costDepts = new HashSet<>();
        Set<String> businessCodes = new HashSet<>();
        stockAssetsList.forEach(dto -> {
            costDepts.add(dto.getCostDept());
        });
        if (CollectionUtils.isNotEmpty(costDepts)) {
            List<String> costDeptList = new ArrayList<>(costDepts);
            List<SysDept> sysDepts = ambaseCommonService.selectDeptByIds(costDeptList);
            if (CollectionUtils.isNotEmpty(sysDepts)) {
                deptMap = sysDepts.stream().collect(Collectors.toMap(dto->dto.getDeptId(),dto->dto,(k1,k2)->k1));
                sysDepts.forEach(dto->businessCodes.add(dto.getCstBusinessLine()));
            }
        }
        if (CollectionUtils.isNotEmpty(businessCodes)) {
            List<String> businessList = new ArrayList<>(businessCodes);
            dictMap = ambaseCommonService.selectDictListByValue("CST_BUSINESS_LINE",businessList);
        }
        //3.获取状态名称、人员名称
        Set<String> empIds = new HashSet<>();
        Set<String> warehouseCodes = new HashSet<>();
        stockAssetsScrapLineList.forEach(dto -> {
            empIds.add(dto.getAssetsKeeper());
            empIds.add(dto.getHolder());
            warehouseCodes.add(dto.getWarehouseCode());
        });
        if (CollectionUtils.isNotEmpty(empIds)) {
            List<String> empIdList = new ArrayList<>(empIds);
            userMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        }
        //4.获取仓库名称
        if (CollectionUtils.isNotEmpty(warehouseCodes)) {
            List<String> warehouseCodeList = new ArrayList<>(warehouseCodes);
            warehouseRespDTOMap = stockWarehouseService.selectWarehouseDetailMapByCode(warehouseCodeList);
        }
        for (StockAssetsScrapLine stockAssetsScrapLine : stockAssetsScrapLineList) {
            StockAssetsScrapLineRespDTO stockAssetsScrapLineRespDTO = new StockAssetsScrapLineRespDTO();
            BeanUtils.copyProperties(stockAssetsScrapLine, stockAssetsScrapLineRespDTO);

            stockAssetsScrapLineRespDTO.setWarehouseCodeName(warehouseRespDTOMap.getOrDefault(stockAssetsScrapLine.getWarehouseCode(), defaultWarehouseRespDTO).getName());

            stockAssetsScrapLineRespDTO.setScrapReasonName(scrapReasonMap.get(stockAssetsScrapLine.getScrapReason()));
            stockAssetsScrapLineRespDTO.setAssetsStatusName(assetsStatusTypeMap.get(stockAssetsScrapLine.getAssetsStatus()));

            stockAssetsScrapLineRespDTO.setHolderName(userMap.getOrDefault(stockAssetsScrapLine.getHolder(), defaultUser).getName());
            stockAssetsScrapLineRespDTO.setAssetsKeeperName(userMap.getOrDefault(stockAssetsScrapLine.getAssetsKeeper(), defaultUser).getName());
            //费用部门
            StockAssets stockAssets = assetsMap.get(stockAssetsScrapLine.getAssetsCode());
            stockAssetsScrapLineRespDTO.setCostDept(stockAssets.getCostDept());
            SysDept sysDept = deptMap.getOrDefault(stockAssets.getCostDept(),defaultDept);
            stockAssetsScrapLineRespDTO.setCostDeptName(sysDept.getDeptName());
            //业务线
            stockAssetsScrapLineRespDTO.setBusinessCode(sysDept.getCstBusinessLine());
            stockAssetsScrapLineRespDTO.setBusinessName(dictMap.getOrDefault(sysDept.getCstBusinessLine(),defaultDict).getName());

            stockAssetsScrapLineRespDTO.setExtractStatus(null != assetsMap.get(stockAssetsScrapLine.getAssetsCode()) ? assetsMap.get(stockAssetsScrapLine.getAssetsCode()).getExtractStatus() : MetaDataEnum.yesOrNo.NO.getValue());
            stockAssetsScrapLineRespDTO.setExtractStatusName(MetaDataEnum.yesOrNo.YES.getValue().equals(stockAssetsScrapLineRespDTO.getExtractStatus()) ? "是" : "否");
            stockAssetsScrapLineRespDTOList.add(stockAssetsScrapLineRespDTO);
        }
    }

    /**
     * 获取报废单行明细数据
     *
     * @param stockAssetsScrapHeadRespDTO
     * @param stockAssetsScrapHead
     */
    private void settingLineDetailData(StockAssetsScrapHeadRespDTO stockAssetsScrapHeadRespDTO, StockAssetsScrapHead stockAssetsScrapHead, StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) {

        if (AssetsScrapEnum.scrapStatus.SAVE.getValue().equals(stockAssetsScrapHead.getScrapStatus()) || AssetsScrapEnum.scrapStatus.CANCEL.getValue().equals(stockAssetsScrapHead.getScrapStatus())) {
            //获取总数量
            Long count = stockInventoryAssetImportService.selectInventoryAssetImportCount(stockAssetsScrapHead.getBindBatchCode());
            stockAssetsScrapHeadRespDTO.setRealScrapCount(count.intValue());
            //查询资产总净值
            BigDecimal netValue = stockInventoryAssetImportService.selectAssetImportNetValue(stockAssetsScrapHead.getBindBatchCode());
            stockAssetsScrapHeadRespDTO.setRealScrapValue(netValue);

            InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO = new InventoryAssetImportSearchReqDTO();
            inventoryAssetImportSearchReqDTO.setBindBatchCode(stockAssetsScrapHead.getBindBatchCode());
            inventoryAssetImportSearchReqDTO.setPageNum(stockAssetsScrapHeadReqDTO.getPageNum());
            inventoryAssetImportSearchReqDTO.setPageSize(stockAssetsScrapHeadReqDTO.getPageSize());
            inventoryAssetImportSearchReqDTO.initPageParam();
            //2.分页获取报废资产
            List<StockInventoryAssetImport> stockInventoryAssetImportList = stockInventoryAssetImportService.selectInventoryAssetImportBySelective(inventoryAssetImportSearchReqDTO, inventoryAssetImportSearchReqDTO.getPageSize(), inventoryAssetImportSearchReqDTO.getStartNum(), "inventory_asset_imp_id");

            //3.补全返回信息
            List<StockAssetsScrapLineRespDTO> stockAssetsScrapLineRespDTOList = new ArrayList<>(stockInventoryAssetImportList.size());
            settingExtendField(stockInventoryAssetImportList, stockAssetsScrapLineRespDTOList);
            stockAssetsScrapHeadRespDTO.setStockAssetsScrapLineRespDTOList(stockAssetsScrapLineRespDTOList);
        } else {
            stockAssetsScrapHeadReqDTO.initPageParam();
            StockAssetsScrapLineExample lineExample = new StockAssetsScrapLineExample();
            StockAssetsScrapLineExample.Criteria criteria = lineExample.createCriteria();
            criteria.andHeadIdEqualTo(stockAssetsScrapHead.getHeadId());
            lineExample.setOrderByClause("line_id desc");
            lineExample.setLimit(stockAssetsScrapHeadReqDTO.getPageSize());
            lineExample.setOffset(stockAssetsScrapHeadReqDTO.getStartNum());
            List<StockAssetsScrapLine> stockAssetsScrapLineList = stockAssetsScrapLineMapper.selectByExample(lineExample);
            if (CollectionUtils.isEmpty(stockAssetsScrapLineList)) {
                stockAssetsScrapHeadRespDTO.setStockAssetsScrapLineRespDTOList(new ArrayList<>());
                return;
            }

            List<StockAssetsScrapLineRespDTO> stockAssetsScrapLineRespDTOList = new ArrayList<>(stockAssetsScrapLineList.size());
            settingLineRespField(stockAssetsScrapLineList, stockAssetsScrapLineRespDTOList);
            stockAssetsScrapHeadRespDTO.setStockAssetsScrapLineRespDTOList(stockAssetsScrapLineRespDTOList);
        }
    }

    /**
     * 获取报废单据号
     *
     * @return
     */
    private String getScrapNo() {
        String poNo = OrderUtil.getOrderNo(OrderEnum.ASSETS_SCRAP_NO);
        if (StringUtils.isBlank(poNo)) {
            log.info("调用公用生成编号出错");
            return "PO" + System.currentTimeMillis();
        }

        return poNo;
    }

    /**
     * 转换资产剩余使用年限类型
     *
     * @param surplusYearStr
     * @return
     */
    private Integer changeSurplusLife(String surplusYearStr) {
        if (surplusYearStr.contains("年")) {
            Integer year = Integer.valueOf(StringUtils.substringBefore(surplusYearStr, "年"));
            Integer month = Integer.valueOf(StringUtils.substringBetween(surplusYearStr, "年", "个月"));
            return year * CommonConstant.MONTH_NUM + month;
        } else {
            Integer month = Integer.valueOf(StringUtils.substringBefore(surplusYearStr, "个月"));
            return month;
        }
    }

    /**
     * 组装查询条件
     *
     * @param stockAssetsScrapHeadReqDTO
     */
    private StockAssetsScrapHeadExample getStockAssetsScrapHeadExample(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException {
        StockAssetsScrapHeadExample example = new StockAssetsScrapHeadExample();
        StockAssetsScrapHeadExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(stockAssetsScrapHeadReqDTO.getScrapNo())) {
            criteria.andScrapNoEqualTo(stockAssetsScrapHeadReqDTO.getScrapNo());
        }
        if (StringUtils.isNotBlank(stockAssetsScrapHeadReqDTO.getBillingUser())) {
            criteria.andBillingUserEqualTo(stockAssetsScrapHeadReqDTO.getBillingUser());
        }
        if (StringUtils.isNotBlank(stockAssetsScrapHeadReqDTO.getBillingTimeStart()) && StringUtils.isNotBlank(stockAssetsScrapHeadReqDTO.getBillingTimeEnd())) {
            Date dateStart = DateUtils.dateParse(stockAssetsScrapHeadReqDTO.getBillingTimeStart(), null);
            Date dateEnd = DateUtils.dateParse(stockAssetsScrapHeadReqDTO.getBillingTimeEnd(), null);
            criteria.andBillingTimeBetween(dateStart, dateEnd);
        }
        if (stockAssetsScrapHeadReqDTO.getScrapStatus() != null) {
            criteria.andScrapStatusEqualTo(stockAssetsScrapHeadReqDTO.getScrapStatus());
        }

        return example;
    }
}
