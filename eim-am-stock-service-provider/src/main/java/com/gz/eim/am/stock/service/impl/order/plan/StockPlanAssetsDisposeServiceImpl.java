package com.gz.eim.am.stock.service.impl.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.FileReqDTO;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import com.gz.eim.am.stock.dto.response.file.StockAttachDTO;
import com.gz.eim.am.stock.dto.response.order.plan.DeliveryPlanHeadRespDTO;
import com.gz.eim.am.stock.dto.response.order.plan.DeliveryPlanLineRespDTO;
import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockDeliveryPlanHead;
import com.gz.eim.am.stock.entity.StockDeliveryPlanLine;
import com.gz.eim.am.stock.entity.vo.StockDeliveryPlanLineInfo;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.file.StockFileCommonService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanHeadService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanLineService;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsDisposeService;
import com.gz.eim.am.stock.service.warehouse.StockRoleKeeperService;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.DeliveryPlanHeadEnum;
import com.gz.eim.am.stock.util.em.DeliveryPlanLineEnum;
import com.gz.eim.am.stock.util.em.ManageRoleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lishuyang
 * @date: 2020/5/7
 * @description: 资产计划处置单service实现类
 */
@Slf4j
@Service
public class StockPlanAssetsDisposeServiceImpl implements StockPlanAssetsDisposeService {
    @Autowired
    private StockRoleKeeperService stockRoleKeeperService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private StockDeliveryPlanHeadService stockDeliveryPlanHeadService;
    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;
    @Autowired
    private FileServiceApi fileServiceApi;
    @Value("${project.file.systemModule}")
    private String systemModule;
    @Value("${project.file.attachModule}")
    private String attachModule;

    @Autowired
    private StockFileCommonService stockFileCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData savePlanAssetsDispose(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) {
        String checkSaveDisposeParam = checkSaveParam (deliveryPlanHeadReqDTO, user);
        if (StringUtils.isNotBlank (checkSaveDisposeParam)) {
            return ResponseData.createFailResult (checkSaveDisposeParam);
        }

        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = new ArrayList<> ();
        //资产处置出库单赋值
        prepareSaveDisposeDbBeanDTO (deliveryPlanHeadReqDTO, stockDeliveryPlanHead, stockDeliveryPlanLineList, user);
        stockDeliveryPlanHeadService.insertStockDeliveryPlanHead (stockDeliveryPlanHead);
        if (!CollectionUtils.isEmpty (stockDeliveryPlanLineList)) {
            stockDeliveryPlanLineList.forEach (stockDeliveryPlanLine -> stockDeliveryPlanLine.setDeliveryPlanHeadId (stockDeliveryPlanHead.getDeliveryPlanHeadId ()));
            stockDeliveryPlanLineService.batchInsertStockDeliveryPlanLine (stockDeliveryPlanLineList);
        }
        // 推送审批流

        return ResponseData.createSuccessResult ();
    }

    @Override
    public ResponseData selectPlanAssetsDispose(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO, JwtUser user) {
        deliveryPlanSearchReqDTO.setOutStockType (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ());
        return stockDeliveryPlanHeadService.selectDeliveryPlan (deliveryPlanSearchReqDTO, user);
    }

    @Override
    public ResponseData selectPlanAssetsDisposeByNo(String deliveryPlanNo, Integer operationType, JwtUser user) throws IllegalAccessException, ParseException, InvocationTargetException {
        String checkSelectDetailParamResult = this.checkSelectDetailParam (deliveryPlanNo, operationType);
        if (StringUtils.isNotBlank (checkSelectDetailParamResult)) {
            return ResponseData.createFailResult (checkSelectDetailParamResult);
        }

        //获取头
        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
        stockDeliveryPlanHead.setDeliveryPlanNo (deliveryPlanNo);
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = stockDeliveryPlanHeadService.selectDeliveryPlanByParam (stockDeliveryPlanHead);
        stockDeliveryPlanHead = stockDeliveryPlanHeadList.get (CommonConstant.NUMBER_ZERO);
        DeliveryPlanHeadRespDTO deliveryPlanHeadRespDTO = stockDeliveryPlanHeadService.modelToDTO (stockDeliveryPlanHead);
        stockDeliveryPlanHeadService.settingRelationValues (deliveryPlanHeadRespDTO);

        StockDeliveryPlanLine stockDeliveryPlanLineParam = new StockDeliveryPlanLine ();
        stockDeliveryPlanLineParam.setDeliveryPlanHeadId (deliveryPlanHeadRespDTO.getDeliveryPlanHeadId ());
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectByParam (stockDeliveryPlanLineParam);
        //详情查看（type=0）：返回已出库数量字段；出库页查看只返回未处置资产行
        if (operationType.equals (CommonConstant.NUMBER_ONE)) {
            stockDeliveryPlanLineList = stockDeliveryPlanLineList.stream ().filter (stockDeliveryPlanLine -> !DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode ().equals (stockDeliveryPlanLine.getStatus ())).collect (Collectors.toList ());
        }else {
            deliveryPlanHeadRespDTO.setDisponseCount(stockDeliveryPlanLineList.stream().filter(stockDeliveryPlanLine -> DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode ().equals (stockDeliveryPlanLine.getStatus ())).count());
        }
        List<DeliveryPlanLineRespDTO> deliveryPlanLineRespDTOS = stockDeliveryPlanLineService.modelToDTO (stockDeliveryPlanLineList);
        stockDeliveryPlanLineService.settingDeliveryPlanLine (deliveryPlanLineRespDTOS);
        stockDeliveryPlanLineService.settingDeliveryPlanLineAssets (deliveryPlanLineRespDTOS);
        deliveryPlanHeadRespDTO.setDeliveryPlanLineRespDTOS (deliveryPlanLineRespDTOS);

        List<StockAttachDTO> stockAttachDTOList = stockFileCommonService.selectFileAttachsByNo(deliveryPlanNo,FileConstant.PAGE_MODULE_DELIVERY);


       /* QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO ();
        queryFileReqDTO.setRelId (deliveryPlanNo);
        queryFileReqDTO.setSystemModule (systemModule);
        queryFileReqDTO.setAttachModule (attachModule);
        queryFileReqDTO.setPageModule (FileConstant.PAGE_MODULE_DELIVERY);
        ResponseData<List<Map<String, Object>>> responseData = fileServiceApi.getSysAttachDTOListSelective (queryFileReqDTO);
        if (!responseData.getCode ().equals (ResponseCode.SUCCESS_CODE)) {
            throw new ServiceUncheckedException ("获取附件信息出错");
        }
        if (CollectionUtils.isEmpty (responseData.getData ())) {
            return ResponseData.createSuccessResult (deliveryPlanHeadRespDTO);
        }

        List<Map<String, Object>> sysAttachDTOList = responseData.getData ();
        List<StockAttachDTO> stockAttachDTOList = new ArrayList<> (sysAttachDTOList.size ());
        for (Map<String, Object> stringObjectMap : sysAttachDTOList) {
            if (null == stringObjectMap) {
                continue;
            }
            StockAttachDTO stockAttachDTO = new StockAttachDTO ();
            if (null != stringObjectMap.get ("attachId")) {
                stockAttachDTO.setAttachId (Long.valueOf ((Integer) stringObjectMap.get ("attachId")));
            }
            stockAttachDTO.setAttachId (Long.valueOf ((Integer) stringObjectMap.get ("attachId")));
            if (null != stringObjectMap.get ("attachExtId")) {
                stockAttachDTO.setAttachExtId (Long.valueOf ((Integer) stringObjectMap.get ("attachExtId")));
            }

            if (null != stringObjectMap.get ("fileUniqueId")) {
                stockAttachDTO.setFileUniqueId ((String) stringObjectMap.get ("fileUniqueId"));
                FileReqDTO fileReqDTO = new FileReqDTO ();
                fileReqDTO.setFileUniqueId ((String) stringObjectMap.get ("fileUniqueId"));
                fileReqDTO.setSystemModule (systemModule);
                ResponseData<String> stringResponseData = fileServiceApi.getDownloadUrl (fileReqDTO);
                if (null != stringResponseData && ResponseCode.SUCCESS_CODE.equals (stringResponseData.getCode ()) && null != stringResponseData.getData ()) {
                    stockAttachDTO.setDownloadUrl (fileServiceApi.getDownloadUrl (fileReqDTO).getData ().toString ());
                }
            }

            if (null != stringObjectMap.get ("attachSaveName")) {
                stockAttachDTO.setFileName ((String) stringObjectMap.get ("attachSaveName"));
            }


            if (null != stringObjectMap.get ("showUrl")) {
                stockAttachDTO.setShowUrl ((String) stringObjectMap.get ("showUrl"));
            }
            stockAttachDTOList.add (stockAttachDTO);
        }*/
        if(stockAttachDTOList != null){
            deliveryPlanHeadRespDTO.setStockAttachDTOList (stockAttachDTOList);
        }
        return ResponseData.createSuccessResult (deliveryPlanHeadRespDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData planAssetsDisposeBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) throws InvocationTargetException, IllegalAccessException, ParseException ,RuntimeException{
        String checkParam = this.checkOutBoundParam (deliveryPlanHeadReqDTO, user);
        if (checkParam != null) {
            return ResponseData.createFailResult (checkParam);
        }

        //赋值
        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
        List<StockDeliveryPlanLine> existDeliveryPlanLineList = new ArrayList<> ();
        prepareOutBoundDbBeanDTO (deliveryPlanHeadReqDTO, user, stockDeliveryPlanHead, existDeliveryPlanLineList);
        //更新出库行
        if (!CollectionUtils.isEmpty (existDeliveryPlanLineList)) {
            for (StockDeliveryPlanLine stockDeliveryPlanLine : existDeliveryPlanLineList) {
                StockDeliveryPlanLineInfo stockDeliveryPlanLineInfo = new StockDeliveryPlanLineInfo ();
                stockDeliveryPlanLineInfo.setAssetsCode (stockDeliveryPlanLine.getAssetsCode ());
                stockDeliveryPlanLineInfo.setDeliveryPlanHeadId (stockDeliveryPlanLine.getDeliveryPlanHeadId ());
                stockDeliveryPlanLineService.updateParamSelective (stockDeliveryPlanLineInfo, stockDeliveryPlanLine);
            }

        }


        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectNotAlready (deliveryPlanHeadReqDTO.getDeliveryPlanHeadId ());
        if (CollectionUtils.isEmpty (stockDeliveryPlanLineList)) {
            stockDeliveryPlanHead.setStatus (DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode ());
        } else {
            stockDeliveryPlanHead.setStatus (DeliveryPlanHeadEnum.Status.SECTION_OUT.getCode ());
        }
        //更新计划出库单
        stockDeliveryPlanHeadService.updateByNoSelective (stockDeliveryPlanHead);
        List<String> assetCodes = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().stream ().map (a -> a.getAssetsCode ()).collect (Collectors.toList ());
        StockDeliveryPlanLineInfo stockDeliveryPlanLineInfo = new StockDeliveryPlanLineInfo ();
        stockDeliveryPlanLineInfo.setDeliveryPlanHeadId (deliveryPlanHeadReqDTO.getDeliveryPlanHeadId ());
        stockDeliveryPlanLineInfo.setAssetCodes (assetCodes);
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList1 = stockDeliveryPlanLineService.selectByInfoParam (stockDeliveryPlanLineInfo);
        deliveryPlanHeadReqDTO.setDeliveryPlanLineReqDTOS (new ArrayList<> ());
        stockDeliveryPlanLineList1.stream ()
                .forEach (line -> {
                    DeliveryPlanLineReqDTO planLineReqDTO = new DeliveryPlanLineReqDTO ();
                    BeanUtils.copyProperties (line, planLineReqDTO);
                    planLineReqDTO.setThisNumber (1);
                    planLineReqDTO.setDeliveryPlanHeadId (deliveryPlanHeadReqDTO.getDeliveryPlanHeadId ());
                    deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().add (planLineReqDTO);
                });

        //生成出库单
        stockDeliveryPlanHeadService.generateStockDelivery (deliveryPlanHeadReqDTO, Boolean.FALSE, user);


        //更新附件
        if (!CollectionUtils.isEmpty (deliveryPlanHeadReqDTO.getAttachIds ())) {
            List<SysAttachReqDTO> sysAttachReqDTOS = new ArrayList<> (deliveryPlanHeadReqDTO.getAttachIds ().size ());
            deliveryPlanHeadReqDTO.getAttachIds ().forEach (attachId -> {
                        SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO ();
                        sysAttachReqDTO.setSystemModule (systemModule);
                        sysAttachReqDTO.setAttachModule (attachModule);
                        sysAttachReqDTO.setPageModule (FileConstant.PAGE_MODULE_DELIVERY);
                        sysAttachReqDTO.setAttachId (attachId);
                        sysAttachReqDTO.setRelId (deliveryPlanHeadReqDTO.getDeliveryPlanNo ());
                        sysAttachReqDTOS.add (sysAttachReqDTO);
                    }
            );
            ResponseData responseData = fileServiceApi.updateSysAttachList (sysAttachReqDTOS);
            if (null == responseData || !ResponseCode.SUCCESS_CODE.equals (responseData.getCode ())) {
                log.error ("附件处理失败，{}", null == responseData ? null : responseData.getMessage ());
                throw new ServiceUncheckedException ("附件处理失败，请联系相关人员");
            }
        }

        return ResponseData.createSuccessResult ();
    }

    @Override
    public ResponseData cancelPlanAssetsDisposeByNo(String deliveryPlanNo) {
        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
        stockDeliveryPlanHead.setDeliveryPlanNo (deliveryPlanNo);
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = stockDeliveryPlanHeadService.selectDeliveryPlanByParam (stockDeliveryPlanHead);
        stockDeliveryPlanHead = stockDeliveryPlanHeadList.get (CommonConstant.NUMBER_ZERO);
        if(stockDeliveryPlanHead == null){
            return ResponseData.createFailResult("该处置单据不存在");
        }
        // 部分出库的单据不可以取消
        if(DeliveryPlanHeadEnum.Status.SECTION_OUT.getCode().equals(stockDeliveryPlanHead.getStatus())){
            return ResponseData.createFailResult("部分出库的单据不可以取消");
        }
        return stockDeliveryPlanHeadService.cancelDeliveryPlanHeadByDeliveryPlanHeadId(stockDeliveryPlanHead.getDeliveryPlanHeadId());
    }

    /**
     * 出库操作赋值出库单
     *
     * @param deliveryPlanHeadReqDTO
     * @param user
     * @param stockDeliveryPlanHead
     * @param existDeliveryPlanLineList
     */
    private void prepareOutBoundDbBeanDTO(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user, StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> existDeliveryPlanLineList) {

        stockDeliveryPlanHead.setDeliveryPlanNo (deliveryPlanHeadReqDTO.getDeliveryPlanNo ());
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = stockDeliveryPlanHeadService.selectDeliveryPlanByParam (stockDeliveryPlanHead);
        deliveryPlanHeadReqDTO.setDeliveryPlanHeadId (stockDeliveryPlanHeadList.get (CommonConstant.NUMBER_ZERO).getDeliveryPlanHeadId ());

        for (DeliveryPlanLineReqDTO deliveryPlanLineReqDTO : deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ()) {
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine ();
            stockDeliveryPlanLine.setDeliveryPlanHeadId (deliveryPlanHeadReqDTO.getDeliveryPlanHeadId ());
            stockDeliveryPlanLine.setStatus (DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode ());
            stockDeliveryPlanLine.setRealNumber (1);
            stockDeliveryPlanLine.setAssetsCode (deliveryPlanLineReqDTO.getAssetsCode ());
            stockDeliveryPlanLine.setUpdatedBy (user.getEmployeeCode ());
            stockDeliveryPlanHead.setUpdatedAt (new Date ());
            existDeliveryPlanLineList.add (stockDeliveryPlanLine);
            deliveryPlanLineReqDTO.setThisNumber (CommonConstant.NUMBER_ONE);
        }

        stockDeliveryPlanHead.setUpdatedBy (user.getEmployeeCode ());
        stockDeliveryPlanHead.setUpdatedAt (new Date ());
    }

    /**
     * 校验出库单出库参数
     *
     * @param deliveryPlanHeadReqDTO
     * @param user
     * @return
     */
    private String checkOutBoundParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) {
        if (null == deliveryPlanHeadReqDTO || StringUtils.isBlank (deliveryPlanHeadReqDTO.getDeliveryPlanNo ()) || CollectionUtils.isEmpty (deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ())) {
            return "必填参数为空";
        }

        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
        stockDeliveryPlanHead.setDeliveryPlanNo (deliveryPlanHeadReqDTO.getDeliveryPlanNo ());
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = stockDeliveryPlanHeadService.selectDeliveryPlanByParam (stockDeliveryPlanHead);
        if (CollectionUtils.isEmpty (stockDeliveryPlanHeadList)) {
            return "当前单号的计划处置单不存在";
        } else if (stockDeliveryPlanHeadList.size () > CommonConstant.NUMBER_ONE) {
            return "当前单号的计划处置单不唯一，请联系相关人员处理";
        } else if (!stockDeliveryPlanHeadList.get (CommonConstant.NUMBER_ZERO).getOutStockType ().equals (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ())) {
            return "当前处置单不存在";
        }

        stockDeliveryPlanHead = stockDeliveryPlanHeadList.get (CommonConstant.NUMBER_ZERO);
        if (!DeliveryPlanHeadEnum.Status.WAIT_OUT.getCode ().equals (stockDeliveryPlanHead.getStatus ()) && !DeliveryPlanHeadEnum.Status.SECTION_OUT.getCode ().equals (stockDeliveryPlanHead.getStatus ())) {
            return "当前单据状态不可出库";
        }

        //查询仓库权限
        if(StringUtils.isNotEmpty(stockDeliveryPlanHead.getOutWarehouseCode ())){
            //查询仓库权限
            final List<String> wc = stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, null);
            if (CollectionUtils.isEmpty (wc)) {
                return "无操作仓库的权限";
            }

            if (!wc.get (0).equals (ManageRoleEnum.Type.ALL.getValue ())) {
                if (!wc.contains (stockDeliveryPlanHead.getOutWarehouseCode ())) {
                    return "无操作处置仓库的权限";
                }
            }
        }

        //校验行
        String checkOutBoundLineParamResult = this.checkOutBoundLineParam (deliveryPlanHeadReqDTO, stockDeliveryPlanHead);
        if (StringUtils.isNotBlank (checkOutBoundLineParamResult)) {
            return checkOutBoundLineParamResult;
        }

        return null;
    }

    /**
     * 校验出库操作行参数
     *
     * @param deliveryPlanHeadReqDTO
     * @return
     */
    private String checkOutBoundLineParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, StockDeliveryPlanHead stockDeliveryPlanHead) {
        if (CollectionUtils.isEmpty (deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ())) {
            return "不存在需要存在的处置的数据";
        }

        List<String> assetCodes = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().stream ().filter (a -> StringUtils.isNotBlank (a.getAssetsCode ())).map (b -> b.getAssetsCode ()).collect (Collectors.toList ());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets (null, assetCodes);
        if (CollectionUtils.isEmpty (stockAssetsList)) {
            return "需要处置的资产编码不存在";
        }

        if (assetCodes.size () != deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().size ()) {
            return "需要处置的资产编码部分不存在或重复";
        }
        StringBuilder sb = new StringBuilder ();
        for (StockAssets stockAssets : stockAssetsList) {
            if (!AssetsEnum.statusType.IDLE.getValue ().equals (stockAssets.getStatus ()) && !AssetsEnum.statusType.USED.getValue ().equals (stockAssets.getStatus ())) {
                sb.append ("资产编码" + stockAssets.getAssetsCode () + "不在库而且不在使用中，所以不可处置;");
                // 如果有仓库编码才去校验相关的逻辑
                continue;
            }
            // 如果在库，就要校验资产仓库是否合法
            if (StringUtils.isNotEmpty (stockDeliveryPlanHead.getOutWarehouseCode ()) && !stockDeliveryPlanHead.getOutWarehouseCode ().equals(stockAssets.getWarehouseCode())) {
                sb.append ("资产编码[" + stockAssets.getAssetsCode() + "]不在当前仓库，或者为使用状态，不能处置");
                continue;
            }
            // 如果不在库，就要校验资产是否不在库
            if (StringUtils.isEmpty (stockDeliveryPlanHead.getOutWarehouseCode ()) && StringUtils.isNotEmpty(stockAssets.getWarehouseCode())) {
                sb.append ("资产编码[" + stockAssets.getAssetsCode() + "]是在库资产，当前单据只能处置使用中的资产");
                continue;
            }
        }

        if (sb.length () > 0) {
            return sb.toString ();
        }


        StockDeliveryPlanLineInfo stockDeliveryPlanLineInfo = new StockDeliveryPlanLineInfo ();
        stockDeliveryPlanLineInfo.setDeliveryPlanHeadId (stockDeliveryPlanHead.getDeliveryPlanHeadId ());
        stockDeliveryPlanLineInfo.setAssetCodes (assetCodes);
        List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectByInfoParam (stockDeliveryPlanLineInfo);
        if (stockDeliveryPlanLineList.size () != assetCodes.size ()) {
            return "需要处置的资产编码部分与处置单头不匹配；";
        }

        if (!CollectionUtils.isEmpty (stockDeliveryPlanLineList)) {
            for (StockDeliveryPlanLine stockDeliveryPlanLine : stockDeliveryPlanLineList) {
                if (!DeliveryPlanLineEnum.Status.WAIT_OUT.getCode ().equals (stockDeliveryPlanLine.getStatus ()) && !DeliveryPlanLineEnum.Status.SECTION_OUT.getCode ().equals (stockDeliveryPlanLine.getStatus ())) {
                    sb.append ("资产编码" + stockDeliveryPlanLine.getAssetsCode () + "已处置过了;");
                }
            }
        }


        if (sb.length () > 0) {
            return sb.toString ();
        }
        return null;
    }


    /**
     * 校验查看详情参数
     *
     * @param deliveryPlanNo
     * @param operationType
     * @return
     */
    private String checkSelectDetailParam(String deliveryPlanNo, Integer operationType) {
        if (StringUtils.isBlank (deliveryPlanNo) || null == operationType) {
            return "必填参数为空";
        }
        StockDeliveryPlanHead stockDeliveryPlanHead = new StockDeliveryPlanHead ();
        stockDeliveryPlanHead.setDeliveryPlanNo (deliveryPlanNo);
        List<StockDeliveryPlanHead> stockDeliveryPlanHeadList = stockDeliveryPlanHeadService.selectDeliveryPlanByParam (stockDeliveryPlanHead);
        if (CollectionUtils.isEmpty (stockDeliveryPlanHeadList)) {
            return "当前处置单不存在";
        } else if (stockDeliveryPlanHeadList.size () > CommonConstant.NUMBER_ONE) {
            return "处置单存在多个，请联系相关负责人";
        } else if (!stockDeliveryPlanHeadList.get (CommonConstant.NUMBER_ZERO).getOutStockType ().equals (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ())) {
            return "当前处置单不存在";
        }

        if (!operationType.equals (CommonConstant.NUMBER_ZERO) && !operationType.equals (CommonConstant.NUMBER_ONE)) {
            return "操作类型不符合规范";
        }
        return null;
    }

    /**
     * 赋值处置计划出库单参数
     *
     * @param deliveryPlanHeadReqDTO
     * @param stockDeliveryPlanHead
     * @param stockDeliveryPlanLineList
     * @param user
     */
    private void prepareSaveDisposeDbBeanDTO(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, StockDeliveryPlanHead stockDeliveryPlanHead, List<StockDeliveryPlanLine> stockDeliveryPlanLineList, JwtUser user) {
        stockDeliveryPlanHead.setDeliveryPlanNo (stockDeliveryPlanHeadService.getDeliveryPlanNo (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ()));
        stockDeliveryPlanHead.setOutStockType (DeliveryPlanHeadEnum.OutType.ASSET_DISPOSE.getCode ());
        stockDeliveryPlanHead.setOutWarehouseCode (StringUtils.isEmpty(deliveryPlanHeadReqDTO.getOutWarehouseCode ()) ? "" : deliveryPlanHeadReqDTO.getOutWarehouseCode ());
        if (null != deliveryPlanHeadReqDTO.getReasonCode ()) {
            stockDeliveryPlanHead.setReasonCode (deliveryPlanHeadReqDTO.getReasonCode ());
        } else {
            stockDeliveryPlanHead.setReasonCode (DeliveryPlanHeadEnum.Reason.DEFAULT.getCode ());
        }
        stockDeliveryPlanHead.setBillingTime (new Date ());
        stockDeliveryPlanHead.setBillingUser (user.getEmployeeCode ());
        stockDeliveryPlanHead.setRemark (deliveryPlanHeadReqDTO.getRemark ());
        stockDeliveryPlanHead.setPlanOutTime (new Date ());

        stockDeliveryPlanHead.setStatus (DeliveryPlanHeadEnum.Status.WAIT_OUT.getCode ());

        stockDeliveryPlanHead.setUpdatedBy (user.getEmployeeCode ());
        stockDeliveryPlanHead.setCreatedBy (user.getEmployeeCode ());

        if (StringUtils.isBlank (deliveryPlanHeadReqDTO.getUseAddress ())) {
            stockDeliveryPlanHead.setUseAddress ("");
        } else {
            stockDeliveryPlanHead.setUseAddress (deliveryPlanHeadReqDTO.getUseAddress ());
        }

        if (null != deliveryPlanHeadReqDTO.getBusinessType ()) {
            stockDeliveryPlanHead.setBusinessType (deliveryPlanHeadReqDTO.getBusinessType ());
        } else {
            stockDeliveryPlanHead.setBusinessType (DeliveryPlanHeadEnum.BusinessType.SELL.getCode ());
        }

        List<String> assetCodeList = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().stream ().map (a -> a.getAssetsCode ()).collect (Collectors.toList ());
        List<StockAssets> stockAssetsList = stockAssetsService.selectAssets (null, assetCodeList);
        Map<String, StockAssets> stockAssetsMap = new HashMap<> (CommonConstant.DEFAULT_MAP_SIZE);
        if (!CollectionUtils.isEmpty (stockAssetsList)) {
            stockAssetsList.forEach (stockAssets -> {
                stockAssetsMap.put (stockAssets.getAssetsCode (), stockAssets);
            });
        }

        for (DeliveryPlanLineReqDTO deliveryPlanLineReqDTO : deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ()) {
            StockDeliveryPlanLine stockDeliveryPlanLine = new StockDeliveryPlanLine ();
            if (MapUtils.isNotEmpty (stockAssetsMap)) {
                stockDeliveryPlanLine.setSuppliesCode (stockAssetsMap.get (deliveryPlanLineReqDTO.getAssetsCode ()).getSuppliesCode ());
            }
            stockDeliveryPlanLine.setAssetsCode (deliveryPlanLineReqDTO.getAssetsCode ());
            stockDeliveryPlanLine.setPlanOutTime (stockDeliveryPlanHead.getPlanOutTime ());
            //默认1个
            stockDeliveryPlanLine.setNumber (1);
            stockDeliveryPlanLine.setRealNumber (0);
            stockDeliveryPlanLine.setStatus (DeliveryPlanLineEnum.Status.WAIT_OUT.getCode ());
            stockDeliveryPlanLine.setCreatedBy (user.getEmployeeCode ());
            stockDeliveryPlanLine.setUpdatedBy (user.getEmployeeCode ());
            stockDeliveryPlanLineList.add (stockDeliveryPlanLine);
        }
    }

    /**
     * 校验计划出单保存参数
     *
     * @param deliveryPlanHeadReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO, JwtUser user) {
        if (null == deliveryPlanHeadReqDTO) {
            return "必填参数为空";
        }
//  可以处置使用中的资产，现在不使用出库仓库进行处置
//        if (StringUtils.isBlank (deliveryPlanHeadReqDTO.getOutWarehouseCode ())) {
//            return "处置仓库不能为空";
//        }
        if(StringUtils.isNotEmpty(deliveryPlanHeadReqDTO.getOutWarehouseCode ())){
            //查询仓库权限
            final List<String> wc = stockRoleKeeperService.selectKeepWarehouseByParam (user.getEmployeeCode (), null, null);
            if (CollectionUtils.isEmpty (wc)) {
                return "无操作仓库的权限";
            }
            // 不是空再去判断是否有仓库权限
            if (!StringUtils.isBlank (deliveryPlanHeadReqDTO.getOutWarehouseCode ())){
                if (!wc.get (0).equals (ManageRoleEnum.Type.ALL.getValue ())) {
                    if (!wc.contains (deliveryPlanHeadReqDTO.getOutWarehouseCode ())) {
                        return "无操作处置仓库的权限";
                    }
                }
            }

        }
        if (null == deliveryPlanHeadReqDTO.getReasonCode ()) {
            return "处置原因不能为空";
        } else if (deliveryPlanHeadReqDTO.getReasonCode () < DeliveryPlanHeadEnum.Reason.ROUTINE_DISPOSAL.getCode () || deliveryPlanHeadReqDTO.getReasonCode () > DeliveryPlanHeadEnum.Reason.OTHER_DISPOSAL.getCode ()) {
            return "处置原因必须符合规则";
        }

        if (null == deliveryPlanHeadReqDTO.getBusinessType ()) {
            return "处置类型不能为空";

        } else if (deliveryPlanHeadReqDTO.getBusinessType () < DeliveryPlanHeadEnum.BusinessType.SELL.getCode () || deliveryPlanHeadReqDTO.getBusinessType () > DeliveryPlanHeadEnum.BusinessType.OTHERS.getCode ()) {
            return "处置类型必须符合规则";
        }


        String checkSaveLineParamResult = this.checkSaveLineParam (deliveryPlanHeadReqDTO);
        if (StringUtils.isNotBlank (checkSaveLineParamResult)) {
            return checkSaveLineParamResult;
        }

        return null;
    }

    /**
     * 校验行参数
     *
     * @param deliveryPlanHeadReqDTO
     * @return
     */
    private String checkSaveLineParam(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {

        //新建处置单详情可为空
        if (CollectionUtils.isEmpty (deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ())) {
            return "处置单行情不能为空";
        }

        final StringBuilder sb = new StringBuilder ();
        final Integer detailSize = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().size ();
        final List<String> receiveAssets = new ArrayList<> (detailSize);
        for (int i = 0; i < detailSize; i++) {
            final DeliveryPlanLineReqDTO lineReqDTO = deliveryPlanHeadReqDTO.getDeliveryPlanLineReqDTOS ().get (i);
            if (StringUtils.isBlank (lineReqDTO.getAssetsCode ())) {
                sb.append ("处置出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产编码不能为空;");
                continue;
            }
            final StockAssets assets = this.stockAssetsService.selectAssetsByCode (lineReqDTO.getAssetsCode ());
            if (null == assets) {
                sb.append ("处置出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产信息查询不到，请检查输入；");
                continue;
            }
            // 如果在库，就要校验资产仓库是否合法
            if (StringUtils.isNotEmpty (deliveryPlanHeadReqDTO.getOutWarehouseCode ()) && !deliveryPlanHeadReqDTO.getOutWarehouseCode ().equals(assets.getWarehouseCode())) {
                sb.append ("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产不在当前仓库，或者为使用状态，不能处置");
                continue;
            }
            // 如果不在库，就要校验资产是否不在库
            if (StringUtils.isEmpty (deliveryPlanHeadReqDTO.getOutWarehouseCode ()) && StringUtils.isNotEmpty(assets.getWarehouseCode())) {
                sb.append ("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产是在库资产，当前单据只能处置使用中的资产");
                continue;
            }
            if (!AssetsEnum.statusType.IDLE.getValue ().equals(assets.getStatus ()) && !AssetsEnum.statusType.USED.getValue ().equals(assets.getStatus ())) {
                sb.append ("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产不是在库状态也不是使用中状态");
                continue;
            }
            if (receiveAssets.contains (lineReqDTO.getAssetsCode ())) {
                sb.append ("领用出库单详细数据的第" + (i + CommonConstant.NUMBER_ONE) + "条数据的资产编码重复;");
                continue;
            } else {
                receiveAssets.add (lineReqDTO.getAssetsCode ());
            }
        }
        if (sb.length () > 0) {
            return sb.toString ();
        }

        return null;
    }


}
