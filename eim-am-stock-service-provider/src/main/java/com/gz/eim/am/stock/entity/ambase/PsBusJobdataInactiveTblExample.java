package com.gz.eim.am.stock.entity.ambase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PsBusJobdataInactiveTblExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public PsBusJobdataInactiveTblExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEmplidIsNull() {
            addCriterion("EMPLID is null");
            return (Criteria) this;
        }

        public Criteria andEmplidIsNotNull() {
            addCriterion("EMPLID is not null");
            return (Criteria) this;
        }

        public Criteria andEmplidEqualTo(String value) {
            addCriterion("EMPLID =", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidNotEqualTo(String value) {
            addCriterion("EMPLID <>", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidGreaterThan(String value) {
            addCriterion("EMPLID >", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidGreaterThanOrEqualTo(String value) {
            addCriterion("EMPLID >=", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidLessThan(String value) {
            addCriterion("EMPLID <", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidLessThanOrEqualTo(String value) {
            addCriterion("EMPLID <=", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidLike(String value) {
            addCriterion("EMPLID like", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidNotLike(String value) {
            addCriterion("EMPLID not like", value, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidIn(List<String> values) {
            addCriterion("EMPLID in", values, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidNotIn(List<String> values) {
            addCriterion("EMPLID not in", values, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidBetween(String value1, String value2) {
            addCriterion("EMPLID between", value1, value2, "emplid");
            return (Criteria) this;
        }

        public Criteria andEmplidNotBetween(String value1, String value2) {
            addCriterion("EMPLID not between", value1, value2, "emplid");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNull() {
            addCriterion("COMPANY is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIsNotNull() {
            addCriterion("COMPANY is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyEqualTo(String value) {
            addCriterion("COMPANY =", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotEqualTo(String value) {
            addCriterion("COMPANY <>", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThan(String value) {
            addCriterion("COMPANY >", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("COMPANY >=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThan(String value) {
            addCriterion("COMPANY <", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLessThanOrEqualTo(String value) {
            addCriterion("COMPANY <=", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyLike(String value) {
            addCriterion("COMPANY like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotLike(String value) {
            addCriterion("COMPANY not like", value, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyIn(List<String> values) {
            addCriterion("COMPANY in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotIn(List<String> values) {
            addCriterion("COMPANY not in", values, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyBetween(String value1, String value2) {
            addCriterion("COMPANY between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andCompanyNotBetween(String value1, String value2) {
            addCriterion("COMPANY not between", value1, value2, "company");
            return (Criteria) this;
        }

        public Criteria andDeptidIsNull() {
            addCriterion("DEPTID is null");
            return (Criteria) this;
        }

        public Criteria andDeptidIsNotNull() {
            addCriterion("DEPTID is not null");
            return (Criteria) this;
        }

        public Criteria andDeptidEqualTo(String value) {
            addCriterion("DEPTID =", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidNotEqualTo(String value) {
            addCriterion("DEPTID <>", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidGreaterThan(String value) {
            addCriterion("DEPTID >", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidGreaterThanOrEqualTo(String value) {
            addCriterion("DEPTID >=", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidLessThan(String value) {
            addCriterion("DEPTID <", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidLessThanOrEqualTo(String value) {
            addCriterion("DEPTID <=", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidLike(String value) {
            addCriterion("DEPTID like", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidNotLike(String value) {
            addCriterion("DEPTID not like", value, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidIn(List<String> values) {
            addCriterion("DEPTID in", values, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidNotIn(List<String> values) {
            addCriterion("DEPTID not in", values, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidBetween(String value1, String value2) {
            addCriterion("DEPTID between", value1, value2, "deptid");
            return (Criteria) this;
        }

        public Criteria andDeptidNotBetween(String value1, String value2) {
            addCriterion("DEPTID not between", value1, value2, "deptid");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("LOCATION is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("LOCATION is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("LOCATION =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("LOCATION <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("LOCATION >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("LOCATION >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("LOCATION <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("LOCATION <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("LOCATION like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("LOCATION not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("LOCATION in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("LOCATION not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("LOCATION between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("LOCATION not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andJobcodeIsNull() {
            addCriterion("JOBCODE is null");
            return (Criteria) this;
        }

        public Criteria andJobcodeIsNotNull() {
            addCriterion("JOBCODE is not null");
            return (Criteria) this;
        }

        public Criteria andJobcodeEqualTo(String value) {
            addCriterion("JOBCODE =", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotEqualTo(String value) {
            addCriterion("JOBCODE <>", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeGreaterThan(String value) {
            addCriterion("JOBCODE >", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeGreaterThanOrEqualTo(String value) {
            addCriterion("JOBCODE >=", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeLessThan(String value) {
            addCriterion("JOBCODE <", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeLessThanOrEqualTo(String value) {
            addCriterion("JOBCODE <=", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeLike(String value) {
            addCriterion("JOBCODE like", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotLike(String value) {
            addCriterion("JOBCODE not like", value, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeIn(List<String> values) {
            addCriterion("JOBCODE in", values, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotIn(List<String> values) {
            addCriterion("JOBCODE not in", values, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeBetween(String value1, String value2) {
            addCriterion("JOBCODE between", value1, value2, "jobcode");
            return (Criteria) this;
        }

        public Criteria andJobcodeNotBetween(String value1, String value2) {
            addCriterion("JOBCODE not between", value1, value2, "jobcode");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNull() {
            addCriterion("SUPERVISOR_ID is null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNotNull() {
            addCriterion("SUPERVISOR_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdEqualTo(String value) {
            addCriterion("SUPERVISOR_ID =", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotEqualTo(String value) {
            addCriterion("SUPERVISOR_ID <>", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThan(String value) {
            addCriterion("SUPERVISOR_ID >", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThanOrEqualTo(String value) {
            addCriterion("SUPERVISOR_ID >=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThan(String value) {
            addCriterion("SUPERVISOR_ID <", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThanOrEqualTo(String value) {
            addCriterion("SUPERVISOR_ID <=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLike(String value) {
            addCriterion("SUPERVISOR_ID like", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotLike(String value) {
            addCriterion("SUPERVISOR_ID not like", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIn(List<String> values) {
            addCriterion("SUPERVISOR_ID in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotIn(List<String> values) {
            addCriterion("SUPERVISOR_ID not in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdBetween(String value1, String value2) {
            addCriterion("SUPERVISOR_ID between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotBetween(String value1, String value2) {
            addCriterion("SUPERVISOR_ID not between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andEmplClassIsNull() {
            addCriterion("empl_class is null");
            return (Criteria) this;
        }

        public Criteria andEmplClassIsNotNull() {
            addCriterion("empl_class is not null");
            return (Criteria) this;
        }

        public Criteria andEmplClassEqualTo(String value) {
            addCriterion("empl_class =", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassNotEqualTo(String value) {
            addCriterion("empl_class <>", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassGreaterThan(String value) {
            addCriterion("empl_class >", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassGreaterThanOrEqualTo(String value) {
            addCriterion("empl_class >=", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassLessThan(String value) {
            addCriterion("empl_class <", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassLessThanOrEqualTo(String value) {
            addCriterion("empl_class <=", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassLike(String value) {
            addCriterion("empl_class like", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassNotLike(String value) {
            addCriterion("empl_class not like", value, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassIn(List<String> values) {
            addCriterion("empl_class in", values, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassNotIn(List<String> values) {
            addCriterion("empl_class not in", values, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassBetween(String value1, String value2) {
            addCriterion("empl_class between", value1, value2, "emplClass");
            return (Criteria) this;
        }

        public Criteria andEmplClassNotBetween(String value1, String value2) {
            addCriterion("empl_class not between", value1, value2, "emplClass");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtIsNull() {
            addCriterion("FIRST_HIRE_DT is null");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtIsNotNull() {
            addCriterion("FIRST_HIRE_DT is not null");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtEqualTo(Date value) {
            addCriterion("FIRST_HIRE_DT =", value, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtNotEqualTo(Date value) {
            addCriterion("FIRST_HIRE_DT <>", value, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtGreaterThan(Date value) {
            addCriterion("FIRST_HIRE_DT >", value, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtGreaterThanOrEqualTo(Date value) {
            addCriterion("FIRST_HIRE_DT >=", value, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtLessThan(Date value) {
            addCriterion("FIRST_HIRE_DT <", value, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtLessThanOrEqualTo(Date value) {
            addCriterion("FIRST_HIRE_DT <=", value, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtIn(List<Date> values) {
            addCriterion("FIRST_HIRE_DT in", values, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtNotIn(List<Date> values) {
            addCriterion("FIRST_HIRE_DT not in", values, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtBetween(Date value1, Date value2) {
            addCriterion("FIRST_HIRE_DT between", value1, value2, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andFirstHireDtNotBetween(Date value1, Date value2) {
            addCriterion("FIRST_HIRE_DT not between", value1, value2, "firstHireDt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtIsNull() {
            addCriterion("TER_EFFDT is null");
            return (Criteria) this;
        }

        public Criteria andTerEffdtIsNotNull() {
            addCriterion("TER_EFFDT is not null");
            return (Criteria) this;
        }

        public Criteria andTerEffdtEqualTo(Date value) {
            addCriterion("TER_EFFDT =", value, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtNotEqualTo(Date value) {
            addCriterion("TER_EFFDT <>", value, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtGreaterThan(Date value) {
            addCriterion("TER_EFFDT >", value, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtGreaterThanOrEqualTo(Date value) {
            addCriterion("TER_EFFDT >=", value, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtLessThan(Date value) {
            addCriterion("TER_EFFDT <", value, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtLessThanOrEqualTo(Date value) {
            addCriterion("TER_EFFDT <=", value, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtIn(List<Date> values) {
            addCriterion("TER_EFFDT in", values, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtNotIn(List<Date> values) {
            addCriterion("TER_EFFDT not in", values, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtBetween(Date value1, Date value2) {
            addCriterion("TER_EFFDT between", value1, value2, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andTerEffdtNotBetween(Date value1, Date value2) {
            addCriterion("TER_EFFDT not between", value1, value2, "terEffdt");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("NAME is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("NAME is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("NAME =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("NAME <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("NAME >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("NAME >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("NAME <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("NAME <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("NAME like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("NAME not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("NAME in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("NAME not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("NAME between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("NAME not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNull() {
            addCriterion("LAST_NAME is null");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNotNull() {
            addCriterion("LAST_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andLastNameEqualTo(String value) {
            addCriterion("LAST_NAME =", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotEqualTo(String value) {
            addCriterion("LAST_NAME <>", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThan(String value) {
            addCriterion("LAST_NAME >", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("LAST_NAME >=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThan(String value) {
            addCriterion("LAST_NAME <", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThanOrEqualTo(String value) {
            addCriterion("LAST_NAME <=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLike(String value) {
            addCriterion("LAST_NAME like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotLike(String value) {
            addCriterion("LAST_NAME not like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameIn(List<String> values) {
            addCriterion("LAST_NAME in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotIn(List<String> values) {
            addCriterion("LAST_NAME not in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameBetween(String value1, String value2) {
            addCriterion("LAST_NAME between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotBetween(String value1, String value2) {
            addCriterion("LAST_NAME not between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNull() {
            addCriterion("FIRST_NAME is null");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNotNull() {
            addCriterion("FIRST_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andFirstNameEqualTo(String value) {
            addCriterion("FIRST_NAME =", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotEqualTo(String value) {
            addCriterion("FIRST_NAME <>", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThan(String value) {
            addCriterion("FIRST_NAME >", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("FIRST_NAME >=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThan(String value) {
            addCriterion("FIRST_NAME <", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThanOrEqualTo(String value) {
            addCriterion("FIRST_NAME <=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLike(String value) {
            addCriterion("FIRST_NAME like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotLike(String value) {
            addCriterion("FIRST_NAME not like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIn(List<String> values) {
            addCriterion("FIRST_NAME in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotIn(List<String> values) {
            addCriterion("FIRST_NAME not in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameBetween(String value1, String value2) {
            addCriterion("FIRST_NAME between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotBetween(String value1, String value2) {
            addCriterion("FIRST_NAME not between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("SEX is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("SEX is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(String value) {
            addCriterion("SEX =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(String value) {
            addCriterion("SEX <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(String value) {
            addCriterion("SEX >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(String value) {
            addCriterion("SEX >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(String value) {
            addCriterion("SEX <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(String value) {
            addCriterion("SEX <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLike(String value) {
            addCriterion("SEX like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotLike(String value) {
            addCriterion("SEX not like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<String> values) {
            addCriterion("SEX in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<String> values) {
            addCriterion("SEX not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(String value1, String value2) {
            addCriterion("SEX between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(String value1, String value2) {
            addCriterion("SEX not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeIsNull() {
            addCriterion("NATIONAL_ID_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeIsNotNull() {
            addCriterion("NATIONAL_ID_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeEqualTo(String value) {
            addCriterion("NATIONAL_ID_TYPE =", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeNotEqualTo(String value) {
            addCriterion("NATIONAL_ID_TYPE <>", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeGreaterThan(String value) {
            addCriterion("NATIONAL_ID_TYPE >", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeGreaterThanOrEqualTo(String value) {
            addCriterion("NATIONAL_ID_TYPE >=", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeLessThan(String value) {
            addCriterion("NATIONAL_ID_TYPE <", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeLessThanOrEqualTo(String value) {
            addCriterion("NATIONAL_ID_TYPE <=", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeLike(String value) {
            addCriterion("NATIONAL_ID_TYPE like", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeNotLike(String value) {
            addCriterion("NATIONAL_ID_TYPE not like", value, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeIn(List<String> values) {
            addCriterion("NATIONAL_ID_TYPE in", values, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeNotIn(List<String> values) {
            addCriterion("NATIONAL_ID_TYPE not in", values, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeBetween(String value1, String value2) {
            addCriterion("NATIONAL_ID_TYPE between", value1, value2, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdTypeNotBetween(String value1, String value2) {
            addCriterion("NATIONAL_ID_TYPE not between", value1, value2, "nationalIdType");
            return (Criteria) this;
        }

        public Criteria andNationalIdIsNull() {
            addCriterion("NATIONAL_ID is null");
            return (Criteria) this;
        }

        public Criteria andNationalIdIsNotNull() {
            addCriterion("NATIONAL_ID is not null");
            return (Criteria) this;
        }

        public Criteria andNationalIdEqualTo(String value) {
            addCriterion("NATIONAL_ID =", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdNotEqualTo(String value) {
            addCriterion("NATIONAL_ID <>", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdGreaterThan(String value) {
            addCriterion("NATIONAL_ID >", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdGreaterThanOrEqualTo(String value) {
            addCriterion("NATIONAL_ID >=", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdLessThan(String value) {
            addCriterion("NATIONAL_ID <", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdLessThanOrEqualTo(String value) {
            addCriterion("NATIONAL_ID <=", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdLike(String value) {
            addCriterion("NATIONAL_ID like", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdNotLike(String value) {
            addCriterion("NATIONAL_ID not like", value, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdIn(List<String> values) {
            addCriterion("NATIONAL_ID in", values, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdNotIn(List<String> values) {
            addCriterion("NATIONAL_ID not in", values, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdBetween(String value1, String value2) {
            addCriterion("NATIONAL_ID between", value1, value2, "nationalId");
            return (Criteria) this;
        }

        public Criteria andNationalIdNotBetween(String value1, String value2) {
            addCriterion("NATIONAL_ID not between", value1, value2, "nationalId");
            return (Criteria) this;
        }

        public Criteria andPhoneCellIsNull() {
            addCriterion("PHONE_CELL is null");
            return (Criteria) this;
        }

        public Criteria andPhoneCellIsNotNull() {
            addCriterion("PHONE_CELL is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneCellEqualTo(String value) {
            addCriterion("PHONE_CELL =", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellNotEqualTo(String value) {
            addCriterion("PHONE_CELL <>", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellGreaterThan(String value) {
            addCriterion("PHONE_CELL >", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellGreaterThanOrEqualTo(String value) {
            addCriterion("PHONE_CELL >=", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellLessThan(String value) {
            addCriterion("PHONE_CELL <", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellLessThanOrEqualTo(String value) {
            addCriterion("PHONE_CELL <=", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellLike(String value) {
            addCriterion("PHONE_CELL like", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellNotLike(String value) {
            addCriterion("PHONE_CELL not like", value, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellIn(List<String> values) {
            addCriterion("PHONE_CELL in", values, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellNotIn(List<String> values) {
            addCriterion("PHONE_CELL not in", values, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellBetween(String value1, String value2) {
            addCriterion("PHONE_CELL between", value1, value2, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andPhoneCellNotBetween(String value1, String value2) {
            addCriterion("PHONE_CELL not between", value1, value2, "phoneCell");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnIsNull() {
            addCriterion("EMAIL_ADDR_BUSN is null");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnIsNotNull() {
            addCriterion("EMAIL_ADDR_BUSN is not null");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnEqualTo(String value) {
            addCriterion("EMAIL_ADDR_BUSN =", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnNotEqualTo(String value) {
            addCriterion("EMAIL_ADDR_BUSN <>", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnGreaterThan(String value) {
            addCriterion("EMAIL_ADDR_BUSN >", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnGreaterThanOrEqualTo(String value) {
            addCriterion("EMAIL_ADDR_BUSN >=", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnLessThan(String value) {
            addCriterion("EMAIL_ADDR_BUSN <", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnLessThanOrEqualTo(String value) {
            addCriterion("EMAIL_ADDR_BUSN <=", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnLike(String value) {
            addCriterion("EMAIL_ADDR_BUSN like", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnNotLike(String value) {
            addCriterion("EMAIL_ADDR_BUSN not like", value, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnIn(List<String> values) {
            addCriterion("EMAIL_ADDR_BUSN in", values, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnNotIn(List<String> values) {
            addCriterion("EMAIL_ADDR_BUSN not in", values, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnBetween(String value1, String value2) {
            addCriterion("EMAIL_ADDR_BUSN between", value1, value2, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andEmailAddrBusnNotBetween(String value1, String value2) {
            addCriterion("EMAIL_ADDR_BUSN not between", value1, value2, "emailAddrBusn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnIsNull() {
            addCriterion("START_DT_CHN is null");
            return (Criteria) this;
        }

        public Criteria andStartDtChnIsNotNull() {
            addCriterion("START_DT_CHN is not null");
            return (Criteria) this;
        }

        public Criteria andStartDtChnEqualTo(Date value) {
            addCriterion("START_DT_CHN =", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotEqualTo(Date value) {
            addCriterion("START_DT_CHN <>", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnGreaterThan(Date value) {
            addCriterion("START_DT_CHN >", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnGreaterThanOrEqualTo(Date value) {
            addCriterion("START_DT_CHN >=", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnLessThan(Date value) {
            addCriterion("START_DT_CHN <", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnLessThanOrEqualTo(Date value) {
            addCriterion("START_DT_CHN <=", value, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnIn(List<Date> values) {
            addCriterion("START_DT_CHN in", values, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotIn(List<Date> values) {
            addCriterion("START_DT_CHN not in", values, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnBetween(Date value1, Date value2) {
            addCriterion("START_DT_CHN between", value1, value2, "startDtChn");
            return (Criteria) this;
        }

        public Criteria andStartDtChnNotBetween(Date value1, Date value2) {
            addCriterion("START_DT_CHN not between", value1, value2, "startDtChn");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}