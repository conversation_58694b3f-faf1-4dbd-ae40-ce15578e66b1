package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockSelfServiceAssetsCategoryConfig {
    private Long id;

    private String ruleType;

    private String matchingFiled;

    private String matchingFiledDesr;

    private String matchingRelation;

    private String code;

    private Integer priority;

    private String jobType;

    private Integer status;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType == null ? null : ruleType.trim();
    }

    public String getMatchingFiled() {
        return matchingFiled;
    }

    public void setMatchingFiled(String matchingFiled) {
        this.matchingFiled = matchingFiled == null ? null : matchingFiled.trim();
    }

    public String getMatchingFiledDesr() {
        return matchingFiledDesr;
    }

    public void setMatchingFiledDesr(String matchingFiledDesr) {
        this.matchingFiledDesr = matchingFiledDesr == null ? null : matchingFiledDesr.trim();
    }

    public String getMatchingRelation() {
        return matchingRelation;
    }

    public void setMatchingRelation(String matchingRelation) {
        this.matchingRelation = matchingRelation == null ? null : matchingRelation.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType == null ? null : jobType.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}