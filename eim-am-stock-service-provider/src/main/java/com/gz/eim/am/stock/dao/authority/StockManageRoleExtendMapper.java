package com.gz.eim.am.stock.dao.authority;

import com.gz.eim.am.stock.entity.StockManageRole;
import com.gz.eim.am.stock.entity.StockRoleKeeper;


import java.util.List;

/**
 * @author: weijun<PERSON>e
 * @date: 2020/5/19
 * @description
 */
public interface StockManageRoleExtendMapper {


    /**
     * 批量生成角色关联数据
     * @param stockManageRoleList
     * @return
     */
    Boolean batchInsertStockManageRole(List<StockManageRole> stockManageRoleList);
}
