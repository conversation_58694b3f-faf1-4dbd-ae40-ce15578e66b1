package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockDeliveryDetailHistory {
    private Long deliveryDetailId;

    private Long deliveryId;

    private String suppliesCode;

    private Integer number;

    private Integer quality;

    private Integer realNumber;

    private String differenceReason;

    private String assetsCode;

    private String batchNo;

    private String snNo;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Date inventoryOutTime;

    private Date planOutTime;

    private String realWarehouseCode;

    private Integer isSend;

    private Integer status;

    private Long deliveryPlanLineId;

    public Long getDeliveryDetailId() {
        return deliveryDetailId;
    }

    public void setDeliveryDetailId(Long deliveryDetailId) {
        this.deliveryDetailId = deliveryDetailId;
    }

    public Long getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(Long deliveryId) {
        this.deliveryId = deliveryId;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode == null ? null : suppliesCode.trim();
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getQuality() {
        return quality;
    }

    public void setQuality(Integer quality) {
        this.quality = quality;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public String getDifferenceReason() {
        return differenceReason;
    }

    public void setDifferenceReason(String differenceReason) {
        this.differenceReason = differenceReason == null ? null : differenceReason.trim();
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo == null ? null : snNo.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getInventoryOutTime() {
        return inventoryOutTime;
    }

    public void setInventoryOutTime(Date inventoryOutTime) {
        this.inventoryOutTime = inventoryOutTime;
    }

    public Date getPlanOutTime() {
        return planOutTime;
    }

    public void setPlanOutTime(Date planOutTime) {
        this.planOutTime = planOutTime;
    }

    public String getRealWarehouseCode() {
        return realWarehouseCode;
    }

    public void setRealWarehouseCode(String realWarehouseCode) {
        this.realWarehouseCode = realWarehouseCode == null ? null : realWarehouseCode.trim();
    }

    public Integer getIsSend() {
        return isSend;
    }

    public void setIsSend(Integer isSend) {
        this.isSend = isSend;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getDeliveryPlanLineId() {
        return deliveryPlanLineId;
    }

    public void setDeliveryPlanLineId(Long deliveryPlanLineId) {
        this.deliveryPlanLineId = deliveryPlanLineId;
    }
}