package com.gz.eim.am.stock.service.impl.repair;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import com.gz.eim.am.base.dto.response.file.SysAttachDTO;
import com.gz.eim.am.pdm.api.external.SupplierDataApi;
import com.gz.eim.am.pdm.dto.response.supplier.SupplierBaseInfoRespDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.constant.WorkflowConstants;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineAssetReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairApproveReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairHeadReqDTO;
import com.gz.eim.am.stock.dto.response.repair.StockAssetsRepairHeadRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.handler.manager.StockAssetsRepairCheckRepairReqLineManager;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.assets.StockCommonAssetService;
import com.gz.eim.am.stock.service.file.StockFileCommonService;
import com.gz.eim.am.stock.service.inventory.plan.StockPlanAssetRemandService;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsReceiveService;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairHeadService;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairLineService;
import com.gz.eim.am.stock.service.wfl.WflSyncService;
import com.gz.eim.am.stock.util.common.ObjectUtils;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.common.SpringUtils;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @className: StockAssetsRepairServiceHelper
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/12/13
 **/
@Slf4j
@Component
public class StockAssetsRepairServiceHelper {

    @Autowired
    private StockPlanAssetRemandService stockPlanAssetRemandService;
    @Autowired
    private StockPlanAssetsReceiveService stockPlanAssetsReceiveService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private SupplierDataApi supplierDataApi;
    @Autowired
    private StockAssetsRepairHeadService stockAssetsRepairHeadService;
    @Autowired
    private StockAssetsRepairCheckRepairReqLineManager stockAssetsRepairCheckRepairLineManager;
    @Autowired
    private FileServiceApi fileServiceApi;
    @Autowired
    private WflSyncService wflSyncService;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockFileCommonService stockFileCommonService;
    @Autowired
    private StockAssetsRepairLineService stockAssetsRepairLineService;

    /**
     * @param: stockAssetsRepairApproveReqDTO, stockAssetsList
     * @description: 校验持有人审批公共数据
     * @return: String
     * @author: <EMAIL>
     * @date: 2022/12/12
     */
    public String checkAssetsHolderApproveCommonData(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO, Map<String, StockAssets> stockAssetsMap) {
        if (null == stockAssetsRepairApproveReqDTO) {
            return "请求参数不能为空";
        }
        Integer approveStatus = stockAssetsRepairApproveReqDTO.getApproveStatus();
        if (null == approveStatus) {
            return "审批状态不能为空";
        }
        if (!CommonEnum.approveStatusMap.containsKey(approveStatus)) {
            return "审批状态不正确";
        }
        String assetsCode = stockAssetsRepairApproveReqDTO.getAssetsCode();
        if (StringUtils.isBlank(assetsCode)) {
            return "旧资产编码不能为空";
        }
        String assetsHolder = stockAssetsRepairApproveReqDTO.getAssetsHolder();
        if (StringUtils.isBlank(assetsHolder)) {
            return "资产持有人不能为空";
        }
        List<String> assetsCodeList = new ArrayList<>(CommonConstant.NUMBER_TWO);
        assetsCodeList.add(assetsCode);
        String newAssetsCode = stockAssetsRepairApproveReqDTO.getNewAssetsCode();
        if (StringUtils.isNotBlank(newAssetsCode)) {
            assetsCodeList.add(newAssetsCode);
        }
        List<StockAssets> stockSearchAssetsList = stockAssetsService.selectAssets(null, assetsCodeList);
        if (CollectionUtils.isEmpty(stockSearchAssetsList)) {
            return "资产编码都不存在";
        }
        for (StockAssets stockAssets : stockSearchAssetsList) {
            stockAssetsMap.put(stockAssets.getAssetsCode(), stockAssets);
        }
        // 查询资产信息
        StockAssets stockAssets = stockAssetsMap.get(assetsCode);
        if (null == stockAssets) {
            return "旧资产编码不存在";
        }
        if (StringUtils.isNotBlank(newAssetsCode)) {
            StockAssets newStockAssets = stockAssetsMap.get(newAssetsCode);
            if (null == newStockAssets) {
                return "新资产编码不存在";
            }
            if (!AssetsEnum.statusType.IDLE.getValue().equals(newStockAssets.getStatus())) {
                return "新资产编码的使用状态不是在库，不能审批通过";
            }
        }
        return StringConstant.EMPTY;
    }

    /**
     * @param: stockAssetsRepairApproveReqDTO, stockAssetsList
     * @description: 校验并获取资产赔偿记录
     * @return: String
     * @author: <EMAIL>
     * @date: 2022/12/12
     */
    public String checkAndGetAssetsCompensation(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO, StockAssetsCompensationRecord stockAssetsCompensationRecord) throws Exception {
        Integer assetsRepairDealMethod = stockAssetsRepairApproveReqDTO.getAssetsRepairDealMethod();
        if (null == assetsRepairDealMethod) {
            return "处理方式不能为空";
        }
        if (!StockAssetsRepairApproveEnum.assetsRepairDealMethodMap.containsKey(assetsRepairDealMethod)) {
            return "处理方式不正确";
        }
        String compensationUser = stockAssetsRepairApproveReqDTO.getCompensationUser();
        if (StringUtils.isBlank(compensationUser)) {
            return "赔偿人员不能为空";
        }
        String assetsHolder = stockAssetsRepairApproveReqDTO.getAssetsHolder();
        if (!StringUtils.equals(compensationUser, assetsHolder)) {
            return "赔偿人员和持有人不一致";
        }
        BigDecimal compensationMoney = stockAssetsRepairApproveReqDTO.getCompensationMoney();
        if (null == compensationMoney) {
            return "赔偿金额不能为空";
        }
        if (compensationMoney.compareTo(BigDecimal.ZERO) <= CommonConstant.NUMBER_ZERO) {
            return "赔偿金额不能小于等于0";
        }
        Integer compensationMethod = stockAssetsRepairApproveReqDTO.getCompensationMethod();
        if (null == compensationMethod) {
            return "赔偿方式不能为空";
        }
        if (!AssetsCompensationEnum.compensationMethod.SALARY_COMPENSATION.getValue().equals(compensationMethod)
                && !AssetsCompensationEnum.compensationMethod.STAFF_REMIT.getValue().equals(compensationMethod)) {
            return "赔偿方式不正确";
        }
        String remark = stockAssetsRepairApproveReqDTO.getRemark();
        if (StringUtils.isNotBlank(remark) && remark.length() > CommonConstant.NUMBER_50) {
            return "其他说明长度不能大于50";
        }
        String formId = stockAssetsRepairApproveReqDTO.getFormId();
        if (StringUtils.isBlank(formId)) {
            return "流程id为空";
        }
        Date currentTime = new Date();
        String adminApprover = stockAssetsRepairApproveReqDTO.getAdminApprover();
        stockAssetsCompensationRecord.setAssetsCode(stockAssetsRepairApproveReqDTO.getAssetsCode());
        stockAssetsCompensationRecord.setAuditTime(currentTime);
        stockAssetsCompensationRecord.setAuditUser(adminApprover);
        stockAssetsCompensationRecord.setCompensationMoney(compensationMoney);
        stockAssetsCompensationRecord.setCompensationMethod(compensationMethod);
        stockAssetsCompensationRecord.setAdviseCompensationMoney(stockAssetsRepairApproveReqDTO.getLastCost());
        stockAssetsCompensationRecord.setIsRegistrationCompensation(CommonEnum.status.NO.getValue());
        stockAssetsCompensationRecord.setReturnWarehouseCode(stockAssetsRepairApproveReqDTO.getReturnWarehouseCode());
        stockAssetsCompensationRecord.setCompensationUser(compensationUser);
        stockAssetsCompensationRecord.setReturnTime(DateUtils.dateParse(DateUtils.dateFormat(currentTime, DateUtils.DATE_PATTERN), DateUtils.DATE_PATTERN));
        stockAssetsCompensationRecord.setReturnWarehouseCode(stockAssetsRepairApproveReqDTO.getReturnWarehouseCode());
        // 如果是员工回购设置为需要报废，是否归还为否
        if (StockAssetsRepairApproveEnum.AssetsRepairDealMethod.EMPLOYEE_BUY_BACK.getValue().equals(assetsRepairDealMethod)) {
            stockAssetsCompensationRecord.setIsReturn(CommonEnum.status.NO.getValue());
            stockAssetsCompensationRecord.setIsNeedScrap(CommonEnum.status.YES.getValue());
            stockAssetsCompensationRecord.setCompensationReason(AssetsCompensationEnum.compensationReason.EMPLOYEE_BUY_BACK.getValue());
        } else {
            stockAssetsCompensationRecord.setIsReturn(CommonEnum.status.YES.getValue());
            stockAssetsCompensationRecord.setIsNeedScrap(CommonEnum.status.NO.getValue());
            stockAssetsCompensationRecord.setCompensationReason(AssetsCompensationEnum.compensationReason.WORK_DAMAGE_COMPENSATION.getValue());
        }
        stockAssetsCompensationRecord.setSyncPaySystemStatus(AssetsCompensationEnum.syncPaySystemStatus.NO_SYNC.getValue());
        stockAssetsCompensationRecord.setDataSource(AssetsCompensationEnum.dataResource.REPAIR_APPROVE.getValue());
        stockAssetsCompensationRecord.setFormId(formId);
        stockAssetsCompensationRecord.setRemark(remark);
        stockAssetsCompensationRecord.setAssetsRepairDealMethod(assetsRepairDealMethod);
        stockAssetsCompensationRecord.setNewAssetsCode(stockAssetsRepairApproveReqDTO.getNewAssetsCode());
        stockAssetsCompensationRecord.setCreatedBy(adminApprover);
        stockAssetsCompensationRecord.setCreatedAt(currentTime);
        stockAssetsCompensationRecord.setUpdatedBy(adminApprover);
        stockAssetsCompensationRecord.setUpdatedAt(currentTime);
        return StringConstant.EMPTY;
    }

    /**
     * @param: assetsReturnRecordList
     * @description: 生成资产赔偿记录
     * @return:
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    public void createAssetsReturnInventoryRecordList(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO, StockAssets stockAssets) throws Exception {
        Date currentDate = new Date();
        String adminUser = stockAssetsRepairApproveReqDTO.getAdminApprover();
        String returnWarehouseCode = stockAssetsRepairApproveReqDTO.getReturnWarehouseCode();
        // 设置头单信息
        InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO = new InventoryInPlanHeadReqDTO();
        // 设置单据类型
        inventoryInPlanHeadReqDTO.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.ASSET_REMAND.getCode());
        inventoryInPlanHeadReqDTO.setPlanInTime(DateUtils.dateFormat(currentDate, DateUtils.DATE_PATTERN));
        inventoryInPlanHeadReqDTO.setBillingUser(adminUser);
        inventoryInPlanHeadReqDTO.setBillingTime(DateUtils.dateFormat(currentDate, DateUtils.DATE_TIME_PATTERN));
        // 设置归还原因
        inventoryInPlanHeadReqDTO.setReasonCode(InventoryInPlanHeadEnum.Reason.SERVICE_REMAND.getCode());
        inventoryInPlanHeadReqDTO.setInWarehouseCode(returnWarehouseCode);
        List<InventoryInPlanLineAssetReqDTO> inventoryInPlanLineAssetReqDTOList = new ArrayList<>(CommonConstant.NUMBER_ONE);
        InventoryInPlanLineAssetReqDTO inventoryInPlanLineAssetReqDTO = new InventoryInPlanLineAssetReqDTO();
        BeanUtils.copyProperties(stockAssets, inventoryInPlanLineAssetReqDTO);
        inventoryInPlanLineAssetReqDTOList.add(inventoryInPlanLineAssetReqDTO);
        inventoryInPlanHeadReqDTO.setInventoryInPlanLineAssetReqDTOS(inventoryInPlanLineAssetReqDTOList);
        // 设置资产情况
        inventoryInPlanLineAssetReqDTO.setAssetsCondition(InventoryInPlanLineAssetsEnum.Condition.DAMAGE.getCode());
        // 设置处理方式为入库待维修
        inventoryInPlanLineAssetReqDTO.setDealType(InventoryInPlanLineAssetsEnum.DealType.WAIT_REPAIR.getCode());
        inventoryInPlanHeadReqDTO.setInWarehouseCode(returnWarehouseCode);
        // 设置责任主体，故障原因为人为损坏，则默认个人；否则，默认公司
        // 设置责任人，如责任主体为个人，默认行政审批单中的赔偿人员；如责任主体为公司，默认为空
        if (StockAssetsRepairApproveEnum.DamageReason.ARTIFICIAL_DAMAGE.getValue().equals(stockAssetsRepairApproveReqDTO.getDamageReason())) {
            inventoryInPlanLineAssetReqDTO.setDutyBody(InventoryInPlanLineAssetsEnum.DutyBody.PERSONAL.getCode());
            inventoryInPlanLineAssetReqDTO.setPersonLiable(stockAssetsRepairApproveReqDTO.getCompensationUser());
            inventoryInPlanLineAssetReqDTO.setAmount(stockAssetsRepairApproveReqDTO.getCompensationMoney());
        } else {
            inventoryInPlanLineAssetReqDTO.setDutyBody(InventoryInPlanLineAssetsEnum.DutyBody.COMPANY.getCode());
        }
        // 设置备注
        inventoryInPlanLineAssetReqDTO.setRemark(stockAssetsRepairApproveReqDTO.getRemark());
        JwtUser user = new JwtUser();
        user.setEmployeeCode(adminUser);
        stockPlanAssetRemandService.savePlanAssetRemandByService(inventoryInPlanHeadReqDTO, user);
    }

    /**
     * @param: stockAssetsRepairApproveReqDTO, newStockAssets
     * @description: 生成资产领用出库单
     * @return: void
     * @author: <EMAIL>
     * @date: 2022/12/13
     */
    public void createAssetsReceiveDeliveryRecordList(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO, StockAssets newStockAssets) throws Exception {
        // 设置头信息
        DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO = new DeliveryPlanHeadReqDTO();
        deliveryPlanHeadReqDTO.setOutWarehouseCode(newStockAssets.getWarehouseCode());
        deliveryPlanHeadReqDTO.setUseUser(stockAssetsRepairApproveReqDTO.getAssetsHolder());
        deliveryPlanHeadReqDTO.setUseAddress(stockAssetsRepairApproveReqDTO.getUseAddress());
        deliveryPlanHeadReqDTO.setReasonCode(DeliveryPlanHeadEnum.Reason.BUSINESS_USE.getCode());
        deliveryPlanHeadReqDTO.setStatus(DeliveryPlanHeadEnum.Status.ALREADY_OUT.getCode());
        // 设置行信息
        List<DeliveryPlanLineReqDTO> deliveryPlanLineReqDTOList = new ArrayList<>(CommonConstant.NUMBER_ONE);
        DeliveryPlanLineReqDTO deliveryPlanLineReqDTO = new DeliveryPlanLineReqDTO();
        deliveryPlanLineReqDTOList.add(deliveryPlanLineReqDTO);
        deliveryPlanHeadReqDTO.setDeliveryPlanLineReqDTOS(deliveryPlanLineReqDTOList);
        deliveryPlanLineReqDTO.setSuppliesCode(newStockAssets.getSuppliesCode());
        deliveryPlanLineReqDTO.setAssetsCode(newStockAssets.getAssetsCode());
        deliveryPlanLineReqDTO.setRealNumber(CommonConstant.NUMBER_ONE);
        deliveryPlanLineReqDTO.setStatus(DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode());
        JwtUser user = new JwtUser();
        user.setEmployeeCode(stockAssetsRepairApproveReqDTO.getAdminApprover());
        stockPlanAssetsReceiveService.savePlanAssetsReceiveByService(deliveryPlanHeadReqDTO, user);
    }

    /**
     * @param: stockAssetsRepairHeadReqDTO, user
     * @param: stockAssetsRepairHead, insertStockAssetsRepairLineList
     * @param: updateStockAssetsRepairLineList, insertStockAssetsRepairLineExtendList
     * @param: updateStockAssetsRepairLineExtendList, updateStockAssetsList
     * @description: 检查请求信息的正确性
     * @return: String
     * @author: <EMAIL>
     * @date: 2022/12/15
     */
    public String checkRepairSaveOrSubmitParamAndGetData(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user,
                                                         StockAssetsRepairHead stockAssetsRepairHead, List<StockAssetsRepairLine> insertStockAssetsRepairLineList,
                                                         List<StockAssetsRepairLine> updateStockAssetsRepairLineList, List<StockAssetsRepairLineExtend> insertStockAssetsRepairLineExtendList,
                                                         List<StockAssetsRepairLineExtend> updateStockAssetsRepairLineExtendList, List<StockAssets> updateStockAssetsList) throws Exception {
        if (null == stockAssetsRepairHeadReqDTO) {
            return "请求参数不能为空";
        }
        Integer operationType = stockAssetsRepairHeadReqDTO.getOperationType();
        if (null == operationType) {
            return "操作类型不能为空";
        }
        if (!StockAssetsRepairHeadEnum.repairTypeMap.containsKey(operationType)) {
            return "操作类型不正确";
        }
        if (StockAssetsRepairHeadEnum.OperationType.SAVE.getValue().equals(stockAssetsRepairHeadReqDTO.getOperationType())) {
            stockAssetsRepairHead.setStatus(StockAssetsRepairHeadEnum.Status.SAVE.getValue());
        } else {
            stockAssetsRepairHead.setStatus(StockAssetsRepairHeadEnum.Status.IN_APPROVAL.getValue());
        }
        Integer repairType = stockAssetsRepairHeadReqDTO.getRepairType();
        if (null == repairType) {
            return "资产维修类型不能为空";
        }
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(repairType);
        if (null == repairTypeEnum) {
            return "资产维修类型不正确";
        }
        String billingUser = stockAssetsRepairHeadReqDTO.getBillingUser();
        if (StringUtils.isBlank(billingUser)) {
            return "制单人不能为空";
        }
        String employeeCode = user.getEmployeeCode();
        // 如果维修类型为资产维修，服务商编码不能为空
        if (StockAssetsRepairHeadEnum.RepairType.ASSETS_REPAIR.getValue().equals(repairType)) {
            String supplierCode = stockAssetsRepairHeadReqDTO.getSupplierCode();
            if (StringUtils.isBlank(supplierCode)) {
                return "维修类型为资产维修，服务商不能为空";
            }
            try {
                com.gz.eim.am.pdm.dto.ResponseData responseData = supplierDataApi.querySupplierBaseInfo(supplierCode);
                if (null == responseData) {
                    return "服务商信息不存在";
                }
                if (null == responseData.getData()) {
                    return "服务商信息不存在";
                }
            } catch (Exception e) {
                log.error("服务商查询异常，异常信息为：{}，请求参数为：supplierCode：{}", e, supplierCode);
                return "服务商校验失败，请重试";
            }
            stockAssetsRepairHead.setSupplierCode(supplierCode);
        }
        BigDecimal repairNumber = stockAssetsRepairHeadReqDTO.getRepairNumber();
        if (null == repairNumber) {
            return "维修数量不能为空";
        }
        if (repairNumber.compareTo(BigDecimal.ZERO) <= CommonConstant.NUMBER_ZERO) {
            return "维修数量不能小于等于0";
        }
        List<Object> stockAssetsRepairLineReqList = stockAssetsRepairHeadReqDTO.getStockAssetsRepairLineReqList();
        if (null == stockAssetsRepairLineReqList) {
            return "维修单行信息不能为空";
        }
        if (repairNumber.compareTo(BigDecimal.valueOf(stockAssetsRepairLineReqList.size())) != CommonConstant.NUMBER_ZERO) {
            return "维修数量和行数量无法匹配";
        }
        String remark = stockAssetsRepairHeadReqDTO.getRemark();
        if (StringUtils.isNotBlank(remark) && remark.length() > CommonConstant.NUMBER_200) {
            return "备注长度不能大于200";
        }
        if (StockAssetsRepairHeadEnum.RepairType.ASSETS_REPAIR.getValue().equals(repairType) && CollectionUtils.isEmpty(stockAssetsRepairHeadReqDTO.getAttachIdList())) {
            return "维修类型为资产维修时，附件为必输";
        }
        Date currentTime = new Date();
        Long id = stockAssetsRepairHeadReqDTO.getId();
        if (id != null) {
            StockAssetsRepairHeadReqDTO stockAssetsRepairHeadOldReqDTO = new StockAssetsRepairHeadReqDTO();
            stockAssetsRepairHeadOldReqDTO.setId(id);
            StockAssetsRepairHead stockAssetsRepairOldHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadOldReqDTO);
            if (null == stockAssetsRepairOldHead) {
                return "原始单据不存在";
            }
            // 如果不是保存状态直接报错
            if (!StockAssetsRepairHeadEnum.Status.SAVE.getValue().equals(stockAssetsRepairOldHead.getStatus())) {
                return "单据类型不是保存状态，不能保存或者提交";
            }
            stockAssetsRepairHead.setRepairNo(stockAssetsRepairOldHead.getRepairNo());
            stockAssetsRepairHead.setId(stockAssetsRepairOldHead.getId());
        } else {
            stockAssetsRepairHead.setRepairNo(OrderUtil.getOrderNo(OrderEnum.REPAIR_NO));
            stockAssetsRepairHead.setBillingUser(billingUser);
            stockAssetsRepairHead.setBillingDate(DateUtils.dateTimeToDate(currentTime));
            stockAssetsRepairHead.setCreatedAt(currentTime);
            stockAssetsRepairHead.setCreatedBy(employeeCode);
        }
        stockAssetsRepairHead.setRemark(remark);
        stockAssetsRepairHead.setRepairType(repairType);
        stockAssetsRepairHead.setRepairNumber(repairNumber);
        stockAssetsRepairHead.setUpdatedAt(currentTime);
        stockAssetsRepairHead.setUpdatedBy(employeeCode);
        return stockAssetsRepairCheckRepairLineManager.checkRepairSaveOrSubmitLineAndGetLineData(
                stockAssetsRepairHead, stockAssetsRepairLineReqList,
                repairTypeEnum, insertStockAssetsRepairLineList,
                updateStockAssetsRepairLineList, insertStockAssetsRepairLineExtendList,
                updateStockAssetsRepairLineExtendList, updateStockAssetsList);
    }

    /**
     * @param: repairNo, attachIdList
     * @description: TODO
     * @return: void
     * @author: <EMAIL>
     * @date: 2022/12/16
     */
    public void dealAttachList(String repairNo, List<Long> attachIdList) {
        List<SysAttachReqDTO> sysAttachReqDTOList = new ArrayList<>();
        // 查询原来的附件信息并删除
        QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO();
        queryFileReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSETS_REPAIR);
        queryFileReqDTO.setPageModule(FileConstant.ATTACH_MODULE_ASSETS_REPAIR_SAVE_OR_SUBMIT);
        queryFileReqDTO.setRelId(repairNo);
        try {
            com.fuu.eim.support.base.ResponseData<List<SysAttachDTO>> resp = fileServiceApi.getSysAttachDTOListSelective(queryFileReqDTO);
            //服务调用失败，保存操作整体失败
            if (ResponseCode.SYSTEM_ERROR.getCode().equals(resp.getCode())) {
                log.error("调用服务fileServiceApi.getSysAttachDTOListSelective失败，请求参数:{}，返回参数{}", JSON.toJSONString(queryFileReqDTO), JSON.toJSONString(resp));
                throw new RuntimeException(resp.getMessage());
            }
            List<SysAttachDTO> sysAttachDTOList = resp.getData();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(sysAttachDTOList)) {
                for (SysAttachDTO sysAttachDTO : sysAttachDTOList) {
                    sysAttachDTO.setDelFlag(CommonEnum.delFlag.YES.getValue());
                    SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO();
                    BeanUtils.copyProperties(sysAttachDTO, sysAttachReqDTO);
                    sysAttachReqDTOList.add(sysAttachReqDTO);
                }
            }
        } catch (Exception e) {
            log.error("调用服务fileServiceApi.getSysAttachDTOListSelective失败，请求参数:{}，异常信息{}", JSON.toJSONString(queryFileReqDTO), e);
            throw new RuntimeException(e.getMessage());
        }
        // 上传附件
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(attachIdList)) {
            for (Long attachId : attachIdList) {
                SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO();
                sysAttachReqDTO.setAttachId(attachId);
                sysAttachReqDTO.setRelId(repairNo);
                sysAttachReqDTO.setPageModule(FileConstant.ATTACH_MODULE_ASSETS_REPAIR_SAVE_OR_SUBMIT);
                sysAttachReqDTOList.add(sysAttachReqDTO);
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(sysAttachReqDTOList)) {
            //服务调用失败，保存操作整体失败
            try {
                com.fuu.eim.support.base.ResponseData resp = fileServiceApi.updateSysAttachList(sysAttachReqDTOList);
                if (ResponseCode.SYSTEM_ERROR.getCode().equals(resp.getCode())) {
                    log.error("调用服务fileServiceApi.updateSysAttachList，请求参数:{}，返回参数{}", JSON.toJSONString(sysAttachReqDTOList), JSON.toJSONString(resp));
                    throw new RuntimeException(resp.getMessage());
                }
            } catch (Exception e) {
                log.error("调用服务fileServiceApi.updateSysAttachList，请求参数:{}，异常信息{}", JSON.toJSONString(sysAttachReqDTOList), e);
                throw new RuntimeException(e.getMessage());
            }
        }
    }

    @Async
    public void repairSaveOrSubmitCallWfl(StockAssetsRepairHead stockAssetsRepairHead) {
        int n = 0;
        String repairNo = stockAssetsRepairHead.getRepairNo();
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO = new StockAssetsRepairHeadReqDTO();
        stockAssetsRepairHeadReqDTO.setRepairNo(repairNo);
        // 防止主线程的事务还没有提交，这里查不到单据
        do {
            log.info("Call Count:" + n);
            stockAssetsRepairHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadReqDTO);
            if (stockAssetsRepairHead != null) {
                break;
            }
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("sleep Exception:" + e.getMessage());
            }
            n++;
        } while (n <= 3);
        if (null == stockAssetsRepairHead) {
            log.error("StockAssetsRepairServiceHelper.callWfl没有查询到差旅申请的单据，头单单号为：{}", repairNo);
            return;
        }
        WflInfo wflInfo = new WflInfo();
        wflInfo.setApplyUser(stockAssetsRepairHead.getCreatedBy());
        wflInfo.setBizId(repairNo);
        wflInfo.setFlowCodeEnum(FlowCodeEnum.ASSETS_REPAIR);
        Map<String, Object> resultMap = wflSyncService.beginAct(wflInfo);
        if (null == resultMap || null == resultMap.get(WorkflowConstants.WFL_FLOW_NO)) {
            log.error("StockAssetsRepairServiceHelper.callWfl调用审批流失败，无法回更头单信息，头单单号为：{}", repairNo);
        } else {
            String flowNo = String.valueOf(resultMap.get(WorkflowConstants.WFL_FLOW_NO));
            stockAssetsRepairHead.setFormId(flowNo);
            stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        }
    }

    /**
     * @param: stockAssetsRepairHead
     * @description: 获取StockAssetsRepairHeadRespDTO
     * @return: StockAssetsRepairHeadRespDTO
     * @author: <EMAIL>
     * @date: 2022/12/19
     */
    public StockAssetsRepairHeadRespDTO getStockAssetsRepairHeadRespDTO(StockAssetsRepairHead stockAssetsRepairHead) throws Exception{
        StockAssetsRepairHeadRespDTO stockAssetsRepairHeadRespDTO = new StockAssetsRepairHeadRespDTO();
        BeanUtils.copyProperties(stockAssetsRepairHead, stockAssetsRepairHeadRespDTO);
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(stockAssetsRepairHead.getRepairType());
        if (repairTypeEnum != null) {
            stockAssetsRepairHeadRespDTO.setRepairTypeDesc(repairTypeEnum.getDesc());
        }
        StockAssetsRepairHeadEnum.Status statusEnum = StockAssetsRepairHeadEnum.statusMap.get(stockAssetsRepairHead.getStatus());
        if (statusEnum != null) {
            stockAssetsRepairHeadRespDTO.setStatusDesc(statusEnum.getDesc());
        }
        BigDecimal repairNumber = stockAssetsRepairHead.getRepairNumber();
        if (repairNumber != null) {
            stockAssetsRepairHeadRespDTO.setRepairNumber(repairNumber.setScale(CommonConstant.NUMBER_ZERO, BigDecimal.ROUND_HALF_UP));
        }
        BigDecimal repairTotalMoney = stockAssetsRepairHead.getRepairTotalMoney();
        if (repairTotalMoney != null) {
            stockAssetsRepairHeadRespDTO.setRepairTotalMoney(repairTotalMoney.setScale(CommonConstant.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
        }
        // 查询制单人
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockAssetsRepairHead.getBillingUser());
        if (sysUserBasicInfo != null) {
            stockAssetsRepairHeadRespDTO.setBillingUserName(sysUserBasicInfo.getName());
        }
        // 查询服务商
        String supplierCode = stockAssetsRepairHead.getSupplierCode();
        if (StringUtils.isNotBlank(supplierCode)) {
            try {
                com.gz.eim.am.pdm.dto.ResponseData<Map<String, String>> responseData = supplierDataApi.querySupplierBaseInfo(supplierCode);
                if (responseData != null) {
                    Map<String, String> supplierBaseInfoRespDTOByMap = responseData.getData();
                    if (supplierBaseInfoRespDTOByMap != null) {
                        SupplierBaseInfoRespDTO supplierBaseInfoRespDTO = ObjectUtils.mapToBean(supplierBaseInfoRespDTOByMap, SupplierBaseInfoRespDTO.class);
                        stockAssetsRepairHeadRespDTO.setSuppliesName(supplierBaseInfoRespDTO.getSupplierName());
                    }
                }
            } catch (Exception e) {
                log.error("服务商查询异常，异常信息为：{}，请求参数为：supplierCode：{}", e, supplierCode);
            }
        }

        Date billingDate = stockAssetsRepairHead.getBillingDate();
        if(billingDate != null){
            stockAssetsRepairHeadRespDTO.setBillingDate(DateUtils.dateFormat(billingDate, DateUtils.DATE_PATTERN));
        }
        // 获取附件信息
        QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO();
        queryFileReqDTO.setAttachModule(FileConstant.ATTACH_MODULE_ASSETS_REPAIR);
        queryFileReqDTO.setPageModule(FileConstant.ATTACH_MODULE_ASSETS_REPAIR_SAVE_OR_SUBMIT);
        queryFileReqDTO.setRelId(stockAssetsRepairHead.getRepairNo());
        List<SysAttachDTO> sysAttachDTOList = stockFileCommonService.selectFileAttachesByReqDTO(queryFileReqDTO);
        if (!CollectionUtils.isEmpty(sysAttachDTOList)) {
            stockAssetsRepairHeadRespDTO.setSysAttachDTOList(sysAttachDTOList);
        }
        return stockAssetsRepairHeadRespDTO;
    }

    /**
     * @param: stockAssetsRepairHeadReqDTO, user
     * @param: stockAssetsRepairHead, updateStockAssetsRepairLineExtendList
     * @description: 检查资产验收提交的参数并获取拼装数据
     * @return: String
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    public String checkRepairCheckSubmitParamAndGetData(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user,
                                                        StockAssetsRepairHead stockAssetsRepairHead, List<StockAssetsRepairLineExtend> updateStockAssetsRepairLineExtendList) {
        Long id = stockAssetsRepairHeadReqDTO.getId();
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadSearchReqDTO = new StockAssetsRepairHeadReqDTO();
        stockAssetsRepairHeadSearchReqDTO.setId(id);
        StockAssetsRepairHead stockAssetsRepairOldHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadSearchReqDTO);
        if (null == stockAssetsRepairOldHead) {
            return "维修头单不存在";
        }
        if (!StockAssetsRepairHeadEnum.Status.UNDER_REPAIR.getValue().equals(stockAssetsRepairOldHead.getStatus())) {
            return "维修头单状态不是维修中，不能提交验收";
        }
        stockAssetsRepairHead.setId(id);
        stockAssetsRepairHead.setRepairNo(stockAssetsRepairOldHead.getRepairNo());
        stockAssetsRepairHead.setStatus(StockAssetsRepairHeadEnum.Status.IN_CHECK.getValue());
        stockAssetsRepairHead.setFormId(stockAssetsRepairOldHead.getFormId());
        stockAssetsRepairHead.setUpdatedBy(user.getEmployeeCode());
        stockAssetsRepairHead.setUpdatedAt(new Date());
        List<Object> stockAssetsRepairLineReqList = stockAssetsRepairHeadReqDTO.getStockAssetsRepairLineReqList();
        if (CollectionUtils.isEmpty(stockAssetsRepairLineReqList)) {
            return "维修单行单信息不能为空";
        }

        return stockAssetsRepairCheckRepairLineManager.checkRepairCheckSubmitLineAndGetLineData(stockAssetsRepairHead, stockAssetsRepairLineReqList,
                StockAssetsRepairHeadEnum.repairTypeMap.get(stockAssetsRepairOldHead.getRepairType()), updateStockAssetsRepairLineExtendList);
    }

    @Async
    public void repairCheckSubmitCallWfl(StockAssetsRepairHead stockAssetsRepairHead) {
        String repairNo = stockAssetsRepairHead.getRepairNo();
        WflInfo wflInfo = new WflInfo();
        wflInfo.setApplyUser(stockAssetsRepairHead.getUpdatedBy());
        wflInfo.setBizId(repairNo);
        wflInfo.setFlowCodeEnum(FlowCodeEnum.ASSETS_REPAIR_CHECK);
        wflInfo.setReferenceFormId(stockAssetsRepairHead.getFormId());
        Map<String, Object> resultMap = wflSyncService.beginAct(wflInfo);
        if (null == resultMap || null == resultMap.get(WorkflowConstants.WFL_FLOW_NO)) {
            log.error("StockAssetsRepairServiceHelper.callWfl调用审批流失败，无法回更头单信息，头单单号为：{}", repairNo);
        } else {
            String flowNo = String.valueOf(resultMap.get(WorkflowConstants.WFL_FLOW_NO));
            stockAssetsRepairHead.setFormId(flowNo);
            stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        }
    }

    /**
     * @param: stockAssetsRepairHeadReqDTO, stockAssetsRepairHead
     * @param: updateStockAssetsRepairLineList
     * @description: 检查资产维修验收提交数据，并返回拼装数据
     * @return: String
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    public String checkRepairCheckApproveParamAndGetData(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, StockAssetsRepairHead stockAssetsRepairHead,
                                                         List<StockAssetsRepairLine> updateStockAssetsRepairLineList, List<StockAssets> updateAssetsList,
                                                         JwtUser user) {
        String employeeCode = user.getEmployeeCode();
        Long id = stockAssetsRepairHeadReqDTO.getId();
        // 查询头单信息
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadSearchReqDTO = new StockAssetsRepairHeadReqDTO();
        stockAssetsRepairHeadSearchReqDTO.setId(id);
        StockAssetsRepairHead stockAssetsRepairOldHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadSearchReqDTO);
        if(null == stockAssetsRepairOldHead){
            return "资产维修单不存在";
        }
        if(!StockAssetsRepairHeadEnum.Status.IN_CHECK.getValue().equals(stockAssetsRepairOldHead.getStatus())){
            return "资产维修单的单据状态不是验收中，无法验收审批通过";
        }
        stockAssetsRepairHead.setId(id);
        stockAssetsRepairHead.setStatus(StockAssetsRepairHeadEnum.Status.CHECK_COMPLETE.getValue());
        stockAssetsRepairHead.setRepairType(stockAssetsRepairOldHead.getRepairType());
        stockAssetsRepairHead.setUpdatedBy(employeeCode);
        stockAssetsRepairHead.setUpdatedAt(new Date());
        // 检验行信息
        return stockAssetsRepairCheckRepairLineManager.checkRepairCheckApproveParamLineListAndGetData(
                stockAssetsRepairHeadReqDTO.getStockAssetsRepairLineReqList(), stockAssetsRepairHead,
                updateStockAssetsRepairLineList, updateAssetsList, StockAssetsRepairHeadEnum.repairTypeMap.get(stockAssetsRepairOldHead.getRepairType()));
    }

    public int batchUpdateAssetsExtendListByAssetsList(List<StockAssets> updateAssetsList) {
        if(CollectionUtils.isEmpty(updateAssetsList)){
            return CommonConstant.NUMBER_ZERO;
        }
        Iterator<StockAssets> iterator = updateAssetsList.iterator();
        while (iterator.hasNext()){
            StockAssets stockAssets = iterator.next();
            if(StringUtils.isBlank(stockAssets.getExtRamMemory()) && StringUtils.isBlank(stockAssets.getExtHardDisk())){
                iterator.remove();
            }
        }
        if(CollectionUtils.isEmpty(updateAssetsList)){
            return CommonConstant.NUMBER_ZERO;
        }
        int count = CommonConstant.NUMBER_ZERO;
        Map<String, List<StockAssets>> updateAssetsMap = updateAssetsList.stream().collect(Collectors.groupingBy(StockAssets :: getCategory));
        for(Map.Entry<String, List<StockAssets>> entry : updateAssetsMap.entrySet()){
            String serviceName = AssetsEnum.assetsTypeServiceEnumMap.get(entry.getKey());
            if (StringUtils.isNotBlank(serviceName)) {
                StockCommonAssetService stockCommonAssetService = (StockCommonAssetService) SpringUtils.getBean(serviceName);
                count += stockCommonAssetService.batchUpdateAssetsExtendListByAssetsList(entry.getValue());
            }
        }
        return count;
    }
}
