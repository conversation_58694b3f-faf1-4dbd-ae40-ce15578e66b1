package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListLineReqDTO;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/11/21
 * @description
 */
public interface StockCheckResultService {

    /**
     * 盘点计划结束，定时调用
     * @return
     * @throws Exception
     */
    ResponseData takingPlanEnd() throws Exception;

    /**
     * 盘点异常清单初始化
     * @param assetQueryScopeReqDTO
     * @return
     * @throws Exception
     */
    ResponseData init(AssetQueryScopeReqDTO assetQueryScopeReqDTO) throws Exception;

    /**
     * 盘点差异清单保存
     * @param checkDifferenceListHeadReqDTO
     * @return
     * @throws Exception
     */
    ResponseData save(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception;

    /**
     * 盘点差异清单查询
     * @param assetQueryScopeReqDTO
     * @return
     * @throws Exception
     */
    ResponseData queryCheckResult(AssetQueryScopeReqDTO assetQueryScopeReqDTO) throws Exception;

    /**
     * 更加行id更新备注字段
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    ResponseData saveCheckResultRemark(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception;

    /**
     * 更加业务主键查询所有绑定的
     * @param takingPlanNo
     * @param assetsCode
     * @return
     */
    ResponseData getFileUrlListByRelId(String takingPlanNo, String assetsCode);
}
