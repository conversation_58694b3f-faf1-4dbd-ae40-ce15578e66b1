package com.gz.eim.am.stock.dao.manage;

import com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO;
import com.gz.eim.am.stock.entity.StockSuppliesConfig;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SuppliesConfigMapper {

    /**
     * 增加库存数
     * @param record
     * @return
     */
    int updateInventoryByKey(StockSuppliesConfig record);


    /**
     * 批量插入
     * @param configs
     * @return
     */
    int insertList(List<StockSuppliesConfig> configs);

    /**
     * 获取gps库存量明细
     * @param reqDTO
     * @return
     */
    List<SuppliesConfigRespDTO> getGpsConfigDetail(StockSuppliesQuantityReqDTO reqDTO);

    /**
     * 获取gps数量
     * @param reqDTO
     * @return
     */
    Long countGpsConfigDetail(StockSuppliesQuantityReqDTO reqDTO);
}
