package com.gz.eim.am.stock.dao.order;

import com.gz.eim.am.stock.dto.response.order.InventoryOutDetailRespDTO;
import com.gz.eim.am.stock.entity.StockDeliveryDetailHistory;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-11 下午 6:00
 */
public interface DeliveryDetailHistoryMapper {

    /**
     * 批量插入
     * @param histories
     * @return
     */
    int insertList(List<StockDeliveryDetailHistory> histories);
    /**
     * 根据出库单ID查询出库单详情
     * @param deliveryId
     * @return
     */
    List<InventoryOutDetailRespDTO> selectInventoryOutDetailRespDTOByDeliveryId(Long deliveryId);
}
