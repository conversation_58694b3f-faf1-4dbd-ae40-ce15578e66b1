package com.gz.eim.am.stock.service.impl.open;

import com.gz.eim.am.stock.dao.open.StockOpenMapper;
import com.gz.eim.am.stock.service.open.StockOpenService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/7/3
 * <p>
 *    健康检查service实现类
 * </p>
 */
@Service
public class StockOpenServiceImpl implements StockOpenService {

    @Resource
    private StockOpenMapper stockOpenMapper;

    @Override
    public void healthCheck() {
        stockOpenMapper.checkMysqlConnect();
    }
}
