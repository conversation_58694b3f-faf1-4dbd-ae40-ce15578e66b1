package com.gz.eim.am.stock.dao.order.plan;

import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadFastReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import com.gz.eim.am.stock.dto.response.order.plan.DeliveryPlanHeadRespDTO;
import com.gz.eim.am.stock.entity.StockDelivery;
import com.gz.eim.am.stock.entity.StockDeliveryPlanHead;
import com.gz.eim.am.stock.entity.vo.StockDeliveryPlanFastInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/12/10
 * @description:
 */
public interface DeliveryPlanHeadMapper {
    /**
     * 批量导入计划出库单
     * @param stockDeliveryPlanHeadList
     * @return
     */
    Boolean batchInsertStockDeliveryPlanHead(List<StockDeliveryPlanHead> stockDeliveryPlanHeadList);

    /**
     * 插入单条
     * @param stockDeliveryPlanHead
     * @return
     */
    int insert(StockDeliveryPlanHead stockDeliveryPlanHead);

    /**
     * 根据参数获取计划出库单
     * @param deliveryPlanSearchReqDTO
     * @return
     */
    Long selectCountByParam(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO);

    /**
     * 根据参数查询计划出库单
     * @param deliveryPlanSearchReqDTO
     * @return
     */
    List<DeliveryPlanHeadRespDTO> selectByPage(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO);

    /**
     * 获取计划出库单详情
     * @param deliveryPlanHeadId
     * @return
     */
    DeliveryPlanHeadRespDTO selectDeliveryPlanHeadByDeliveryPlanHeadId(Long deliveryPlanHeadId);

    /**
     * 获取快快速出库汇总
     * @param deliveryPlanHeadReqDTOList
     * @return
     */
    List<StockDeliveryPlanFastInfo> selectStockDeliveryPlanFastInfo(@Param ("deliveryPlanHeadReqDTOList") List<DeliveryPlanHeadReqDTO> deliveryPlanHeadReqDTOList);

    /**
     * 快速出库更新计划出库单
     * @param deliveryPlanHeadFastReqDTO
     */
    void updateHeadByFastOutBound(DeliveryPlanHeadFastReqDTO deliveryPlanHeadFastReqDTO);

    /**
     * 批量更新
     * @param stockDeliveryPlanHeadList
     */
    Long batchUpdate(List<StockDeliveryPlanHead> stockDeliveryPlanHeadList);

    /**
     * 查询出已出库未归还的执照借用申请单
     * @return
     */
    List<StockDeliveryPlanHead> selectOverdueReturn();
}
