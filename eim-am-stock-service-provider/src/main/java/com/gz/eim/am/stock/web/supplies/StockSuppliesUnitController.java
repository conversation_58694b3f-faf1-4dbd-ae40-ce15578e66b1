package com.gz.eim.am.stock.web.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.supplies.StockSuppliesUnitApi;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesUnitReqDTO;
import com.gz.eim.am.stock.entity.StockSuppliesUnit;
import com.gz.eim.am.stock.service.supplies.StockSuppliesUnitService;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * describe 物料单位
 * <AUTHOR>
 * @date 2019-12-11 AM 11:46
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/supplies/unit")
public class StockSuppliesUnitController implements StockSuppliesUnitApi {

    @Autowired
    private StockSuppliesUnitService unitService;

    /**
     * describe 查询物料单位
     * <AUTHOR>
     * @date 2020-05-11 10:48
     * @param unitReqDTO 请求参数
     * @return list
     **/
    @Override
    public ResponseData selectSuppliesUnit(SuppliesUnitReqDTO unitReqDTO) {
        return unitService.selectSuppliesUnit(unitReqDTO);
    }

}
