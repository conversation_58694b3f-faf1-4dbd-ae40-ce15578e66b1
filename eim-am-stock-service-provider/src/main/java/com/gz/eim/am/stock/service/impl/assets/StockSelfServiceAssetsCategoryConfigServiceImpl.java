package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.SuppliesConfigConstant;
import com.gz.eim.am.stock.dao.base.StockSelfServiceAssetsCategoryConfigMapper;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCategoryReqDTO;
import com.gz.eim.am.stock.dto.request.supplies.StockAssetsReceiveAssetsCategoryConfigReqDTO;
import com.gz.eim.am.stock.dto.request.supplies.StockSelfServiceAssetsCategoryConfigReqDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsCategoryRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsCategoryService;
import com.gz.eim.am.stock.service.assets.StockAssetsReceiveAssetsCategoryConfigService;
import com.gz.eim.am.stock.service.assets.StockSelfServiceAssetsCategoryConfigService;
import com.gz.eim.am.stock.service.supplies.StockSuppliesService;
import com.gz.eim.am.stock.util.em.FieldRuleEnum;
import com.gz.eim.am.stock.util.em.FieldRuleEnum.fieldRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.method.P;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.security.acl.Group;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.gz.eim.am.stock.util.em.FieldRuleEnum.fieldRelation.*;

/**
 * @className: StockSelfServiceSuppliesConfigServiceImpl
 * @description: 物料和部门或职级配置信息查询
 * @author: <EMAIL>
 * @date: 2021/10/11
 **/
@Slf4j
@Service
public class StockSelfServiceAssetsCategoryConfigServiceImpl implements StockSelfServiceAssetsCategoryConfigService {
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockSelfServiceAssetsCategoryConfigMapper stockSelfServiceAssetsCategoryConfigMapper;
    @Autowired
    private StockAssetsReceiveAssetsCategoryConfigService stockAssetsReceiveAssetsCategoryConfigService;
    @Autowired
    private StockSuppliesService stockSuppliesService;
    @Autowired
    private StockAssetsCategoryService stockAssetsCategoryService;
    /**
     * @param: stockSelfServiceAssetsCategoryConfigReqDTO
     * @description: 搜索资产类型，资产领用使用
     * @return:
     * @author: <EMAIL>
     * @date: 2021/9/8
     */
    @Override
    public ResponseData selectAssetsCategoryByAssetsReceive(StockSelfServiceAssetsCategoryConfigReqDTO stockSelfServiceAssetsCategoryConfigReqDTO) throws Exception {
        String empId = stockSelfServiceAssetsCategoryConfigReqDTO.getRealUseUser();
        // 如果实际领用人为空，就去取当前登陆人的工号
        if(StringUtils.isEmpty(empId)){
            JwtUser user = SecurityUtil.getJwtUser();
            empId = user.getEmployeeCode();
        }
        // 获取登陆人信息
        Map<String,String> sysUseInfoMap = ambaseCommonService.queryUserInfoMapByEmpId(empId);
        if(sysUseInfoMap.isEmpty()){
            return ResponseData.createFailResult("当前登陆人或实际使用人为无效人员");
        }
//        // 获取职位级别名称、部门code
//        String hpsJobcdDescr = sysUseInfoMap.get("hpsJobcdDescr");
//        String deptFullId = sysUseInfoMap.get("deptFullId");
        // 查询数据
        StockSelfServiceAssetsCategoryConfigExample stockSelfServiceAssetsCategoryConfigExample = new StockSelfServiceAssetsCategoryConfigExample();
        StockSelfServiceAssetsCategoryConfigExample.Criteria criteria = stockSelfServiceAssetsCategoryConfigExample.createCriteria();
        criteria.andStatusEqualTo(CommonConstant.NUMBER_ONE);
        criteria.andDelFlagEqualTo(CommonConstant.NUMBER_ZERO);
        List<StockSelfServiceAssetsCategoryConfig> stockSelfServiceSuppliesConfigList = stockSelfServiceAssetsCategoryConfigMapper.selectByExample(stockSelfServiceAssetsCategoryConfigExample);
        if(CollectionUtils.isEmpty(stockSelfServiceSuppliesConfigList)){
            return ResponseData.createSuccessResult(new ArrayList<>());
        }
        // 将数据groupBy
        Map<String, List<StockSelfServiceAssetsCategoryConfig>> stockSelfServiceSuppliesConfigMap = new TreeMap<>();
        for (StockSelfServiceAssetsCategoryConfig stockSelfServiceAssetsCategoryConfig : stockSelfServiceSuppliesConfigList) {
            String ruleType = stockSelfServiceAssetsCategoryConfig.getRuleType();
            List<StockSelfServiceAssetsCategoryConfig> tempStockSelfServiceAssetsCategoryConfigList = stockSelfServiceSuppliesConfigMap.get(ruleType);
            if(CollectionUtils.isEmpty(tempStockSelfServiceAssetsCategoryConfigList)){
                tempStockSelfServiceAssetsCategoryConfigList = new ArrayList<>();
                stockSelfServiceSuppliesConfigMap.put(ruleType, tempStockSelfServiceAssetsCategoryConfigList);
            }
            tempStockSelfServiceAssetsCategoryConfigList.add(stockSelfServiceAssetsCategoryConfig);
        }

        // 用于存储职位类型编码
        Set<String> jobTypeList = new HashSet<>();
        // 遍历分组之后的数据，匹配每一条，如果成功直接返回
        ruleLoop:
        for(Map.Entry<String, List<StockSelfServiceAssetsCategoryConfig>> ruleEntry : stockSelfServiceSuppliesConfigMap.entrySet()){
            Map<Integer, List<StockSelfServiceAssetsCategoryConfig>> priorityMap = ruleEntry.getValue().stream().collect(Collectors.groupingBy(StockSelfServiceAssetsCategoryConfig::getPriority));
            //map排序
            priorityMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(e -> priorityMap.put(e.getKey(),e.getValue()));
            for(Map.Entry<Integer,List<StockSelfServiceAssetsCategoryConfig>> priorityEntry: priorityMap.entrySet()){
                List<StockSelfServiceAssetsCategoryConfig> stockSelfServiceAssetsCategoryConfigList = priorityEntry.getValue();
                //匹配条数
                int matchCount = 0;
                for(int i=0,len=stockSelfServiceAssetsCategoryConfigList.size();i<len;i++){
                    StockSelfServiceAssetsCategoryConfig stockSelfServiceAssetsCategoryConfig = stockSelfServiceAssetsCategoryConfigList.get(i);
                    String matchFiled = sysUseInfoMap.get(stockSelfServiceAssetsCategoryConfig.getMatchingFiled());
                    if(StringUtils.isNotBlank(matchFiled)){
                        fieldRelation fieldRelationEnum = getFieldRelationEnum(stockSelfServiceAssetsCategoryConfig.getMatchingRelation());
                        switch (fieldRelationEnum)
                        {
                            case EQUAL:
                                if(matchFiled.equals(stockSelfServiceAssetsCategoryConfig.getCode())){
                                    matchCount++;
                                    jobTypeList.add(stockSelfServiceAssetsCategoryConfig.getJobType());
                                }else {
                                    jobTypeList.clear();
                                }
                                break;
                            case NO_EQUAL:
                                if(!matchFiled.equals(stockSelfServiceAssetsCategoryConfig.getCode())){
                                    matchCount++;
                                    jobTypeList.add(stockSelfServiceAssetsCategoryConfig.getJobType());
                                }else {
                                    jobTypeList.clear();
                                }
                                break;
                            case LIKE:
                                if(matchFiled.contains(stockSelfServiceAssetsCategoryConfig.getCode())){
                                    matchCount++;
                                    jobTypeList.add(stockSelfServiceAssetsCategoryConfig.getJobType());
                                }else {
                                    jobTypeList.clear();
                                }
                                break;
                            case NO_LIKE:
                                if(!matchFiled.contains(stockSelfServiceAssetsCategoryConfig.getCode())){
                                    matchCount++;
                                    jobTypeList.add(stockSelfServiceAssetsCategoryConfig.getJobType());
                                }else {
                                    jobTypeList.clear();
                                }
                                break;
                            default:
                        }

                      //必须条数完全匹配
                      if(matchCount != 0 && i == stockSelfServiceAssetsCategoryConfigList.size()-1 && matchCount == stockSelfServiceAssetsCategoryConfigList.size()){
                          break ruleLoop;
                      }

                    }else {
                        break;
                    }


                }
            }





//            for (StockSelfServiceAssetsCategoryConfig stockSelfServiceSuppliesConfig : entry.getValue()) {
//                String matchingFiled = stockSelfServiceSuppliesConfig.getMatchingFiled();
//                String code = stockSelfServiceSuppliesConfig.getCode();
//                String jobType = stockSelfServiceSuppliesConfig.getJobType();
//                if(SuppliesConfigConstant.DEPARTMENT.equals(matchingFiled) && StringUtils.isNotEmpty(deptFullId)){
//                    if(deptFullId.contains(code)){
//                        jobTypeList.add(jobType);
//                        break;
//                    }
//                }else if(SuppliesConfigConstant.JOB.equals(matchingFiled) && StringUtils.isNotEmpty(hpsJobcdDescr)){
//                    if(hpsJobcdDescr.contains(code)){
//                        jobTypeList.add(jobType);
//                        break;
//                    }
//                }else if(SuppliesConfigConstant.DEFAULT.equals(matchingFiled)){
//                    if(SuppliesConfigConstant.DEFAULT_CODE.equals(code)){
//                        jobTypeList.add(jobType);
//                        break;
//                    }
//                }
//            }
            if(!CollectionUtils.isEmpty(jobTypeList)){
                break;
            }
        }
        // 如果没有找到任何配置
        if(CollectionUtils.isEmpty(jobTypeList)){
            return ResponseData.createSuccessResult(new ArrayList<>());
        }
        // 根据职位类型查询出来所有的资产分类规则
        StockAssetsReceiveAssetsCategoryConfigReqDTO stockAssetsReceiveAssetsCategoryConfigReqDTO = new StockAssetsReceiveAssetsCategoryConfigReqDTO();
        stockAssetsReceiveAssetsCategoryConfigReqDTO.setJobTypeList(new ArrayList<>(jobTypeList));
        List<StockAssetsReceiveAssetsCategoryConfig> stockAssetsReceiveAssetsCategoryConfigList = stockAssetsReceiveAssetsCategoryConfigService.getStockAssetsReceiveAssetsCategoryConfigList(stockAssetsReceiveAssetsCategoryConfigReqDTO);
        // 如果没有找到任何配置
        if(CollectionUtils.isEmpty(stockAssetsReceiveAssetsCategoryConfigList)){
            return ResponseData.createSuccessResult(new ArrayList<>());
        }
        // 获取所有的资产分类编码
        List<String> assetsCategoryCodeList = stockAssetsReceiveAssetsCategoryConfigList.stream().map(StockAssetsReceiveAssetsCategoryConfig :: getAssetsCategoryCode).distinct().collect(Collectors.toList());
        // 获取对应的资产分类
        StockAssetsCategoryReqDTO stockAssetsCategoryReqDTO = new StockAssetsCategoryReqDTO();
        stockAssetsCategoryReqDTO.setCategoryCodeList(assetsCategoryCodeList);
        stockAssetsCategoryReqDTO.setParam(stockSelfServiceAssetsCategoryConfigReqDTO.getParam());
        stockAssetsCategoryReqDTO.setStatus(CommonConstant.NUMBER_ONE);
        stockAssetsCategoryReqDTO.setDelFlag(CommonConstant.NUMBER_ZERO);
        List<StockAssetsCategory> stockAssetsCategoryList = stockAssetsCategoryService.selectAssetsCategoryByDTO(stockAssetsCategoryReqDTO);
        // 使用返回类型返回资产分类集合
        List<StockAssetsCategoryRespDTO> stockAssetsCategoryRespDTOList = ConvertUtil.convertToType(StockAssetsCategoryRespDTO.class, stockAssetsCategoryList);
        return ResponseData.createSuccessResult(stockAssetsCategoryRespDTOList);
    }

}
