package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockAssetsCategoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockAssetsCategoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeIsNull() {
            addCriterion("parent_category_code is null");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeIsNotNull() {
            addCriterion("parent_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeEqualTo(String value) {
            addCriterion("parent_category_code =", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeNotEqualTo(String value) {
            addCriterion("parent_category_code <>", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeGreaterThan(String value) {
            addCriterion("parent_category_code >", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("parent_category_code >=", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeLessThan(String value) {
            addCriterion("parent_category_code <", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeLessThanOrEqualTo(String value) {
            addCriterion("parent_category_code <=", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeLike(String value) {
            addCriterion("parent_category_code like", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeNotLike(String value) {
            addCriterion("parent_category_code not like", value, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeIn(List<String> values) {
            addCriterion("parent_category_code in", values, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeNotIn(List<String> values) {
            addCriterion("parent_category_code not in", values, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeBetween(String value1, String value2) {
            addCriterion("parent_category_code between", value1, value2, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryCodeNotBetween(String value1, String value2) {
            addCriterion("parent_category_code not between", value1, value2, "parentCategoryCode");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameIsNull() {
            addCriterion("parent_category_name is null");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameIsNotNull() {
            addCriterion("parent_category_name is not null");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameEqualTo(String value) {
            addCriterion("parent_category_name =", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameNotEqualTo(String value) {
            addCriterion("parent_category_name <>", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameGreaterThan(String value) {
            addCriterion("parent_category_name >", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("parent_category_name >=", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameLessThan(String value) {
            addCriterion("parent_category_name <", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("parent_category_name <=", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameLike(String value) {
            addCriterion("parent_category_name like", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameNotLike(String value) {
            addCriterion("parent_category_name not like", value, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameIn(List<String> values) {
            addCriterion("parent_category_name in", values, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameNotIn(List<String> values) {
            addCriterion("parent_category_name not in", values, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameBetween(String value1, String value2) {
            addCriterion("parent_category_name between", value1, value2, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andParentCategoryNameNotBetween(String value1, String value2) {
            addCriterion("parent_category_name not between", value1, value2, "parentCategoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNull() {
            addCriterion("category_code is null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNotNull() {
            addCriterion("category_code is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeEqualTo(String value) {
            addCriterion("category_code =", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotEqualTo(String value) {
            addCriterion("category_code <>", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThan(String value) {
            addCriterion("category_code >", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("category_code >=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThan(String value) {
            addCriterion("category_code <", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThanOrEqualTo(String value) {
            addCriterion("category_code <=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLike(String value) {
            addCriterion("category_code like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotLike(String value) {
            addCriterion("category_code not like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIn(List<String> values) {
            addCriterion("category_code in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotIn(List<String> values) {
            addCriterion("category_code not in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeBetween(String value1, String value2) {
            addCriterion("category_code between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotBetween(String value1, String value2) {
            addCriterion("category_code not between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeIsNull() {
            addCriterion("Default_used_time is null");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeIsNotNull() {
            addCriterion("Default_used_time is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeEqualTo(Integer value) {
            addCriterion("Default_used_time =", value, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeNotEqualTo(Integer value) {
            addCriterion("Default_used_time <>", value, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeGreaterThan(Integer value) {
            addCriterion("Default_used_time >", value, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("Default_used_time >=", value, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeLessThan(Integer value) {
            addCriterion("Default_used_time <", value, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeLessThanOrEqualTo(Integer value) {
            addCriterion("Default_used_time <=", value, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeIn(List<Integer> values) {
            addCriterion("Default_used_time in", values, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeNotIn(List<Integer> values) {
            addCriterion("Default_used_time not in", values, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeBetween(Integer value1, Integer value2) {
            addCriterion("Default_used_time between", value1, value2, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("Default_used_time not between", value1, value2, "defaultUsedTime");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostIsNull() {
            addCriterion("Default_used_cost is null");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostIsNotNull() {
            addCriterion("Default_used_cost is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostEqualTo(BigDecimal value) {
            addCriterion("Default_used_cost =", value, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostNotEqualTo(BigDecimal value) {
            addCriterion("Default_used_cost <>", value, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostGreaterThan(BigDecimal value) {
            addCriterion("Default_used_cost >", value, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("Default_used_cost >=", value, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostLessThan(BigDecimal value) {
            addCriterion("Default_used_cost <", value, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("Default_used_cost <=", value, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostIn(List<BigDecimal> values) {
            addCriterion("Default_used_cost in", values, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostNotIn(List<BigDecimal> values) {
            addCriterion("Default_used_cost not in", values, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Default_used_cost between", value1, value2, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andDefaultUsedCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("Default_used_cost not between", value1, value2, "defaultUsedCost");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagIsNull() {
            addCriterion("sync_ebs_flag is null");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagIsNotNull() {
            addCriterion("sync_ebs_flag is not null");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagEqualTo(Integer value) {
            addCriterion("sync_ebs_flag =", value, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagNotEqualTo(Integer value) {
            addCriterion("sync_ebs_flag <>", value, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagGreaterThan(Integer value) {
            addCriterion("sync_ebs_flag >", value, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("sync_ebs_flag >=", value, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagLessThan(Integer value) {
            addCriterion("sync_ebs_flag <", value, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagLessThanOrEqualTo(Integer value) {
            addCriterion("sync_ebs_flag <=", value, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagIn(List<Integer> values) {
            addCriterion("sync_ebs_flag in", values, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagNotIn(List<Integer> values) {
            addCriterion("sync_ebs_flag not in", values, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagBetween(Integer value1, Integer value2) {
            addCriterion("sync_ebs_flag between", value1, value2, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andSyncEbsFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("sync_ebs_flag not between", value1, value2, "syncEbsFlag");
            return (Criteria) this;
        }

        public Criteria andLowestAmountIsNull() {
            addCriterion("lowest_amount is null");
            return (Criteria) this;
        }

        public Criteria andLowestAmountIsNotNull() {
            addCriterion("lowest_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLowestAmountEqualTo(BigDecimal value) {
            addCriterion("lowest_amount =", value, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountNotEqualTo(BigDecimal value) {
            addCriterion("lowest_amount <>", value, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountGreaterThan(BigDecimal value) {
            addCriterion("lowest_amount >", value, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("lowest_amount >=", value, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountLessThan(BigDecimal value) {
            addCriterion("lowest_amount <", value, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("lowest_amount <=", value, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountIn(List<BigDecimal> values) {
            addCriterion("lowest_amount in", values, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountNotIn(List<BigDecimal> values) {
            addCriterion("lowest_amount not in", values, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lowest_amount between", value1, value2, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andLowestAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lowest_amount not between", value1, value2, "lowestAmount");
            return (Criteria) this;
        }

        public Criteria andDelFlagIsNull() {
            addCriterion("del_flag is null");
            return (Criteria) this;
        }

        public Criteria andDelFlagIsNotNull() {
            addCriterion("del_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDelFlagEqualTo(Integer value) {
            addCriterion("del_flag =", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotEqualTo(Integer value) {
            addCriterion("del_flag <>", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagGreaterThan(Integer value) {
            addCriterion("del_flag >", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("del_flag >=", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagLessThan(Integer value) {
            addCriterion("del_flag <", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagLessThanOrEqualTo(Integer value) {
            addCriterion("del_flag <=", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagIn(List<Integer> values) {
            addCriterion("del_flag in", values, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotIn(List<Integer> values) {
            addCriterion("del_flag not in", values, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagBetween(Integer value1, Integer value2) {
            addCriterion("del_flag between", value1, value2, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("del_flag not between", value1, value2, "delFlag");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andPictureUrlIsNull() {
            addCriterion("picture_url is null");
            return (Criteria) this;
        }

        public Criteria andPictureUrlIsNotNull() {
            addCriterion("picture_url is not null");
            return (Criteria) this;
        }

        public Criteria andPictureUrlEqualTo(String value) {
            addCriterion("picture_url =", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotEqualTo(String value) {
            addCriterion("picture_url <>", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlGreaterThan(String value) {
            addCriterion("picture_url >", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlGreaterThanOrEqualTo(String value) {
            addCriterion("picture_url >=", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlLessThan(String value) {
            addCriterion("picture_url <", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlLessThanOrEqualTo(String value) {
            addCriterion("picture_url <=", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlLike(String value) {
            addCriterion("picture_url like", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotLike(String value) {
            addCriterion("picture_url not like", value, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlIn(List<String> values) {
            addCriterion("picture_url in", values, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotIn(List<String> values) {
            addCriterion("picture_url not in", values, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlBetween(String value1, String value2) {
            addCriterion("picture_url between", value1, value2, "pictureUrl");
            return (Criteria) this;
        }

        public Criteria andPictureUrlNotBetween(String value1, String value2) {
            addCriterion("picture_url not between", value1, value2, "pictureUrl");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}