package com.gz.eim.am.stock.service.check;

import com.gz.eim.am.stock.entity.StockAssetsCheckTaskAssignPeople;
import com.gz.eim.am.stock.entity.StockAssetsCheckTaskAssignPeopleReqDO;

import java.util.List;

/**
 * @className: StockAssetsCheckTaskAssignPeopleService
 * @description: 资产盘点任务分配Service
 * @author: <EMAIL>
 * @date: 2023/12/8
 **/
public interface StockAssetsCheckTaskAssignPeopleService {
    /**
     * @param: stockAssetsCheckTaskAssignPeopleReqDO
     * @description: 查询资产盘点任务分发人员集合
     * @return: List<StockAssetsCheckTaskAssignPeople>
     * @author: <EMAIL>
     * @date: 2023/12/8
     */
    List<StockAssetsCheckTaskAssignPeople> selectList(StockAssetsCheckTaskAssignPeopleReqDO stockAssetsCheckTaskAssignPeopleReqDO);
    /**
     * @param: stockAssetsCheckTaskAssignPeopleReqDO
     * @description: 查询资产盘点任务分发人员
     * @return: StockAssetsCheckTaskAssignPeople
     * @author: <EMAIL>
     * @date: 2023/12/8
     */
    StockAssetsCheckTaskAssignPeople selectOne(StockAssetsCheckTaskAssignPeopleReqDO stockAssetsCheckTaskAssignPeopleReqDO);
    /**
     * @param: stockAssetsCheckTaskAssignPeople
     * @description: 插入资产盘点任务分发人员
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/12/8
     */
    int insertOne(StockAssetsCheckTaskAssignPeople stockAssetsCheckTaskAssignPeople);
    /**
     * @param: stockAssetsCheckTaskAssignPeopleList
     * @description: 批量插入资产盘点任务分发人员
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/12/8
     */
    int batchInsert(List<StockAssetsCheckTaskAssignPeople> stockAssetsCheckTaskAssignPeopleList);
}
