package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSequence;
import com.gz.eim.am.stock.entity.StockSequenceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSequenceMapper {
    long countByExample(StockSequenceExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StockSequence record);

    int insertSelective(StockSequence record);

    List<StockSequence> selectByExample(StockSequenceExample example);

    StockSequence selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StockSequence record, @Param("example") StockSequenceExample example);

    int updateByExample(@Param("record") StockSequence record, @Param("example") StockSequenceExample example);

    int updateByPrimaryKeySelective(StockSequence record);

    int updateByPrimaryKey(StockSequence record);
}