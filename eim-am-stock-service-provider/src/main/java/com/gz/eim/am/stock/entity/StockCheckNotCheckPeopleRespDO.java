package com.gz.eim.am.stock.entity;

import lombok.Data;

import java.util.Date;

/**
 * @className: StockCheckNotCheckPeopleRespDO
 * @description: 未盘点人响应类
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Data
public class StockCheckNotCheckPeopleRespDO {
    /**
     * 盘点人
     */
    private String checkPeople;
    /**
     * 盘点email
     */
    private String checkPeopleEmail;
    /**
     * 计划单号
     */
    private String takingPlanNo;
    /**
     * 计划名称
     */
    private String takingPlanName;
    /**
     * 计划结束时间
     */
    private String supervisorId;
    /**
     * 计划结束时间
     */
    private Date takingDeadlineDate;
    /**
     * 盘点人手机号
     */
    private String checkPeoplePhone;
    /**
     * 任务结束时间
     */
    private Date taskLastTime;

}
