package com.gz.eim.am.stock.service.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesUnitReqDTO;
import com.gz.eim.am.stock.entity.StockSuppliesUnit;

import java.util.List;

import org.bouncycastle.cert.ocsp.RespData;

//import sun.util.resources.ga.LocaleNames_ga;

/**
 * 物料单位
 * <AUTHOR>
 * @date 2019-12-11 AM 10:30
 */
public interface StockSuppliesUnitService {

    /**
     * 获取所有的物料单位
     * @param unitReqDTO
     * @return
     */
    ResponseData selectSuppliesUnit(final SuppliesUnitReqDTO unitReqDTO);


    /**
     * 查询单位列表
     * @param unitReqDTO
     * @return
     */
    List<StockSuppliesUnit> selectSuppliesUnitList(final SuppliesUnitReqDTO unitReqDTO);

    /**
     * 根据code获取物料单位
     * @param code
     * @return
     */
    StockSuppliesUnit selectSuppliesUnitByCode(final String code);

    /**
     * 根据id获取物料单位
     * @param id
     * @return
     */
    StockSuppliesUnit selectSuppliesUnitById(Long id);

    /**
     * 根据ids或codes获取物料单位
     * @param ids
     * @param codes
     * @return
     */
    List<StockSuppliesUnit> selectSuppliesUnit(List<Long> ids, List<String> codes);
}
