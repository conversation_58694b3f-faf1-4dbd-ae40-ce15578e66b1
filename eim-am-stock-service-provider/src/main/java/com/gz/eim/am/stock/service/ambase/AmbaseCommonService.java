package com.gz.eim.am.stock.service.ambase;

import com.gz.eim.am.base.dto.response.file.CategoryPictureMetaRespDTO;
import com.gz.eim.am.stock.dto.request.ambase.CuxGetfadetailTblReqDTO;
import com.gz.eim.am.stock.dto.response.address.AddressProvinceRespDTO;
import com.gz.eim.am.stock.dto.response.assets.AssetsCostRespDTO;
import com.gz.eim.am.stock.entity.PsBusLocationTblDO;
import com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTbl;
import com.gz.eim.am.stock.entity.ambase.SysDept;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.ambase.*;

import java.util.List;
import java.util.Map;

/**
 * ambase数据库查询操作服务
 * <AUTHOR>
 * @date 2019-12-26 PM 3:28
 */
public interface AmbaseCommonService {

    /**
     * 查询员工集合
     * @param sysUserReqDO
     * @return
     */
    List<SysUser> selectUserList(SysUserReqDO sysUserReqDO);

    /**
     * 查询员工信息
     * @param sysUserReqDO
     * @return
     */
    SysUser selectUserOne(SysUserReqDO sysUserReqDO);

    /**
     * 根据ids 查询部门
     * @param deptIds
     * @return
     */
    List<SysDept> selectDeptByIds(List<String> deptIds);

    /**
     * 根据ids 查询员工
     * @param empIds
     * @return
     */
    List<SysUser> selectUsersByIds(List<String> empIds);

    /**
     * 根据员工编号查询基本信息
     * @param empId
     * @return
     */
    SysUserBasicInfo queryUserBasicInfoByEmpId(final String empId);

    /**
     * 根据员工编号查询基本信息
     * @param empId
     * @return
     */
    Map<String,String> queryUserInfoMapByEmpId(final String empId);

    /**
     * 根据ids获取员工Map
     * @param empIds
     * @return
     */
    Map<String, SysUser> selectSysUserMapByIds(List<String> empIds);

    /**
     * 根据ids获取部门Map
     * @param deptIds
     * @return
     */
    Map<String, SysDept> selectSysDeptMapByIds(List<String> deptIds);

    /**
     * 根据JobCode获取职级
     * @param JobCodes
     * @return
     */
    List<PsBusJobCodeTbl> selectJobCodesByCodes(List<String> JobCodes);

    /**
     * 根据jobCodes获取职级Map
     * @param jobCodes
     * @return
     */
    Map<String, PsBusJobCodeTbl> selectJobCodesMapByCodes(List<String> jobCodes);

    /**
     * 根据bizNo查询工作流记录表
     * @param bizNo
     * @return
     */
    List<SysApvTask> selectApvTastBybizNo(String bizNo);

    /**
     * 根据资产编码查询资产残值
     * @param assetsCodes
     * @return
     */
    List<AssetsCostRespDTO> selectCostByAssetsCode(List<String> assetsCodes);

    /**
     * 从字典表查询数据
     * @param code
     * @param values
     * @return
     */
    Map<String,SysDict> selectDictListByValue(String code,List<String> values);

    /**
     * 获取枚举枚举配置
     * @param mdKey
     * @param docType
     * @return
     */
    List<Integer> selectMetaDataByKey(String mdKey,Integer docType);

    /**
     * 根据公司编码批量获取公司名称
     * @param companyCodes
     * @return
     */
    Map<String,SysCompanyInfo> getCompanysByCodes(List<String> companyCodes);
     /**
     * 根据ids 查询员工邮箱
     * @param empIds
     * @return
     */
    List<String> selectUsersEmailByIds(List<String> empIds);

    /**
     * 根据仓库类型查询出该仓库类型下对应单据的值
     * @param mdkey
     * @return
     */
    Map<String,Integer> selectMetaDataByParam(String mdkey);

    /**
     * 获取仓库类型+接口类型下的对应单据的值
     * @param warehouseType
     * @return
     */
    Integer getStockBillTypeValue(String interfaceTypeDesc,Integer warehouseType);

    /**
     * 获取资产分类上传图片配置数据
     * @param mdKey 资产分类
     * @return
     */
    CategoryPictureMetaRespDTO selectMetaDataByKey(String mdKey);


    /**
     * 获取枚举枚举配置
     * @param mdKey
     * @param module
     * @return
     */
    List<String> selectMetaDataByMdKey(String module,String mdKey);

    /**
     * 查询部门下所有员工的邮箱和工号
     * @param deptIds
     * @return
     */
    List<SysUserBasicInfo> selectUserEmailsAndEmpIdByDeptIds(List<String> deptIds,List<Integer> empTypeList);

    /**
     * 查询地址表全量的地址信息
     * @return
     */
    List<AddressProvinceRespDTO> queryAllAddress();

    /**
     * 查询员工的地址
     * @param empId
     * @return
     */
    Map<String,String> queryEmployAddress(String empId);

    /**
     * 根据编码查询位置名称
     * @param locations
     * @return
     */
    Map<String,String> queryAddressName(List<String> locations);

    /**
     * 获取部门以及部门下所有的子部门集合
     * @param deptIds
     * @return
     */
    List<String> selectAllSubDeptIds(List<String> deptIds);
    /**
     * 查询员工集合详细信息
     * @param empIds
     * @return
     */
    List<SysUserBasicInfo> selectUserBasicListByEmpIdList(List<String> empIds);
    /**
     * 根据上级id集合获取用户集合信息
     * @param supervisorIdList
     * @return
     */
    List<SysUserBasicInfo> selectUserBasicInfoBySupervisorIdList(List<String> supervisorIdList);

    /**
     * 根据上级id集合获取用户集合信息
     * @param supervisorIdList
     * @return
     */
    Map<String, SysUserBasicInfo> selectUserBasicInfoMapBySupervisorIdList(List<String> supervisorIdList);
    /**
     * 查询员工集合详细信息
     * @param empIds
     * @return
     */
     Map<String, SysUserBasicInfo> selectUserBasicInfoMapByEmpIdList(List<String> empIds);

    /**
     * 查询配置信息
     * @param module,mdKey
     * @return
     */
     Map<String, String> selectMetaDataMapByParam(String module, String mdKey);

    /**
     * @description: 查询资产残值集合
     * @author: <EMAIL>
     * @date: 2022/12/06
     */
    List<CuxGetfadetailTbl> selectCuxGetfadetailTblList(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO);

    /**
     * @description: 查询资产残值
     * @author: <EMAIL>
     * @date: 2022/12/06
     */
    CuxGetfadetailTbl selectCuxGetfadetailTbl(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO);

    /**
     * @description: 查询资产残值
     * @author: <EMAIL>
     * @date: 2022/12/07
     */
    Map<String, CuxGetfadetailTbl> selectCuxGetfadetailTblMap(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO);

    /**
     * @param: psBusLocationTblDO
     * @description: 根据区域编码集合查询区域信息Map
     * @return: Map<String, PsBusLocationTbl>
     * @author: <EMAIL>
     * @date: 2023/3/9
     */
    Map<String, PsBusLocationTbl> selectPsBusLocationTblMap(PsBusLocationTblDO psBusLocationTblDO);

    /**
     * @param: module,mdKey,clazz
     * @description: 查询配置集合信息
     * @return: clazz
     * @author: <EMAIL>
     * @date: 2023/7/5
     */
    <T> List<T> selectMetaDataByParam(String module, String mdKey, Class<T> clazz);

    /**
     * @param: module,mdKey,clazz
     * @description: 查询配置集合信息
     * @return: clazz
     * @author: <EMAIL>
     * @date: 2023/7/17
     */
    <T> T selectMetaDataOneByParam(String module, String mdKey, Class<T> clazz);
    /**
     * @param: deptIds
     * @description: 查询部门下的用户id集合
     * @return: List<String>
     * @author: <EMAIL>
     * @date: 2023/12/1
     */
    List<String> selectUserEmpIdListByDeptIds(List<String> deptIds);

}
