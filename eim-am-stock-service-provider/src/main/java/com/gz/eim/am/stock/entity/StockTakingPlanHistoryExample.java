package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockTakingPlanHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockTakingPlanHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIsNull() {
            addCriterion("taking_plan_no is null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIsNotNull() {
            addCriterion("taking_plan_no is not null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoEqualTo(String value) {
            addCriterion("taking_plan_no =", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotEqualTo(String value) {
            addCriterion("taking_plan_no <>", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoGreaterThan(String value) {
            addCriterion("taking_plan_no >", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoGreaterThanOrEqualTo(String value) {
            addCriterion("taking_plan_no >=", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLessThan(String value) {
            addCriterion("taking_plan_no <", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLessThanOrEqualTo(String value) {
            addCriterion("taking_plan_no <=", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLike(String value) {
            addCriterion("taking_plan_no like", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotLike(String value) {
            addCriterion("taking_plan_no not like", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIn(List<String> values) {
            addCriterion("taking_plan_no in", values, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotIn(List<String> values) {
            addCriterion("taking_plan_no not in", values, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoBetween(String value1, String value2) {
            addCriterion("taking_plan_no between", value1, value2, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotBetween(String value1, String value2) {
            addCriterion("taking_plan_no not between", value1, value2, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameIsNull() {
            addCriterion("taking_plan_name is null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameIsNotNull() {
            addCriterion("taking_plan_name is not null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameEqualTo(String value) {
            addCriterion("taking_plan_name =", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameNotEqualTo(String value) {
            addCriterion("taking_plan_name <>", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameGreaterThan(String value) {
            addCriterion("taking_plan_name >", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameGreaterThanOrEqualTo(String value) {
            addCriterion("taking_plan_name >=", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameLessThan(String value) {
            addCriterion("taking_plan_name <", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameLessThanOrEqualTo(String value) {
            addCriterion("taking_plan_name <=", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameLike(String value) {
            addCriterion("taking_plan_name like", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameNotLike(String value) {
            addCriterion("taking_plan_name not like", value, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameIn(List<String> values) {
            addCriterion("taking_plan_name in", values, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameNotIn(List<String> values) {
            addCriterion("taking_plan_name not in", values, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameBetween(String value1, String value2) {
            addCriterion("taking_plan_name between", value1, value2, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNameNotBetween(String value1, String value2) {
            addCriterion("taking_plan_name not between", value1, value2, "takingPlanName");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateIsNull() {
            addCriterion("taking_plan_state is null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateIsNotNull() {
            addCriterion("taking_plan_state is not null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateEqualTo(String value) {
            addCriterion("taking_plan_state =", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateNotEqualTo(String value) {
            addCriterion("taking_plan_state <>", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateGreaterThan(String value) {
            addCriterion("taking_plan_state >", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateGreaterThanOrEqualTo(String value) {
            addCriterion("taking_plan_state >=", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateLessThan(String value) {
            addCriterion("taking_plan_state <", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateLessThanOrEqualTo(String value) {
            addCriterion("taking_plan_state <=", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateLike(String value) {
            addCriterion("taking_plan_state like", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateNotLike(String value) {
            addCriterion("taking_plan_state not like", value, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateIn(List<String> values) {
            addCriterion("taking_plan_state in", values, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateNotIn(List<String> values) {
            addCriterion("taking_plan_state not in", values, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateBetween(String value1, String value2) {
            addCriterion("taking_plan_state between", value1, value2, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingPlanStateNotBetween(String value1, String value2) {
            addCriterion("taking_plan_state not between", value1, value2, "takingPlanState");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateIsNull() {
            addCriterion("taking_deadline_date is null");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateIsNotNull() {
            addCriterion("taking_deadline_date is not null");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateEqualTo(Date value) {
            addCriterion("taking_deadline_date =", value, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateNotEqualTo(Date value) {
            addCriterion("taking_deadline_date <>", value, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateGreaterThan(Date value) {
            addCriterion("taking_deadline_date >", value, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateGreaterThanOrEqualTo(Date value) {
            addCriterion("taking_deadline_date >=", value, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateLessThan(Date value) {
            addCriterion("taking_deadline_date <", value, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateLessThanOrEqualTo(Date value) {
            addCriterion("taking_deadline_date <=", value, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateIn(List<Date> values) {
            addCriterion("taking_deadline_date in", values, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateNotIn(List<Date> values) {
            addCriterion("taking_deadline_date not in", values, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateBetween(Date value1, Date value2) {
            addCriterion("taking_deadline_date between", value1, value2, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andTakingDeadlineDateNotBetween(Date value1, Date value2) {
            addCriterion("taking_deadline_date not between", value1, value2, "takingDeadlineDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateIsNull() {
            addCriterion("system_date is null");
            return (Criteria) this;
        }

        public Criteria andSystemDateIsNotNull() {
            addCriterion("system_date is not null");
            return (Criteria) this;
        }

        public Criteria andSystemDateEqualTo(Date value) {
            addCriterion("system_date =", value, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateNotEqualTo(Date value) {
            addCriterion("system_date <>", value, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateGreaterThan(Date value) {
            addCriterion("system_date >", value, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateGreaterThanOrEqualTo(Date value) {
            addCriterion("system_date >=", value, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateLessThan(Date value) {
            addCriterion("system_date <", value, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateLessThanOrEqualTo(Date value) {
            addCriterion("system_date <=", value, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateIn(List<Date> values) {
            addCriterion("system_date in", values, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateNotIn(List<Date> values) {
            addCriterion("system_date not in", values, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateBetween(Date value1, Date value2) {
            addCriterion("system_date between", value1, value2, "systemDate");
            return (Criteria) this;
        }

        public Criteria andSystemDateNotBetween(Date value1, Date value2) {
            addCriterion("system_date not between", value1, value2, "systemDate");
            return (Criteria) this;
        }

        public Criteria andBillStatusIsNull() {
            addCriterion("bill_status is null");
            return (Criteria) this;
        }

        public Criteria andBillStatusIsNotNull() {
            addCriterion("bill_status is not null");
            return (Criteria) this;
        }

        public Criteria andBillStatusEqualTo(Integer value) {
            addCriterion("bill_status =", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusNotEqualTo(Integer value) {
            addCriterion("bill_status <>", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusGreaterThan(Integer value) {
            addCriterion("bill_status >", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("bill_status >=", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusLessThan(Integer value) {
            addCriterion("bill_status <", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusLessThanOrEqualTo(Integer value) {
            addCriterion("bill_status <=", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusIn(List<Integer> values) {
            addCriterion("bill_status in", values, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusNotIn(List<Integer> values) {
            addCriterion("bill_status not in", values, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusBetween(Integer value1, Integer value2) {
            addCriterion("bill_status between", value1, value2, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("bill_status not between", value1, value2, "billStatus");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeIsNull() {
            addCriterion("asset_query_scope is null");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeIsNotNull() {
            addCriterion("asset_query_scope is not null");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeEqualTo(String value) {
            addCriterion("asset_query_scope =", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeNotEqualTo(String value) {
            addCriterion("asset_query_scope <>", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeGreaterThan(String value) {
            addCriterion("asset_query_scope >", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeGreaterThanOrEqualTo(String value) {
            addCriterion("asset_query_scope >=", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeLessThan(String value) {
            addCriterion("asset_query_scope <", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeLessThanOrEqualTo(String value) {
            addCriterion("asset_query_scope <=", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeLike(String value) {
            addCriterion("asset_query_scope like", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeNotLike(String value) {
            addCriterion("asset_query_scope not like", value, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeIn(List<String> values) {
            addCriterion("asset_query_scope in", values, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeNotIn(List<String> values) {
            addCriterion("asset_query_scope not in", values, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeBetween(String value1, String value2) {
            addCriterion("asset_query_scope between", value1, value2, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andAssetQueryScopeNotBetween(String value1, String value2) {
            addCriterion("asset_query_scope not between", value1, value2, "assetQueryScope");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Integer value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Integer value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Integer value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Integer value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Integer value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Integer> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Integer> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Integer value1, Integer value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Integer value1, Integer value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andAssignFlagIsNull() {
            addCriterion("assign_flag is null");
            return (Criteria) this;
        }

        public Criteria andAssignFlagIsNotNull() {
            addCriterion("assign_flag is not null");
            return (Criteria) this;
        }

        public Criteria andAssignFlagEqualTo(Integer value) {
            addCriterion("assign_flag =", value, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagNotEqualTo(Integer value) {
            addCriterion("assign_flag <>", value, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagGreaterThan(Integer value) {
            addCriterion("assign_flag >", value, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("assign_flag >=", value, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagLessThan(Integer value) {
            addCriterion("assign_flag <", value, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagLessThanOrEqualTo(Integer value) {
            addCriterion("assign_flag <=", value, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagIn(List<Integer> values) {
            addCriterion("assign_flag in", values, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagNotIn(List<Integer> values) {
            addCriterion("assign_flag not in", values, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagBetween(Integer value1, Integer value2) {
            addCriterion("assign_flag between", value1, value2, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andAssignFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("assign_flag not between", value1, value2, "assignFlag");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtIsNull() {
            addCriterion("copy_at is null");
            return (Criteria) this;
        }

        public Criteria andCopyAtIsNotNull() {
            addCriterion("copy_at is not null");
            return (Criteria) this;
        }

        public Criteria andCopyAtEqualTo(Date value) {
            addCriterion("copy_at =", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtNotEqualTo(Date value) {
            addCriterion("copy_at <>", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtGreaterThan(Date value) {
            addCriterion("copy_at >", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtGreaterThanOrEqualTo(Date value) {
            addCriterion("copy_at >=", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtLessThan(Date value) {
            addCriterion("copy_at <", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtLessThanOrEqualTo(Date value) {
            addCriterion("copy_at <=", value, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtIn(List<Date> values) {
            addCriterion("copy_at in", values, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtNotIn(List<Date> values) {
            addCriterion("copy_at not in", values, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtBetween(Date value1, Date value2) {
            addCriterion("copy_at between", value1, value2, "copyAt");
            return (Criteria) this;
        }

        public Criteria andCopyAtNotBetween(Date value1, Date value2) {
            addCriterion("copy_at not between", value1, value2, "copyAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}