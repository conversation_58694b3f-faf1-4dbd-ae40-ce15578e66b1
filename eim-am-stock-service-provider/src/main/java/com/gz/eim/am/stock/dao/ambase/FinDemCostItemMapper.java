package com.gz.eim.am.stock.dao.ambase;

import com.gz.eim.am.stock.entity.ambase.FinDemCostItem;
import com.gz.eim.am.stock.entity.ambase.FinDemCostItemExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FinDemCostItemMapper {
    long countByExample(FinDemCostItemExample example);

    int deleteByPrimaryKey(Long costItemId);

    int insert(FinDemCostItem record);

    int insertSelective(FinDemCostItem record);

    List<FinDemCostItem> selectByExample(FinDemCostItemExample example);

    FinDemCostItem selectByPrimaryKey(Long costItemId);

    int updateByExampleSelective(@Param("record") FinDemCostItem record, @Param("example") FinDemCostItemExample example);

    int updateByExample(@Param("record") FinDemCostItem record, @Param("example") FinDemCostItemExample example);

    int updateByPrimaryKeySelective(FinDemCostItem record);

    int updateByPrimaryKey(FinDemCostItem record);
}