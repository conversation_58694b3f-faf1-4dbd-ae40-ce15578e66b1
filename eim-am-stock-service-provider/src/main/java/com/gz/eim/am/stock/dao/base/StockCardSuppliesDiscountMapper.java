package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockCardSuppliesDiscount;
import com.gz.eim.am.stock.entity.StockCardSuppliesDiscountExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockCardSuppliesDiscountMapper {
    long countByExample(StockCardSuppliesDiscountExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockCardSuppliesDiscount record);

    int insertSelective(StockCardSuppliesDiscount record);

    List<StockCardSuppliesDiscount> selectByExample(StockCardSuppliesDiscountExample example);

    StockCardSuppliesDiscount selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockCardSuppliesDiscount record, @Param("example") StockCardSuppliesDiscountExample example);

    int updateByExample(@Param("record") StockCardSuppliesDiscount record, @Param("example") StockCardSuppliesDiscountExample example);

    int updateByPrimaryKeySelective(StockCardSuppliesDiscount record);

    int updateByPrimaryKey(StockCardSuppliesDiscount record);
}