package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockAssetsScrapLineExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockAssetsScrapLineExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andLineIdIsNull() {
            addCriterion("line_id is null");
            return (Criteria) this;
        }

        public Criteria andLineIdIsNotNull() {
            addCriterion("line_id is not null");
            return (Criteria) this;
        }

        public Criteria andLineIdEqualTo(Long value) {
            addCriterion("line_id =", value, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdNotEqualTo(Long value) {
            addCriterion("line_id <>", value, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdGreaterThan(Long value) {
            addCriterion("line_id >", value, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdGreaterThanOrEqualTo(Long value) {
            addCriterion("line_id >=", value, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdLessThan(Long value) {
            addCriterion("line_id <", value, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdLessThanOrEqualTo(Long value) {
            addCriterion("line_id <=", value, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdIn(List<Long> values) {
            addCriterion("line_id in", values, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdNotIn(List<Long> values) {
            addCriterion("line_id not in", values, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdBetween(Long value1, Long value2) {
            addCriterion("line_id between", value1, value2, "lineId");
            return (Criteria) this;
        }

        public Criteria andLineIdNotBetween(Long value1, Long value2) {
            addCriterion("line_id not between", value1, value2, "lineId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNull() {
            addCriterion("head_id is null");
            return (Criteria) this;
        }

        public Criteria andHeadIdIsNotNull() {
            addCriterion("head_id is not null");
            return (Criteria) this;
        }

        public Criteria andHeadIdEqualTo(Long value) {
            addCriterion("head_id =", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotEqualTo(Long value) {
            addCriterion("head_id <>", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThan(Long value) {
            addCriterion("head_id >", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdGreaterThanOrEqualTo(Long value) {
            addCriterion("head_id >=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThan(Long value) {
            addCriterion("head_id <", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdLessThanOrEqualTo(Long value) {
            addCriterion("head_id <=", value, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdIn(List<Long> values) {
            addCriterion("head_id in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotIn(List<Long> values) {
            addCriterion("head_id not in", values, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdBetween(Long value1, Long value2) {
            addCriterion("head_id between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andHeadIdNotBetween(Long value1, Long value2) {
            addCriterion("head_id not between", value1, value2, "headId");
            return (Criteria) this;
        }

        public Criteria andScrapNoIsNull() {
            addCriterion("scrap_no is null");
            return (Criteria) this;
        }

        public Criteria andScrapNoIsNotNull() {
            addCriterion("scrap_no is not null");
            return (Criteria) this;
        }

        public Criteria andScrapNoEqualTo(String value) {
            addCriterion("scrap_no =", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoNotEqualTo(String value) {
            addCriterion("scrap_no <>", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoGreaterThan(String value) {
            addCriterion("scrap_no >", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoGreaterThanOrEqualTo(String value) {
            addCriterion("scrap_no >=", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoLessThan(String value) {
            addCriterion("scrap_no <", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoLessThanOrEqualTo(String value) {
            addCriterion("scrap_no <=", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoLike(String value) {
            addCriterion("scrap_no like", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoNotLike(String value) {
            addCriterion("scrap_no not like", value, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoIn(List<String> values) {
            addCriterion("scrap_no in", values, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoNotIn(List<String> values) {
            addCriterion("scrap_no not in", values, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoBetween(String value1, String value2) {
            addCriterion("scrap_no between", value1, value2, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andScrapNoNotBetween(String value1, String value2) {
            addCriterion("scrap_no not between", value1, value2, "scrapNo");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNull() {
            addCriterion("assets_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNotNull() {
            addCriterion("assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeEqualTo(String value) {
            addCriterion("assets_code =", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotEqualTo(String value) {
            addCriterion("assets_code <>", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThan(String value) {
            addCriterion("assets_code >", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("assets_code >=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThan(String value) {
            addCriterion("assets_code <", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("assets_code <=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLike(String value) {
            addCriterion("assets_code like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotLike(String value) {
            addCriterion("assets_code not like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIn(List<String> values) {
            addCriterion("assets_code in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotIn(List<String> values) {
            addCriterion("assets_code not in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeBetween(String value1, String value2) {
            addCriterion("assets_code between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("assets_code not between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIsNull() {
            addCriterion("assets_name is null");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIsNotNull() {
            addCriterion("assets_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsNameEqualTo(String value) {
            addCriterion("assets_name =", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotEqualTo(String value) {
            addCriterion("assets_name <>", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameGreaterThan(String value) {
            addCriterion("assets_name >", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameGreaterThanOrEqualTo(String value) {
            addCriterion("assets_name >=", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLessThan(String value) {
            addCriterion("assets_name <", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLessThanOrEqualTo(String value) {
            addCriterion("assets_name <=", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLike(String value) {
            addCriterion("assets_name like", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotLike(String value) {
            addCriterion("assets_name not like", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIn(List<String> values) {
            addCriterion("assets_name in", values, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotIn(List<String> values) {
            addCriterion("assets_name not in", values, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameBetween(String value1, String value2) {
            addCriterion("assets_name between", value1, value2, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotBetween(String value1, String value2) {
            addCriterion("assets_name not between", value1, value2, "assetsName");
            return (Criteria) this;
        }

        public Criteria andSnCodeIsNull() {
            addCriterion("sn_code is null");
            return (Criteria) this;
        }

        public Criteria andSnCodeIsNotNull() {
            addCriterion("sn_code is not null");
            return (Criteria) this;
        }

        public Criteria andSnCodeEqualTo(String value) {
            addCriterion("sn_code =", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotEqualTo(String value) {
            addCriterion("sn_code <>", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeGreaterThan(String value) {
            addCriterion("sn_code >", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sn_code >=", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLessThan(String value) {
            addCriterion("sn_code <", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLessThanOrEqualTo(String value) {
            addCriterion("sn_code <=", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLike(String value) {
            addCriterion("sn_code like", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotLike(String value) {
            addCriterion("sn_code not like", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeIn(List<String> values) {
            addCriterion("sn_code in", values, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotIn(List<String> values) {
            addCriterion("sn_code not in", values, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeBetween(String value1, String value2) {
            addCriterion("sn_code between", value1, value2, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotBetween(String value1, String value2) {
            addCriterion("sn_code not between", value1, value2, "snCode");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIsNull() {
            addCriterion("assets_keeper is null");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIsNotNull() {
            addCriterion("assets_keeper is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperEqualTo(String value) {
            addCriterion("assets_keeper =", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotEqualTo(String value) {
            addCriterion("assets_keeper <>", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperGreaterThan(String value) {
            addCriterion("assets_keeper >", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperGreaterThanOrEqualTo(String value) {
            addCriterion("assets_keeper >=", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLessThan(String value) {
            addCriterion("assets_keeper <", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLessThanOrEqualTo(String value) {
            addCriterion("assets_keeper <=", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLike(String value) {
            addCriterion("assets_keeper like", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotLike(String value) {
            addCriterion("assets_keeper not like", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIn(List<String> values) {
            addCriterion("assets_keeper in", values, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotIn(List<String> values) {
            addCriterion("assets_keeper not in", values, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperBetween(String value1, String value2) {
            addCriterion("assets_keeper between", value1, value2, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotBetween(String value1, String value2) {
            addCriterion("assets_keeper not between", value1, value2, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNull() {
            addCriterion("warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNotNull() {
            addCriterion("warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeEqualTo(String value) {
            addCriterion("warehouse_code =", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotEqualTo(String value) {
            addCriterion("warehouse_code <>", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThan(String value) {
            addCriterion("warehouse_code >", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_code >=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThan(String value) {
            addCriterion("warehouse_code <", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("warehouse_code <=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLike(String value) {
            addCriterion("warehouse_code like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotLike(String value) {
            addCriterion("warehouse_code not like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIn(List<String> values) {
            addCriterion("warehouse_code in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotIn(List<String> values) {
            addCriterion("warehouse_code not in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeBetween(String value1, String value2) {
            addCriterion("warehouse_code between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("warehouse_code not between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andHolderIsNull() {
            addCriterion("holder is null");
            return (Criteria) this;
        }

        public Criteria andHolderIsNotNull() {
            addCriterion("holder is not null");
            return (Criteria) this;
        }

        public Criteria andHolderEqualTo(String value) {
            addCriterion("holder =", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotEqualTo(String value) {
            addCriterion("holder <>", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderGreaterThan(String value) {
            addCriterion("holder >", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderGreaterThanOrEqualTo(String value) {
            addCriterion("holder >=", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderLessThan(String value) {
            addCriterion("holder <", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderLessThanOrEqualTo(String value) {
            addCriterion("holder <=", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderLike(String value) {
            addCriterion("holder like", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotLike(String value) {
            addCriterion("holder not like", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderIn(List<String> values) {
            addCriterion("holder in", values, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotIn(List<String> values) {
            addCriterion("holder not in", values, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderBetween(String value1, String value2) {
            addCriterion("holder between", value1, value2, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotBetween(String value1, String value2) {
            addCriterion("holder not between", value1, value2, "holder");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressIsNull() {
            addCriterion("assets_warehouse_address is null");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressIsNotNull() {
            addCriterion("assets_warehouse_address is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressEqualTo(String value) {
            addCriterion("assets_warehouse_address =", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressNotEqualTo(String value) {
            addCriterion("assets_warehouse_address <>", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressGreaterThan(String value) {
            addCriterion("assets_warehouse_address >", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressGreaterThanOrEqualTo(String value) {
            addCriterion("assets_warehouse_address >=", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressLessThan(String value) {
            addCriterion("assets_warehouse_address <", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressLessThanOrEqualTo(String value) {
            addCriterion("assets_warehouse_address <=", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressLike(String value) {
            addCriterion("assets_warehouse_address like", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressNotLike(String value) {
            addCriterion("assets_warehouse_address not like", value, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressIn(List<String> values) {
            addCriterion("assets_warehouse_address in", values, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressNotIn(List<String> values) {
            addCriterion("assets_warehouse_address not in", values, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressBetween(String value1, String value2) {
            addCriterion("assets_warehouse_address between", value1, value2, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsWarehouseAddressNotBetween(String value1, String value2) {
            addCriterion("assets_warehouse_address not between", value1, value2, "assetsWarehouseAddress");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusIsNull() {
            addCriterion("assets_status is null");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusIsNotNull() {
            addCriterion("assets_status is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusEqualTo(Integer value) {
            addCriterion("assets_status =", value, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusNotEqualTo(Integer value) {
            addCriterion("assets_status <>", value, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusGreaterThan(Integer value) {
            addCriterion("assets_status >", value, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("assets_status >=", value, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusLessThan(Integer value) {
            addCriterion("assets_status <", value, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusLessThanOrEqualTo(Integer value) {
            addCriterion("assets_status <=", value, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusIn(List<Integer> values) {
            addCriterion("assets_status in", values, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusNotIn(List<Integer> values) {
            addCriterion("assets_status not in", values, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusBetween(Integer value1, Integer value2) {
            addCriterion("assets_status between", value1, value2, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andAssetsStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("assets_status not between", value1, value2, "assetsStatus");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIsNull() {
            addCriterion("need_dept is null");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIsNotNull() {
            addCriterion("need_dept is not null");
            return (Criteria) this;
        }

        public Criteria andNeedDeptEqualTo(String value) {
            addCriterion("need_dept =", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotEqualTo(String value) {
            addCriterion("need_dept <>", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptGreaterThan(String value) {
            addCriterion("need_dept >", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptGreaterThanOrEqualTo(String value) {
            addCriterion("need_dept >=", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLessThan(String value) {
            addCriterion("need_dept <", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLessThanOrEqualTo(String value) {
            addCriterion("need_dept <=", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLike(String value) {
            addCriterion("need_dept like", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotLike(String value) {
            addCriterion("need_dept not like", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIn(List<String> values) {
            addCriterion("need_dept in", values, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotIn(List<String> values) {
            addCriterion("need_dept not in", values, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptBetween(String value1, String value2) {
            addCriterion("need_dept between", value1, value2, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotBetween(String value1, String value2) {
            addCriterion("need_dept not between", value1, value2, "needDept");
            return (Criteria) this;
        }

        public Criteria andAssetsCostIsNull() {
            addCriterion("assets_cost is null");
            return (Criteria) this;
        }

        public Criteria andAssetsCostIsNotNull() {
            addCriterion("assets_cost is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsCostEqualTo(BigDecimal value) {
            addCriterion("assets_cost =", value, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostNotEqualTo(BigDecimal value) {
            addCriterion("assets_cost <>", value, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostGreaterThan(BigDecimal value) {
            addCriterion("assets_cost >", value, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("assets_cost >=", value, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostLessThan(BigDecimal value) {
            addCriterion("assets_cost <", value, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("assets_cost <=", value, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostIn(List<BigDecimal> values) {
            addCriterion("assets_cost in", values, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostNotIn(List<BigDecimal> values) {
            addCriterion("assets_cost not in", values, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assets_cost between", value1, value2, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assets_cost not between", value1, value2, "assetsCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostIsNull() {
            addCriterion("assets_last_cost is null");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostIsNotNull() {
            addCriterion("assets_last_cost is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostEqualTo(BigDecimal value) {
            addCriterion("assets_last_cost =", value, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostNotEqualTo(BigDecimal value) {
            addCriterion("assets_last_cost <>", value, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostGreaterThan(BigDecimal value) {
            addCriterion("assets_last_cost >", value, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("assets_last_cost >=", value, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostLessThan(BigDecimal value) {
            addCriterion("assets_last_cost <", value, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("assets_last_cost <=", value, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostIn(List<BigDecimal> values) {
            addCriterion("assets_last_cost in", values, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostNotIn(List<BigDecimal> values) {
            addCriterion("assets_last_cost not in", values, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assets_last_cost between", value1, value2, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLastCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("assets_last_cost not between", value1, value2, "assetsLastCost");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeIsNull() {
            addCriterion("assets_life is null");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeIsNotNull() {
            addCriterion("assets_life is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeEqualTo(Integer value) {
            addCriterion("assets_life =", value, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeNotEqualTo(Integer value) {
            addCriterion("assets_life <>", value, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeGreaterThan(Integer value) {
            addCriterion("assets_life >", value, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeGreaterThanOrEqualTo(Integer value) {
            addCriterion("assets_life >=", value, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeLessThan(Integer value) {
            addCriterion("assets_life <", value, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeLessThanOrEqualTo(Integer value) {
            addCriterion("assets_life <=", value, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeIn(List<Integer> values) {
            addCriterion("assets_life in", values, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeNotIn(List<Integer> values) {
            addCriterion("assets_life not in", values, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeBetween(Integer value1, Integer value2) {
            addCriterion("assets_life between", value1, value2, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsLifeNotBetween(Integer value1, Integer value2) {
            addCriterion("assets_life not between", value1, value2, "assetsLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeIsNull() {
            addCriterion("assets_surplus_life is null");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeIsNotNull() {
            addCriterion("assets_surplus_life is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeEqualTo(Integer value) {
            addCriterion("assets_surplus_life =", value, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeNotEqualTo(Integer value) {
            addCriterion("assets_surplus_life <>", value, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeGreaterThan(Integer value) {
            addCriterion("assets_surplus_life >", value, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeGreaterThanOrEqualTo(Integer value) {
            addCriterion("assets_surplus_life >=", value, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeLessThan(Integer value) {
            addCriterion("assets_surplus_life <", value, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeLessThanOrEqualTo(Integer value) {
            addCriterion("assets_surplus_life <=", value, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeIn(List<Integer> values) {
            addCriterion("assets_surplus_life in", values, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeNotIn(List<Integer> values) {
            addCriterion("assets_surplus_life not in", values, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeBetween(Integer value1, Integer value2) {
            addCriterion("assets_surplus_life between", value1, value2, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andAssetsSurplusLifeNotBetween(Integer value1, Integer value2) {
            addCriterion("assets_surplus_life not between", value1, value2, "assetsSurplusLife");
            return (Criteria) this;
        }

        public Criteria andScrapReasonIsNull() {
            addCriterion("scrap_reason is null");
            return (Criteria) this;
        }

        public Criteria andScrapReasonIsNotNull() {
            addCriterion("scrap_reason is not null");
            return (Criteria) this;
        }

        public Criteria andScrapReasonEqualTo(Integer value) {
            addCriterion("scrap_reason =", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotEqualTo(Integer value) {
            addCriterion("scrap_reason <>", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonGreaterThan(Integer value) {
            addCriterion("scrap_reason >", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonGreaterThanOrEqualTo(Integer value) {
            addCriterion("scrap_reason >=", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonLessThan(Integer value) {
            addCriterion("scrap_reason <", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonLessThanOrEqualTo(Integer value) {
            addCriterion("scrap_reason <=", value, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonIn(List<Integer> values) {
            addCriterion("scrap_reason in", values, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotIn(List<Integer> values) {
            addCriterion("scrap_reason not in", values, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonBetween(Integer value1, Integer value2) {
            addCriterion("scrap_reason between", value1, value2, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapReasonNotBetween(Integer value1, Integer value2) {
            addCriterion("scrap_reason not between", value1, value2, "scrapReason");
            return (Criteria) this;
        }

        public Criteria andScrapFlagIsNull() {
            addCriterion("scrap_flag is null");
            return (Criteria) this;
        }

        public Criteria andScrapFlagIsNotNull() {
            addCriterion("scrap_flag is not null");
            return (Criteria) this;
        }

        public Criteria andScrapFlagEqualTo(Integer value) {
            addCriterion("scrap_flag =", value, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagNotEqualTo(Integer value) {
            addCriterion("scrap_flag <>", value, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagGreaterThan(Integer value) {
            addCriterion("scrap_flag >", value, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("scrap_flag >=", value, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagLessThan(Integer value) {
            addCriterion("scrap_flag <", value, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagLessThanOrEqualTo(Integer value) {
            addCriterion("scrap_flag <=", value, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagIn(List<Integer> values) {
            addCriterion("scrap_flag in", values, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagNotIn(List<Integer> values) {
            addCriterion("scrap_flag not in", values, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagBetween(Integer value1, Integer value2) {
            addCriterion("scrap_flag between", value1, value2, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andScrapFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("scrap_flag not between", value1, value2, "scrapFlag");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}