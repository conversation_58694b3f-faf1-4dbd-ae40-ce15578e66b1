package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockAssetsCheckTask {
    private Long checkTaskId;

    private String checkTaskNo;

    private String takingPlanNo;

    private String checkTaskName;

    private Integer checkTaskMethod;

    private String dutyUser;

    private Date taskBeginTime;

    private Date taskEndTime;

    private Date taskLastTime;

    private Integer checkTaskStatus;

    private Integer assignFlag;

    private String assignDept;

    private String assignExcludePeople;

    private Double taskProgressRate;

    private String checkTaskScope;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    public Long getCheckTaskId() {
        return checkTaskId;
    }

    public void setCheckTaskId(Long checkTaskId) {
        this.checkTaskId = checkTaskId;
    }

    public String getCheckTaskNo() {
        return checkTaskNo;
    }

    public void setCheckTaskNo(String checkTaskNo) {
        this.checkTaskNo = checkTaskNo == null ? null : checkTaskNo.trim();
    }

    public String getTakingPlanNo() {
        return takingPlanNo;
    }

    public void setTakingPlanNo(String takingPlanNo) {
        this.takingPlanNo = takingPlanNo == null ? null : takingPlanNo.trim();
    }

    public String getCheckTaskName() {
        return checkTaskName;
    }

    public void setCheckTaskName(String checkTaskName) {
        this.checkTaskName = checkTaskName == null ? null : checkTaskName.trim();
    }

    public Integer getCheckTaskMethod() {
        return checkTaskMethod;
    }

    public void setCheckTaskMethod(Integer checkTaskMethod) {
        this.checkTaskMethod = checkTaskMethod;
    }

    public String getDutyUser() {
        return dutyUser;
    }

    public void setDutyUser(String dutyUser) {
        this.dutyUser = dutyUser == null ? null : dutyUser.trim();
    }

    public Date getTaskBeginTime() {
        return taskBeginTime;
    }

    public void setTaskBeginTime(Date taskBeginTime) {
        this.taskBeginTime = taskBeginTime;
    }

    public Date getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(Date taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public Date getTaskLastTime() {
        return taskLastTime;
    }

    public void setTaskLastTime(Date taskLastTime) {
        this.taskLastTime = taskLastTime;
    }

    public Integer getCheckTaskStatus() {
        return checkTaskStatus;
    }

    public void setCheckTaskStatus(Integer checkTaskStatus) {
        this.checkTaskStatus = checkTaskStatus;
    }

    public Integer getAssignFlag() {
        return assignFlag;
    }

    public void setAssignFlag(Integer assignFlag) {
        this.assignFlag = assignFlag;
    }

    public String getAssignDept() {
        return assignDept;
    }

    public void setAssignDept(String assignDept) {
        this.assignDept = assignDept == null ? null : assignDept.trim();
    }

    public String getAssignExcludePeople() {
        return assignExcludePeople;
    }

    public void setAssignExcludePeople(String assignExcludePeople) {
        this.assignExcludePeople = assignExcludePeople == null ? null : assignExcludePeople.trim();
    }

    public Double getTaskProgressRate() {
        return taskProgressRate;
    }

    public void setTaskProgressRate(Double taskProgressRate) {
        this.taskProgressRate = taskProgressRate;
    }

    public String getCheckTaskScope() {
        return checkTaskScope;
    }

    public void setCheckTaskScope(String checkTaskScope) {
        this.checkTaskScope = checkTaskScope == null ? null : checkTaskScope.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}