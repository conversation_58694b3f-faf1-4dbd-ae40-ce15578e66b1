package com.gz.eim.am.stock.constant;

/**
 * <AUTHOR>
 * @Title: eim-am-contract
 * @Description:
 * @date 2019/11/6下午3:49
 */
public class RedisKeyConstants {

    /**
     * 出库Redis key
     */
    public static String DELIVERY_OUT = "stock:supplies:delivery:out:";
    /**
     * 入库Redis key
     */
    public static String INVENTORY_IN = "stock:supplies:inventory:in:";
    /**
     * 资产采购单据操作RedisKey
     */
    public static String PLAN_ASSET_PURCHASE_BOUND = "stock:assets:purchase:inbound:";
    /**
     * 资产归还单归还操作RedisKey
     */
    public static String PLAN_ASSET_REMAND_BOUND = "stock:assets:remand:bound:";

    /**
     * 资产归还单驳回操作RedisKey
     */
    public static  final String PLAN_ASSET_REMAND_REJECT = "stock:assets:remand:reject:";

    /**
     * 资产归还单初始化操作RedisKey
     */
    public static String PLAN_ASSET_REMAND_INITIALIZATION = "stock:assets:remand:in:";
    /**
     * 资产处置单出库操作RedisKey
     */
    public static String PLAN_ASSET_DISPOSE_BOUND = "stock:assets:dispose:bound:";
    /**
     * 资产调拨出库操作RedisKey
     */
    public static String PLAN_TRANSFER_OUT_BOUND = "stock:assets:transfer:outbound:";
    /**
     * 资产自助领用出库操作RedisKey
     */
    public static String PLAN_ASSET_RECEIVE_BOUND = "stock:assets:receive:bound:";
    /**
     * 资产领用出库操作RedisKey
     */
    public static String REMAND_ASSETS_SELF_HELP_RECEIVE_BOUND = "stock:assets:self:help:receive:bound:";
    /**
     * 资产需求单快递员下单RedisKey
     */
    public static String ASSETS_DEMAND_COURIER_CREATE_ORDER = "stock:assets:demand:courier:create:order:";
    /**
     * 资产需求单员工提交RedisKey
     */
    public static String ASSETS_DEMAND_EMPLOYEE_CONFIRM_SUBMIT = "stock:assets:demand:employee:confirm:submit:";
    /**
     * 资产需求单管理员审批提交RedisKey
     */
    public static String ASSETS_DEMAND_ADMIN_APPROVE_SUBMIT = "stock:assets:demand:admin:approve:submit:";
    /**
     * 资产批量打印上传操作RedisKey
     */
    public static String BATCH_ASSETS_PRINT_UPLOAD = "stock:assets:batch:print:upload:";
    /**
     * 批量生成资产计划调拨单
     */
    public static String BATCH_ASSETS_TRANSFER_OUT_IMPORT_SAVE = "stock:assets:batch:transfer:import:";
    /**
     * 资产调拨出库单快速出库
     */
    public static String PLAN_ASSETS_TRANSFER_OUT_FAST = "stock:assets:batch:transfer:fast:";
    /**
     * 结算
     */
    public static String SETTLEMENT_DELIVERY = "stock:assets:setLeMeat:delivery:";
    /**
     * 结算Redis lock time
     */
    public static final Integer SETTLEMENT_DELIVERY_LOCK_SECOND = 60*10;
    /**
     * 抽取资产数据同步到ebs锁
     */
    public static String BATCH_ASSETS_EXTRACT_EBS = "stock:assets:extract:ebs:";
    /**
     * 抽取报送资产数据同步到ebs锁
     */
    public static String BATCH_ASSETS_EXTRACT_SYNC_EBS = "stock:assets:extract:sync:ebs:";
    /**
     * 同步ebs数据处理结果锁
     */
    public static String BATCH_ASSETS_SYNCRESULT_EBS = "stock:assets:sync:result:ebs:";

    /**
     * 手动同步ebs锁
     */
    public static String MANUAL_SYNC_EBS = "stock:assets:manual:sync:ebs:";


    /**
     * 创建资产盘点任务锁
     */
    public static String CREATE_STOCK_ASSETS_CHECK_TASK ="stock:check:create:task:";
    /**
     * 更新资产盘点人锁
     */
    public static String UPDATE_ASSETS_CHECK_PEOPLES ="stock:check:update:peoples:";
    /**
     * 盘点任务下发锁
     */
    public static String CHECK_TASK_ASSIGN_TO_PEOPLE ="stock:check:assign:people:";
    /**
     * 盘点任务结束锁
     */
    public static String ASSETS_CHECK_TASK_FINISH ="stock:check:task:finish:";
    /**
     * 盘点任务线下上传确认操作锁
     */
    public static String ASSETS_CHECK_IMPORT_CONFIRM ="stock:check:assets:import:confirm:";

    /**
     * 移动端提交盘点结果锁
     */
    public static String SUBMIT_STOCK_CHECK_RESULT = "stock:check:submit:check:result:";

    /**
     * 资产盘点结果审批锁
     */
    public static String STOCK_CHECK_RESULT_APPROVE = "stock:check:result:approve:";
    /**
     * 资产盘点员工结束盘点锁
     */
    public static String STOCK_CHECK_EMPLOYEE_FINISH_CHECK = "stock:check:employee:finish:check:";

    /**
     * 盘盈亏调整执行锁
     */
    public static String STOCK_CHECK_ADJUST_PROFIT = "stock:check:adjust:profit:";
    /**
     * 盘盈亏调整执行锁
     */
    public static String STOCK_CHECK_ADJUST_UPDATE = "stock:check:adjust:update:";

    /**
     * 生成盘点差异清单锁
     */
    public static String STOCK_CHECK_RESULT_SAVE = "stock:check:result:save:";
    /**
     * 采购退货申请单出库锁
     */
    public static String PLAN_ASSET_PURCHASE_RETURN_BOUND = "stock:purchase:return:bound:";
    /**
     * Gps采购申请单入库操作RedisKey
     */
    public static String PLAN_GPS_PURCHASE_BOUND = "stock:assets:remand:bound:";
    /**
     * 调拨出库催办操作RedisKey
     */
    public static String PLAN_CARD_OUT = "stock:card:urge:out:";
    /**
     * 执照续借操作RedisKey
     */
    public static String STOCK_ASSETS_LICENSE_RENEW = "stock:assets:license:renew:";

    /**
     * 资产报废数据保存锁
     */
    public static String STOCK_ASSETS_SCRAP_SAVE_TEMP = "stock:assets:scrap:save:temp:";
    /**
     * 资产报废数据保存锁
     */
    public static String STOCK_ASSETS_SCRAP_EXCEL_TEMP = "stock:assets:scrap:excel:temp:";
    /**
     * 资产报废单据创建锁
     */
    public static String STOCK_ASSETS_SCRAP_CREATE = "stock:assets:scrap:create:";
    /**
     * 资产报废单据提交审批锁
     */
    public static String STOCK_ASSETS_SCRAP_SUBMIT = "stock:assets:scrap:submit:";
    /**
     * 资产批量打印生成编码锁
     */
    public static String BATCH_ASSETS_PRINT_CREATE_CODE = "stock:assets:batch:print:create:code:";
    /**
     * 资产赔偿单据提交锁
     */
    public static String STOCK_ASSETS_COMPENSATION_SUBMIT = "stock:assets:compensation:submit:";
    /**
     * 资产维修单保存或提交锁
     */
    public static String STOCK_ASSETS_REPAIR_SAVE_OR_SUBMIT = "stock:assets:repair:save:or:submit:";
    /**
     * 资产维修验收单提交锁
     */
    public static String STOCK_ASSETS_REPAIR_CHECK_SUBMIT = "stock:assets:repair:check:submit:";
    /**
     * 资产维修验收单审批锁
     */
    public static String STOCK_ASSETS_REPAIR_CHECK_APPROVE = "stock:assets:repair:check:approve:";
    /**
     * 资产维修验收单删除锁
     */
    public static String STOCK_ASSETS_REPAIR_DELETE= "stock:assets:repair:delete";
}
