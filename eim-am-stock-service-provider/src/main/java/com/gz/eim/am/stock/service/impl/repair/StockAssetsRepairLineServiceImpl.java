package com.gz.eim.am.stock.service.impl.repair;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockAssetsRepairLineMapper;
import com.gz.eim.am.stock.dao.repair.AssetsRepairLineMapper;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairLineBaseReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecord;
import com.gz.eim.am.stock.entity.StockAssetsRepairLine;
import com.gz.eim.am.stock.entity.StockAssetsRepairLineExample;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairLineService;
import com.gz.eim.am.stock.util.common.ListUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: StockAssetsRepairLineServiceImpl
 * @description: 资产维修行信息Service
 * @author: <EMAIL>
 * @date: 2022/12/15
 **/
@Slf4j
@Service
public class StockAssetsRepairLineServiceImpl implements StockAssetsRepairLineService {

    @Autowired
    private StockAssetsRepairLineMapper stockAssetsRepairLineMapper;
    @Autowired
    private AssetsRepairLineMapper assetsRepairLineMapper;

    @Override
    public List<StockAssetsRepairLine> selectList(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO) {
        if(null == stockAssetsRepairLineBaseReqDTO){
            return new ArrayList<>();
        }
        StockAssetsRepairLineExample stockAssetsRepairLineExample = new StockAssetsRepairLineExample();
        StockAssetsRepairLineExample.Criteria criteria = stockAssetsRepairLineExample.createCriteria();
        if(StringUtils.isNotBlank(stockAssetsRepairLineBaseReqDTO.getRepairNo())){
            criteria.andRepairNoEqualTo(stockAssetsRepairLineBaseReqDTO.getRepairNo());
        }
        if(!CollectionUtils.isEmpty(stockAssetsRepairLineBaseReqDTO.getIdList())){
            criteria.andIdIn(stockAssetsRepairLineBaseReqDTO.getIdList());
        }
        if(stockAssetsRepairLineBaseReqDTO.getIsValid() != null){
            criteria.andIsValidEqualTo(stockAssetsRepairLineBaseReqDTO.getIsValid());
        }
        return stockAssetsRepairLineMapper.selectByExample(stockAssetsRepairLineExample);
    }

    @Override
    public StockAssetsRepairLine selectOne(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO) {
        List<StockAssetsRepairLine> stockAssetsRepairLineList = selectList(stockAssetsRepairLineBaseReqDTO);
        if(CollectionUtils.isEmpty(stockAssetsRepairLineList)){
            return null;
        }
        return stockAssetsRepairLineList.get(CommonConstant.NUMBER_ZERO);
    }

    @Override
    public Map<Long, StockAssetsRepairLine> selectMapById(StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO) {
        List<StockAssetsRepairLine> stockAssetsRepairLineList = selectList(stockAssetsRepairLineBaseReqDTO);
        if(CollectionUtils.isEmpty(stockAssetsRepairLineList)){
            return new HashMap<>();
        }
        return stockAssetsRepairLineList.stream().collect(Collectors.toMap(StockAssetsRepairLine :: getId, stockAssetsRepairLine -> stockAssetsRepairLine, (k1, k2) -> k2));
    }

    @Override
    public int batchInsert(List<StockAssetsRepairLine> stockAssetsRepairLineList) {
        if(CollectionUtils.isEmpty(stockAssetsRepairLineList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsRepairLineList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsRepairLine>> stockAssetsRepairLineListList = ListUtil.splitList(stockAssetsRepairLineList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsRepairLine> stockAssetsRepairLines : stockAssetsRepairLineListList) {
                count += assetsRepairLineMapper.batchInsert(stockAssetsRepairLines);
            }
            return count;
        }else {
            return assetsRepairLineMapper.batchInsert(stockAssetsRepairLineList);
        }
    }

    @Override
    public int batchUpdate(List<StockAssetsRepairLine> stockAssetsRepairLineList) {
        if(CollectionUtils.isEmpty(stockAssetsRepairLineList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsRepairLineList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsRepairLine>> stockAssetsRepairLineListList = ListUtil.splitList(stockAssetsRepairLineList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsRepairLine> stockAssetsRepairLines : stockAssetsRepairLineListList) {
                count += assetsRepairLineMapper.batchUpdate(stockAssetsRepairLines);
            }
            return count;
        }else {
            return assetsRepairLineMapper.batchUpdate(stockAssetsRepairLineList);
        }
    }
}
