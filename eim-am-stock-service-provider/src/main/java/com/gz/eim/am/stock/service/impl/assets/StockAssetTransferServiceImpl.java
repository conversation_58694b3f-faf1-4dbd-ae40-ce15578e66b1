package com.gz.eim.am.stock.service.impl.assets;

import com.fuu.eim.fin.base.api.SysUserServiceApi;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtTokenUtil;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import com.gz.eim.am.base.dto.request.file.UploadFileReqDTO;
import com.gz.eim.am.base.dto.response.file.SysAttachDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.FileConstant;
import com.gz.eim.am.stock.dao.ambase.SysUserMapper;
import com.gz.eim.am.stock.dao.assets.AssetsDocumentExtendMapper;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsDocumentDetailMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsDocumentMapper;
import com.gz.eim.am.stock.dto.request.assets.AssetTransferHeadReqDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetTransferLineReqDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsDocumentHeadRespDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsDocumentLineRespDTO;
import com.gz.eim.am.stock.dto.response.file.StockAttachDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.StockAssetsInfo;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.entity.vo.download.ExportAssetsDocumentDetailEntity;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetTransferService;
import com.gz.eim.am.stock.service.assets.StockAssetsOperationLogService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.file.StockFileCommonService;
import com.gz.eim.am.stock.service.wfl.WflService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.common.OrderUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/5/7
 * @description 资产转移业务处理类
 */
@Service
@Slf4j
public class StockAssetTransferServiceImpl implements StockAssetTransferService {
    @Lazy
    @Autowired
    private SysUserMapper sysUserMapper;
    @Lazy
    @Autowired
    private StockAssetsOperationLogService operationLogService;
    @Autowired
    private FileServiceApi fileServiceApi;

    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Autowired
    private StockAssetsDocumentMapper stockAssetsDocumentMapper;

    @Autowired
    private StockAssetsDocumentDetailMapper stockAssetsDocumentDetailMapper;

    @Autowired
    private AssetsDocumentExtendMapper assetsDocumentExtendMapper;

    @Autowired
    private AssetsMapper assetsMapper;

    @Autowired
    private WflService wflService;

    @Autowired
    private StockAssetsService stockAssetsService;

    @Autowired
    private StockFileCommonService stockFileCommonService;


    @Value("${project.file.filePath}")
    private String filePath;
    @Value("${project.file.systemModule}")
    private String systemModule;
    @Value("${project.file.attachModule}")
    private String attachModule;

    @Override
    public ResponseData upload(MultipartFile[] files, JwtUser user) {

        if (files == null || files.length < 1) {
            throw new ServiceUncheckedException(ResponseCode.FILE_NOT_EXISTS_ERROR.getMessage());
        }
        UploadFileReqDTO uploadFileReqDTO = new UploadFileReqDTO();
        uploadFileReqDTO.setFilePath(filePath);
        uploadFileReqDTO.setSystemModule(systemModule);
        uploadFileReqDTO.setAttachModule(attachModule);
        return fileServiceApi.upload(files, uploadFileReqDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData save(AssetTransferHeadReqDTO assetTransferHeadReqDTO, JwtUser user) {
        //校验参数
        String checkSaveParam = checkSaveParam(assetTransferHeadReqDTO, user);
        if (StringUtils.isNotBlank(checkSaveParam)) {
            return ResponseData.createFailResult(checkSaveParam);
        }
        log.info("资产转移保存校验参数已通过》》");
        //准备入库数据
        StockAssetsDocument stockAssetsDocument = new StockAssetsDocument();
        List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = new ArrayList<>();
        prepareSaveDbBeanDTO(assetTransferHeadReqDTO, stockAssetsDocument, stockAssetsDocumentDetailList, user);
        //保存转移头信息
        stockAssetsDocumentMapper.insert(stockAssetsDocument);
        //保存转移行信息
        assetsDocumentExtendMapper.batchInsertStockAssetsDocumentDetail(stockAssetsDocumentDetailList);
        //更新附件表业务字段
        List<SysAttachReqDTO> attachDTOList = new ArrayList<>();
        if (assetTransferHeadReqDTO.getAttachIds() != null && assetTransferHeadReqDTO.getAttachIds().size() > 0) {
            assetTransferHeadReqDTO.getAttachIds().forEach(dto -> {
                SysAttachReqDTO attach = new SysAttachReqDTO();
                attach.setAttachId(dto);
                attach.setPageModule(FileConstant.PAGE_MODE_STOCK_ASSETS_DOCUMENT);
                attach.setRelId(stockAssetsDocument.getDocumentNo());
                attachDTOList.add(attach);
            });
            //服务调用失败，保存操作整体失败
            ResponseData resp = fileServiceApi.updateSysAttachList(attachDTOList);
            if (ResponseCode.SYSTEM_ERROR.getCode().equals(resp.getCode())) {
                log.error("调用服务fileServiceApi失败，返回参数" + resp.getMessage());
                throw new ServiceUncheckedException(resp.getMessage());
            }
        }

        //调用审批工作流
        startWorkFlow(stockAssetsDocument, user, FlowCodeEnum.ASSETS_TRANSFER, null);

        return ResponseData.createSuccessResult();

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData saveHolder(AssetTransferHeadReqDTO assetTransferHeadReqDTO, JwtUser user) {
        //校验参数
        String checkSaveParam = checkHolderSaveParam(assetTransferHeadReqDTO, user);
        if (StringUtils.isNotBlank(checkSaveParam)) {
            return ResponseData.createFailResult(checkSaveParam);
        }
        //准备入库数据
        StockAssetsDocument stockAssetsDocument = new StockAssetsDocument();
        List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = new ArrayList<>();
        prepareSaveDbBeanDTO(assetTransferHeadReqDTO, stockAssetsDocument, stockAssetsDocumentDetailList, user);
        List<String> assetsCodeList = stockAssetsDocumentDetailList.stream().map(stockAssetsDocumentDetail -> stockAssetsDocumentDetail.getAssetsCode()).collect(Collectors.toList());
        // 根据codeList查询code
        //Map<String,StockAssets> assetsMap = assetsMapper.selectAssetsByCodes(assetsCodeList).stream().collect(Collectors.toMap(StockAssets ::getAssetsCode, assets -> assets,(v1, v2) -> v1));
        //保存转移头信息
        stockAssetsDocumentMapper.insert(stockAssetsDocument);
        //保存转移行信息
        assetsDocumentExtendMapper.batchInsertStockAssetsDocumentDetail(stockAssetsDocumentDetailList);
        //更新附件表业务字段
        List<SysAttachReqDTO> attachDTOList = new ArrayList<>();
        if (assetTransferHeadReqDTO.getAttachIds() != null && assetTransferHeadReqDTO.getAttachIds().size() > 0) {
            assetTransferHeadReqDTO.getAttachIds().forEach(dto -> {
                SysAttachReqDTO attach = new SysAttachReqDTO();
                attach.setAttachId(dto);
                attach.setPageModule(FileConstant.PAGE_MODE_STOCK_ASSETS_DOCUMENT);
                attach.setRelId(stockAssetsDocument.getDocumentNo());
                attachDTOList.add(attach);
            });
            //服务调用失败，保存操作整体失败
            ResponseData resp = fileServiceApi.updateSysAttachList(attachDTOList);
            if (ResponseCode.SYSTEM_ERROR.getCode().equals(resp.getCode())) {
                log.error("调用服务fileServiceApi失败，返回参数" + resp.getMessage());
                throw new ServiceUncheckedException(resp.getMessage());
            }
        }
        String loginUser = user.getEmployeeCode();
        //员工自助需要调用审批流
        if (AssetTransferEnum.TransferMethod.STAFF_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
            startWorkFlow(stockAssetsDocument, user, FlowCodeEnum.ASSETS_TRANSFER_HOLDER, stockAssetsDocumentDetailList);
            // 同时把conditions修改为转移中
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setConditions(AssetsEnum.Conditions.TRANSFER.getValue());
            assetsSearchDTO.setUpdatedBy(loginUser);
            stockAssetsService.batchUpdateAssetsSameMessage(assetsSearchDTO, assetsCodeList);
            //管理员转移直接更新
        } else if (AssetTransferEnum.TransferMethod.MANAGER_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
            //todo 更新资产使用人
            // 更新单据
            updateStockAssetsDocumentStatus(stockAssetsDocument.getDocumentNo());
        }
        // 日志位置调整
        List<StockAssets> stockAssetsList = assetsMapper.selectAssetsByCodes(assetsCodeList);
        for (StockAssets stockAssets : stockAssetsList) {
            stockAssets.setUpdatedBy(loginUser);
            stockAssets.setCreatedBy(loginUser);
        }
        operationLogService.insertMultipleOperationLog(stockAssetsList, AssetsEnum.operationType.BUSINESS);
        return ResponseData.createSuccessResult();
    }

    /**
     * @param:
     * @description: 管理员处理后更新资产状态
     * @return:
     * @author: <EMAIL>
     * @date: 2021/8/5
     */
    private void updateStockAssetsDocumentStatus(String documentNo) {
        //资产卡片持有人更新为新持有人
        assetsDocumentExtendMapper.batchUpdateAssetHolderByDocumentNo(documentNo);
    }

    /**
     * @param:
     * @description: 参数校验
     * @return:
     * @author: <EMAIL>
     * @date: 2021/7/28
     */
    private String checkSelectAssetsTransferOutParam(AssetTransferHeadReqDTO assetTransferHeadReqDTO) {
        // 判断类型是否为空
        StringBuilder stringBuilder = new StringBuilder();
        if (assetTransferHeadReqDTO.getType() == null) {
            stringBuilder.append("请输入单据类型：1:资产管理员转移，2:资产领用人转移，此处为：2");
        }
        if (stringBuilder.length() != 0) {
            return stringBuilder.toString();
        }
        return null;
    }

    @Override
    public ResponseData selectAssetsTransferOut(AssetTransferHeadReqDTO assetTransferHeadReqDTO) {
        // 判断传入参数是否合法
        String checkResult = checkSelectAssetsTransferOutParam(assetTransferHeadReqDTO);
        if (StringUtils.isNotBlank(checkResult)) {
            return ResponseData.createFailResult(checkResult);
        }
        //获取总条数
        /*StockAssetsDocumentExample StockAssetsDocumentExample = new StockAssetsDocumentExample();
        StockAssetsDocumentExample.Criteria criteria = StockAssetsDocumentExample.createCriteria();
        if (null != assetTransferHeadReqDTO.getReasonCode()){
            criteria.andReasonCodeEqualTo(assetTransferHeadReqDTO.getReasonCode());
        }
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getDocumentNo())){
            criteria.andDocumentNoEqualTo(assetTransferHeadReqDTO.getDocumentNo());
        }
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getBillingUser())){
            criteria.andBillingUserEqualTo(assetTransferHeadReqDTO.getBillingUser());
        }
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getBeginBillingTime()) && StringUtils.isNotBlank(assetTransferHeadReqDTO.getEndBillingTime())){
            try{
                Date beginDate = DateUtils.dateParse(assetTransferHeadReqDTO.getBeginBillingTime(),"yyyy-MM-dd");
                Date endDate = DateUtils.dateParse(assetTransferHeadReqDTO.getEndBillingTime(),"yyyy-MM-dd");
                criteria.andCreatedAtBetween(beginDate,endDate);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        long count = stockAssetsDocumentMapper.countByExample(StockAssetsDocumentExample);*/
        long count = assetsDocumentExtendMapper.selectPageCount(assetTransferHeadReqDTO);
        //设置分页数据
        assetTransferHeadReqDTO.initPageParam();
        /*String orderByClause = " id desc";
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getSortColumns())) {
            orderByClause = assetTransferHeadReqDTO.getSortColumns();
        }
        StockAssetsDocumentExample.setOrderByClause(orderByClause);*/
        //分页查询获取数据列表
        //List<StockAssetsDocument> stockAssetsDocumentList = stockAssetsDocumentMapper.selectByExample(StockAssetsDocumentExample);
        List<StockAssetsDocument> stockAssetsDocumentList = assetsDocumentExtendMapper.selectPage(assetTransferHeadReqDTO);
        List<StockAssetsDocumentHeadRespDTO> stockAssetsDocumentHeadRespDTOS = new ArrayList<>();
        HashSet<String> empIds = new HashSet<>();
        stockAssetsDocumentList.stream().forEach(dto -> {
            StockAssetsDocumentHeadRespDTO stockAssetsDocumentHeadRespDTO = new StockAssetsDocumentHeadRespDTO();
            BeanUtils.copyProperties(dto, stockAssetsDocumentHeadRespDTO);
            stockAssetsDocumentHeadRespDTOS.add(stockAssetsDocumentHeadRespDTO);
            empIds.add(dto.getBillingUser());
            empIds.add(dto.getNewAssetsKeeper());
            empIds.add(dto.getNewAssetsHolder());
        });
        List<String> impIdList = new ArrayList<>();
        impIdList.addAll(empIds);

        Map<String, SysUser> userMap = ambaseCommonService.selectSysUserMapByIds(impIdList);
        for (StockAssetsDocumentHeadRespDTO stockAssetsDocumentHeadRespDTO : stockAssetsDocumentHeadRespDTOS) {
            stockAssetsDocumentHeadRespDTO.setBillingUserName(userMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()) != null ? userMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()).getName() : null);
            stockAssetsDocumentHeadRespDTO.setNewAssetsKeeperName(userMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper()) != null ? userMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper()).getName() : null);
            stockAssetsDocumentHeadRespDTO.setNewAssetsHolderName(userMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()) != null ? userMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()).getName() : null);
        }
        //返回结果
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        pageRespDTO.setCount(count);
        pageRespDTO.setPageNum(assetTransferHeadReqDTO.getPageNum());
        pageRespDTO.setPageSize(assetTransferHeadReqDTO.getPageSize());
        pageRespDTO.setStartNum(assetTransferHeadReqDTO.getStartNum());
        pageRespDTO.setData(stockAssetsDocumentHeadRespDTOS);
        return pageRespDTO;
    }

    @Override
    public ResponseData selectAssetsDocumentById(long id, String bizId) {
        //创建集合存储员工id
        HashSet<String> empIds = new HashSet<>();
        //查询资产转移头
        StockAssetsDocumentExample StockAssetsDocumentExample = new StockAssetsDocumentExample();
        StockAssetsDocumentExample.Criteria criteria = StockAssetsDocumentExample.createCriteria();
        // 判断是否通过订单id查询
        if (StringUtils.isBlank(bizId)) {
            criteria.andIdEqualTo(id);
        } else {
            criteria.andDocumentNoEqualTo(bizId);
        }
        StockAssetsDocument stockAssetsDocument = stockAssetsDocumentMapper.selectByExample(StockAssetsDocumentExample).get(0);
        StockAssetsDocumentHeadRespDTO stockAssetsDocumentHeadRespDTO = new StockAssetsDocumentHeadRespDTO();
        BeanUtils.copyProperties(stockAssetsDocument, stockAssetsDocumentHeadRespDTO);
        empIds.add(stockAssetsDocumentHeadRespDTO.getBillingUser());
        empIds.add(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper());
        empIds.add(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder());
        //查询资产转移行
        List<StockAssetsDocumentLineRespDTO> stockAssetsDocumentLineRespDTOList = assetsDocumentExtendMapper.selectAssetsDocumentDetailByDocumentNo(stockAssetsDocumentHeadRespDTO.getDocumentNo());
        stockAssetsDocumentLineRespDTOList.stream().forEach(dto -> {
            AssetsEnum.statusType[] statusTypes = AssetsEnum.statusType.values();
            for (int i = 0; i < statusTypes.length; i++) {
                if (statusTypes[i].getValue().equals(dto.getAssetStatus())) {
                    dto.setAssetStatusName(statusTypes[i].getDesc());
                    break;
                }
            }
            dto.setTransferStatusName(AssetTransferEnum.LineStatus.FINISH.getCode().equals(dto.getTransferStatus()) ? AssetTransferEnum.LineStatus.FINISH.getValue() : AssetTransferEnum.LineStatus.NO_FINISH.getValue());
            empIds.add(dto.getOldHolder());
            empIds.add(dto.getHolder());
            empIds.add(dto.getNewAssetsKeeper());
            empIds.add(dto.getOldAssetsKeeper());
        });
        List<String> empIdList = new ArrayList<>();
        empIdList.addAll(empIds);
        //为返回对象赋值员工名称
        //Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        Map<String, SysUserBasicInfo> sysUserMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(empIdList);
        stockAssetsDocumentHeadRespDTO.setNewAssetsKeeperName(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsKeeper()).getName() : "");
        stockAssetsDocumentHeadRespDTO.setBillingUserName(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()).getName() : null);
        stockAssetsDocumentHeadRespDTO.setHpsJobcdDescr(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getBillingUser()).getHpsJobcdDescr() : "");
        stockAssetsDocumentHeadRespDTO.setNewAssetsHolderName(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()).getName() : null);
        stockAssetsDocumentHeadRespDTO.setNewHpsJobcdDescr(sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()) != null ? sysUserMap.get(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder()).getHpsJobcdDescr() : "");
        stockAssetsDocumentLineRespDTOList.stream().forEach(dto -> {
            dto.setNewAssetsKeeperName(sysUserMap.get(dto.getNewAssetsKeeper()) != null ? sysUserMap.get(dto.getNewAssetsKeeper()).getName() : null);
            dto.setOldAssetsKeeperName(sysUserMap.get(dto.getOldAssetsKeeper()) != null ? sysUserMap.get(dto.getOldAssetsKeeper()).getName() : null);
            dto.setHolderName(sysUserMap.get(dto.getOldHolder()) != null ? sysUserMap.get(dto.getOldHolder()).getName() : null);
        });
        // 设置旧资产持有人
        if (StringUtils.isNotEmpty(stockAssetsDocumentLineRespDTOList.get(0).getOldHolder())) {
            SysUserBasicInfo oldUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockAssetsDocumentLineRespDTOList.get(0).getOldHolder());
            stockAssetsDocumentHeadRespDTO.setHolder(oldUserBasicInfo.getEmpId());
            stockAssetsDocumentHeadRespDTO.setHolderName(oldUserBasicInfo.getName());
            stockAssetsDocumentHeadRespDTO.setDeptCode(oldUserBasicInfo.getDeptId());
            stockAssetsDocumentHeadRespDTO.setDeptName(oldUserBasicInfo.getDeptName());
            stockAssetsDocumentHeadRespDTO.setHpsJobcdDescr(oldUserBasicInfo.getHpsJobcdDescr());
            stockAssetsDocumentHeadRespDTO.setDeptFullId(oldUserBasicInfo.getDeptFullId());
            stockAssetsDocumentHeadRespDTO.setDeptFullName(oldUserBasicInfo.getDeptFullName());
        }
        // 设置新资产持有人
        if (StringUtils.isNotEmpty(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder())) {
            SysUserBasicInfo newUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(stockAssetsDocumentHeadRespDTO.getNewAssetsHolder());
            stockAssetsDocumentHeadRespDTO.setNewDeptCode(newUserBasicInfo.getDeptId());
            stockAssetsDocumentHeadRespDTO.setNewDeptName(newUserBasicInfo.getDeptName());
            stockAssetsDocumentHeadRespDTO.setNewHpsJobcdDescr(newUserBasicInfo.getHpsJobcdDescr());
            stockAssetsDocumentHeadRespDTO.setNewDeptFullId(newUserBasicInfo.getDeptFullId());
            stockAssetsDocumentHeadRespDTO.setNewDeptFullName(newUserBasicInfo.getDeptFullName());
        }
        stockAssetsDocumentHeadRespDTO.setStockAssetsDocumentLineRespDTOs(stockAssetsDocumentLineRespDTOList);
        //调用接口查询资产附件信息
        List<StockAttachDTO> stockAttachDTOList = stockFileCommonService.selectFileAttachsByNo(stockAssetsDocumentHeadRespDTO.getDocumentNo(), FileConstant.PAGE_MODE_STOCK_ASSETS_DOCUMENT);
        if (stockAttachDTOList != null) {
            stockAssetsDocumentHeadRespDTO.setStockAttachDTOs(stockAttachDTOList);
        }

        //返回结果
        return ResponseData.createSuccessResult(stockAssetsDocumentHeadRespDTO);
    }

    @Override
    public List<ExportAssetsDocumentDetailEntity> selectAssetsDetailEntity(String documentNo) {
        List<StockAssetsDocumentLineRespDTO> stockAssetsDocumentLineRespDTOList = assetsDocumentExtendMapper.selectAssetsDocumentDetailByDocumentNo(documentNo);
        List<ExportAssetsDocumentDetailEntity> exportAssetsDocumentDetailEntities = new ArrayList<>();
        HashSet<String> empIds = new HashSet<>();
        stockAssetsDocumentLineRespDTOList.stream().forEach(dto -> {
            empIds.add(dto.getOldAssetsKeeper());
            empIds.add(dto.getHolder());
            empIds.add(dto.getNewAssetsKeeper());
        });
        List<String> empIdList = new ArrayList<>();
        empIdList.addAll(empIds);
        //为返回对象赋值员工名称
        Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdList);

        for (StockAssetsDocumentLineRespDTO stockAssetsDocumentLineRespDTO : stockAssetsDocumentLineRespDTOList) {
            stockAssetsDocumentLineRespDTO.setHolder(sysUserMap.get(stockAssetsDocumentLineRespDTO.getHolder()) != null ?
                    sysUserMap.get(stockAssetsDocumentLineRespDTO.getHolder()).getName() + " " + stockAssetsDocumentLineRespDTO.getHolder() : "");
            stockAssetsDocumentLineRespDTO.setOldAssetsKeeper(sysUserMap.get(stockAssetsDocumentLineRespDTO.getOldAssetsKeeper()) != null ?
                    sysUserMap.get(stockAssetsDocumentLineRespDTO.getOldAssetsKeeper()).getName() + " " + stockAssetsDocumentLineRespDTO.getOldAssetsKeeper() : "");
            stockAssetsDocumentLineRespDTO.setNewAssetsKeeper(sysUserMap.get(stockAssetsDocumentLineRespDTO.getNewAssetsKeeper()) != null ?
                    sysUserMap.get(stockAssetsDocumentLineRespDTO.getNewAssetsKeeper()).getName() + " " + stockAssetsDocumentLineRespDTO.getNewAssetsKeeper() : "");
            ExportAssetsDocumentDetailEntity exportAssetsDocumentDetailEntity = new ExportAssetsDocumentDetailEntity(stockAssetsDocumentLineRespDTO);
            exportAssetsDocumentDetailEntities.add(exportAssetsDocumentDetailEntity);
        }

        return exportAssetsDocumentDetailEntities;
    }


    /**
     * 校验资产转移入库参数
     *
     * @param assetTransferHeadReqDTO
     * @param user
     * @return
     */
    private String checkSaveParam(AssetTransferHeadReqDTO assetTransferHeadReqDTO, JwtUser user) {
        if (null == assetTransferHeadReqDTO) {
            return "必填参数为空";
        }

        if (null == assetTransferHeadReqDTO.getReasonCode()) {
            return "资产转移原因不能为空";
        }

        if (null == assetTransferHeadReqDTO.getAssetTransferLineReqDTOS() || assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().size() < 1) {
            return "资产转移行不能为空";
        }

        if (!AssetTransferEnum.Reason.allReasonCode().contains(assetTransferHeadReqDTO.getReasonCode())) {
            return "资产转移原因值没有被定义";
        }

        List<String> empIds = new ArrayList<String>();
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getAssetsKeeper())) {
            empIds.add(assetTransferHeadReqDTO.getAssetsKeeper());
            List<SysUser> userList = ambaseCommonService.selectUsersByIds(empIds);
            if (userList == null || userList.size() < 1 || AssetTransferEnum.EMP_JOB.NO_JOB.getCode().equals(userList.get(0).getStatus())) {
                return "资产转移头中的新管理员在系统不存在或已经离职";
            }
            empIds.clear();
        }

        StringBuilder sb = new StringBuilder();
        List<String> assetsCodes = new ArrayList<String>();
        for (int i = 0; i < assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().size(); i++) {
            AssetTransferLineReqDTO assetTransferLineReqDTO = assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().get(i);
            if (!StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsKeeper()) && !StringUtils.isNotBlank(assetTransferHeadReqDTO.getAssetsKeeper())) {
                sb.append("资产转移头和第" + (i + 1) + "行中的新资产管理员不能同时为空；");
            }

            if (!StringUtils.isNotBlank(assetTransferLineReqDTO.getAssetsCode())) {
                sb.append("资产转移第" + (i + 1) + "行中资产编码不能为空；");
            } else {
                assetsCodes.add(assetTransferLineReqDTO.getAssetsCode());
            }

            if (StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsKeeper())) {
                empIds.add(assetTransferLineReqDTO.getNewAssetsKeeper());
                List<SysUser> userList = ambaseCommonService.selectUsersByIds(empIds);
                if (userList == null || userList.size() < 1 || AssetTransferEnum.EMP_JOB.NO_JOB.getCode().equals(userList.get(0).getStatus())) {
                    sb.append("资产转移第" + (i + 1) + "行中的新管理员在系统不存在或已经离职;");
                }
                empIds.clear();
            }
        }

        //校验行中资产编码是否有不存在的情况
        List<String> existAssetsCode = assetsMapper.selectNoExistsCodeByCodes(assetsCodes);
        assetsCodes.removeAll(existAssetsCode);
        if (assetsCodes.size() > 0) {
            assetsCodes.stream().forEach(code -> {
                sb.append("系统中不存在资产编码：" + code + ";");
            });
        }

        if (sb.length() > 0) {
            return sb.toString();
        }

        return null;
    }


    /**
     * 校验资产转移入库参数
     *
     * @param assetTransferHeadReqDTO
     * @param user
     * @return
     */
    private String checkHolderSaveParam(AssetTransferHeadReqDTO assetTransferHeadReqDTO, JwtUser user) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        if (null == assetTransferHeadReqDTO) {
            return "必填参数为空";
        }

        if (null == assetTransferHeadReqDTO.getReasonCode()) {
            return "资产转移原因不能为空";
        }

        if (null == assetTransferHeadReqDTO.getAssetTransferLineReqDTOS() || assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().size() < 1) {
            return "资产转移行不能为空";
        }

        if (!AssetTransferEnum.Reason.allReasonCode().contains(assetTransferHeadReqDTO.getReasonCode())) {
            return "资产转移原因值没有被定义";
        }

        if (null == assetTransferHeadReqDTO.getTransferMethod() || StringUtils.isBlank(AssetTransferEnum.transferMethodMap.get(assetTransferHeadReqDTO.getTransferMethod()))) {
            return "转移方式不正确!";
        }

        if (StringUtils.isBlank(assetTransferHeadReqDTO.getNewAssetsHolder()) || StringUtils.isBlank(assetTransferHeadReqDTO.getNewHolderAddress())) {
             return "新持有人或新持有人地址不能为空";
        }

        // 判断新资产持有人是否是正式和正式外迁人员
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(assetTransferHeadReqDTO.getNewAssetsHolder());
        if (null == sysUserBasicInfo) {
            return "新持有人不存在";
        }
        if(AssetTransferEnum.EMP_JOB.NO_JOB.getCode().equals(sysUserBasicInfo.getStatus())){
            return "新持有人已经离职";
        }
        // 判断领用人是否为外包和实习生
        Integer empType = sysUserBasicInfo.getEmpType();
        if(!SysUserEnum.empType.FORMAL.getValue().equals(empType) && !SysUserEnum.empType.OUTSOURCING.getValue().equals(empType)){
            return "新持有人只能是正式或正式外签员工";
        }

        StringBuilder sb = new StringBuilder();
        List<String> assetsCodes = new ArrayList<String>();
        List<String> assetsDocumentCodes = new ArrayList<String>();
        for (int i = 0; i < assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().size(); i++) {
            AssetTransferLineReqDTO assetTransferLineReqDTO = assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().get(i);
            if (!StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsKeeper()) && !StringUtils.isNotBlank(assetTransferHeadReqDTO.getAssetsKeeper()) && !StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsHolder()) && !StringUtils.isNotBlank(assetTransferHeadReqDTO.getNewAssetsHolder())) {
                sb.append("资产转移头和第" + (i + 1) + "行中的新资产管理员不能同时为空；或者新资产持有人头和行不能同时为空");
            }

            if (!StringUtils.isNotBlank(assetTransferLineReqDTO.getAssetsCode())) {
                sb.append("资产转移第" + (i + 1) + "行中资产编码不能为空；");
            } else {
                assetsCodes.add(assetTransferLineReqDTO.getAssetsCode());
                assetsDocumentCodes.add(assetTransferLineReqDTO.getAssetsCode());
            }

//            if(StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsKeeper())){
//                empIds.add(assetTransferLineReqDTO.getNewAssetsKeeper());
//                List<SysUser> userList = ambaseCommonService.selectUsersByIds(empIds);
//                if(userList == null || userList.size()<1 || AssetTransferEnum.EMP_JOB.NO_JOB.getCode().equals(userList.get(0).getStatus())){
//                    sb.append("资产转移第" + (i + 1) + "行中的新管理员在系统不存在或已经离职;");
//                }
//                empIds.clear();
//            }
        }

        //校验行中资产编码是否有不存在的情况
        List<StockAssets> stockAssetsList = assetsMapper.selectAssetsByCodes(assetsCodes);
        if (stockAssetsList.size() <= CommonConstant.NUMBER_ZERO) {
            return "所有资产编码均不存在";
        }
        // 判断资产合法
        for (StockAssets stockAssets : stockAssetsList) {
            String assetsCode = stockAssets.getAssetsCode();
            // 删除存在的值
            assetsCodes.remove(assetsCode);
            // 判断资产状态
            if(!AssetsEnum.statusType.USED.getValue().equals(stockAssets.getStatus())){
                sb.append("资产：" + assetsCode + "的状态不是使用中状态，无法进行转移;");
            }
            if(!AssetsEnum.Conditions.NORMAL.getValue().equals(stockAssets.getConditions())){
                sb.append("资产：" + assetsCode + "的使用情况不是正常，无法进行转移;");
            }
            if(!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAssets.getApproveStatus())){
                sb.append("资产：" + assetsCode + "已经报废或者在报废/IT工单审批中;");
            }
        }
        if(sb.length() > 0){
            return sb.toString();
        }
        // 判断资产编码是否都存在
        if(CollectionUtils.isNotEmpty(assetsCodes)){
            assetsCodes.stream().forEach(code -> {
                sb.append("系统中不存在资产编码：" + code + ";");
            });
            return sb.toString();
        }
        // 判断是不是自己转移给自己
        if (assetTransferHeadReqDTO.getTransferMethod() != null) {
            String holder = "";
            if (AssetTransferEnum.TransferMethod.MANAGER_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                holder = assetTransferHeadReqDTO.getHolder();
            } else if (AssetTransferEnum.TransferMethod.STAFF_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                holder = jwtUser.getEmployeeCode();
            }
            String newHolder = StringUtils.isNotBlank(assetTransferHeadReqDTO.getNewAssetsHolder()) ? assetTransferHeadReqDTO.getNewAssetsHolder() : assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().get(0).getNewAssetsHolder();
            if (holder.equals(newHolder)) {
                sb.append("请不要尝试将自己的资产转移给自己！！！");
            }
        }
        // 资产转移判断审批单中是否已经提交过这个单据，防止前端漏检，以及App端PC同时操作
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getNewAssetsHolder()) && StringUtils.isBlank(assetTransferHeadReqDTO.getAssetsKeeper())) {
            // 获取当前登陆人已经在审批中的资产编码列表
            StockAssetsDocumentLineRespDTO stockAssetsDocumentLineRespDTO = new StockAssetsDocumentLineRespDTO();
            stockAssetsDocumentLineRespDTO.setHeadStatus(AssetTransferEnum.Status.APPROVE.getCode());
            stockAssetsDocumentLineRespDTO.setAssetsCodeList(assetsDocumentCodes);
            List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList = stockAssetsDocumentDetailMapper.selectByStockAssetsDocumentLineReq(stockAssetsDocumentLineRespDTO);
            // 获取审核中行单
            Set<String> exitsAssetsCode = new HashSet<>();
            if (CollectionUtils.isNotEmpty(stockAssetsDocumentDetailList)) {
                stockAssetsDocumentDetailList.forEach(stockAssetsDocumentDetail -> {
                    if (exitsAssetsCode.add(stockAssetsDocumentDetail.getAssetsCode())) {
                        sb.append("资产编码为：" + stockAssetsDocumentDetail.getAssetsCode() + " 的资产已经在审批中了，请勿重复提交！！！\n");
                    }
                });
            }
        }
        // 判断资产是不是当前持有人的，防止前端漏检，以及App端PC同时操作
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getNewAssetsHolder()) && StringUtils.isBlank(assetTransferHeadReqDTO.getAssetsKeeper())) {
            String holder = null;
            // 获取当前登陆人所有的资产
            if (AssetTransferEnum.TransferMethod.MANAGER_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                holder = assetTransferHeadReqDTO.getHolder();
            } else if (AssetTransferEnum.TransferMethod.STAFF_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                holder = jwtUser.getEmployeeCode();
            }
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setHolder(holder);
            List<StockAssetsInfo> stockAssetsInfoList = assetsMapper.selectAssetByTypeAndHolder(assetsSearchDTO);
            List<String> oldAssetsCodeList = stockAssetsInfoList.stream().map(oldAssetsCode -> oldAssetsCode.getAssetsCode()).collect(Collectors.toList());
            assetsDocumentCodes.forEach(assetsNewCodes -> {
                if (!oldAssetsCodeList.contains(assetsNewCodes)) {
                    sb.append("资产编码为：" + assetsNewCodes + " 的资产已经不是本人资产，无权限转移！！！\n");
                }
            });
        }
        // 资产的资产是否还属于本人
        if (sb.length() > 0) {
            return sb.toString();
        }
        return null;
    }

    /**
     * 保存资产转移单赋值
     *
     * @param assetTransferHeadReqDTO
     * @param stockAssetsDocument
     * @param stockAssetsDocumentDetailList
     * @param user
     */
    private void prepareSaveDbBeanDTO(AssetTransferHeadReqDTO assetTransferHeadReqDTO, StockAssetsDocument stockAssetsDocument, List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList, JwtUser user) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //获取单据编号
        String documentNo = getDocumentNo();

        //转移头赋值
        if (StringUtils.isNotBlank(assetTransferHeadReqDTO.getNeedTime())) {
            stockAssetsDocument.setNeedTime(DateTimeUtil.strToDate(assetTransferHeadReqDTO.getNeedTime()));
        }
        stockAssetsDocument.setDocumentNo(documentNo);
        stockAssetsDocument.setBillingTime(new Date());
        stockAssetsDocument.setBillingUser(user.getEmployeeCode());
        stockAssetsDocument.setCreatedAt(new Date());
        stockAssetsDocument.setCreatedBy(user.getEmployeeCode());
        stockAssetsDocument.setDelFlag(AssetTransferEnum.DelFlag.NO_DELETE.getCode());
        stockAssetsDocument.setReasonCode(assetTransferHeadReqDTO.getReasonCode());
        stockAssetsDocument.setRemark(!StringUtils.isNotBlank(assetTransferHeadReqDTO.getRemark()) ? "" : assetTransferHeadReqDTO.getRemark());
        if (null != assetTransferHeadReqDTO.getTransferMethod()) {
            //管理员转移资产持有人不需要审批
            if (AssetTransferEnum.TransferMethod.MANAGER_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                stockAssetsDocument.setHeadStatus(AssetTransferEnum.Status.SECTION_IN.getCode());
            } else if (AssetTransferEnum.TransferMethod.STAFF_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                stockAssetsDocument.setHeadStatus(AssetTransferEnum.Status.APPROVE.getCode());
            }
            stockAssetsDocument.setTransferMethod(assetTransferHeadReqDTO.getTransferMethod());
            stockAssetsDocument.setType(AssetTransferEnum.Type.AUTO_ASSET_HOLDER_RANSFER.getCode());
        } else {
            stockAssetsDocument.setHeadStatus(AssetTransferEnum.Status.APPROVE.getCode());
            stockAssetsDocument.setType(AssetTransferEnum.Type.ASSET_RANSFER.getCode());

        }

        stockAssetsDocument.setNewAssetsHolder(StringUtils.isNotBlank(assetTransferHeadReqDTO.getNewAssetsHolder()) ? assetTransferHeadReqDTO.getNewAssetsHolder() : assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().get(0).getNewAssetsHolder());
        stockAssetsDocument.setNewAssetsHolderAddress(StringUtils.isNotBlank(assetTransferHeadReqDTO.getNewHolderAddress()) ? assetTransferHeadReqDTO.getNewHolderAddress() : "");
        stockAssetsDocument.setNewAssetsKeeper(StringUtils.isNotBlank(assetTransferHeadReqDTO.getAssetsKeeper()) ? assetTransferHeadReqDTO.getAssetsKeeper() : "");
        stockAssetsDocument.setUpdatedAt(new Date());
        stockAssetsDocument.setUpdatedBy(user.getEmployeeCode());

        List<String> assetsCodes = new ArrayList<>();
        assetTransferHeadReqDTO.getAssetTransferLineReqDTOS().stream().forEach(dto -> {
            assetsCodes.add(dto.getAssetsCode());
        });
        Map<String, StockAssets> assetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodes);
        String employeeCode = jwtUser.getEmployeeCode();
        // 查询登陆人信息
        SysUserBasicInfo sysUserBasicInfo = sysUserMapper.queryUserBasicInfoByEmpId(employeeCode);
        //转移行赋值
        for (AssetTransferLineReqDTO assetTransferLineReqDTO : assetTransferHeadReqDTO.getAssetTransferLineReqDTOS()) {
            StockAssetsDocumentDetail stockAssetsDocumentDetail = new StockAssetsDocumentDetail();
            stockAssetsDocumentDetail.setDocumentNo(documentNo);
            stockAssetsDocumentDetail.setAssetsCode(assetTransferLineReqDTO.getAssetsCode());
            stockAssetsDocumentDetail.setCreatedAt(new Date());
            stockAssetsDocumentDetail.setCreatedBy(user.getEmployeeCode());
            stockAssetsDocumentDetail.setDelFlag(AssetTransferEnum.DelFlag.NO_DELETE.getCode());

            //资产所属公司
            stockAssetsDocumentDetail.setOldCompanyCode(assetsMap.get(assetTransferLineReqDTO.getAssetsCode()) != null ? assetsMap.get(assetTransferLineReqDTO.getAssetsCode()).getCompanyCode() : "");
            stockAssetsDocumentDetail.setNewCompanyCode(assetsMap.get(assetTransferLineReqDTO.getAssetsCode()) != null ? assetsMap.get(assetTransferLineReqDTO.getAssetsCode()).getCompanyCode() : "");
            //资产领用人转移
            if (AssetTransferEnum.Type.AUTO_ASSET_HOLDER_RANSFER.getCode().equals(stockAssetsDocument.getType())) {
                // 判断是App还是PC
                if (AssetTransferEnum.TransferMethod.MANAGER_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                    stockAssetsDocumentDetail.setOldHolder(assetTransferHeadReqDTO.getHolder() != null ? assetTransferHeadReqDTO.getHolder() : "");
                } else if (AssetTransferEnum.TransferMethod.STAFF_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                    stockAssetsDocumentDetail.setOldHolder(sysUserBasicInfo.getEmpId());
                }
                // 添加旧部门
                StockAssets stockAssets = assetsMap.get(stockAssetsDocumentDetail.getAssetsCode());
                if(stockAssets != null){
                    stockAssetsDocumentDetail.setOldDeptCode(stockAssets.getHolderDept());
                }
                stockAssetsDocumentDetail.setNewHolder(StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsHolder()) ? assetTransferLineReqDTO.getNewAssetsHolder() : assetTransferHeadReqDTO.getNewAssetsHolder());
                stockAssetsDocumentDetail.setNewDeptCode(StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsHolderDept()) ? assetTransferLineReqDTO.getNewAssetsHolderDept() : assetTransferHeadReqDTO.getNewAssetsHolderDept());
                // 因为是资产转移，所以默认新旧持有人都为原持有人
                stockAssetsDocumentDetail.setNewAssetsKeeper(assetsMap.get(assetTransferLineReqDTO.getAssetsCode()) != null ? assetsMap.get(assetTransferLineReqDTO.getAssetsCode()).getAssetsKeeper() : "");
                stockAssetsDocumentDetail.setOldAssetsKeeper(assetsMap.get(assetTransferLineReqDTO.getAssetsCode()) != null ? assetsMap.get(assetTransferLineReqDTO.getAssetsCode()).getAssetsKeeper() : "");

                stockAssetsDocumentDetail.setOldHolderAddress(assetsMap.get(assetTransferLineReqDTO.getAssetsCode()) != null ? assetsMap.get(assetTransferLineReqDTO.getAssetsCode()).getHolderAddress() : "");
                stockAssetsDocumentDetail.setNewHolderAddress(assetTransferHeadReqDTO.getNewHolderAddress());
                //资产管理员转移
            } else if (AssetTransferEnum.Type.ASSET_RANSFER.getCode().equals(stockAssetsDocument.getType())) {
                stockAssetsDocumentDetail.setOldAssetsKeeper(assetsMap.get(assetTransferLineReqDTO.getAssetsCode()) != null ? assetsMap.get(assetTransferLineReqDTO.getAssetsCode()).getAssetsKeeper() : "");
                stockAssetsDocumentDetail.setNewAssetsKeeper(!StringUtils.isNotBlank(assetTransferLineReqDTO.getNewAssetsKeeper()) ? assetTransferHeadReqDTO.getAssetsKeeper() : assetTransferLineReqDTO.getNewAssetsKeeper());
            }

            //资产管理员转移资产领用人不需要审批
            if (null != assetTransferHeadReqDTO.getTransferMethod() && AssetTransferEnum.TransferMethod.MANAGER_TRANSFER.getCode().equals(assetTransferHeadReqDTO.getTransferMethod())) {
                stockAssetsDocumentDetail.setLineStatus(AssetTransferEnum.LineStatus.FINISH.getCode());
            } else {
                stockAssetsDocumentDetail.setLineStatus(AssetTransferEnum.LineStatus.NO_FINISH.getCode());
            }

            stockAssetsDocumentDetail.setUpdatedAt(new Date());
            stockAssetsDocumentDetail.setUpdatedBy(user.getEmployeeCode());
            stockAssetsDocumentDetailList.add(stockAssetsDocumentDetail);
        }

    }

    /**
     * 获取资产转移单编号
     *
     * @return
     */
    @Override
    public String getDocumentNo() {
        String trNo = OrderUtil.getOrderNo(OrderEnum.DOCUMENT_NO);
        if (StringUtils.isBlank(trNo)) {
            log.error("调用公用生成编号出错，将使用系统默认生成的资产编号》》");
            return "TR" + System.currentTimeMillis();
        }
        return trNo;
    }


    /**
     * 调用工作流审批
     *
     * @param stockAssetsDocument
     */
    private void startWorkFlow(StockAssetsDocument stockAssetsDocument, JwtUser user, FlowCodeEnum flowCodeEnum, List<StockAssetsDocumentDetail> stockAssetsDocumentDetailList) {
        //获取申请人名称
        WflInfo wflInfo = new WflInfo();
        wflInfo.setApplyUser(user.getEmployeeCode());
        wflInfo.setFlowCodeEnum(flowCodeEnum);

        List<Map<String, Object>> dateItemList = new ArrayList<>();

        if (FlowCodeEnum.ASSETS_TRANSFER.equals(flowCodeEnum)) {
            // 需求申请人
            Map<String, Object> map = new HashMap<>(2);
            map.put("key", "billingUser");
            map.put("value", user.getEmployeeName() + " " + user.getEmployeeCode());
            dateItemList.add(map);

            //申请类型
            Map<String, Object> map1 = new HashMap<>(2);
            map1.put("key", "type");
            map1.put("value", AssetTransferEnum.Type.ASSET_RANSFER.getValue());
            dateItemList.add(map1);

            //申请原因
            Map<String, Object> map2 = new HashMap<>(2);
            map2.put("key", "reason");
            AssetTransferEnum.Reason[] assetReason = AssetTransferEnum.Reason.values();
            for (int i = 0; i < assetReason.length; i++) {
                if (assetReason[i].getCode().equals(stockAssetsDocument.getReasonCode())) {
                    map2.put("value", assetReason[i].getValue());
                    break;
                }
            }
            dateItemList.add(map2);

            //转移条数
            StockAssetsDocumentDetailExample example = new StockAssetsDocumentDetailExample();
            StockAssetsDocumentDetailExample.Criteria criteria = example.createCriteria();
            criteria.andDocumentNoEqualTo(stockAssetsDocument.getDocumentNo());
            long count = stockAssetsDocumentDetailMapper.countByExample(example);
            Map<String, Object> map3 = new HashMap<>(2);
            map3.put("key", "count");
            map3.put("value", count);
            dateItemList.add(map3);

            //备注
            Map<String, Object> map4 = new HashMap<>(2);
            map4.put("key", "remark");
            map4.put("value", stockAssetsDocument.getRemark() == null ? "" : stockAssetsDocument.getRemark());
            dateItemList.add(map4);

            //业务线
            Map<String, Object> map6 = new HashMap<>(2);
            map6.put("key", "lobNo");
            map6.put("value", FlowCodeEnum.ASSETS_TRANSFER.getLob());
            dateItemList.add(map6);

            //新资产管理员
            Map<String, Object> map7 = new HashMap<>(2);
            map7.put("key", "newAssetsKeeper");
            map7.put("value", stockAssetsDocument.getNewAssetsKeeper());
            dateItemList.add(map7);

        } else if (FlowCodeEnum.ASSETS_TRANSFER_HOLDER.equals(flowCodeEnum)) {

            Map<String, Object> map1 = new HashMap<>(2);
            map1.put("key", "lobNo");
            map1.put("value", FlowCodeEnum.ASSETS_TRANSFER_HOLDER.getLob());
            dateItemList.add(map1);

            Map<String, Object> map2 = new HashMap<>(2);
            map2.put("key", "newAssetsHolder");
            map2.put("value", stockAssetsDocument.getNewAssetsHolder());
            dateItemList.add(map2);

            // 拼接资产管理员
            StringBuilder stringBuilder = new StringBuilder("");
            if (CollectionUtils.isNotEmpty(stockAssetsDocumentDetailList)) {
                stockAssetsDocumentDetailList.forEach(stockAssetsDocumentDetail -> {
                    stringBuilder.append(stockAssetsDocumentDetail.getNewAssetsKeeper() + ",");
                });
                stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            }
            //新资产管理员
            Map<String, Object> map3 = new HashMap<>(2);
            map3.put("key", "newAssetsKeeper");
            map3.put("value", stringBuilder.toString());
            dateItemList.add(map3);

            // 查询新资产持有人的邮箱
            if(StringUtils.isNotEmpty(stockAssetsDocument.getNewAssetsHolder())){
                List<String> empIdList = new ArrayList<>(CommonConstant.NUMBER_ONE);
                empIdList.add(stockAssetsDocument.getNewAssetsHolder());
                List<SysUser> sysUsers = ambaseCommonService.selectUsersByIds(empIdList);
                if(CollectionUtils.isNotEmpty(sysUsers)){
                    Map<String, Object> map4 = new HashMap<>(2);
                    map4.put("key", "newAssetsHolderEmail");
                    map4.put("value", sysUsers.get(CommonConstant.NUMBER_ZERO).getEmail());
                    dateItemList.add(map4);
                }
            }

        }

        //编号
        Map<String, Object> map = new HashMap<>(2);
        map.put("key", "bizId");
        map.put("value", stockAssetsDocument.getDocumentNo());
        dateItemList.add(map);


        wflInfo.setDateItemList(dateItemList);
        //异步发起工作流发
        wflService.beginAct(wflInfo);
    }
}
