package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockInventoryInPlanHead;
import com.gz.eim.am.stock.entity.StockInventoryInPlanHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockInventoryInPlanHeadMapper {
    long countByExample(StockInventoryInPlanHeadExample example);

    int deleteByPrimaryKey(Long inventoryInPlanHeadId);

    int insert(StockInventoryInPlanHead record);

    int insertSelective(StockInventoryInPlanHead record);

    List<StockInventoryInPlanHead> selectByExample(StockInventoryInPlanHeadExample example);

    StockInventoryInPlanHead selectByPrimaryKey(Long inventoryInPlanHeadId);

    int updateByExampleSelective(@Param("record") StockInventoryInPlanHead record, @Param("example") StockInventoryInPlanHeadExample example);

    int updateByExample(@Param("record") StockInventoryInPlanHead record, @Param("example") StockInventoryInPlanHeadExample example);

    int updateByPrimaryKeySelective(StockInventoryInPlanHead record);

    int updateByPrimaryKey(StockInventoryInPlanHead record);
}