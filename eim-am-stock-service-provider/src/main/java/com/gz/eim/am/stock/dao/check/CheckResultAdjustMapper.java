package com.gz.eim.am.stock.dao.check;

import com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO;
import com.gz.eim.am.stock.dto.response.check.AdjustResultHeadRespDTO;
import com.gz.eim.am.stock.dto.response.check.AdjustResultLineRespDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2021/2/25
 * @description
 */
public interface CheckResultAdjustMapper {
    /**
     * 盘点入库单总数查询
     * @param adjustResultReqDTO
     * @return
     */
    Long queryCheckInventoryCount(AdjustResultReqDTO adjustResultReqDTO);
    /**
     * 盘点入库单明细
     * @param adjustResultReqDTO
     * @return
     */
    List<AdjustResultHeadRespDTO> queryCheckInventory(AdjustResultReqDTO adjustResultReqDTO);
    /**
     * 盘点出库单总数查询
     * @param adjustResultReqDTO
     * @return
     */
    Long queryCheckDeliveryCount(AdjustResultReqDTO adjustResultReqDTO);
    /**
     * 盘点出库单明细
     * @param adjustResultReqDTO
     * @return
     */
    List<AdjustResultHeadRespDTO> queryCheckDelivery(AdjustResultReqDTO adjustResultReqDTO);

    /**
     * 盘点入库单明细
     * @param billNo
     * @return
     */
    long queryCheckAdjustDetailCount(@Param("billNo") String billNo);
    /**
     * 盘点入库单明细
     * @param adjustResultReqDTO
     * @return
     */
    List<AdjustResultLineRespDTO> queryCheckAdjustDetail(AdjustResultReqDTO adjustResultReqDTO);
}
