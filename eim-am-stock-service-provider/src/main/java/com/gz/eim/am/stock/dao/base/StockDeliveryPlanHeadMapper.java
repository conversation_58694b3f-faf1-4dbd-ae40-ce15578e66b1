package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockDeliveryPlanHead;
import com.gz.eim.am.stock.entity.StockDeliveryPlanHeadExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockDeliveryPlanHeadMapper {
    long countByExample(StockDeliveryPlanHeadExample example);

    int deleteByPrimaryKey(Long deliveryPlanHeadId);

    int insert(StockDeliveryPlanHead record);

    int insertSelective(StockDeliveryPlanHead record);

    List<StockDeliveryPlanHead> selectByExample(StockDeliveryPlanHeadExample example);

    StockDeliveryPlanHead selectByPrimaryKey(Long deliveryPlanHeadId);

    int updateByExampleSelective(@Param("record") StockDeliveryPlanHead record, @Param("example") StockDeliveryPlanHeadExample example);

    int updateByExample(@Param("record") StockDeliveryPlanHead record, @Param("example") StockDeliveryPlanHeadExample example);

    int updateByPrimaryKeySelective(StockDeliveryPlanHead record);

    int updateByPrimaryKey(StockDeliveryPlanHead record);
}