package com.gz.eim.am.stock.service.impl.assets;

import com.gz.eim.am.stock.dao.base.StockLeaveApproveUserConfigMapper;
import com.gz.eim.am.stock.dto.request.assets.StockLeaveApproveUserConfigReqDTO;
import com.gz.eim.am.stock.entity.StockLeaveApproveUserConfig;
import com.gz.eim.am.stock.entity.StockLeaveApproveUserConfigExample;
import com.gz.eim.am.stock.service.assets.StockLeaveApproveUserConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @className: StockLeaveApproveUserConfigServiceImpl
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/4/11
 **/
@Service
public class StockLeaveApproveUserConfigServiceImpl implements StockLeaveApproveUserConfigService {

    @Autowired
    private StockLeaveApproveUserConfigMapper stockLeaveApproveUserConfigMapper;

    /**
     * @param: stockLeaveApproveUserConfigReqDTO
     * @description: 查询离职人员配置信息集合
     * @return: List<StockLeaveApproveUserConfig>
     * @author: <EMAIL>
     * @date: 2022/4/11
     */
    @Override
    public List<StockLeaveApproveUserConfig> selectStockLeaveApproveUserConfigList(StockLeaveApproveUserConfigReqDTO stockLeaveApproveUserConfigReqDTO) {
        if(null == stockLeaveApproveUserConfigReqDTO){
            return new ArrayList<>();
        }
        StockLeaveApproveUserConfigExample stockLeaveApproveUserConfigExample = new StockLeaveApproveUserConfigExample();
        StockLeaveApproveUserConfigExample.Criteria criteria = stockLeaveApproveUserConfigExample.createCriteria();
        if(StringUtils.isNotEmpty(stockLeaveApproveUserConfigReqDTO.getProvinceCode())){
            criteria.andProvinceCodeEqualTo(stockLeaveApproveUserConfigReqDTO.getProvinceCode());
        }
        if(stockLeaveApproveUserConfigReqDTO.getStatus() != null){
            criteria.andStatusEqualTo(stockLeaveApproveUserConfigReqDTO.getStatus());
        }
        if(stockLeaveApproveUserConfigReqDTO.getDelFlag() != null){
            criteria.andDelFlagEqualTo(stockLeaveApproveUserConfigReqDTO.getDelFlag());
        }
        return stockLeaveApproveUserConfigMapper.selectByExample(stockLeaveApproveUserConfigExample);
    }
}
