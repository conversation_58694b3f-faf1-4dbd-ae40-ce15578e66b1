package com.gz.eim.am.stock.handler.manager;

import com.gz.eim.am.stock.entity.StockAssetsRepairLine;
import com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend;
import com.gz.eim.am.stock.handler.AbstractStockAssetsRepairAssetsDetailExportExcelHandler;
import com.gz.eim.am.stock.util.em.StockAssetsRepairHeadEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @className: StockAssetsRepairAssetsDetailExportExcelManager
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/12/20
 **/
@Component
public class StockAssetsRepairAssetsDetailExportExcelManager implements ApplicationContextAware {

    private Map<StockAssetsRepairHeadEnum.RepairType, AbstractStockAssetsRepairAssetsDetailExportExcelHandler> handlerMap = new HashMap<>();

    /**
     * @param: applicationContext
     * @description: 初始化handlerMap
     * @return: void
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, AbstractStockAssetsRepairAssetsDetailExportExcelHandler> handlerMap = applicationContext.getBeansOfType(AbstractStockAssetsRepairAssetsDetailExportExcelHandler.class);
        tag:
        for (AbstractStockAssetsRepairAssetsDetailExportExcelHandler handler : handlerMap.values()) {
            Annotation[] annotations = handler.getClass().getAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation.annotationType() == Deprecated.class) {
                    continue tag;
                }
            }
            //注册bean
            register(handler);
        }
    }

    private void register(AbstractStockAssetsRepairAssetsDetailExportExcelHandler handler) {
        handlerMap.put(handler.getRepairType(), handler);
    }

    /**
     * @param: repairType, fileName
     * @param: stockAssetsRepairLineList, stockAssetsRepairLineExtendList
     * @param: request, response
     * @description: 导出资产明细excel
     * @return:
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    public void exportAssetsDetailExcel(StockAssetsRepairHeadEnum.RepairType repairType, String fileName,
                                        List<StockAssetsRepairLine> stockAssetsRepairLineList, Map<String, StockAssetsRepairLineExtend> stockAssetsRepairLineExtendMap,
                                        HttpServletRequest request, HttpServletResponse response, StockAssetsRepairHeadEnum.Status statusEnum) {
        AbstractStockAssetsRepairAssetsDetailExportExcelHandler stockAssetsRepairAssetsDetailExportExcelHandler = handlerMap.get(repairType);
        if(null == stockAssetsRepairAssetsDetailExportExcelHandler){
            return;
        }
        stockAssetsRepairAssetsDetailExportExcelHandler.exportAssetsDetailExcel(fileName, stockAssetsRepairLineList, stockAssetsRepairLineExtendMap, request, response, statusEnum);
    }
}
