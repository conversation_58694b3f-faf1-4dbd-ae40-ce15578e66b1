package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockManageRole;
import com.gz.eim.am.stock.entity.StockManageRoleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockManageRoleMapper {
    long countByExample(StockManageRoleExample example);

    int deleteByPrimaryKey(Long roleId);

    int insert(StockManageRole record);

    int insertSelective(StockManageRole record);

    List<StockManageRole> selectByExample(StockManageRoleExample example);

    StockManageRole selectByPrimaryKey(Long roleId);

    int updateByExampleSelective(@Param("record") StockManageRole record, @Param("example") StockManageRoleExample example);

    int updateByExample(@Param("record") StockManageRole record, @Param("example") StockManageRoleExample example);

    int updateByPrimaryKeySelective(StockManageRole record);

    int updateByPrimaryKey(StockManageRole record);
}