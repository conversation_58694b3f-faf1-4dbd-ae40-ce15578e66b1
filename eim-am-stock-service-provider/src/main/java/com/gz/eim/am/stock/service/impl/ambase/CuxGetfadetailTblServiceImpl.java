package com.gz.eim.am.stock.service.impl.ambase;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.ambase.CuxGetfadetailTblMapper;
import com.gz.eim.am.stock.dto.request.ambase.CuxGetfadetailTblReqDTO;
import com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl;
import com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTblExample;
import com.gz.eim.am.stock.service.ambase.CuxGetfadetailTblService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: CuxGetfadetailTblServiceImpl
 * @description: 查询资产残值Service
 * @author: <EMAIL>
 * @date: 2022/2/15
 **/
@Service
public class CuxGetfadetailTblServiceImpl implements CuxGetfadetailTblService {

    @Autowired
    private CuxGetfadetailTblMapper cuxGetfadetailTblMapper;

     /**
       * @param: cuxGetfadetailTblReqDTO
       * @description: 查询资产残值信息
       * @return: List<CuxGetfadetailTbl>
       * @author: <EMAIL>
       * @date: 2022/2/15
       */
    @Override
    public List<CuxGetfadetailTbl> selectCuxGetfadetailTblList(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO) {
        if(null == cuxGetfadetailTblReqDTO){
            return new ArrayList<>();
        }
        CuxGetfadetailTblExample cuxGetfadetailTblExample = new CuxGetfadetailTblExample();
        CuxGetfadetailTblExample.Criteria criteria = cuxGetfadetailTblExample.createCriteria();
        if(!CollectionUtils.isEmpty(cuxGetfadetailTblReqDTO.getAssetNumberList())){
            criteria.andAssetNumberIn(cuxGetfadetailTblReqDTO.getAssetNumberList());
        }
        if(!CollectionUtils.isEmpty(cuxGetfadetailTblReqDTO.getTagNumberList())){
            criteria.andTagNumberIn(cuxGetfadetailTblReqDTO.getTagNumberList());
        }
        if(StringUtils.isNotBlank(cuxGetfadetailTblReqDTO.getTagNumber())){
            criteria.andTagNumberEqualTo(cuxGetfadetailTblReqDTO.getTagNumber());
        }
        return cuxGetfadetailTblMapper.selectByExample(cuxGetfadetailTblExample);
    }

    @Override
    public CuxGetfadetailTbl selectCuxGetfadetailTbl(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO) {
        List<CuxGetfadetailTbl> cuxGetfadetailTblList = selectCuxGetfadetailTblList(cuxGetfadetailTblReqDTO);
        if(CollectionUtils.isEmpty(cuxGetfadetailTblList)){
            return null;
        }
        return cuxGetfadetailTblList.get(CommonConstant.NUMBER_ZERO);
    }

    @Override
    public Map<String, CuxGetfadetailTbl> selectCuxGetfadetailTblMap(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO) {
        List<CuxGetfadetailTbl> cuxGetfadetailTblList = selectCuxGetfadetailTblList(cuxGetfadetailTblReqDTO);
        if(CollectionUtils.isEmpty(cuxGetfadetailTblList)){
            return new HashMap<>(CommonConstant.NUMBER_TWO);
        }
        return cuxGetfadetailTblList.stream().collect(Collectors.toMap(CuxGetfadetailTbl :: getTagNumber, cuxGetfadetailTbl -> cuxGetfadetailTbl, (k1, k2) -> k2));
    }
}
