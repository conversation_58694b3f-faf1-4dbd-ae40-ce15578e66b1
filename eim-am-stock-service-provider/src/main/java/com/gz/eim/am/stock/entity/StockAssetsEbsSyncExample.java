package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockAssetsEbsSyncExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockAssetsEbsSyncExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNull() {
            addCriterion("assets_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNotNull() {
            addCriterion("assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeEqualTo(String value) {
            addCriterion("assets_code =", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotEqualTo(String value) {
            addCriterion("assets_code <>", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThan(String value) {
            addCriterion("assets_code >", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("assets_code >=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThan(String value) {
            addCriterion("assets_code <", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("assets_code <=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLike(String value) {
            addCriterion("assets_code like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotLike(String value) {
            addCriterion("assets_code not like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIn(List<String> values) {
            addCriterion("assets_code in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotIn(List<String> values) {
            addCriterion("assets_code not in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeBetween(String value1, String value2) {
            addCriterion("assets_code between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("assets_code not between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andQueryCodeIsNull() {
            addCriterion("query_code is null");
            return (Criteria) this;
        }

        public Criteria andQueryCodeIsNotNull() {
            addCriterion("query_code is not null");
            return (Criteria) this;
        }

        public Criteria andQueryCodeEqualTo(String value) {
            addCriterion("query_code =", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotEqualTo(String value) {
            addCriterion("query_code <>", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeGreaterThan(String value) {
            addCriterion("query_code >", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("query_code >=", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeLessThan(String value) {
            addCriterion("query_code <", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeLessThanOrEqualTo(String value) {
            addCriterion("query_code <=", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeLike(String value) {
            addCriterion("query_code like", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotLike(String value) {
            addCriterion("query_code not like", value, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeIn(List<String> values) {
            addCriterion("query_code in", values, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotIn(List<String> values) {
            addCriterion("query_code not in", values, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeBetween(String value1, String value2) {
            addCriterion("query_code between", value1, value2, "queryCode");
            return (Criteria) this;
        }

        public Criteria andQueryCodeNotBetween(String value1, String value2) {
            addCriterion("query_code not between", value1, value2, "queryCode");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNull() {
            addCriterion("sync_status is null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNotNull() {
            addCriterion("sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusEqualTo(Integer value) {
            addCriterion("sync_status =", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotEqualTo(Integer value) {
            addCriterion("sync_status <>", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThan(Integer value) {
            addCriterion("sync_status >", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("sync_status >=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThan(Integer value) {
            addCriterion("sync_status <", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThanOrEqualTo(Integer value) {
            addCriterion("sync_status <=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIn(List<Integer> values) {
            addCriterion("sync_status in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotIn(List<Integer> values) {
            addCriterion("sync_status not in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusBetween(Integer value1, Integer value2) {
            addCriterion("sync_status between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("sync_status not between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIsNull() {
            addCriterion("error_message is null");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIsNotNull() {
            addCriterion("error_message is not null");
            return (Criteria) this;
        }

        public Criteria andErrorMessageEqualTo(String value) {
            addCriterion("error_message =", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotEqualTo(String value) {
            addCriterion("error_message <>", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageGreaterThan(String value) {
            addCriterion("error_message >", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageGreaterThanOrEqualTo(String value) {
            addCriterion("error_message >=", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLessThan(String value) {
            addCriterion("error_message <", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLessThanOrEqualTo(String value) {
            addCriterion("error_message <=", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageLike(String value) {
            addCriterion("error_message like", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotLike(String value) {
            addCriterion("error_message not like", value, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageIn(List<String> values) {
            addCriterion("error_message in", values, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotIn(List<String> values) {
            addCriterion("error_message not in", values, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageBetween(String value1, String value2) {
            addCriterion("error_message between", value1, value2, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andErrorMessageNotBetween(String value1, String value2) {
            addCriterion("error_message not between", value1, value2, "errorMessage");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateIsNull() {
            addCriterion("extract_end_date is null");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateIsNotNull() {
            addCriterion("extract_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateEqualTo(Date value) {
            addCriterion("extract_end_date =", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateNotEqualTo(Date value) {
            addCriterion("extract_end_date <>", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateGreaterThan(Date value) {
            addCriterion("extract_end_date >", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("extract_end_date >=", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateLessThan(Date value) {
            addCriterion("extract_end_date <", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateLessThanOrEqualTo(Date value) {
            addCriterion("extract_end_date <=", value, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateIn(List<Date> values) {
            addCriterion("extract_end_date in", values, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateNotIn(List<Date> values) {
            addCriterion("extract_end_date not in", values, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateBetween(Date value1, Date value2) {
            addCriterion("extract_end_date between", value1, value2, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractEndDateNotBetween(Date value1, Date value2) {
            addCriterion("extract_end_date not between", value1, value2, "extractEndDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateIsNull() {
            addCriterion("extract_start_date is null");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateIsNotNull() {
            addCriterion("extract_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateEqualTo(Date value) {
            addCriterion("extract_start_date =", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateNotEqualTo(Date value) {
            addCriterion("extract_start_date <>", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateGreaterThan(Date value) {
            addCriterion("extract_start_date >", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("extract_start_date >=", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateLessThan(Date value) {
            addCriterion("extract_start_date <", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateLessThanOrEqualTo(Date value) {
            addCriterion("extract_start_date <=", value, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateIn(List<Date> values) {
            addCriterion("extract_start_date in", values, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateNotIn(List<Date> values) {
            addCriterion("extract_start_date not in", values, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateBetween(Date value1, Date value2) {
            addCriterion("extract_start_date between", value1, value2, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andExtractStartDateNotBetween(Date value1, Date value2) {
            addCriterion("extract_start_date not between", value1, value2, "extractStartDate");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNull() {
            addCriterion("billing_time is null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNotNull() {
            addCriterion("billing_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeEqualTo(String value) {
            addCriterion("billing_time =", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotEqualTo(String value) {
            addCriterion("billing_time <>", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThan(String value) {
            addCriterion("billing_time >", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThanOrEqualTo(String value) {
            addCriterion("billing_time >=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThan(String value) {
            addCriterion("billing_time <", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThanOrEqualTo(String value) {
            addCriterion("billing_time <=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLike(String value) {
            addCriterion("billing_time like", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotLike(String value) {
            addCriterion("billing_time not like", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIn(List<String> values) {
            addCriterion("billing_time in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotIn(List<String> values) {
            addCriterion("billing_time not in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeBetween(String value1, String value2) {
            addCriterion("billing_time between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotBetween(String value1, String value2) {
            addCriterion("billing_time not between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNull() {
            addCriterion("billing_user is null");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNotNull() {
            addCriterion("billing_user is not null");
            return (Criteria) this;
        }

        public Criteria andBillingUserEqualTo(String value) {
            addCriterion("billing_user =", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotEqualTo(String value) {
            addCriterion("billing_user <>", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThan(String value) {
            addCriterion("billing_user >", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThanOrEqualTo(String value) {
            addCriterion("billing_user >=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThan(String value) {
            addCriterion("billing_user <", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThanOrEqualTo(String value) {
            addCriterion("billing_user <=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLike(String value) {
            addCriterion("billing_user like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotLike(String value) {
            addCriterion("billing_user not like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserIn(List<String> values) {
            addCriterion("billing_user in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotIn(List<String> values) {
            addCriterion("billing_user not in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserBetween(String value1, String value2) {
            addCriterion("billing_user between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotBetween(String value1, String value2) {
            addCriterion("billing_user not between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoIsNull() {
            addCriterion("business_line_no is null");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoIsNotNull() {
            addCriterion("business_line_no is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoEqualTo(String value) {
            addCriterion("business_line_no =", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotEqualTo(String value) {
            addCriterion("business_line_no <>", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoGreaterThan(String value) {
            addCriterion("business_line_no >", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoGreaterThanOrEqualTo(String value) {
            addCriterion("business_line_no >=", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoLessThan(String value) {
            addCriterion("business_line_no <", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoLessThanOrEqualTo(String value) {
            addCriterion("business_line_no <=", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoLike(String value) {
            addCriterion("business_line_no like", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotLike(String value) {
            addCriterion("business_line_no not like", value, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoIn(List<String> values) {
            addCriterion("business_line_no in", values, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotIn(List<String> values) {
            addCriterion("business_line_no not in", values, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoBetween(String value1, String value2) {
            addCriterion("business_line_no between", value1, value2, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNoNotBetween(String value1, String value2) {
            addCriterion("business_line_no not between", value1, value2, "businessLineNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIsNull() {
            addCriterion("business_no is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIsNotNull() {
            addCriterion("business_no is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNoEqualTo(String value) {
            addCriterion("business_no =", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotEqualTo(String value) {
            addCriterion("business_no <>", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoGreaterThan(String value) {
            addCriterion("business_no >", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoGreaterThanOrEqualTo(String value) {
            addCriterion("business_no >=", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLessThan(String value) {
            addCriterion("business_no <", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLessThanOrEqualTo(String value) {
            addCriterion("business_no <=", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLike(String value) {
            addCriterion("business_no like", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotLike(String value) {
            addCriterion("business_no not like", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIn(List<String> values) {
            addCriterion("business_no in", values, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotIn(List<String> values) {
            addCriterion("business_no not in", values, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoBetween(String value1, String value2) {
            addCriterion("business_no between", value1, value2, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotBetween(String value1, String value2) {
            addCriterion("business_no not between", value1, value2, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNull() {
            addCriterion("business_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNotNull() {
            addCriterion("business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeEqualTo(Integer value) {
            addCriterion("business_type =", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotEqualTo(Integer value) {
            addCriterion("business_type <>", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThan(Integer value) {
            addCriterion("business_type >", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_type >=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThan(Integer value) {
            addCriterion("business_type <", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThanOrEqualTo(Integer value) {
            addCriterion("business_type <=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIn(List<Integer> values) {
            addCriterion("business_type in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotIn(List<Integer> values) {
            addCriterion("business_type not in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeBetween(Integer value1, Integer value2) {
            addCriterion("business_type between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("business_type not between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andSysCodeIsNull() {
            addCriterion("SYS_CODE is null");
            return (Criteria) this;
        }

        public Criteria andSysCodeIsNotNull() {
            addCriterion("SYS_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andSysCodeEqualTo(String value) {
            addCriterion("SYS_CODE =", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeNotEqualTo(String value) {
            addCriterion("SYS_CODE <>", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeGreaterThan(String value) {
            addCriterion("SYS_CODE >", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeGreaterThanOrEqualTo(String value) {
            addCriterion("SYS_CODE >=", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeLessThan(String value) {
            addCriterion("SYS_CODE <", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeLessThanOrEqualTo(String value) {
            addCriterion("SYS_CODE <=", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeLike(String value) {
            addCriterion("SYS_CODE like", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeNotLike(String value) {
            addCriterion("SYS_CODE not like", value, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeIn(List<String> values) {
            addCriterion("SYS_CODE in", values, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeNotIn(List<String> values) {
            addCriterion("SYS_CODE not in", values, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeBetween(String value1, String value2) {
            addCriterion("SYS_CODE between", value1, value2, "sysCode");
            return (Criteria) this;
        }

        public Criteria andSysCodeNotBetween(String value1, String value2) {
            addCriterion("SYS_CODE not between", value1, value2, "sysCode");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1IsNull() {
            addCriterion("ASSET_CATEGORY_SEGMENT1 is null");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1IsNotNull() {
            addCriterion("ASSET_CATEGORY_SEGMENT1 is not null");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1EqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 =", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1NotEqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 <>", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1GreaterThan(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 >", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1GreaterThanOrEqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 >=", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1LessThan(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 <", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1LessThanOrEqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 <=", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1Like(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 like", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1NotLike(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 not like", value, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1In(List<String> values) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 in", values, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1NotIn(List<String> values) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 not in", values, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1Between(String value1, String value2) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 between", value1, value2, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment1NotBetween(String value1, String value2) {
            addCriterion("ASSET_CATEGORY_SEGMENT1 not between", value1, value2, "assetCategorySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2IsNull() {
            addCriterion("ASSET_CATEGORY_SEGMENT2 is null");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2IsNotNull() {
            addCriterion("ASSET_CATEGORY_SEGMENT2 is not null");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2EqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 =", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2NotEqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 <>", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2GreaterThan(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 >", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2GreaterThanOrEqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 >=", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2LessThan(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 <", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2LessThanOrEqualTo(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 <=", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2Like(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 like", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2NotLike(String value) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 not like", value, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2In(List<String> values) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 in", values, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2NotIn(List<String> values) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 not in", values, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2Between(String value1, String value2) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 between", value1, value2, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetCategorySegment2NotBetween(String value1, String value2) {
            addCriterion("ASSET_CATEGORY_SEGMENT2 not between", value1, value2, "assetCategorySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1IsNull() {
            addCriterion("ASSET_KEY_SEGMENT1 is null");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1IsNotNull() {
            addCriterion("ASSET_KEY_SEGMENT1 is not null");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1EqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 =", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1NotEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 <>", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1GreaterThan(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 >", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1GreaterThanOrEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 >=", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1LessThan(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 <", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1LessThanOrEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 <=", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1Like(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 like", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1NotLike(String value) {
            addCriterion("ASSET_KEY_SEGMENT1 not like", value, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1In(List<String> values) {
            addCriterion("ASSET_KEY_SEGMENT1 in", values, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1NotIn(List<String> values) {
            addCriterion("ASSET_KEY_SEGMENT1 not in", values, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1Between(String value1, String value2) {
            addCriterion("ASSET_KEY_SEGMENT1 between", value1, value2, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment1NotBetween(String value1, String value2) {
            addCriterion("ASSET_KEY_SEGMENT1 not between", value1, value2, "assetKeySegment1");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2IsNull() {
            addCriterion("ASSET_KEY_SEGMENT2 is null");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2IsNotNull() {
            addCriterion("ASSET_KEY_SEGMENT2 is not null");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2EqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 =", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2NotEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 <>", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2GreaterThan(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 >", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2GreaterThanOrEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 >=", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2LessThan(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 <", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2LessThanOrEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 <=", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2Like(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 like", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2NotLike(String value) {
            addCriterion("ASSET_KEY_SEGMENT2 not like", value, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2In(List<String> values) {
            addCriterion("ASSET_KEY_SEGMENT2 in", values, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2NotIn(List<String> values) {
            addCriterion("ASSET_KEY_SEGMENT2 not in", values, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2Between(String value1, String value2) {
            addCriterion("ASSET_KEY_SEGMENT2 between", value1, value2, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment2NotBetween(String value1, String value2) {
            addCriterion("ASSET_KEY_SEGMENT2 not between", value1, value2, "assetKeySegment2");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3IsNull() {
            addCriterion("ASSET_KEY_SEGMENT3 is null");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3IsNotNull() {
            addCriterion("ASSET_KEY_SEGMENT3 is not null");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3EqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 =", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3NotEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 <>", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3GreaterThan(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 >", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3GreaterThanOrEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 >=", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3LessThan(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 <", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3LessThanOrEqualTo(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 <=", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3Like(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 like", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3NotLike(String value) {
            addCriterion("ASSET_KEY_SEGMENT3 not like", value, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3In(List<String> values) {
            addCriterion("ASSET_KEY_SEGMENT3 in", values, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3NotIn(List<String> values) {
            addCriterion("ASSET_KEY_SEGMENT3 not in", values, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3Between(String value1, String value2) {
            addCriterion("ASSET_KEY_SEGMENT3 between", value1, value2, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetKeySegment3NotBetween(String value1, String value2) {
            addCriterion("ASSET_KEY_SEGMENT3 not between", value1, value2, "assetKeySegment3");
            return (Criteria) this;
        }

        public Criteria andAssetTypeIsNull() {
            addCriterion("ASSET_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andAssetTypeIsNotNull() {
            addCriterion("ASSET_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andAssetTypeEqualTo(String value) {
            addCriterion("ASSET_TYPE =", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNotEqualTo(String value) {
            addCriterion("ASSET_TYPE <>", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeGreaterThan(String value) {
            addCriterion("ASSET_TYPE >", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeGreaterThanOrEqualTo(String value) {
            addCriterion("ASSET_TYPE >=", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeLessThan(String value) {
            addCriterion("ASSET_TYPE <", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeLessThanOrEqualTo(String value) {
            addCriterion("ASSET_TYPE <=", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeLike(String value) {
            addCriterion("ASSET_TYPE like", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNotLike(String value) {
            addCriterion("ASSET_TYPE not like", value, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeIn(List<String> values) {
            addCriterion("ASSET_TYPE in", values, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNotIn(List<String> values) {
            addCriterion("ASSET_TYPE not in", values, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeBetween(String value1, String value2) {
            addCriterion("ASSET_TYPE between", value1, value2, "assetType");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNotBetween(String value1, String value2) {
            addCriterion("ASSET_TYPE not between", value1, value2, "assetType");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeIsNull() {
            addCriterion("BOOK_TYPE_CODE is null");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeIsNotNull() {
            addCriterion("BOOK_TYPE_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeEqualTo(String value) {
            addCriterion("BOOK_TYPE_CODE =", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeNotEqualTo(String value) {
            addCriterion("BOOK_TYPE_CODE <>", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeGreaterThan(String value) {
            addCriterion("BOOK_TYPE_CODE >", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("BOOK_TYPE_CODE >=", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeLessThan(String value) {
            addCriterion("BOOK_TYPE_CODE <", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("BOOK_TYPE_CODE <=", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeLike(String value) {
            addCriterion("BOOK_TYPE_CODE like", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeNotLike(String value) {
            addCriterion("BOOK_TYPE_CODE not like", value, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeIn(List<String> values) {
            addCriterion("BOOK_TYPE_CODE in", values, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeNotIn(List<String> values) {
            addCriterion("BOOK_TYPE_CODE not in", values, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeBetween(String value1, String value2) {
            addCriterion("BOOK_TYPE_CODE between", value1, value2, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andBookTypeCodeNotBetween(String value1, String value2) {
            addCriterion("BOOK_TYPE_CODE not between", value1, value2, "bookTypeCode");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceIsNull() {
            addCriterion("DATE_PLACED_IN_SERVICE is null");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceIsNotNull() {
            addCriterion("DATE_PLACED_IN_SERVICE is not null");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceEqualTo(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE =", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceNotEqualTo(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE <>", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceGreaterThan(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE >", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceGreaterThanOrEqualTo(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE >=", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceLessThan(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE <", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceLessThanOrEqualTo(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE <=", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceLike(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE like", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceNotLike(String value) {
            addCriterion("DATE_PLACED_IN_SERVICE not like", value, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceIn(List<String> values) {
            addCriterion("DATE_PLACED_IN_SERVICE in", values, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceNotIn(List<String> values) {
            addCriterion("DATE_PLACED_IN_SERVICE not in", values, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceBetween(String value1, String value2) {
            addCriterion("DATE_PLACED_IN_SERVICE between", value1, value2, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDatePlacedInServiceNotBetween(String value1, String value2) {
            addCriterion("DATE_PLACED_IN_SERVICE not between", value1, value2, "datePlacedInService");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagIsNull() {
            addCriterion("DEPRECIATE_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagIsNotNull() {
            addCriterion("DEPRECIATE_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagEqualTo(String value) {
            addCriterion("DEPRECIATE_FLAG =", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagNotEqualTo(String value) {
            addCriterion("DEPRECIATE_FLAG <>", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagGreaterThan(String value) {
            addCriterion("DEPRECIATE_FLAG >", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagGreaterThanOrEqualTo(String value) {
            addCriterion("DEPRECIATE_FLAG >=", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagLessThan(String value) {
            addCriterion("DEPRECIATE_FLAG <", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagLessThanOrEqualTo(String value) {
            addCriterion("DEPRECIATE_FLAG <=", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagLike(String value) {
            addCriterion("DEPRECIATE_FLAG like", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagNotLike(String value) {
            addCriterion("DEPRECIATE_FLAG not like", value, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagIn(List<String> values) {
            addCriterion("DEPRECIATE_FLAG in", values, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagNotIn(List<String> values) {
            addCriterion("DEPRECIATE_FLAG not in", values, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagBetween(String value1, String value2) {
            addCriterion("DEPRECIATE_FLAG between", value1, value2, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDepreciateFlagNotBetween(String value1, String value2) {
            addCriterion("DEPRECIATE_FLAG not between", value1, value2, "depreciateFlag");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("DESCRIPTION is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("DESCRIPTION is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("DESCRIPTION =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("DESCRIPTION <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("DESCRIPTION >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("DESCRIPTION >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("DESCRIPTION <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("DESCRIPTION <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("DESCRIPTION like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("DESCRIPTION not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("DESCRIPTION in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("DESCRIPTION not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("DESCRIPTION between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("DESCRIPTION not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeIsNull() {
            addCriterion("DEPRN_METHOD_CODE is null");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeIsNotNull() {
            addCriterion("DEPRN_METHOD_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeEqualTo(String value) {
            addCriterion("DEPRN_METHOD_CODE =", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeNotEqualTo(String value) {
            addCriterion("DEPRN_METHOD_CODE <>", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeGreaterThan(String value) {
            addCriterion("DEPRN_METHOD_CODE >", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeGreaterThanOrEqualTo(String value) {
            addCriterion("DEPRN_METHOD_CODE >=", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeLessThan(String value) {
            addCriterion("DEPRN_METHOD_CODE <", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeLessThanOrEqualTo(String value) {
            addCriterion("DEPRN_METHOD_CODE <=", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeLike(String value) {
            addCriterion("DEPRN_METHOD_CODE like", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeNotLike(String value) {
            addCriterion("DEPRN_METHOD_CODE not like", value, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeIn(List<String> values) {
            addCriterion("DEPRN_METHOD_CODE in", values, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeNotIn(List<String> values) {
            addCriterion("DEPRN_METHOD_CODE not in", values, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeBetween(String value1, String value2) {
            addCriterion("DEPRN_METHOD_CODE between", value1, value2, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andDeprnMethodCodeNotBetween(String value1, String value2) {
            addCriterion("DEPRN_METHOD_CODE not between", value1, value2, "deprnMethodCode");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostIsNull() {
            addCriterion("FIXED_ASSETS_COST is null");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostIsNotNull() {
            addCriterion("FIXED_ASSETS_COST is not null");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostEqualTo(BigDecimal value) {
            addCriterion("FIXED_ASSETS_COST =", value, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostNotEqualTo(BigDecimal value) {
            addCriterion("FIXED_ASSETS_COST <>", value, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostGreaterThan(BigDecimal value) {
            addCriterion("FIXED_ASSETS_COST >", value, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("FIXED_ASSETS_COST >=", value, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostLessThan(BigDecimal value) {
            addCriterion("FIXED_ASSETS_COST <", value, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("FIXED_ASSETS_COST <=", value, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostIn(List<BigDecimal> values) {
            addCriterion("FIXED_ASSETS_COST in", values, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostNotIn(List<BigDecimal> values) {
            addCriterion("FIXED_ASSETS_COST not in", values, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("FIXED_ASSETS_COST between", value1, value2, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("FIXED_ASSETS_COST not between", value1, value2, "fixedAssetsCost");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsIsNull() {
            addCriterion("FIXED_ASSETS_UNITS is null");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsIsNotNull() {
            addCriterion("FIXED_ASSETS_UNITS is not null");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsEqualTo(Integer value) {
            addCriterion("FIXED_ASSETS_UNITS =", value, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsNotEqualTo(Integer value) {
            addCriterion("FIXED_ASSETS_UNITS <>", value, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsGreaterThan(Integer value) {
            addCriterion("FIXED_ASSETS_UNITS >", value, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsGreaterThanOrEqualTo(Integer value) {
            addCriterion("FIXED_ASSETS_UNITS >=", value, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsLessThan(Integer value) {
            addCriterion("FIXED_ASSETS_UNITS <", value, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsLessThanOrEqualTo(Integer value) {
            addCriterion("FIXED_ASSETS_UNITS <=", value, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsIn(List<Integer> values) {
            addCriterion("FIXED_ASSETS_UNITS in", values, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsNotIn(List<Integer> values) {
            addCriterion("FIXED_ASSETS_UNITS not in", values, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsBetween(Integer value1, Integer value2) {
            addCriterion("FIXED_ASSETS_UNITS between", value1, value2, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andFixedAssetsUnitsNotBetween(Integer value1, Integer value2) {
            addCriterion("FIXED_ASSETS_UNITS not between", value1, value2, "fixedAssetsUnits");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberIsNull() {
            addCriterion("INVOICE_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberIsNotNull() {
            addCriterion("INVOICE_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberEqualTo(String value) {
            addCriterion("INVOICE_NUMBER =", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberNotEqualTo(String value) {
            addCriterion("INVOICE_NUMBER <>", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberGreaterThan(String value) {
            addCriterion("INVOICE_NUMBER >", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_NUMBER >=", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberLessThan(String value) {
            addCriterion("INVOICE_NUMBER <", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_NUMBER <=", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberLike(String value) {
            addCriterion("INVOICE_NUMBER like", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberNotLike(String value) {
            addCriterion("INVOICE_NUMBER not like", value, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberIn(List<String> values) {
            addCriterion("INVOICE_NUMBER in", values, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberNotIn(List<String> values) {
            addCriterion("INVOICE_NUMBER not in", values, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberBetween(String value1, String value2) {
            addCriterion("INVOICE_NUMBER between", value1, value2, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumberNotBetween(String value1, String value2) {
            addCriterion("INVOICE_NUMBER not between", value1, value2, "invoiceNumber");
            return (Criteria) this;
        }

        public Criteria andLocSegment1IsNull() {
            addCriterion("LOC_SEGMENT1 is null");
            return (Criteria) this;
        }

        public Criteria andLocSegment1IsNotNull() {
            addCriterion("LOC_SEGMENT1 is not null");
            return (Criteria) this;
        }

        public Criteria andLocSegment1EqualTo(String value) {
            addCriterion("LOC_SEGMENT1 =", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1NotEqualTo(String value) {
            addCriterion("LOC_SEGMENT1 <>", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1GreaterThan(String value) {
            addCriterion("LOC_SEGMENT1 >", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1GreaterThanOrEqualTo(String value) {
            addCriterion("LOC_SEGMENT1 >=", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1LessThan(String value) {
            addCriterion("LOC_SEGMENT1 <", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1LessThanOrEqualTo(String value) {
            addCriterion("LOC_SEGMENT1 <=", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1Like(String value) {
            addCriterion("LOC_SEGMENT1 like", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1NotLike(String value) {
            addCriterion("LOC_SEGMENT1 not like", value, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1In(List<String> values) {
            addCriterion("LOC_SEGMENT1 in", values, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1NotIn(List<String> values) {
            addCriterion("LOC_SEGMENT1 not in", values, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1Between(String value1, String value2) {
            addCriterion("LOC_SEGMENT1 between", value1, value2, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment1NotBetween(String value1, String value2) {
            addCriterion("LOC_SEGMENT1 not between", value1, value2, "locSegment1");
            return (Criteria) this;
        }

        public Criteria andLocSegment2IsNull() {
            addCriterion("LOC_SEGMENT2 is null");
            return (Criteria) this;
        }

        public Criteria andLocSegment2IsNotNull() {
            addCriterion("LOC_SEGMENT2 is not null");
            return (Criteria) this;
        }

        public Criteria andLocSegment2EqualTo(String value) {
            addCriterion("LOC_SEGMENT2 =", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2NotEqualTo(String value) {
            addCriterion("LOC_SEGMENT2 <>", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2GreaterThan(String value) {
            addCriterion("LOC_SEGMENT2 >", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2GreaterThanOrEqualTo(String value) {
            addCriterion("LOC_SEGMENT2 >=", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2LessThan(String value) {
            addCriterion("LOC_SEGMENT2 <", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2LessThanOrEqualTo(String value) {
            addCriterion("LOC_SEGMENT2 <=", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2Like(String value) {
            addCriterion("LOC_SEGMENT2 like", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2NotLike(String value) {
            addCriterion("LOC_SEGMENT2 not like", value, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2In(List<String> values) {
            addCriterion("LOC_SEGMENT2 in", values, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2NotIn(List<String> values) {
            addCriterion("LOC_SEGMENT2 not in", values, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2Between(String value1, String value2) {
            addCriterion("LOC_SEGMENT2 between", value1, value2, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment2NotBetween(String value1, String value2) {
            addCriterion("LOC_SEGMENT2 not between", value1, value2, "locSegment2");
            return (Criteria) this;
        }

        public Criteria andLocSegment3IsNull() {
            addCriterion("LOC_SEGMENT3 is null");
            return (Criteria) this;
        }

        public Criteria andLocSegment3IsNotNull() {
            addCriterion("LOC_SEGMENT3 is not null");
            return (Criteria) this;
        }

        public Criteria andLocSegment3EqualTo(String value) {
            addCriterion("LOC_SEGMENT3 =", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3NotEqualTo(String value) {
            addCriterion("LOC_SEGMENT3 <>", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3GreaterThan(String value) {
            addCriterion("LOC_SEGMENT3 >", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3GreaterThanOrEqualTo(String value) {
            addCriterion("LOC_SEGMENT3 >=", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3LessThan(String value) {
            addCriterion("LOC_SEGMENT3 <", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3LessThanOrEqualTo(String value) {
            addCriterion("LOC_SEGMENT3 <=", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3Like(String value) {
            addCriterion("LOC_SEGMENT3 like", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3NotLike(String value) {
            addCriterion("LOC_SEGMENT3 not like", value, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3In(List<String> values) {
            addCriterion("LOC_SEGMENT3 in", values, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3NotIn(List<String> values) {
            addCriterion("LOC_SEGMENT3 not in", values, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3Between(String value1, String value2) {
            addCriterion("LOC_SEGMENT3 between", value1, value2, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLocSegment3NotBetween(String value1, String value2) {
            addCriterion("LOC_SEGMENT3 not between", value1, value2, "locSegment3");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsIsNull() {
            addCriterion("LIFE_IN_MONTHS is null");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsIsNotNull() {
            addCriterion("LIFE_IN_MONTHS is not null");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsEqualTo(Integer value) {
            addCriterion("LIFE_IN_MONTHS =", value, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsNotEqualTo(Integer value) {
            addCriterion("LIFE_IN_MONTHS <>", value, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsGreaterThan(Integer value) {
            addCriterion("LIFE_IN_MONTHS >", value, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsGreaterThanOrEqualTo(Integer value) {
            addCriterion("LIFE_IN_MONTHS >=", value, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsLessThan(Integer value) {
            addCriterion("LIFE_IN_MONTHS <", value, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsLessThanOrEqualTo(Integer value) {
            addCriterion("LIFE_IN_MONTHS <=", value, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsIn(List<Integer> values) {
            addCriterion("LIFE_IN_MONTHS in", values, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsNotIn(List<Integer> values) {
            addCriterion("LIFE_IN_MONTHS not in", values, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsBetween(Integer value1, Integer value2) {
            addCriterion("LIFE_IN_MONTHS between", value1, value2, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andLifeInMonthsNotBetween(Integer value1, Integer value2) {
            addCriterion("LIFE_IN_MONTHS not between", value1, value2, "lifeInMonths");
            return (Criteria) this;
        }

        public Criteria andModelNumberIsNull() {
            addCriterion("MODEL_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andModelNumberIsNotNull() {
            addCriterion("MODEL_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andModelNumberEqualTo(String value) {
            addCriterion("MODEL_NUMBER =", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberNotEqualTo(String value) {
            addCriterion("MODEL_NUMBER <>", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberGreaterThan(String value) {
            addCriterion("MODEL_NUMBER >", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberGreaterThanOrEqualTo(String value) {
            addCriterion("MODEL_NUMBER >=", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberLessThan(String value) {
            addCriterion("MODEL_NUMBER <", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberLessThanOrEqualTo(String value) {
            addCriterion("MODEL_NUMBER <=", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberLike(String value) {
            addCriterion("MODEL_NUMBER like", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberNotLike(String value) {
            addCriterion("MODEL_NUMBER not like", value, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberIn(List<String> values) {
            addCriterion("MODEL_NUMBER in", values, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberNotIn(List<String> values) {
            addCriterion("MODEL_NUMBER not in", values, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberBetween(String value1, String value2) {
            addCriterion("MODEL_NUMBER between", value1, value2, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andModelNumberNotBetween(String value1, String value2) {
            addCriterion("MODEL_NUMBER not between", value1, value2, "modelNumber");
            return (Criteria) this;
        }

        public Criteria andQueueNameIsNull() {
            addCriterion("QUEUE_NAME is null");
            return (Criteria) this;
        }

        public Criteria andQueueNameIsNotNull() {
            addCriterion("QUEUE_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andQueueNameEqualTo(String value) {
            addCriterion("QUEUE_NAME =", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameNotEqualTo(String value) {
            addCriterion("QUEUE_NAME <>", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameGreaterThan(String value) {
            addCriterion("QUEUE_NAME >", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameGreaterThanOrEqualTo(String value) {
            addCriterion("QUEUE_NAME >=", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameLessThan(String value) {
            addCriterion("QUEUE_NAME <", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameLessThanOrEqualTo(String value) {
            addCriterion("QUEUE_NAME <=", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameLike(String value) {
            addCriterion("QUEUE_NAME like", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameNotLike(String value) {
            addCriterion("QUEUE_NAME not like", value, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameIn(List<String> values) {
            addCriterion("QUEUE_NAME in", values, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameNotIn(List<String> values) {
            addCriterion("QUEUE_NAME not in", values, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameBetween(String value1, String value2) {
            addCriterion("QUEUE_NAME between", value1, value2, "queueName");
            return (Criteria) this;
        }

        public Criteria andQueueNameNotBetween(String value1, String value2) {
            addCriterion("QUEUE_NAME not between", value1, value2, "queueName");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIsNull() {
            addCriterion("SERIAL_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIsNotNull() {
            addCriterion("SERIAL_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andSerialNumberEqualTo(String value) {
            addCriterion("SERIAL_NUMBER =", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotEqualTo(String value) {
            addCriterion("SERIAL_NUMBER <>", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberGreaterThan(String value) {
            addCriterion("SERIAL_NUMBER >", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberGreaterThanOrEqualTo(String value) {
            addCriterion("SERIAL_NUMBER >=", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLessThan(String value) {
            addCriterion("SERIAL_NUMBER <", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLessThanOrEqualTo(String value) {
            addCriterion("SERIAL_NUMBER <=", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberLike(String value) {
            addCriterion("SERIAL_NUMBER like", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotLike(String value) {
            addCriterion("SERIAL_NUMBER not like", value, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberIn(List<String> values) {
            addCriterion("SERIAL_NUMBER in", values, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotIn(List<String> values) {
            addCriterion("SERIAL_NUMBER not in", values, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberBetween(String value1, String value2) {
            addCriterion("SERIAL_NUMBER between", value1, value2, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andSerialNumberNotBetween(String value1, String value2) {
            addCriterion("SERIAL_NUMBER not between", value1, value2, "serialNumber");
            return (Criteria) this;
        }

        public Criteria andAccSegment1IsNull() {
            addCriterion("ACC_SEGMENT1 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment1IsNotNull() {
            addCriterion("ACC_SEGMENT1 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment1EqualTo(String value) {
            addCriterion("ACC_SEGMENT1 =", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT1 <>", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1GreaterThan(String value) {
            addCriterion("ACC_SEGMENT1 >", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT1 >=", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1LessThan(String value) {
            addCriterion("ACC_SEGMENT1 <", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT1 <=", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1Like(String value) {
            addCriterion("ACC_SEGMENT1 like", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1NotLike(String value) {
            addCriterion("ACC_SEGMENT1 not like", value, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1In(List<String> values) {
            addCriterion("ACC_SEGMENT1 in", values, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT1 not in", values, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT1 between", value1, value2, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment1NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT1 not between", value1, value2, "accSegment1");
            return (Criteria) this;
        }

        public Criteria andAccSegment2IsNull() {
            addCriterion("ACC_SEGMENT2 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment2IsNotNull() {
            addCriterion("ACC_SEGMENT2 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment2EqualTo(String value) {
            addCriterion("ACC_SEGMENT2 =", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT2 <>", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2GreaterThan(String value) {
            addCriterion("ACC_SEGMENT2 >", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT2 >=", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2LessThan(String value) {
            addCriterion("ACC_SEGMENT2 <", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT2 <=", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2Like(String value) {
            addCriterion("ACC_SEGMENT2 like", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2NotLike(String value) {
            addCriterion("ACC_SEGMENT2 not like", value, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2In(List<String> values) {
            addCriterion("ACC_SEGMENT2 in", values, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT2 not in", values, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT2 between", value1, value2, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment2NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT2 not between", value1, value2, "accSegment2");
            return (Criteria) this;
        }

        public Criteria andAccSegment3IsNull() {
            addCriterion("ACC_SEGMENT3 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment3IsNotNull() {
            addCriterion("ACC_SEGMENT3 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment3EqualTo(String value) {
            addCriterion("ACC_SEGMENT3 =", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT3 <>", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3GreaterThan(String value) {
            addCriterion("ACC_SEGMENT3 >", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT3 >=", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3LessThan(String value) {
            addCriterion("ACC_SEGMENT3 <", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT3 <=", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3Like(String value) {
            addCriterion("ACC_SEGMENT3 like", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3NotLike(String value) {
            addCriterion("ACC_SEGMENT3 not like", value, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3In(List<String> values) {
            addCriterion("ACC_SEGMENT3 in", values, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT3 not in", values, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT3 between", value1, value2, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment3NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT3 not between", value1, value2, "accSegment3");
            return (Criteria) this;
        }

        public Criteria andAccSegment4IsNull() {
            addCriterion("ACC_SEGMENT4 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment4IsNotNull() {
            addCriterion("ACC_SEGMENT4 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment4EqualTo(String value) {
            addCriterion("ACC_SEGMENT4 =", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT4 <>", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4GreaterThan(String value) {
            addCriterion("ACC_SEGMENT4 >", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT4 >=", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4LessThan(String value) {
            addCriterion("ACC_SEGMENT4 <", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT4 <=", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4Like(String value) {
            addCriterion("ACC_SEGMENT4 like", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4NotLike(String value) {
            addCriterion("ACC_SEGMENT4 not like", value, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4In(List<String> values) {
            addCriterion("ACC_SEGMENT4 in", values, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT4 not in", values, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT4 between", value1, value2, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment4NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT4 not between", value1, value2, "accSegment4");
            return (Criteria) this;
        }

        public Criteria andAccSegment5IsNull() {
            addCriterion("ACC_SEGMENT5 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment5IsNotNull() {
            addCriterion("ACC_SEGMENT5 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment5EqualTo(String value) {
            addCriterion("ACC_SEGMENT5 =", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT5 <>", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5GreaterThan(String value) {
            addCriterion("ACC_SEGMENT5 >", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT5 >=", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5LessThan(String value) {
            addCriterion("ACC_SEGMENT5 <", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT5 <=", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5Like(String value) {
            addCriterion("ACC_SEGMENT5 like", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5NotLike(String value) {
            addCriterion("ACC_SEGMENT5 not like", value, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5In(List<String> values) {
            addCriterion("ACC_SEGMENT5 in", values, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT5 not in", values, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT5 between", value1, value2, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment5NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT5 not between", value1, value2, "accSegment5");
            return (Criteria) this;
        }

        public Criteria andAccSegment6IsNull() {
            addCriterion("ACC_SEGMENT6 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment6IsNotNull() {
            addCriterion("ACC_SEGMENT6 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment6EqualTo(String value) {
            addCriterion("ACC_SEGMENT6 =", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT6 <>", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6GreaterThan(String value) {
            addCriterion("ACC_SEGMENT6 >", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT6 >=", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6LessThan(String value) {
            addCriterion("ACC_SEGMENT6 <", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT6 <=", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6Like(String value) {
            addCriterion("ACC_SEGMENT6 like", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6NotLike(String value) {
            addCriterion("ACC_SEGMENT6 not like", value, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6In(List<String> values) {
            addCriterion("ACC_SEGMENT6 in", values, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT6 not in", values, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT6 between", value1, value2, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment6NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT6 not between", value1, value2, "accSegment6");
            return (Criteria) this;
        }

        public Criteria andAccSegment7IsNull() {
            addCriterion("ACC_SEGMENT7 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment7IsNotNull() {
            addCriterion("ACC_SEGMENT7 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment7EqualTo(String value) {
            addCriterion("ACC_SEGMENT7 =", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT7 <>", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7GreaterThan(String value) {
            addCriterion("ACC_SEGMENT7 >", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT7 >=", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7LessThan(String value) {
            addCriterion("ACC_SEGMENT7 <", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT7 <=", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7Like(String value) {
            addCriterion("ACC_SEGMENT7 like", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7NotLike(String value) {
            addCriterion("ACC_SEGMENT7 not like", value, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7In(List<String> values) {
            addCriterion("ACC_SEGMENT7 in", values, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT7 not in", values, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT7 between", value1, value2, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment7NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT7 not between", value1, value2, "accSegment7");
            return (Criteria) this;
        }

        public Criteria andAccSegment8IsNull() {
            addCriterion("ACC_SEGMENT8 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment8IsNotNull() {
            addCriterion("ACC_SEGMENT8 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment8EqualTo(String value) {
            addCriterion("ACC_SEGMENT8 =", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT8 <>", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8GreaterThan(String value) {
            addCriterion("ACC_SEGMENT8 >", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT8 >=", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8LessThan(String value) {
            addCriterion("ACC_SEGMENT8 <", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT8 <=", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8Like(String value) {
            addCriterion("ACC_SEGMENT8 like", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8NotLike(String value) {
            addCriterion("ACC_SEGMENT8 not like", value, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8In(List<String> values) {
            addCriterion("ACC_SEGMENT8 in", values, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT8 not in", values, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT8 between", value1, value2, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment8NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT8 not between", value1, value2, "accSegment8");
            return (Criteria) this;
        }

        public Criteria andAccSegment9IsNull() {
            addCriterion("ACC_SEGMENT9 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment9IsNotNull() {
            addCriterion("ACC_SEGMENT9 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment9EqualTo(String value) {
            addCriterion("ACC_SEGMENT9 =", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT9 <>", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9GreaterThan(String value) {
            addCriterion("ACC_SEGMENT9 >", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT9 >=", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9LessThan(String value) {
            addCriterion("ACC_SEGMENT9 <", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT9 <=", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9Like(String value) {
            addCriterion("ACC_SEGMENT9 like", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9NotLike(String value) {
            addCriterion("ACC_SEGMENT9 not like", value, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9In(List<String> values) {
            addCriterion("ACC_SEGMENT9 in", values, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT9 not in", values, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT9 between", value1, value2, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment9NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT9 not between", value1, value2, "accSegment9");
            return (Criteria) this;
        }

        public Criteria andAccSegment10IsNull() {
            addCriterion("ACC_SEGMENT10 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment10IsNotNull() {
            addCriterion("ACC_SEGMENT10 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment10EqualTo(String value) {
            addCriterion("ACC_SEGMENT10 =", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT10 <>", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10GreaterThan(String value) {
            addCriterion("ACC_SEGMENT10 >", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT10 >=", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10LessThan(String value) {
            addCriterion("ACC_SEGMENT10 <", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT10 <=", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10Like(String value) {
            addCriterion("ACC_SEGMENT10 like", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10NotLike(String value) {
            addCriterion("ACC_SEGMENT10 not like", value, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10In(List<String> values) {
            addCriterion("ACC_SEGMENT10 in", values, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT10 not in", values, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT10 between", value1, value2, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment10NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT10 not between", value1, value2, "accSegment10");
            return (Criteria) this;
        }

        public Criteria andAccSegment11IsNull() {
            addCriterion("ACC_SEGMENT11 is null");
            return (Criteria) this;
        }

        public Criteria andAccSegment11IsNotNull() {
            addCriterion("ACC_SEGMENT11 is not null");
            return (Criteria) this;
        }

        public Criteria andAccSegment11EqualTo(String value) {
            addCriterion("ACC_SEGMENT11 =", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11NotEqualTo(String value) {
            addCriterion("ACC_SEGMENT11 <>", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11GreaterThan(String value) {
            addCriterion("ACC_SEGMENT11 >", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11GreaterThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT11 >=", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11LessThan(String value) {
            addCriterion("ACC_SEGMENT11 <", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11LessThanOrEqualTo(String value) {
            addCriterion("ACC_SEGMENT11 <=", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11Like(String value) {
            addCriterion("ACC_SEGMENT11 like", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11NotLike(String value) {
            addCriterion("ACC_SEGMENT11 not like", value, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11In(List<String> values) {
            addCriterion("ACC_SEGMENT11 in", values, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11NotIn(List<String> values) {
            addCriterion("ACC_SEGMENT11 not in", values, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11Between(String value1, String value2) {
            addCriterion("ACC_SEGMENT11 between", value1, value2, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andAccSegment11NotBetween(String value1, String value2) {
            addCriterion("ACC_SEGMENT11 not between", value1, value2, "accSegment11");
            return (Criteria) this;
        }

        public Criteria andVersionIdIsNull() {
            addCriterion("version_id is null");
            return (Criteria) this;
        }

        public Criteria andVersionIdIsNotNull() {
            addCriterion("version_id is not null");
            return (Criteria) this;
        }

        public Criteria andVersionIdEqualTo(Integer value) {
            addCriterion("version_id =", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotEqualTo(Integer value) {
            addCriterion("version_id <>", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdGreaterThan(Integer value) {
            addCriterion("version_id >", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("version_id >=", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdLessThan(Integer value) {
            addCriterion("version_id <", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("version_id <=", value, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdIn(List<Integer> values) {
            addCriterion("version_id in", values, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotIn(List<Integer> values) {
            addCriterion("version_id not in", values, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("version_id between", value1, value2, "versionId");
            return (Criteria) this;
        }

        public Criteria andVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("version_id not between", value1, value2, "versionId");
            return (Criteria) this;
        }

        public Criteria andTagNumberIsNull() {
            addCriterion("TAG_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andTagNumberIsNotNull() {
            addCriterion("TAG_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andTagNumberEqualTo(String value) {
            addCriterion("TAG_NUMBER =", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberNotEqualTo(String value) {
            addCriterion("TAG_NUMBER <>", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberGreaterThan(String value) {
            addCriterion("TAG_NUMBER >", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberGreaterThanOrEqualTo(String value) {
            addCriterion("TAG_NUMBER >=", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberLessThan(String value) {
            addCriterion("TAG_NUMBER <", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberLessThanOrEqualTo(String value) {
            addCriterion("TAG_NUMBER <=", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberLike(String value) {
            addCriterion("TAG_NUMBER like", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberNotLike(String value) {
            addCriterion("TAG_NUMBER not like", value, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberIn(List<String> values) {
            addCriterion("TAG_NUMBER in", values, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberNotIn(List<String> values) {
            addCriterion("TAG_NUMBER not in", values, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberBetween(String value1, String value2) {
            addCriterion("TAG_NUMBER between", value1, value2, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andTagNumberNotBetween(String value1, String value2) {
            addCriterion("TAG_NUMBER not between", value1, value2, "tagNumber");
            return (Criteria) this;
        }

        public Criteria andFaCodeIsNull() {
            addCriterion("FA_CODE is null");
            return (Criteria) this;
        }

        public Criteria andFaCodeIsNotNull() {
            addCriterion("FA_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andFaCodeEqualTo(String value) {
            addCriterion("FA_CODE =", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotEqualTo(String value) {
            addCriterion("FA_CODE <>", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeGreaterThan(String value) {
            addCriterion("FA_CODE >", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("FA_CODE >=", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeLessThan(String value) {
            addCriterion("FA_CODE <", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeLessThanOrEqualTo(String value) {
            addCriterion("FA_CODE <=", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeLike(String value) {
            addCriterion("FA_CODE like", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotLike(String value) {
            addCriterion("FA_CODE not like", value, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeIn(List<String> values) {
            addCriterion("FA_CODE in", values, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotIn(List<String> values) {
            addCriterion("FA_CODE not in", values, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeBetween(String value1, String value2) {
            addCriterion("FA_CODE between", value1, value2, "faCode");
            return (Criteria) this;
        }

        public Criteria andFaCodeNotBetween(String value1, String value2) {
            addCriterion("FA_CODE not between", value1, value2, "faCode");
            return (Criteria) this;
        }

        public Criteria andAssetIdIsNull() {
            addCriterion("ASSET_ID is null");
            return (Criteria) this;
        }

        public Criteria andAssetIdIsNotNull() {
            addCriterion("ASSET_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAssetIdEqualTo(Integer value) {
            addCriterion("ASSET_ID =", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdNotEqualTo(Integer value) {
            addCriterion("ASSET_ID <>", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdGreaterThan(Integer value) {
            addCriterion("ASSET_ID >", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ASSET_ID >=", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdLessThan(Integer value) {
            addCriterion("ASSET_ID <", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdLessThanOrEqualTo(Integer value) {
            addCriterion("ASSET_ID <=", value, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdIn(List<Integer> values) {
            addCriterion("ASSET_ID in", values, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdNotIn(List<Integer> values) {
            addCriterion("ASSET_ID not in", values, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdBetween(Integer value1, Integer value2) {
            addCriterion("ASSET_ID between", value1, value2, "assetId");
            return (Criteria) this;
        }

        public Criteria andAssetIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ASSET_ID not between", value1, value2, "assetId");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}