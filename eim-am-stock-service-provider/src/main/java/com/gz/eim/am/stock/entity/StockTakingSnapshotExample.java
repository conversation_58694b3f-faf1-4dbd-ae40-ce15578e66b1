package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockTakingSnapshotExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockTakingSnapshotExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIsNull() {
            addCriterion("assets_name is null");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIsNotNull() {
            addCriterion("assets_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsNameEqualTo(String value) {
            addCriterion("assets_name =", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotEqualTo(String value) {
            addCriterion("assets_name <>", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameGreaterThan(String value) {
            addCriterion("assets_name >", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameGreaterThanOrEqualTo(String value) {
            addCriterion("assets_name >=", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLessThan(String value) {
            addCriterion("assets_name <", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLessThanOrEqualTo(String value) {
            addCriterion("assets_name <=", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameLike(String value) {
            addCriterion("assets_name like", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotLike(String value) {
            addCriterion("assets_name not like", value, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameIn(List<String> values) {
            addCriterion("assets_name in", values, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotIn(List<String> values) {
            addCriterion("assets_name not in", values, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameBetween(String value1, String value2) {
            addCriterion("assets_name between", value1, value2, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsNameNotBetween(String value1, String value2) {
            addCriterion("assets_name not between", value1, value2, "assetsName");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNull() {
            addCriterion("assets_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIsNotNull() {
            addCriterion("assets_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeEqualTo(String value) {
            addCriterion("assets_code =", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotEqualTo(String value) {
            addCriterion("assets_code <>", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThan(String value) {
            addCriterion("assets_code >", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("assets_code >=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThan(String value) {
            addCriterion("assets_code <", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLessThanOrEqualTo(String value) {
            addCriterion("assets_code <=", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeLike(String value) {
            addCriterion("assets_code like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotLike(String value) {
            addCriterion("assets_code not like", value, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeIn(List<String> values) {
            addCriterion("assets_code in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotIn(List<String> values) {
            addCriterion("assets_code not in", values, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeBetween(String value1, String value2) {
            addCriterion("assets_code between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andAssetsCodeNotBetween(String value1, String value2) {
            addCriterion("assets_code not between", value1, value2, "assetsCode");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("category is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("category is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("category =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("category <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("category >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("category >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("category <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("category <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("category like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("category not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("category in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("category not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("category between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("category not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andConditionsIsNull() {
            addCriterion("conditions is null");
            return (Criteria) this;
        }

        public Criteria andConditionsIsNotNull() {
            addCriterion("conditions is not null");
            return (Criteria) this;
        }

        public Criteria andConditionsEqualTo(Integer value) {
            addCriterion("conditions =", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotEqualTo(Integer value) {
            addCriterion("conditions <>", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsGreaterThan(Integer value) {
            addCriterion("conditions >", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsGreaterThanOrEqualTo(Integer value) {
            addCriterion("conditions >=", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsLessThan(Integer value) {
            addCriterion("conditions <", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsLessThanOrEqualTo(Integer value) {
            addCriterion("conditions <=", value, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsIn(List<Integer> values) {
            addCriterion("conditions in", values, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotIn(List<Integer> values) {
            addCriterion("conditions not in", values, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsBetween(Integer value1, Integer value2) {
            addCriterion("conditions between", value1, value2, "conditions");
            return (Criteria) this;
        }

        public Criteria andConditionsNotBetween(Integer value1, Integer value2) {
            addCriterion("conditions not between", value1, value2, "conditions");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andHolderIsNull() {
            addCriterion("holder is null");
            return (Criteria) this;
        }

        public Criteria andHolderIsNotNull() {
            addCriterion("holder is not null");
            return (Criteria) this;
        }

        public Criteria andHolderEqualTo(String value) {
            addCriterion("holder =", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotEqualTo(String value) {
            addCriterion("holder <>", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderGreaterThan(String value) {
            addCriterion("holder >", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderGreaterThanOrEqualTo(String value) {
            addCriterion("holder >=", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderLessThan(String value) {
            addCriterion("holder <", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderLessThanOrEqualTo(String value) {
            addCriterion("holder <=", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderLike(String value) {
            addCriterion("holder like", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotLike(String value) {
            addCriterion("holder not like", value, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderIn(List<String> values) {
            addCriterion("holder in", values, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotIn(List<String> values) {
            addCriterion("holder not in", values, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderBetween(String value1, String value2) {
            addCriterion("holder between", value1, value2, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderNotBetween(String value1, String value2) {
            addCriterion("holder not between", value1, value2, "holder");
            return (Criteria) this;
        }

        public Criteria andHolderTimeIsNull() {
            addCriterion("holder_time is null");
            return (Criteria) this;
        }

        public Criteria andHolderTimeIsNotNull() {
            addCriterion("holder_time is not null");
            return (Criteria) this;
        }

        public Criteria andHolderTimeEqualTo(Date value) {
            addCriterion("holder_time =", value, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeNotEqualTo(Date value) {
            addCriterion("holder_time <>", value, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeGreaterThan(Date value) {
            addCriterion("holder_time >", value, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("holder_time >=", value, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeLessThan(Date value) {
            addCriterion("holder_time <", value, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeLessThanOrEqualTo(Date value) {
            addCriterion("holder_time <=", value, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeIn(List<Date> values) {
            addCriterion("holder_time in", values, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeNotIn(List<Date> values) {
            addCriterion("holder_time not in", values, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeBetween(Date value1, Date value2) {
            addCriterion("holder_time between", value1, value2, "holderTime");
            return (Criteria) this;
        }

        public Criteria andHolderTimeNotBetween(Date value1, Date value2) {
            addCriterion("holder_time not between", value1, value2, "holderTime");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNull() {
            addCriterion("warehouse_code is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIsNotNull() {
            addCriterion("warehouse_code is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeEqualTo(String value) {
            addCriterion("warehouse_code =", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotEqualTo(String value) {
            addCriterion("warehouse_code <>", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThan(String value) {
            addCriterion("warehouse_code >", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_code >=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThan(String value) {
            addCriterion("warehouse_code <", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLessThanOrEqualTo(String value) {
            addCriterion("warehouse_code <=", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeLike(String value) {
            addCriterion("warehouse_code like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotLike(String value) {
            addCriterion("warehouse_code not like", value, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeIn(List<String> values) {
            addCriterion("warehouse_code in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotIn(List<String> values) {
            addCriterion("warehouse_code not in", values, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeBetween(String value1, String value2) {
            addCriterion("warehouse_code between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andWarehouseCodeNotBetween(String value1, String value2) {
            addCriterion("warehouse_code not between", value1, value2, "warehouseCode");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIsNull() {
            addCriterion("assets_keeper is null");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIsNotNull() {
            addCriterion("assets_keeper is not null");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperEqualTo(String value) {
            addCriterion("assets_keeper =", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotEqualTo(String value) {
            addCriterion("assets_keeper <>", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperGreaterThan(String value) {
            addCriterion("assets_keeper >", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperGreaterThanOrEqualTo(String value) {
            addCriterion("assets_keeper >=", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLessThan(String value) {
            addCriterion("assets_keeper <", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLessThanOrEqualTo(String value) {
            addCriterion("assets_keeper <=", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperLike(String value) {
            addCriterion("assets_keeper like", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotLike(String value) {
            addCriterion("assets_keeper not like", value, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperIn(List<String> values) {
            addCriterion("assets_keeper in", values, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotIn(List<String> values) {
            addCriterion("assets_keeper not in", values, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperBetween(String value1, String value2) {
            addCriterion("assets_keeper between", value1, value2, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andAssetsKeeperNotBetween(String value1, String value2) {
            addCriterion("assets_keeper not between", value1, value2, "assetsKeeper");
            return (Criteria) this;
        }

        public Criteria andHolderAddressIsNull() {
            addCriterion("holder_address is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressIsNotNull() {
            addCriterion("holder_address is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressEqualTo(String value) {
            addCriterion("holder_address =", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressNotEqualTo(String value) {
            addCriterion("holder_address <>", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressGreaterThan(String value) {
            addCriterion("holder_address >", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address >=", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressLessThan(String value) {
            addCriterion("holder_address <", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressLessThanOrEqualTo(String value) {
            addCriterion("holder_address <=", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressLike(String value) {
            addCriterion("holder_address like", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressNotLike(String value) {
            addCriterion("holder_address not like", value, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressIn(List<String> values) {
            addCriterion("holder_address in", values, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressNotIn(List<String> values) {
            addCriterion("holder_address not in", values, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressBetween(String value1, String value2) {
            addCriterion("holder_address between", value1, value2, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andHolderAddressNotBetween(String value1, String value2) {
            addCriterion("holder_address not between", value1, value2, "holderAddress");
            return (Criteria) this;
        }

        public Criteria andSnCodeIsNull() {
            addCriterion("sn_code is null");
            return (Criteria) this;
        }

        public Criteria andSnCodeIsNotNull() {
            addCriterion("sn_code is not null");
            return (Criteria) this;
        }

        public Criteria andSnCodeEqualTo(String value) {
            addCriterion("sn_code =", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotEqualTo(String value) {
            addCriterion("sn_code <>", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeGreaterThan(String value) {
            addCriterion("sn_code >", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sn_code >=", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLessThan(String value) {
            addCriterion("sn_code <", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLessThanOrEqualTo(String value) {
            addCriterion("sn_code <=", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeLike(String value) {
            addCriterion("sn_code like", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotLike(String value) {
            addCriterion("sn_code not like", value, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeIn(List<String> values) {
            addCriterion("sn_code in", values, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotIn(List<String> values) {
            addCriterion("sn_code not in", values, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeBetween(String value1, String value2) {
            addCriterion("sn_code between", value1, value2, "snCode");
            return (Criteria) this;
        }

        public Criteria andSnCodeNotBetween(String value1, String value2) {
            addCriterion("sn_code not between", value1, value2, "snCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeIsNull() {
            addCriterion("device_code is null");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeIsNotNull() {
            addCriterion("device_code is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeEqualTo(String value) {
            addCriterion("device_code =", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeNotEqualTo(String value) {
            addCriterion("device_code <>", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeGreaterThan(String value) {
            addCriterion("device_code >", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("device_code >=", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeLessThan(String value) {
            addCriterion("device_code <", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeLessThanOrEqualTo(String value) {
            addCriterion("device_code <=", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeLike(String value) {
            addCriterion("device_code like", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeNotLike(String value) {
            addCriterion("device_code not like", value, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeIn(List<String> values) {
            addCriterion("device_code in", values, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeNotIn(List<String> values) {
            addCriterion("device_code not in", values, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeBetween(String value1, String value2) {
            addCriterion("device_code between", value1, value2, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andDeviceCodeNotBetween(String value1, String value2) {
            addCriterion("device_code not between", value1, value2, "deviceCode");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIsNull() {
            addCriterion("taking_plan_no is null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIsNotNull() {
            addCriterion("taking_plan_no is not null");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoEqualTo(String value) {
            addCriterion("taking_plan_no =", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotEqualTo(String value) {
            addCriterion("taking_plan_no <>", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoGreaterThan(String value) {
            addCriterion("taking_plan_no >", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoGreaterThanOrEqualTo(String value) {
            addCriterion("taking_plan_no >=", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLessThan(String value) {
            addCriterion("taking_plan_no <", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLessThanOrEqualTo(String value) {
            addCriterion("taking_plan_no <=", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoLike(String value) {
            addCriterion("taking_plan_no like", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotLike(String value) {
            addCriterion("taking_plan_no not like", value, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoIn(List<String> values) {
            addCriterion("taking_plan_no in", values, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotIn(List<String> values) {
            addCriterion("taking_plan_no not in", values, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoBetween(String value1, String value2) {
            addCriterion("taking_plan_no between", value1, value2, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andTakingPlanNoNotBetween(String value1, String value2) {
            addCriterion("taking_plan_no not between", value1, value2, "takingPlanNo");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIsNull() {
            addCriterion("need_dept is null");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIsNotNull() {
            addCriterion("need_dept is not null");
            return (Criteria) this;
        }

        public Criteria andNeedDeptEqualTo(String value) {
            addCriterion("need_dept =", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotEqualTo(String value) {
            addCriterion("need_dept <>", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptGreaterThan(String value) {
            addCriterion("need_dept >", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptGreaterThanOrEqualTo(String value) {
            addCriterion("need_dept >=", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLessThan(String value) {
            addCriterion("need_dept <", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLessThanOrEqualTo(String value) {
            addCriterion("need_dept <=", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptLike(String value) {
            addCriterion("need_dept like", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotLike(String value) {
            addCriterion("need_dept not like", value, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptIn(List<String> values) {
            addCriterion("need_dept in", values, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotIn(List<String> values) {
            addCriterion("need_dept not in", values, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptBetween(String value1, String value2) {
            addCriterion("need_dept between", value1, value2, "needDept");
            return (Criteria) this;
        }

        public Criteria andNeedDeptNotBetween(String value1, String value2) {
            addCriterion("need_dept not between", value1, value2, "needDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptIsNull() {
            addCriterion("cost_dept is null");
            return (Criteria) this;
        }

        public Criteria andCostDeptIsNotNull() {
            addCriterion("cost_dept is not null");
            return (Criteria) this;
        }

        public Criteria andCostDeptEqualTo(String value) {
            addCriterion("cost_dept =", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotEqualTo(String value) {
            addCriterion("cost_dept <>", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptGreaterThan(String value) {
            addCriterion("cost_dept >", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptGreaterThanOrEqualTo(String value) {
            addCriterion("cost_dept >=", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptLessThan(String value) {
            addCriterion("cost_dept <", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptLessThanOrEqualTo(String value) {
            addCriterion("cost_dept <=", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptLike(String value) {
            addCriterion("cost_dept like", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotLike(String value) {
            addCriterion("cost_dept not like", value, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptIn(List<String> values) {
            addCriterion("cost_dept in", values, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotIn(List<String> values) {
            addCriterion("cost_dept not in", values, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptBetween(String value1, String value2) {
            addCriterion("cost_dept between", value1, value2, "costDept");
            return (Criteria) this;
        }

        public Criteria andCostDeptNotBetween(String value1, String value2) {
            addCriterion("cost_dept not between", value1, value2, "costDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptIsNull() {
            addCriterion("holder_dept is null");
            return (Criteria) this;
        }

        public Criteria andHolderDeptIsNotNull() {
            addCriterion("holder_dept is not null");
            return (Criteria) this;
        }

        public Criteria andHolderDeptEqualTo(String value) {
            addCriterion("holder_dept =", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotEqualTo(String value) {
            addCriterion("holder_dept <>", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptGreaterThan(String value) {
            addCriterion("holder_dept >", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptGreaterThanOrEqualTo(String value) {
            addCriterion("holder_dept >=", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptLessThan(String value) {
            addCriterion("holder_dept <", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptLessThanOrEqualTo(String value) {
            addCriterion("holder_dept <=", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptLike(String value) {
            addCriterion("holder_dept like", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotLike(String value) {
            addCriterion("holder_dept not like", value, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptIn(List<String> values) {
            addCriterion("holder_dept in", values, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotIn(List<String> values) {
            addCriterion("holder_dept not in", values, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptBetween(String value1, String value2) {
            addCriterion("holder_dept between", value1, value2, "holderDept");
            return (Criteria) this;
        }

        public Criteria andHolderDeptNotBetween(String value1, String value2) {
            addCriterion("holder_dept not between", value1, value2, "holderDept");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoIsNull() {
            addCriterion("purchase_no is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoIsNotNull() {
            addCriterion("purchase_no is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoEqualTo(String value) {
            addCriterion("purchase_no =", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotEqualTo(String value) {
            addCriterion("purchase_no <>", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoGreaterThan(String value) {
            addCriterion("purchase_no >", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_no >=", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoLessThan(String value) {
            addCriterion("purchase_no <", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoLessThanOrEqualTo(String value) {
            addCriterion("purchase_no <=", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoLike(String value) {
            addCriterion("purchase_no like", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotLike(String value) {
            addCriterion("purchase_no not like", value, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoIn(List<String> values) {
            addCriterion("purchase_no in", values, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotIn(List<String> values) {
            addCriterion("purchase_no not in", values, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoBetween(String value1, String value2) {
            addCriterion("purchase_no between", value1, value2, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andPurchaseNoNotBetween(String value1, String value2) {
            addCriterion("purchase_no not between", value1, value2, "purchaseNo");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionIsNull() {
            addCriterion("bus_large_region is null");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionIsNotNull() {
            addCriterion("bus_large_region is not null");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionEqualTo(String value) {
            addCriterion("bus_large_region =", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotEqualTo(String value) {
            addCriterion("bus_large_region <>", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionGreaterThan(String value) {
            addCriterion("bus_large_region >", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionGreaterThanOrEqualTo(String value) {
            addCriterion("bus_large_region >=", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionLessThan(String value) {
            addCriterion("bus_large_region <", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionLessThanOrEqualTo(String value) {
            addCriterion("bus_large_region <=", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionLike(String value) {
            addCriterion("bus_large_region like", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotLike(String value) {
            addCriterion("bus_large_region not like", value, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionIn(List<String> values) {
            addCriterion("bus_large_region in", values, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotIn(List<String> values) {
            addCriterion("bus_large_region not in", values, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionBetween(String value1, String value2) {
            addCriterion("bus_large_region between", value1, value2, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusLargeRegionNotBetween(String value1, String value2) {
            addCriterion("bus_large_region not between", value1, value2, "busLargeRegion");
            return (Criteria) this;
        }

        public Criteria andBusCityIsNull() {
            addCriterion("bus_city is null");
            return (Criteria) this;
        }

        public Criteria andBusCityIsNotNull() {
            addCriterion("bus_city is not null");
            return (Criteria) this;
        }

        public Criteria andBusCityEqualTo(String value) {
            addCriterion("bus_city =", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotEqualTo(String value) {
            addCriterion("bus_city <>", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityGreaterThan(String value) {
            addCriterion("bus_city >", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityGreaterThanOrEqualTo(String value) {
            addCriterion("bus_city >=", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityLessThan(String value) {
            addCriterion("bus_city <", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityLessThanOrEqualTo(String value) {
            addCriterion("bus_city <=", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityLike(String value) {
            addCriterion("bus_city like", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotLike(String value) {
            addCriterion("bus_city not like", value, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityIn(List<String> values) {
            addCriterion("bus_city in", values, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotIn(List<String> values) {
            addCriterion("bus_city not in", values, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityBetween(String value1, String value2) {
            addCriterion("bus_city between", value1, value2, "busCity");
            return (Criteria) this;
        }

        public Criteria andBusCityNotBetween(String value1, String value2) {
            addCriterion("bus_city not between", value1, value2, "busCity");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andCpuIsNull() {
            addCriterion("cpu is null");
            return (Criteria) this;
        }

        public Criteria andCpuIsNotNull() {
            addCriterion("cpu is not null");
            return (Criteria) this;
        }

        public Criteria andCpuEqualTo(String value) {
            addCriterion("cpu =", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotEqualTo(String value) {
            addCriterion("cpu <>", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuGreaterThan(String value) {
            addCriterion("cpu >", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuGreaterThanOrEqualTo(String value) {
            addCriterion("cpu >=", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuLessThan(String value) {
            addCriterion("cpu <", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuLessThanOrEqualTo(String value) {
            addCriterion("cpu <=", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuLike(String value) {
            addCriterion("cpu like", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotLike(String value) {
            addCriterion("cpu not like", value, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuIn(List<String> values) {
            addCriterion("cpu in", values, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotIn(List<String> values) {
            addCriterion("cpu not in", values, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuBetween(String value1, String value2) {
            addCriterion("cpu between", value1, value2, "cpu");
            return (Criteria) this;
        }

        public Criteria andCpuNotBetween(String value1, String value2) {
            addCriterion("cpu not between", value1, value2, "cpu");
            return (Criteria) this;
        }

        public Criteria andHardDiskIsNull() {
            addCriterion("hard_disk is null");
            return (Criteria) this;
        }

        public Criteria andHardDiskIsNotNull() {
            addCriterion("hard_disk is not null");
            return (Criteria) this;
        }

        public Criteria andHardDiskEqualTo(String value) {
            addCriterion("hard_disk =", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskNotEqualTo(String value) {
            addCriterion("hard_disk <>", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskGreaterThan(String value) {
            addCriterion("hard_disk >", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskGreaterThanOrEqualTo(String value) {
            addCriterion("hard_disk >=", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskLessThan(String value) {
            addCriterion("hard_disk <", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskLessThanOrEqualTo(String value) {
            addCriterion("hard_disk <=", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskLike(String value) {
            addCriterion("hard_disk like", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskNotLike(String value) {
            addCriterion("hard_disk not like", value, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskIn(List<String> values) {
            addCriterion("hard_disk in", values, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskNotIn(List<String> values) {
            addCriterion("hard_disk not in", values, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskBetween(String value1, String value2) {
            addCriterion("hard_disk between", value1, value2, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andHardDiskNotBetween(String value1, String value2) {
            addCriterion("hard_disk not between", value1, value2, "hardDisk");
            return (Criteria) this;
        }

        public Criteria andRamMemoryIsNull() {
            addCriterion("ram_memory is null");
            return (Criteria) this;
        }

        public Criteria andRamMemoryIsNotNull() {
            addCriterion("ram_memory is not null");
            return (Criteria) this;
        }

        public Criteria andRamMemoryEqualTo(String value) {
            addCriterion("ram_memory =", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryNotEqualTo(String value) {
            addCriterion("ram_memory <>", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryGreaterThan(String value) {
            addCriterion("ram_memory >", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryGreaterThanOrEqualTo(String value) {
            addCriterion("ram_memory >=", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryLessThan(String value) {
            addCriterion("ram_memory <", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryLessThanOrEqualTo(String value) {
            addCriterion("ram_memory <=", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryLike(String value) {
            addCriterion("ram_memory like", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryNotLike(String value) {
            addCriterion("ram_memory not like", value, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryIn(List<String> values) {
            addCriterion("ram_memory in", values, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryNotIn(List<String> values) {
            addCriterion("ram_memory not in", values, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryBetween(String value1, String value2) {
            addCriterion("ram_memory between", value1, value2, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andRamMemoryNotBetween(String value1, String value2) {
            addCriterion("ram_memory not between", value1, value2, "ramMemory");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceIsNull() {
            addCriterion("holder_address_province is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceIsNotNull() {
            addCriterion("holder_address_province is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceEqualTo(String value) {
            addCriterion("holder_address_province =", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotEqualTo(String value) {
            addCriterion("holder_address_province <>", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceGreaterThan(String value) {
            addCriterion("holder_address_province >", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address_province >=", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceLessThan(String value) {
            addCriterion("holder_address_province <", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceLessThanOrEqualTo(String value) {
            addCriterion("holder_address_province <=", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceLike(String value) {
            addCriterion("holder_address_province like", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotLike(String value) {
            addCriterion("holder_address_province not like", value, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceIn(List<String> values) {
            addCriterion("holder_address_province in", values, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotIn(List<String> values) {
            addCriterion("holder_address_province not in", values, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceBetween(String value1, String value2) {
            addCriterion("holder_address_province between", value1, value2, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressProvinceNotBetween(String value1, String value2) {
            addCriterion("holder_address_province not between", value1, value2, "holderAddressProvince");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityIsNull() {
            addCriterion("holder_address_city is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityIsNotNull() {
            addCriterion("holder_address_city is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityEqualTo(String value) {
            addCriterion("holder_address_city =", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotEqualTo(String value) {
            addCriterion("holder_address_city <>", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityGreaterThan(String value) {
            addCriterion("holder_address_city >", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address_city >=", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityLessThan(String value) {
            addCriterion("holder_address_city <", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityLessThanOrEqualTo(String value) {
            addCriterion("holder_address_city <=", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityLike(String value) {
            addCriterion("holder_address_city like", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotLike(String value) {
            addCriterion("holder_address_city not like", value, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityIn(List<String> values) {
            addCriterion("holder_address_city in", values, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotIn(List<String> values) {
            addCriterion("holder_address_city not in", values, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityBetween(String value1, String value2) {
            addCriterion("holder_address_city between", value1, value2, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCityNotBetween(String value1, String value2) {
            addCriterion("holder_address_city not between", value1, value2, "holderAddressCity");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyIsNull() {
            addCriterion("holder_address_county is null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyIsNotNull() {
            addCriterion("holder_address_county is not null");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyEqualTo(String value) {
            addCriterion("holder_address_county =", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotEqualTo(String value) {
            addCriterion("holder_address_county <>", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyGreaterThan(String value) {
            addCriterion("holder_address_county >", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyGreaterThanOrEqualTo(String value) {
            addCriterion("holder_address_county >=", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyLessThan(String value) {
            addCriterion("holder_address_county <", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyLessThanOrEqualTo(String value) {
            addCriterion("holder_address_county <=", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyLike(String value) {
            addCriterion("holder_address_county like", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotLike(String value) {
            addCriterion("holder_address_county not like", value, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyIn(List<String> values) {
            addCriterion("holder_address_county in", values, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotIn(List<String> values) {
            addCriterion("holder_address_county not in", values, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyBetween(String value1, String value2) {
            addCriterion("holder_address_county between", value1, value2, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andHolderAddressCountyNotBetween(String value1, String value2) {
            addCriterion("holder_address_county not between", value1, value2, "holderAddressCounty");
            return (Criteria) this;
        }

        public Criteria andIsConfirmIsNull() {
            addCriterion("is_confirm is null");
            return (Criteria) this;
        }

        public Criteria andIsConfirmIsNotNull() {
            addCriterion("is_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andIsConfirmEqualTo(Integer value) {
            addCriterion("is_confirm =", value, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmNotEqualTo(Integer value) {
            addCriterion("is_confirm <>", value, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmGreaterThan(Integer value) {
            addCriterion("is_confirm >", value, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_confirm >=", value, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmLessThan(Integer value) {
            addCriterion("is_confirm <", value, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmLessThanOrEqualTo(Integer value) {
            addCriterion("is_confirm <=", value, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmIn(List<Integer> values) {
            addCriterion("is_confirm in", values, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmNotIn(List<Integer> values) {
            addCriterion("is_confirm not in", values, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmBetween(Integer value1, Integer value2) {
            addCriterion("is_confirm between", value1, value2, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsConfirmNotBetween(Integer value1, Integer value2) {
            addCriterion("is_confirm not between", value1, value2, "isConfirm");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskIsNull() {
            addCriterion("is_assign_task is null");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskIsNotNull() {
            addCriterion("is_assign_task is not null");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskEqualTo(Integer value) {
            addCriterion("is_assign_task =", value, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskNotEqualTo(Integer value) {
            addCriterion("is_assign_task <>", value, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskGreaterThan(Integer value) {
            addCriterion("is_assign_task >", value, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_assign_task >=", value, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskLessThan(Integer value) {
            addCriterion("is_assign_task <", value, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskLessThanOrEqualTo(Integer value) {
            addCriterion("is_assign_task <=", value, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskIn(List<Integer> values) {
            addCriterion("is_assign_task in", values, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskNotIn(List<Integer> values) {
            addCriterion("is_assign_task not in", values, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskBetween(Integer value1, Integer value2) {
            addCriterion("is_assign_task between", value1, value2, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAssignTaskNotBetween(Integer value1, Integer value2) {
            addCriterion("is_assign_task not between", value1, value2, "isAssignTask");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckIsNull() {
            addCriterion("is_auto_check is null");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckIsNotNull() {
            addCriterion("is_auto_check is not null");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckEqualTo(Integer value) {
            addCriterion("is_auto_check =", value, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckNotEqualTo(Integer value) {
            addCriterion("is_auto_check <>", value, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckGreaterThan(Integer value) {
            addCriterion("is_auto_check >", value, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_auto_check >=", value, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckLessThan(Integer value) {
            addCriterion("is_auto_check <", value, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckLessThanOrEqualTo(Integer value) {
            addCriterion("is_auto_check <=", value, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckIn(List<Integer> values) {
            addCriterion("is_auto_check in", values, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckNotIn(List<Integer> values) {
            addCriterion("is_auto_check not in", values, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckBetween(Integer value1, Integer value2) {
            addCriterion("is_auto_check between", value1, value2, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andIsAutoCheckNotBetween(Integer value1, Integer value2) {
            addCriterion("is_auto_check not between", value1, value2, "isAutoCheck");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnIsNull() {
            addCriterion("guagua_login_sn is null");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnIsNotNull() {
            addCriterion("guagua_login_sn is not null");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnEqualTo(String value) {
            addCriterion("guagua_login_sn =", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnNotEqualTo(String value) {
            addCriterion("guagua_login_sn <>", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnGreaterThan(String value) {
            addCriterion("guagua_login_sn >", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnGreaterThanOrEqualTo(String value) {
            addCriterion("guagua_login_sn >=", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnLessThan(String value) {
            addCriterion("guagua_login_sn <", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnLessThanOrEqualTo(String value) {
            addCriterion("guagua_login_sn <=", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnLike(String value) {
            addCriterion("guagua_login_sn like", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnNotLike(String value) {
            addCriterion("guagua_login_sn not like", value, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnIn(List<String> values) {
            addCriterion("guagua_login_sn in", values, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnNotIn(List<String> values) {
            addCriterion("guagua_login_sn not in", values, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnBetween(String value1, String value2) {
            addCriterion("guagua_login_sn between", value1, value2, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andGuaguaLoginSnNotBetween(String value1, String value2) {
            addCriterion("guagua_login_sn not between", value1, value2, "guaguaLoginSn");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}