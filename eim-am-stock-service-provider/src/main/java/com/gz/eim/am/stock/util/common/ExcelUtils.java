package com.gz.eim.am.stock.util.common;


import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.fuu.eim.tool.excel.ExportModel;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: weijunjie
 * @date: 2020/7/7
 * @description
 */
public class ExcelUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelUtils.class);

    /**
     * 根据传入的表头数据，动态生成excel并下载
     * @param fieldNames
     * @param fileName
     * @param request
     * @param response
     */
    public static void createExcelWithBuffer(List<String> fieldNames, String fileName, HttpServletRequest request, HttpServletResponse response) {
        String agent = request.getHeader("USER-AGENT");

        try {
            if (null == agent || !agent.contains("MSIE") && !agent.contains("Trident")) {
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1"));
            } else {
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF8"));
            }

            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            OutputStream outputStream = response.getOutputStream();
            Workbook workbook = new SXSSFWorkbook();
            Sheet sheet = workbook.createSheet("sheet0");
            Row row = sheet.createRow(0);
            for(int i=0;i<fieldNames.size();i++){
                Cell cell = row.createCell(i);
                cell.setCellValue(fieldNames.get(i));
                cell.setCellStyle(createDefaultDataStyle(workbook));
            }

            workbook.write(outputStream);
        } catch (Exception e) {
            LOGGER.error("生成excel模版失败");
        }

    }

    /**
     * 设置格式
     * @param workbook
     * @return
     */
    private static CellStyle createDefaultDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor((short)9);
        style.setFillPattern((short)1);
        style.setAlignment((short)2);
        style.setBorderBottom((short)1);
        style.setBorderLeft((short)1);
        style.setBorderRight((short)1);
        style.setBorderTop((short)1);
        style.setWrapText(true);
        Font font = workbook.createFont();
        font.setFontName("黑体");
        font.setFontHeightInPoints((short)10);
        font.setColor((short)8);
        style.setFont(font);
        return style;
    }



    /**
     * 动态解析excel
     * List<数据行>
     * Map<列名称，列对应数据>
     * @param is
     * @return
     * @throws IOException
     * @throws InvalidFormatException
     */
    public static List<Map<String,Object>> getData(InputStream is) throws IOException, InvalidFormatException {
        List<Map<String,Object>> result = new ArrayList<>();
        List<String> slist = new ArrayList<>();
        String[][] arr = getExcelData(is,0);
        for(int i=0;i<arr.length;i++){

            Map<String,Object> tmap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
            for(int j=0;j<arr[i].length;j++){
                if(i==0){
                    slist.add(arr[i][j]);
                }else{
                    tmap.put(slist.get(j),arr[i][j]);
                }
            }
            if(i>0){
                result.add(tmap);
            }
        }
        return result;
    }

    private static String[][] getExcelData(InputStream is, int ignoreRows) throws IOException, InvalidFormatException {
        List<String[]> result = new ArrayList();
        Workbook workbook = WorkbookFactory.create(is);
        int sheetNum = 1;
        int cellSize = 0;

        for(int i = 0; i < sheetNum; ++i) {
            Sheet sheet = workbook.getSheetAt(i);

            for(int rowIndex = ignoreRows; rowIndex <= sheet.getLastRowNum(); ++rowIndex) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    int tempCellSize = row.getLastCellNum();
                    if (tempCellSize > cellSize) {
                        cellSize = tempCellSize;
                    }

                    String[] cellValues = new String[cellSize];
                    Arrays.fill(cellValues, "");
                    boolean hasValue = false;

                    for(int columnIndex = 0; columnIndex < row.getLastCellNum(); ++columnIndex) {
                        String cellValue = null;
                        Cell cell = row.getCell(columnIndex);
                        if (cell != null) {
                            cellValue = getCellValue(cell);
                        }

                        if (cellValue != null) {
                            cellValue = cellValue.trim();
                        }

                        cellValues[columnIndex] = cellValue;
                        hasValue = true;
                    }

                    if (hasValue) {
                        result.add(cellValues);
                    }
                }
            }
        }

        String[][] returnArray = new String[result.size()][cellSize];

        for(int i = 0; i < returnArray.length; ++i) {
            returnArray[i] = (String[])((String[])result.get(i));
        }

        return returnArray;
    }

    private static String getCellValue(Cell cell) {
        String cellValue = null;
        int cellType = cell.getCellType();
        String temp;
        switch(cellType) {
            case 0:
                if (DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = cell.getDateCellValue();
                    if (date != null) {
                        cellValue = String.valueOf(dateFormat.format(date));
                    }
                } else {
                    cell.setCellType(1);
                    temp = cell.getRichStringCellValue().getString();
                    cellValue = getNumberStr(temp);
                }
                break;
            case 1:
                cellValue = cell.getRichStringCellValue().getString();
                break;
            case 2:
                temp = cell.getCellFormula();
                if (null != temp) {
                    cellValue = temp.replaceAll("#N/A", "").trim();
                }
                break;
            case 3:
                cellValue = null;
                break;
            case 4:
                cellValue = Boolean.toString(cell.getBooleanCellValue());
                break;
            case 5:
                cellValue = null;
                break;
            default:
                cellValue = "";
        }

        return cellValue;
    }

    private static String getNumberStr(String temp) {
        String cellValue = "";
        if (temp.contains(".")) {
            String temp1 = temp.substring(temp.indexOf(".") + 1, temp.length());
            if (!StringUtils.isEmpty(temp1)) {
                if (temp1.matches("0+$")) {
                    cellValue = temp.substring(0, temp.indexOf("."));
                } else {
                    cellValue = temp;
                }
            }
        } else {
            cellValue = temp;
        }

        return cellValue;
    }

     /**
       * @description: 根据数据生成excel
       * @author: <EMAIL>
       * @date: 2022/4/22
       */
    public static <T> MockMultipartFile getExcelToMockMultipartFile(List<T> sourceList, Class<? extends ExportModel> targetClass, String fileName) throws Exception{
        if(CollectionUtils.isEmpty(sourceList)){
            return null;
        }
        List<? extends ExportModel> exportModelList = ConvertUtil.convertToType(targetClass, sourceList);
        // 生成输出流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
        ExcelUtil.createExcelWithBuffer(exportModelList, bufferedOutputStream);
        bufferedOutputStream.close();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        BufferedInputStream bufferedInputStream = new BufferedInputStream(byteArrayInputStream);
        LocalDateTime dateTime = LocalDateTime.now();
        fileName = fileName + dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        MockMultipartFile multipartFile = new MockMultipartFile("file", fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), bufferedInputStream);
        bufferedInputStream.close();
        return multipartFile;
    }

    /**
     * @description: 根据数据生成excel
     * @author: <EMAIL>
     * @date: 2022/4/22
     */
    public static MockMultipartFile getExcelToMockMultipartFile(List<? extends ExportModel> sourceList,  String fileName) throws Exception{
        if(CollectionUtils.isEmpty(sourceList)){
            return null;
        }
        // 生成输出流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
        ExcelUtil.createExcelWithBuffer(sourceList, bufferedOutputStream);
        bufferedOutputStream.close();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        BufferedInputStream bufferedInputStream = new BufferedInputStream(byteArrayInputStream);
        LocalDateTime dateTime = LocalDateTime.now();
        fileName = fileName + dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        MockMultipartFile multipartFile = new MockMultipartFile("file", fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), bufferedInputStream);
        bufferedInputStream.close();
        return multipartFile;
    }

}
