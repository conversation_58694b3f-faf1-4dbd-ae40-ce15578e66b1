package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.math.BigDecimal;

/**
 * @author: <EMAIL>
 * @date: 2020/1/3
 * @description: 报废资产
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportAssetsScrapRecordEntity implements ExportModel{
    /**
     * 资产编码
     */
    @ExportField(name = "资产编码")
    private String assetsCode;
    /**
     * 设备序列号
     */
    @ExportField(name = "设备序列号")
    private String snCode;
    /**
     * 资产名称
     */
    @ExportField(name = "资产名称")
    private String assetsName;
    /**
     * 资产分类
     */
    @ExportField(name = "资产分类")
    private String category;
    /**
     * 资产使用状态
     */
    @ExportField(name = "资产使用状态")
    private String conditionsName;
    /**
     * 资产状态
     */
    @ExportField(name = "资产状态")
    private String statusName;
    /**
     * 所在仓库
     */
    @ExportField(name = "所在仓库")
    private String warehouseName;
    /**
     * 资产管理员
     */
    @ExportField(name = "资产管理员")
    private String assetsKeeperName;
    /**
     * 采购码
     */
    @ExportField(name = "采购码")
    private String suppliesCode;
    /**
     * 所在公司
     */
    @ExportField(name = "所属公司")
    private String companyName;
    /**
     * 报废原因
     */
    @ExportField(name = "报废原因")
    private String scrapReasonName;
    /**
     * 资产净值
     */
    @ExportField(name = "资产净值")
    private BigDecimal initialValue;
    /**
     * 币种
     */
    @ExportField(name = "币种")
    private String currency;
    /**
     * 报废时间
     */
    @ExportField(name = "报废时间")
    private String scrapTime;
    /**
     * 资产新增时间
     */
    @ExportField(name = "处置时间")
    private String dealTime;
    /**
     * 是否需要同步ebs
     */
    @ExportField(name = "是否需要同步ebs")
    private String extractStatusName;
    @Override
    public String getSheetName() {
        return "资产报废报表";
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getSnCode() {
        return snCode;
    }

    public void setSnCode(String snCode) {
        this.snCode = snCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getConditionsName() {
        return conditionsName;
    }

    public void setConditionsName(String conditionsName) {
        this.conditionsName = conditionsName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getAssetsKeeperName() {
        return assetsKeeperName;
    }

    public void setAssetsKeeperName(String assetsKeeperName) {
        this.assetsKeeperName = assetsKeeperName;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getScrapReasonName() {
        return scrapReasonName;
    }

    public void setScrapReasonName(String scrapReasonName) {
        this.scrapReasonName = scrapReasonName;
    }

    public BigDecimal getInitialValue() {
        return initialValue;
    }

    public void setInitialValue(BigDecimal initialValue) {
        this.initialValue = initialValue;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getScrapTime() {
        return scrapTime;
    }

    public void setScrapTime(String scrapTime) {
        this.scrapTime = scrapTime;
    }

    public String getDealTime() {
        return dealTime;
    }

    public void setDealTime(String dealTime) {
        this.dealTime = dealTime;
    }

    public String getExtractStatusName() {
        return extractStatusName;
    }

    public void setExtractStatusName(String extractStatusName) {
        this.extractStatusName = extractStatusName;
    }
}
