package com.gz.eim.am.stock.service.inventory.plan;

import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSns;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/7/3
 * @description
 */
public interface StockInventoryInPlanLineSnsService {

    /**
     * 批量插入计划入库单行Info
     * @param stockInventoryInPlanLinesSnsList
     */
    Integer batchInsertStockInventoryPlanLineSns(List<StockInventoryInPlanLinesSns> stockInventoryInPlanLinesSnsList);

    /**
     * 根据入库单行id查询数量
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    Long selectCountByParam(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 查询入库单行下的序列号
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    List<InventoryInPlanLineAssetRespDTO> selectByPage(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);
}
