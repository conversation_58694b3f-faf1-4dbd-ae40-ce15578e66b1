package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsDemandLine;
import com.gz.eim.am.stock.entity.StockAssetsDemandLineExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsDemandLineMapper {
    long countByExample(StockAssetsDemandLineExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsDemandLine record);

    int insertSelective(StockAssetsDemandLine record);

    List<StockAssetsDemandLine> selectByExample(StockAssetsDemandLineExample example);

    StockAssetsDemandLine selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsDemandLine record, @Param("example") StockAssetsDemandLineExample example);

    int updateByExample(@Param("record") StockAssetsDemandLine record, @Param("example") StockAssetsDemandLineExample example);

    int updateByPrimaryKeySelective(StockAssetsDemandLine record);

    int updateByPrimaryKey(StockAssetsDemandLine record);
}