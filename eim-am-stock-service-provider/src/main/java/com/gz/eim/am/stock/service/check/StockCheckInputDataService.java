package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.check.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;

import java.util.List;

/**
 * @Author: wangjing67
 * @Date: 11/20/20 11:54 上午
 * @description
 */
public interface StockCheckInputDataService {

    /**
     * 根据责任人与盘点方式查询盘点任务 --员工自助
     *
     * @param user 当前登录人
     * @return
     * @throws Exception
     */
    ResponseData queryEmployeeTask(JwtUser user,Integer pageNum,Long checkTaskId, Integer checkStatus) throws Exception;

    /**
     * 根据责任人与盘点方式查询盘点任务 --管理员自助
     *
     * @param user       当前登录人
     * @param finishFlag 1 待处理 2 已完成
     * @return
     * @throws Exception
     */
    ResponseData queryTaskByParam(JwtUser user, Integer finishFlag,Integer pageNum, Integer checkMethod) throws Exception;


    /**
     * 上报当前盘点任务下的其他资产
     *
     * @param user
     * @param stockAssetsReportReqDTO
     * @return
     * @throws Exception
     */
    ResponseData reportCheckTaskAsset(JwtUser user, StockAssetsReportReqDTO stockAssetsReportReqDTO) throws Exception;

    /**
     * 查询任务下待盘点任务仓库列表
     *
     * @param user
     * @param checkTaskId
     * @param keyWord
     * @return
     * @throws Exception
     */
    ResponseData selectTaskWarehouseList(JwtUser user, Long checkTaskId, String keyWord) throws Exception;

    /**
     * 根据资产编码或序列号查询资产信息
     *
     * @param assetsCodeOrSnNo
     * @param takingPlanNo
     * @return
     * @throws Exception
     */
    ResponseData selectByAssetsCodeOrSnNo(String assetsCodeOrSnNo,String takingPlanNo,Long taskDetailId) throws Exception;


    /**
     * 根据盘点任务id  仓库位置 查询待盘点资产
     *
     * @param stockCheckCommonReqDTO
     * @param user
     * @return
     * @throws Exception
     */
    ResponseData selectWaitCheckAssets(StockCheckCommonReqDTO stockCheckCommonReqDTO,JwtUser user) throws Exception;

    /**
     * 提交盘点结果
     *
     * @param stockCheckSubmitResultReqDTO
     * @param user
     * @return
     * @throws Exception
     */
    ResponseData submitCheckResult(StockCheckSubmitResultReqDTO stockCheckSubmitResultReqDTO, JwtUser user) throws Exception;

    /**
     * 盘点方式配置化信息查询接口
     * @param checkMethod
     * @param user
     * @return
     * @throws Exception
     */
    ResponseData selectCheckMethodConfig(Long checkMethod,JwtUser user)throws Exception;

    /**
     * 查询员工已盘点资产信息
     * @param taskDetailId
     * @return
     */
    ResponseData showAlreadyCheckAssets(Long taskDetailId);

    /**
     * 查询所有的地址信息
     * @return
     */
    ResponseData queryAllAddress();

    /**
     * 分页查询任务下所有的仓库
     * @param checkTaskId
     * @param pageNum
     * @return
     */
    ResponseData queryTaskWarehouseAll(Long checkTaskId, Integer pageNum, String keyWord);
    /**
     * @param:
     * @description: 员工手动结束盘点
     * @return:
     * @author: <EMAIL>
     * @date: 2022/1/20
     */
    ResponseData employeeFinishCheck(Long checkTaskId);
    /**
     * @param: stockCheckSubmitResultSuperReqDTO
     * @description: 盘点录入数据
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    ResponseData submitCheckResultSuper(StockCheckSubmitResultSuperReqDTO stockCheckSubmitResultSuperReqDTO, JwtUser user);
    /**
     * @param: checkTaskId
     * @description: 员工手动结束盘点
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/8
     */
    ResponseData employeeFinishCheckSuper(Long checkTaskId);
    /**
     * @param: checkTaskNo,checkPerson
     * @description: 查询员工异常盘点资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/10
     */
    ResponseData queryEmployeeAbnormalCheckAssets(String checkTaskNo, String checkPerson);
    /**
     * @param: stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO,loginUser
     * @description: 更新员工异常盘点资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/13
     */
    ResponseData updateEmployeeAbnormalCheckAssets(StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO, JwtUser loginUser);
}
