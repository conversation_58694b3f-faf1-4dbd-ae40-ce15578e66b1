package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: ZtoDeliveryExtendEnum
 * @description: 中通快递枚举类
 * @author: <EMAIL>
 * @date: 2023/6/15
 **/
public class ZtoExpressEnum {

    public enum OrderType {
        FULL_NETWORK_ORDER("1", "全网件"),
        RESERVATION_ORDER("2", "预约件");
        OrderType(String code, String desc){
            this.code = code;
            this.desc = desc;
        }

        private String code;
        private String desc;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public static final Map<String, OrderType> orderTypeMap = Arrays.stream(OrderType.values()).collect(Collectors.toMap(OrderType:: getCode, orderType -> orderType));
}
