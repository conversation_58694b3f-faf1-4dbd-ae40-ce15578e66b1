package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsEbsSyncReqDTO;

import java.io.IOException;
import java.text.ParseException;

/**
 * @author: wei<PERSON><PERSON>e
 * @date: 2020/10/9
 * @description
 */
public interface StockAssetsExtractService {

    /**
     * 抽取要同步到ebs的资产到记录表
     *
     * @param wfl
     * @return
     * @throws ParseException
     */
    ResponseData extractAssets(String wfl) throws ParseException;

    /**
     * 同步ebs数据处理结果
     *
     * @return
     */
    ResponseData syncResult();

    /**
     * 工作流回调查询待同步到ebs的所有资产数据
     *
     * @param bizId
     * @return
     */
    ResponseData selectWflFormLineListByBatchNo(String bizId);

    /**
     * 审批通过，执行数据同步动作
     *
     * @param bizId
     * @param status
     * @return
     */
    ResponseData complete(String bizId, Integer status);


    ////////////////////////ebs同步数据二期//////////////////////

    /**
     * 抽取同步数据
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     * @throws ParseException
     */
    ResponseData extractAndSyncAssets(String startDateStr, String endDateStr) throws ParseException;

    /**
     * 数据更新操作
     *
     * @param assetsEbsSyncReqDTO
     * @return
     */
    ResponseData syncUpdate(AssetsEbsSyncReqDTO assetsEbsSyncReqDTO);
}
