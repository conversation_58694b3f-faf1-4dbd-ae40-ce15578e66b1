package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockDeliveryDetailAsset;
import com.gz.eim.am.stock.entity.StockDeliveryDetailAssetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockDeliveryDetailAssetMapper {
    long countByExample(StockDeliveryDetailAssetExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockDeliveryDetailAsset record);

    int insertSelective(StockDeliveryDetailAsset record);

    List<StockDeliveryDetailAsset> selectByExample(StockDeliveryDetailAssetExample example);

    StockDeliveryDetailAsset selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockDeliveryDetailAsset record, @Param("example") StockDeliveryDetailAssetExample example);

    int updateByExample(@Param("record") StockDeliveryDetailAsset record, @Param("example") StockDeliveryDetailAssetExample example);

    int updateByPrimaryKeySelective(StockDeliveryDetailAsset record);

    int updateByPrimaryKey(StockDeliveryDetailAsset record);
}