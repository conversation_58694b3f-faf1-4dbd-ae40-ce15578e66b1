package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSnAsset;
import com.gz.eim.am.stock.entity.StockSnAssetExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSnAssetMapper {
    long countByExample(StockSnAssetExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockSnAsset record);

    int insertSelective(StockSnAsset record);

    List<StockSnAsset> selectByExample(StockSnAssetExample example);

    StockSnAsset selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockSnAsset record, @Param("example") StockSnAssetExample example);

    int updateByExample(@Param("record") StockSnAsset record, @Param("example") StockSnAssetExample example);

    int updateByPrimaryKeySelective(StockSnAsset record);

    int updateByPrimaryKey(StockSnAsset record);
}