package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSuppliesPurchaseExt;
import com.gz.eim.am.stock.entity.StockSuppliesPurchaseExtExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSuppliesPurchaseExtMapper {
    long countByExample(StockSuppliesPurchaseExtExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockSuppliesPurchaseExt record);

    int insertSelective(StockSuppliesPurchaseExt record);

    List<StockSuppliesPurchaseExt> selectByExample(StockSuppliesPurchaseExtExample example);

    StockSuppliesPurchaseExt selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockSuppliesPurchaseExt record, @Param("example") StockSuppliesPurchaseExtExample example);

    int updateByExample(@Param("record") StockSuppliesPurchaseExt record, @Param("example") StockSuppliesPurchaseExtExample example);

    int updateByPrimaryKeySelective(StockSuppliesPurchaseExt record);

    int updateByPrimaryKey(StockSuppliesPurchaseExt record);
}