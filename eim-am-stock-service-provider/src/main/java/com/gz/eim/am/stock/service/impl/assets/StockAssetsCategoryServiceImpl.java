package com.gz.eim.am.stock.service.impl.assets;


import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.assets.AssetsCategoryMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsCategoryMapper;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCategoryReqDTO;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.entity.StockAssetCompany;
import com.gz.eim.am.stock.entity.StockAssetCompanyExample;
import com.gz.eim.am.stock.entity.StockAssetsCategory;
import com.gz.eim.am.stock.entity.StockAssetsCategoryExample;
import com.gz.eim.am.stock.service.assets.StockAssetsCategoryService;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2019-12-12 APM 10:58
 */
@Service
public class StockAssetsCategoryServiceImpl implements StockAssetsCategoryService {

    @Autowired
    private StockAssetsCategoryMapper mapper;

    @Autowired
    private AssetsCategoryMapper assetsCategoryMapper;

     /**
       * @param:
       * @description: 根据查询条件获取所有的资产分类
       * @return:
       * @author: <EMAIL>
       * @date: 2021/11/22
       */
    @Override
    public List<StockAssetsCategory> selectAssetsCategoryByDTO(StockAssetsCategoryReqDTO stockAssetsCategoryReqDTO) {
        return assetsCategoryMapper.selectAssetsCategoryByDTO(stockAssetsCategoryReqDTO);
    }

    @Override
    public ResponseData selectAssetsCategory() {
        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        List<StockAssetsCategory> categoryList = mapper.selectByExample(example);
        ResponseData resp = ResponseData.createSuccessResult(categoryList);
        return resp;
    }

    @Override
    public List<StockAssetsCategory> selectAssetsCategory(final List<Long> ids, final List<String> codes) {
        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        criteria.andStatusEqualTo(CommonConstant.NUMBER_ONE);
        if (ids != null && ids.size() > 0) {
            criteria.andIdIn(ids);
            return this.mapper.selectByExample(example);
        } else if (codes != null && codes.size() > 0) {
            criteria.andCategoryCodeIn(codes);
            return this.mapper.selectByExample(example);
        }
        return null;
    }

    @Override
    public Map<String, StockAssetsCategory> getAssetsCategoryMap(List<Long> ids, List<String> codes) {
        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        criteria.andStatusEqualTo(CommonConstant.NUMBER_ONE);
        List<StockAssetsCategory> stockAssetsCategoryList = null;
        if (ids != null && ids.size() > 0) {
            criteria.andIdIn(ids);
            stockAssetsCategoryList =  this.mapper.selectByExample(example);
        } else if (codes != null && codes.size() > 0) {
            criteria.andCategoryCodeIn(codes);
            stockAssetsCategoryList =  this.mapper.selectByExample(example);
        }
        if(CollectionUtils.isEmpty(stockAssetsCategoryList)){
            return new HashMap<>(2);
        }
        return stockAssetsCategoryList.stream().collect(Collectors.toMap(stockAssetsCategory -> stockAssetsCategory.getCategoryCode(), stockAssetsCategory -> stockAssetsCategory, (v1, v2) -> v2));
    }

    @Override
    public List<StockAssetsCategory> selectByStockAssetsCategory(StockAssetsCategory stockAssetsCategory) {
        if(null == stockAssetsCategory){
            return new ArrayList<>();
        }
        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(stockAssetsCategory.getCategoryCode())){
            criteria.andCategoryCodeEqualTo(stockAssetsCategory.getCategoryCode());
        }

        if(null != stockAssetsCategory.getDelFlag()){
            criteria.andDelFlagEqualTo(stockAssetsCategory.getDelFlag());
        }

        if(StringUtils.isNotBlank(stockAssetsCategory.getCategoryName())){
            criteria.andCategoryNameEqualTo(stockAssetsCategory.getCategoryName());
        }

        if(null != stockAssetsCategory.getStatus()){
            criteria.andStatusEqualTo(stockAssetsCategory.getStatus());
        }

        return mapper.selectByExample(example);
    }

    @Override
    public List<StockAssetsCategory> selectAssetsCategoryByCategoryCodeList(List<String> categoryCodeList) {
        if(CollectionUtils.isEmpty(categoryCodeList)){
            return new ArrayList<>();
        }

        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andCategoryCodeIn(categoryCodeList);
        return mapper.selectByExample(example);
    }

    @Override
    public ResponseData selectAssetsCategoryVague(String param, Integer limit, String notIncludeCategoryList) {
        if (StringUtils.isBlank(param)) {
            return ResponseData.createFailResult("查询参数不能为空");
        }
        if (limit != null && limit <= 0){
            return ResponseData.createFailResult("查询参数不正确");
        }
        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andCategoryNameLike("%"+param+"%");
        if (null != limit){
            example.setLimit(limit);
        }
        if(StringUtils.isNotEmpty(notIncludeCategoryList)){
            criteria.andCategoryCodeNotIn(Arrays.asList(notIncludeCategoryList.split(",")));
        }
        List<StockAssetsCategory> stockAssetsCategoryList = mapper.selectByExample(example);
        if (CollectionUtils.isEmpty(stockAssetsCategoryList)) {
            return ResponseData.createSuccessResult(new ArrayList<>(CommonConstant.NUMBER_ZERO));
        }
        return ResponseData.createSuccessResult(stockAssetsCategoryList);
    }

    @Override
    public StockAssetsCategory selectByCategoryCode(String categoryCode) {
        StockAssetsCategoryExample example = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(categoryCode)) {
            criteria.andCategoryCodeEqualTo(categoryCode);
        }
        List<StockAssetsCategory> stockAssetsCategoryList = mapper.selectByExample(example);
        return !CollectionUtils.isEmpty(stockAssetsCategoryList) ? stockAssetsCategoryList.get(0) : null;
    }

    @Override
    public ResponseData selectByStockAssetsCategoryByPage(StockAssetsCategoryReqDTO stockAssetsCategory) {
        PageRespDTO pageRespDTO = PageRespDTO.createSuccessRes();
        stockAssetsCategory.initPageDefaultParam();
        stockAssetsCategory.setStartNum((stockAssetsCategory.getPageNum() - 1) * stockAssetsCategory.getPageSize());
        StockAssetsCategoryExample categoryExample = new StockAssetsCategoryExample();
        StockAssetsCategoryExample.Criteria criteria = categoryExample.createCriteria();
        if(StringUtils.isNotBlank(stockAssetsCategory.getCategoryCode())){
            criteria.andCategoryCodeLike("%"+ stockAssetsCategory.getCategoryCode() +"%");
        }

        if(StringUtils.isNotBlank(stockAssetsCategory.getCategoryName())){
            criteria.andCategoryNameLike("%"+ stockAssetsCategory.getCategoryName() +"%");
        }
        criteria.andDelFlagEqualTo(0);
        Long totalCount = mapper.countByExample(categoryExample);
        categoryExample.setLimit(stockAssetsCategory.getPageSize());
        categoryExample.setOffset(stockAssetsCategory.getStartNum());
        List<StockAssetsCategory> list = mapper.selectByExample(categoryExample);
        pageRespDTO.setPageSize(stockAssetsCategory.getPageSize());
        pageRespDTO.setCount(totalCount);
        pageRespDTO.setPageNum(stockAssetsCategory.getPageNum());
        pageRespDTO.setData(list);
        return pageRespDTO;
    }

}
