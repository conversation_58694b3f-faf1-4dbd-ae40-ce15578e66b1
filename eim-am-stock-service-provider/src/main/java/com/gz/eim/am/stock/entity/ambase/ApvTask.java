package com.gz.eim.am.stock.entity.ambase;

import java.util.Date;

public class ApvTask {
    private Long id;

    private String sn;

    private String flowId;

    private String formId;

    private String bizId;

    private Integer flowCategoryId;

    private String categoryCode;

    private String nodeCode;

    private String nodeName;

    private String apprvoeTitle;

    private String applyUser;

    private String approveUser;

    private String status;

    private String actionCode;

    private String actionValue;

    private String actionLabel;

    private Date apvDate;

    private Integer apvSource;

    private String remark;

    private Date startDate;

    private Date expireDate;

    private Date expireNotifyDate;

    private Integer delayMaxNum;

    private Integer delayCurNum;

    private Integer overTimeMaxNum;

    private Integer overTimeCurNum;

    private Date overTimeDateTime;

    private Integer isBeta;

    private String betaUsers;

    private String nodeType;

    private Integer notify;

    private String notifyContent;

    private String callBackStr;

    private Integer emergency;

    private String mailCheckCode;

    private Integer mailCheckCodeStatus;

    private Date mailCheckCodeBegin;

    private Date mailCheckCodeEnd;

    private Integer isAgent;

    private String agentUser;

    private String applyUserName;

    private String isApprove;

    private Date createDate;

    private String createUser;

    private Date updateDate;

    private String updateUser;

    private String processId;

    private String batchNo;

    private String opinion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn == null ? null : sn.trim();
    }

    public String getFlowId() {
        return flowId;
    }

    public void setFlowId(String flowId) {
        this.flowId = flowId == null ? null : flowId.trim();
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId == null ? null : formId.trim();
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId == null ? null : bizId.trim();
    }

    public Integer getFlowCategoryId() {
        return flowCategoryId;
    }

    public void setFlowCategoryId(Integer flowCategoryId) {
        this.flowCategoryId = flowCategoryId;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode == null ? null : categoryCode.trim();
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode == null ? null : nodeCode.trim();
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName == null ? null : nodeName.trim();
    }

    public String getApprvoeTitle() {
        return apprvoeTitle;
    }

    public void setApprvoeTitle(String apprvoeTitle) {
        this.apprvoeTitle = apprvoeTitle == null ? null : apprvoeTitle.trim();
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser == null ? null : applyUser.trim();
    }

    public String getApproveUser() {
        return approveUser;
    }

    public void setApproveUser(String approveUser) {
        this.approveUser = approveUser == null ? null : approveUser.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode == null ? null : actionCode.trim();
    }

    public String getActionValue() {
        return actionValue;
    }

    public void setActionValue(String actionValue) {
        this.actionValue = actionValue == null ? null : actionValue.trim();
    }

    public String getActionLabel() {
        return actionLabel;
    }

    public void setActionLabel(String actionLabel) {
        this.actionLabel = actionLabel == null ? null : actionLabel.trim();
    }

    public Date getApvDate() {
        return apvDate;
    }

    public void setApvDate(Date apvDate) {
        this.apvDate = apvDate;
    }

    public Integer getApvSource() {
        return apvSource;
    }

    public void setApvSource(Integer apvSource) {
        this.apvSource = apvSource;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getExpireNotifyDate() {
        return expireNotifyDate;
    }

    public void setExpireNotifyDate(Date expireNotifyDate) {
        this.expireNotifyDate = expireNotifyDate;
    }

    public Integer getDelayMaxNum() {
        return delayMaxNum;
    }

    public void setDelayMaxNum(Integer delayMaxNum) {
        this.delayMaxNum = delayMaxNum;
    }

    public Integer getDelayCurNum() {
        return delayCurNum;
    }

    public void setDelayCurNum(Integer delayCurNum) {
        this.delayCurNum = delayCurNum;
    }

    public Integer getOverTimeMaxNum() {
        return overTimeMaxNum;
    }

    public void setOverTimeMaxNum(Integer overTimeMaxNum) {
        this.overTimeMaxNum = overTimeMaxNum;
    }

    public Integer getOverTimeCurNum() {
        return overTimeCurNum;
    }

    public void setOverTimeCurNum(Integer overTimeCurNum) {
        this.overTimeCurNum = overTimeCurNum;
    }

    public Date getOverTimeDateTime() {
        return overTimeDateTime;
    }

    public void setOverTimeDateTime(Date overTimeDateTime) {
        this.overTimeDateTime = overTimeDateTime;
    }

    public Integer getIsBeta() {
        return isBeta;
    }

    public void setIsBeta(Integer isBeta) {
        this.isBeta = isBeta;
    }

    public String getBetaUsers() {
        return betaUsers;
    }

    public void setBetaUsers(String betaUsers) {
        this.betaUsers = betaUsers == null ? null : betaUsers.trim();
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType == null ? null : nodeType.trim();
    }

    public Integer getNotify() {
        return notify;
    }

    public void setNotify(Integer notify) {
        this.notify = notify;
    }

    public String getNotifyContent() {
        return notifyContent;
    }

    public void setNotifyContent(String notifyContent) {
        this.notifyContent = notifyContent == null ? null : notifyContent.trim();
    }

    public String getCallBackStr() {
        return callBackStr;
    }

    public void setCallBackStr(String callBackStr) {
        this.callBackStr = callBackStr == null ? null : callBackStr.trim();
    }

    public Integer getEmergency() {
        return emergency;
    }

    public void setEmergency(Integer emergency) {
        this.emergency = emergency;
    }

    public String getMailCheckCode() {
        return mailCheckCode;
    }

    public void setMailCheckCode(String mailCheckCode) {
        this.mailCheckCode = mailCheckCode == null ? null : mailCheckCode.trim();
    }

    public Integer getMailCheckCodeStatus() {
        return mailCheckCodeStatus;
    }

    public void setMailCheckCodeStatus(Integer mailCheckCodeStatus) {
        this.mailCheckCodeStatus = mailCheckCodeStatus;
    }

    public Date getMailCheckCodeBegin() {
        return mailCheckCodeBegin;
    }

    public void setMailCheckCodeBegin(Date mailCheckCodeBegin) {
        this.mailCheckCodeBegin = mailCheckCodeBegin;
    }

    public Date getMailCheckCodeEnd() {
        return mailCheckCodeEnd;
    }

    public void setMailCheckCodeEnd(Date mailCheckCodeEnd) {
        this.mailCheckCodeEnd = mailCheckCodeEnd;
    }

    public Integer getIsAgent() {
        return isAgent;
    }

    public void setIsAgent(Integer isAgent) {
        this.isAgent = isAgent;
    }

    public String getAgentUser() {
        return agentUser;
    }

    public void setAgentUser(String agentUser) {
        this.agentUser = agentUser == null ? null : agentUser.trim();
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName == null ? null : applyUserName.trim();
    }

    public String getIsApprove() {
        return isApprove;
    }

    public void setIsApprove(String isApprove) {
        this.isApprove = isApprove == null ? null : isApprove.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId == null ? null : processId.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion == null ? null : opinion.trim();
    }
}