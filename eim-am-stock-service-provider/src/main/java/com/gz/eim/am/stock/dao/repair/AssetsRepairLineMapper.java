package com.gz.eim.am.stock.dao.repair;

import com.gz.eim.am.stock.entity.StockAssetsRepairLine;

import java.util.List;

public interface AssetsRepairLineMapper {

    /**
     * @param: stockAssetsRepairLineList
     * @description: 批量插入维修行信息
     * @return: int
     * @author: <EMAIL>
     * @date: 2022/12/16
     */
    int batchInsert(List<StockAssetsRepairLine> stockAssetsRepairLineList);

    /**
     * @param: stockAssetsRepairLineList
     * @description: 批量更新维修行信息
     * @return: int
     * @author: <EMAIL>
     * @date: 2022/12/16
     */
    int batchUpdate(List<StockAssetsRepairLine> stockAssetsRepairLineList);

}