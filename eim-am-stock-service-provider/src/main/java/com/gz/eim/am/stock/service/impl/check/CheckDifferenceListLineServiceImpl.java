package com.gz.eim.am.stock.service.impl.check;

import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.ambase.PsBusLocationTblMapper;
import com.gz.eim.am.stock.dao.base.CheckDifferenceListHeadMapper;
import com.gz.eim.am.stock.dao.base.CheckDifferenceListLineMapper;
import com.gz.eim.am.stock.dao.check.CheckDifferenceLineMapper;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.dto.response.assets.AssetsCostRespDTO;
import com.gz.eim.am.stock.dto.response.check.CheckDifferenceListLineRespDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.*;
import com.gz.eim.am.stock.entity.vo.CheckDifferenceLineInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.check.*;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.common.ListUtil;
import com.gz.eim.am.stock.util.common.SetDefaultValueUtils;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.StockCheckAdjustEnum;
import com.gz.eim.am.stock.util.em.StockCheckMissionEnum;
import com.gz.eim.am.stock.util.em.StockTakingPlanEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: weijunjie
 * @date: 2020/11/22
 * @description
 */
@Slf4j
@Service
public class CheckDifferenceListLineServiceImpl implements CheckDifferenceListLineService {


    @Autowired
    private CheckDifferenceLineMapper checkDifferenceLineMapper;
    @Autowired
    private CheckDifferenceListLineMapper checkDifferenceListLineMapper;
    @Autowired
    CheckDifferenceListHeadMapper checkDifferenceListHeadMapper;
    @Autowired
    private StockWarehouseService stockWarehouseService;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockCheckMissionDetailService stockCheckMissionDetailService;
    @Autowired
    private StockTakingProcessService stockTakingProcessService;
    @Autowired
    private StockCheckMissionService stockCheckMissionService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    StockTakingPlanService stockTakingPlanService;
    @Autowired
    private PsBusLocationTblMapper psBusLocationTblMapper;
    @Autowired
    private StockAssetsCheckTaskDetailService stockAssetsCheckTaskDetailService;


    @Override
    public Integer batchInsertCheckDifference(List<CheckDifferenceListLine> checkDifferenceListLines) {
        if (org.springframework.util.CollectionUtils.isEmpty(checkDifferenceListLines)) {
            return new Integer(0);
        }

        int count = 0;
        List<List<CheckDifferenceListLine>> checkDifferenceListLineList = ListUtil.splitList(checkDifferenceListLines,CommonConstant.MAX_INSERT_COUNT);
        for (List<CheckDifferenceListLine> subCheckDifferenceListLineList : checkDifferenceListLineList){
            int insertCount = checkDifferenceLineMapper.batchInsertCheckDifference(subCheckDifferenceListLineList);
            count = count + insertCount;
        }

        return count;
    }

    @Override
    public List<CheckDifferenceListLineRespDTO> getLineRespDTOs(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        List<CheckDifferenceListLineRespDTO> checkDifferenceListLineRespDTOList = new ArrayList<>();

        CheckDifferenceListLineExample example = new CheckDifferenceListLineExample();
        CheckDifferenceListLineExample.Criteria criteria = example.createCriteria();
        criteria.andHeadIdEqualTo(assetQueryScopeReqDTO.getHeadId());
        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(assetQueryScopeReqDTO.getType())){
            criteria.andAdviseHandleMethodIn(Arrays.asList(StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue(),StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue()));
        }
        if (StockCheckAdjustEnum.CheckAdjustType.ADJUST_DIFF.getValue().equals(assetQueryScopeReqDTO.getType())){
            criteria.andAdviseHandleMethodIn(Arrays.asList(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue(),StockCheckMissionEnum.CheckResult.CHECK_CHANGE.getValue(),
                    StockCheckMissionEnum.CheckResult.CHECK_RECEIVE.getValue(),StockCheckMissionEnum.CheckResult.CHECK_RETURN.getValue(),
                    StockCheckMissionEnum.CheckResult.CHECK_TRANSFER.getValue(),StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue()));
        }
        if (assetQueryScopeReqDTO.getNoPaging() == null){
            example.setLimit(assetQueryScopeReqDTO.getPageSize());
            example.setOffset(assetQueryScopeReqDTO.getStartNum());
        }
        example.setOrderByClause("approve_check_flag,adjust_flag,actual_handle_method,line_id");
        List<CheckDifferenceListLine> checkDifferenceListLineList = checkDifferenceListLineMapper.selectByExample(example);
        Set<String> snapshotAssetsCodeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(checkDifferenceListLineList)){

            Map<Integer, String> statusTypeMap = AssetsEnum.assetsStatusTypeMap;
            Map<Integer, String> conditionsMap = AssetsEnum.conditionsMap;
            Map<Integer, String> adjustCheckMethod = StockCheckMissionEnum.AdjustCheckMethod;

            //用来将仓库编码和人员工号去重
            Set<String> warehouseCodes = new HashSet<>();
            Set<String> empIds = new HashSet<>();
            Set<String> companyCodes = new HashSet<>();
            //查询接口入参数
            List<String> warehouseCodesParam = new ArrayList<>();
            List<String> empIdsParam = new ArrayList<>();
            List<String> companyCodesParam = new ArrayList<>();
            checkDifferenceListLineList.stream().forEach(dto -> {
                empIds.add(dto.getSnapshotAssetsHolder());
                empIds.add(dto.getRealAssetsHolder());
                warehouseCodes.add(dto.getSnapshotWarehouseCode());
                warehouseCodes.add(dto.getRealWarehouseCode());
                companyCodes.add(dto.getCompanyCode());
                snapshotAssetsCodeSet.add(dto.getSnapshotAssetsCode());
            });
            warehouseCodesParam.addAll(warehouseCodes);
            empIdsParam.addAll(empIds);
            companyCodesParam.addAll(companyCodes);

            Map<String, WarehouseRespDTO> warehouseRespDTOMap = stockWarehouseService.selectWarehouseDetailMapByCode(warehouseCodesParam);
            if (warehouseRespDTOMap == null) {
                warehouseRespDTOMap = new HashMap<>(CommonConstant.NUMBER_ONE);
            }
            Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdsParam);
            if (sysUserMap == null){
                sysUserMap = new HashMap<>(CommonConstant.NUMBER_ONE);
            }
            Map<String, SysDept> sysDeptMap = new HashMap<>(empIdsParam.size());
            List<String> locationList = new ArrayList<>();
            List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(empIdsParam);
            if (CollectionUtils.isNotEmpty(sysUserList)) {
                List<String> deptIdList = sysUserList.stream().map(sysUser -> sysUser.getDeptId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deptIdList)) {
                    List<SysDept> sysDeptList = ambaseCommonService.selectDeptByIds(deptIdList);
                    if (CollectionUtils.isNotEmpty(sysDeptList)) {
                        for (SysDept sysDept : sysDeptList) {
                            if (!locationList.contains(sysDept.getLocation())) {
                                locationList.add(sysDept.getLocation());
                            }
                            sysDeptMap.put(sysDept.getDeptId(), sysDept);
                        }
                    }
                }
            }
            Map<String, PsBusLocationTbl> psBusLocationTblMap = new HashMap<>(locationList.size());
            if (CollectionUtils.isNotEmpty(locationList)) {
                List<PsBusLocationTbl> psBusLocationTblList = psBusLocationTblMapper.queryBusLocation(locationList);
                if (CollectionUtils.isNotEmpty(psBusLocationTblList)) {
                    for (PsBusLocationTbl psBusLocationTbl : psBusLocationTblList) {
                        psBusLocationTblMap.put(psBusLocationTbl.getLocation(), psBusLocationTbl);
                    }
                }
            }

            Map<String, SysCompanyInfo> companyInfoMap = ambaseCommonService.getCompanysByCodes(companyCodesParam);
            if (companyInfoMap == null){
                companyInfoMap = new HashMap<>(CommonConstant.NUMBER_ONE);
            }
            String takingPlanNo = assetQueryScopeReqDTO.getTakingPlanNo();
            Map<String, String> checkPeopleMap = new HashMap<>();
            if(StringUtils.isNotBlank(takingPlanNo)){
                StockAssetsCheckTaskDetailReqDO stockAssetsCheckTaskDetailReqDO = new StockAssetsCheckTaskDetailReqDO();
                stockAssetsCheckTaskDetailReqDO.setTakingPlanNo(takingPlanNo);
                stockAssetsCheckTaskDetailReqDO.setSnapshotAssetsCodeList(new ArrayList<>(snapshotAssetsCodeSet));
                List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailService.selectList(stockAssetsCheckTaskDetailReqDO);
                if(CollectionUtils.isNotEmpty(stockAssetsCheckTaskDetailList)){
                    Set<String> checkPeopleSet = new HashSet<>();
                    for (StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail : stockAssetsCheckTaskDetailList) {
                        String checkPeople = stockAssetsCheckTaskDetail.getCheckPeople();
                        checkPeopleMap.put(stockAssetsCheckTaskDetail.getSnapshotAssetsCode(), stockAssetsCheckTaskDetail.getCheckPeople());
                        if(StringUtils.isNotBlank(checkPeople)){
                            checkPeopleSet.addAll(Arrays.asList(checkPeople.split(StringConstant.SEMI_COLON_HALF)));
                        }
                    }
                    Map<String, SysUserBasicInfo> sysUserBasicInfoMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(checkPeopleSet)){
                        sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(checkPeopleSet));
                    }
                    for (Map.Entry<String, String> entry : checkPeopleMap.entrySet()) {
                        String value = entry.getValue();
                        if (StringUtils.isBlank(value)) {
                            continue;
                        }
                        StringBuilder checkPeopleBuilder = new StringBuilder();
                        for (String empId : value.split(StringConstant.SEMI_COLON_HALF)) {
                            SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(empId);
                            if (null == sysUserBasicInfo) {
                                continue;
                            }
                            checkPeopleBuilder.append(sysUserBasicInfo.getName());
                            String email = sysUserBasicInfo.getEmail();
                            if (StringUtils.isNotBlank(email)) {
                                checkPeopleBuilder.append(StringConstant.ENGLISH_LEFT_PARENTHESIS);
                                checkPeopleBuilder.append(email, CommonConstant.NUMBER_ZERO, email.indexOf(StringConstant.AT));
                                checkPeopleBuilder.append(StringConstant.ENGLISH_RIGHT_PARENTHESIS);
                            }
                            checkPeopleBuilder.append(StringConstant.SEMI_COLON_HALF);
                        }
                        if(checkPeopleBuilder.length() > CommonConstant.NUMBER_ZERO){
                            entry.setValue(checkPeopleBuilder.substring(CommonConstant.NUMBER_ZERO, checkPeopleBuilder.length() - CommonConstant.NUMBER_ONE));
                        }
                    }
                }
            }
            // 遍历增加盘点人
            for(CheckDifferenceListLine checkDifferenceListLine : checkDifferenceListLineList){
                CheckDifferenceListLineRespDTO checkDifferenceListLineRespDTO = new CheckDifferenceListLineRespDTO();
                BeanUtils.copyProperties(checkDifferenceListLine,checkDifferenceListLineRespDTO);
                checkDifferenceListLineRespDTO.setCheckPersonListStr(checkPeopleMap.get(checkDifferenceListLine.getSnapshotAssetsCode()));
                //给人员类字段赋值
                checkDifferenceListLineRespDTO.setSnapshotAssetsHolderName(sysUserMap.getOrDefault(checkDifferenceListLineRespDTO.getSnapshotAssetsHolder(),new SysUser()).getName());
                checkDifferenceListLineRespDTO.setRealAssetsHolderName(sysUserMap.getOrDefault(checkDifferenceListLineRespDTO.getRealAssetsHolder(),new SysUser()).getName());
                //账目人员信息
                if(StringUtils.isNotBlank(checkDifferenceListLineRespDTO.getSnapshotAssetsHolder())){
                    SysUser sysUser = sysUserMap.get(checkDifferenceListLineRespDTO.getSnapshotAssetsHolder());
                    if(null != sysUser && StringUtils.isNotBlank(sysUser.getDeptId())) {
                        SysDept sysDept = sysDeptMap.get(sysUser.getDeptId());
                        if (null != sysDept && StringUtils.isNotBlank(sysDept.getLocation())) {
                            checkDifferenceListLineRespDTO.setSnapshotAssetsHolderDept(sysDept.getDeptId());
                            checkDifferenceListLineRespDTO.setSnapshotAssetsHolderDeptName(sysDept.getDeptFullName());
                            PsBusLocationTbl psBusLocationTbl = psBusLocationTblMap.get(sysDept.getLocation());
                            if(null != psBusLocationTbl){
                                checkDifferenceListLineRespDTO.setSnapshotAssetsHolderAddress(psBusLocationTbl.getDescr());
                            }
                        }
                    }
                }
                //实际人员信息
                if(StringUtils.isNotBlank(checkDifferenceListLineRespDTO.getRealAssetsHolder())){
                    SysUser sysUser = sysUserMap.get(checkDifferenceListLineRespDTO.getRealAssetsHolder());
                    if(null != sysUser && StringUtils.isNotBlank(sysUser.getDeptId())) {
                        SysDept sysDept = sysDeptMap.get(sysUser.getDeptId());
                        if (null != sysDept && StringUtils.isNotBlank(sysDept.getLocation())) {
                            checkDifferenceListLineRespDTO.setRealAssetsHolderDept(sysDept.getDeptId());
                            checkDifferenceListLineRespDTO.setRealAssetsHolderDeptName(sysDept.getDeptFullName());
                            PsBusLocationTbl psBusLocationTbl = psBusLocationTblMap.get(sysDept.getLocation());
                            if(null != psBusLocationTbl){
                                checkDifferenceListLineRespDTO.setRealAssetsHolderAddress(psBusLocationTbl.getDescr());
                            }
                        }
                    }
                }
                //给仓库类字段赋值
                checkDifferenceListLineRespDTO.setSnapshotWarehouseName(warehouseRespDTOMap.getOrDefault(checkDifferenceListLineRespDTO.getSnapshotWarehouseCode(),new WarehouseRespDTO()).getName());
                checkDifferenceListLineRespDTO.setRealWarehouseName(warehouseRespDTOMap.getOrDefault(checkDifferenceListLineRespDTO.getRealWarehouseCode(),new WarehouseRespDTO()).getName());
                //给状态类字段赋值
                checkDifferenceListLineRespDTO.setSnapshotAssetsStatusName(statusTypeMap.get(checkDifferenceListLineRespDTO.getSnapshotAssetsStatus()));
                checkDifferenceListLineRespDTO.setRealAssetsStatusName(statusTypeMap.get(checkDifferenceListLineRespDTO.getRealAssetsStatus()));
                checkDifferenceListLineRespDTO.setSnapshotAssetsConditionsName(conditionsMap.get(checkDifferenceListLineRespDTO.getSnapshotAssetsConditions()));
                checkDifferenceListLineRespDTO.setRealAssetsConditionsName(conditionsMap.get(checkDifferenceListLineRespDTO.getRealAssetsConditions()));
                checkDifferenceListLineRespDTO.setDifferenceName(StockCheckMissionEnum.DetailFlag.NO.getValue().equals(checkDifferenceListLineRespDTO.getDifference()) ? "是" : "否");
                checkDifferenceListLineRespDTO.setErrMessage(checkDifferenceListLine.getDiffMessage());
                //给处理方式赋值
                checkDifferenceListLineRespDTO.setActualHandleMethodName(adjustCheckMethod.get(checkDifferenceListLineRespDTO.getActualHandleMethod()));
                checkDifferenceListLineRespDTO.setAdviseHandleMethodName(adjustCheckMethod.get(checkDifferenceListLineRespDTO.getAdviseHandleMethod()));
                checkDifferenceListLineRespDTO.setCompanyName(companyInfoMap.getOrDefault(checkDifferenceListLineRespDTO.getCompanyCode(),new SysCompanyInfo()).getCompanyFullName());
                Date checkDate = checkDifferenceListLine.getCheckDate();
                if(checkDate != null){
                    try {
                        checkDifferenceListLineRespDTO.setCheckDate(DateUtils.dateFormat(checkDate, DateUtils.DATE_PATTERN));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
                checkDifferenceListLineRespDTOList.add(checkDifferenceListLineRespDTO);
            }
        }
        return checkDifferenceListLineRespDTOList;
    }

    @Override
    public Integer batchUpdateSelective(List<CheckDifferenceListLine> checkDifferenceListLines) {
        if (org.springframework.util.CollectionUtils.isEmpty(checkDifferenceListLines)) {
            return new Integer(0);
        }

        List<List<CheckDifferenceListLine>> checkDifferenceListLineList = ListUtil.splitList(checkDifferenceListLines,CommonConstant.MAX_INSERT_COUNT);
        for (List<CheckDifferenceListLine> subCheckDifferenceListLineList : checkDifferenceListLineList){
            checkDifferenceLineMapper.batchUpdateSelective(subCheckDifferenceListLineList);
        }

        return checkDifferenceListLines.size();
    }

    @Override
    public long getLineCount(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        CheckDifferenceListLineExample example = new CheckDifferenceListLineExample();
        CheckDifferenceListLineExample.Criteria criteria = example.createCriteria();
        criteria.andHeadIdEqualTo(assetQueryScopeReqDTO.getHeadId());
        if (assetQueryScopeReqDTO.getRemarkIsNull() != null){
            criteria.andRemarkEqualTo("");
        }
        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(assetQueryScopeReqDTO.getType())){
            criteria.andAdviseHandleMethodIn(Arrays.asList(StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue(),StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue()));
        }
        if (StockCheckAdjustEnum.CheckAdjustType.ADJUST_DIFF.getValue().equals(assetQueryScopeReqDTO.getType())){
            criteria.andAdviseHandleMethodIn(Arrays.asList(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue(),StockCheckMissionEnum.CheckResult.CHECK_CHANGE.getValue(),
                    StockCheckMissionEnum.CheckResult.CHECK_RECEIVE.getValue(),StockCheckMissionEnum.CheckResult.CHECK_RETURN.getValue(),
                    StockCheckMissionEnum.CheckResult.CHECK_TRANSFER.getValue(),StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue()));
        }
        if (assetQueryScopeReqDTO.getNoContainCheckType() != null){
            criteria.andActualHandleMethodNotEqualTo(assetQueryScopeReqDTO.getNoContainCheckType());
        }

        if (assetQueryScopeReqDTO.getOfflineCheckFlag() != null) {
            criteria.andOfflineCheckFlagEqualTo(assetQueryScopeReqDTO.getOfflineCheckFlag());
        }

        if (assetQueryScopeReqDTO.getAdjustFlag() != null) {
            criteria.andAdjustFlagEqualTo(assetQueryScopeReqDTO.getAdjustFlag());
        }
        return checkDifferenceListLineMapper.countByExample(example);
    }

    @Override
    public Integer batchInsertByConditions(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        if (assetQueryScopeReqDTO != null){
            return checkDifferenceLineMapper.batchInsertByConditions(assetQueryScopeReqDTO);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCheckDifferenceList(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO,JwtUser user) throws Exception {
        log.info("createCheckDifferenceList checkDifferenceListHeadReqDTO={}",checkDifferenceListHeadReqDTO.toString());
        StockTakingProcess stockTakingProcess = new StockTakingProcess();
        stockTakingProcess.setGenerateTakingResultDate(new Date());

        //1.保存头表数据
        CheckDifferenceListHead checkDifferenceListHead = new CheckDifferenceListHead();
        BeanUtils.copyProperties(checkDifferenceListHeadReqDTO, checkDifferenceListHead);
        SetDefaultValueUtils.defaultValue(checkDifferenceListHead);
        checkDifferenceListHead.setCreatedBy(user.getEmployeeCode());
        checkDifferenceListHead.setUpdatedBy(user.getEmployeeCode());
        //获取实际盘点数量
        AssetQueryScopeReqDTO assetQueryScopeReqDTO = new AssetQueryScopeReqDTO();
        assetQueryScopeReqDTO.setCheckFlag(StockCheckMissionEnum.CheckFlag.YES.getValue());
        assetQueryScopeReqDTO.setTakingPlanNo(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        long actualCheckCount = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        checkDifferenceListHead.setActualCheckNumber((int) actualCheckCount);
        //获取本次应该盘点数量（快照创建数量）
        assetQueryScopeReqDTO.setCheckFlag(null);
        assetQueryScopeReqDTO.setNoContainCheckFlag(StockCheckMissionEnum.CheckFlag.CANCEL.getValue());
        assetQueryScopeReqDTO.setNewInsertFlag(CommonConstant.NUMBER_ZERO);
        long planCheckCount = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        checkDifferenceListHead.setPlanCheckNumber((int) planCheckCount);

        checkDifferenceListHeadMapper.insertSelective(checkDifferenceListHead);
        //2.生成差异行数据
        //查询异常资产数据
        stockTakingProcess.setGenerateTakingResultStatus(StockTakingPlanEnum.ProcessStatus.FINISH.getStatus());
        stockTakingProcess.setGenerateTakingResultDate(new Date());
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockCheckMissionDetailService.getAllDiffTaskDetail(checkDifferenceListHeadReqDTO.getTakingPlanNo());
        //没有差异数据，更新调整状态=已完成,盘点计划状态=已完成
        if (CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)) {
            stockTakingProcess.setPerformTakingAdjustStatus(StockTakingPlanEnum.ProcessStatus.FINISH.getStatus());
            stockTakingProcess.setPerformTakingAdjustDate(new Date());
            stockTakingProcessService.updateTakingPlanStaus(stockTakingProcess, checkDifferenceListHeadReqDTO.getTakingPlanNo());
            //更新计划单状态=结束
            stockTakingPlanService.updateTakingPlanEnd(Arrays.asList(checkDifferenceListHeadReqDTO.getTakingPlanNo()),user);
            return;
        }
        setAndSaveLine(checkDifferenceListHead, stockAssetsCheckTaskDetailList, user);

        //3.更新盘点流程节点 生成盘点差异清单="已完成" 执行差异调整="进行中"
        stockTakingProcess.setPerformTakingAdjustStatus(StockTakingPlanEnum.ProcessStatus.INPROCESS.getStatus());
        stockTakingProcessService.updateTakingPlanStaus(stockTakingProcess, checkDifferenceListHeadReqDTO.getTakingPlanNo());

    }

    @Override
    public List<CheckDifferenceLineInfo> getLineInfoGroupByHandlerMethod(long headId) {
        return checkDifferenceLineMapper.getLineInfoGroupByHandlerMethod(headId);
    }

    @Override
    public long batchUpdateByConditions(CheckDifferenceListLine checkDifferenceListLine, AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        CheckDifferenceListLineExample example = new CheckDifferenceListLineExample();
        CheckDifferenceListLineExample.Criteria criteria = example.createCriteria();
        criteria.andHeadIdEqualTo(assetQueryScopeReqDTO.getHeadId());
        if (assetQueryScopeReqDTO.getRemarkIsNull() != null) {
            criteria.andRemarkEqualTo("");
        }
        if (StockCheckAdjustEnum.CheckAdjustType.MORE_OR_LESS.getValue().equals(assetQueryScopeReqDTO.getType())) {
            criteria.andAdviseHandleMethodIn(Arrays.asList(StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue(), StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue()));
        }
        if (StockCheckAdjustEnum.CheckAdjustType.ADJUST_DIFF.getValue().equals(assetQueryScopeReqDTO.getType())) {
            criteria.andAdviseHandleMethodIn(Arrays.asList(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue(), StockCheckMissionEnum.CheckResult.CHECK_CHANGE.getValue(),
                    StockCheckMissionEnum.CheckResult.CHECK_RECEIVE.getValue(), StockCheckMissionEnum.CheckResult.CHECK_RETURN.getValue(), StockCheckMissionEnum.CheckResult.CHECK_TRANSFER.getValue()));
        }

        return checkDifferenceListLineMapper.updateByExampleSelective(checkDifferenceListLine, example);
    }


    @Override
    public List<CheckDifferenceListLine> getLineIdsByAdjustFlag(Long headId, List<Long> lineIds, Integer adjustFlag,Integer adjustType) {
        if (headId == null && CollectionUtils.isEmpty(lineIds) && adjustFlag == null && adjustType == null) {
            return new ArrayList<>();
        }
        CheckDifferenceListLineExample example = new CheckDifferenceListLineExample();
        CheckDifferenceListLineExample.Criteria criteria = example.createCriteria();
        if (headId != null) {
            criteria.andHeadIdEqualTo(headId);
        }
        if (CollectionUtils.isNotEmpty(lineIds)) {
            criteria.andLineIdIn(lineIds);
        }
        if (adjustFlag != null) {
            criteria.andAdjustFlagEqualTo(adjustFlag);
        }
        if (adjustType != null) {
            criteria.andActualHandleMethodEqualTo(adjustType);
        }
        return checkDifferenceListLineMapper.selectByExample(example);
    }

    @Override
    public List<Long> getLineIdsByHeadId(Long headId, Integer adjustFlg) {
        return checkDifferenceLineMapper.getLineIdsByHeadId(headId, adjustFlg);
    }

    /**
     * 生成差异清单行数据
     * @param checkDifferenceListHead
     * @param user
     */
    private void setAndSaveLine(CheckDifferenceListHead checkDifferenceListHead, List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList, JwtUser user) {

        List<CheckDifferenceListLine> checkDifferenceListLines = new ArrayList<>(stockAssetsCheckTaskDetailList.size());
        for (StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail : stockAssetsCheckTaskDetailList) {
            CheckDifferenceListLine checkDifferenceListLine = new CheckDifferenceListLine();
            BeanUtils.copyProperties(stockAssetsCheckTaskDetail, checkDifferenceListLine);
            checkDifferenceListLine.setOfflineCheckFlag(CommonConstant.NUMBER_ZERO);
            //系统建议
            getSystemAdvise(checkDifferenceListLine,stockAssetsCheckTaskDetail);
            //取盘点后的序列号和资产名称
            checkDifferenceListLine.setSnapshotSnNo(StringUtils.isNotBlank(stockAssetsCheckTaskDetail.getRealAssetsSnno()) ? stockAssetsCheckTaskDetail.getRealAssetsSnno() : stockAssetsCheckTaskDetail.getSnapshotSnNo());
            checkDifferenceListLine.setSnapshotAssetsName(StringUtils.isNotBlank(stockAssetsCheckTaskDetail.getRealAssetsName()) ? stockAssetsCheckTaskDetail.getRealAssetsName() : stockAssetsCheckTaskDetail.getSnapshotAssetsName());
            checkDifferenceListLine.setRealBrand(stockAssetsCheckTaskDetail.getRealAssetsBrand());
            checkDifferenceListLine.setRealModel(stockAssetsCheckTaskDetail.getRealAssetsModel());
            checkDifferenceListLine.setRealCpu(stockAssetsCheckTaskDetail.getRealAssetsCpu());
            checkDifferenceListLine.setCreatedAt(new Date());
            checkDifferenceListLine.setCreatedBy(user.getEmployeeCode());
            checkDifferenceListLine.setUpdatedAt(new Date());
            checkDifferenceListLine.setUpdatedBy(user.getEmployeeCode());
            //赋值清单头id
            checkDifferenceListLine.setHeadId(checkDifferenceListHead.getHeadId());
            checkDifferenceListLine.setRemark("");
            checkDifferenceListLine.setAdjustFlag(CommonConstant.NUMBER_ZERO);
            if (StringUtils.isBlank(checkDifferenceListLine.getDiffMessage())) {
                checkDifferenceListLine.setDiffMessage("");
            }

            checkDifferenceListLine.setApproveCheckFlag(StockCheckAdjustEnum.ApproveCheckFlag.YES.getValue());
            Date checkTime = stockAssetsCheckTaskDetail.getCheckTime();
            if(checkTime != null){
                try {
                    checkDifferenceListLine.setCheckDate(DateUtils.dateParse(DateUtils.dateFormat(checkTime, DateUtils.DATE_PATTERN), DateUtils.DATE_PATTERN));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            checkDifferenceListLines.add(checkDifferenceListLine);
        }

        //获取盘亏资产的资产净值
        List<String> checkLessAssetsCodes = checkDifferenceListLines.stream().filter(dto->StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue().equals(dto.getAdviseHandleMethod()))
                                                                             .map(CheckDifferenceListLine::getSnapshotAssetsCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(checkLessAssetsCodes)){
            List<AssetsCostRespDTO> assetsCostRespDTOList = ambaseCommonService.selectCostByAssetsCode(checkLessAssetsCodes);
            if (CollectionUtils.isNotEmpty(assetsCostRespDTOList)){
                Map<String, BigDecimal> lastCostMap = assetsCostRespDTOList.stream().collect(Collectors.toMap(AssetsCostRespDTO::getAssetsCode, AssetsCostRespDTO::getLastCost,(a, b)->a));
                checkDifferenceListLines.stream().filter(dto->StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue().equals(dto.getAdviseHandleMethod())).forEach(dto->dto.setNetValue(lastCostMap.get(dto.getSnapshotAssetsCode())));
            }
        }

        //保存行表数据
        batchInsertCheckDifference(checkDifferenceListLines);
    }

    /**
     * 赋值系统处理建议
     * @param checkDifferenceListLine
     * @param stockAssetsCheckTaskDetail
     */
    private void getSystemAdvise(CheckDifferenceListLine checkDifferenceListLine, StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail) {
        if (!StockCheckMissionEnum.CheckFlag.YES.getValue().equals(stockAssetsCheckTaskDetail.getCheckFlag())) {
            checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue());
            checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
            checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.NOT_CHECK.getDesc());
            return;
        }
       if (CommonConstant.NUMBER_ONE.equals(stockAssetsCheckTaskDetail.getSnapshotNumber()) && CommonConstant.NUMBER_ZERO.equals(stockAssetsCheckTaskDetail.getRealNumber())){
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_LESS.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_LESS.getDesc());
           return;
        }
       if (CommonConstant.NUMBER_ZERO.equals(stockAssetsCheckTaskDetail.getSnapshotNumber()) && CommonConstant.NUMBER_ONE.equals(stockAssetsCheckTaskDetail.getRealNumber())){
           //将盘盈资产的编码和序列号进行赋值
           checkDifferenceListLine.setSnapshotAssetsCode(stockAssetsCheckTaskDetail.getRealAssetsCode());
           checkDifferenceListLine.setSnapshotSnNo(stockAssetsCheckTaskDetail.getRealAssetsSnno());
           checkDifferenceListLine.setSnapshotAssetsName(stockAssetsCheckTaskDetail.getRealAssetsName());
           //判断是否为真正的盘盈（系统中不存在）
           StockAssets stockAssets = stockAssetsService.selectAssetsByCode(stockAssetsCheckTaskDetail.getRealAssetsCode());
           if (!Objects.isNull(stockAssets) && stockAssets.getConditions().equals(stockAssetsCheckTaskDetail.getRealAssetsConditions())) {
               checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
               checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
               checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getDesc());
               return;
           }
           //判断是否为线下盘点方式 处理方式=暂不处理
           StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(stockAssetsCheckTaskDetail.getCheckTaskId(),null);
           if (stockAssetsCheckTask != null && StockCheckMissionEnum.TaskCheckMethod.OFFLINE_CHECK.getValue().equals(stockAssetsCheckTask.getCheckTaskMethod())) {
               checkDifferenceListLine.setOfflineCheckFlag(CommonConstant.NUMBER_ONE);
               checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
               checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
               checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getDesc());
               return;
           }
           if (!Objects.isNull(stockAssets) && !stockAssets.getConditions().equals(stockAssetsCheckTaskDetail.getRealAssetsConditions())) {
               checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue());
               checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue());
               checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getDesc());
               return;
           }
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_MORE.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_MORE.getDesc());
           //初始化需要页面调整的盘盈资产编码和序列号字段，以便页面初始化显示
           checkDifferenceListLine.setAdjustAssetsCode(checkDifferenceListLine.getSnapshotAssetsCode());
           checkDifferenceListLine.setAdjustSnCode(checkDifferenceListLine.getSnapshotSnNo());
           return;
       }
       if (AssetsEnum.statusType.IDLE.getValue().equals(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()) && AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getRealAssetsStatus())){
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_RECEIVE.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_RECEIVE.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_RECEIVE.getDesc());
           return;
       }
       if (AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()) && AssetsEnum.statusType.IDLE.getValue().equals(stockAssetsCheckTaskDetail.getRealAssetsStatus())){
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_RETURN.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_RETURN.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_RETURN.getDesc());
           return;
       }
       if (StringUtils.isNotBlank(stockAssetsCheckTaskDetail.getSnapshotWarehouseCode()) && StringUtils.isNotBlank(stockAssetsCheckTaskDetail.getRealWarehouseCode()) &&
               !stockAssetsCheckTaskDetail.getSnapshotWarehouseCode().equals(stockAssetsCheckTaskDetail.getRealWarehouseCode())){
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_TRANSFER.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_TRANSFER.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_TRANSFER.getDesc());
           return;
       }
       if (AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()) && AssetsEnum.statusType.USED.getValue().equals(stockAssetsCheckTaskDetail.getRealAssetsStatus()) &&
               !stockAssetsCheckTaskDetail.getSnapshotAssetsHolder().equals(stockAssetsCheckTaskDetail.getRealAssetsHolder())){
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_CHANGE.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_CHANGE.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_CHANGE.getDesc());
           return;
       }
       if (!stockAssetsCheckTaskDetail.getSnapshotAssetsConditions().equals(stockAssetsCheckTaskDetail.getRealAssetsConditions())){
           //判断是否为线下盘点方式 处理方式=暂不处理
           StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(stockAssetsCheckTaskDetail.getCheckTaskId(),null);
           if (stockAssetsCheckTask != null && StockCheckMissionEnum.TaskCheckMethod.OFFLINE_CHECK.getValue().equals(stockAssetsCheckTask.getCheckTaskMethod())) {
               checkDifferenceListLine.setOfflineCheckFlag(CommonConstant.NUMBER_ONE);
               checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
               checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
               checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getDesc());
               return;
           }
           checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue());
           checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getValue());
           checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_UPDATE.getDesc());
           return;
       }

        checkDifferenceListLine.setAdviseHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
        checkDifferenceListLine.setActualHandleMethod(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getValue());
        checkDifferenceListLine.setCheckResult(StockCheckMissionEnum.CheckResult.CHECK_OTHE.getDesc());
    }
}


