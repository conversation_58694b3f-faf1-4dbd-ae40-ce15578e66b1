package com.gz.eim.am.stock.service.impl.check;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.file.MockMultipartFile;
import com.fuu.eim.support.util.DateUtils;
import com.guazi.gzencrypt.common.constants.CryptType;
import com.gz.eim.am.common.enums.EncryptModuleEnum;
import com.gz.eim.am.common.util.EncryptsPlus;
import com.gz.eim.am.stock.config.CheckRemindPopFrameConfig;
import com.gz.eim.am.stock.constant.*;
import com.gz.eim.am.stock.dto.request.check.StockCheckSendCheckRemindPopFrameReqDTO;
import com.gz.eim.am.stock.dto.response.check.StockCheckQueryRemindLeaderCheckDetailLineRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.vo.StockBeanMap;
import com.gz.eim.am.stock.entity.vo.StockCheckInDoubtDataExportExcel;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.common.ExcelUtils;
import com.gz.eim.am.stock.util.common.FileUtil;
import com.gz.eim.am.stock.util.common.SendGuaGuaMessageUtil;
import com.gz.eim.am.stock.util.em.AssetsEnum;
import com.gz.eim.am.stock.util.em.StockCheckMissionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: StockCheckRemindServiceHelper
 * @description: 资产盘点提醒Shelper
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Slf4j
@Component
public class StockCheckRemindServiceHelper{
    @Autowired
    private FileUtil fileUtil;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Value("${project.check.remindLeaderCheckDetailUrl}")
    private String remindLeaderCheckDetailUrl;
    @Autowired
    private CheckRemindPopFrameConfig checkRemindPopFrameConfig;

    /**
     * @param: checkPersonSetMap,stringSysUserBasicInfoMap
     * @description: 获取盘点存疑数据导出excel集合
     * @return: Map<String, List<StockCheckInDoubtDataExportExcel>>
     * @author: <EMAIL>
     * @date: 2023/11/22
     */
    public Map<String, List<StockCheckInDoubtDataExportExcel>> getStockCheckInDoubtDataExportExcelList(Map<String, List<StockCheckInDoubtDataRespDO>> stockCheckInDoubtDataRespDOListMap, Map<String, SysUserBasicInfo> sysUserBasicInfoMap) throws Exception{
        Map<String, List<StockCheckInDoubtDataExportExcel>> stockCheckInDoubtDataExportExcelListMap = new HashMap<>();
        for (Map.Entry<String, List<StockCheckInDoubtDataRespDO>> entry : stockCheckInDoubtDataRespDOListMap.entrySet()) {
            List<StockCheckInDoubtDataRespDO> stockCheckInDoubtDataRespDOList = entry.getValue();
            List<StockCheckInDoubtDataExportExcel> stockCheckInDoubtDataExportExcelList = new ArrayList<>(stockCheckInDoubtDataRespDOList.size());
            for (StockCheckInDoubtDataRespDO stockCheckInDoubtDataRespDO : stockCheckInDoubtDataRespDOList) {
                StockCheckInDoubtDataExportExcel stockCheckInDoubtDataExportExcel = new StockCheckInDoubtDataExportExcel();
                stockCheckInDoubtDataExportExcelList.add(stockCheckInDoubtDataExportExcel);
                BeanUtils.copyProperties(stockCheckInDoubtDataRespDO, stockCheckInDoubtDataExportExcel);
                stockCheckInDoubtDataExportExcel.setSnCode(stockCheckInDoubtDataRespDO.getSnapshotSnNo());
                stockCheckInDoubtDataExportExcel.setStatusName(AssetsEnum.statusTypeMap.get(stockCheckInDoubtDataRespDO.getSnapshotAssetsStatus()));
                String snapshotAssetsHolderName = stockCheckInDoubtDataRespDO.getSnapshotAssetsHolderName();
                stockCheckInDoubtDataExportExcel.setHolder(snapshotAssetsHolderName);
                String snapshotAssetsHolderEmail = stockCheckInDoubtDataRespDO.getSnapshotAssetsHolderEmail();
                if(StringUtils.isNotBlank(snapshotAssetsHolderEmail)){
                    stockCheckInDoubtDataExportExcel.setHolder(snapshotAssetsHolderName + StringConstant.ENGLISH_LEFT_PARENTHESIS + snapshotAssetsHolderEmail.substring(CommonConstant.NUMBER_ZERO, snapshotAssetsHolderEmail.indexOf(StringConstant.AT)) + StringConstant.ENGLISH_RIGHT_PARENTHESIS);
                }
                Date holderTime = stockCheckInDoubtDataRespDO.getHolderTime();
                if(holderTime != null){
                    stockCheckInDoubtDataExportExcel.setHolderTime(DateUtils.dateFormat(holderTime, DateUtils.MINUTE_PATTERN));
                }
                Date checkTime = stockCheckInDoubtDataRespDO.getCheckTime();
                if(checkTime != null){
                    stockCheckInDoubtDataExportExcel.setCheckTime(DateUtils.dateFormat(checkTime, DateUtils.MINUTE_PATTERN));
                }
                String checkPeople = stockCheckInDoubtDataRespDO.getCheckPeople();
                stockCheckInDoubtDataExportExcel.setCheckPeople(checkPeople);
                SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(checkPeople);
                if(sysUserBasicInfo != null){
                    String email = sysUserBasicInfo.getEmail();
                    if(StringUtils.isNotBlank(email)){
                        stockCheckInDoubtDataExportExcel.setCheckPeople(sysUserBasicInfo.getName() + StringConstant.ENGLISH_LEFT_PARENTHESIS + email.substring(CommonConstant.NUMBER_ZERO, email.indexOf(StringConstant.AT)) + StringConstant.ENGLISH_RIGHT_PARENTHESIS);
                    }
                    stockCheckInDoubtDataExportExcel.setCheckPeoplePhone(sysUserBasicInfo.getPhone());
                    stockCheckInDoubtDataExportExcel.setEntryLocationName(sysUserBasicInfo.getEntryLocationName());
                    String supervisorId = sysUserBasicInfo.getSupervisorId();
                    stockCheckInDoubtDataExportExcel.setSuperUser(supervisorId);
                    sysUserBasicInfo = sysUserBasicInfoMap.get(supervisorId);
                    if(sysUserBasicInfo != null){
                        String superEmail = sysUserBasicInfo.getEmail();
                        if(StringUtils.isNotBlank(superEmail)){
                            stockCheckInDoubtDataExportExcel.setSuperUser(sysUserBasicInfo.getName() + StringConstant.ENGLISH_LEFT_PARENTHESIS + superEmail.substring(CommonConstant.NUMBER_ZERO, superEmail.indexOf(StringConstant.AT)) + StringConstant.ENGLISH_RIGHT_PARENTHESIS);
                        }
                        stockCheckInDoubtDataExportExcel.setSuperUserPhone(sysUserBasicInfo.getPhone());
                    }
                }
            }
            stockCheckInDoubtDataExportExcelListMap.put(entry.getKey(), stockCheckInDoubtDataExportExcelList);
        }
        return stockCheckInDoubtDataExportExcelListMap;
    }
    /**
     * @param: stockCheckInDoubtDataExportExcelListMap
     * @description: 给行政发送盘点存疑数据
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/11/22
     */
    public void sendCheckInDoubtDataExcelToAdministration(Map<String, List<StockCheckInDoubtDataExportExcel>> stockCheckInDoubtDataExportExcelListMap) throws Exception{
        log.info("StockCheckRemindServiceHelper.sendCheckInDoubtDataExcelToAdministration开始准备生成excel文件，盘点计划单号为：{}", JSON.toJSONString(stockCheckInDoubtDataExportExcelListMap.keySet()));
        // 获取昨天的日期
        String yesterdayDate = DateUtils.dateFormat(DateUtils.dateAddDays(new Date(), -CommonConstant.NUMBER_ONE), DateUtils.DATE_PATTERN);
        for (Map.Entry<String, List<StockCheckInDoubtDataExportExcel>> entry : stockCheckInDoubtDataExportExcelListMap.entrySet()) {
            // 生成excel
            // 把excel上传到文件平台，获取下载链接
            String takingPlanNo = entry.getKey();
            List<StockCheckInDoubtDataExportExcel> stockCheckInDoubtDataExportExcelList = entry.getValue();
            MockMultipartFile file = ExcelUtils.getExcelToMockMultipartFile(stockCheckInDoubtDataExportExcelList, "盘点存疑报表" + StringConstant.UNDERLINE + takingPlanNo + StringConstant.UNDERLINE);
            Map<String, String> returnMap = fileUtil.uploadFile(file);
            if(null == returnMap){
                log.error("StockCheckRemindServiceHelper.sendCheckInDoubtDataExcelToAdministration上传文件失败，盘点计划单号为：takingPlanNo:{}", takingPlanNo);
                continue;
            }
            log.info("StockCheckRemindServiceHelper.sendCheckInDoubtDataExcelToAdministration上传文件成功，返回参数为：{}", JSON.toJSONString(returnMap));
            String downloadUrl = returnMap.get(FileUtilConstant.DOWNLOAD_URL);
            if(StringUtils.isBlank(downloadUrl)){
                log.error("StockCheckRemindServiceHelper.sendCheckInDoubtDataExcelToAdministration下载链接不存在，盘点计划单号为：takingPlanNo:{}", takingPlanNo);
                continue;
            }
            StockCheckInDoubtDataExportExcel stockCheckInDoubtDataExportExcel = stockCheckInDoubtDataExportExcelList.get(CommonConstant.NUMBER_ZERO);
            String takingPlanName = stockCheckInDoubtDataExportExcel.getTakingPlanName();
            // 发送链接到邮件
            doSendCheckInDoubtDataExcelToAdministration(yesterdayDate, takingPlanNo, takingPlanName, downloadUrl, PropertyConstants.CHECK_IN_DOUBT_DATA_EMAIL);
            // 发送链接到呱呱
            doSendCheckInDoubtDataExcelToAdministration(yesterdayDate, takingPlanNo, takingPlanName, downloadUrl, PropertyConstants.CHECK_IN_DOUBT_DATA_MESSAGE);
        }
    }
    /**
     * @param: takingPlanNo,takingPlanName
     * @param: downloadUrl,taskCode
     * @description: 给行政发送email
     * @return: 发送物流申请完成提醒
     * @author: <EMAIL>
     * @date: 2023/11/22
     */
    private void doSendCheckInDoubtDataExcelToAdministration(String yesterdayDate, String takingPlanNo, String takingPlanName, String downloadUrl, String taskCode) throws Exception{
        List<Map<String, String>> content = new ArrayList<>();
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.TAKING_PLAN_NO, takingPlanNo));
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.TAKING_PLAN_NAME, takingPlanName));
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.DOWNLOAD_URL, downloadUrl));
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.YESTERDAY_DATE, yesterdayDate));
        SendGuaGuaMessageUtil.sendMessage(taskCode, null, StringConstant.EMPTY, content);
    }

    /**
     * @param: sysUserBasicInfo
     * @description: 设置解密手机号
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    public void setDecryptSysUserBasicInfoPhone(SysUserBasicInfo sysUserBasicInfo) {
        // 手机号解密
        String phone = sysUserBasicInfo.getPhone();
        if (StringUtils.isBlank(phone)) {
            return;
        }
        try {
            phone = EncryptsPlus.decrypt(EncryptModuleEnum.DATA.value, CryptType.PHONE, phone);
            if (StringUtils.isBlank(phone)) {
                return;
            }
            sysUserBasicInfo.setPhone(phone);
        } catch (Exception e) {
            log.error("StockCheckRemindServiceImpl.sendCheckInDoubtDataExcelToAdministration调用解密出现异常，请求参数为：module:{},type:{},crypt:{}", EncryptModuleEnum.PDM.value, CryptType.PHONE.getCode(), phone);
        }
    }

    /**
     * @param: stockCheckNotCheckPeopleRespDOList
     * @description: 开始发送盘点提醒给员工
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    public void doSendCheckRemindMessageToEmployee(List<StockCheckNotCheckPeopleRespDO> stockCheckNotCheckPeopleRespDOList) throws Exception{
        for (StockCheckNotCheckPeopleRespDO stockCheckNotCheckPeopleRespDO : stockCheckNotCheckPeopleRespDOList) {
            doSendCheckRemindMessageToEmployeeOne(PropertyConstants.CHECK_REMIND_TO_EMPLOYEE_GUAGUA_MESSAGE, stockCheckNotCheckPeopleRespDO, stockCheckNotCheckPeopleRespDO.getCheckPeopleEmail());
            doSendCheckRemindMessageToEmployeeOne(PropertyConstants.CHECK_REMIND_TO_EMPLOYEE_TEXT_MESSAGE, stockCheckNotCheckPeopleRespDO, stockCheckNotCheckPeopleRespDO.getCheckPeoplePhone());
        }
    }

    /**
     * @param: taskCode,stockCheckNotCheckPeopleRespDO
     * @param: users
     * @description: 开始发送盘点提醒给员工
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    public void doSendCheckRemindMessageToEmployeeOne(String taskCode, StockCheckNotCheckPeopleRespDO stockCheckNotCheckPeopleRespDO, String users) throws Exception{
        List<Map<String, String>> content = new ArrayList<>(CommonConstant.NUMBER_TWO);
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.TAKING_PLAN_NAME, stockCheckNotCheckPeopleRespDO.getTakingPlanName()));
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.TAKING_PLAN_END_DATE, DateUtils.dateFormat(stockCheckNotCheckPeopleRespDO.getTaskLastTime(), "yyyy年MM月dd日")));
        SendGuaGuaMessageUtil.sendMessage(taskCode, users, null, content);
    }

    /**
     * @param: stockCheckNotCheckPeopleSendToLeaderDataRespDOMap,stockCheckNotCheckPeopleRespDOList
     * @param: sysUserBasicInfoMap,supvLvlId
     * @description: 递归获取上级为L07及以上的人员信息
     * @return: Map<String, StockCheckNotCheckPeopleSendToLeaderDataRespDO>
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    public Map<String, StockCheckNotCheckPeopleSendToLeaderDataRespDO> getStockCheckNotCheckPeopleSendToLeaderDataRespDOList(Map<String, StockCheckNotCheckPeopleSendToLeaderDataRespDO> stockCheckNotCheckPeopleSendToLeaderDataRespDOMap, List<StockCheckNotCheckPeopleRespDO> stockCheckNotCheckPeopleRespDOList,
                                                                                                                     Map<String, SysUserBasicInfo> sysUserBasicInfoMap, String supvLvlId){
        if(stockCheckNotCheckPeopleRespDOList.isEmpty()){
            return stockCheckNotCheckPeopleSendToLeaderDataRespDOMap;
        }
        Set<String> supervisorIdSet = stockCheckNotCheckPeopleRespDOList.stream().map(StockCheckNotCheckPeopleRespDO :: getSupervisorId).filter(StringUtils :: isNotBlank).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(supervisorIdSet)){
            return stockCheckNotCheckPeopleSendToLeaderDataRespDOMap;
        }
        supervisorIdSet.removeAll(sysUserBasicInfoMap.keySet());
        if(!CollectionUtils.isEmpty(supervisorIdSet)){
            Map<String, SysUserBasicInfo> tempSysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(supervisorIdSet));
            sysUserBasicInfoMap.putAll(tempSysUserBasicInfoMap);
        }
        Iterator<StockCheckNotCheckPeopleRespDO> iterator = stockCheckNotCheckPeopleRespDOList.iterator();
        while (iterator.hasNext()){
            StockCheckNotCheckPeopleRespDO stockCheckNotCheckPeopleRespDO = iterator.next();
            String supervisorId = stockCheckNotCheckPeopleRespDO.getSupervisorId();
            // 如果没有上级直接排除
            if(StringUtils.isBlank(supervisorId)){
                iterator.remove();
                continue;
            }
            // 如果上级不存在直接排除
            SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(supervisorId);
            if(null == sysUserBasicInfo){
                iterator.remove();
                continue;
            }
            // 如果找到了对应的标准职级直接排除
            String sysUserSupvLvlId = sysUserBasicInfo.getSupvLvlId();
            if(StringUtils.isNotBlank(sysUserSupvLvlId) && sysUserSupvLvlId.compareTo(supvLvlId) >= CommonConstant.NUMBER_ZERO){
                String key = stockCheckNotCheckPeopleRespDO.getTakingPlanNo() + StringConstant.UNDERLINE + supervisorId;
                StockCheckNotCheckPeopleSendToLeaderDataRespDO stockCheckNotCheckPeopleSendToLeaderDataRespDO = stockCheckNotCheckPeopleSendToLeaderDataRespDOMap.get(key);
                if(null == stockCheckNotCheckPeopleSendToLeaderDataRespDO){
                    stockCheckNotCheckPeopleSendToLeaderDataRespDO = new StockCheckNotCheckPeopleSendToLeaderDataRespDO();
                    stockCheckNotCheckPeopleSendToLeaderDataRespDO.setSupervisorId(supervisorId);
                    stockCheckNotCheckPeopleSendToLeaderDataRespDO.setSupervisorEmail(sysUserBasicInfo.getEmail());
                    stockCheckNotCheckPeopleSendToLeaderDataRespDO.setTakingPlanNo(stockCheckNotCheckPeopleRespDO.getTakingPlanNo());
                    stockCheckNotCheckPeopleSendToLeaderDataRespDO.setTakingPlanName(stockCheckNotCheckPeopleRespDO.getTakingPlanName());
                    stockCheckNotCheckPeopleSendToLeaderDataRespDO.setNotCheckPeopleTotal(CommonConstant.NUMBER_ZERO);
                    stockCheckNotCheckPeopleSendToLeaderDataRespDOMap.put(key, stockCheckNotCheckPeopleSendToLeaderDataRespDO);
                }
                stockCheckNotCheckPeopleSendToLeaderDataRespDO.setNotCheckPeopleTotal(stockCheckNotCheckPeopleSendToLeaderDataRespDO.getNotCheckPeopleTotal() + CommonConstant.NUMBER_ONE);
                iterator.remove();
                continue;
            }
            // 如果自己的id和上级的id相同，直接返回
            if(StringUtils.equals(sysUserBasicInfo.getEmpId(), sysUserBasicInfo.getSupervisorId())){
                iterator.remove();
                continue;
            }
            // 没有找到就更新上级id为上上级的id
            stockCheckNotCheckPeopleRespDO.setSupervisorId(sysUserBasicInfo.getSupervisorId());
        }
        return getStockCheckNotCheckPeopleSendToLeaderDataRespDOList(stockCheckNotCheckPeopleSendToLeaderDataRespDOMap, stockCheckNotCheckPeopleRespDOList,
                sysUserBasicInfoMap, supvLvlId);
    }

    /**
     * @param: stockCheckNotCheckPeopleSendToLeaderDataRespDOSet,supvLvlId
     * @param: notCheckPeopleTotal
     * @description: 开始给领导发送盘点提醒
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/11/24
     */
    public void sendCheckRemindToLeader(Map<String, StockCheckNotCheckPeopleSendToLeaderDataRespDO> stockCheckNotCheckPeopleSendToLeaderDataRespDOMap, String supvLvlId, int notCheckPeopleTotal) {
        for (StockCheckNotCheckPeopleSendToLeaderDataRespDO stockCheckNotCheckPeopleSendToLeaderDataRespDO : stockCheckNotCheckPeopleSendToLeaderDataRespDOMap.values()) {
            // 如果上级id是杨浩涌直接排除
            if(StringUtils.equals(stockCheckNotCheckPeopleSendToLeaderDataRespDO.getSupervisorId(), "10000001")){
                continue;
            }
            doSendCheckRemindToLeader(stockCheckNotCheckPeopleSendToLeaderDataRespDO, supvLvlId, PropertyConstants.CHECK_REMIND_TO_LEADER_GUAGUA_MESSAGE, notCheckPeopleTotal);
            doSendCheckRemindToLeader(stockCheckNotCheckPeopleSendToLeaderDataRespDO, supvLvlId, PropertyConstants.CHECK_REMIND_TO_LEADER_EMAIL, notCheckPeopleTotal);
        }
    }

    /**
     * @param: taskCode,stockCheckNotCheckPeopleRespDO
     * @description: 开始发送盘点提醒给员工
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    public void doSendCheckRemindToLeader(StockCheckNotCheckPeopleSendToLeaderDataRespDO stockCheckNotCheckPeopleSendToLeaderDataRespDO, String supvLvlId, String taskCode, int notCheckPeopleTotal) {
        List<Map<String, String>> content = new ArrayList<>(CommonConstant.NUMBER_TWO);
        String url = remindLeaderCheckDetailUrl + StringConstant.QUESTION + StockAssetsCheckConstant.TAKING_PLAN_NO + StringConstant.EQUAL
                + stockCheckNotCheckPeopleSendToLeaderDataRespDO.getTakingPlanNo() + StringConstant.AMPERSAND + StockAssetsCheckConstant.SUPERVISOR_ID + StringConstant.EQUAL + stockCheckNotCheckPeopleSendToLeaderDataRespDO.getSupervisorId()
                + StringConstant.AMPERSAND + StockAssetsCheckConstant.SUPV_LVL_ID + StringConstant.EQUAL + supvLvlId;
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.REMIND_LEADER_CHECK_DETAIL_URL, url));
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.TAKING_PLAN_NAME, stockCheckNotCheckPeopleSendToLeaderDataRespDO.getTakingPlanName()));
        int superUserNotCheckPeopleTotal = stockCheckNotCheckPeopleSendToLeaderDataRespDO.getNotCheckPeopleTotal();
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.NOT_CHECK_PEOPLE_TOTAL, String.valueOf(superUserNotCheckPeopleTotal)));
        // 展示下单完成数量百分比
        BigDecimal notCheckPeoplePercent = new BigDecimal(superUserNotCheckPeopleTotal).divide(new BigDecimal(notCheckPeopleTotal), 4, RoundingMode.UP).multiply(new BigDecimal(CommonConstant.NUMBER_100)).setScale(CommonConstant.NUMBER_ZERO, RoundingMode.HALF_UP);
        content.add(StockBeanMap.putKeyValueNew(WorkflowConstants.NOT_CHECK_PEOPLE_PERCENT, notCheckPeoplePercent.toString()));
        SendGuaGuaMessageUtil.sendMessage(taskCode, stockCheckNotCheckPeopleSendToLeaderDataRespDO.getSupervisorEmail(), null, content);
    }


    /**
     * @param: checkPeopleList,supervisorIdList
     * @param: supvLvlId
     * @description: 获取盘点人工号集合
     * @return: List<String>
     * @author: <EMAIL>
     * @date: 2023/11/24
     */
    public Set<String> getCheckPeopleList(Set<String> checkPeopleSet, Set<String> supervisorIdSet, String supvLvlId) {
        if(CollectionUtils.isEmpty(supervisorIdSet)){
            return supervisorIdSet;
        }
        // 查询下级员工信息
        List<SysUserBasicInfo> sysUserBasicInfoList = ambaseCommonService.selectUserBasicInfoBySupervisorIdList(new ArrayList<>(supervisorIdSet));
        if(CollectionUtils.isEmpty(sysUserBasicInfoList)){
            return checkPeopleSet;
        }
        supervisorIdSet.clear();
        for (SysUserBasicInfo sysUserBasicInfo : sysUserBasicInfoList) {
            String empId = sysUserBasicInfo.getEmpId();
            checkPeopleSet.add(empId);
            String sysUserSupvLvlId = sysUserBasicInfo.getSupvLvlId();
            if(StringUtils.isBlank(sysUserSupvLvlId) || supvLvlId.compareTo(sysUserSupvLvlId) > CommonConstant.NUMBER_ZERO){
                // 如果下级的id和上级相同，直接返回
                if(StringUtils.equals(sysUserBasicInfo.getEmpId(), sysUserBasicInfo.getSupervisorId())){
                    continue;
                }
                supervisorIdSet.add(empId);
            }
        }
        return getCheckPeopleList(checkPeopleSet, supervisorIdSet, supvLvlId);
    }

    /**
     * @param: stockCheckSelectCheckPeopleDataRespDOList,sysUserBasicInfoMap
     * @param: stockTakingPlan
     * @description: 获取提醒领导盘点详情返回行信息
     * @return: List<StockCheckQueryRemindLeaderCheckDetailLineRespDTO>
     * @author: <EMAIL>
     * @date: 2023/11/24
     */
    public List<StockCheckQueryRemindLeaderCheckDetailLineRespDTO> getStockCheckQueryRemindLeaderCheckDetailHeadRespDTOList(List<StockCheckSelectCheckPeopleDataRespDO> stockCheckSelectCheckPeopleDataRespDOList, Map<String, SysUserBasicInfo> sysUserBasicInfoMap,
                                                                                                                             StockTakingPlan stockTakingPlan) throws Exception{
        String takingDeadlineDate = DateUtils.dateFormat(stockTakingPlan.getTakingDeadlineDate(), DateUtils.DATE_PATTERN);
        Map<String, StockCheckQueryRemindLeaderCheckDetailLineRespDTO> queryRemindLeaderCheckDetailLineRespDTOMap = new HashMap<>();
        for (StockCheckSelectCheckPeopleDataRespDO stockCheckSelectCheckPeopleDataRespDO : stockCheckSelectCheckPeopleDataRespDOList) {
            String checkPeople = stockCheckSelectCheckPeopleDataRespDO.getCheckPeople();
            StockCheckQueryRemindLeaderCheckDetailLineRespDTO stockCheckQueryRemindLeaderCheckDetailLineRespDTO = queryRemindLeaderCheckDetailLineRespDTOMap.get(checkPeople);
            if(null == stockCheckQueryRemindLeaderCheckDetailLineRespDTO){
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO = new StockCheckQueryRemindLeaderCheckDetailLineRespDTO();
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setCheckPerson(checkPeople);
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setHoldAssetsQuantity(CommonConstant.NUMBER_ZERO);
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setCheckedQuantity(CommonConstant.NUMBER_ZERO);
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setUncheckQuantity(CommonConstant.NUMBER_ZERO);
                Date taskLastTime = stockCheckSelectCheckPeopleDataRespDO.getTaskLastTime();
                if(taskLastTime != null){
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setCheckEndDate(DateUtils.dateFormat(taskLastTime, DateUtils.DATE_PATTERN));
                }
                SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(checkPeople);
                if(sysUserBasicInfo != null){
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setCheckPersonName(sysUserBasicInfo.getName());
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setCheckPersonEmail(sysUserBasicInfo.getEmail());
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setDeptFullName(sysUserBasicInfo.getDeptFullName());
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setHpsJobcdDescr(sysUserBasicInfo.getHpsJobcdDescr());
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setEntryLocationName(sysUserBasicInfo.getEntryLocationName());
                    String supervisorId = sysUserBasicInfo.getSupervisorId();
                    stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setSuperUser(supervisorId);
                    sysUserBasicInfo = sysUserBasicInfoMap.get(supervisorId);
                    if(null == sysUserBasicInfo){
                        sysUserBasicInfo  = ambaseCommonService.queryUserBasicInfoByEmpId(supervisorId);
                    }

                    if (null != sysUserBasicInfo){
                        stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setSuperUserName(sysUserBasicInfo.getName());
                        stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setSuperUserEmail(sysUserBasicInfo.getEmail());
                    }

                }
                queryRemindLeaderCheckDetailLineRespDTOMap.put(checkPeople, stockCheckQueryRemindLeaderCheckDetailLineRespDTO);
            }
            stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setHoldAssetsQuantity(stockCheckQueryRemindLeaderCheckDetailLineRespDTO.getHoldAssetsQuantity() + CommonConstant.NUMBER_ONE);
            if(StockCheckMissionEnum.CheckFlag.YES.getValue().equals(stockCheckSelectCheckPeopleDataRespDO.getCheckFlag())){
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setCheckedQuantity(stockCheckQueryRemindLeaderCheckDetailLineRespDTO.getCheckedQuantity() + CommonConstant.NUMBER_ONE);
            }else {
                stockCheckQueryRemindLeaderCheckDetailLineRespDTO.setUncheckQuantity(stockCheckQueryRemindLeaderCheckDetailLineRespDTO.getUncheckQuantity() + CommonConstant.NUMBER_ONE);
            }
        }
        return new ArrayList<>(queryRemindLeaderCheckDetailLineRespDTOMap.values());
    }

    /**
     * @param: stockCheckSendCheckRemindPopFrameReqDTO
     * @description: 处理stockCheckSendCheckRemindPopFrameReqDTO
     * @return: StockCheckSendCheckRemindPopFrameReqDTO
     * @author: <EMAIL>
     * @date: 2023/11/30
     */
    public StockCheckSendCheckRemindPopFrameReqDTO dealSendCheckRemindPopFrameReqDTO(StockCheckSendCheckRemindPopFrameReqDTO stockCheckSendCheckRemindPopFrameReqDTO) throws Exception{
        if(null == stockCheckSendCheckRemindPopFrameReqDTO){
            stockCheckSendCheckRemindPopFrameReqDTO = new StockCheckSendCheckRemindPopFrameReqDTO();
            BeanUtils.copyProperties(checkRemindPopFrameConfig, stockCheckSendCheckRemindPopFrameReqDTO);
        }else {
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getAdNo())){
                stockCheckSendCheckRemindPopFrameReqDTO.setAdNo(checkRemindPopFrameConfig.getAdNo());
            }
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getIconUrl())){
                stockCheckSendCheckRemindPopFrameReqDTO.setIconUrl(checkRemindPopFrameConfig.getIconUrl());
            }
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getJumpUrl())){
                stockCheckSendCheckRemindPopFrameReqDTO.setJumpUrl(checkRemindPopFrameConfig.getJumpUrl());
            }
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getShowPolicy())){
                stockCheckSendCheckRemindPopFrameReqDTO.setShowPolicy(checkRemindPopFrameConfig.getShowPolicy());
            }
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getCanClose())){
                stockCheckSendCheckRemindPopFrameReqDTO.setCanClose(checkRemindPopFrameConfig.getCanClose());
            }
            String currentDate = DateUtils.dateFormat(new Date(), DateUtils.DATE_PATTERN);
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getStartTime())){
                stockCheckSendCheckRemindPopFrameReqDTO.setStartTime(DateTimeUtil.addTimeStartEnd(currentDate));
            }
            if(StringUtils.isBlank(stockCheckSendCheckRemindPopFrameReqDTO.getEndTime())){
                stockCheckSendCheckRemindPopFrameReqDTO.setEndTime(DateTimeUtil.addTimeEndEnd(currentDate));
            }
        }
        return stockCheckSendCheckRemindPopFrameReqDTO;
    }

    /**
     * @param: userIdList,stockCheckSendCheckRemindPopFrameReqDTO
     * @description: 获取发送盘点提醒弹框sql
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/11/30
     */
    public String getSendCheckRemindPopFrameSql(List<String> userIdList, StockCheckSendCheckRemindPopFrameReqDTO stockCheckSendCheckRemindPopFrameReqDTO) {
        // 开始拼接sql
        String adNo = stockCheckSendCheckRemindPopFrameReqDTO.getAdNo();
        String jumpUrl = stockCheckSendCheckRemindPopFrameReqDTO.getJumpUrl();
        String iconUrl = stockCheckSendCheckRemindPopFrameReqDTO.getIconUrl();
        String canClose = stockCheckSendCheckRemindPopFrameReqDTO.getCanClose();
        String showPolicy = stockCheckSendCheckRemindPopFrameReqDTO.getShowPolicy();
        String startTime = stockCheckSendCheckRemindPopFrameReqDTO.getStartTime();
        String endTime = stockCheckSendCheckRemindPopFrameReqDTO.getEndTime();
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("INSERT INTO `workspace_start_popup` (`jump_url`, `icon_url`, `start_time`, `end_time`, `status_code`, `user_id`, `ad_no`, `can_close`, `show_policy`) VALUES");
        for (String userId : userIdList) {
            sqlBuilder.append("('");
            sqlBuilder.append(jumpUrl);
            sqlBuilder.append("','");
            sqlBuilder.append(iconUrl);
            sqlBuilder.append("','");
            sqlBuilder.append(startTime);
            sqlBuilder.append("','");
            sqlBuilder.append(endTime);
            sqlBuilder.append("',");
            sqlBuilder.append("1");
            sqlBuilder.append(",'");
            sqlBuilder.append(userId);
            sqlBuilder.append("','");
            sqlBuilder.append(adNo);
            sqlBuilder.append("','");
            sqlBuilder.append(canClose);
            sqlBuilder.append("','");
            sqlBuilder.append(showPolicy);
            sqlBuilder.append("'),");
        }
        return sqlBuilder.replace(sqlBuilder.length() - CommonConstant.NUMBER_ONE, sqlBuilder.length(), StringConstant.SEMI_COLON_HALF).toString();
    }
}
