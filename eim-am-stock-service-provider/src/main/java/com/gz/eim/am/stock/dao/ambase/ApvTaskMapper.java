package com.gz.eim.am.stock.dao.ambase;

import com.gz.eim.am.stock.entity.ambase.ApvTask;
import com.gz.eim.am.stock.entity.ambase.ApvTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ApvTaskMapper {
    long countByExample(ApvTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ApvTask record);

    int insertSelective(ApvTask record);

    List<ApvTask> selectByExampleWithBLOBs(ApvTaskExample example);

    List<ApvTask> selectByExample(ApvTaskExample example);

    ApvTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ApvTask record, @Param("example") ApvTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") ApvTask record, @Param("example") ApvTaskExample example);

    int updateByExample(@Param("record") ApvTask record, @Param("example") ApvTaskExample example);

    int updateByPrimaryKeySelective(ApvTask record);

    int updateByPrimaryKeyWithBLOBs(ApvTask record);

    int updateByPrimaryKey(ApvTask record);
}