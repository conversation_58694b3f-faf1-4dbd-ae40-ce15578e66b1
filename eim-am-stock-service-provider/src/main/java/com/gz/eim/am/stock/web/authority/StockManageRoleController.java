package com.gz.eim.am.stock.web.authority;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.api.authority.StockManageRoleApi;
import com.gz.eim.am.stock.dto.request.authority.StockManageRoleReqDTO;
import com.gz.eim.am.stock.service.authority.StockManageRoleUpgradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author: weijunjie
 * @date: 2020/5/17
 * @description
 */
@RestController
@Slf4j
@RequestMapping("/api/am/stock/role")
public class StockManageRoleController implements StockManageRoleApi {

    @Autowired
    private StockManageRoleUpgradeService stockManageRoleService;

    @Override
    public ResponseData save(StockManageRoleReqDTO stockManageRoleReqDTO) {
        log.info("/api/am/stock/role/save {}", "新建角色");
        log.info("客户端传入参数 {}", stockManageRoleReqDTO.toString());
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            return stockManageRoleService.save(stockManageRoleReqDTO,user);
        }catch (ServiceUncheckedException e){
            return ResponseData.createFailResult(e.getMessage());
        }catch (Exception e){
            log.error ("新建角色，错误{}", e.getMessage());
            return ResponseData.createFailResult("系统错误");
        }
    }

    @Override
    public ResponseData selectManageRoleOut(StockManageRoleReqDTO stockManageRoleReqDTO) {
        log.info("/api/am/stock/role/search {}", "角色分页查询");
        log.info("客户端传入参数 {}", stockManageRoleReqDTO.toString());
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            return stockManageRoleService.selectManageRoles(stockManageRoleReqDTO);
        }catch (ServiceUncheckedException e){
            return ResponseData.createFailResult(e.getMessage());
        }catch (Exception e){
            log.error ("角色分页查询，错误{}", e.getMessage());
            return ResponseData.createFailResult("系统错误");
        }
    }

}
