package com.gz.eim.am.stock.handler;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.response.repair.StockAssetsRepairLineBaseRespDTO;
import com.gz.eim.am.stock.util.em.StockAssetsRepairHeadEnum;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * @className: StockRepairAddAssetsImportExcelHandler
 * @description: TODO 类描述
 * @author: <EMAIL>
 * @date: 2022/12/14
 **/
public interface StockAssetsRepairAddAssetsImportExcelHandler<T extends StockAssetsRepairLineBaseRespDTO> {

     /**
       * @param:
       * @description: 获取维修类型
       * @return: StockAssetsRepairHeadEnum.RepairType
       * @author: <EMAIL>
       * @date: 2022/12/14
       */
     StockAssetsRepairHeadEnum.RepairType getRepairType();

    /**
     * @param: multipartFile
     * @description: 获取维修类型
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    ResponseData<List<T>> getStockAssetsRepairLineRespList(MultipartFile multipartFile) throws Exception;

}
