package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange;
import com.gz.eim.am.stock.entity.StockAssetsEbsSyncChangeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsEbsSyncChangeMapper {
    long countByExample(StockAssetsEbsSyncChangeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsEbsSyncChange record);

    int insertSelective(StockAssetsEbsSyncChange record);

    List<StockAssetsEbsSyncChange> selectByExample(StockAssetsEbsSyncChangeExample example);

    StockAssetsEbsSyncChange selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsEbsSyncChange record, @Param("example") StockAssetsEbsSyncChangeExample example);

    int updateByExample(@Param("record") StockAssetsEbsSyncChange record, @Param("example") StockAssetsEbsSyncChangeExample example);

    int updateByPrimaryKeySelective(StockAssetsEbsSyncChange record);

    int updateByPrimaryKey(StockAssetsEbsSyncChange record);
}