package com.gz.eim.am.stock.dao.check;

import com.gz.eim.am.stock.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @className: StockCheckRemindMapper
 * @description: 盘点提醒Mapper
 * @author: <EMAIL>
 * @date: 2023/11/22
 */
public interface StockCheckRemindMapper {
    /**
     * @param:
     * @description: 查询盘点异常数据列表
     * @return: List<StockCheckInDoubtDataRespDO>
     * @author: <EMAIL>
     * @date: 2023/11/22
     */
    List<StockCheckInDoubtDataRespDO> selectCheckInDoubtDataList(@Param("checkStartDate") Date checkStartDate, @Param("checkEndDate")Date checkEndDate);
    /**
     * @param: stockCheckNotCheckPeopleReqDO
     * @description: 查询未盘点人列表
     * @return: List<StockCheckNotCheckPeopleRespDO>
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    List<StockCheckNotCheckPeopleRespDO> selectNotCheckPeopleList(StockCheckNotCheckPeopleReqDO stockCheckNotCheckPeopleReqDO);
    /**
     * @param: stockCheckSelectCheckPeopleDataReqDO
     * @description: 查询盘点人的数据列表
     * @return: List<StockCheckSelectCheckPeopleDataRespDO>
     * @author: <EMAIL>
     * @date: 2023/11/24
     */
    List<StockCheckSelectCheckPeopleDataRespDO> selectCheckPeopleDataList(StockCheckSelectCheckPeopleDataReqDO stockCheckSelectCheckPeopleDataReqDO);

    /**
     * @param:
     * @description: 查询未盘点人userId集合
     * @return: List<String>
     * @author: <EMAIL>
     * @date: 2023/11/30
     */
    List<String> selectNotCheckPeopleUserIdList();
}
