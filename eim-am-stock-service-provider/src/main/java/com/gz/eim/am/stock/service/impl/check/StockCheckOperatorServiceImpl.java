package com.gz.eim.am.stock.service.impl.check;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.google.zxing.common.detector.MathUtils;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.response.check.StockAssetsCheckImportRespDTO;
import com.gz.eim.am.stock.dto.response.check.StockAssetsCheckTaskDetailRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.vo.StockCheckDataImportExcel;
import com.gz.eim.am.stock.service.check.*;
import com.gz.eim.am.stock.util.common.SetDefaultValueUtils;
import com.gz.eim.am.stock.util.em.CommonEnum;
import com.gz.eim.am.stock.util.em.StockCheckMissionEnum;
import com.gz.eim.am.stock.util.em.StockTakingPlanEnum;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: weijunjie
 * @date: 2020/11/18
 * @description
 */
@Slf4j
@Service
public class StockCheckOperatorServiceImpl implements StockCheckOperatorService {

    @Autowired
    private StockCheckMissionService stockCheckMissionService;
    @Autowired
    private StockAssetsCheckImportService stockAssetsCheckImportService;
    @Autowired
    private StockCheckMissionDetailService stockCheckMissionDetailService;
    @Autowired
    private StockTakingProcessService stockTakingProcessService;


    @Override
    public ResponseData checkDataImport(List<StockCheckDataImportExcel> checkExcelList,Long checkTaskId) {
        String loginUser = SecurityUtil.getJwtUser().getEmployeeCode();
        log.info("盘点任务清单导入开始》》");
        //文件数据不能为空
        if (org.apache.commons.collections.CollectionUtils.isEmpty(checkExcelList)) {
            return ResponseData.createFailResult("文件内数据为空");
        }
        Set<String> assetsCodeSet = new HashSet<>();
        //将解析出来的字段转换类型
        for (StockCheckDataImportExcel dto : checkExcelList){
            if (StringUtils.isNotBlank(dto.getAssetsCode())){
                dto.setAssetsCode(StringUtils.strip(dto.getAssetsCode()));
            }else{
                return ResponseData.createFailResult("文件内资产编码存在为空的列");
            }

            if (StringUtils.isNotBlank(dto.getDifferenceImport())){
                dto.setDifferenceImport(StringUtils.strip(dto.getDifferenceImport()));
            }else{
                return ResponseData.createFailResult("文件内'是否异常'存在为空的列");
            }
            if(!assetsCodeSet.add(dto.getAssetsCode())){
                return ResponseData.createFailResult("导入资产编码重复，重复的资产编码为：{}", dto.getAssetsCode());
            }
            dto.copyFieldValue();
            //校验资产编码只能为字母和数字
            if (!isNumOrABC(dto.getAssetsCode())){
                return ResponseData.createFailResult("资产编码："+dto.getAssetsCode()+"不符合规范，只能包含数字或字母");
            }
            //校验是否差异列只能是0或1
            if (!CommonConstant.NUMBER_ZERO.equals(dto.getDifference()) && !CommonConstant.NUMBER_ONE.equals(dto.getDifference())){
                return ResponseData.createFailResult("是否异常编码："+dto.getDifference()+"不符合规范,只能为0或1");
            }
        }

        StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(checkTaskId,null);
        if (stockAssetsCheckTask == null){
            return ResponseData.createFailResult("盘点任务id"+checkTaskId+"未查到对应任务单据");
        }

        //查询当前任务下已上传的数据，存在的更新，不存在的新增
        StockAssetsCheckImportReqDO stockAssetsCheckImportReqDO = new StockAssetsCheckImportReqDO();
        stockAssetsCheckImportReqDO.setCheckTaskNo(stockAssetsCheckTask.getCheckTaskNo());
        Map<String, StockAssetsCheckImport> stockAssetsCheckImportMap = stockAssetsCheckImportService.selectMapByAssetsCode(stockAssetsCheckImportReqDO);
        List<StockAssetsCheckImport> updateStockAssetsCheckImportList = new ArrayList<>();
        List<StockAssetsCheckImport> insertStockAssetsCheckImportList = new ArrayList<>();
        for (StockCheckDataImportExcel stockCheckDataImportExcel : checkExcelList) {
            StockAssetsCheckImport stockAssetsCheckImport = stockAssetsCheckImportMap.get(stockCheckDataImportExcel.getAssetsCode());
            if(stockAssetsCheckImport != null){
                stockAssetsCheckImport.setDifference(stockCheckDataImportExcel.getDifference());
                stockAssetsCheckImport.setUpdatedAt(new Date());
                updateStockAssetsCheckImportList.add(stockAssetsCheckImport);
            }else {
                StockAssetsCheckImport insertStockAssetsCheckImport = getStockAssetsCheckImport(stockCheckDataImportExcel, stockAssetsCheckTask, loginUser);
                insertStockAssetsCheckImportList.add(insertStockAssetsCheckImport);
            }
        }
//        //对数据进行去重，相同的资产不能重复上传
//        HashSet hs1 = new HashSet(checkExcelList);
//        HashSet hs2 = new HashSet(importExcelsAlreadySave);
//        hs1.removeAll(hs2);
//        List<StockCheckDataImportExcel> saveImportExcels = new ArrayList<>();
//        saveImportExcels.addAll(hs1);
//        if (CollectionUtils.isEmpty(saveImportExcels)){
//            return ResponseData.createFailResult("本次上传的数据与当前任务下已上传过的数据重复");
//        }
        if(CollectionUtils.isNotEmpty(updateStockAssetsCheckImportList)){
            stockAssetsCheckImportService.batchUpdateCheckImportData(updateStockAssetsCheckImportList);
        }
        if(CollectionUtils.isNotEmpty(insertStockAssetsCheckImportList)){
            stockAssetsCheckImportService.batchInsertCheckImportData(insertStockAssetsCheckImportList);
        }
        //保存上传数据
//        int count = stockAssetsCheckImportService.batchInsertCheckImportData(stockAssetsCheckImportList);
//        if (count<1){
//            throw new ServiceUncheckedException("盘点上传数据批量插入失败，盘点任务单："+stockAssetsCheckTask.getCheckTaskNo());
//        }
        log.info("盘点任务清单导入完成》》");
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData uploadDataShow(AssetQueryScopeReqDTO assetQueryScopeReqDTO) {
        return stockAssetsCheckImportService.uploadDataShow(assetQueryScopeReqDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData uploadDataConfirm(Long checkTaskId) throws Exception {
        //1.参数校验
        if (checkTaskId == null){
            throw new ServiceUncheckedException("参数不能为空");
        }
        StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(checkTaskId,null);
        if (stockAssetsCheckTask == null){
            throw new ServiceUncheckedException("盘点任务id："+checkTaskId+"没有查到对应的任务单据");
        }
        long count = stockAssetsCheckImportService.queryCountByTaskId(checkTaskId);
        if (count<=0){
            throw new ServiceUncheckedException("该任务没有上传数据，不能确认");
        }
        //2.确认盘点结果，明细表中存在的进行更新，不存在的进行插入
        JwtUser user = SecurityUtil.getJwtUser();
        StockAssetsCheckImport stockAssetsCheckImport = new StockAssetsCheckImport();
        stockAssetsCheckImport.setCheckTaskId(checkTaskId);
        stockAssetsCheckImport.setCheckTaskNo(stockAssetsCheckTask.getCheckTaskNo());
        stockAssetsCheckImport.setUpdatedBy(user.getEmployeeCode());
        stockAssetsCheckImport.setUpdatedAt(new Date());
        stockAssetsCheckImportService.updateImportData(stockAssetsCheckImport);
        //查询插入盘点明细中不存在的资产
        saveNewUploadAssets(stockAssetsCheckTask,user);

        //3.删除临时表记录
        stockAssetsCheckImportService.deleteByCheckTaskNo(stockAssetsCheckTask.getCheckTaskNo());

        //4.更新盘点任务状态="已完成"，更新盘点完成比例
        AssetQueryScopeReqDTO assetQueryScopeReqDTO = new AssetQueryScopeReqDTO();
        assetQueryScopeReqDTO.setCheckTaskId(checkTaskId);
        assetQueryScopeReqDTO.setNewInsertFlag(StockCheckMissionEnum.DetailFlag.NO.getValue());
        //任务下总的盘点资产数量
        long allCheckDetailCount = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        if (allCheckDetailCount<=0){
            log.error("盘点任务单："+checkTaskId+"没有查到对应盘点明细数据");
            throw new ServiceUncheckedException("盘点任务单："+checkTaskId+"没有查到对应盘点明细数据");
        }
        //任务下已经盘点过的资产数量
        assetQueryScopeReqDTO.setCheckFlag(StockCheckMissionEnum.DetailFlag.YES.getValue());
        long alreadyCheckDetailCount = stockCheckMissionDetailService.getTaskDetailCountByConditions(assetQueryScopeReqDTO);
        double rate = BigDecimal.valueOf((float)alreadyCheckDetailCount/allCheckDetailCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        StockAssetsCheckTask stockAssetsCheckTaskUpdate = new StockAssetsCheckTask();
        stockAssetsCheckTaskUpdate.setCheckTaskId(checkTaskId);
        stockAssetsCheckTaskUpdate.setCheckTaskStatus(StockCheckMissionEnum.TaskStatus.END.getValue());
        stockAssetsCheckTaskUpdate.setTaskProgressRate(rate);
        stockCheckMissionService.updateCheckMissionById(stockAssetsCheckTaskUpdate);

        //5.如果"创建盘点任务"="已完成"&&所有任务都已经结束，那么更新"录入盘点数据"="已完成"&&"生成盘点差异清单"="进行中"
        StockTakingProcess stockTakingProcess = stockTakingProcessService.queryByTakingPlanNo(stockAssetsCheckTask.getTakingPlanNo());
        if (StockTakingPlanEnum.ProcessStatus.FINISH.getStatus().equals(stockTakingProcess.getCreateTakingTaskStatus())){
            long missionCount = stockCheckMissionService.selectMisssionCountByNoContainStatus(stockAssetsCheckTask.getTakingPlanNo(),StockCheckMissionEnum.TaskStatus.END.getValue());
            if (missionCount<=0){
                StockTakingProcess stockTakingProcessUpdate = new StockTakingProcess();
                stockTakingProcessUpdate.setInputTakingDataDate(new Date());
                stockTakingProcessUpdate.setInputTakingDataStatus(StockTakingPlanEnum.ProcessStatus.FINISH.getStatus());
                stockTakingProcessUpdate.setGenerateTakingResult(StockTakingPlanEnum.TakingPlanProcessEnum.GENERATE_TAKING_RESULT.getCode());
                stockTakingProcessUpdate.setGenerateTakingResultStatus(StockTakingPlanEnum.ProcessStatus.INPROCESS.getStatus());
                stockTakingProcessService.updateTakingPlanStaus(stockTakingProcessUpdate, stockAssetsCheckTask.getTakingPlanNo());
            }
        }
        return ResponseData.createSuccessResult();
    }

    /**
     * 插入盘点任务明细表中不存在的资产
     * @param stockAssetsCheckTask
     * @param user
     */
    private void saveNewUploadAssets(StockAssetsCheckTask stockAssetsCheckTask, JwtUser user) throws Exception {
        List<StockAssetsCheckImport> stockAssetsCheckImportList = stockAssetsCheckImportService.selectNoContainAssets(stockAssetsCheckTask.getCheckTaskId(),stockAssetsCheckTask.getTakingPlanNo());
        if (CollectionUtils.isNotEmpty(stockAssetsCheckImportList)){
            List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = new ArrayList<>(stockAssetsCheckImportList.size());
            for(StockAssetsCheckImport dto : stockAssetsCheckImportList){
                StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = new StockAssetsCheckTaskDetail();
                stockAssetsCheckTaskDetail.setCheckTaskId(stockAssetsCheckTask.getCheckTaskId());
                stockAssetsCheckTaskDetail.setCheckPeople(user.getEmployeeCode());
                stockAssetsCheckTaskDetail.setTakingPlanNo(stockAssetsCheckTask.getTakingPlanNo());
                stockAssetsCheckTaskDetail.setRealNumber(1);
//                stockAssetsCheckTaskDetail.setDifference(dto.getDifference());
                stockAssetsCheckTaskDetail.setDifference(StockCheckMissionEnum.CheckDiff.YES.getValue());
                stockAssetsCheckTaskDetail.setSnapshotAssetsCode(dto.getRealAssetsCode());
                stockAssetsCheckTaskDetail.setRealAssetsCode(dto.getRealAssetsCode());
                stockAssetsCheckTaskDetail.setCheckFlag(StockCheckMissionEnum.DetailFlag.YES.getValue());
                stockAssetsCheckTaskDetail.setNewInsertFlag(StockCheckMissionEnum.DetailFlag.YES.getValue());
                stockAssetsCheckTaskDetail.setCreatedBy(user.getEmployeeCode());
                stockAssetsCheckTaskDetail.setCreatedAt(new Date());
                stockAssetsCheckTaskDetail.setUpdatedBy(user.getEmployeeCode());
                stockAssetsCheckTaskDetail.setUpdatedAt(new Date());
                //对象中的空字段赋默认值
                SetDefaultValueUtils.defaultValue(stockAssetsCheckTaskDetail);
                //将实际资产状态置为空值
                stockAssetsCheckTaskDetail.setSnapshotAssetsStatus(null);
                stockAssetsCheckTaskDetail.setSnapshotAssetsConditions(null);
                stockAssetsCheckTaskDetail.setRealAssetsStatus(null);
                stockAssetsCheckTaskDetail.setRealAssetsConditions(null);
                stockAssetsCheckTaskDetail.setCheckTime(new Date());
                stockAssetsCheckTaskDetailList.add(stockAssetsCheckTaskDetail);
            }
            //保存数据
            stockCheckMissionDetailService.batchInsertCheckMissionDetail(stockAssetsCheckTaskDetailList);
        }
    }

    /**
     * @param: stockAssetsCheckImport,stockAssetsCheckTask
     * @param: loginUser
     * @description: 设置资产盘点导入数据
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/12/8
     */
    private StockAssetsCheckImport getStockAssetsCheckImport(StockCheckDataImportExcel stockCheckDataImportExcel, StockAssetsCheckTask stockAssetsCheckTask, String loginUser){
        StockAssetsCheckImport stockAssetsCheckImport = new StockAssetsCheckImport();
        stockAssetsCheckImport.setCheckTaskId(stockAssetsCheckTask.getCheckTaskId());
        stockAssetsCheckImport.setCheckTaskNo(stockAssetsCheckTask.getCheckTaskNo());
        stockAssetsCheckImport.setDifference(stockCheckDataImportExcel.getDifference());
        stockAssetsCheckImport.setDiffMessage("");
        stockAssetsCheckImport.setRealAssetsCode(stockCheckDataImportExcel.getAssetsCode());
        stockAssetsCheckImport.setRealAssetsHolder("");
        stockAssetsCheckImport.setRealAssetsName("");
        stockAssetsCheckImport.setRealAssetsStatus(0);
        stockAssetsCheckImport.setRealHolderAddress("");
        stockAssetsCheckImport.setRealWarehouseCode("");
        stockAssetsCheckImport.setCreatedAt(new Date());
        stockAssetsCheckImport.setCreatedBy(loginUser);
        stockAssetsCheckImport.setUpdatedAt(new Date());
        stockAssetsCheckImport.setUpdatedBy(loginUser);
        return stockAssetsCheckImport;
    }

    /**
     * 将上传数据对象转换为保存数据对象
     * @param StockCheckDataImportExcels
     * @return
     */
    private List<StockAssetsCheckImport> changeDto(List<StockCheckDataImportExcel> StockCheckDataImportExcels,StockAssetsCheckTask stockAssetsCheckTask){
        JwtUser user = SecurityUtil.getJwtUser();
        List<StockAssetsCheckImport> stockAssetsCheckImportList = new ArrayList<>();
        StockCheckDataImportExcels.stream().forEach(dto->{
            StockAssetsCheckImport stockAssetsCheckImport = new StockAssetsCheckImport();
            stockAssetsCheckImport.setCheckTaskId(stockAssetsCheckTask.getCheckTaskId());
            stockAssetsCheckImport.setCheckTaskNo(stockAssetsCheckTask.getCheckTaskNo());
            stockAssetsCheckImport.setDifference(dto.getDifference());
            stockAssetsCheckImport.setDiffMessage("");
            stockAssetsCheckImport.setRealAssetsCode(dto.getAssetsCode());
            stockAssetsCheckImport.setRealAssetsHolder("");
            stockAssetsCheckImport.setRealAssetsName("");
            stockAssetsCheckImport.setRealAssetsStatus(0);
            stockAssetsCheckImport.setRealHolderAddress("");
            stockAssetsCheckImport.setRealWarehouseCode("");
            stockAssetsCheckImport.setCreatedAt(new Date());
            stockAssetsCheckImport.setCreatedBy(user.getEmployeeCode());
            stockAssetsCheckImport.setUpdatedAt(new Date());
            stockAssetsCheckImport.setUpdatedBy(user.getEmployeeCode());
            stockAssetsCheckImportList.add(stockAssetsCheckImport);
        });
        return stockAssetsCheckImportList;
    }

    /**
     * 校验字符串是否为字母和数字
     * @param str
     * @return true：字符串中只包含字母和数字
     */
    private static boolean isNumOrABC(String str){
        String regEx="[A-Z,a-z,0-9]*";
        Pattern pattern = Pattern.compile(regEx);
        return pattern.matcher(str).matches();
    }
}
