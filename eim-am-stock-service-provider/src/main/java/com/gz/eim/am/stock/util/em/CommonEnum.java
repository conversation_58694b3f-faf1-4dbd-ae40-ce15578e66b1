package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/3/13
 * @description 公用枚举
 */
public class CommonEnum {
    /**
     * 是否逻辑删除
     */
    public enum delFlag {

        /**
         * 否
         */
        NO (0, "否"),
        /**
         * 使用中
         */
        YES (1, "是"),
        ;

        delFlag(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    /**
     * 是否有效
     */
    public enum status {

        /**
         * 否
         */
        NO (0, "否"),
        /**
         * 是
         */
        YES (1, "是"),
        ;

        status(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    /**
     * 是否逻辑删除
     */
    public enum approveStatus {

        /**
         * 驳回
         */
        REJECT(0, "驳回"),
        /**
         * 通过
         */
        PASS(1, "通过")
        ;

        approveStatus(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<Integer, String> statusMap =
            Arrays.stream (CommonEnum.status.values ()).collect (
                    Collectors.toMap (CommonEnum.status::getValue, CommonEnum.status::getDesc));

    public static Map<Integer, String> delFlagMap =
            Arrays.stream (CommonEnum.delFlag.values ()).collect (
                    Collectors.toMap (CommonEnum.delFlag::getValue, CommonEnum.delFlag::getDesc));

    public static Map<Integer, String> approveStatusMap =
            Arrays.stream (CommonEnum.approveStatus.values ()).collect (
                    Collectors.toMap (CommonEnum.approveStatus::getValue, CommonEnum.approveStatus::getDesc));
}





