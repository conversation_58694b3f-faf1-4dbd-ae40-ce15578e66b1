package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecord;
import java.util.List;

/**
 * @className: AssetsCompensationRecordMapper
 * @description: 资产赔偿记录操作Mapper
 * @author: <EMAIL>
 * @date: 2022/2/15
 **/
public interface AssetsCompensationRecordMapper {


     Long queryAssetsCompensationRecordCount(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO);

     List<StockAssetsCompensationRecord> queryAssetsCompensationRecordPage(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO);

     /**
       * @param:
       * @description: 批量插入
       * @return:
       * @author: <EMAIL>
       * @date: 2022/2/15
       */
     int batchInsert(List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList);


     /**
      * @param:
      * @description: 批量更新
      * @return:
      * @author: <EMAIL>
      * @date: 2022/2/15
      */
     int batchUpdate(List<StockAssetsCompensationRecord> stockAssetsCompensationRecordList);
}
