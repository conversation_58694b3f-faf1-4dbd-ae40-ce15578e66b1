package com.gz.eim.am.stock.service.impl.inventory;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockInventoryInSuppliesSnMapper;
import com.gz.eim.am.stock.dao.inventory.InventoryInSuppliesSnMapper;
import com.gz.eim.am.stock.entity.StockInventoryInSuppliesSn;
import com.gz.eim.am.stock.service.inventory.StockInventoryInSuppliesSnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: lishuy<PERSON>
 * @date: 2019/12/13
 * @description: 入单行sn关联service实现
 */
@Service
public class StockInventoryInSuppliesSnServiceImpl implements StockInventoryInSuppliesSnService {
    @Autowired
    private InventoryInSuppliesSnMapper inventoryInSuppliesSnMapper;

    @Autowired
    private StockInventoryInSuppliesSnMapper stockInventoryInSuppliesSnMapper;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsert(List<StockInventoryInSuppliesSn> stockInventoryInSuppliesSnList) {
        if(CollectionUtils.isEmpty (stockInventoryInSuppliesSnList)){
            return false;
        }

        if(stockInventoryInSuppliesSnList.size() > 0){
            int toIndex= CommonConstant.MAX_INSERT_COUNT;
            for(int i = 0;i<stockInventoryInSuppliesSnList.size();i+=CommonConstant.MAX_INSERT_COUNT){
                //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                if(i+CommonConstant.MAX_INSERT_COUNT>stockInventoryInSuppliesSnList.size()){
                    toIndex=stockInventoryInSuppliesSnList.size()-i;
                }
                List<StockInventoryInSuppliesSn> newSubDetailList = stockInventoryInSuppliesSnList.subList(i,i+toIndex);
                inventoryInSuppliesSnMapper.insertMultiple (newSubDetailList);
            }
        }

        return false;
    }

    @Override
    public void insert(StockInventoryInSuppliesSn stockInventoryInSuppliesSn) {
        if(null == stockInventoryInSuppliesSn){
            return;
        }
        stockInventoryInSuppliesSnMapper.insert (stockInventoryInSuppliesSn);
    }
}