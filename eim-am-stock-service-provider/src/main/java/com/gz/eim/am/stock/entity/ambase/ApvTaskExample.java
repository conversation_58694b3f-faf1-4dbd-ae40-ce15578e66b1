package com.gz.eim.am.stock.entity.ambase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApvTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ApvTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("flow_id like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("flow_id not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(String value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(String value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(String value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(String value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(String value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(String value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLike(String value) {
            addCriterion("form_id like", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotLike(String value) {
            addCriterion("form_id not like", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<String> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<String> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(String value1, String value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(String value1, String value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNull() {
            addCriterion("biz_id is null");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNotNull() {
            addCriterion("biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizIdEqualTo(String value) {
            addCriterion("biz_id =", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotEqualTo(String value) {
            addCriterion("biz_id <>", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThan(String value) {
            addCriterion("biz_id >", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("biz_id >=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThan(String value) {
            addCriterion("biz_id <", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanOrEqualTo(String value) {
            addCriterion("biz_id <=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLike(String value) {
            addCriterion("biz_id like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotLike(String value) {
            addCriterion("biz_id not like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdIn(List<String> values) {
            addCriterion("biz_id in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotIn(List<String> values) {
            addCriterion("biz_id not in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdBetween(String value1, String value2) {
            addCriterion("biz_id between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotBetween(String value1, String value2) {
            addCriterion("biz_id not between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdIsNull() {
            addCriterion("flow_category_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdIsNotNull() {
            addCriterion("flow_category_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdEqualTo(Integer value) {
            addCriterion("flow_category_id =", value, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdNotEqualTo(Integer value) {
            addCriterion("flow_category_id <>", value, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdGreaterThan(Integer value) {
            addCriterion("flow_category_id >", value, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("flow_category_id >=", value, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdLessThan(Integer value) {
            addCriterion("flow_category_id <", value, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("flow_category_id <=", value, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdIn(List<Integer> values) {
            addCriterion("flow_category_id in", values, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdNotIn(List<Integer> values) {
            addCriterion("flow_category_id not in", values, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("flow_category_id between", value1, value2, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andFlowCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("flow_category_id not between", value1, value2, "flowCategoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNull() {
            addCriterion("category_code is null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNotNull() {
            addCriterion("category_code is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeEqualTo(String value) {
            addCriterion("category_code =", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotEqualTo(String value) {
            addCriterion("category_code <>", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThan(String value) {
            addCriterion("category_code >", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("category_code >=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThan(String value) {
            addCriterion("category_code <", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThanOrEqualTo(String value) {
            addCriterion("category_code <=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLike(String value) {
            addCriterion("category_code like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotLike(String value) {
            addCriterion("category_code not like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIn(List<String> values) {
            addCriterion("category_code in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotIn(List<String> values) {
            addCriterion("category_code not in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeBetween(String value1, String value2) {
            addCriterion("category_code between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotBetween(String value1, String value2) {
            addCriterion("category_code not between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeIsNull() {
            addCriterion("node_code is null");
            return (Criteria) this;
        }

        public Criteria andNodeCodeIsNotNull() {
            addCriterion("node_code is not null");
            return (Criteria) this;
        }

        public Criteria andNodeCodeEqualTo(String value) {
            addCriterion("node_code =", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeNotEqualTo(String value) {
            addCriterion("node_code <>", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeGreaterThan(String value) {
            addCriterion("node_code >", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("node_code >=", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeLessThan(String value) {
            addCriterion("node_code <", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeLessThanOrEqualTo(String value) {
            addCriterion("node_code <=", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeLike(String value) {
            addCriterion("node_code like", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeNotLike(String value) {
            addCriterion("node_code not like", value, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeIn(List<String> values) {
            addCriterion("node_code in", values, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeNotIn(List<String> values) {
            addCriterion("node_code not in", values, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeBetween(String value1, String value2) {
            addCriterion("node_code between", value1, value2, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeCodeNotBetween(String value1, String value2) {
            addCriterion("node_code not between", value1, value2, "nodeCode");
            return (Criteria) this;
        }

        public Criteria andNodeNameIsNull() {
            addCriterion("node_name is null");
            return (Criteria) this;
        }

        public Criteria andNodeNameIsNotNull() {
            addCriterion("node_name is not null");
            return (Criteria) this;
        }

        public Criteria andNodeNameEqualTo(String value) {
            addCriterion("node_name =", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotEqualTo(String value) {
            addCriterion("node_name <>", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameGreaterThan(String value) {
            addCriterion("node_name >", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameGreaterThanOrEqualTo(String value) {
            addCriterion("node_name >=", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameLessThan(String value) {
            addCriterion("node_name <", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameLessThanOrEqualTo(String value) {
            addCriterion("node_name <=", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameLike(String value) {
            addCriterion("node_name like", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotLike(String value) {
            addCriterion("node_name not like", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameIn(List<String> values) {
            addCriterion("node_name in", values, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotIn(List<String> values) {
            addCriterion("node_name not in", values, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameBetween(String value1, String value2) {
            addCriterion("node_name between", value1, value2, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotBetween(String value1, String value2) {
            addCriterion("node_name not between", value1, value2, "nodeName");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleIsNull() {
            addCriterion("apprvoe_title is null");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleIsNotNull() {
            addCriterion("apprvoe_title is not null");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleEqualTo(String value) {
            addCriterion("apprvoe_title =", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleNotEqualTo(String value) {
            addCriterion("apprvoe_title <>", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleGreaterThan(String value) {
            addCriterion("apprvoe_title >", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleGreaterThanOrEqualTo(String value) {
            addCriterion("apprvoe_title >=", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleLessThan(String value) {
            addCriterion("apprvoe_title <", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleLessThanOrEqualTo(String value) {
            addCriterion("apprvoe_title <=", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleLike(String value) {
            addCriterion("apprvoe_title like", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleNotLike(String value) {
            addCriterion("apprvoe_title not like", value, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleIn(List<String> values) {
            addCriterion("apprvoe_title in", values, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleNotIn(List<String> values) {
            addCriterion("apprvoe_title not in", values, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleBetween(String value1, String value2) {
            addCriterion("apprvoe_title between", value1, value2, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApprvoeTitleNotBetween(String value1, String value2) {
            addCriterion("apprvoe_title not between", value1, value2, "apprvoeTitle");
            return (Criteria) this;
        }

        public Criteria andApplyUserIsNull() {
            addCriterion("apply_user is null");
            return (Criteria) this;
        }

        public Criteria andApplyUserIsNotNull() {
            addCriterion("apply_user is not null");
            return (Criteria) this;
        }

        public Criteria andApplyUserEqualTo(String value) {
            addCriterion("apply_user =", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserNotEqualTo(String value) {
            addCriterion("apply_user <>", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserGreaterThan(String value) {
            addCriterion("apply_user >", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserGreaterThanOrEqualTo(String value) {
            addCriterion("apply_user >=", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserLessThan(String value) {
            addCriterion("apply_user <", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserLessThanOrEqualTo(String value) {
            addCriterion("apply_user <=", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserLike(String value) {
            addCriterion("apply_user like", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserNotLike(String value) {
            addCriterion("apply_user not like", value, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserIn(List<String> values) {
            addCriterion("apply_user in", values, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserNotIn(List<String> values) {
            addCriterion("apply_user not in", values, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserBetween(String value1, String value2) {
            addCriterion("apply_user between", value1, value2, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserNotBetween(String value1, String value2) {
            addCriterion("apply_user not between", value1, value2, "applyUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserIsNull() {
            addCriterion("approve_user is null");
            return (Criteria) this;
        }

        public Criteria andApproveUserIsNotNull() {
            addCriterion("approve_user is not null");
            return (Criteria) this;
        }

        public Criteria andApproveUserEqualTo(String value) {
            addCriterion("approve_user =", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserNotEqualTo(String value) {
            addCriterion("approve_user <>", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserGreaterThan(String value) {
            addCriterion("approve_user >", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserGreaterThanOrEqualTo(String value) {
            addCriterion("approve_user >=", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserLessThan(String value) {
            addCriterion("approve_user <", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserLessThanOrEqualTo(String value) {
            addCriterion("approve_user <=", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserLike(String value) {
            addCriterion("approve_user like", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserNotLike(String value) {
            addCriterion("approve_user not like", value, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserIn(List<String> values) {
            addCriterion("approve_user in", values, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserNotIn(List<String> values) {
            addCriterion("approve_user not in", values, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserBetween(String value1, String value2) {
            addCriterion("approve_user between", value1, value2, "approveUser");
            return (Criteria) this;
        }

        public Criteria andApproveUserNotBetween(String value1, String value2) {
            addCriterion("approve_user not between", value1, value2, "approveUser");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andActionCodeIsNull() {
            addCriterion("action_code is null");
            return (Criteria) this;
        }

        public Criteria andActionCodeIsNotNull() {
            addCriterion("action_code is not null");
            return (Criteria) this;
        }

        public Criteria andActionCodeEqualTo(String value) {
            addCriterion("action_code =", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeNotEqualTo(String value) {
            addCriterion("action_code <>", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeGreaterThan(String value) {
            addCriterion("action_code >", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("action_code >=", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeLessThan(String value) {
            addCriterion("action_code <", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeLessThanOrEqualTo(String value) {
            addCriterion("action_code <=", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeLike(String value) {
            addCriterion("action_code like", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeNotLike(String value) {
            addCriterion("action_code not like", value, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeIn(List<String> values) {
            addCriterion("action_code in", values, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeNotIn(List<String> values) {
            addCriterion("action_code not in", values, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeBetween(String value1, String value2) {
            addCriterion("action_code between", value1, value2, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionCodeNotBetween(String value1, String value2) {
            addCriterion("action_code not between", value1, value2, "actionCode");
            return (Criteria) this;
        }

        public Criteria andActionValueIsNull() {
            addCriterion("action_value is null");
            return (Criteria) this;
        }

        public Criteria andActionValueIsNotNull() {
            addCriterion("action_value is not null");
            return (Criteria) this;
        }

        public Criteria andActionValueEqualTo(String value) {
            addCriterion("action_value =", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueNotEqualTo(String value) {
            addCriterion("action_value <>", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueGreaterThan(String value) {
            addCriterion("action_value >", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueGreaterThanOrEqualTo(String value) {
            addCriterion("action_value >=", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueLessThan(String value) {
            addCriterion("action_value <", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueLessThanOrEqualTo(String value) {
            addCriterion("action_value <=", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueLike(String value) {
            addCriterion("action_value like", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueNotLike(String value) {
            addCriterion("action_value not like", value, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueIn(List<String> values) {
            addCriterion("action_value in", values, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueNotIn(List<String> values) {
            addCriterion("action_value not in", values, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueBetween(String value1, String value2) {
            addCriterion("action_value between", value1, value2, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionValueNotBetween(String value1, String value2) {
            addCriterion("action_value not between", value1, value2, "actionValue");
            return (Criteria) this;
        }

        public Criteria andActionLabelIsNull() {
            addCriterion("action_label is null");
            return (Criteria) this;
        }

        public Criteria andActionLabelIsNotNull() {
            addCriterion("action_label is not null");
            return (Criteria) this;
        }

        public Criteria andActionLabelEqualTo(String value) {
            addCriterion("action_label =", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelNotEqualTo(String value) {
            addCriterion("action_label <>", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelGreaterThan(String value) {
            addCriterion("action_label >", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelGreaterThanOrEqualTo(String value) {
            addCriterion("action_label >=", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelLessThan(String value) {
            addCriterion("action_label <", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelLessThanOrEqualTo(String value) {
            addCriterion("action_label <=", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelLike(String value) {
            addCriterion("action_label like", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelNotLike(String value) {
            addCriterion("action_label not like", value, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelIn(List<String> values) {
            addCriterion("action_label in", values, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelNotIn(List<String> values) {
            addCriterion("action_label not in", values, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelBetween(String value1, String value2) {
            addCriterion("action_label between", value1, value2, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andActionLabelNotBetween(String value1, String value2) {
            addCriterion("action_label not between", value1, value2, "actionLabel");
            return (Criteria) this;
        }

        public Criteria andApvDateIsNull() {
            addCriterion("apv_date is null");
            return (Criteria) this;
        }

        public Criteria andApvDateIsNotNull() {
            addCriterion("apv_date is not null");
            return (Criteria) this;
        }

        public Criteria andApvDateEqualTo(Date value) {
            addCriterion("apv_date =", value, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateNotEqualTo(Date value) {
            addCriterion("apv_date <>", value, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateGreaterThan(Date value) {
            addCriterion("apv_date >", value, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateGreaterThanOrEqualTo(Date value) {
            addCriterion("apv_date >=", value, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateLessThan(Date value) {
            addCriterion("apv_date <", value, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateLessThanOrEqualTo(Date value) {
            addCriterion("apv_date <=", value, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateIn(List<Date> values) {
            addCriterion("apv_date in", values, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateNotIn(List<Date> values) {
            addCriterion("apv_date not in", values, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateBetween(Date value1, Date value2) {
            addCriterion("apv_date between", value1, value2, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvDateNotBetween(Date value1, Date value2) {
            addCriterion("apv_date not between", value1, value2, "apvDate");
            return (Criteria) this;
        }

        public Criteria andApvSourceIsNull() {
            addCriterion("apv_source is null");
            return (Criteria) this;
        }

        public Criteria andApvSourceIsNotNull() {
            addCriterion("apv_source is not null");
            return (Criteria) this;
        }

        public Criteria andApvSourceEqualTo(Integer value) {
            addCriterion("apv_source =", value, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceNotEqualTo(Integer value) {
            addCriterion("apv_source <>", value, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceGreaterThan(Integer value) {
            addCriterion("apv_source >", value, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("apv_source >=", value, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceLessThan(Integer value) {
            addCriterion("apv_source <", value, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceLessThanOrEqualTo(Integer value) {
            addCriterion("apv_source <=", value, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceIn(List<Integer> values) {
            addCriterion("apv_source in", values, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceNotIn(List<Integer> values) {
            addCriterion("apv_source not in", values, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceBetween(Integer value1, Integer value2) {
            addCriterion("apv_source between", value1, value2, "apvSource");
            return (Criteria) this;
        }

        public Criteria andApvSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("apv_source not between", value1, value2, "apvSource");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateIsNull() {
            addCriterion("expire_date is null");
            return (Criteria) this;
        }

        public Criteria andExpireDateIsNotNull() {
            addCriterion("expire_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpireDateEqualTo(Date value) {
            addCriterion("expire_date =", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateNotEqualTo(Date value) {
            addCriterion("expire_date <>", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateGreaterThan(Date value) {
            addCriterion("expire_date >", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateGreaterThanOrEqualTo(Date value) {
            addCriterion("expire_date >=", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateLessThan(Date value) {
            addCriterion("expire_date <", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateLessThanOrEqualTo(Date value) {
            addCriterion("expire_date <=", value, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateIn(List<Date> values) {
            addCriterion("expire_date in", values, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateNotIn(List<Date> values) {
            addCriterion("expire_date not in", values, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateBetween(Date value1, Date value2) {
            addCriterion("expire_date between", value1, value2, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireDateNotBetween(Date value1, Date value2) {
            addCriterion("expire_date not between", value1, value2, "expireDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateIsNull() {
            addCriterion("expire_notify_date is null");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateIsNotNull() {
            addCriterion("expire_notify_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateEqualTo(Date value) {
            addCriterion("expire_notify_date =", value, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateNotEqualTo(Date value) {
            addCriterion("expire_notify_date <>", value, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateGreaterThan(Date value) {
            addCriterion("expire_notify_date >", value, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateGreaterThanOrEqualTo(Date value) {
            addCriterion("expire_notify_date >=", value, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateLessThan(Date value) {
            addCriterion("expire_notify_date <", value, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateLessThanOrEqualTo(Date value) {
            addCriterion("expire_notify_date <=", value, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateIn(List<Date> values) {
            addCriterion("expire_notify_date in", values, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateNotIn(List<Date> values) {
            addCriterion("expire_notify_date not in", values, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateBetween(Date value1, Date value2) {
            addCriterion("expire_notify_date between", value1, value2, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andExpireNotifyDateNotBetween(Date value1, Date value2) {
            addCriterion("expire_notify_date not between", value1, value2, "expireNotifyDate");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumIsNull() {
            addCriterion("delay_max_num is null");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumIsNotNull() {
            addCriterion("delay_max_num is not null");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumEqualTo(Integer value) {
            addCriterion("delay_max_num =", value, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumNotEqualTo(Integer value) {
            addCriterion("delay_max_num <>", value, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumGreaterThan(Integer value) {
            addCriterion("delay_max_num >", value, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("delay_max_num >=", value, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumLessThan(Integer value) {
            addCriterion("delay_max_num <", value, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumLessThanOrEqualTo(Integer value) {
            addCriterion("delay_max_num <=", value, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumIn(List<Integer> values) {
            addCriterion("delay_max_num in", values, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumNotIn(List<Integer> values) {
            addCriterion("delay_max_num not in", values, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumBetween(Integer value1, Integer value2) {
            addCriterion("delay_max_num between", value1, value2, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayMaxNumNotBetween(Integer value1, Integer value2) {
            addCriterion("delay_max_num not between", value1, value2, "delayMaxNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumIsNull() {
            addCriterion("delay_cur_num is null");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumIsNotNull() {
            addCriterion("delay_cur_num is not null");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumEqualTo(Integer value) {
            addCriterion("delay_cur_num =", value, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumNotEqualTo(Integer value) {
            addCriterion("delay_cur_num <>", value, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumGreaterThan(Integer value) {
            addCriterion("delay_cur_num >", value, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("delay_cur_num >=", value, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumLessThan(Integer value) {
            addCriterion("delay_cur_num <", value, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumLessThanOrEqualTo(Integer value) {
            addCriterion("delay_cur_num <=", value, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumIn(List<Integer> values) {
            addCriterion("delay_cur_num in", values, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumNotIn(List<Integer> values) {
            addCriterion("delay_cur_num not in", values, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumBetween(Integer value1, Integer value2) {
            addCriterion("delay_cur_num between", value1, value2, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andDelayCurNumNotBetween(Integer value1, Integer value2) {
            addCriterion("delay_cur_num not between", value1, value2, "delayCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumIsNull() {
            addCriterion("over_time_max_num is null");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumIsNotNull() {
            addCriterion("over_time_max_num is not null");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumEqualTo(Integer value) {
            addCriterion("over_time_max_num =", value, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumNotEqualTo(Integer value) {
            addCriterion("over_time_max_num <>", value, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumGreaterThan(Integer value) {
            addCriterion("over_time_max_num >", value, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("over_time_max_num >=", value, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumLessThan(Integer value) {
            addCriterion("over_time_max_num <", value, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumLessThanOrEqualTo(Integer value) {
            addCriterion("over_time_max_num <=", value, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumIn(List<Integer> values) {
            addCriterion("over_time_max_num in", values, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumNotIn(List<Integer> values) {
            addCriterion("over_time_max_num not in", values, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumBetween(Integer value1, Integer value2) {
            addCriterion("over_time_max_num between", value1, value2, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeMaxNumNotBetween(Integer value1, Integer value2) {
            addCriterion("over_time_max_num not between", value1, value2, "overTimeMaxNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumIsNull() {
            addCriterion("over_time_cur_num is null");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumIsNotNull() {
            addCriterion("over_time_cur_num is not null");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumEqualTo(Integer value) {
            addCriterion("over_time_cur_num =", value, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumNotEqualTo(Integer value) {
            addCriterion("over_time_cur_num <>", value, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumGreaterThan(Integer value) {
            addCriterion("over_time_cur_num >", value, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("over_time_cur_num >=", value, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumLessThan(Integer value) {
            addCriterion("over_time_cur_num <", value, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumLessThanOrEqualTo(Integer value) {
            addCriterion("over_time_cur_num <=", value, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumIn(List<Integer> values) {
            addCriterion("over_time_cur_num in", values, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumNotIn(List<Integer> values) {
            addCriterion("over_time_cur_num not in", values, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumBetween(Integer value1, Integer value2) {
            addCriterion("over_time_cur_num between", value1, value2, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeCurNumNotBetween(Integer value1, Integer value2) {
            addCriterion("over_time_cur_num not between", value1, value2, "overTimeCurNum");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeIsNull() {
            addCriterion("over_time_date_time is null");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeIsNotNull() {
            addCriterion("over_time_date_time is not null");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeEqualTo(Date value) {
            addCriterion("over_time_date_time =", value, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeNotEqualTo(Date value) {
            addCriterion("over_time_date_time <>", value, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeGreaterThan(Date value) {
            addCriterion("over_time_date_time >", value, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("over_time_date_time >=", value, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeLessThan(Date value) {
            addCriterion("over_time_date_time <", value, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("over_time_date_time <=", value, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeIn(List<Date> values) {
            addCriterion("over_time_date_time in", values, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeNotIn(List<Date> values) {
            addCriterion("over_time_date_time not in", values, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeBetween(Date value1, Date value2) {
            addCriterion("over_time_date_time between", value1, value2, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("over_time_date_time not between", value1, value2, "overTimeDateTime");
            return (Criteria) this;
        }

        public Criteria andIsBetaIsNull() {
            addCriterion("is_beta is null");
            return (Criteria) this;
        }

        public Criteria andIsBetaIsNotNull() {
            addCriterion("is_beta is not null");
            return (Criteria) this;
        }

        public Criteria andIsBetaEqualTo(Integer value) {
            addCriterion("is_beta =", value, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaNotEqualTo(Integer value) {
            addCriterion("is_beta <>", value, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaGreaterThan(Integer value) {
            addCriterion("is_beta >", value, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_beta >=", value, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaLessThan(Integer value) {
            addCriterion("is_beta <", value, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaLessThanOrEqualTo(Integer value) {
            addCriterion("is_beta <=", value, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaIn(List<Integer> values) {
            addCriterion("is_beta in", values, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaNotIn(List<Integer> values) {
            addCriterion("is_beta not in", values, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaBetween(Integer value1, Integer value2) {
            addCriterion("is_beta between", value1, value2, "isBeta");
            return (Criteria) this;
        }

        public Criteria andIsBetaNotBetween(Integer value1, Integer value2) {
            addCriterion("is_beta not between", value1, value2, "isBeta");
            return (Criteria) this;
        }

        public Criteria andBetaUsersIsNull() {
            addCriterion("beta_users is null");
            return (Criteria) this;
        }

        public Criteria andBetaUsersIsNotNull() {
            addCriterion("beta_users is not null");
            return (Criteria) this;
        }

        public Criteria andBetaUsersEqualTo(String value) {
            addCriterion("beta_users =", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersNotEqualTo(String value) {
            addCriterion("beta_users <>", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersGreaterThan(String value) {
            addCriterion("beta_users >", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersGreaterThanOrEqualTo(String value) {
            addCriterion("beta_users >=", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersLessThan(String value) {
            addCriterion("beta_users <", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersLessThanOrEqualTo(String value) {
            addCriterion("beta_users <=", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersLike(String value) {
            addCriterion("beta_users like", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersNotLike(String value) {
            addCriterion("beta_users not like", value, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersIn(List<String> values) {
            addCriterion("beta_users in", values, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersNotIn(List<String> values) {
            addCriterion("beta_users not in", values, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersBetween(String value1, String value2) {
            addCriterion("beta_users between", value1, value2, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andBetaUsersNotBetween(String value1, String value2) {
            addCriterion("beta_users not between", value1, value2, "betaUsers");
            return (Criteria) this;
        }

        public Criteria andNodeTypeIsNull() {
            addCriterion("node_type is null");
            return (Criteria) this;
        }

        public Criteria andNodeTypeIsNotNull() {
            addCriterion("node_type is not null");
            return (Criteria) this;
        }

        public Criteria andNodeTypeEqualTo(String value) {
            addCriterion("node_type =", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeNotEqualTo(String value) {
            addCriterion("node_type <>", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeGreaterThan(String value) {
            addCriterion("node_type >", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("node_type >=", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeLessThan(String value) {
            addCriterion("node_type <", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeLessThanOrEqualTo(String value) {
            addCriterion("node_type <=", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeLike(String value) {
            addCriterion("node_type like", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeNotLike(String value) {
            addCriterion("node_type not like", value, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeIn(List<String> values) {
            addCriterion("node_type in", values, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeNotIn(List<String> values) {
            addCriterion("node_type not in", values, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeBetween(String value1, String value2) {
            addCriterion("node_type between", value1, value2, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNodeTypeNotBetween(String value1, String value2) {
            addCriterion("node_type not between", value1, value2, "nodeType");
            return (Criteria) this;
        }

        public Criteria andNotifyIsNull() {
            addCriterion("notify is null");
            return (Criteria) this;
        }

        public Criteria andNotifyIsNotNull() {
            addCriterion("notify is not null");
            return (Criteria) this;
        }

        public Criteria andNotifyEqualTo(Integer value) {
            addCriterion("notify =", value, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyNotEqualTo(Integer value) {
            addCriterion("notify <>", value, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyGreaterThan(Integer value) {
            addCriterion("notify >", value, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyGreaterThanOrEqualTo(Integer value) {
            addCriterion("notify >=", value, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyLessThan(Integer value) {
            addCriterion("notify <", value, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyLessThanOrEqualTo(Integer value) {
            addCriterion("notify <=", value, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyIn(List<Integer> values) {
            addCriterion("notify in", values, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyNotIn(List<Integer> values) {
            addCriterion("notify not in", values, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyBetween(Integer value1, Integer value2) {
            addCriterion("notify between", value1, value2, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyNotBetween(Integer value1, Integer value2) {
            addCriterion("notify not between", value1, value2, "notify");
            return (Criteria) this;
        }

        public Criteria andNotifyContentIsNull() {
            addCriterion("notify_content is null");
            return (Criteria) this;
        }

        public Criteria andNotifyContentIsNotNull() {
            addCriterion("notify_content is not null");
            return (Criteria) this;
        }

        public Criteria andNotifyContentEqualTo(String value) {
            addCriterion("notify_content =", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentNotEqualTo(String value) {
            addCriterion("notify_content <>", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentGreaterThan(String value) {
            addCriterion("notify_content >", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentGreaterThanOrEqualTo(String value) {
            addCriterion("notify_content >=", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentLessThan(String value) {
            addCriterion("notify_content <", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentLessThanOrEqualTo(String value) {
            addCriterion("notify_content <=", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentLike(String value) {
            addCriterion("notify_content like", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentNotLike(String value) {
            addCriterion("notify_content not like", value, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentIn(List<String> values) {
            addCriterion("notify_content in", values, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentNotIn(List<String> values) {
            addCriterion("notify_content not in", values, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentBetween(String value1, String value2) {
            addCriterion("notify_content between", value1, value2, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andNotifyContentNotBetween(String value1, String value2) {
            addCriterion("notify_content not between", value1, value2, "notifyContent");
            return (Criteria) this;
        }

        public Criteria andCallBackStrIsNull() {
            addCriterion("call_back_str is null");
            return (Criteria) this;
        }

        public Criteria andCallBackStrIsNotNull() {
            addCriterion("call_back_str is not null");
            return (Criteria) this;
        }

        public Criteria andCallBackStrEqualTo(String value) {
            addCriterion("call_back_str =", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrNotEqualTo(String value) {
            addCriterion("call_back_str <>", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrGreaterThan(String value) {
            addCriterion("call_back_str >", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrGreaterThanOrEqualTo(String value) {
            addCriterion("call_back_str >=", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrLessThan(String value) {
            addCriterion("call_back_str <", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrLessThanOrEqualTo(String value) {
            addCriterion("call_back_str <=", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrLike(String value) {
            addCriterion("call_back_str like", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrNotLike(String value) {
            addCriterion("call_back_str not like", value, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrIn(List<String> values) {
            addCriterion("call_back_str in", values, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrNotIn(List<String> values) {
            addCriterion("call_back_str not in", values, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrBetween(String value1, String value2) {
            addCriterion("call_back_str between", value1, value2, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andCallBackStrNotBetween(String value1, String value2) {
            addCriterion("call_back_str not between", value1, value2, "callBackStr");
            return (Criteria) this;
        }

        public Criteria andEmergencyIsNull() {
            addCriterion("emergency is null");
            return (Criteria) this;
        }

        public Criteria andEmergencyIsNotNull() {
            addCriterion("emergency is not null");
            return (Criteria) this;
        }

        public Criteria andEmergencyEqualTo(Integer value) {
            addCriterion("emergency =", value, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyNotEqualTo(Integer value) {
            addCriterion("emergency <>", value, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyGreaterThan(Integer value) {
            addCriterion("emergency >", value, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyGreaterThanOrEqualTo(Integer value) {
            addCriterion("emergency >=", value, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyLessThan(Integer value) {
            addCriterion("emergency <", value, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyLessThanOrEqualTo(Integer value) {
            addCriterion("emergency <=", value, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyIn(List<Integer> values) {
            addCriterion("emergency in", values, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyNotIn(List<Integer> values) {
            addCriterion("emergency not in", values, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyBetween(Integer value1, Integer value2) {
            addCriterion("emergency between", value1, value2, "emergency");
            return (Criteria) this;
        }

        public Criteria andEmergencyNotBetween(Integer value1, Integer value2) {
            addCriterion("emergency not between", value1, value2, "emergency");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeIsNull() {
            addCriterion("mail_check_code is null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeIsNotNull() {
            addCriterion("mail_check_code is not null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEqualTo(String value) {
            addCriterion("mail_check_code =", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeNotEqualTo(String value) {
            addCriterion("mail_check_code <>", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeGreaterThan(String value) {
            addCriterion("mail_check_code >", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeGreaterThanOrEqualTo(String value) {
            addCriterion("mail_check_code >=", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeLessThan(String value) {
            addCriterion("mail_check_code <", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeLessThanOrEqualTo(String value) {
            addCriterion("mail_check_code <=", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeLike(String value) {
            addCriterion("mail_check_code like", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeNotLike(String value) {
            addCriterion("mail_check_code not like", value, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeIn(List<String> values) {
            addCriterion("mail_check_code in", values, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeNotIn(List<String> values) {
            addCriterion("mail_check_code not in", values, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBetween(String value1, String value2) {
            addCriterion("mail_check_code between", value1, value2, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeNotBetween(String value1, String value2) {
            addCriterion("mail_check_code not between", value1, value2, "mailCheckCode");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusIsNull() {
            addCriterion("mail_check_code_status is null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusIsNotNull() {
            addCriterion("mail_check_code_status is not null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusEqualTo(Integer value) {
            addCriterion("mail_check_code_status =", value, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusNotEqualTo(Integer value) {
            addCriterion("mail_check_code_status <>", value, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusGreaterThan(Integer value) {
            addCriterion("mail_check_code_status >", value, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("mail_check_code_status >=", value, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusLessThan(Integer value) {
            addCriterion("mail_check_code_status <", value, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusLessThanOrEqualTo(Integer value) {
            addCriterion("mail_check_code_status <=", value, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusIn(List<Integer> values) {
            addCriterion("mail_check_code_status in", values, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusNotIn(List<Integer> values) {
            addCriterion("mail_check_code_status not in", values, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusBetween(Integer value1, Integer value2) {
            addCriterion("mail_check_code_status between", value1, value2, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("mail_check_code_status not between", value1, value2, "mailCheckCodeStatus");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginIsNull() {
            addCriterion("mail_check_code_begin is null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginIsNotNull() {
            addCriterion("mail_check_code_begin is not null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginEqualTo(Date value) {
            addCriterion("mail_check_code_begin =", value, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginNotEqualTo(Date value) {
            addCriterion("mail_check_code_begin <>", value, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginGreaterThan(Date value) {
            addCriterion("mail_check_code_begin >", value, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginGreaterThanOrEqualTo(Date value) {
            addCriterion("mail_check_code_begin >=", value, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginLessThan(Date value) {
            addCriterion("mail_check_code_begin <", value, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginLessThanOrEqualTo(Date value) {
            addCriterion("mail_check_code_begin <=", value, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginIn(List<Date> values) {
            addCriterion("mail_check_code_begin in", values, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginNotIn(List<Date> values) {
            addCriterion("mail_check_code_begin not in", values, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginBetween(Date value1, Date value2) {
            addCriterion("mail_check_code_begin between", value1, value2, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeBeginNotBetween(Date value1, Date value2) {
            addCriterion("mail_check_code_begin not between", value1, value2, "mailCheckCodeBegin");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndIsNull() {
            addCriterion("mail_check_code_end is null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndIsNotNull() {
            addCriterion("mail_check_code_end is not null");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndEqualTo(Date value) {
            addCriterion("mail_check_code_end =", value, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndNotEqualTo(Date value) {
            addCriterion("mail_check_code_end <>", value, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndGreaterThan(Date value) {
            addCriterion("mail_check_code_end >", value, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndGreaterThanOrEqualTo(Date value) {
            addCriterion("mail_check_code_end >=", value, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndLessThan(Date value) {
            addCriterion("mail_check_code_end <", value, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndLessThanOrEqualTo(Date value) {
            addCriterion("mail_check_code_end <=", value, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndIn(List<Date> values) {
            addCriterion("mail_check_code_end in", values, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndNotIn(List<Date> values) {
            addCriterion("mail_check_code_end not in", values, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndBetween(Date value1, Date value2) {
            addCriterion("mail_check_code_end between", value1, value2, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andMailCheckCodeEndNotBetween(Date value1, Date value2) {
            addCriterion("mail_check_code_end not between", value1, value2, "mailCheckCodeEnd");
            return (Criteria) this;
        }

        public Criteria andIsAgentIsNull() {
            addCriterion("is_agent is null");
            return (Criteria) this;
        }

        public Criteria andIsAgentIsNotNull() {
            addCriterion("is_agent is not null");
            return (Criteria) this;
        }

        public Criteria andIsAgentEqualTo(Integer value) {
            addCriterion("is_agent =", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentNotEqualTo(Integer value) {
            addCriterion("is_agent <>", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentGreaterThan(Integer value) {
            addCriterion("is_agent >", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_agent >=", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentLessThan(Integer value) {
            addCriterion("is_agent <", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentLessThanOrEqualTo(Integer value) {
            addCriterion("is_agent <=", value, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentIn(List<Integer> values) {
            addCriterion("is_agent in", values, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentNotIn(List<Integer> values) {
            addCriterion("is_agent not in", values, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentBetween(Integer value1, Integer value2) {
            addCriterion("is_agent between", value1, value2, "isAgent");
            return (Criteria) this;
        }

        public Criteria andIsAgentNotBetween(Integer value1, Integer value2) {
            addCriterion("is_agent not between", value1, value2, "isAgent");
            return (Criteria) this;
        }

        public Criteria andAgentUserIsNull() {
            addCriterion("agent_user is null");
            return (Criteria) this;
        }

        public Criteria andAgentUserIsNotNull() {
            addCriterion("agent_user is not null");
            return (Criteria) this;
        }

        public Criteria andAgentUserEqualTo(String value) {
            addCriterion("agent_user =", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserNotEqualTo(String value) {
            addCriterion("agent_user <>", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserGreaterThan(String value) {
            addCriterion("agent_user >", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserGreaterThanOrEqualTo(String value) {
            addCriterion("agent_user >=", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserLessThan(String value) {
            addCriterion("agent_user <", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserLessThanOrEqualTo(String value) {
            addCriterion("agent_user <=", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserLike(String value) {
            addCriterion("agent_user like", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserNotLike(String value) {
            addCriterion("agent_user not like", value, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserIn(List<String> values) {
            addCriterion("agent_user in", values, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserNotIn(List<String> values) {
            addCriterion("agent_user not in", values, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserBetween(String value1, String value2) {
            addCriterion("agent_user between", value1, value2, "agentUser");
            return (Criteria) this;
        }

        public Criteria andAgentUserNotBetween(String value1, String value2) {
            addCriterion("agent_user not between", value1, value2, "agentUser");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameIsNull() {
            addCriterion("apply_user_name is null");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameIsNotNull() {
            addCriterion("apply_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameEqualTo(String value) {
            addCriterion("apply_user_name =", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotEqualTo(String value) {
            addCriterion("apply_user_name <>", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameGreaterThan(String value) {
            addCriterion("apply_user_name >", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("apply_user_name >=", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameLessThan(String value) {
            addCriterion("apply_user_name <", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameLessThanOrEqualTo(String value) {
            addCriterion("apply_user_name <=", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameLike(String value) {
            addCriterion("apply_user_name like", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotLike(String value) {
            addCriterion("apply_user_name not like", value, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameIn(List<String> values) {
            addCriterion("apply_user_name in", values, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotIn(List<String> values) {
            addCriterion("apply_user_name not in", values, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameBetween(String value1, String value2) {
            addCriterion("apply_user_name between", value1, value2, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andApplyUserNameNotBetween(String value1, String value2) {
            addCriterion("apply_user_name not between", value1, value2, "applyUserName");
            return (Criteria) this;
        }

        public Criteria andIsApproveIsNull() {
            addCriterion("is_approve is null");
            return (Criteria) this;
        }

        public Criteria andIsApproveIsNotNull() {
            addCriterion("is_approve is not null");
            return (Criteria) this;
        }

        public Criteria andIsApproveEqualTo(String value) {
            addCriterion("is_approve =", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveNotEqualTo(String value) {
            addCriterion("is_approve <>", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveGreaterThan(String value) {
            addCriterion("is_approve >", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveGreaterThanOrEqualTo(String value) {
            addCriterion("is_approve >=", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveLessThan(String value) {
            addCriterion("is_approve <", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveLessThanOrEqualTo(String value) {
            addCriterion("is_approve <=", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveLike(String value) {
            addCriterion("is_approve like", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveNotLike(String value) {
            addCriterion("is_approve not like", value, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveIn(List<String> values) {
            addCriterion("is_approve in", values, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveNotIn(List<String> values) {
            addCriterion("is_approve not in", values, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveBetween(String value1, String value2) {
            addCriterion("is_approve between", value1, value2, "isApprove");
            return (Criteria) this;
        }

        public Criteria andIsApproveNotBetween(String value1, String value2) {
            addCriterion("is_approve not between", value1, value2, "isApprove");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Date value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Date value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Date value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Date value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Date value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Date> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Date> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Date value1, Date value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Date value1, Date value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Date value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Date value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Date value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Date value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Date value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Date> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Date> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Date value1, Date value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Date value1, Date value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andProcessIdIsNull() {
            addCriterion("process_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessIdIsNotNull() {
            addCriterion("process_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessIdEqualTo(String value) {
            addCriterion("process_id =", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotEqualTo(String value) {
            addCriterion("process_id <>", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdGreaterThan(String value) {
            addCriterion("process_id >", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdGreaterThanOrEqualTo(String value) {
            addCriterion("process_id >=", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdLessThan(String value) {
            addCriterion("process_id <", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdLessThanOrEqualTo(String value) {
            addCriterion("process_id <=", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdLike(String value) {
            addCriterion("process_id like", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotLike(String value) {
            addCriterion("process_id not like", value, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdIn(List<String> values) {
            addCriterion("process_id in", values, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotIn(List<String> values) {
            addCriterion("process_id not in", values, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdBetween(String value1, String value2) {
            addCriterion("process_id between", value1, value2, "processId");
            return (Criteria) this;
        }

        public Criteria andProcessIdNotBetween(String value1, String value2) {
            addCriterion("process_id not between", value1, value2, "processId");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("batch_no like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("batch_no not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}