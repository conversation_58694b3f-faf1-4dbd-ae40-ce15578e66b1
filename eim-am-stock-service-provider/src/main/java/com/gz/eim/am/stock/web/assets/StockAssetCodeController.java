package com.gz.eim.am.stock.web.assets;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.stock.api.assets.StockAssetCodeApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dto.request.assets.AssetCodeReqDTO;
import com.gz.eim.am.stock.dto.response.assets.AssetCodeRespDTO;
import com.gz.eim.am.stock.entity.vo.download.ExportAssetCodeEntity;
import com.gz.eim.am.stock.service.assets.StockAssetCodeService;
import com.gz.eim.am.stock.util.em.StockAssetsCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: lishuyang
 * @date: 2020/3/13
 * @description StockassetCode
 */
@RestController
@Slf4j
@RequestMapping("/api/am/stock/asset_code")
public class StockAssetCodeController implements StockAssetCodeApi {
    @Autowired
    private StockAssetCodeService stockAssetCodeService;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${namespace.name}")
    private String nameSpace;

    @Override
    public ResponseData batchGenerateAssetCode(AssetCodeReqDTO assetCodeReqDTO) {
        log.info("/api/am/stock/asset_code/batchGenerate {}", assetCodeReqDTO.toString());
        // 设置redis锁防止并发同时访问
        JwtUser user = SecurityUtil.getJwtUser();
        String redisKey = RedisKeyConstants.BATCH_ASSETS_PRINT_CREATE_CODE + assetCodeReqDTO.getCompanyCode() + StringConstant.COLON + assetCodeReqDTO.getCategoryCode() + StringConstant.COLON + assetCodeReqDTO.getYesLowValue();
        ResponseData res = null;
        try {
            if (redisUtil.setNx (nameSpace, redisKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                res = this.stockAssetCodeService.batchGenerateAssetCode(assetCodeReqDTO, user);
                // 直接删除相关key
                redisUtil.deleteByKey(nameSpace, redisKey);
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        }catch (ServiceUncheckedException e){
            // 直接删除相关key
            redisUtil.deleteByKey(nameSpace, redisKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            // 直接删除相关key
            redisUtil.deleteByKey(nameSpace, redisKey);
            log.error("一键生成资产编号", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData downloadAssetCode(AssetCodeReqDTO assetCodeReqDTO, final HttpServletRequest request, final HttpServletResponse response) {
        log.info("/api/am/stock/asset_code/download {}", assetCodeReqDTO.toString());
        try {

            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            assetCodeReqDTO.setNoPaging(true);
            final ResponseData data = stockAssetCodeService.selectAssetCode (assetCodeReqDTO, user);
            if(!data.getCode().equals(ResponseCode.SUCCESS_CODE)){
                return data;
            }
            final List<AssetCodeRespDTO> dtoList = (List<AssetCodeRespDTO>) data.getData();
            if (CollectionUtils.isNotEmpty(dtoList)) {
                //更新资产编号操作标识，现在只有打印才会更新操作标识
                //stockAssetCodeService.updateIsUsedByOperating(assetCodeReqDTO.getOperating(), null);
                final List<ExportAssetCodeEntity> modelList = new ArrayList<>(dtoList.size());
                dtoList.stream().forEach(dto -> {
                    final ExportAssetCodeEntity entity = new ExportAssetCodeEntity();
                    BeanUtils.copyProperties(dto, entity);
                    modelList.add(entity);
                });
                final LocalDateTime dateTime = LocalDateTime.now();
                final String fileName = "资产编号" + dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
                ExcelUtil.createExcelWithBuffer(modelList, fileName, request, response);
            }
        }catch (ServiceUncheckedException e){
            return ResponseData.createFailResult(e.getMessage());
        }catch (Exception e){
            e.printStackTrace ();
            log.error ("资产编号导出异常");
            return ResponseData.createFailResult("系统错误");
        }

        return ResponseData.createFailResult("无数据导出");
    }

    @Override
    public ResponseData serverPrintAssetCode(AssetCodeReqDTO assetCodeReqDTO) {
        log.info("/api/am/stock/asset_code/print {}", assetCodeReqDTO.toString());
        try {

            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            return stockAssetCodeService.assetCodePrint (assetCodeReqDTO, user);
        }catch (ServiceUncheckedException e){
            return ResponseData.createFailResult(e.getMessage());
        }catch (Exception e){
            log.error ("资产编号打印，错误{}", e.getMessage());
            return ResponseData.createFailResult("系统错误");
        }
    }

    @Override
    public ResponseData selectAssetCode(AssetCodeReqDTO assetCodeReqDTO) {
        log.info("/api/am/stock/asset_code/search {}", assetCodeReqDTO.toString());
        try {

            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            return stockAssetCodeService.selectAssetCode (assetCodeReqDTO, user);
        }catch (ServiceUncheckedException e){
            return ResponseData.createFailResult(e.getMessage());
        }catch (Exception e){
            log.error ("资产编号分页查询出错", e.getMessage());
            return ResponseData.createFailResult("系统错误");
        }
    }

}
