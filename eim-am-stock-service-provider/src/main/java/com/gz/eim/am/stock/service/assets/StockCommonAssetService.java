package com.gz.eim.am.stock.service.assets;

import com.gz.eim.am.stock.entity.StockAssets;
import com.gz.eim.am.stock.entity.StockInventoryAssetImport;

import java.util.List;
import java.util.Map;

/**
 * @author: weijun<PERSON>e
 * @date: 2020/7/9
 * @description 各资产尾表通用服务类
 */
public interface StockCommonAssetService {

    /**
     * 批量插入资产尾表数据
     * @param stockInventoryAssetImports
     * @return
     */
    Boolean batchInsertAssetsTail(List<StockInventoryAssetImport> stockInventoryAssetImports);

    /**
     * 根据资产编码查询查询尾表数据
     * @param assetsCode
     * @return
     */
    Map<String,String> selectTailDataByCode(String assetsCode);

    /**
     * 设置资产卡片扩展字段（型号、cup、内存、硬盘）
     * @param stockAssets
     * @param stockInventoryAssetImport1
     */
    void settingAssetExtFiled(StockAssets stockAssets, StockInventoryAssetImport stockInventoryAssetImport1);

     /**
       * @param: updateAssetsList
       * @description: 通过资产列表信息批量更新资产维修信息
       * @return: int
       * @author: <EMAIL>
       * @date: 2023/1/17
       */
    int batchUpdateAssetsExtendListByAssetsList(List<StockAssets> updateAssetsList);
}
