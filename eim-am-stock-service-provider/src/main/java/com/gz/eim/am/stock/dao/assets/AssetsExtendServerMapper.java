package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.StockAssetsExtendServer;
import com.gz.eim.am.stock.entity.StockInventoryAssetImport;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: weijunjie
 * @date: 2021/1/6
 * @description
 */
public interface AssetsExtendServerMapper {
    /**
     * 批量插入服务器尾表
     * @param stockInventoryAssetImports
     * @return
     */
    Boolean batchInsertAssetsServer(List<StockInventoryAssetImport> stockInventoryAssetImports);

    /**
     * 查询资产尾表数据
     * @param assetsCode
     * @return
     */
    Map<String, String> selectTailDataByCode(@Param("assetsCode") String assetsCode);

    /**
     * @param: list
     * @description: 根据资产编码批量更新资产尾表信息
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/1/17
     */
    int batchUpdateByAssetsCode(List<StockAssetsExtendServer> list);


}
