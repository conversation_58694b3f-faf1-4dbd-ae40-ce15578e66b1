package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.StockInventoryAssetImport;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: wei<PERSON><PERSON><PERSON>
 * @date: 2021/1/6
 * @description
 */
public interface AssetsExtendDiskMapper {
    /**
     * 批量插入硬盘尾表
     * @param stockInventoryAssetImports
     * @return
     */
    Boolean batchInsertAssetsDisk(List<StockInventoryAssetImport> stockInventoryAssetImports);

    /**
     * 查询尾表数据
     * @param assetsCode
     * @return
     */
    Map<String, String> selectTailDataByCode(@Param("assetsCode") String assetsCode);
}
