package com.gz.eim.am.stock.service.inventory.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;

import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;

/**
 * @author: lishuy<PERSON>
 * @date: 2019/12/27
 * @description:
 */
public interface StockPlanAssetRemandService {
    /**
     * 保存资产归还单
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     * @throws ParseException
     */
    ResponseData savePlanAssetRemand(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException;

    /**
     * 资产归还单分查询
     * @param inventoryInPlanSearchReqDTO
     * @param user
     * @return
     */
    ResponseData selectPlanAssetRemand(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO, JwtUser user);

    /**
     * 资产归还单详情查询
     * @param inventoryInPlanLineReqDTO
     * @param user
     * @return
     */
    ResponseData selectPlanAssetRemandById(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser user);

    /**
     * 资产归还单归还
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData assetPlanAssetRemandInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws InvocationTargetException, IllegalAccessException, ParseException;

    /**
     * 资产归还打印
     * @param inventoryInPlanHeadId
     * @param user
     * @return
     */
    ResponseData assetPlanAssetRemandPrint(Long inventoryInPlanHeadId, JwtUser user);

    /**
     * 查询持有人持有的当前仓库可归还的资产
     * @param holder
     * @param remandWarehouseCode
     * @return
     */
    ResponseData selectAssetByRemand(String holder, String remandWarehouseCode,String assetsCode,Integer pageNum,Integer pageSize);

    /**
     * 资产归还批量初始化
     * @param batchId
     * @param user
     * @return
     */
    ResponseData assetRemandInitialization(Integer batchId, JwtUser user);

    /**
     * 资产归还取消
     * @param inventoryInPlanHeadId
     * @return
     */
    ResponseData cancelPlanAssetRemandById(Long inventoryInPlanHeadId);

     /**
       * @param:
       * @description: 生成资产归还单Service调用
       * @return:
       * @author: <EMAIL>
       * @date: 2022/2/17
       */
     void savePlanAssetRemandByService(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws Exception;
}
