package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsExtendServer;
import com.gz.eim.am.stock.entity.StockAssetsExtendServerExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsExtendServerMapper {
    long countByExample(StockAssetsExtendServerExample example);

    int deleteByPrimaryKey(Long assetsServerId);

    int insert(StockAssetsExtendServer record);

    int insertSelective(StockAssetsExtendServer record);

    List<StockAssetsExtendServer> selectByExample(StockAssetsExtendServerExample example);

    StockAssetsExtendServer selectByPrimaryKey(Long assetsServerId);

    int updateByExampleSelective(@Param("record") StockAssetsExtendServer record, @Param("example") StockAssetsExtendServerExample example);

    int updateByExample(@Param("record") StockAssetsExtendServer record, @Param("example") StockAssetsExtendServerExample example);

    int updateByPrimaryKeySelective(StockAssetsExtendServer record);

    int updateByPrimaryKey(StockAssetsExtendServer record);
}