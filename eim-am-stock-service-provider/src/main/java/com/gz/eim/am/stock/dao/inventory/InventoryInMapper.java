package com.gz.eim.am.stock.dao.inventory;

import com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryInSearchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryInSnSearchReqDTO;
import com.gz.eim.am.stock.dto.response.InventoryFlowRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.InventoryInRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryIn;
import com.gz.eim.am.stock.entity.vo.StockInventoryInInfo;
import com.gz.eim.am.stock.entity.vo.download.ExportInventoryFlowEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 3:42
 */
public interface InventoryInMapper {

    /**
     * 插入单条返回主键
     * @param inventoryIn
     * @return
     */
    int insert(StockInventoryIn inventoryIn);

    /**
     * 入库流水
     * @param inventoryFlowReqDTO
     * @return
     */
    List<InventoryFlowRespDTO> selectInventoryInFlow(InventoryFlowReqDTO inventoryFlowReqDTO);

    /**
     * 入库流水计数
     * @param inventoryFlowReqDTO
     * @return
     */
    Long countInventoryInFlow(InventoryFlowReqDTO inventoryFlowReqDTO);

    /**
     * 出入库流水
     * @param inventoryFlowReqDTO
     * @return
     */
    List<InventoryFlowRespDTO> selectInventoryFlow(InventoryFlowReqDTO inventoryFlowReqDTO);

    /**
     * 出入库流水计数
     * @param inventoryFlowReqDTO
     * @return
     */
    Long countInventoryFlow(InventoryFlowReqDTO inventoryFlowReqDTO);

    /**
     * 根据参数查询入库单数量
     * @param inventoryInSearchReqDTO
     * @return
     */
    Long selectCountByParam(InventoryInSearchReqDTO inventoryInSearchReqDTO);

    /**
     * 根据参数查询入库单
     * @param inventoryInSearchReqDTO
     * @return
     */
    List<InventoryInRespDTO>  selectByPage(InventoryInSearchReqDTO inventoryInSearchReqDTO);

    /**
     * 根据入库单id查询入库单
     * @param inventoryInId
     * @return
     */
    InventoryInRespDTO  selectByInventoryInId(Long inventoryInId);

    /**
     * 根据参数查询可入库的入库单数量
     * @param inventoryInSearchReqDTO
     * @return
     */
    Long selectAccessibleCountByParam(InventoryInSearchReqDTO inventoryInSearchReqDTO);

    /**
     * 根据参数查询可入库的入库单
     * @param inventoryInSearchReqDTO
     * @return
     */
    List<InventoryInRespDTO>  selectAccessibleByPage(InventoryInSearchReqDTO inventoryInSearchReqDTO);

    /**
     * 根据参数查询入库单序列号数量
     * @param inventoryInSnSearchReqDTO
     * @return
     */
    Long selectSnCountByParam(InventoryInSnSearchReqDTO inventoryInSnSearchReqDTO);

    /**
     * 根据参数分页查询序列号
     * @param inventoryInSnSearchReqDTO
     * @return
     */
    List<String> selectSnByPage(InventoryInSnSearchReqDTO inventoryInSnSearchReqDTO);

    /**
     * 导出入库单流水
     * @param inventoryFlowReqDTO
     * @return
     */
    List<InventoryFlowRespDTO> exportInventoryInFlow(InventoryFlowReqDTO inventoryFlowReqDTO);

    /**
     * 批量插入入库单
     * @param stockInventoryInInfoList
     */
    void batchInsertInfo(List<StockInventoryInInfo> stockInventoryInInfoList);
}
