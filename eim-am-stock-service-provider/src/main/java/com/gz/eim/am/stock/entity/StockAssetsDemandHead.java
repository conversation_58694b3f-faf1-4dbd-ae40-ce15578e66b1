package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockAssetsDemandHead {
    private Long id;

    private String demandNo;

    private Date demandTime;

    private Integer receiveReason;

    private String applyUser;

    private Integer status;

    private String applyUserAddress;

    private String realUseUser;

    private String defaultReceiveWarehouseCode;

    private String claimUser;

    private String remark;

    private String billingUser;

    private Date billingTime;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Integer delFlag;

    private String applyUserProvince;

    private String applyUserCity;

    private String applyUserRegion;

    private String applyUserPhone;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDemandNo() {
        return demandNo;
    }

    public void setDemandNo(String demandNo) {
        this.demandNo = demandNo == null ? null : demandNo.trim();
    }

    public Date getDemandTime() {
        return demandTime;
    }

    public void setDemandTime(Date demandTime) {
        this.demandTime = demandTime;
    }

    public Integer getReceiveReason() {
        return receiveReason;
    }

    public void setReceiveReason(Integer receiveReason) {
        this.receiveReason = receiveReason;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser == null ? null : applyUser.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getApplyUserAddress() {
        return applyUserAddress;
    }

    public void setApplyUserAddress(String applyUserAddress) {
        this.applyUserAddress = applyUserAddress == null ? null : applyUserAddress.trim();
    }

    public String getRealUseUser() {
        return realUseUser;
    }

    public void setRealUseUser(String realUseUser) {
        this.realUseUser = realUseUser == null ? null : realUseUser.trim();
    }

    public String getDefaultReceiveWarehouseCode() {
        return defaultReceiveWarehouseCode;
    }

    public void setDefaultReceiveWarehouseCode(String defaultReceiveWarehouseCode) {
        this.defaultReceiveWarehouseCode = defaultReceiveWarehouseCode == null ? null : defaultReceiveWarehouseCode.trim();
    }

    public String getClaimUser() {
        return claimUser;
    }

    public void setClaimUser(String claimUser) {
        this.claimUser = claimUser == null ? null : claimUser.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser == null ? null : billingUser.trim();
    }

    public Date getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(Date billingTime) {
        this.billingTime = billingTime;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getApplyUserProvince() {
        return applyUserProvince;
    }

    public void setApplyUserProvince(String applyUserProvince) {
        this.applyUserProvince = applyUserProvince == null ? null : applyUserProvince.trim();
    }

    public String getApplyUserCity() {
        return applyUserCity;
    }

    public void setApplyUserCity(String applyUserCity) {
        this.applyUserCity = applyUserCity == null ? null : applyUserCity.trim();
    }

    public String getApplyUserRegion() {
        return applyUserRegion;
    }

    public void setApplyUserRegion(String applyUserRegion) {
        this.applyUserRegion = applyUserRegion == null ? null : applyUserRegion.trim();
    }

    public String getApplyUserPhone() {
        return applyUserPhone;
    }

    public void setApplyUserPhone(String applyUserPhone) {
        this.applyUserPhone = applyUserPhone == null ? null : applyUserPhone.trim();
    }
}