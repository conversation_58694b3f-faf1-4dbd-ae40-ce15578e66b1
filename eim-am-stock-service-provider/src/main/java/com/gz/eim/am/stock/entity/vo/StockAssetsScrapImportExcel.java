package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * @author: weijunjie
 * @date: 2021/3/25
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockAssetsScrapImportExcel  implements ExportModel {
    @ExportField(name = "资产编码")
    private String assetCode;

    @ExportField(name = "资产名称")
    private String attr1;

    @ExportField(name = "报废原因")
    private String status;

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }

    public String getAttr1() {
        return attr1;
    }

    public void setAttr1(String attr1) {
        this.attr1 = attr1;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        StockAssetsScrapImportExcel that = (StockAssetsScrapImportExcel) o;
        return Objects.equals(assetCode, that.assetCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(assetCode);
    }
}
