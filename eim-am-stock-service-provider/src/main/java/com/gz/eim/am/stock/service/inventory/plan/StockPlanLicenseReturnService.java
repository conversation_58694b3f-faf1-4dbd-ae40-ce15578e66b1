package com.gz.eim.am.stock.service.inventory.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;

import java.text.ParseException;

/**
 * @author: weijunjie
 * @date: 2020/9/2
 * @description 执照借用服务类
 */
public interface StockPlanLicenseReturnService {

    /**
     * 查询人员借用的执照
     * @param holder
     * @return
     */
    ResponseData selectLicense(String holder,Integer type);

    /**
     * 执照归还
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData planLicenseInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws ParseException;


    /**
     * 工作流平台查询计划归还单审批详情
     * @param bizId
     * @param jwtUser
     * @return
     */
    ResponseData selectLicenseReturnInfo(String bizId,JwtUser jwtUser);

    /**
     * 员工入口执照归还
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData licenseReturn(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws Exception;


    /**
     * 员工入口执照归还 流程平台回调
     * @param inventoryInPlanNo
     * @param status
     * @return
     */
    Boolean updateStatusByInventoryInPlanNo(String inventoryInPlanNo, Integer status);

    /**
     * 离职执照归还
     * @param inventoryInPlanHeadReqDTO
     * @param user
     * @return
     */
    ResponseData leaveLicenseInbound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser user) throws Exception;
}
