package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockTakingSnapshotHistory;
import com.gz.eim.am.stock.entity.StockTakingSnapshotHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockTakingSnapshotHistoryMapper {
    long countByExample(StockTakingSnapshotHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockTakingSnapshotHistory record);

    int insertSelective(StockTakingSnapshotHistory record);

    List<StockTakingSnapshotHistory> selectByExample(StockTakingSnapshotHistoryExample example);

    StockTakingSnapshotHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockTakingSnapshotHistory record, @Param("example") StockTakingSnapshotHistoryExample example);

    int updateByExample(@Param("record") StockTakingSnapshotHistory record, @Param("example") StockTakingSnapshotHistoryExample example);

    int updateByPrimaryKeySelective(StockTakingSnapshotHistory record);

    int updateByPrimaryKey(StockTakingSnapshotHistory record);
}