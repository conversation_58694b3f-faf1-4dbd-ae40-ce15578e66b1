package com.gz.eim.am.stock.service.impl.order.plan;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.base.StockAssetsDemandAssetsMapper;
import com.gz.eim.am.stock.dao.order.plan.AssetsDemandAssetsMapper;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecord;
import com.gz.eim.am.stock.entity.StockAssetsDemandAssets;
import com.gz.eim.am.stock.entity.StockAssetsDemandAssetsExample;
import com.gz.eim.am.stock.entity.StockAssetsDemandAssetsReqDO;
import com.gz.eim.am.stock.service.order.plan.StockAssetsDemandAssetsService;
import com.gz.eim.am.stock.util.common.ListUtil;
import com.gz.eim.am.stock.util.em.StockAssetsDemandAssetsEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class StockAssetsDemandAssetsServiceImpl implements StockAssetsDemandAssetsService {
    @Autowired
    private StockAssetsDemandAssetsMapper stockAssetsDemandAssetsMapper;
    @Autowired
    private AssetsDemandAssetsMapper assetsDemandAssetsMapper;


    @Override
    public int batchInsert(List<StockAssetsDemandAssets> stockAssetsDemandAssetsList) {
        if(CollectionUtils.isEmpty(stockAssetsDemandAssetsList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(stockAssetsDemandAssetsList.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsDemandAssets>> stockAssetsDemandAssetsListList = ListUtil.splitList(stockAssetsDemandAssetsList, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockAssetsDemandAssets> tempStockAssetsDemandAssetsList : stockAssetsDemandAssetsListList) {
                count += assetsDemandAssetsMapper.batchInsert(tempStockAssetsDemandAssetsList);
            }
            return count;
        }else {
            return assetsDemandAssetsMapper.batchInsert(stockAssetsDemandAssetsList);
        }
    }

    @Override
    public int batchUpdate(List<StockAssetsDemandAssets> stockAssetsDemandAssetsList) {
        if(CollectionUtils.isEmpty(stockAssetsDemandAssetsList)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次更新
        if(stockAssetsDemandAssetsList.size() > CommonConstant.MAX_UPDATE_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockAssetsDemandAssets>> stockAssetsDemandAssetsListList = ListUtil.splitList(stockAssetsDemandAssetsList, CommonConstant.MAX_UPDATE_COUNT);
            for (List<StockAssetsDemandAssets> tempStockAssetsDemandAssetsList : stockAssetsDemandAssetsListList) {
                count += assetsDemandAssetsMapper.batchUpdate(tempStockAssetsDemandAssetsList);
            }
            return count;
        }else {
            return assetsDemandAssetsMapper.batchUpdate(stockAssetsDemandAssetsList);
        }
    }

    @Override
    public List<StockAssetsDemandAssets> selectList(StockAssetsDemandAssetsReqDO stockAssetsDemandAssetsReqDO) {
        if(null == stockAssetsDemandAssetsReqDO){
            return new ArrayList<>();
        }
        StockAssetsDemandAssetsExample stockAssetsDemandAssetsExample = new StockAssetsDemandAssetsExample();
        StockAssetsDemandAssetsExample.Criteria criteria = stockAssetsDemandAssetsExample.createCriteria();
        if(!StringUtils.isEmpty(stockAssetsDemandAssetsReqDO.getDeliveryPlanNo())){
            criteria.andDeliveryPlanNoEqualTo(stockAssetsDemandAssetsReqDO.getDeliveryPlanNo());
        }
        if(!StringUtils.isEmpty(stockAssetsDemandAssetsReqDO.getDemandNo())){
            criteria.andDemandNoEqualTo(stockAssetsDemandAssetsReqDO.getDemandNo());
        }
        if(!StringUtils.isEmpty(stockAssetsDemandAssetsReqDO.getAssetsCode())){
            criteria.andAssetsCodeEqualTo(stockAssetsDemandAssetsReqDO.getAssetsCode());
        }
        if(stockAssetsDemandAssetsReqDO.getDeliveryMethod() != null){
            criteria.andDeliveryMethodEqualTo(stockAssetsDemandAssetsReqDO.getDeliveryMethod());
        }
        if(stockAssetsDemandAssetsReqDO.getDeliveryStatus() != null){
            criteria.andDeliveryStatusEqualTo(stockAssetsDemandAssetsReqDO.getDeliveryStatus());
        }
        if(stockAssetsDemandAssetsReqDO.getTrackingNumberIsNotNullAndIsNotEmpty() != null && stockAssetsDemandAssetsReqDO.getTrackingNumberIsNotNullAndIsNotEmpty()){
            criteria.andTrackingNumberIsNotNull();
            criteria.andTrackingNumberNotEqualTo(StringConstant.EMPTY);
        }
        if(stockAssetsDemandAssetsReqDO.getLessThanSendDate() != null){
            criteria.andSendDateLessThan(stockAssetsDemandAssetsReqDO.getLessThanSendDate());
        }
        return stockAssetsDemandAssetsMapper.selectByExample(stockAssetsDemandAssetsExample);
    }

    @Override
    public StockAssetsDemandAssets selectOne(StockAssetsDemandAssetsReqDO stockAssetsDemandAssetsReqDO) {
        List<StockAssetsDemandAssets> stockAssetsDemandAssetsList = selectList(stockAssetsDemandAssetsReqDO);
        if(CollectionUtils.isEmpty(stockAssetsDemandAssetsList)){
            return null;
        }
        return stockAssetsDemandAssetsList.get(CommonConstant.NUMBER_ZERO);
    }
}
