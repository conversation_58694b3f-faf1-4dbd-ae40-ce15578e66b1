package com.gz.eim.am.stock.web.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.assets.StockAssetsCategoryApi;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCategoryReqDTO;
import com.gz.eim.am.stock.service.assets.StockAssetsCategoryService;

import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-12 AM 10:51
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/assets/category")
public class StockAssetsCategoryController implements StockAssetsCategoryApi {

    @Autowired
    private StockAssetsCategoryService categoryService;

    @Override
    public ResponseData selectAssetsCategory() {
        return categoryService.selectAssetsCategory();
    }

    @Override
    public ResponseData selectAssetsCategoryByPage(StockAssetsCategoryReqDTO reqDTO) {
        log.info("/api/am/stock/assets/category", reqDTO.toString());
        return categoryService.selectByStockAssetsCategoryByPage(reqDTO);
    }

    @Override
    public ResponseData selectAssetsCategoryVague(String param, Integer limit, String notIncludeCategoryList) {
        log.info("/api/am/stock/assets/category/vague{},notIncludeCategory{}", param, notIncludeCategoryList);
        return categoryService.selectAssetsCategoryVague(param,limit,notIncludeCategoryList);
    }
}
