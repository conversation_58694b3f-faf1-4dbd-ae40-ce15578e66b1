package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wangjing67
 * @Date: 7/9/21 2:37 下午
 * @description
 */
public class SysUserEnum {

    /**
     * 员工类型
     */
    public enum empType {

       // 0:正式 1:正式实习生 11:外签 37:外签实习生
        /**
         * 正式
         */
        FORMAL(0, "正式"),
        /**
         * 正式实习生
         */
        FORMAL_INTERN(1, "正式实习生"),
        /**
         * 外签
         */
        OUTSOURCING(11, "外签"),
        /**
         * 外签实习生
         */
        OUTSOURCING_INTERN(37, "外签实习生"),
        ;

        empType(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    public static List<Integer> empTypeList = Arrays.stream(SysUserEnum.empType.values()).map(SysUserEnum.empType::getValue).collect(Collectors.toList());

    public static Map<Integer, String> empTypeMap =
            Arrays.stream(SysUserEnum.empType.values()).collect(
                    Collectors.toMap(SysUserEnum.empType::getValue, SysUserEnum.empType::getDesc));
}
