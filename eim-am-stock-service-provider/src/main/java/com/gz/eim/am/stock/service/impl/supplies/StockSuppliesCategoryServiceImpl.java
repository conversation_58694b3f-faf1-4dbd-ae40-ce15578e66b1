package com.gz.eim.am.stock.service.impl.supplies;


import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockSuppliesCategoryMapper;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesCategoryReqDTO;
import com.gz.eim.am.stock.dto.response.supplies.BusinessLineRespDTO;
import com.gz.eim.am.stock.dto.response.supplies.SuppliesCategoryRespDTO;
import com.gz.eim.am.stock.entity.StockSuppliesCategory;
import com.gz.eim.am.stock.entity.StockSuppliesCategoryExample;
import com.gz.eim.am.stock.service.supplies.StockSuppliesCategoryService;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2019-12-10 PM 8:10
 */
@Service
public class StockSuppliesCategoryServiceImpl implements StockSuppliesCategoryService {

    @Autowired
    private StockSuppliesCategoryMapper mapper;

    @Override
    public ResponseData selectSuppliesCategory(final SuppliesCategoryReqDTO categoryReqDTO) {
        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        if (categoryReqDTO != null) {
            StockSuppliesCategoryExample.Criteria criteria = example.createCriteria();
            if (categoryReqDTO.getParentId() != null) {
                criteria.andParentIdEqualTo(categoryReqDTO.getParentId());
            }
            if (StringUtils.isNotBlank(categoryReqDTO.getCode())) {
                criteria.andCodeEqualTo(categoryReqDTO.getCode());
            }
            if (categoryReqDTO.getLevel() != null) {
                criteria.andLevelEqualTo(categoryReqDTO.getLevel());
            }
            if (categoryReqDTO.getStatus() != null) {
                criteria.andStatusEqualTo(categoryReqDTO.getStatus());
            }
            if (categoryReqDTO.getVendorType() != null){
                criteria.andVendorTypeEqualTo(categoryReqDTO.getVendorType());
            }
            criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        }
        List<StockSuppliesCategory> categoryList = mapper.selectByExample(example);
        ResponseData resp = ResponseData.createSuccessResult(categoryList);
        return resp;
    }

    @Override
    public List<StockSuppliesCategory> selectSuppliesCategory(final List<Long> ids, final List<String> codes) {
        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        StockSuppliesCategoryExample.Criteria criteria = example.createCriteria();
        if (ids != null && ids.size() > 0) {
            criteria.andSuppliesCategoryIdIn(ids);
            return this.mapper.selectByExample(example);
        } else if (codes != null && codes.size() > 0) {
            criteria.andCodeIn(codes);
            return this.mapper.selectByExample(example);
        }
        return null;
    }

    @Override
    public List<StockSuppliesCategory> selectSuppliesCategoryList(final SuppliesCategoryReqDTO categoryReqDTO) {
        return null;
    }

    @Override
    public StockSuppliesCategory selectSuppliesCategoryById(final Long id) {
        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        example.createCriteria().andSuppliesCategoryIdEqualTo(id);
        List<StockSuppliesCategory> categoryList = this.mapper.selectByExample(example);
        if (categoryList != null && categoryList.size() > 0) {
            return categoryList.get(0);
        }
        return null;
    }

    @Override
    public StockSuppliesCategory selectSuppliesCategoryByCode(final String code) {
        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        example.createCriteria().andCodeEqualTo(code);
        List<StockSuppliesCategory> categoryList = this.mapper.selectByExample(example);
        if (categoryList != null && categoryList.size() > 0) {
            return categoryList.get(0);
        }
        return null;
    }

    @Override
    public ResponseData selectSuppliesCategoryBus(SuppliesCategoryReqDTO categoryReqDTO) {
        ResponseData responseData = selectSuppliesCategory(categoryReqDTO);
        List<StockSuppliesCategory> categoryList = (List<StockSuppliesCategory>) responseData.getData();
        if (CollectionUtils.isEmpty(categoryList)) {
            return ResponseData.createSuccessResult();
        }

        List<SuppliesCategoryRespDTO> suppliesCategoryResqDTOList = changeToRespDTO(categoryList);
        return ResponseData.createSuccessResult(suppliesCategoryResqDTOList);
    }

    @Override
    public com.gz.eim.am.pdm.dto.ResponseData selectSuppliesSubCategory(List<String> subCategoryCodes) {
        if (CollectionUtils.isEmpty(subCategoryCodes)) {
            return com.gz.eim.am.pdm.dto.ResponseData.createSuccessResult();
        }

        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        StockSuppliesCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andCodeIn(subCategoryCodes);
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        List<StockSuppliesCategory> categoryList = mapper.selectByExample(example);
        if (CollectionUtils.isEmpty(categoryList)) {
            return com.gz.eim.am.pdm.dto.ResponseData.createSuccessResult();
        }

        List<SuppliesCategoryRespDTO> suppliesCategoryRespDTOList = changeToRespDTO(categoryList);
        return com.gz.eim.am.pdm.dto.ResponseData.createSuccessResult(suppliesCategoryRespDTOList);
    }

    @Override
    public StockSuppliesCategory selectSuppliesCategoryByName(String name) {
        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        example.createCriteria().andNameEqualTo(name);
        List<StockSuppliesCategory> categoryList = this.mapper.selectByExample(example);
        if (categoryList != null && categoryList.size() > 0) {
            return categoryList.get(0);
        }
        return null;
    }

    @Override
    public List<StockSuppliesCategory> selectSuppliesCategoryByNameList(List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return new ArrayList<>();
        }

        StockSuppliesCategoryExample example = new StockSuppliesCategoryExample();
        StockSuppliesCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andNameIn(nameList);
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        List<StockSuppliesCategory> categoryList = mapper.selectByExample(example);
        if (CollectionUtils.isEmpty(categoryList)) {
            return new ArrayList<>();
        }
        return categoryList;
    }

    /**
     * 转换数据格式
     * @param categoryList
     * @return
     */
    private List<SuppliesCategoryRespDTO> changeToRespDTO(List<StockSuppliesCategory> categoryList){
        if (CollectionUtils.isEmpty(categoryList)){
            return null;
        }
        List<SuppliesCategoryRespDTO> suppliesCategoryResqDTOList = new ArrayList<>(categoryList.size());

        for (StockSuppliesCategory stockSuppliesCategory : categoryList) {
            SuppliesCategoryRespDTO suppliesCategoryResqDTO = new SuppliesCategoryRespDTO();
            BeanUtils.copyProperties(stockSuppliesCategory, suppliesCategoryResqDTO);
            if (StringUtils.isNotBlank(stockSuppliesCategory.getBussinessLine())) {
                suppliesCategoryResqDTO.setBusinessLineList(JsonUtil.readStringToList(stockSuppliesCategory.getBussinessLine(), BusinessLineRespDTO.class));
            }
            suppliesCategoryResqDTOList.add(suppliesCategoryResqDTO);
        }

        return suppliesCategoryResqDTOList;
    }

}
