package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockSuppliesConfig;
import com.gz.eim.am.stock.entity.StockSuppliesConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockSuppliesConfigMapper {
    long countByExample(StockSuppliesConfigExample example);

    int deleteByPrimaryKey(Long suppliesConfigId);

    int insert(StockSuppliesConfig record);

    int insertSelective(StockSuppliesConfig record);

    List<StockSuppliesConfig> selectByExample(StockSuppliesConfigExample example);

    StockSuppliesConfig selectByPrimaryKey(Long suppliesConfigId);

    int updateByExampleSelective(@Param("record") StockSuppliesConfig record, @Param("example") StockSuppliesConfigExample example);

    int updateByExample(@Param("record") StockSuppliesConfig record, @Param("example") StockSuppliesConfigExample example);

    int updateByPrimaryKeySelective(StockSuppliesConfig record);

    int updateByPrimaryKey(StockSuppliesConfig record);
}