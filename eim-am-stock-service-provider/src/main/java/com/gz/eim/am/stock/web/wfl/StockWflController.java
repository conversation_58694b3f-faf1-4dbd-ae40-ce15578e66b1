package com.gz.eim.am.stock.web.wfl;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.wfl.StockWflApi;
import com.gz.eim.am.stock.service.wfl.WflService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/11/14
 * @description: 工作流
 */
@RestController
@RequestMapping("/api/am/stock")
public class StockWflController implements StockWflApi {
    private final Logger logger = LoggerFactory.getLogger(StockWflController.class);

    @Autowired
    private WflService wflService;

    @Override
    public ResponseData selectWflFormLineListByBizId(String bizId, String lobNo) {

        logger.info("/api/am/stock/wfl/, bizId:{}, lobNo:{}", bizId, lobNo);
        ResponseData res = null;
        try {
            res = this.wflService.selectWflFormLineListByBizId(bizId, lobNo);
        } catch (Exception e) {
            logger.error("获取表单行集合异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    @Override
    public ResponseData receiveWflApproveResult(String bizId, String lobNo, Integer approveStatus) {
        logger.info("/api/am/stock/wfl/receiveResult, bizId:{}, lobNo:{}， approveStatus：{}", bizId, lobNo, approveStatus);
        ResponseData res = null;
        try {
            res = this.wflService.receiveWflApproveResult(bizId, lobNo, approveStatus);
        } catch (RuntimeException e){
            logger.error("接受审批流状态 errorMessage ={}", e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e) {
            logger.error("接受审批流状态异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData updateCheckDetail(String bizId, String checkPerson, String formId, String taskId) {
        logger.info("/api/am/stock/wfl/updateCheckDetail, bizId={}, checkPerson={}, formId={}, taskId = {}", bizId, checkPerson, formId, taskId);
        ResponseData res = null;
        try {
            res = this.wflService.updateCheckTaskDetail(bizId, checkPerson, formId, taskId);
        } catch (Exception e) {
            logger.error("获取表单行集合异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
    /**
     * 审批回调接口，管理员认领资产领用单
     * @return
     */
    @Override
    public ResponseData updateDemand(String bizId, String claimUser, String lobNo) {
        logger.info("/api/am/stock/wfl/updateDemand, bizId={}, claimUser={}, lobNo={}, formId={}, sn={}", bizId, claimUser, lobNo);
        ResponseData res = null;
        try {
            res = this.wflService.updateDemand(bizId, claimUser, lobNo);
        } catch (Exception e) {
            logger.error("获取表单行集合异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
    /**
     * 审批回调接口，更新计划单的实际领用人
     * @return
     */
    @Override
    public ResponseData updatePlanAssetsDemandReceiveUser(String bizId, String receiveUser, String lobNo) {
        logger.info("/api/am/stock/wfl/updatePlanAssetsDemandReceiveUser, bizId={}, receiveUser={}, lobNo={}", bizId, receiveUser, lobNo);
        ResponseData res = null;
        try {
            res = this.wflService.updatePlanAssetsDemandReceiveUser(bizId, receiveUser, lobNo);
        } catch (Exception e) {
            logger.error("获取表单行集合异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
