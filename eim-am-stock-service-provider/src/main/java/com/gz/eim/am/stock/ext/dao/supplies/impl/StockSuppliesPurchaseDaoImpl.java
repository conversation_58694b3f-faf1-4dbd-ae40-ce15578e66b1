package com.gz.eim.am.stock.ext.dao.supplies.impl;

import com.gz.eim.am.stock.entity.StockSuppliesPurchase;
import com.gz.eim.am.stock.entity.StockSuppliesPurchaseExample;
import com.gz.eim.am.stock.ext.dao.supplies.StockSuppliesPurchaseDao;
import com.gz.eim.am.stock.mapper.base.StockSuppliesPurchaseMapper;
import com.gz.eim.am.stock.util.em.CommonEnum;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/7/13
 * <p>
 *   物料采购属性实现类
 * </p>
 */
@Repository
public class StockSuppliesPurchaseDaoImpl implements StockSuppliesPurchaseDao {
    @Resource
    private StockSuppliesPurchaseMapper stockSuppliesPurchaseMapper;

    @Override
    public List<StockSuppliesPurchase> listBySuppliesCodeListAndWarehouseType(List<String> suppliesCodeList , Integer warehouseType) {
        if(CollectionUtils.isEmpty (suppliesCodeList) || Objects.isNull (warehouseType)){
            return Collections.emptyList ();
        }

        StockSuppliesPurchaseExample example = new StockSuppliesPurchaseExample ();
        StockSuppliesPurchaseExample.Criteria criteria = example.createCriteria ();
        criteria.andSuppliesCodeIn (suppliesCodeList);
        criteria.andWarehouseTypeCodeEqualTo (warehouseType);
        criteria.andIsDelFlagEqualTo(CommonEnum.delFlag.NO.getValue());
        return stockSuppliesPurchaseMapper.selectByExample (example);
    }
}
