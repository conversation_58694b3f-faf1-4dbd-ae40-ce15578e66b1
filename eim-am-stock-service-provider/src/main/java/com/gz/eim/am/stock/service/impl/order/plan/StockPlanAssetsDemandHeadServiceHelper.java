package com.gz.eim.am.stock.service.impl.order.plan;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.constant.ApvTaskConstant;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.MetaDataConstants;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsReqDTO;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandCourierCreateOrderReqDTO;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandHeadReqDTO;
import com.gz.eim.am.stock.dto.request.demand.StockAssetsDemandLineReqDTO;
import com.gz.eim.am.stock.dto.response.UserRespDTO;
import com.gz.eim.am.stock.dto.response.demand.QueryStockAssetsDemandAssetsListRespDTO;
import com.gz.eim.am.stock.dto.response.demand.StockAssetsDemandCourierApproveDetailRespDTO;
import com.gz.eim.am.stock.dto.response.demand.StockAssetsDemandHeadRespDTO;
import com.gz.eim.am.stock.dto.response.demand.StockAssetsDemandTransportTrackDetailRespDTO;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.PsBusLocationTbl;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.entity.ambase.SysUserBasicInfo;
import com.gz.eim.am.stock.entity.ambase.SysUserReqDO;
import com.gz.eim.am.stock.entity.ambase.meta.AssetsDemandOperationApproveUserConfig;
import com.gz.eim.am.stock.entity.ambase.meta.AssetsDemandSendRegionMappingConfig;
import com.gz.eim.am.stock.entity.vo.StockAssetsDemandAssetsListExportExcel;
import com.gz.eim.am.stock.entity.vo.StockDeliveryPlanLineInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.order.StockDeliveryDetailAssetsService;
import com.gz.eim.am.stock.service.order.StockDeliveryService;
import com.gz.eim.am.stock.service.order.plan.*;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseBaseService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.service.wfl.WflService;
import com.gz.eim.am.stock.util.common.ListUtil;
import com.gz.eim.am.stock.util.common.RegexCheckUtil;
import com.gz.eim.am.stock.util.em.*;
import com.gz.eim.am.stock.util.task.StockThreadPool;
import com.gz.eim.am.transport.api.delivery.zto.ZtoDeliveryApi;
import com.gz.eim.am.transport.dto.request.delivery.zto.ZtoBatchCloudPrintReqDTO;
import com.gz.eim.am.transport.dto.request.delivery.zto.ZtoTransportOrderReqDTO;
import com.gz.eim.am.transport.dto.response.order.TransportOrderRespDTO;
import com.gz.eim.am.transport.dto.response.track.TransportTrackLineRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: StockPlanAssetsDemandHeadServiceHelper
 * @description: 资产需求帮助类
 * @author: <EMAIL>
 * @date: 2023/6/27
 **/
@Slf4j
@Component
public class StockPlanAssetsDemandHeadServiceHelper {

    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockWarehouseBaseService stockWarehouseBaseService;
    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private StockDeliveryPlanHeadService stockDeliveryPlanHeadService;
    @Autowired
    private StockDeliveryService stockDeliveryService;
    @Autowired
    private StockDeliveryDetailAssetsService stockDeliveryDetailAssetsService;
    @Autowired
    private ZtoDeliveryApi ztoDeliveryApi;
    @Autowired
    private StockPlanAssetsDemandHeadService stockPlanAssetsDemandHeadService;
    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;
    @Autowired
    private StockWarehouseService stockWarehouseService;
    @Autowired
    private StockAssetsDemandAssetsService stockAssetsDemandAssetsService;
    @Autowired
    private WflService wflService;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;
    @Autowired
    private StockPlanAssetsDemandLineService stockPlanAssetsDemandLineService;

    /**
     * @param: stockAssetsDemandCourierApproveDetailRespDTO, stockDeliveryPlanHead
     * @param: stockAssetsDemandHead
     * @description: 设置快递员审批页头信息
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/6/26
     */
    public void setStockAssetsDemandCourierApproveDetailRespDTOHeadData(StockAssetsDemandCourierApproveDetailRespDTO stockAssetsDemandCourierApproveDetailRespDTO, StockDeliveryPlanHead stockDeliveryPlanHead,
                                                                        StockAssetsDemandHead stockAssetsDemandHead) {
        // 根据制单人姓名查询制单人的手机号
        String billingUser = stockDeliveryPlanHead.getBillingUser();
        String applyUser = stockAssetsDemandHead.getApplyUser();
        List<String> empIdList = new ArrayList<>(CommonConstant.NUMBER_TWO);
        empIdList.add(billingUser);
        empIdList.add(applyUser);
        Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdList);
        SysUser sysUser = stringSysUserMap.get(billingUser);
        if (sysUser != null) {
            stockAssetsDemandCourierApproveDetailRespDTO.setSenderName(sysUser.getName());
            stockAssetsDemandCourierApproveDetailRespDTO.setSenderMobile(sysUser.getPhone());
        }
        // 查询区域映射配置信息
        List<AssetsDemandSendRegionMappingConfig> assetsDemandSendRegionMappingConfigList = ambaseCommonService.selectMetaDataByParam(MetaDataConstants.STOCK_MODULE_ASSETS_DEMAND, MetaDataConstants.SEND_REGION_MAPPING, AssetsDemandSendRegionMappingConfig.class);
        Map<String, String> regionMap = null;
        if (CollectionUtils.isNotEmpty(assetsDemandSendRegionMappingConfigList)) {
            regionMap = assetsDemandSendRegionMappingConfigList.stream().collect(Collectors.toMap(AssetsDemandSendRegionMappingConfig::getSource, AssetsDemandSendRegionMappingConfig::getTarget, (k1, k2) -> k2));
        }
        // 根据出库仓库查询发件人的省、市、区、发件人详细地址
        String outWarehouseCode = stockDeliveryPlanHead.getOutWarehouseCode();
        StockWarehouseBase stockWarehouseBase = stockWarehouseBaseService.getByWarehouseCode(outWarehouseCode);
        if (stockWarehouseBase != null) {
            stockAssetsDemandCourierApproveDetailRespDTO.setSenderProvince(stockWarehouseBase.getProvince());
            stockAssetsDemandCourierApproveDetailRespDTO.setSenderCity(stockWarehouseBase.getCity());
            stockAssetsDemandCourierApproveDetailRespDTO.setSenderDistrict(getMappingRegion(stockWarehouseBase.getArea(), regionMap));
            stockAssetsDemandCourierApproveDetailRespDTO.setSenderAddress(stockWarehouseBase.getAddress());
        }
        // 根据领用单查询需求单，获取收件人姓名、手机号、省、市、区、收件人详细地址
        sysUser = stringSysUserMap.get(applyUser);
        if (sysUser != null) {
            stockAssetsDemandCourierApproveDetailRespDTO.setReceiverName(sysUser.getName());
        }
        String applyUserProvince = stockAssetsDemandHead.getApplyUserProvince();
        String applyUserCity = stockAssetsDemandHead.getApplyUserCity();
        String applyUserRegion = stockAssetsDemandHead.getApplyUserRegion();
        List<String> locationList = new ArrayList<>(CommonConstant.NUMBER_THREE);
        locationList.add(applyUserProvince);
        locationList.add(applyUserCity);
        locationList.add(applyUserRegion);
        PsBusLocationTblDO psBusLocationTblDO = new PsBusLocationTblDO();
        psBusLocationTblDO.setLocationList(locationList);
        Map<String, PsBusLocationTbl> psBusLocationTblMap = ambaseCommonService.selectPsBusLocationTblMap(psBusLocationTblDO);
        stockAssetsDemandCourierApproveDetailRespDTO.setReceiverMobile(stockAssetsDemandHead.getApplyUserPhone());
        stockAssetsDemandCourierApproveDetailRespDTO.setReceiverAddress(stockAssetsDemandHead.getApplyUserAddress());
        PsBusLocationTbl psBusLocationTbl = psBusLocationTblMap.get(applyUserProvince);
        if (psBusLocationTbl != null) {
            String descr = psBusLocationTbl.getDescr();
            // 如果是直辖市，需要把市字去掉
            if (descr.endsWith("市")) {
                descr = descr.substring(CommonConstant.NUMBER_ZERO, descr.length() - CommonConstant.NUMBER_ONE);
            }
            stockAssetsDemandCourierApproveDetailRespDTO.setReceiverProvince(descr);
        }
        psBusLocationTbl = psBusLocationTblMap.get(applyUserCity);
        if (psBusLocationTbl != null) {
            String descr = psBusLocationTbl.getDescr();
            String[] descrArray = descr.split(StringConstant.HYPHEN);
            // 这里只截取到市
            if (descrArray.length > CommonConstant.NUMBER_ONE) {
                descr = descrArray[CommonConstant.NUMBER_ONE];
            }
            stockAssetsDemandCourierApproveDetailRespDTO.setReceiverCity(descr);
        }
        psBusLocationTbl = psBusLocationTblMap.get(applyUserRegion);
        if (psBusLocationTbl != null) {
            String descr = psBusLocationTbl.getDescr();
            String[] descrArray = descr.split(StringConstant.HYPHEN);
            // 这里只截取到县或者区
            if (descrArray.length == CommonConstant.NUMBER_TWO) {
                descr = descrArray[CommonConstant.NUMBER_ONE];
            }else if(descrArray.length > CommonConstant.NUMBER_TWO){
                descr = descrArray[CommonConstant.NUMBER_TWO];
            }
            stockAssetsDemandCourierApproveDetailRespDTO.setReceiverDistrict(getMappingRegion(descr, regionMap));
        }
    }

    /**
     * @param: region, regionMap
     * @description: 设置映射区域信息
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/7/5
     */
    private String getMappingRegion(String region, Map<String, String> regionMap) {
        if (MapUtils.isEmpty(regionMap)) {
            return region;
        }
        String target = regionMap.get(region);
        if (StringUtils.isBlank(target)) {
            return region;
        }
        return target;
    }

    /**
     * @param: stockAssetsDemandAssetsList, stockAssetsDemandCourierApproveDetailRespDTO
     * @description: 设置快递员审批行信息
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/6/26
     */
    public void setStockAssetsDemandCourierApproveDetailRespDTOLineData(List<StockAssetsDemandAssets> stockAssetsDemandAssetsList, StockAssetsDemandCourierApproveDetailRespDTO stockAssetsDemandCourierApproveDetailRespDTO) {
        if (CollectionUtils.isEmpty(stockAssetsDemandAssetsList)) {
            return;
        }
        List<String> assetsCodeList = stockAssetsDemandAssetsList.stream().map(StockAssetsDemandAssets::getAssetsCode).collect(Collectors.toList());
        // 根据资产分类聚合，统计发货数量
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        Map<String, StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsCategoryLineResDTO> deliveryAssetsCategoryLineResDTOMap = new HashMap<>();
        List<StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsCategoryLineResDTO> deliveryAssetsCategoryLineResDTOList = new ArrayList<>();
        // 根据快递单号聚合，统计已经打包的数量，同时把根据快递单号统计的发货资产信息，放入一个list中返回
        Map<String, StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsLineResDTO> deliveryAssetsLineResDTOMap = new HashMap<>();
        List<StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsLineResDTO> deliveryAssetsLineResDTOList = new ArrayList<>();
        for (StockAssetsDemandAssets stockAssetsDemandAssets : stockAssetsDemandAssetsList) {
            StockAssets stockAssets = stockAssetsMap.get(stockAssetsDemandAssets.getAssetsCode());
            String categoryCode = stockAssets.getCategoryCode();
            StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsCategoryLineResDTO deliveryAssetsCategoryLineResDTO = deliveryAssetsCategoryLineResDTOMap.get(categoryCode);
            String trackingNumber = stockAssetsDemandAssets.getTrackingNumber();
            // 设置发货数量
            if (null == deliveryAssetsCategoryLineResDTO) {
                deliveryAssetsCategoryLineResDTO = new StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsCategoryLineResDTO();
                deliveryAssetsCategoryLineResDTO.setNeedSendQuantity(CommonConstant.NUMBER_ONE);
                deliveryAssetsCategoryLineResDTO.setHaveSendQuantity(CommonConstant.NUMBER_ZERO);
                deliveryAssetsCategoryLineResDTO.setCategoryCode(categoryCode);
                deliveryAssetsCategoryLineResDTO.setCategory(stockAssets.getCategory());
                deliveryAssetsCategoryLineResDTOMap.put(categoryCode, deliveryAssetsCategoryLineResDTO);
                deliveryAssetsCategoryLineResDTOList.add(deliveryAssetsCategoryLineResDTO);
            } else {
                deliveryAssetsCategoryLineResDTO.setNeedSendQuantity(deliveryAssetsCategoryLineResDTO.getNeedSendQuantity() + CommonConstant.NUMBER_ONE);
            }
            // 设置已下单数量
            if (StringUtils.isNotBlank(trackingNumber)) {
                deliveryAssetsCategoryLineResDTO.setHaveSendQuantity(deliveryAssetsCategoryLineResDTO.getHaveSendQuantity() + CommonConstant.NUMBER_ONE);
                // 设置已经下单的资产信息
                AssetsDTO assetsDTO = new AssetsDTO();
                BeanUtils.copyProperties(stockAssets, assetsDTO);
                StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsLineResDTO deliveryAssetsLineResDTO = deliveryAssetsLineResDTOMap.get(trackingNumber);
                if(null == deliveryAssetsLineResDTO){
                    deliveryAssetsLineResDTO = new StockAssetsDemandCourierApproveDetailRespDTO.DeliveryAssetsLineResDTO();
                    deliveryAssetsLineResDTO.setTrackingNumber(trackingNumber);
                    List<AssetsDTO> assetsDTOList = new ArrayList<>();
                    assetsDTOList.add(assetsDTO);
                    deliveryAssetsLineResDTO.setAssetsDTOList(assetsDTOList);
                    deliveryAssetsLineResDTOMap.put(trackingNumber, deliveryAssetsLineResDTO);
                    deliveryAssetsLineResDTOList.add(deliveryAssetsLineResDTO);
                }else {
                    deliveryAssetsLineResDTO.getAssetsDTOList().add(assetsDTO);
                }
            }
        }
        stockAssetsDemandCourierApproveDetailRespDTO.setDeliveryAssetsCategoryLineResDTOList(deliveryAssetsCategoryLineResDTOList);
        stockAssetsDemandCourierApproveDetailRespDTO.setDeliveryAssetsLineResDTOList(deliveryAssetsLineResDTOList);
    }

    /**
     * @param: stockAssetsDemandCourierCreateOrderReqDTO, ztoTransportOrderReqDTO
     * @param: updateStockAssetsDemandAssetsList, otherStockAssetsDemandAssetsList
     * @param: stockDeliveryPlanHead, inOrderStockAssetsList
     * @description: 检查快递员创建订单参数
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/6/27
     */
    public String checkCreateOrderByCourierParam(StockAssetsDemandCourierCreateOrderReqDTO stockAssetsDemandCourierCreateOrderReqDTO, ZtoTransportOrderReqDTO ztoTransportOrderReqDTO,
                                                 List<StockAssetsDemandAssets> updateStockAssetsDemandAssetsList, List<StockAssetsDemandAssets> otherStockAssetsDemandAssetsList,
                                                 StockDeliveryPlanHead stockDeliveryPlanHead, List<StockAssets> inOrderStockAssetsList) {
        String deliveryPlanNo = stockAssetsDemandCourierCreateOrderReqDTO.getDeliveryPlanNo();
        String senderName = stockAssetsDemandCourierCreateOrderReqDTO.getSenderName();
        if (StringUtils.isBlank(senderName)) {
            return "寄件人姓名不能为空";
        }
        String senderMobile = stockAssetsDemandCourierCreateOrderReqDTO.getSenderMobile();
        if (StringUtils.isBlank(senderMobile)) {
            return "寄件人手机号不能为空";
        }
        if (!RegexCheckUtil.isMobile(senderMobile)) {
            return "寄件人手机号不合法";
        }
        String senderProvince = stockAssetsDemandCourierCreateOrderReqDTO.getSenderProvince();
        if (StringUtils.isBlank(senderProvince)) {
            return "寄件人省不能为空";
        }
        String senderCity = stockAssetsDemandCourierCreateOrderReqDTO.getSenderCity();
        if (StringUtils.isBlank(senderCity)) {
            return "寄件人市不能为空";
        }
        String senderDistrict = stockAssetsDemandCourierCreateOrderReqDTO.getSenderDistrict();
        if (StringUtils.isBlank(senderDistrict)) {
            return "寄件人区不能为空";
        }
        String senderAddress = stockAssetsDemandCourierCreateOrderReqDTO.getSenderAddress();
        if (StringUtils.isBlank(senderAddress)) {
            return "寄件人详细地址不能为空";
        }
        String receiverName = stockAssetsDemandCourierCreateOrderReqDTO.getReceiverName();
        if (StringUtils.isBlank(receiverName)) {
            return "收件人姓名不能为空";
        }
        String receiverMobile = stockAssetsDemandCourierCreateOrderReqDTO.getReceiverMobile();
        if (StringUtils.isBlank(receiverMobile)) {
            return "收件人手机号不能为空";
        }
        if (!RegexCheckUtil.isMobile(receiverMobile)) {
            return "收件人手机号不合法";
        }
        String receiverProvince = stockAssetsDemandCourierCreateOrderReqDTO.getReceiverProvince();
        if (StringUtils.isBlank(receiverProvince)) {
            return "收件人省不能为空";
        }
        String receiverCity = stockAssetsDemandCourierCreateOrderReqDTO.getReceiverCity();
        if (StringUtils.isBlank(receiverCity)) {
            return "收件人市不能为空";
        }
        String receiverDistrict = stockAssetsDemandCourierCreateOrderReqDTO.getReceiverDistrict();
        if (StringUtils.isBlank(receiverDistrict)) {
            return "收件人区不能为空";
        }
        String receiverAddress = stockAssetsDemandCourierCreateOrderReqDTO.getReceiverAddress();
        if (StringUtils.isBlank(receiverAddress)) {
            return "收件人详细地址不能为空";
        }
        // 查询计划领用单，判断计划领用单是否存在
        StockDeliveryPlanHeadDO stockDeliveryPlanHeadDO = new StockDeliveryPlanHeadDO();
        stockDeliveryPlanHeadDO.setDeliveryPlanNo(deliveryPlanNo);
        StockDeliveryPlanHead searchStockDeliveryPlanHead = stockDeliveryPlanHeadService.selectOne(stockDeliveryPlanHeadDO);
        if (null == searchStockDeliveryPlanHead) {
            return "领用单不存在";
        }
        StockAssetsDemandAssetsReqDO stockAssetsDemandAssetsReqDO = new StockAssetsDemandAssetsReqDO();
        stockAssetsDemandAssetsReqDO.setDeliveryPlanNo(deliveryPlanNo);
        List<StockAssetsDemandAssets> allStockAssetsDemandAssetsList = stockAssetsDemandAssetsService.selectList(stockAssetsDemandAssetsReqDO);
        if (CollectionUtils.isEmpty(allStockAssetsDemandAssetsList)) {
            return "需求单资产信息不存在";
        }
        StockAssetsDemandAssets stockAssetsDemandAssets = allStockAssetsDemandAssetsList.get(CommonConstant.NUMBER_ZERO);
        Integer deliveryMethod = stockAssetsDemandAssets.getDeliveryMethod();
        if (!StockAssetsDemandAssetsEnum.DeliveryMethod.SEND.getCode().equals(deliveryMethod)) {
            return "领用单的领用方式不是邮寄，单据有误";
        }
        // 获取快递单号set
        Set<String> trackingNumberSet = allStockAssetsDemandAssetsList.stream().map(StockAssetsDemandAssets::getTrackingNumber).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        StockDeliveryReqDO stockDeliveryReqDO = new StockDeliveryReqDO();
        stockDeliveryReqDO.setDeliveryPlanHeadId(searchStockDeliveryPlanHead.getDeliveryPlanHeadId());
        // 根据计划单头id，查询实际出库单头信息
        StockDelivery stockDelivery = stockDeliveryService.selectOne(stockDeliveryReqDO);
        if (null == stockDelivery) {
            return "实际出库单不存在";
        }
        String billingUser = searchStockDeliveryPlanHead.getBillingUser();
        stockDeliveryPlanHead.setBillingUser(billingUser);
        ztoTransportOrderReqDTO.setSenderCode(billingUser);
        ztoTransportOrderReqDTO.setSenderName(senderName);
        ztoTransportOrderReqDTO.setSenderMobile(senderMobile);
        ztoTransportOrderReqDTO.setSenderProvince(senderProvince);
        ztoTransportOrderReqDTO.setSenderCity(senderCity);
        ztoTransportOrderReqDTO.setSenderDistrict(senderDistrict);
        ztoTransportOrderReqDTO.setSenderAddress(senderAddress);
        ztoTransportOrderReqDTO.setReceiverName(receiverName);
        ztoTransportOrderReqDTO.setReceiverMobile(receiverMobile);
        ztoTransportOrderReqDTO.setReceiverProvince(receiverProvince);
        ztoTransportOrderReqDTO.setReceiverCity(receiverCity);
        ztoTransportOrderReqDTO.setReceiverDistrict(receiverDistrict);
        ztoTransportOrderReqDTO.setReceiverAddress(receiverAddress);
        ztoTransportOrderReqDTO.setOrderType(ZtoExpressEnum.OrderType.FULL_NETWORK_ORDER.getCode());
        ztoTransportOrderReqDTO.setBizNo(deliveryPlanNo + (trackingNumberSet.size() + CommonConstant.NUMBER_ONE));
        // 获取制单人的职场地编码
        SysUserReqDO sysUserReqDO = new SysUserReqDO();
        SysUser sysUser = ambaseCommonService.selectUserOne(sysUserReqDO);
        if(sysUser != null){
            ztoTransportOrderReqDTO.setSenderWorkplaceLocationCode(sysUser.getEntryLocation());
        }
        return checkCreateOrderByCourierParamLine(stockAssetsDemandCourierCreateOrderReqDTO, updateStockAssetsDemandAssetsList,
                stockDelivery, ztoTransportOrderReqDTO, otherStockAssetsDemandAssetsList, allStockAssetsDemandAssetsList, inOrderStockAssetsList);
    }

    /**
     * @param: stockAssetsDemandCourierCreateOrderReqDTO, updateStockDeliveryDetailAssetList
     * @param: stockDelivery, ztoTransportOrderReqDTO
     * @param: otherStockAssetsDemandAssetsList, allStockAssetsDemandAssetsList
     * @description: 检查快递员创建订单行信息
     * @return:
     * @author: <EMAIL>
     * @date: 2023/6/27
     */
    private String checkCreateOrderByCourierParamLine(StockAssetsDemandCourierCreateOrderReqDTO stockAssetsDemandCourierCreateOrderReqDTO, List<StockAssetsDemandAssets> updateStockAssetsDemandAssetsList,
                                                      StockDelivery stockDelivery, ZtoTransportOrderReqDTO ztoTransportOrderReqDTO, List<StockAssetsDemandAssets> otherStockAssetsDemandAssetsList,
                                                      List<StockAssetsDemandAssets> allStockAssetsDemandAssetsList, List<StockAssets> inOrderStockAssetsList) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        Date currentTime = new Date();
        List<AssetsReqDTO> assetsReqDTOList = stockAssetsDemandCourierCreateOrderReqDTO.getAssetsReqDTOList();
        if (CollectionUtils.isEmpty(assetsReqDTOList)) {
            return "下单的资产明细信息不能为空";
        }
        Set<String> assetsCodeSet = new HashSet<>();
        int assetsReqDTOListLength = assetsReqDTOList.size();
        for (int i = 0; i < assetsReqDTOListLength; i++) {
            AssetsReqDTO assetsReqDTO = assetsReqDTOList.get(i);
            String assetsCode = assetsReqDTO.getAssetsCode();
            if (StringUtils.isBlank(assetsCode)) {
                return "第" + (i + 1) + "行的资产编码不能为空";
            }
            if (!assetsCodeSet.add(assetsCode)) {
                return "第" + (i + 1) + "行的资产编码不能重复";
            }
        }
        List<String> assetsCodeList = new ArrayList<>(assetsCodeSet);
        // 查询资产信息
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        Map<String, Integer> categoryCodeAndNumberMap = new HashMap<>();
        Map<String, String> categoryCodeAndNameMap = new HashMap<>();
        Map<String, StockAssetsDemandAssets> stockAssetsDemandAssetsMap = allStockAssetsDemandAssetsList.stream().collect(Collectors.toMap(StockAssetsDemandAssets::getAssetsCode, stockAssetsDemandAssets -> stockAssetsDemandAssets, (k1, k2) -> k2));
        for (int i = 0; i < assetsReqDTOListLength; i++) {
            AssetsReqDTO assetsReqDTO = assetsReqDTOList.get(i);
            String assetsCode = assetsReqDTO.getAssetsCode();
            StockAssetsDemandAssets stockAssetsDemandAssets = stockAssetsDemandAssetsMap.remove(assetsCode);
            // 判断下单的资产行信息是否在本单据中
            if (null == stockAssetsDemandAssets) {
                return "第" + (i + 1) + "行的资产编码不在此单据中";
            }
            // 判断资产是否已经下单过
            if (StringUtils.isNotBlank(stockAssetsDemandAssets.getTrackingNumber())) {
                return "第" + (i + 1) + "行的资产编码已经下单过，不可以重复下单";
            }
            StockAssetsDemandAssets updateStockAssetsDemandAssets = new StockAssetsDemandAssets();
            updateStockAssetsDemandAssets.setId(stockAssetsDemandAssets.getId());
            updateStockAssetsDemandAssets.setUpdatedAt(currentTime);
            updateStockAssetsDemandAssets.setUpdatedBy(jwtUser.getEmployeeCode());
            updateStockAssetsDemandAssetsList.add(updateStockAssetsDemandAssets);
            // 获取资产分类
            StockAssets stockAssets = stockAssetsMap.get(assetsCode);
            inOrderStockAssetsList.add(stockAssets);
            String categoryCode = stockAssets.getCategoryCode();
            Integer number = categoryCodeAndNumberMap.get(categoryCode);
            if (null == number) {
                categoryCodeAndNumberMap.put(categoryCode, CommonConstant.NUMBER_ONE);
                categoryCodeAndNameMap.put(categoryCode, stockAssets.getCategory());
            } else {
                categoryCodeAndNumberMap.put(categoryCode, number + CommonConstant.NUMBER_ONE);
            }
        }
        StringBuilder remarkBuilder = new StringBuilder();
        StringBuilder goodsNameBuilder = new StringBuilder();
        for (Map.Entry<String, Integer> categoryCodeAndNumber : categoryCodeAndNumberMap.entrySet()) {
            String categoryName = categoryCodeAndNameMap.get(categoryCodeAndNumber.getKey());
            goodsNameBuilder.append(categoryName).append(StringConstant.DUN_HAO);
            remarkBuilder.append(categoryName).append(categoryCodeAndNumber.getValue()).append("个").append("\n");
        }
        ztoTransportOrderReqDTO.setRemark(remarkBuilder.toString());
        ztoTransportOrderReqDTO.setGoodsName(goodsNameBuilder.substring(CommonConstant.NUMBER_ZERO, goodsNameBuilder.length() - CommonConstant.NUMBER_ONE));
        // 设置其他的行信息
        otherStockAssetsDemandAssetsList.addAll(stockAssetsDemandAssetsMap.values());
        return StringConstant.EMPTY;
    }

    /**
     * @param: ztoTransportOrderReqDTO
     * @description: 调用物流系统下单
     * @return: TransportOrderRespDTO
     * @author: <EMAIL>
     * @date: 2023/6/27
     */
    public TransportOrderRespDTO callTransportSystemCreateOrder(ZtoTransportOrderReqDTO ztoTransportOrderReqDTO) throws Exception {
        // 调用物流系统下单
        com.gz.eim.am.transport.dto.ResponseData<TransportOrderRespDTO> responseData;
        try {
            responseData = ztoDeliveryApi.createOrder(ztoTransportOrderReqDTO);
        } catch (Exception e) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.callTransportSystemCreateOrder调用物流系统下单失败，请求参数为：ztoTransportOrderReqDTO:{}", JSON.toJSONString(ztoTransportOrderReqDTO));
            throw e;
        }
        if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.callTransportSystemCreateOrder调用物流系统下单失败，请求参数为：ztoTransportOrderReqDTO:{},返回信息为：responseData:{}", JSON.toJSONString(ztoTransportOrderReqDTO), JSON.toJSONString(responseData));
            return null;
        }
        TransportOrderRespDTO transportOrderRespDTO = responseData.getData();
        String wayBillCode = transportOrderRespDTO.getWayBillCode();
        if (StringUtils.isBlank(wayBillCode)) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.callTransportSystemCreateOrder调用物流系统下单失败，请求参数为：ztoTransportOrderReqDTO:{},返回信息为：responseData:{}", JSON.toJSONString(ztoTransportOrderReqDTO), JSON.toJSONString(responseData));
            return null;
        }
        // 调用物流系统进行快递单打印
        ZtoBatchCloudPrintReqDTO ztoBatchCloudPrintReqDTO = new ZtoBatchCloudPrintReqDTO();
        ztoBatchCloudPrintReqDTO.setRepetition(CommonConstant.NUMBER_ONE);
        ztoBatchCloudPrintReqDTO.setWayBillCode(wayBillCode);
        responseData = ztoDeliveryApi.batchCloudPrint(ztoBatchCloudPrintReqDTO);
        if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.callTransportSystemCreateOrder调用物流系统打印失败，请求参数为：ztoBatchCloudPrintReqDTO:{},返回信息为：responseData:{}", JSON.toJSONString(ztoBatchCloudPrintReqDTO), JSON.toJSONString(responseData));
            return null;
        }
        return transportOrderRespDTO;
    }

    /**
     * @param: deliveryPlanNo, stockDeliveryDetailAssetList
     * @param: stockDeliveryPlanHead, stockAssetsDemandHead
     * @param: stockAssetsDemandAssetsList
     * @description: 判断参数是否合法同时设置实际出库单集合
     * @return: String
     * @author: <EMAIL>
     * @date: 2023/6/28
     */
    public String judgeParamAndSetStockDeliveryDetailAssetList(String deliveryPlanNo, StockDeliveryPlanHead stockDeliveryPlanHead, StockAssetsDemandHead stockAssetsDemandHead,
                                                               List<StockAssetsDemandAssets> stockAssetsDemandAssetsList) {
        // 查询计划领用头单，判断领用头单是否存在
        StockDeliveryPlanHeadDO stockDeliveryPlanHeadDO = new StockDeliveryPlanHeadDO();
        stockDeliveryPlanHeadDO.setDeliveryPlanNo(deliveryPlanNo);
        StockDeliveryPlanHead searchStockDeliveryPlanHead = stockDeliveryPlanHeadService.selectOne(stockDeliveryPlanHeadDO);
        if (null == searchStockDeliveryPlanHead) {
            return "领用单不存在";
        }
        if (stockDeliveryPlanHead != null) {
            BeanUtils.copyProperties(searchStockDeliveryPlanHead, stockDeliveryPlanHead);
        }
        StockAssetsDemandAssetsReqDO stockAssetsDemandAssetsReqDO = new StockAssetsDemandAssetsReqDO();
        stockAssetsDemandAssetsReqDO.setDeliveryPlanNo(deliveryPlanNo);
        List<StockAssetsDemandAssets> searchStockAssetsDemandAssetsList = stockAssetsDemandAssetsService.selectList(stockAssetsDemandAssetsReqDO);
        if (CollectionUtils.isEmpty(searchStockAssetsDemandAssetsList)) {
            return "需求单资产信息不存在";
        }
        StockAssetsDemandAssets stockAssetsDemandAssets = searchStockAssetsDemandAssetsList.get(CommonConstant.NUMBER_ZERO);
        Integer deliveryMethod = stockAssetsDemandAssets.getDeliveryMethod();
        if (!StockAssetsDemandAssetsEnum.DeliveryMethod.SEND.getCode().equals(deliveryMethod)) {
            return "领用单的领用方式不是邮寄，单据有误";
        }
        if (stockAssetsDemandAssetsList != null) {
            stockAssetsDemandAssetsList.addAll(searchStockAssetsDemandAssetsList);
        }
        StockAssetsDemandHeadReqDTO stockAssetsDemandHeadReqDTO = new StockAssetsDemandHeadReqDTO();
        stockAssetsDemandHeadReqDTO.setDemandNo(searchStockDeliveryPlanHead.getBizNo());
        StockAssetsDemandHead searchStockAssetsDemandHead = stockPlanAssetsDemandHeadService.getStockAssetsDemandHead(stockAssetsDemandHeadReqDTO);
        if (null == searchStockAssetsDemandHead) {
            return "需求单不存在";
        }
        if (stockAssetsDemandHead != null) {
            BeanUtils.copyProperties(searchStockAssetsDemandHead, stockAssetsDemandHead);
        }
        return StringConstant.EMPTY;
    }

    /**
     * @param: stockAssetsDemandAssetsList
     * @description: 查询物流轨迹信息
     * @return: List<StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO>
     * @author: <EMAIL>
     * @date: 2023/6/28
     */
    public List<StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO> queryTrackList(List<StockAssetsDemandAssets> stockAssetsDemandAssetsList) {
        if (CollectionUtils.isEmpty(stockAssetsDemandAssetsList)) {
            return new ArrayList<>();
        }
        Map<String, List<String>> trackingNumberAndAssetsCodeListMap = new HashMap<>();
        List<String> assetsCodeList = new ArrayList<>();
        getTrackingMap(stockAssetsDemandAssetsList, trackingNumberAndAssetsCodeListMap, assetsCodeList);
        // 如果不存在直接返回
        if (MapUtils.isEmpty(trackingNumberAndAssetsCodeListMap)) {
            return new ArrayList<>();
        }
        // 调用物流系统查询轨迹信息，如果不存在直接返回
        List<TransportTrackLineRespDTO> transportTrackLineRespDTOList = callTransportSystemQueryTrackList(new ArrayList<>(trackingNumberAndAssetsCodeListMap.keySet()));
        if (CollectionUtils.isEmpty(transportTrackLineRespDTOList)) {
            return new ArrayList<>();
        }
        // 获取封装好的物流轨迹信息
        return getTransportTrackDetailResDTOList(transportTrackLineRespDTOList, assetsCodeList, trackingNumberAndAssetsCodeListMap);
    }

    /**
     * @param: transportTrackRespDTOList, assetsCodeList
     * @param: trackingNumberAndAssetsCodeListMap
     * @description: 获取封装好的物流轨迹信息
     * @return: List<StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO>
     * @author: <EMAIL>
     * @date: 2023/6/28
     */
    private List<StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO> getTransportTrackDetailResDTOList(List<TransportTrackLineRespDTO> transportTrackLineRespDTOList, List<String> assetsCodeList,
                                                                                                                            Map<String, List<String>> trackingNumberAndAssetsCodeListMap) {
        // 如果存在就根据上面的资产查询出来的所有资产的Map集合
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
        List<StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO> transportTrackDetailResDTOList = new ArrayList<>(transportTrackLineRespDTOList.size());
        // 遍历物流轨迹，封装返回值，同时获取每个轨迹的资产集合信息，拼接资产信息
        for (TransportTrackLineRespDTO transportTrackLineRespDTO : transportTrackLineRespDTOList) {
            StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO transportTrackDetailResDTO = new StockAssetsDemandTransportTrackDetailRespDTO.TransportTrackDetailResDTO();
            BeanUtils.copyProperties(transportTrackLineRespDTO, transportTrackDetailResDTO);
            String wayBillCode = transportTrackLineRespDTO.getWayBillCode();
            transportTrackDetailResDTO.setTrackingNumber(wayBillCode);
            transportTrackDetailResDTOList.add(transportTrackDetailResDTO);
            // 获取资产信息
            List<String> tempAssetsCodeList = trackingNumberAndAssetsCodeListMap.get(wayBillCode);
            if (CollectionUtils.isNotEmpty(tempAssetsCodeList)) {
                Map<String, String> categoryAndName = new HashMap<>();
                Map<String, Integer> categoryAndNumber = new HashMap<>();
                Map<String, StringBuilder> categoryAndAssetsMap = new HashMap<>();
                for (String tempAssetsCode : tempAssetsCodeList) {
                    StockAssets stockAssets = stockAssetsMap.get(tempAssetsCode);
                    if (null == stockAssets) {
                        continue;
                    }
                    String categoryCode = stockAssets.getCategoryCode();
                    Integer number = categoryAndNumber.get(categoryCode);
                    if (null == number) {
                        categoryAndNumber.put(categoryCode, CommonConstant.NUMBER_ONE);
                        categoryAndName.put(categoryCode, stockAssets.getCategory());
                        StringBuilder assetsBuilder = new StringBuilder();
                        assetsBuilder.append(tempAssetsCode).append("\n");
                        categoryAndAssetsMap.put(categoryCode, assetsBuilder);
                    } else {
                        categoryAndNumber.put(categoryCode, number + CommonConstant.NUMBER_ONE);
                        categoryAndAssetsMap.get(categoryCode).append(tempAssetsCode).append("\n");
                    }
                }
                StringBuilder remarkBuilder = new StringBuilder();
                for (Map.Entry<String, String> categoryAndNameEntry : categoryAndName.entrySet()) {
                    String categoryCode = categoryAndNameEntry.getKey();
                    StringBuilder assetsNameBuilder = categoryAndAssetsMap.get(categoryCode);
                    String assetsNames = assetsNameBuilder.substring(CommonConstant.NUMBER_ZERO, assetsNameBuilder.length() - CommonConstant.NUMBER_ONE);
                    remarkBuilder.append(categoryAndNameEntry.getValue())
                            .append(StringConstant.ASTERISK)
                            .append(categoryAndNumber.get(categoryCode))
                            .append(StringConstant.CHINESE_COLON)
                            .append(assetsNames)
                            .append("\n");
                }
                transportTrackDetailResDTO.setRemark(remarkBuilder.toString());
            }
        }
        return transportTrackDetailResDTOList;
    }

    /**
     * @param: stockAssetsDemandAssetsList, trackingNumberAndAssetsCodeListMap
     * @param: assetsCodeList
     * @description: 获取轨迹map
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/6/28
     */
    private void getTrackingMap(List<StockAssetsDemandAssets> stockAssetsDemandAssetsList, Map<String, List<String>> trackingNumberAndAssetsCodeListMap,
                                List<String> assetsCodeList) {
        // 遍历所有实际的出库单资产行信息，获取快递单号，同时根据快递单号把不同的资产编码group by，没有直接返回
        for (StockAssetsDemandAssets stockAssetsDemandAssets : stockAssetsDemandAssetsList) {
            String trackingNumber = stockAssetsDemandAssets.getTrackingNumber();
            if (StringUtils.isBlank(trackingNumber)) {
                continue;
            }
            List<String> tempAssetsCodeList = trackingNumberAndAssetsCodeListMap.get(trackingNumber);
            if (CollectionUtils.isEmpty(tempAssetsCodeList)) {
                tempAssetsCodeList = new ArrayList<>();
                trackingNumberAndAssetsCodeListMap.put(trackingNumber, tempAssetsCodeList);
            }
            String assetsCode = stockAssetsDemandAssets.getAssetsCode();
            tempAssetsCodeList.add(assetsCode);
            assetsCodeList.add(assetsCode);
        }
    }

    /**
     * @param: trackNumberList
     * @description: 调用物流系统查询轨迹信息
     * @return: List<TransportTrackLineRespDTO>
     * @author: <EMAIL>
     * @date: 2023/6/28
     */
    private List<TransportTrackLineRespDTO> callTransportSystemQueryTrackList(List<String> trackingNumberList) {
        // 调用物流系统查询轨迹信息
        com.gz.eim.am.transport.dto.ResponseData<List<TransportTrackLineRespDTO>> responseData;
        try {
            responseData = ztoDeliveryApi.queryTrackList(trackingNumberList);
        } catch (Exception e) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.callTransportSystemQueryTrackList查询物流轨迹失败，请求参数为：trackingNumberList:{}，错误信息为：{}", JSON.toJSONString(trackingNumberList), e.getMessage());
            throw e;
        }
        if (!ResponseCode.SUCCESS.getCode().equals(responseData.getCode())) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.callTransportSystemQueryTrackList查询物流轨迹失败，请求参数为：trackingNumberList:{},返回信息为：responseData:{}", JSON.toJSONString(trackingNumberList), JSON.toJSONString(responseData));
            return null;
        }
        return responseData.getData();
    }

    public StockAssetsDemandTransportTrackDetailRespDTO getStockAssetsDemandTransportTrackDetailRespDTO(StockAssetsDemandHead stockAssetsDemandHead) {
        StockAssetsDemandTransportTrackDetailRespDTO stockAssetsDemandTransportTrackDetailRespDTO = new StockAssetsDemandTransportTrackDetailRespDTO();
        StockAssetsDemandHeadRespDTO stockAssetsDemandHeadRespDTO = prepareStockAssetsDemandHeadRespDTO(stockAssetsDemandHead, null);
        stockAssetsDemandTransportTrackDetailRespDTO.setReceiverName(stockAssetsDemandHeadRespDTO.getApplyUserName());
        stockAssetsDemandTransportTrackDetailRespDTO.setReceiverMobile(stockAssetsDemandHeadRespDTO.getApplyUserPhone());
        stockAssetsDemandTransportTrackDetailRespDTO.setReceiverProvince(stockAssetsDemandHeadRespDTO.getApplyUserProvinceDesc());
        stockAssetsDemandTransportTrackDetailRespDTO.setReceiverCity(stockAssetsDemandHeadRespDTO.getApplyUserCityDesc());
        stockAssetsDemandTransportTrackDetailRespDTO.setReceiverDistrict(stockAssetsDemandHeadRespDTO.getApplyUserRegionDesc());
        stockAssetsDemandTransportTrackDetailRespDTO.setReceiverAddress(stockAssetsDemandHeadRespDTO.getApplyUserAddress());
        return stockAssetsDemandTransportTrackDetailRespDTO;
    }

    /**
     * @param: stockAssetsDemandHead, demandItemNoList
     * @description: 拼装返回的头单信息
     * @return: StockAssetsDemandHeadRespDTO
     * @author: <EMAIL>
     * @date: 2021/10/12
     */
    public StockAssetsDemandHeadRespDTO prepareStockAssetsDemandHeadRespDTO(StockAssetsDemandHead stockAssetsDemandHead, List<String> demandItemNoList) {
        StockAssetsDemandHeadRespDTO stockAssetsDemandHeadRespDTO = new StockAssetsDemandHeadRespDTO();
        BeanUtils.copyProperties(stockAssetsDemandHead, stockAssetsDemandHeadRespDTO);
        // 获取省份和市的描述
        List<String> locationList = new ArrayList<>(CommonConstant.NUMBER_TWO);
        String applyUserProvince = stockAssetsDemandHead.getApplyUserProvince();
        if (StringUtils.isNotBlank(applyUserProvince)) {
            locationList.add(applyUserProvince);
        }
        String applyUserCity = stockAssetsDemandHead.getApplyUserCity();
        if (StringUtils.isNotBlank(applyUserCity)) {
            locationList.add(applyUserCity);
        }
        String applyUserRegion = stockAssetsDemandHead.getApplyUserRegion();
        if (StringUtils.isNotBlank(applyUserRegion)) {
            locationList.add(applyUserRegion);
        }
        if (CollectionUtils.isNotEmpty(locationList)) {
            PsBusLocationTblDO psBusLocationTblDO = new PsBusLocationTblDO();
            psBusLocationTblDO.setLocationList(locationList);
            Map<String, PsBusLocationTbl> psBusLocationTblMap = ambaseCommonService.selectPsBusLocationTblMap(psBusLocationTblDO);
            PsBusLocationTbl applyUserProvinceEntity = psBusLocationTblMap.get(applyUserProvince);
            if (applyUserProvinceEntity != null) {
                stockAssetsDemandHeadRespDTO.setApplyUserProvinceDesc(applyUserProvinceEntity.getDescr());
            }
            PsBusLocationTbl applyUserCityEntity = psBusLocationTblMap.get(applyUserCity);
            if (applyUserCityEntity != null) {
                stockAssetsDemandHeadRespDTO.setApplyUserCityDesc(applyUserCityEntity.getDescr());
            }
            PsBusLocationTbl applyUserRegionEntity = psBusLocationTblMap.get(applyUserRegion);
            if (applyUserRegionEntity != null) {
                stockAssetsDemandHeadRespDTO.setApplyUserRegionDesc(applyUserRegionEntity.getDescr());
            }
        }
        // 获取人员集合
        List<String> employeeCodeList = new ArrayList<>();
        // 如果集合不是空就根据集合去找到对应的领用人
        if (CollectionUtils.isNotEmpty(demandItemNoList)) {
            // 查询最后一条的数据
            StockDeliveryPlanLineInfo stockDeliveryPlanLineInfo = new StockDeliveryPlanLineInfo();
            stockDeliveryPlanLineInfo.setReceiveItemNos(demandItemNoList);
            stockDeliveryPlanLineInfo.setOrderByDesc(true);
            stockDeliveryPlanLineInfo.setLimit(CommonConstant.NUMBER_ONE);
            stockDeliveryPlanLineInfo.setStatus(DeliveryPlanLineEnum.Status.ALREADY_OUT.getCode());
            List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectByInfoParam(stockDeliveryPlanLineInfo);
            if (CollectionUtils.isNotEmpty(stockDeliveryPlanLineList)) {
                // 获取头单信息
                stockAssetsDemandHeadRespDTO.setReceiveTime(stockDeliveryPlanLineList.get(0).getUpdatedAt());
                StockDeliveryPlanHead stockDeliveryPlanHead = stockDeliveryPlanHeadService.selectDeliveryPlanById(stockDeliveryPlanLineList.get(0).getDeliveryPlanHeadId());
                stockAssetsDemandHeadRespDTO.setReceiveUser(stockDeliveryPlanHead.getReceiveUser());
                stockAssetsDemandHeadRespDTO.setRealReceiveWarehouseCode(stockDeliveryPlanHead.getOutWarehouseCode());
                employeeCodeList.add(stockDeliveryPlanHead.getReceiveUser());
            }
        }

        employeeCodeList.add(stockAssetsDemandHeadRespDTO.getApplyUser());
        employeeCodeList.add(stockAssetsDemandHeadRespDTO.getRealUseUser());
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(employeeCodeList);
        if (sysUserBasicInfoMap != null) {
            // 设置其他信息
            SysUserBasicInfo applyUser = sysUserBasicInfoMap.get(stockAssetsDemandHeadRespDTO.getApplyUser());
            if (applyUser != null) {
                stockAssetsDemandHeadRespDTO.setApplyUserName(applyUser.getName());
            }
            SysUserBasicInfo receiveUser = sysUserBasicInfoMap.get(stockAssetsDemandHeadRespDTO.getReceiveUser());
            if (receiveUser != null) {
                stockAssetsDemandHeadRespDTO.setReceiveUserName(receiveUser.getName());
            }
            SysUserBasicInfo realUseUser = sysUserBasicInfoMap.get(stockAssetsDemandHeadRespDTO.getRealUseUser());
            if (realUseUser != null) {
                stockAssetsDemandHeadRespDTO.setRealUseUserName(realUseUser.getName());
                stockAssetsDemandHeadRespDTO.setRealUseUserHpsJobcdDescr(realUseUser.getHpsJobcdDescr());
                // 查询实际使用人
                UserRespDTO userRespDTO = new UserRespDTO();
                BeanUtils.copyProperties(realUseUser, userRespDTO);
                userRespDTO.setEmpTypeName(SysUserEnum.empTypeMap.get(realUseUser.getEmpType()));
                stockAssetsDemandHeadRespDTO.setRealUseUserInfo(userRespDTO);
            }
        }
        // 获取领用仓库名称
        List<String> warehouseCode = new ArrayList<>();
        warehouseCode.add(stockAssetsDemandHeadRespDTO.getDefaultReceiveWarehouseCode());
        warehouseCode.add(stockAssetsDemandHeadRespDTO.getRealReceiveWarehouseCode());
        if (CollectionUtils.isNotEmpty(warehouseCode)) {
            Map<String, WarehouseRespDTO> warehouseRespDTOMap = stockWarehouseService.selectWarehouseDetailMapByCode(warehouseCode);
            if (warehouseRespDTOMap.get(stockAssetsDemandHeadRespDTO.getDefaultReceiveWarehouseCode()) != null) {
                stockAssetsDemandHeadRespDTO.setDefaultReceiveWarehouseName(warehouseRespDTOMap.get(stockAssetsDemandHeadRespDTO.getDefaultReceiveWarehouseCode()).getName());
            }
            if (warehouseRespDTOMap.get(stockAssetsDemandHeadRespDTO.getRealReceiveWarehouseCode()) != null) {
                stockAssetsDemandHeadRespDTO.setRealReceiveWarehouseName(warehouseRespDTOMap.get(stockAssetsDemandHeadRespDTO.getRealReceiveWarehouseCode()).getName());
            }
        }
        stockAssetsDemandHeadRespDTO.setReceiveReasonName(StockAssetsDemandHeadEnum.reasonMap.get(stockAssetsDemandHeadRespDTO.getReceiveReason()));
        return stockAssetsDemandHeadRespDTO;
    }

    /**
     * @param: stockAssetsDemandAssetsList, days
     * @description: 异步自动签收
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/7/7
     */
    public void automaticConfirmReceipt(List<StockAssetsDemandAssets> stockAssetsDemandAssetsList, Integer days) {
        List<List<StockAssetsDemandAssets>> stockAssetsDemandAssetsListList = new ArrayList<>();
        Map<String, List<StockAssetsDemandAssets>> demandNoAndStockAssetsDemandAssetsListMap = new HashMap<>();
        for (StockAssetsDemandAssets stockAssetsDemandAssets : stockAssetsDemandAssetsList) {
            String demandNo = stockAssetsDemandAssets.getDemandNo();
            List<StockAssetsDemandAssets> tempStockAssetsDemandAssetsList = demandNoAndStockAssetsDemandAssetsListMap.get(demandNo);
            if (CollectionUtils.isEmpty(tempStockAssetsDemandAssetsList)) {
                tempStockAssetsDemandAssetsList = new ArrayList<>();
                demandNoAndStockAssetsDemandAssetsListMap.put(demandNo, tempStockAssetsDemandAssetsList);
                stockAssetsDemandAssetsListList.add(tempStockAssetsDemandAssetsList);
            }
            tempStockAssetsDemandAssetsList.add(stockAssetsDemandAssets);
        }
        log.info("StockPlanAssetsDemandHeadServiceImpl.automaticConfirmReceipt开始执行自动签收逻辑，共需要处理计划单{} 条", stockAssetsDemandAssetsListList.size());
        int coreThreadNum = Runtime.getRuntime().availableProcessors();
        // 向上取整，保证用到核心线程数
        int size = (int) Math.ceil((double) stockAssetsDemandAssetsListList.size() / coreThreadNum);
        List<List<List<StockAssetsDemandAssets>>> stockAssetsDemandAssetsListListList = ListUtil.splitList(stockAssetsDemandAssetsListList, size);
        for (List<List<StockAssetsDemandAssets>> tempStockAssetsDemandAssetsListList : stockAssetsDemandAssetsListListList) {
//            StockThreadPool.getStockExecutor().execute(() -> {
                asyncCallTransportAutomaticConfirmReceipt(tempStockAssetsDemandAssetsListList, days);
//            });
        }
    }

    /**
     * @param: stockAssetsDemandAssetsList
     * @description: 异步调用物流系统自动签收
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/7/7
     */
    public void asyncCallTransportAutomaticConfirmReceipt(List<List<StockAssetsDemandAssets>> stockAssetsDemandAssetsListList, Integer days) {
        Map<String, List<StockAssetsDemandAssets>> deliveryNoAndStockAssetsDemandAssetsMap= new HashMap<>();
        // 按照计划单据进行重新分配
        for (List<StockAssetsDemandAssets> stockAssetsDemandAssetsList : stockAssetsDemandAssetsListList) {
            for (StockAssetsDemandAssets stockAssetsDemandAssets : stockAssetsDemandAssetsList) {
                String deliveryPlanNo = stockAssetsDemandAssets.getDeliveryPlanNo();
                List<StockAssetsDemandAssets> tempStockAssetsDemandAssetsList = deliveryNoAndStockAssetsDemandAssetsMap.get(deliveryPlanNo);
                if(CollectionUtils.isEmpty(tempStockAssetsDemandAssetsList)){
                    tempStockAssetsDemandAssetsList = new ArrayList<>();
                    deliveryNoAndStockAssetsDemandAssetsMap.put(deliveryPlanNo, tempStockAssetsDemandAssetsList);
                }
                tempStockAssetsDemandAssetsList.add(stockAssetsDemandAssets);
            }
        }
        Date currentDate = new Date();
        for (List<StockAssetsDemandAssets> stockAssetsDemandAssetsList : deliveryNoAndStockAssetsDemandAssetsMap.values()) {
            Map<String, List<StockAssetsDemandAssets>> trackingNumberAndStockAssetsDemandAssetsListMap = new HashMap<>();
            List<String> assetsCodeList = new ArrayList<>();
            for(StockAssetsDemandAssets stockAssetsDemandAssets : stockAssetsDemandAssetsList){
                String trackingNumber = stockAssetsDemandAssets.getTrackingNumber();
                if(StringUtils.isBlank(trackingNumber)){
                    continue;
                }
                List<StockAssetsDemandAssets> tempStockAssetsDemandAssetsList = trackingNumberAndStockAssetsDemandAssetsListMap.get(trackingNumber);
                if(CollectionUtils.isEmpty(tempStockAssetsDemandAssetsList)){
                    tempStockAssetsDemandAssetsList = new ArrayList<>();
                    trackingNumberAndStockAssetsDemandAssetsListMap.put(trackingNumber, tempStockAssetsDemandAssetsList);
                }
                tempStockAssetsDemandAssetsList.add(stockAssetsDemandAssets);
                assetsCodeList.add(stockAssetsDemandAssets.getAssetsCode());
            }
            if(CollectionUtils.isEmpty(assetsCodeList)){
                continue;
            }
            String demandNo = stockAssetsDemandAssetsList.get(CommonConstant.NUMBER_ZERO).getDemandNo();
            // 查询需求单行信息
            StockAssetsDemandLineReqDTO stockAssetsDemandLineReqDTO = new StockAssetsDemandLineReqDTO();
            stockAssetsDemandLineReqDTO.setDemandNo(demandNo);
            List<StockAssetsDemandLine> stockAssetsDemandLineList = stockPlanAssetsDemandLineService.getStockAssetsDemandLineList(stockAssetsDemandLineReqDTO);
            // 更新需求单行信息使用
            Set<String> updateStockAssetsDemandLineCategorySet = new HashSet<>(stockAssetsDemandLineList.size());
            List<StockAssetsDemandLine> updateStockAssetsDemandLineList = new ArrayList<>();
            Map<String, StockAssetsDemandLine> categoryCodeAndStockAssetsDemandLineMap = stockAssetsDemandLineList.stream().collect(Collectors.toMap(StockAssetsDemandLine :: getAssetsCategoryCode, stockAssetsDemandLine -> stockAssetsDemandLine, (k1, k2) -> k2));
            // 查询资产信息
            Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(assetsCodeList);
            // 调用物流系统查询运单
            List<String> trackingNumberList = new ArrayList<>(trackingNumberAndStockAssetsDemandAssetsListMap.keySet());
            List<TransportTrackLineRespDTO> transportTrackLineRespDTOList = callTransportSystemQueryTrackList(trackingNumberList);
            log.info("StockPlanAssetsDemandHeadServiceHelper.asyncCallTransportAutomaticConfirmReceipt快递单号：{}，查询到的快递信息为：{}", JSON.toJSONString(trackingNumberList), JSON.toJSONString(transportTrackLineRespDTOList));
            if (CollectionUtils.isEmpty(transportTrackLineRespDTOList)) {
                continue;
            }
            List<StockAssets> updateStockAssetsList = new ArrayList<>();
            List<StockAssetsDemandAssets> updateStockAssetsDemandAssetsList = new ArrayList<>();
            int signNumber = CommonConstant.NUMBER_ZERO;
            for (TransportTrackLineRespDTO transportTrackLineRespDTO : transportTrackLineRespDTOList) {
                // 判断是否为已签收，如果未签收不做操作
                if (!"签收".equals(transportTrackLineRespDTO.getScanType())) {
                    continue;
                }
                String scanTime = transportTrackLineRespDTO.getScanTime();
                if (StringUtils.isBlank(scanTime)) {
                    continue;
                }
                // 判断是否已经超时多少天，如果未超过直接返回
                try {
                    Date scanDate = DateUtils.dateParse(scanTime, DateUtils.DATE_PATTERN);
                    if (DateUtils.dateBetween(scanDate, currentDate) <= days) {
                        continue;
                    }
                } catch (Exception e) {
                    log.error("StockPlanAssetsDemandHeadServiceHelper.asyncCallTransportAutomaticConfirmReceipt解析时间格式异常,transportTrackLineRespDTO:{},message:{}", JSON.toJSONString(transportTrackLineRespDTO), e.getMessage());
                    continue;
                }
                signNumber++;
                String wayBillCode = transportTrackLineRespDTO.getWayBillCode();
                log.info("StockPlanAssetsDemandHeadServiceHelper.asyncCallTransportAutomaticConfirmReceipt已自动签收资产，快递单号为：{}", wayBillCode);
                // 如果已签收就去更新资产状态为使用中，未审批，同时更新资产需求行为已收货
                List<StockAssetsDemandAssets> tempStockAssetsDemandAssetsList = trackingNumberAndStockAssetsDemandAssetsListMap.get(wayBillCode);
                for (StockAssetsDemandAssets stockAssetsDemandAssets : tempStockAssetsDemandAssetsList) {
                    stockAssetsDemandAssets.setDeliveryStatus(StockAssetsDemandAssetsEnum.DeliveryStatus.SYSTEM_AUTOMATIC_DELIVERY.getCode());
                    stockAssetsDemandAssets.setUpdatedAt(currentDate);
                    updateStockAssetsDemandAssetsList.add(stockAssetsDemandAssets);
                    // 获取资产信息
                    StockAssets tempStockAssets = stockAssetsMap.get(stockAssetsDemandAssets.getAssetsCode());
                    StockAssets stockAssets = new StockAssets();
                    stockAssets.setAssetsId(tempStockAssets.getAssetsId());
                    stockAssets.setStatus(AssetsEnum.statusType.USED.getValue());
                    stockAssets.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
                    stockAssets.setUpdatedAt(currentDate);
                    updateStockAssetsList.add(stockAssets);
                    // 减去已发出数量，增加已领用数量
                    String categoryCode = tempStockAssets.getCategoryCode();
                    StockAssetsDemandLine stockAssetsDemandLine = categoryCodeAndStockAssetsDemandLineMap.get(categoryCode);
                    stockAssetsDemandLine.setSendQuantity(stockAssetsDemandLine.getSendQuantity().subtract(BigDecimal.ONE));
                    BigDecimal newReceiveQuantity = stockAssetsDemandLine.getReceiveQuantity().add(BigDecimal.ONE);
                    stockAssetsDemandLine.setReceiveQuantity(stockAssetsDemandLine.getReceiveQuantity().add(BigDecimal.ONE));
                    // 如果驳回数量等于已领用数量
                    if (stockAssetsDemandLine.getDemandQuantity().compareTo(newReceiveQuantity.add(stockAssetsDemandLine.getRejectQuantity())) == CommonConstant.NUMBER_ZERO) {
                        stockAssetsDemandLine.setStatus(StockAssetsDemandLineEnum.Status.DONE.getCode());
                    }
                    // 第一次修改需要加入到修改集合中去
                    if(updateStockAssetsDemandLineCategorySet.add(categoryCode)){
                        stockAssetsDemandLine.setUpdatedAt(currentDate);
                        updateStockAssetsDemandLineList.add(stockAssetsDemandLine);
                    }
                }
            }
            // 自动签收资产
            automaticSignAssets(signNumber, trackingNumberList.size(), updateStockAssetsList,
                    updateStockAssetsDemandAssetsList, updateStockAssetsDemandLineList, stockAssetsDemandLineList, demandNo);
        }
    }

    /**
     * @param: signNumber,allNeedSignNumber
     * @param: updateStockAssetsList,updateStockAssetsDemandAssetsList
     * @param: updateStockAssetsDemandLineList, stockAssetsDemandLineList
     * @param: demandNo
     * @description: 自动签收资产
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/7/11
     */
    private void automaticSignAssets(int signNumber, int allNeedSignNumber,
                                     List<StockAssets> updateStockAssetsList, List<StockAssetsDemandAssets> updateStockAssetsDemandAssetsList,
                                     List<StockAssetsDemandLine> updateStockAssetsDemandLineList, List<StockAssetsDemandLine> stockAssetsDemandLineList,
                                     String demandNo) {
        if (signNumber == CommonConstant.NUMBER_ZERO) {
            return;
        }
        // 手动开启事物
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            stockAssetsService.batchUpdate(updateStockAssetsList);
            stockAssetsDemandAssetsService.batchUpdate(updateStockAssetsDemandAssetsList);
            stockPlanAssetsDemandLineService.batchUpdate(updateStockAssetsDemandLineList);
            // 判断如果同一个领用单的所有需求单都为已收货，就结束掉单据代办
            if (signNumber == allNeedSignNumber) {
                // 结束通知领用/下单代办
                String deliveryPlanNo = updateStockAssetsDemandAssetsList.get(CommonConstant.NUMBER_ZERO).getDeliveryPlanNo();
                log.info("StockPlanAssetsDemandHeadServiceHelper.asyncCallTransportAutomaticConfirmReceipt已经自动结束资产自助领用的代办，领用单号为：{}", deliveryPlanNo);
                wflService.endWflBusiness(deliveryPlanNo, ApvTaskConstant.SUBMIT, FlowCodeEnum.ASSETS_SELF_HELP_RECEIVE_NOTICE, StringConstant.EMPTY);
                // 同时判断是否所有的需求单行都完成，如果都完成更新需求单头信息为已完成
                boolean allIsDone = true;
                for (StockAssetsDemandLine stockAssetsDemandLine : stockAssetsDemandLineList) {
                    if (!StockAssetsDemandLineEnum.Status.DONE.getCode().equals(stockAssetsDemandLine.getStatus())) {
                        allIsDone = false;
                        break;
                    }
                }
                if(allIsDone){
                    StockAssetsDemandHead stockAssetsDemandHead = new StockAssetsDemandHead();
                    stockAssetsDemandHead.setUpdatedAt(new Date());
                    stockAssetsDemandHead.setStatus(StockAssetsDemandHeadEnum.Status.DONE.getCode());
                    StockAssetsDemandHeadReqDO stockAssetsDemandHeadReqDO = new StockAssetsDemandHeadReqDO();
                    stockAssetsDemandHeadReqDO.setDemandNo(demandNo);
                    stockPlanAssetsDemandHeadService.updateByStockAssetsDemandHeadReqDO(stockAssetsDemandHead, stockAssetsDemandHeadReqDO);
                }
            }
            // 手动提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            log.error("StockPlanAssetsDemandHeadServiceHelper.asyncCallTransportAutomaticConfirmReceipt更新资产信息和需求单资产行信息失败，updateStockAssetsList:{},updateStockAssetsDemandAssetsList:{},message:{}", JSON.toJSONString(updateStockAssetsList), JSON.toJSONString(updateStockAssetsDemandAssetsList), e.getMessage());
            // 手动回滚事物
            platformTransactionManager.rollback(transactionStatus);
        }
    }

    /**
     * @param: updateStockAssetsDemandAssetsList,stockDeliveryPlanLineList
     * @description: 过滤掉已经签收的资产
     * @return: void
     * @author: <EMAIL>
     * @date: 2023/7/14
     */
    public void filterHaveSignAssets(List<StockAssetsDemandAssets> updateStockAssetsDemandAssetsList, List<StockDeliveryPlanLine> stockDeliveryPlanLineList) {
        // 过滤掉已签收的资产
        Map<String, Integer> assetsAndDeliveryStatusMap = updateStockAssetsDemandAssetsList.stream().collect(Collectors.toMap(StockAssetsDemandAssets :: getAssetsCode, StockAssetsDemandAssets :: getDeliveryStatus, (k1, k2) -> k2));
        Iterator<StockDeliveryPlanLine> stockDeliveryPlanLineListIterator = stockDeliveryPlanLineList.iterator();
        while (stockDeliveryPlanLineListIterator.hasNext()){
            StockDeliveryPlanLine stockDeliveryPlanLine = stockDeliveryPlanLineListIterator.next();
            Integer deliveryStatus = assetsAndDeliveryStatusMap.get(stockDeliveryPlanLine.getAssetsCode());
            if(!StockAssetsDemandAssetsEnum.DeliveryStatus.NOT_DELIVERY.getCode().equals(deliveryStatus)){
                stockDeliveryPlanLineListIterator.remove();
            }
        }
    }

    /**
     * @param: queryStockAssetsDemandAssetsListRespDOList
     * @description: 获取资产自助领用资产返回列表
     * @return: List<QueryStockAssetsDemandAssetsListRespDTO>
     * @author: <EMAIL>
     * @date: 2023/8/15
     */
    public List<QueryStockAssetsDemandAssetsListRespDTO> getQueryStockAssetsDemandAssetsListRespDTOList(List<QueryStockAssetsDemandAssetsListRespDO> queryStockAssetsDemandAssetsListRespDOList) throws Exception{
        // 获取资产需求运营人配置信息
        List<AssetsDemandOperationApproveUserConfig> assetsDemandOperationApproveUserConfigList = ambaseCommonService.selectMetaDataByParam(MetaDataConstants.STOCK_MODULE_ASSETS_DEMAND, MetaDataConstants.OPERATION_APPROVE_USER_CONFIG, AssetsDemandOperationApproveUserConfig.class);
        List<QueryStockAssetsDemandAssetsListRespDTO> queryStockAssetsDemandAssetsListRespDTOList = new ArrayList<>(queryStockAssetsDemandAssetsListRespDOList.size());
        Set<String> empIdSet = new HashSet<>();
        Set<String> locationSet = new HashSet<>();
        Set<String> assetsCodeSet = new HashSet<>();
        for (QueryStockAssetsDemandAssetsListRespDO queryStockAssetsDemandAssetsListRespDO : queryStockAssetsDemandAssetsListRespDOList) {
            empIdSet.add(queryStockAssetsDemandAssetsListRespDO.getClaimUser());
            empIdSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUser());
            empIdSet.add(queryStockAssetsDemandAssetsListRespDO.getRealUseUser());
            locationSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUserProvince());
            locationSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUserCity());
            locationSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUserRegion());
            assetsCodeSet.add(queryStockAssetsDemandAssetsListRespDO.getAssetsCode());
        }
        // 查询人员信息列表
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(empIdSet));
        // 查询地址列表
        PsBusLocationTblDO psBusLocationTblDO = new PsBusLocationTblDO();
        psBusLocationTblDO.setLocationList(new ArrayList<>(locationSet));
        Map<String, PsBusLocationTbl> psBusLocationTblMap = ambaseCommonService.selectPsBusLocationTblMap(psBusLocationTblDO);
        // 查询资产列表
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(new ArrayList<>(assetsCodeSet));
        for (QueryStockAssetsDemandAssetsListRespDO queryStockAssetsDemandAssetsListRespDO : queryStockAssetsDemandAssetsListRespDOList) {
            QueryStockAssetsDemandAssetsListRespDTO queryStockAssetsDemandAssetsListRespDTO = new QueryStockAssetsDemandAssetsListRespDTO();
            queryStockAssetsDemandAssetsListRespDTOList.add(queryStockAssetsDemandAssetsListRespDTO);
            BeanUtils.copyProperties(queryStockAssetsDemandAssetsListRespDO, queryStockAssetsDemandAssetsListRespDTO);
            // 设置领用日期
            Date holderTime = queryStockAssetsDemandAssetsListRespDO.getHolderTime();
            if(holderTime != null){
                queryStockAssetsDemandAssetsListRespDTO.setHolderTime(DateUtils.dateFormat(holderTime, DateUtils.DATE_PATTERN));
            }
            // 设置认领人信息
            SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(queryStockAssetsDemandAssetsListRespDO.getClaimUser());
            if(sysUserBasicInfo != null){
                queryStockAssetsDemandAssetsListRespDTO.setClaimUserName(sysUserBasicInfo.getName());
            }
            // 设置实际领用人信息
            sysUserBasicInfo = sysUserBasicInfoMap.get(queryStockAssetsDemandAssetsListRespDO.getRealUseUser());
            if(sysUserBasicInfo != null){
                queryStockAssetsDemandAssetsListRespDTO.setRealUseUserName(sysUserBasicInfo.getName());
                queryStockAssetsDemandAssetsListRespDTO.setDeptFullName(sysUserBasicInfo.getDeptFullName());
                String deptFullId = sysUserBasicInfo.getDeptFullId();
                if(StringUtils.isBlank(deptFullId)){
                    continue;
                }
                for (AssetsDemandOperationApproveUserConfig assetsDemandOperationApproveUserConfig : assetsDemandOperationApproveUserConfigList) {
                    for (String deptId : assetsDemandOperationApproveUserConfig.getDeptIdList()) {
                        if(deptFullId.contains(deptId)){
                            queryStockAssetsDemandAssetsListRespDTO.setOperationApproveUser(assetsDemandOperationApproveUserConfig.getEmpId());
                            queryStockAssetsDemandAssetsListRespDTO.setOperationApproveUserName(assetsDemandOperationApproveUserConfig.getEmpName());
                            break;
                        }
                    }
                }
            }
            // 设置收货人信息
            String applyUser = queryStockAssetsDemandAssetsListRespDO.getApplyUser();
            queryStockAssetsDemandAssetsListRespDTO.setReceiveUser(applyUser);
            sysUserBasicInfo = sysUserBasicInfoMap.get(applyUser);
            if(sysUserBasicInfo != null){
                queryStockAssetsDemandAssetsListRespDTO.setReceiveUserName(sysUserBasicInfo.getName());
            }
            // 设置收货人收件信息
            queryStockAssetsDemandAssetsListRespDTO.setReceiveUserPhone(queryStockAssetsDemandAssetsListRespDO.getApplyUserPhone());
            queryStockAssetsDemandAssetsListRespDTO.setReceiveUserAddress(queryStockAssetsDemandAssetsListRespDO.getApplyUserAddress());
            PsBusLocationTbl psBusLocationTbl = psBusLocationTblMap.get(queryStockAssetsDemandAssetsListRespDO.getApplyUserProvince());
            if (psBusLocationTbl != null) {
                String descr = psBusLocationTbl.getDescr();
                // 如果是直辖市，需要把市字去掉
                if (descr.endsWith("市")) {
                    descr = descr.substring(CommonConstant.NUMBER_ZERO, descr.length() - CommonConstant.NUMBER_ONE);
                }
                queryStockAssetsDemandAssetsListRespDTO.setReceiveUserProvinceName(descr);
            }
            psBusLocationTbl = psBusLocationTblMap.get(queryStockAssetsDemandAssetsListRespDO.getApplyUserCity());
            if (psBusLocationTbl != null) {
                String descr = psBusLocationTbl.getDescr();
                String[] descrArray = descr.split(StringConstant.HYPHEN);
                // 这里只截取到市
                if (descrArray.length > CommonConstant.NUMBER_ONE) {
                    descr = descrArray[CommonConstant.NUMBER_ONE];
                }
                queryStockAssetsDemandAssetsListRespDTO.setReceiveUserCityName(descr);
            }
            psBusLocationTbl = psBusLocationTblMap.get(queryStockAssetsDemandAssetsListRespDO.getApplyUserRegion());
            if (psBusLocationTbl != null) {
                String descr = psBusLocationTbl.getDescr();
                String[] descrArray = descr.split(StringConstant.HYPHEN);
                // 这里只截取到县或者区
                if (descrArray.length == CommonConstant.NUMBER_TWO) {
                    descr = descrArray[CommonConstant.NUMBER_ONE];
                }else if(descrArray.length > CommonConstant.NUMBER_TWO){
                    descr = descrArray[CommonConstant.NUMBER_TWO];
                }
                queryStockAssetsDemandAssetsListRespDTO.setReceiveUserRegionName(descr);
            }
            StockAssets stockAssets = stockAssetsMap.get(queryStockAssetsDemandAssetsListRespDO.getAssetsCode());
            if(stockAssets != null){
                queryStockAssetsDemandAssetsListRespDTO.setAssetsName(stockAssets.getAssetsName());
                queryStockAssetsDemandAssetsListRespDTO.setSnCode(stockAssets.getSnCode());
            }
        }
        return queryStockAssetsDemandAssetsListRespDTOList;
    }
    /**
     * @param: queryStockAssetsDemandAssetsListRespDOList
     * @description: 获取资产自助领用资产返回列表
     * @return: List<StockAssetsDemandAssetsListExportExcel>
     * @author: <EMAIL>
     * @date: 2023/8/15
     */
    public List<StockAssetsDemandAssetsListExportExcel> getStockAssetsDemandAssetsListExportExcelList(List<QueryStockAssetsDemandAssetsListRespDO> queryStockAssetsDemandAssetsListRespDOList) throws Exception{
        // 查询资产运营人配置信息
        List<AssetsDemandOperationApproveUserConfig> assetsDemandOperationApproveUserConfigList = ambaseCommonService.selectMetaDataByParam(MetaDataConstants.STOCK_MODULE_ASSETS_DEMAND, MetaDataConstants.OPERATION_APPROVE_USER_CONFIG, AssetsDemandOperationApproveUserConfig.class);
        List<StockAssetsDemandAssetsListExportExcel> stockAssetsDemandAssetsListExportExcelList = new ArrayList<>(queryStockAssetsDemandAssetsListRespDOList.size());
        Set<String> empIdSet = new HashSet<>();
        Set<String> locationSet = new HashSet<>();
        Set<String> assetsCodeSet = new HashSet<>();
        for (QueryStockAssetsDemandAssetsListRespDO queryStockAssetsDemandAssetsListRespDO : queryStockAssetsDemandAssetsListRespDOList) {
            empIdSet.add(queryStockAssetsDemandAssetsListRespDO.getClaimUser());
            empIdSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUser());
            empIdSet.add(queryStockAssetsDemandAssetsListRespDO.getRealUseUser());
            locationSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUserProvince());
            locationSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUserCity());
            locationSet.add(queryStockAssetsDemandAssetsListRespDO.getApplyUserRegion());
            assetsCodeSet.add(queryStockAssetsDemandAssetsListRespDO.getAssetsCode());
        }
        // 查询人员信息列表
        Map<String, SysUserBasicInfo> sysUserBasicInfoMap = ambaseCommonService.selectUserBasicInfoMapByEmpIdList(new ArrayList<>(empIdSet));
        // 查询地址列表
        PsBusLocationTblDO psBusLocationTblDO = new PsBusLocationTblDO();
        psBusLocationTblDO.setLocationList(new ArrayList<>(locationSet));
        Map<String, PsBusLocationTbl> psBusLocationTblMap = ambaseCommonService.selectPsBusLocationTblMap(psBusLocationTblDO);
        // 查询资产列表
        Map<String, StockAssets> stockAssetsMap = stockAssetsService.selectAssetsMapByCodes(new ArrayList<>(assetsCodeSet));
        for (QueryStockAssetsDemandAssetsListRespDO queryStockAssetsDemandAssetsListRespDO : queryStockAssetsDemandAssetsListRespDOList) {
            StockAssetsDemandAssetsListExportExcel stockAssetsDemandAssetsListExportExcel = new StockAssetsDemandAssetsListExportExcel();
            stockAssetsDemandAssetsListExportExcelList.add(stockAssetsDemandAssetsListExportExcel);
            BeanUtils.copyProperties(queryStockAssetsDemandAssetsListRespDO, stockAssetsDemandAssetsListExportExcel);
            // 设置领用日期
            Date holderTime = queryStockAssetsDemandAssetsListRespDO.getHolderTime();
            if(holderTime != null){
                stockAssetsDemandAssetsListExportExcel.setHolderTime(DateUtils.dateFormat(holderTime, DateUtils.DATE_PATTERN));
            }
            // 设置认领人信息
            SysUserBasicInfo sysUserBasicInfo = sysUserBasicInfoMap.get(queryStockAssetsDemandAssetsListRespDO.getClaimUser());
            if(sysUserBasicInfo != null){
                stockAssetsDemandAssetsListExportExcel.setClaimUserName(sysUserBasicInfo.getName());
            }
            // 设置实际领用人信息
            sysUserBasicInfo = sysUserBasicInfoMap.get(queryStockAssetsDemandAssetsListRespDO.getRealUseUser());
            if(sysUserBasicInfo != null){
                stockAssetsDemandAssetsListExportExcel.setRealUseUserName(sysUserBasicInfo.getName());
                stockAssetsDemandAssetsListExportExcel.setDeptFullName(sysUserBasicInfo.getDeptFullName());
                String deptFullId = sysUserBasicInfo.getDeptFullId();
                if(StringUtils.isBlank(deptFullId)){
                    continue;
                }
                for (AssetsDemandOperationApproveUserConfig assetsDemandOperationApproveUserConfig : assetsDemandOperationApproveUserConfigList) {
                    for (String deptId : assetsDemandOperationApproveUserConfig.getDeptIdList()) {
                        if(deptFullId.contains(deptId)){
                            stockAssetsDemandAssetsListExportExcel.setOperationApproveUser(assetsDemandOperationApproveUserConfig.getEmpId());
                            stockAssetsDemandAssetsListExportExcel.setOperationApproveUserName(assetsDemandOperationApproveUserConfig.getEmpName());
                            break;
                        }
                    }
                }
            }
            // 设置收货人信息
            String applyUser = queryStockAssetsDemandAssetsListRespDO.getApplyUser();
            stockAssetsDemandAssetsListExportExcel.setReceiveUser(applyUser);
            sysUserBasicInfo = sysUserBasicInfoMap.get(applyUser);
            if(sysUserBasicInfo != null){
                stockAssetsDemandAssetsListExportExcel.setReceiveUserName(sysUserBasicInfo.getName());
            }
            // 设置收货人收件信息
            stockAssetsDemandAssetsListExportExcel.setReceiveUserPhone(queryStockAssetsDemandAssetsListRespDO.getApplyUserPhone());
            stockAssetsDemandAssetsListExportExcel.setReceiveUserAddress(queryStockAssetsDemandAssetsListRespDO.getApplyUserAddress());
            PsBusLocationTbl psBusLocationTbl = psBusLocationTblMap.get(queryStockAssetsDemandAssetsListRespDO.getApplyUserProvince());
            if (psBusLocationTbl != null) {
                String descr = psBusLocationTbl.getDescr();
                // 如果是直辖市，需要把市字去掉
                if (descr.endsWith("市")) {
                    descr = descr.substring(CommonConstant.NUMBER_ZERO, descr.length() - CommonConstant.NUMBER_ONE);
                }
                stockAssetsDemandAssetsListExportExcel.setReceiveUserProvinceName(descr);
            }
            psBusLocationTbl = psBusLocationTblMap.get(queryStockAssetsDemandAssetsListRespDO.getApplyUserCity());
            if (psBusLocationTbl != null) {
                String descr = psBusLocationTbl.getDescr();
                String[] descrArray = descr.split(StringConstant.HYPHEN);
                // 这里只截取到市
                if (descrArray.length > CommonConstant.NUMBER_ONE) {
                    descr = descrArray[CommonConstant.NUMBER_ONE];
                }
                stockAssetsDemandAssetsListExportExcel.setReceiveUserCityName(descr);
            }
            psBusLocationTbl = psBusLocationTblMap.get(queryStockAssetsDemandAssetsListRespDO.getApplyUserRegion());
            if (psBusLocationTbl != null) {
                String descr = psBusLocationTbl.getDescr();
                String[] descrArray = descr.split(StringConstant.HYPHEN);
                // 这里只截取到县或者区
                if (descrArray.length == CommonConstant.NUMBER_TWO) {
                    descr = descrArray[CommonConstant.NUMBER_ONE];
                }else if(descrArray.length > CommonConstant.NUMBER_TWO){
                    descr = descrArray[CommonConstant.NUMBER_TWO];
                }
                stockAssetsDemandAssetsListExportExcel.setReceiveUserRegionName(descr);
            }
            StockAssets stockAssets = stockAssetsMap.get(queryStockAssetsDemandAssetsListRespDO.getAssetsCode());
            if(stockAssets != null){
                stockAssetsDemandAssetsListExportExcel.setAssetsName(stockAssets.getAssetsName());
                stockAssetsDemandAssetsListExportExcel.setSnCode(stockAssets.getSnCode());
            }
        }
        return stockAssetsDemandAssetsListExportExcelList;
    }
}
