package com.gz.eim.am.stock.service.repair;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairApproveReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairHeadReqDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 资产维修Service
 * @author: <EMAIL>
 * @date: 2022/12/6
 */
public interface StockAssetsRepairService {

    /**
     * @param: stockAssetsRepairApproveReqDTO
     * @description: IT审批页审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/6
     */
    ResponseData ITApprove(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO);

    /**
     * @param: stockAssetsRepairApproveReqDTO
     * @description: 行政审批页审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/6
     */
    ResponseData adminApprove(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO);

    /**
     * @param: stockAssetsRepairApproveReqDTO
     * @description: 资产持有人审批页审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/6
     */
    ResponseData assetsHolderApprove(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO) throws Exception;

    /**
     * @param: file,repairType
     * @description: 导入资产excel
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    ResponseData importAssetsExcel(MultipartFile file, Integer repairType) throws Exception;

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单保存和提交
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    ResponseData repairSaveOrSubmit(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser jwtUser) throws Exception;

    /**
     * @param: repairNo,status
     * @description: 资产维修单审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/19
     */
    void repairApprove(String repairNo, Integer status);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单详情查询
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    ResponseData queryRepairDetail(Long id, String repairNo) throws Exception;

    /**
     * @param: repairNo,request,response
     * @description: 导出资产明细
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    ResponseData exportAssetsExcel(String repairNo, HttpServletRequest request, HttpServletResponse response);

    /**
     * @param: file,repairType
     * @param: status
     * @description: 导入批量更新资产excel
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/14
     */
    ResponseData importBatchUpdateAssetsExcel(MultipartFile file, Integer repairType, Integer status) throws Exception;

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单验收提交
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    ResponseData repairCheckSubmit(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单验收审批
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    ResponseData repairCheckApprove(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 删除资产维修单
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    ResponseData repairDelete(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user);

    /**
     * @param: stockAssetsRepairHeadReqDTO
     * @description: 资产维修单列表查询
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/12/21
     */
    ResponseData queryRepairList(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO);
}
