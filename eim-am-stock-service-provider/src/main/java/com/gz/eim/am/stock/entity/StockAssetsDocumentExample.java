package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockAssetsDocumentExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockAssetsDocumentExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDocumentNoIsNull() {
            addCriterion("document_no is null");
            return (Criteria) this;
        }

        public Criteria andDocumentNoIsNotNull() {
            addCriterion("document_no is not null");
            return (Criteria) this;
        }

        public Criteria andDocumentNoEqualTo(String value) {
            addCriterion("document_no =", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoNotEqualTo(String value) {
            addCriterion("document_no <>", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoGreaterThan(String value) {
            addCriterion("document_no >", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoGreaterThanOrEqualTo(String value) {
            addCriterion("document_no >=", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoLessThan(String value) {
            addCriterion("document_no <", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoLessThanOrEqualTo(String value) {
            addCriterion("document_no <=", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoLike(String value) {
            addCriterion("document_no like", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoNotLike(String value) {
            addCriterion("document_no not like", value, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoIn(List<String> values) {
            addCriterion("document_no in", values, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoNotIn(List<String> values) {
            addCriterion("document_no not in", values, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoBetween(String value1, String value2) {
            addCriterion("document_no between", value1, value2, "documentNo");
            return (Criteria) this;
        }

        public Criteria andDocumentNoNotBetween(String value1, String value2) {
            addCriterion("document_no not between", value1, value2, "documentNo");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTransferMethodIsNull() {
            addCriterion("transfer_method is null");
            return (Criteria) this;
        }

        public Criteria andTransferMethodIsNotNull() {
            addCriterion("transfer_method is not null");
            return (Criteria) this;
        }

        public Criteria andTransferMethodEqualTo(Integer value) {
            addCriterion("transfer_method =", value, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodNotEqualTo(Integer value) {
            addCriterion("transfer_method <>", value, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodGreaterThan(Integer value) {
            addCriterion("transfer_method >", value, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodGreaterThanOrEqualTo(Integer value) {
            addCriterion("transfer_method >=", value, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodLessThan(Integer value) {
            addCriterion("transfer_method <", value, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodLessThanOrEqualTo(Integer value) {
            addCriterion("transfer_method <=", value, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodIn(List<Integer> values) {
            addCriterion("transfer_method in", values, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodNotIn(List<Integer> values) {
            addCriterion("transfer_method not in", values, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodBetween(Integer value1, Integer value2) {
            addCriterion("transfer_method between", value1, value2, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andTransferMethodNotBetween(Integer value1, Integer value2) {
            addCriterion("transfer_method not between", value1, value2, "transferMethod");
            return (Criteria) this;
        }

        public Criteria andHeadStatusIsNull() {
            addCriterion("head_status is null");
            return (Criteria) this;
        }

        public Criteria andHeadStatusIsNotNull() {
            addCriterion("head_status is not null");
            return (Criteria) this;
        }

        public Criteria andHeadStatusEqualTo(Integer value) {
            addCriterion("head_status =", value, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusNotEqualTo(Integer value) {
            addCriterion("head_status <>", value, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusGreaterThan(Integer value) {
            addCriterion("head_status >", value, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("head_status >=", value, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusLessThan(Integer value) {
            addCriterion("head_status <", value, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusLessThanOrEqualTo(Integer value) {
            addCriterion("head_status <=", value, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusIn(List<Integer> values) {
            addCriterion("head_status in", values, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusNotIn(List<Integer> values) {
            addCriterion("head_status not in", values, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusBetween(Integer value1, Integer value2) {
            addCriterion("head_status between", value1, value2, "headStatus");
            return (Criteria) this;
        }

        public Criteria andHeadStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("head_status not between", value1, value2, "headStatus");
            return (Criteria) this;
        }

        public Criteria andReasonCodeIsNull() {
            addCriterion("reason_code is null");
            return (Criteria) this;
        }

        public Criteria andReasonCodeIsNotNull() {
            addCriterion("reason_code is not null");
            return (Criteria) this;
        }

        public Criteria andReasonCodeEqualTo(Integer value) {
            addCriterion("reason_code =", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeNotEqualTo(Integer value) {
            addCriterion("reason_code <>", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeGreaterThan(Integer value) {
            addCriterion("reason_code >", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("reason_code >=", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeLessThan(Integer value) {
            addCriterion("reason_code <", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeLessThanOrEqualTo(Integer value) {
            addCriterion("reason_code <=", value, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeIn(List<Integer> values) {
            addCriterion("reason_code in", values, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeNotIn(List<Integer> values) {
            addCriterion("reason_code not in", values, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeBetween(Integer value1, Integer value2) {
            addCriterion("reason_code between", value1, value2, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andReasonCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("reason_code not between", value1, value2, "reasonCode");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNull() {
            addCriterion("billing_user is null");
            return (Criteria) this;
        }

        public Criteria andBillingUserIsNotNull() {
            addCriterion("billing_user is not null");
            return (Criteria) this;
        }

        public Criteria andBillingUserEqualTo(String value) {
            addCriterion("billing_user =", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotEqualTo(String value) {
            addCriterion("billing_user <>", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThan(String value) {
            addCriterion("billing_user >", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserGreaterThanOrEqualTo(String value) {
            addCriterion("billing_user >=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThan(String value) {
            addCriterion("billing_user <", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLessThanOrEqualTo(String value) {
            addCriterion("billing_user <=", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserLike(String value) {
            addCriterion("billing_user like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotLike(String value) {
            addCriterion("billing_user not like", value, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserIn(List<String> values) {
            addCriterion("billing_user in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotIn(List<String> values) {
            addCriterion("billing_user not in", values, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserBetween(String value1, String value2) {
            addCriterion("billing_user between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingUserNotBetween(String value1, String value2) {
            addCriterion("billing_user not between", value1, value2, "billingUser");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNull() {
            addCriterion("billing_time is null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNotNull() {
            addCriterion("billing_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeEqualTo(Date value) {
            addCriterion("billing_time =", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotEqualTo(Date value) {
            addCriterion("billing_time <>", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThan(Date value) {
            addCriterion("billing_time >", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("billing_time >=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThan(Date value) {
            addCriterion("billing_time <", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThanOrEqualTo(Date value) {
            addCriterion("billing_time <=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIn(List<Date> values) {
            addCriterion("billing_time in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotIn(List<Date> values) {
            addCriterion("billing_time not in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeBetween(Date value1, Date value2) {
            addCriterion("billing_time between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotBetween(Date value1, Date value2) {
            addCriterion("billing_time not between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperIsNull() {
            addCriterion("new_assets_keeper is null");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperIsNotNull() {
            addCriterion("new_assets_keeper is not null");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperEqualTo(String value) {
            addCriterion("new_assets_keeper =", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperNotEqualTo(String value) {
            addCriterion("new_assets_keeper <>", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperGreaterThan(String value) {
            addCriterion("new_assets_keeper >", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperGreaterThanOrEqualTo(String value) {
            addCriterion("new_assets_keeper >=", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperLessThan(String value) {
            addCriterion("new_assets_keeper <", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperLessThanOrEqualTo(String value) {
            addCriterion("new_assets_keeper <=", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperLike(String value) {
            addCriterion("new_assets_keeper like", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperNotLike(String value) {
            addCriterion("new_assets_keeper not like", value, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperIn(List<String> values) {
            addCriterion("new_assets_keeper in", values, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperNotIn(List<String> values) {
            addCriterion("new_assets_keeper not in", values, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperBetween(String value1, String value2) {
            addCriterion("new_assets_keeper between", value1, value2, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsKeeperNotBetween(String value1, String value2) {
            addCriterion("new_assets_keeper not between", value1, value2, "newAssetsKeeper");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderIsNull() {
            addCriterion("new_assets_holder is null");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderIsNotNull() {
            addCriterion("new_assets_holder is not null");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderEqualTo(String value) {
            addCriterion("new_assets_holder =", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderNotEqualTo(String value) {
            addCriterion("new_assets_holder <>", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderGreaterThan(String value) {
            addCriterion("new_assets_holder >", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderGreaterThanOrEqualTo(String value) {
            addCriterion("new_assets_holder >=", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderLessThan(String value) {
            addCriterion("new_assets_holder <", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderLessThanOrEqualTo(String value) {
            addCriterion("new_assets_holder <=", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderLike(String value) {
            addCriterion("new_assets_holder like", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderNotLike(String value) {
            addCriterion("new_assets_holder not like", value, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderIn(List<String> values) {
            addCriterion("new_assets_holder in", values, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderNotIn(List<String> values) {
            addCriterion("new_assets_holder not in", values, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderBetween(String value1, String value2) {
            addCriterion("new_assets_holder between", value1, value2, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderNotBetween(String value1, String value2) {
            addCriterion("new_assets_holder not between", value1, value2, "newAssetsHolder");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressIsNull() {
            addCriterion("new_assets_holder_address is null");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressIsNotNull() {
            addCriterion("new_assets_holder_address is not null");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressEqualTo(String value) {
            addCriterion("new_assets_holder_address =", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressNotEqualTo(String value) {
            addCriterion("new_assets_holder_address <>", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressGreaterThan(String value) {
            addCriterion("new_assets_holder_address >", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("new_assets_holder_address >=", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressLessThan(String value) {
            addCriterion("new_assets_holder_address <", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressLessThanOrEqualTo(String value) {
            addCriterion("new_assets_holder_address <=", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressLike(String value) {
            addCriterion("new_assets_holder_address like", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressNotLike(String value) {
            addCriterion("new_assets_holder_address not like", value, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressIn(List<String> values) {
            addCriterion("new_assets_holder_address in", values, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressNotIn(List<String> values) {
            addCriterion("new_assets_holder_address not in", values, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressBetween(String value1, String value2) {
            addCriterion("new_assets_holder_address between", value1, value2, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andNewAssetsHolderAddressNotBetween(String value1, String value2) {
            addCriterion("new_assets_holder_address not between", value1, value2, "newAssetsHolderAddress");
            return (Criteria) this;
        }

        public Criteria andDelFlagIsNull() {
            addCriterion("del_flag is null");
            return (Criteria) this;
        }

        public Criteria andDelFlagIsNotNull() {
            addCriterion("del_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDelFlagEqualTo(Integer value) {
            addCriterion("del_flag =", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotEqualTo(Integer value) {
            addCriterion("del_flag <>", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagGreaterThan(Integer value) {
            addCriterion("del_flag >", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("del_flag >=", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagLessThan(Integer value) {
            addCriterion("del_flag <", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagLessThanOrEqualTo(Integer value) {
            addCriterion("del_flag <=", value, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagIn(List<Integer> values) {
            addCriterion("del_flag in", values, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotIn(List<Integer> values) {
            addCriterion("del_flag not in", values, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagBetween(Integer value1, Integer value2) {
            addCriterion("del_flag between", value1, value2, "delFlag");
            return (Criteria) this;
        }

        public Criteria andDelFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("del_flag not between", value1, value2, "delFlag");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andNeedTimeIsNull() {
            addCriterion("need_time is null");
            return (Criteria) this;
        }

        public Criteria andNeedTimeIsNotNull() {
            addCriterion("need_time is not null");
            return (Criteria) this;
        }

        public Criteria andNeedTimeEqualTo(Date value) {
            addCriterion("need_time =", value, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeNotEqualTo(Date value) {
            addCriterion("need_time <>", value, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeGreaterThan(Date value) {
            addCriterion("need_time >", value, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("need_time >=", value, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeLessThan(Date value) {
            addCriterion("need_time <", value, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeLessThanOrEqualTo(Date value) {
            addCriterion("need_time <=", value, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeIn(List<Date> values) {
            addCriterion("need_time in", values, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeNotIn(List<Date> values) {
            addCriterion("need_time not in", values, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeBetween(Date value1, Date value2) {
            addCriterion("need_time between", value1, value2, "needTime");
            return (Criteria) this;
        }

        public Criteria andNeedTimeNotBetween(Date value1, Date value2) {
            addCriterion("need_time not between", value1, value2, "needTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}