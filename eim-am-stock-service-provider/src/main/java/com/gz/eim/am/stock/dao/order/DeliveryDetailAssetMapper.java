package com.gz.eim.am.stock.dao.order;

import com.gz.eim.am.stock.entity.StockDeliveryDetailAsset;
import com.gz.eim.am.stock.entity.StockWarehouseSuppliesCategory;

import java.util.List;

/**
 * @author: he<PERSON>ong
 * @date: 2019-12-21 PM 5:30
 * @description:
 */
public interface DeliveryDetailAssetMapper {

    /**
     * 批量插入
     * @param detailAssetList
     * @return
     */
    Integer insertMultiple(final List<StockDeliveryDetailAsset> detailAssetList);

    /**
     * @param: stockDeliveryDetailAssetList
     * @description: 批量插入
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/6/27
     */
    int batchUpdate(List<StockDeliveryDetailAsset> list);
}
