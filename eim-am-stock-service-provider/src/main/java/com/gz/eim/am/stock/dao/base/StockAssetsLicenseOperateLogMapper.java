package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsLicenseOperateLog;
import com.gz.eim.am.stock.entity.StockAssetsLicenseOperateLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsLicenseOperateLogMapper {
    long countByExample(StockAssetsLicenseOperateLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsLicenseOperateLog record);

    int insertSelective(StockAssetsLicenseOperateLog record);

    List<StockAssetsLicenseOperateLog> selectByExample(StockAssetsLicenseOperateLogExample example);

    StockAssetsLicenseOperateLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsLicenseOperateLog record, @Param("example") StockAssetsLicenseOperateLogExample example);

    int updateByExample(@Param("record") StockAssetsLicenseOperateLog record, @Param("example") StockAssetsLicenseOperateLogExample example);

    int updateByPrimaryKeySelective(StockAssetsLicenseOperateLog record);

    int updateByPrimaryKey(StockAssetsLicenseOperateLog record);
}