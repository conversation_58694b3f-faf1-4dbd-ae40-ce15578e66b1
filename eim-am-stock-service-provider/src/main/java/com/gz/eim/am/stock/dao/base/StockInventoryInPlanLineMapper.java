package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockInventoryInPlanLine;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLineExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockInventoryInPlanLineMapper {
    long countByExample(StockInventoryInPlanLineExample example);

    int deleteByPrimaryKey(Long inventoryInPlanLineId);

    int insert(StockInventoryInPlanLine record);

    int insertSelective(StockInventoryInPlanLine record);

    List<StockInventoryInPlanLine> selectByExample(StockInventoryInPlanLineExample example);

    StockInventoryInPlanLine selectByPrimaryKey(Long inventoryInPlanLineId);

    int updateByExampleSelective(@Param("record") StockInventoryInPlanLine record, @Param("example") StockInventoryInPlanLineExample example);

    int updateByExample(@Param("record") StockInventoryInPlanLine record, @Param("example") StockInventoryInPlanLineExample example);

    int updateByPrimaryKeySelective(StockInventoryInPlanLine record);

    int updateByPrimaryKey(StockInventoryInPlanLine record);
}