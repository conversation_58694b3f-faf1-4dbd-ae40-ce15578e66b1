package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import com.fuu.eim.tool.excel.constant.ExportType;

import java.util.Date;

/**
 * @author: weijunjie
 * @date: 2020/9/13
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportLicenseEntity implements ExportModel {


    @ExportField(name = "执照编码")
    private String assetsCode;
    @ExportField(name = "执照名称")
    private String assetsName;
    @ExportField(name = "执照分类编码")
    private String suppliesCode;
    @ExportField(name = "执照分类名称")
    private String suppliesName;

    @ExportField(name = "执照法人名称")
    private String licenseLegalPersonName;

    @ExportField(name = "法人身份证号")
    private String corporateIdNumber;

    @ExportField(name = "执照注册地址")
    private String registeredAddress;

    @ExportField(name = "执照类型")
    private String licenseType;

    @ExportField(name = "注册资产")
    private String registeredCapital;

    @ExportField(name = "经营范围")
    private String businessScope;

    @ExportField(name = "计划归还时间")
    private String planReturnTime;

    @ExportField(name = "资产状态")
    private String statusText;
    @ExportField(name = "当前使用人")
    private String holderName;
    @ExportField(name = "仓库名称")
    private String warehouseName;
    @ExportField(name = "入库日期", type = ExportType.DATE, format = "yyyy-MM-dd HH:mm:ss")
    private Date storageTime;
    @ExportField(name = "资产管理员")
    private String assetsKeeperName;
    @ExportField(name = "图片", type = ExportType.URL)
    private String assetsPic;
    @ExportField(name = "备注")
    private String remark;

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public String getStatusText() {
        return statusText;
    }

    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Date getStorageTime() {
        return storageTime;
    }

    public void setStorageTime(Date storageTime) {
        this.storageTime = storageTime;
    }

    public String getAssetsKeeperName() {
        return assetsKeeperName;
    }

    public void setAssetsKeeperName(String assetsKeeperName) {
        this.assetsKeeperName = assetsKeeperName;
    }

    public String getAssetsPic() {
        return assetsPic;
    }

    public void setAssetsPic(String assetsPic) {
        this.assetsPic = assetsPic;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesName() {
        return suppliesName;
    }

    public void setSuppliesName(String suppliesName) {
        this.suppliesName = suppliesName;
    }

    public String getLicenseLegalPersonName() {
        return licenseLegalPersonName;
    }

    public void setLicenseLegalPersonName(String licenseLegalPersonName) {
        this.licenseLegalPersonName = licenseLegalPersonName;
    }

    public String getCorporateIdNumber() {
        return corporateIdNumber;
    }

    public void setCorporateIdNumber(String corporateIdNumber) {
        this.corporateIdNumber = corporateIdNumber;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getPlanReturnTime() {
        return planReturnTime;
    }

    public void setPlanReturnTime(String planReturnTime) {
        this.planReturnTime = planReturnTime;
    }

    @Override
    public String getSheetName() {
        return "执照信息";
    }
}
