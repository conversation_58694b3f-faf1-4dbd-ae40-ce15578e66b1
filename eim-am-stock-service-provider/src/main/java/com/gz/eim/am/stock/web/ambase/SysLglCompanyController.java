package com.gz.eim.am.stock.web.ambase;

import com.fuu.eim.support.base.ResponseCode;
import com.gz.eim.am.pdm.dto.ResponseData;
import com.gz.eim.am.stock.api.ambase.SysLglCompanyApi;
import com.gz.eim.am.stock.dto.request.ambase.SysLglCompanySearchDTO;
import com.gz.eim.am.stock.service.ambase.SysLglCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: yangjifan1
 * @Date: 12/29/21
 * @description 公司信息
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/company")
public class SysLglCompanyController implements SysLglCompanyApi {
    @Autowired
    private SysLglCompanyService sysLglCompanyService;

    @Override
    public ResponseData selectSysLglCompany(SysLglCompanySearchDTO searchDTO) {
        ResponseData res = null;
        try {
            res = sysLglCompanyService.queryCompany(searchDTO);
        } catch (Exception e) {
            log.info("查询公司信息出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
