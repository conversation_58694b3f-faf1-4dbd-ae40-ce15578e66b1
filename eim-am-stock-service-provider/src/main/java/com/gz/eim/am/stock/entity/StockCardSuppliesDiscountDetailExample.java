package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockCardSuppliesDiscountDetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockCardSuppliesDiscountDetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDiscountNoIsNull() {
            addCriterion("discount_no is null");
            return (Criteria) this;
        }

        public Criteria andDiscountNoIsNotNull() {
            addCriterion("discount_no is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountNoEqualTo(String value) {
            addCriterion("discount_no =", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoNotEqualTo(String value) {
            addCriterion("discount_no <>", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoGreaterThan(String value) {
            addCriterion("discount_no >", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoGreaterThanOrEqualTo(String value) {
            addCriterion("discount_no >=", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoLessThan(String value) {
            addCriterion("discount_no <", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoLessThanOrEqualTo(String value) {
            addCriterion("discount_no <=", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoLike(String value) {
            addCriterion("discount_no like", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoNotLike(String value) {
            addCriterion("discount_no not like", value, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoIn(List<String> values) {
            addCriterion("discount_no in", values, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoNotIn(List<String> values) {
            addCriterion("discount_no not in", values, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoBetween(String value1, String value2) {
            addCriterion("discount_no between", value1, value2, "discountNo");
            return (Criteria) this;
        }

        public Criteria andDiscountNoNotBetween(String value1, String value2) {
            addCriterion("discount_no not between", value1, value2, "discountNo");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeIsNull() {
            addCriterion("convert_supplies_code is null");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeIsNotNull() {
            addCriterion("convert_supplies_code is not null");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeEqualTo(String value) {
            addCriterion("convert_supplies_code =", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeNotEqualTo(String value) {
            addCriterion("convert_supplies_code <>", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeGreaterThan(String value) {
            addCriterion("convert_supplies_code >", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeGreaterThanOrEqualTo(String value) {
            addCriterion("convert_supplies_code >=", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeLessThan(String value) {
            addCriterion("convert_supplies_code <", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeLessThanOrEqualTo(String value) {
            addCriterion("convert_supplies_code <=", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeLike(String value) {
            addCriterion("convert_supplies_code like", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeNotLike(String value) {
            addCriterion("convert_supplies_code not like", value, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeIn(List<String> values) {
            addCriterion("convert_supplies_code in", values, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeNotIn(List<String> values) {
            addCriterion("convert_supplies_code not in", values, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeBetween(String value1, String value2) {
            addCriterion("convert_supplies_code between", value1, value2, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertSuppliesCodeNotBetween(String value1, String value2) {
            addCriterion("convert_supplies_code not between", value1, value2, "convertSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeIsNull() {
            addCriterion("switch_supplies_code is null");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeIsNotNull() {
            addCriterion("switch_supplies_code is not null");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeEqualTo(String value) {
            addCriterion("switch_supplies_code =", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeNotEqualTo(String value) {
            addCriterion("switch_supplies_code <>", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeGreaterThan(String value) {
            addCriterion("switch_supplies_code >", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeGreaterThanOrEqualTo(String value) {
            addCriterion("switch_supplies_code >=", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeLessThan(String value) {
            addCriterion("switch_supplies_code <", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeLessThanOrEqualTo(String value) {
            addCriterion("switch_supplies_code <=", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeLike(String value) {
            addCriterion("switch_supplies_code like", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeNotLike(String value) {
            addCriterion("switch_supplies_code not like", value, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeIn(List<String> values) {
            addCriterion("switch_supplies_code in", values, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeNotIn(List<String> values) {
            addCriterion("switch_supplies_code not in", values, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeBetween(String value1, String value2) {
            addCriterion("switch_supplies_code between", value1, value2, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andSwitchSuppliesCodeNotBetween(String value1, String value2) {
            addCriterion("switch_supplies_code not between", value1, value2, "switchSuppliesCode");
            return (Criteria) this;
        }

        public Criteria andConvertNumberIsNull() {
            addCriterion("convert_number is null");
            return (Criteria) this;
        }

        public Criteria andConvertNumberIsNotNull() {
            addCriterion("convert_number is not null");
            return (Criteria) this;
        }

        public Criteria andConvertNumberEqualTo(Integer value) {
            addCriterion("convert_number =", value, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberNotEqualTo(Integer value) {
            addCriterion("convert_number <>", value, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberGreaterThan(Integer value) {
            addCriterion("convert_number >", value, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("convert_number >=", value, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberLessThan(Integer value) {
            addCriterion("convert_number <", value, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberLessThanOrEqualTo(Integer value) {
            addCriterion("convert_number <=", value, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberIn(List<Integer> values) {
            addCriterion("convert_number in", values, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberNotIn(List<Integer> values) {
            addCriterion("convert_number not in", values, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberBetween(Integer value1, Integer value2) {
            addCriterion("convert_number between", value1, value2, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("convert_number not between", value1, value2, "convertNumber");
            return (Criteria) this;
        }

        public Criteria andConvertResultIsNull() {
            addCriterion("convert_result is null");
            return (Criteria) this;
        }

        public Criteria andConvertResultIsNotNull() {
            addCriterion("convert_result is not null");
            return (Criteria) this;
        }

        public Criteria andConvertResultEqualTo(BigDecimal value) {
            addCriterion("convert_result =", value, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultNotEqualTo(BigDecimal value) {
            addCriterion("convert_result <>", value, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultGreaterThan(BigDecimal value) {
            addCriterion("convert_result >", value, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("convert_result >=", value, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultLessThan(BigDecimal value) {
            addCriterion("convert_result <", value, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultLessThanOrEqualTo(BigDecimal value) {
            addCriterion("convert_result <=", value, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultIn(List<BigDecimal> values) {
            addCriterion("convert_result in", values, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultNotIn(List<BigDecimal> values) {
            addCriterion("convert_result not in", values, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("convert_result between", value1, value2, "convertResult");
            return (Criteria) this;
        }

        public Criteria andConvertResultNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("convert_result not between", value1, value2, "convertResult");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleIsNull() {
            addCriterion("discount_scale is null");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleIsNotNull() {
            addCriterion("discount_scale is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleEqualTo(BigDecimal value) {
            addCriterion("discount_scale =", value, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleNotEqualTo(BigDecimal value) {
            addCriterion("discount_scale <>", value, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleGreaterThan(BigDecimal value) {
            addCriterion("discount_scale >", value, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_scale >=", value, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleLessThan(BigDecimal value) {
            addCriterion("discount_scale <", value, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_scale <=", value, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleIn(List<BigDecimal> values) {
            addCriterion("discount_scale in", values, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleNotIn(List<BigDecimal> values) {
            addCriterion("discount_scale not in", values, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_scale between", value1, value2, "discountScale");
            return (Criteria) this;
        }

        public Criteria andDiscountScaleNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_scale not between", value1, value2, "discountScale");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}