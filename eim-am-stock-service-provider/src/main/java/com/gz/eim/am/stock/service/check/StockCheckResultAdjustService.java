package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.entity.CheckDifferenceListHead;

/**
 * @author: weijunjie
 * @date: 2021/2/20
 * @description
 */
public interface StockCheckResultAdjustService {

    /**
     * 查询盘点异常数据
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    ResponseData resultAdjustDataSelect(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception;

    /**
     * 盘点差异结果提交审批
     * @param checkDifferenceListHeadReqDTO
     * @return
     * @throws Exception
     */
    ResponseData approveCheckResult(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception;

    /**
     * 盘点差异结果提交审批
     * @param checkDifferenceListHead
     * @return
     */
    ResponseData updateApproveStatus(CheckDifferenceListHead checkDifferenceListHead);

    /**
     * 查询盘点入库单
     * @param adjustResultReqDTO
     * @return
     */
    ResponseData queryInventoryPage(AdjustResultReqDTO adjustResultReqDTO);

    /**
     * 查询盘点出库单
     * @param adjustResultReqDTO
     * @return
     */
    ResponseData queryDeliveryPage(AdjustResultReqDTO adjustResultReqDTO);

    /**
     * 查询盘盈亏出入库结果明细 type：1入库 2出库
     * @param adjustResultReqDTO
     * @return
     */
    ResponseData queryAdjustResultDetail(AdjustResultReqDTO adjustResultReqDTO);
}
