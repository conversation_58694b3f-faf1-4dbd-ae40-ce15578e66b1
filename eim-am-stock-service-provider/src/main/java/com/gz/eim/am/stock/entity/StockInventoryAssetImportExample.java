package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockInventoryAssetImportExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockInventoryAssetImportExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andInventoryAssetImpIdIsNull() {
            addCriterion("inventory_asset_imp_id is null");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdIsNotNull() {
            addCriterion("inventory_asset_imp_id is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdEqualTo(Integer value) {
            addCriterion("inventory_asset_imp_id =", value, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdNotEqualTo(Integer value) {
            addCriterion("inventory_asset_imp_id <>", value, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdGreaterThan(Integer value) {
            addCriterion("inventory_asset_imp_id >", value, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_asset_imp_id >=", value, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdLessThan(Integer value) {
            addCriterion("inventory_asset_imp_id <", value, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_asset_imp_id <=", value, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdIn(List<Integer> values) {
            addCriterion("inventory_asset_imp_id in", values, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdNotIn(List<Integer> values) {
            addCriterion("inventory_asset_imp_id not in", values, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdBetween(Integer value1, Integer value2) {
            addCriterion("inventory_asset_imp_id between", value1, value2, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetImpIdNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_asset_imp_id not between", value1, value2, "inventoryAssetImpId");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeIsNull() {
            addCriterion("inventory_asset_batch_code is null");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeIsNotNull() {
            addCriterion("inventory_asset_batch_code is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeEqualTo(String value) {
            addCriterion("inventory_asset_batch_code =", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeNotEqualTo(String value) {
            addCriterion("inventory_asset_batch_code <>", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeGreaterThan(String value) {
            addCriterion("inventory_asset_batch_code >", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_asset_batch_code >=", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeLessThan(String value) {
            addCriterion("inventory_asset_batch_code <", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeLessThanOrEqualTo(String value) {
            addCriterion("inventory_asset_batch_code <=", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeLike(String value) {
            addCriterion("inventory_asset_batch_code like", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeNotLike(String value) {
            addCriterion("inventory_asset_batch_code not like", value, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeIn(List<String> values) {
            addCriterion("inventory_asset_batch_code in", values, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeNotIn(List<String> values) {
            addCriterion("inventory_asset_batch_code not in", values, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeBetween(String value1, String value2) {
            addCriterion("inventory_asset_batch_code between", value1, value2, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andInventoryAssetBatchCodeNotBetween(String value1, String value2) {
            addCriterion("inventory_asset_batch_code not between", value1, value2, "inventoryAssetBatchCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeIsNull() {
            addCriterion("asset_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetCodeIsNotNull() {
            addCriterion("asset_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetCodeEqualTo(String value) {
            addCriterion("asset_code =", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeNotEqualTo(String value) {
            addCriterion("asset_code <>", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeGreaterThan(String value) {
            addCriterion("asset_code >", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeGreaterThanOrEqualTo(String value) {
            addCriterion("asset_code >=", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeLessThan(String value) {
            addCriterion("asset_code <", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeLessThanOrEqualTo(String value) {
            addCriterion("asset_code <=", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeLike(String value) {
            addCriterion("asset_code like", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeNotLike(String value) {
            addCriterion("asset_code not like", value, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeIn(List<String> values) {
            addCriterion("asset_code in", values, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeNotIn(List<String> values) {
            addCriterion("asset_code not in", values, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeBetween(String value1, String value2) {
            addCriterion("asset_code between", value1, value2, "assetCode");
            return (Criteria) this;
        }

        public Criteria andAssetCodeNotBetween(String value1, String value2) {
            addCriterion("asset_code not between", value1, value2, "assetCode");
            return (Criteria) this;
        }

        public Criteria andSnNoIsNull() {
            addCriterion("sn_no is null");
            return (Criteria) this;
        }

        public Criteria andSnNoIsNotNull() {
            addCriterion("sn_no is not null");
            return (Criteria) this;
        }

        public Criteria andSnNoEqualTo(String value) {
            addCriterion("sn_no =", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotEqualTo(String value) {
            addCriterion("sn_no <>", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoGreaterThan(String value) {
            addCriterion("sn_no >", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoGreaterThanOrEqualTo(String value) {
            addCriterion("sn_no >=", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLessThan(String value) {
            addCriterion("sn_no <", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLessThanOrEqualTo(String value) {
            addCriterion("sn_no <=", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLike(String value) {
            addCriterion("sn_no like", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotLike(String value) {
            addCriterion("sn_no not like", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoIn(List<String> values) {
            addCriterion("sn_no in", values, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotIn(List<String> values) {
            addCriterion("sn_no not in", values, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoBetween(String value1, String value2) {
            addCriterion("sn_no between", value1, value2, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotBetween(String value1, String value2) {
            addCriterion("sn_no not between", value1, value2, "snNo");
            return (Criteria) this;
        }

        public Criteria andUsedLeftIsNull() {
            addCriterion("used_left is null");
            return (Criteria) this;
        }

        public Criteria andUsedLeftIsNotNull() {
            addCriterion("used_left is not null");
            return (Criteria) this;
        }

        public Criteria andUsedLeftEqualTo(Integer value) {
            addCriterion("used_left =", value, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftNotEqualTo(Integer value) {
            addCriterion("used_left <>", value, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftGreaterThan(Integer value) {
            addCriterion("used_left >", value, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftGreaterThanOrEqualTo(Integer value) {
            addCriterion("used_left >=", value, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftLessThan(Integer value) {
            addCriterion("used_left <", value, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftLessThanOrEqualTo(Integer value) {
            addCriterion("used_left <=", value, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftIn(List<Integer> values) {
            addCriterion("used_left in", values, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftNotIn(List<Integer> values) {
            addCriterion("used_left not in", values, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftBetween(Integer value1, Integer value2) {
            addCriterion("used_left between", value1, value2, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andUsedLeftNotBetween(Integer value1, Integer value2) {
            addCriterion("used_left not between", value1, value2, "usedLeft");
            return (Criteria) this;
        }

        public Criteria andAttr1IsNull() {
            addCriterion("attr_1 is null");
            return (Criteria) this;
        }

        public Criteria andAttr1IsNotNull() {
            addCriterion("attr_1 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr1EqualTo(String value) {
            addCriterion("attr_1 =", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1NotEqualTo(String value) {
            addCriterion("attr_1 <>", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1GreaterThan(String value) {
            addCriterion("attr_1 >", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1GreaterThanOrEqualTo(String value) {
            addCriterion("attr_1 >=", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1LessThan(String value) {
            addCriterion("attr_1 <", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1LessThanOrEqualTo(String value) {
            addCriterion("attr_1 <=", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1Like(String value) {
            addCriterion("attr_1 like", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1NotLike(String value) {
            addCriterion("attr_1 not like", value, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1In(List<String> values) {
            addCriterion("attr_1 in", values, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1NotIn(List<String> values) {
            addCriterion("attr_1 not in", values, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1Between(String value1, String value2) {
            addCriterion("attr_1 between", value1, value2, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr1NotBetween(String value1, String value2) {
            addCriterion("attr_1 not between", value1, value2, "attr1");
            return (Criteria) this;
        }

        public Criteria andAttr2IsNull() {
            addCriterion("attr_2 is null");
            return (Criteria) this;
        }

        public Criteria andAttr2IsNotNull() {
            addCriterion("attr_2 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr2EqualTo(String value) {
            addCriterion("attr_2 =", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotEqualTo(String value) {
            addCriterion("attr_2 <>", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2GreaterThan(String value) {
            addCriterion("attr_2 >", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2GreaterThanOrEqualTo(String value) {
            addCriterion("attr_2 >=", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2LessThan(String value) {
            addCriterion("attr_2 <", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2LessThanOrEqualTo(String value) {
            addCriterion("attr_2 <=", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2Like(String value) {
            addCriterion("attr_2 like", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotLike(String value) {
            addCriterion("attr_2 not like", value, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2In(List<String> values) {
            addCriterion("attr_2 in", values, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotIn(List<String> values) {
            addCriterion("attr_2 not in", values, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2Between(String value1, String value2) {
            addCriterion("attr_2 between", value1, value2, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr2NotBetween(String value1, String value2) {
            addCriterion("attr_2 not between", value1, value2, "attr2");
            return (Criteria) this;
        }

        public Criteria andAttr3IsNull() {
            addCriterion("attr_3 is null");
            return (Criteria) this;
        }

        public Criteria andAttr3IsNotNull() {
            addCriterion("attr_3 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr3EqualTo(String value) {
            addCriterion("attr_3 =", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotEqualTo(String value) {
            addCriterion("attr_3 <>", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3GreaterThan(String value) {
            addCriterion("attr_3 >", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3GreaterThanOrEqualTo(String value) {
            addCriterion("attr_3 >=", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3LessThan(String value) {
            addCriterion("attr_3 <", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3LessThanOrEqualTo(String value) {
            addCriterion("attr_3 <=", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3Like(String value) {
            addCriterion("attr_3 like", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotLike(String value) {
            addCriterion("attr_3 not like", value, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3In(List<String> values) {
            addCriterion("attr_3 in", values, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotIn(List<String> values) {
            addCriterion("attr_3 not in", values, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3Between(String value1, String value2) {
            addCriterion("attr_3 between", value1, value2, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr3NotBetween(String value1, String value2) {
            addCriterion("attr_3 not between", value1, value2, "attr3");
            return (Criteria) this;
        }

        public Criteria andAttr4IsNull() {
            addCriterion("attr_4 is null");
            return (Criteria) this;
        }

        public Criteria andAttr4IsNotNull() {
            addCriterion("attr_4 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr4EqualTo(String value) {
            addCriterion("attr_4 =", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4NotEqualTo(String value) {
            addCriterion("attr_4 <>", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4GreaterThan(String value) {
            addCriterion("attr_4 >", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4GreaterThanOrEqualTo(String value) {
            addCriterion("attr_4 >=", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4LessThan(String value) {
            addCriterion("attr_4 <", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4LessThanOrEqualTo(String value) {
            addCriterion("attr_4 <=", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4Like(String value) {
            addCriterion("attr_4 like", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4NotLike(String value) {
            addCriterion("attr_4 not like", value, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4In(List<String> values) {
            addCriterion("attr_4 in", values, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4NotIn(List<String> values) {
            addCriterion("attr_4 not in", values, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4Between(String value1, String value2) {
            addCriterion("attr_4 between", value1, value2, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr4NotBetween(String value1, String value2) {
            addCriterion("attr_4 not between", value1, value2, "attr4");
            return (Criteria) this;
        }

        public Criteria andAttr5IsNull() {
            addCriterion("attr_5 is null");
            return (Criteria) this;
        }

        public Criteria andAttr5IsNotNull() {
            addCriterion("attr_5 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr5EqualTo(String value) {
            addCriterion("attr_5 =", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5NotEqualTo(String value) {
            addCriterion("attr_5 <>", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5GreaterThan(String value) {
            addCriterion("attr_5 >", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5GreaterThanOrEqualTo(String value) {
            addCriterion("attr_5 >=", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5LessThan(String value) {
            addCriterion("attr_5 <", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5LessThanOrEqualTo(String value) {
            addCriterion("attr_5 <=", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5Like(String value) {
            addCriterion("attr_5 like", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5NotLike(String value) {
            addCriterion("attr_5 not like", value, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5In(List<String> values) {
            addCriterion("attr_5 in", values, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5NotIn(List<String> values) {
            addCriterion("attr_5 not in", values, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5Between(String value1, String value2) {
            addCriterion("attr_5 between", value1, value2, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr5NotBetween(String value1, String value2) {
            addCriterion("attr_5 not between", value1, value2, "attr5");
            return (Criteria) this;
        }

        public Criteria andAttr6IsNull() {
            addCriterion("attr_6 is null");
            return (Criteria) this;
        }

        public Criteria andAttr6IsNotNull() {
            addCriterion("attr_6 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr6EqualTo(String value) {
            addCriterion("attr_6 =", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6NotEqualTo(String value) {
            addCriterion("attr_6 <>", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6GreaterThan(String value) {
            addCriterion("attr_6 >", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6GreaterThanOrEqualTo(String value) {
            addCriterion("attr_6 >=", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6LessThan(String value) {
            addCriterion("attr_6 <", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6LessThanOrEqualTo(String value) {
            addCriterion("attr_6 <=", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6Like(String value) {
            addCriterion("attr_6 like", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6NotLike(String value) {
            addCriterion("attr_6 not like", value, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6In(List<String> values) {
            addCriterion("attr_6 in", values, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6NotIn(List<String> values) {
            addCriterion("attr_6 not in", values, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6Between(String value1, String value2) {
            addCriterion("attr_6 between", value1, value2, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr6NotBetween(String value1, String value2) {
            addCriterion("attr_6 not between", value1, value2, "attr6");
            return (Criteria) this;
        }

        public Criteria andAttr7IsNull() {
            addCriterion("attr_7 is null");
            return (Criteria) this;
        }

        public Criteria andAttr7IsNotNull() {
            addCriterion("attr_7 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr7EqualTo(String value) {
            addCriterion("attr_7 =", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7NotEqualTo(String value) {
            addCriterion("attr_7 <>", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7GreaterThan(String value) {
            addCriterion("attr_7 >", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7GreaterThanOrEqualTo(String value) {
            addCriterion("attr_7 >=", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7LessThan(String value) {
            addCriterion("attr_7 <", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7LessThanOrEqualTo(String value) {
            addCriterion("attr_7 <=", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7Like(String value) {
            addCriterion("attr_7 like", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7NotLike(String value) {
            addCriterion("attr_7 not like", value, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7In(List<String> values) {
            addCriterion("attr_7 in", values, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7NotIn(List<String> values) {
            addCriterion("attr_7 not in", values, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7Between(String value1, String value2) {
            addCriterion("attr_7 between", value1, value2, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr7NotBetween(String value1, String value2) {
            addCriterion("attr_7 not between", value1, value2, "attr7");
            return (Criteria) this;
        }

        public Criteria andAttr8IsNull() {
            addCriterion("attr_8 is null");
            return (Criteria) this;
        }

        public Criteria andAttr8IsNotNull() {
            addCriterion("attr_8 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr8EqualTo(String value) {
            addCriterion("attr_8 =", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8NotEqualTo(String value) {
            addCriterion("attr_8 <>", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8GreaterThan(String value) {
            addCriterion("attr_8 >", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8GreaterThanOrEqualTo(String value) {
            addCriterion("attr_8 >=", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8LessThan(String value) {
            addCriterion("attr_8 <", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8LessThanOrEqualTo(String value) {
            addCriterion("attr_8 <=", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8Like(String value) {
            addCriterion("attr_8 like", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8NotLike(String value) {
            addCriterion("attr_8 not like", value, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8In(List<String> values) {
            addCriterion("attr_8 in", values, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8NotIn(List<String> values) {
            addCriterion("attr_8 not in", values, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8Between(String value1, String value2) {
            addCriterion("attr_8 between", value1, value2, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr8NotBetween(String value1, String value2) {
            addCriterion("attr_8 not between", value1, value2, "attr8");
            return (Criteria) this;
        }

        public Criteria andAttr9IsNull() {
            addCriterion("attr_9 is null");
            return (Criteria) this;
        }

        public Criteria andAttr9IsNotNull() {
            addCriterion("attr_9 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr9EqualTo(String value) {
            addCriterion("attr_9 =", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9NotEqualTo(String value) {
            addCriterion("attr_9 <>", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9GreaterThan(String value) {
            addCriterion("attr_9 >", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9GreaterThanOrEqualTo(String value) {
            addCriterion("attr_9 >=", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9LessThan(String value) {
            addCriterion("attr_9 <", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9LessThanOrEqualTo(String value) {
            addCriterion("attr_9 <=", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9Like(String value) {
            addCriterion("attr_9 like", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9NotLike(String value) {
            addCriterion("attr_9 not like", value, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9In(List<String> values) {
            addCriterion("attr_9 in", values, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9NotIn(List<String> values) {
            addCriterion("attr_9 not in", values, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9Between(String value1, String value2) {
            addCriterion("attr_9 between", value1, value2, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr9NotBetween(String value1, String value2) {
            addCriterion("attr_9 not between", value1, value2, "attr9");
            return (Criteria) this;
        }

        public Criteria andAttr10IsNull() {
            addCriterion("attr_10 is null");
            return (Criteria) this;
        }

        public Criteria andAttr10IsNotNull() {
            addCriterion("attr_10 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr10EqualTo(String value) {
            addCriterion("attr_10 =", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10NotEqualTo(String value) {
            addCriterion("attr_10 <>", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10GreaterThan(String value) {
            addCriterion("attr_10 >", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10GreaterThanOrEqualTo(String value) {
            addCriterion("attr_10 >=", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10LessThan(String value) {
            addCriterion("attr_10 <", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10LessThanOrEqualTo(String value) {
            addCriterion("attr_10 <=", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10Like(String value) {
            addCriterion("attr_10 like", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10NotLike(String value) {
            addCriterion("attr_10 not like", value, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10In(List<String> values) {
            addCriterion("attr_10 in", values, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10NotIn(List<String> values) {
            addCriterion("attr_10 not in", values, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10Between(String value1, String value2) {
            addCriterion("attr_10 between", value1, value2, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr10NotBetween(String value1, String value2) {
            addCriterion("attr_10 not between", value1, value2, "attr10");
            return (Criteria) this;
        }

        public Criteria andAttr11IsNull() {
            addCriterion("attr_11 is null");
            return (Criteria) this;
        }

        public Criteria andAttr11IsNotNull() {
            addCriterion("attr_11 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr11EqualTo(String value) {
            addCriterion("attr_11 =", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11NotEqualTo(String value) {
            addCriterion("attr_11 <>", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11GreaterThan(String value) {
            addCriterion("attr_11 >", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11GreaterThanOrEqualTo(String value) {
            addCriterion("attr_11 >=", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11LessThan(String value) {
            addCriterion("attr_11 <", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11LessThanOrEqualTo(String value) {
            addCriterion("attr_11 <=", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11Like(String value) {
            addCriterion("attr_11 like", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11NotLike(String value) {
            addCriterion("attr_11 not like", value, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11In(List<String> values) {
            addCriterion("attr_11 in", values, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11NotIn(List<String> values) {
            addCriterion("attr_11 not in", values, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11Between(String value1, String value2) {
            addCriterion("attr_11 between", value1, value2, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr11NotBetween(String value1, String value2) {
            addCriterion("attr_11 not between", value1, value2, "attr11");
            return (Criteria) this;
        }

        public Criteria andAttr12IsNull() {
            addCriterion("attr_12 is null");
            return (Criteria) this;
        }

        public Criteria andAttr12IsNotNull() {
            addCriterion("attr_12 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr12EqualTo(String value) {
            addCriterion("attr_12 =", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12NotEqualTo(String value) {
            addCriterion("attr_12 <>", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12GreaterThan(String value) {
            addCriterion("attr_12 >", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12GreaterThanOrEqualTo(String value) {
            addCriterion("attr_12 >=", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12LessThan(String value) {
            addCriterion("attr_12 <", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12LessThanOrEqualTo(String value) {
            addCriterion("attr_12 <=", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12Like(String value) {
            addCriterion("attr_12 like", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12NotLike(String value) {
            addCriterion("attr_12 not like", value, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12In(List<String> values) {
            addCriterion("attr_12 in", values, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12NotIn(List<String> values) {
            addCriterion("attr_12 not in", values, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12Between(String value1, String value2) {
            addCriterion("attr_12 between", value1, value2, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr12NotBetween(String value1, String value2) {
            addCriterion("attr_12 not between", value1, value2, "attr12");
            return (Criteria) this;
        }

        public Criteria andAttr13IsNull() {
            addCriterion("attr_13 is null");
            return (Criteria) this;
        }

        public Criteria andAttr13IsNotNull() {
            addCriterion("attr_13 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr13EqualTo(String value) {
            addCriterion("attr_13 =", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13NotEqualTo(String value) {
            addCriterion("attr_13 <>", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13GreaterThan(String value) {
            addCriterion("attr_13 >", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13GreaterThanOrEqualTo(String value) {
            addCriterion("attr_13 >=", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13LessThan(String value) {
            addCriterion("attr_13 <", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13LessThanOrEqualTo(String value) {
            addCriterion("attr_13 <=", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13Like(String value) {
            addCriterion("attr_13 like", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13NotLike(String value) {
            addCriterion("attr_13 not like", value, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13In(List<String> values) {
            addCriterion("attr_13 in", values, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13NotIn(List<String> values) {
            addCriterion("attr_13 not in", values, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13Between(String value1, String value2) {
            addCriterion("attr_13 between", value1, value2, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr13NotBetween(String value1, String value2) {
            addCriterion("attr_13 not between", value1, value2, "attr13");
            return (Criteria) this;
        }

        public Criteria andAttr14IsNull() {
            addCriterion("attr_14 is null");
            return (Criteria) this;
        }

        public Criteria andAttr14IsNotNull() {
            addCriterion("attr_14 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr14EqualTo(String value) {
            addCriterion("attr_14 =", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14NotEqualTo(String value) {
            addCriterion("attr_14 <>", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14GreaterThan(String value) {
            addCriterion("attr_14 >", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14GreaterThanOrEqualTo(String value) {
            addCriterion("attr_14 >=", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14LessThan(String value) {
            addCriterion("attr_14 <", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14LessThanOrEqualTo(String value) {
            addCriterion("attr_14 <=", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14Like(String value) {
            addCriterion("attr_14 like", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14NotLike(String value) {
            addCriterion("attr_14 not like", value, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14In(List<String> values) {
            addCriterion("attr_14 in", values, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14NotIn(List<String> values) {
            addCriterion("attr_14 not in", values, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14Between(String value1, String value2) {
            addCriterion("attr_14 between", value1, value2, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr14NotBetween(String value1, String value2) {
            addCriterion("attr_14 not between", value1, value2, "attr14");
            return (Criteria) this;
        }

        public Criteria andAttr15IsNull() {
            addCriterion("attr_15 is null");
            return (Criteria) this;
        }

        public Criteria andAttr15IsNotNull() {
            addCriterion("attr_15 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr15EqualTo(String value) {
            addCriterion("attr_15 =", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15NotEqualTo(String value) {
            addCriterion("attr_15 <>", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15GreaterThan(String value) {
            addCriterion("attr_15 >", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15GreaterThanOrEqualTo(String value) {
            addCriterion("attr_15 >=", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15LessThan(String value) {
            addCriterion("attr_15 <", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15LessThanOrEqualTo(String value) {
            addCriterion("attr_15 <=", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15Like(String value) {
            addCriterion("attr_15 like", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15NotLike(String value) {
            addCriterion("attr_15 not like", value, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15In(List<String> values) {
            addCriterion("attr_15 in", values, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15NotIn(List<String> values) {
            addCriterion("attr_15 not in", values, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15Between(String value1, String value2) {
            addCriterion("attr_15 between", value1, value2, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr15NotBetween(String value1, String value2) {
            addCriterion("attr_15 not between", value1, value2, "attr15");
            return (Criteria) this;
        }

        public Criteria andAttr16IsNull() {
            addCriterion("attr_16 is null");
            return (Criteria) this;
        }

        public Criteria andAttr16IsNotNull() {
            addCriterion("attr_16 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr16EqualTo(String value) {
            addCriterion("attr_16 =", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16NotEqualTo(String value) {
            addCriterion("attr_16 <>", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16GreaterThan(String value) {
            addCriterion("attr_16 >", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16GreaterThanOrEqualTo(String value) {
            addCriterion("attr_16 >=", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16LessThan(String value) {
            addCriterion("attr_16 <", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16LessThanOrEqualTo(String value) {
            addCriterion("attr_16 <=", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16Like(String value) {
            addCriterion("attr_16 like", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16NotLike(String value) {
            addCriterion("attr_16 not like", value, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16In(List<String> values) {
            addCriterion("attr_16 in", values, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16NotIn(List<String> values) {
            addCriterion("attr_16 not in", values, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16Between(String value1, String value2) {
            addCriterion("attr_16 between", value1, value2, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr16NotBetween(String value1, String value2) {
            addCriterion("attr_16 not between", value1, value2, "attr16");
            return (Criteria) this;
        }

        public Criteria andAttr17IsNull() {
            addCriterion("attr_17 is null");
            return (Criteria) this;
        }

        public Criteria andAttr17IsNotNull() {
            addCriterion("attr_17 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr17EqualTo(String value) {
            addCriterion("attr_17 =", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17NotEqualTo(String value) {
            addCriterion("attr_17 <>", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17GreaterThan(String value) {
            addCriterion("attr_17 >", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17GreaterThanOrEqualTo(String value) {
            addCriterion("attr_17 >=", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17LessThan(String value) {
            addCriterion("attr_17 <", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17LessThanOrEqualTo(String value) {
            addCriterion("attr_17 <=", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17Like(String value) {
            addCriterion("attr_17 like", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17NotLike(String value) {
            addCriterion("attr_17 not like", value, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17In(List<String> values) {
            addCriterion("attr_17 in", values, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17NotIn(List<String> values) {
            addCriterion("attr_17 not in", values, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17Between(String value1, String value2) {
            addCriterion("attr_17 between", value1, value2, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr17NotBetween(String value1, String value2) {
            addCriterion("attr_17 not between", value1, value2, "attr17");
            return (Criteria) this;
        }

        public Criteria andAttr18IsNull() {
            addCriterion("attr_18 is null");
            return (Criteria) this;
        }

        public Criteria andAttr18IsNotNull() {
            addCriterion("attr_18 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr18EqualTo(String value) {
            addCriterion("attr_18 =", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18NotEqualTo(String value) {
            addCriterion("attr_18 <>", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18GreaterThan(String value) {
            addCriterion("attr_18 >", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18GreaterThanOrEqualTo(String value) {
            addCriterion("attr_18 >=", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18LessThan(String value) {
            addCriterion("attr_18 <", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18LessThanOrEqualTo(String value) {
            addCriterion("attr_18 <=", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18Like(String value) {
            addCriterion("attr_18 like", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18NotLike(String value) {
            addCriterion("attr_18 not like", value, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18In(List<String> values) {
            addCriterion("attr_18 in", values, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18NotIn(List<String> values) {
            addCriterion("attr_18 not in", values, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18Between(String value1, String value2) {
            addCriterion("attr_18 between", value1, value2, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr18NotBetween(String value1, String value2) {
            addCriterion("attr_18 not between", value1, value2, "attr18");
            return (Criteria) this;
        }

        public Criteria andAttr19IsNull() {
            addCriterion("attr_19 is null");
            return (Criteria) this;
        }

        public Criteria andAttr19IsNotNull() {
            addCriterion("attr_19 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr19EqualTo(String value) {
            addCriterion("attr_19 =", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19NotEqualTo(String value) {
            addCriterion("attr_19 <>", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19GreaterThan(String value) {
            addCriterion("attr_19 >", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19GreaterThanOrEqualTo(String value) {
            addCriterion("attr_19 >=", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19LessThan(String value) {
            addCriterion("attr_19 <", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19LessThanOrEqualTo(String value) {
            addCriterion("attr_19 <=", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19Like(String value) {
            addCriterion("attr_19 like", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19NotLike(String value) {
            addCriterion("attr_19 not like", value, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19In(List<String> values) {
            addCriterion("attr_19 in", values, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19NotIn(List<String> values) {
            addCriterion("attr_19 not in", values, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19Between(String value1, String value2) {
            addCriterion("attr_19 between", value1, value2, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr19NotBetween(String value1, String value2) {
            addCriterion("attr_19 not between", value1, value2, "attr19");
            return (Criteria) this;
        }

        public Criteria andAttr20IsNull() {
            addCriterion("attr_20 is null");
            return (Criteria) this;
        }

        public Criteria andAttr20IsNotNull() {
            addCriterion("attr_20 is not null");
            return (Criteria) this;
        }

        public Criteria andAttr20EqualTo(String value) {
            addCriterion("attr_20 =", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20NotEqualTo(String value) {
            addCriterion("attr_20 <>", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20GreaterThan(String value) {
            addCriterion("attr_20 >", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20GreaterThanOrEqualTo(String value) {
            addCriterion("attr_20 >=", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20LessThan(String value) {
            addCriterion("attr_20 <", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20LessThanOrEqualTo(String value) {
            addCriterion("attr_20 <=", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20Like(String value) {
            addCriterion("attr_20 like", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20NotLike(String value) {
            addCriterion("attr_20 not like", value, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20In(List<String> values) {
            addCriterion("attr_20 in", values, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20NotIn(List<String> values) {
            addCriterion("attr_20 not in", values, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20Between(String value1, String value2) {
            addCriterion("attr_20 between", value1, value2, "attr20");
            return (Criteria) this;
        }

        public Criteria andAttr20NotBetween(String value1, String value2) {
            addCriterion("attr_20 not between", value1, value2, "attr20");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CREATED_BY like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CREATED_BY not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("CREATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("CREATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("CREATED_AT =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("CREATED_AT <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("CREATED_AT >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("CREATED_AT <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("CREATED_AT in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("CREATED_AT not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("UPDATED_BY =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("UPDATED_BY <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("UPDATED_BY >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("UPDATED_BY <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("UPDATED_BY like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("UPDATED_BY not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("UPDATED_BY in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("UPDATED_BY not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("UPDATED_BY between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("UPDATED_BY not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("UPDATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("UPDATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("UPDATED_AT =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("UPDATED_AT <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("UPDATED_AT >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("UPDATED_AT <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("UPDATED_AT in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("UPDATED_AT not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeIsNull() {
            addCriterion("asset_type_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeIsNotNull() {
            addCriterion("asset_type_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeEqualTo(String value) {
            addCriterion("asset_type_code =", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeNotEqualTo(String value) {
            addCriterion("asset_type_code <>", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeGreaterThan(String value) {
            addCriterion("asset_type_code >", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("asset_type_code >=", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeLessThan(String value) {
            addCriterion("asset_type_code <", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeLessThanOrEqualTo(String value) {
            addCriterion("asset_type_code <=", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeLike(String value) {
            addCriterion("asset_type_code like", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeNotLike(String value) {
            addCriterion("asset_type_code not like", value, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeIn(List<String> values) {
            addCriterion("asset_type_code in", values, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeNotIn(List<String> values) {
            addCriterion("asset_type_code not in", values, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeBetween(String value1, String value2) {
            addCriterion("asset_type_code between", value1, value2, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeCodeNotBetween(String value1, String value2) {
            addCriterion("asset_type_code not between", value1, value2, "assetTypeCode");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameIsNull() {
            addCriterion("asset_type_name is null");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameIsNotNull() {
            addCriterion("asset_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameEqualTo(String value) {
            addCriterion("asset_type_name =", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameNotEqualTo(String value) {
            addCriterion("asset_type_name <>", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameGreaterThan(String value) {
            addCriterion("asset_type_name >", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("asset_type_name >=", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameLessThan(String value) {
            addCriterion("asset_type_name <", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameLessThanOrEqualTo(String value) {
            addCriterion("asset_type_name <=", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameLike(String value) {
            addCriterion("asset_type_name like", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameNotLike(String value) {
            addCriterion("asset_type_name not like", value, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameIn(List<String> values) {
            addCriterion("asset_type_name in", values, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameNotIn(List<String> values) {
            addCriterion("asset_type_name not in", values, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameBetween(String value1, String value2) {
            addCriterion("asset_type_name between", value1, value2, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andAssetTypeNameNotBetween(String value1, String value2) {
            addCriterion("asset_type_name not between", value1, value2, "assetTypeName");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}