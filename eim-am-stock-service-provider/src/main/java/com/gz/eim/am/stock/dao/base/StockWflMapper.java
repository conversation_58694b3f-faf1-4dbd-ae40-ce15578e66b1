package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockWfl;
import com.gz.eim.am.stock.entity.StockWflExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockWflMapper {
    long countByExample(StockWflExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockWfl record);

    int insertSelective(StockWfl record);

    List<StockWfl> selectByExample(StockWflExample example);

    StockWfl selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockWfl record, @Param("example") StockWflExample example);

    int updateByExample(@Param("record") StockWfl record, @Param("example") StockWflExample example);

    int updateByPrimaryKeySelective(StockWfl record);

    int updateByPrimaryKey(StockWfl record);
}