package com.gz.eim.am.stock.entity.ambase;

import java.math.BigDecimal;

public class CuxGetfadetailTbl {
    private String assetNumber;

    private String gsName;

    private String tagNumber;

    private String serialNumber;

    private String periodName;

    private String description;

    private String fimsClassB;

    private String fimsClassBName;

    private String fimsClassL;

    private String fimsClassLName;

    private String faKeyWord;

    private String modelNumber;

    private String manufacturerName;

    private String retiredType;

    private String retiredDate;

    private String flsegment1;

    private String usedCompany;

    private String flsegment2;

    private String usedDepartment;

    private String flsegment3;

    private String fullName;

    private String additionDate;

    private String datePlacedInService;

    private Integer lifeInYear;

    private String deprnMethodCode;

    private BigDecimal cost;

    private BigDecimal deprnAmount;

    private BigDecimal ytdDeprn;

    private BigDecimal deprnReserve;

    private BigDecimal lastCost;

    private String deprnType;

    private String surplusYear;

    private Long pzNo;

    private String concatenatedSegments;

    public String getAssetNumber() {
        return assetNumber;
    }

    public void setAssetNumber(String assetNumber) {
        this.assetNumber = assetNumber == null ? null : assetNumber.trim();
    }

    public String getGsName() {
        return gsName;
    }

    public void setGsName(String gsName) {
        this.gsName = gsName == null ? null : gsName.trim();
    }

    public String getTagNumber() {
        return tagNumber;
    }

    public void setTagNumber(String tagNumber) {
        this.tagNumber = tagNumber == null ? null : tagNumber.trim();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber == null ? null : serialNumber.trim();
    }

    public String getPeriodName() {
        return periodName;
    }

    public void setPeriodName(String periodName) {
        this.periodName = periodName == null ? null : periodName.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getFimsClassB() {
        return fimsClassB;
    }

    public void setFimsClassB(String fimsClassB) {
        this.fimsClassB = fimsClassB == null ? null : fimsClassB.trim();
    }

    public String getFimsClassBName() {
        return fimsClassBName;
    }

    public void setFimsClassBName(String fimsClassBName) {
        this.fimsClassBName = fimsClassBName == null ? null : fimsClassBName.trim();
    }

    public String getFimsClassL() {
        return fimsClassL;
    }

    public void setFimsClassL(String fimsClassL) {
        this.fimsClassL = fimsClassL == null ? null : fimsClassL.trim();
    }

    public String getFimsClassLName() {
        return fimsClassLName;
    }

    public void setFimsClassLName(String fimsClassLName) {
        this.fimsClassLName = fimsClassLName == null ? null : fimsClassLName.trim();
    }

    public String getFaKeyWord() {
        return faKeyWord;
    }

    public void setFaKeyWord(String faKeyWord) {
        this.faKeyWord = faKeyWord == null ? null : faKeyWord.trim();
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber == null ? null : modelNumber.trim();
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName == null ? null : manufacturerName.trim();
    }

    public String getRetiredType() {
        return retiredType;
    }

    public void setRetiredType(String retiredType) {
        this.retiredType = retiredType == null ? null : retiredType.trim();
    }

    public String getRetiredDate() {
        return retiredDate;
    }

    public void setRetiredDate(String retiredDate) {
        this.retiredDate = retiredDate == null ? null : retiredDate.trim();
    }

    public String getFlsegment1() {
        return flsegment1;
    }

    public void setFlsegment1(String flsegment1) {
        this.flsegment1 = flsegment1 == null ? null : flsegment1.trim();
    }

    public String getUsedCompany() {
        return usedCompany;
    }

    public void setUsedCompany(String usedCompany) {
        this.usedCompany = usedCompany == null ? null : usedCompany.trim();
    }

    public String getFlsegment2() {
        return flsegment2;
    }

    public void setFlsegment2(String flsegment2) {
        this.flsegment2 = flsegment2 == null ? null : flsegment2.trim();
    }

    public String getUsedDepartment() {
        return usedDepartment;
    }

    public void setUsedDepartment(String usedDepartment) {
        this.usedDepartment = usedDepartment == null ? null : usedDepartment.trim();
    }

    public String getFlsegment3() {
        return flsegment3;
    }

    public void setFlsegment3(String flsegment3) {
        this.flsegment3 = flsegment3 == null ? null : flsegment3.trim();
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName == null ? null : fullName.trim();
    }

    public String getAdditionDate() {
        return additionDate;
    }

    public void setAdditionDate(String additionDate) {
        this.additionDate = additionDate == null ? null : additionDate.trim();
    }

    public String getDatePlacedInService() {
        return datePlacedInService;
    }

    public void setDatePlacedInService(String datePlacedInService) {
        this.datePlacedInService = datePlacedInService == null ? null : datePlacedInService.trim();
    }

    public Integer getLifeInYear() {
        return lifeInYear;
    }

    public void setLifeInYear(Integer lifeInYear) {
        this.lifeInYear = lifeInYear;
    }

    public String getDeprnMethodCode() {
        return deprnMethodCode;
    }

    public void setDeprnMethodCode(String deprnMethodCode) {
        this.deprnMethodCode = deprnMethodCode == null ? null : deprnMethodCode.trim();
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public BigDecimal getDeprnAmount() {
        return deprnAmount;
    }

    public void setDeprnAmount(BigDecimal deprnAmount) {
        this.deprnAmount = deprnAmount;
    }

    public BigDecimal getYtdDeprn() {
        return ytdDeprn;
    }

    public void setYtdDeprn(BigDecimal ytdDeprn) {
        this.ytdDeprn = ytdDeprn;
    }

    public BigDecimal getDeprnReserve() {
        return deprnReserve;
    }

    public void setDeprnReserve(BigDecimal deprnReserve) {
        this.deprnReserve = deprnReserve;
    }

    public BigDecimal getLastCost() {
        return lastCost;
    }

    public void setLastCost(BigDecimal lastCost) {
        this.lastCost = lastCost;
    }

    public String getDeprnType() {
        return deprnType;
    }

    public void setDeprnType(String deprnType) {
        this.deprnType = deprnType == null ? null : deprnType.trim();
    }

    public String getSurplusYear() {
        return surplusYear;
    }

    public void setSurplusYear(String surplusYear) {
        this.surplusYear = surplusYear == null ? null : surplusYear.trim();
    }

    public Long getPzNo() {
        return pzNo;
    }

    public void setPzNo(Long pzNo) {
        this.pzNo = pzNo;
    }

    public String getConcatenatedSegments() {
        return concatenatedSegments;
    }

    public void setConcatenatedSegments(String concatenatedSegments) {
        this.concatenatedSegments = concatenatedSegments == null ? null : concatenatedSegments.trim();
    }
}