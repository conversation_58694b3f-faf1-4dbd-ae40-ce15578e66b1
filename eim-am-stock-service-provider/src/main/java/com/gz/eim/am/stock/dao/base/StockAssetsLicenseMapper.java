package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetsLicense;
import com.gz.eim.am.stock.entity.StockAssetsLicenseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetsLicenseMapper {
    long countByExample(StockAssetsLicenseExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetsLicense record);

    int insertSelective(StockAssetsLicense record);

    List<StockAssetsLicense> selectByExample(StockAssetsLicenseExample example);

    StockAssetsLicense selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetsLicense record, @Param("example") StockAssetsLicenseExample example);

    int updateByExample(@Param("record") StockAssetsLicense record, @Param("example") StockAssetsLicenseExample example);

    int updateByPrimaryKeySelective(StockAssetsLicense record);

    int updateByPrimaryKey(StockAssetsLicense record);
}