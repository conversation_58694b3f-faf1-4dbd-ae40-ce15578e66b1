package com.gz.eim.am.stock.dao.inventory.plan;

import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryPlanAssetRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/7/2
 * @description
 */
public interface InventoryInPlanLinesAssetsMapper {

    /**
     * 根据参数获取计划入库单关联资产
     * @param stockInventoryInPlanLinesAssets
     * @return
     */
    List<InventoryPlanAssetRespDTO> selectInventoryPlanAssetRespDTO(StockInventoryInPlanLinesAssets stockInventoryInPlanLinesAssets);

    /**
     * 批量插入计划入库单行
     * @param stockInventoryInPlanLinesAssetsList
     * @return
     */
    int batchInsertStockInventoryPlanLineAssets(List<StockInventoryInPlanLinesAssets> stockInventoryInPlanLinesAssetsList);

    /**
     * 根据计划入库单行查询管理资产行数
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    Long selectCountByParam(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 查询计划入库单行下的资产
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    List<InventoryInPlanLineAssetRespDTO> selectByPage(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 查询行下的资产
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    List<InventoryInPlanLineAssetRespDTO> selectLineAssets(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 根据id批量更新行下资产表的入库状态
     * @param empId
     * @param ids
     * @return
     */
    int batchUpdateStockInventoryPlanLineAssets(@Param("status")Integer status, @Param("empId")String empId, @Param("ids")List<Long> ids);

    /**
     * 根据计划入库单头查询管理资产行数
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    Long selectLineAssetsCountByParam(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

     /**
       * @param: list
       * @description: 批量插入逻辑
       * @return: int
       * @author: <EMAIL>
       * @date: 2023/3/3
       */
    int batchUpdate(List<StockInventoryInPlanLinesAssets> list);
}
