package com.gz.eim.am.stock.web.check;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.api.check.StockCheckRemindApi;
import com.gz.eim.am.stock.dto.request.check.StockCheckQueryRemindLeaderCheckDetailReqDTO;
import com.gz.eim.am.stock.dto.request.check.StockCheckSendCheckRemindPopFrameReqDTO;
import com.gz.eim.am.stock.dto.response.check.StockCheckQueryRemindLeaderCheckDetailHeadRespDTO;
import com.gz.eim.am.stock.service.check.StockCheckRemindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * @className: StockCheckRemindController
 * @description: 资产盘点提醒Controller
 * @author: <EMAIL>
 * @date: 2023/11/22
 **/
@Slf4j
@RequestMapping("/api/am/stock/check/remind")
@RestController
public class StockCheckRemindController implements StockCheckRemindApi {
    @Autowired
    private StockCheckRemindService stockCheckRemindService;
    /**
     * @param: checkDate
     * @description: 发送盘点存疑数据Excel给行政
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/22
     */
    @Override
    public ResponseData sendCheckInDoubtDataExcelToAdministration(String checkDate) {
        log.info("/api/am/stock/check/remind/sendCheckInDoubtDataExcelToAdministration,checkDate;{}", checkDate);
        ResponseData responseData;
        try {
            responseData  = stockCheckRemindService.sendCheckInDoubtDataExcelToAdministration(checkDate);
        } catch (Exception e){
            log.error ("发送盘点存疑数据Excel给行政，出现异常，异常信息为：{}", e.getMessage());
            responseData = ResponseData.createFailResult("系统错误");
        }
        return responseData;
    }

    /**
     * @param: days
     * @description: 发送盘点提醒消息给员工
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/22
     */
    @Override
    public ResponseData sendCheckRemindMessageToEmployee(Integer days) {
        log.info("/api/am/stock/check/remind/sendCheckRemindMessageToEmployee,days:{}", days);
        ResponseData responseData;
        try {
            responseData  = stockCheckRemindService.sendCheckRemindMessageToEmployee(days);
        } catch (Exception e){
            log.error ("发送盘点存疑数据Excel给行政，出现异常，异常信息为：{}", e.getMessage());
            responseData = ResponseData.createFailResult("系统错误");
        }
        return responseData;
    }

    /**
     * @param: supvLvlId
     * @description: 发送盘点提醒给领导
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    @Override
    public ResponseData sendCheckRemindToLeader(String supvLvlId) {
        log.info("/api/am/stock/check/remind/sendCheckRemindToLeader,supvLvlId:{}", supvLvlId);
        ResponseData responseData;
        try {
            responseData  = stockCheckRemindService.sendCheckRemindToLeader(supvLvlId);
        } catch (Exception e){
            log.error ("发送盘点提醒给领导，出现异常，异常信息为：{}", e.getMessage());
            responseData = ResponseData.createFailResult("系统错误");
        }
        return responseData;
    }
    /**
     * @param: stockCheckQueryRemindLeaderCheckDetailReqDTO
     * @description: 查询提醒领导盘点详情
     * @return: ResponseData<StockCheckQueryRemindLeaderCheckDetailHeadRespDTO>
     * @author: <EMAIL>
     * @date: 2023/11/23
     */
    @Override
    public ResponseData<StockCheckQueryRemindLeaderCheckDetailHeadRespDTO> queryRemindLeaderCheckDetail(StockCheckQueryRemindLeaderCheckDetailReqDTO stockCheckQueryRemindLeaderCheckDetailReqDTO) {
        log.info("/api/am/stock/check/remind/queryRemindLeaderCheckDetail,stockCheckQueryRemindLeaderCheckDetailReqDTO:{}", JSON.toJSONString(stockCheckQueryRemindLeaderCheckDetailReqDTO));
        ResponseData responseData;
        try {
            responseData  = stockCheckRemindService.queryRemindLeaderCheckDetail(stockCheckQueryRemindLeaderCheckDetailReqDTO);
        } catch (Exception e){
            log.error ("发送盘点提醒给领导，出现异常，异常信息为：", e);
            responseData = ResponseData.createFailResult("系统错误");
        }
        return responseData;
    }

    @Override
    public ResponseData sendCheckRemindPopFrameToNotCheckPeople(StockCheckSendCheckRemindPopFrameReqDTO stockCheckSendCheckRemindPopFrameReqDTO) {
        log.info("/api/am/stock/check/remind/sendCheckRemindPopFrameToNotCheckPeople,stockCheckSendCheckRemindPopFrameReqDTO:{}", JSON.toJSONString(stockCheckSendCheckRemindPopFrameReqDTO));
        ResponseData responseData;
        try {
            responseData  = stockCheckRemindService.sendCheckRemindPopFrameToNotCheckPeople(stockCheckSendCheckRemindPopFrameReqDTO);
        } catch (Exception e){
            log.error ("发送盘点提醒弹框请求，出现异常，异常信息为：{}", e.getMessage());
            responseData = ResponseData.createFailResult("系统错误");
        }
        return responseData;
    }

}
