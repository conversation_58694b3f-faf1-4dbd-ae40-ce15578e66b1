package com.gz.eim.am.stock.dao.assets;

import com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.util.List;

/**
 * @author: weijun<PERSON>e
 * @date: 2021/4/7
 * @description
 */
public interface StockAssetsSyncToEbsChangeMapper {

    /**
     * 抽取领用变更资产
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSyncChange> extractReceiveAssets(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 抽取归还变更资产
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSyncChange> extractReturnAssets(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 抽取调出资产
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSyncChange> extractTransOut(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 抽取调入资产
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSyncChange> extractTransIn(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 抽取结算资产
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSyncChange> extractAccount(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 抽取领用人变更资产
     * @param startDate
     * @param endDate
     * @return
     */
    List<StockAssetsEbsSyncChange> extractHolderChange(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 批量插入数据
     * @param subStockAssetsEbsSyncList
     * @return
     */
    int batchInsert(List<StockAssetsEbsSyncChange> subStockAssetsEbsSyncList);

    /**
     * 批量更新
     * @param stockAssetsEbsSyncChangeList
     * @return
     */
    Integer updateMultipleSelective(final List<StockAssetsEbsSyncChange> stockAssetsEbsSyncChangeList);

    /**
     * 查询所有待查询结果的查询码
     * @param syncStatus
     * @return
     */
    List<String> getQueryCodeList(List<Integer> syncStatus);
}
