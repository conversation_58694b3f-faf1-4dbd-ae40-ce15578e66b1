package com.gz.eim.am.stock.web.assets;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.RedisUtil;
import com.gz.eim.am.stock.api.assets.StockAssetsCompensationApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsCompensationRecordReqDTO;
import com.gz.eim.am.stock.dto.response.assets.StockAssetsCompensationRecordRespDTO;
import com.gz.eim.am.stock.service.assets.StockAssetsCompensationService;
import com.gz.eim.am.stock.service.assets.StockAssetsLeaveNoReturnService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: yangjifan1
 * @date: 2022/3/29
 */
@RestController
@RequestMapping("/api/am/stock/assets/compensation")
@Slf4j
public class StockAssetsCompensationController implements StockAssetsCompensationApi {

    @Autowired
    private RedisUtil redisUtil;
    @Value("${namespace.name}")
    private String nameSpace;
    @Autowired
    private StockAssetsCompensationService stockAssetsCompensationService;
    @Autowired
    private StockAssetsLeaveNoReturnService stockAssetsLeaveNoReturnService;


    @Override
    public ResponseData leaveNoReturnAssets(String leaveDateStr) {
        log.info("/api/am/stock/assets/compensation/leaveNoReturnAssets,leaveDateStr={}",leaveDateStr);
        ResponseData responseData = null;
        try {
            responseData = stockAssetsLeaveNoReturnService.getLeaveNoReturnAssets(leaveDateStr);
        }catch (Exception e){
            log.error("获取离职人员未归还资产异常",e);
            responseData = ResponseData.createFailResult(e.getMessage());
        }
        return responseData;
    }

    @Override
    public ResponseData queryLastMonthNoReturnAssetsAndSendEmail() {
        log.info("/api/am/stock/assets/compensation/queryLastMonthNoReturnAssetsAndSendEmail");
        ResponseData responseData = null;
        try {
            responseData = stockAssetsLeaveNoReturnService.queryLastMonthNoReturnAssetsAndSendEmail();
        }catch (Exception e){
            log.error("检索职日期为上个月1号到xx号范围内的员工名下未归还资产且赔偿金额为0的数据，并发送邮件提醒异常",e);
            responseData = ResponseData.createFailResult(e.getMessage());
        }
        return responseData;
    }

    /**
     * @param: leaveUser
     * @description: 审批流查询离职人对应的审批人
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/4/11
     */
    @Override
    public ResponseData queryLeaveApproveUserByWfl(String leaveUser) {
        log.info("/api/am/stock/assets/compensation/queryLeaveApproveUserByWfl,leaveUser={}",leaveUser);
        ResponseData res = null;
        try {
            res = stockAssetsCompensationService.queryLeaveApproveUserByWfl(leaveUser);
        }catch (Exception e){
            log.error("查询资产赔偿记录表异常",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryAssetsCompensationRecordList(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/queryAssetsCompensationRecordList,stockAssetsCompensationRecordReqDTO={}",JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try {
            res = stockAssetsCompensationService.queryAssetsCompensationRecordPage(stockAssetsCompensationRecordReqDTO);
        }catch (Exception e){
            log.error("查询资产赔偿记录表异常",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData updateAssetsCompensationRecord(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/updateAssetsCompensationRecord,stockAssetsCompensationRecordReqDTO={}",JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try {
            res = stockAssetsCompensationService.updateAssetsCompensationRecord(stockAssetsCompensationRecordReqDTO);
        }catch (Exception e){
            log.error("修改资产赔偿记录表异常",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * 手动推送hr扣款数据记录
     * @param stockAssetsCompensationRecordReqDTO
     * @return
     */
    @Override
    public ResponseData manualPushHrCompensationData(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/manualPushHrCompensationData,stockAssetsCompensationRecordReqDTO={}",JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try{
            res = stockAssetsCompensationService.manualPushHrCompensationData(stockAssetsCompensationRecordReqDTO);
        }catch (Exception e){
            log.error("手动推送hr扣款数据记录",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData importAssetsCompensationRecord(MultipartFile file) {
        if (file == null) {
            return ResponseData.createFailResult("未上传文件");
        }
        ResponseData res = null;
        try{
            res = stockAssetsCompensationService.importAssetsCompensationRecord(file);
        }catch (Exception e){
            log.error("导入资产赔偿记录表异常",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData confirmImportAssetsCompensationRecord(String batchCode) {
        log.info("/api/am/stock/assets/compensation/confirmImportAssetsCompensationRecord,batchCode={}",batchCode);
        ResponseData res = null;
        try {
            res = stockAssetsCompensationService.confirmImportAssetsCompensationRecord(batchCode);
        }catch (Exception e){
            log.error("确认导入资产赔偿记录表异常",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    @Override
    public ResponseData exportAssetsCompensationRecord(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO, HttpServletRequest request, HttpServletResponse response) {
        log.info("/api/am/stock/assets/compensation/exportAssetsCompensationRecord,stockAssetsCompensationRecordReqDTO={}",JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try {
            res = stockAssetsCompensationService.exportAssetsCompensationRecord(stockAssetsCompensationRecordReqDTO,request,response);
        }catch (Exception e){
            log.error("资产赔偿记录导出异常",e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 离职查询人员名下资产
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public ResponseData<StockAssetsCompensationRecordRespDTO> queryCompensationAssets(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/queryCompensationAssets,stockAssetsCompensationRecordReqDTO{}", JSON.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try {
            res = this.stockAssetsCompensationService.queryCompensationAssets(stockAssetsCompensationRecordReqDTO);
        } catch (Exception e) {
            log.error("离职查询人员名下资产异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 提交资产赔款信息
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public ResponseData submitAssetsCompensationRecord(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/submitAssetsCompensationRecord,stockAssetsCompensationRecordReqDTO{}", JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res;
        if(null == stockAssetsCompensationRecordReqDTO){
            return ResponseData.createSuccessResult("请求参数不能为空");
        }
        String formId = stockAssetsCompensationRecordReqDTO.getFormId();
        if(StringUtils.isBlank(formId)){
            return ResponseData.createSuccessResult("流程单号不能为空");
        }
        // 获取redisKey
        String redisKey = RedisKeyConstants.STOCK_ASSETS_COMPENSATION_SUBMIT + formId;
        if (redisUtil.setNx (nameSpace, redisKey, CommonConstant.DEFAULT_LOCK_NAME)) {
            redisUtil.setEx(nameSpace, redisKey, CommonConstant.DEFAULT_LOCK_NAME, CommonConstant.NUMBER_30, TimeUnit.SECONDS);
            try {
                res = this.stockAssetsCompensationService.submitAssetsCompensationRecord(stockAssetsCompensationRecordReqDTO);
            } catch (Exception e) {
                log.error("提交资产赔款信息异常",e);
                res = ResponseData.createFailResult (e.getMessage());
            }
        } else {
            res = ResponseData.createFailResult ("请勿重复点击...");
        }
        return res;
    }

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 审批流回查资产赔偿情况
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public ResponseData queryWflAssetsCompensationRecordList(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/queryWflAssetsCompensationRecordList,stockAssetsCompensationRecordReqDTO{}", JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try {
            res = this.stockAssetsCompensationService.queryWflAssetsCompensationRecordList(stockAssetsCompensationRecordReqDTO);
        } catch (Exception e) {
            log.error("审批流回查资产赔偿情况异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * @param: stockAssetsCompensationRecordReqDTO
     * @description: 处理hr下放的离职人员
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/2/15
     */
    @Override
    public ResponseData dealHrDevolveLeaveUser(StockAssetsCompensationRecordReqDTO stockAssetsCompensationRecordReqDTO) {
        log.info("/api/am/stock/assets/compensation/dealHrDevolveLeaveUser,stockAssetsCompensationRecordReqDTO{}", JSONObject.toJSONString(stockAssetsCompensationRecordReqDTO));
        ResponseData res = null;
        try {
            res = this.stockAssetsCompensationService.dealHrDevolveLeaveUser(stockAssetsCompensationRecordReqDTO);
        } catch (Exception e) {
            log.error("处理hr下放的离职人员异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData deleteAssetsCompensationRecord(Long id) {
        log.info("/api/am/stock/assets/compensation/deleteAssetsCompensationRecord,id{}", id);
        ResponseData res = null;
        try {
            res = this.stockAssetsCompensationService.deleteAssetsCompensationRecord(id);
        } catch (Exception e) {
            log.error("删除资产赔偿记录异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
