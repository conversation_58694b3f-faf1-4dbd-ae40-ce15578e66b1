package com.gz.eim.am.stock.service.order;

import com.gz.eim.am.stock.entity.StockDeliveryDetailAsset;
import com.gz.eim.am.stock.entity.StockDeliveryDetailAssetReqDO;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/4/1
 * @description 出库单行关联资产service
 */
public interface  StockDeliveryDetailAssetsService {

    /**
     * 批量插入
     * @param stockDeliveryDetailAssetList
     * @return
     */
    boolean insertMultiple(List<StockDeliveryDetailAsset> stockDeliveryDetailAssetList);

    /**
     * @param: stockDeliveryDetailAssetReqDO
     * @description: 查询实际出库单资产行信息列表
     * @return: List<StockDeliveryDetailAsset>
     * @author: <EMAIL>
     * @date: 2023/6/26
     */
    List<StockDeliveryDetailAsset> selectList(StockDeliveryDetailAssetReqDO stockDeliveryDetailAssetReqDO);

    /**
     * @param: stockDeliveryDetailAssetReqDO
     * @description: 查询实际出库单资产行信息
     * @return: StockDeliveryDetailAsset
     * @author: <EMAIL>
     * @date: 2023/6/26
     */
    StockDeliveryDetailAsset selectOne(StockDeliveryDetailAssetReqDO stockDeliveryDetailAssetReqDO);

    /**
     * @param: stockDeliveryDetailAssetList
     * @description: 批量插入
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/6/27
     */
    int batchUpdate(List<StockDeliveryDetailAsset> stockDeliveryDetailAssetList);
}
