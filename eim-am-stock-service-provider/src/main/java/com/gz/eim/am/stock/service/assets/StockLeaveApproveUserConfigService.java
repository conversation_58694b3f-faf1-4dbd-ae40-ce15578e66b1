package com.gz.eim.am.stock.service.assets;

import com.gz.eim.am.stock.dto.request.assets.StockLeaveApproveUserConfigReqDTO;
import com.gz.eim.am.stock.entity.StockLeaveApproveUserConfig;

import java.util.List;

/**
   * @description: 离职人员配置信息操作
   * @author: <EMAIL>
   * @date: 2022/4/11
   */
public interface StockLeaveApproveUserConfigService {

     /**
       * @param: stockLeaveApproveUserConfigReqDTO
       * @description: 查询离职人员配置信息集合
       * @return: List<StockLeaveApproveUserConfig>
       * @author: <EMAIL>
       * @date: 2022/4/11
       */
    List<StockLeaveApproveUserConfig> selectStockLeaveApproveUserConfigList(StockLeaveApproveUserConfigReqDTO stockLeaveApproveUserConfigReqDTO);

}
