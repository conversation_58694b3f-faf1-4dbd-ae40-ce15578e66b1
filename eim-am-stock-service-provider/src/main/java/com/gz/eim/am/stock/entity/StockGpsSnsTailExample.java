package com.gz.eim.am.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockGpsSnsTailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockGpsSnsTailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSnIdIsNull() {
            addCriterion("sn_id is null");
            return (Criteria) this;
        }

        public Criteria andSnIdIsNotNull() {
            addCriterion("sn_id is not null");
            return (Criteria) this;
        }

        public Criteria andSnIdEqualTo(Long value) {
            addCriterion("sn_id =", value, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdNotEqualTo(Long value) {
            addCriterion("sn_id <>", value, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdGreaterThan(Long value) {
            addCriterion("sn_id >", value, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdGreaterThanOrEqualTo(Long value) {
            addCriterion("sn_id >=", value, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdLessThan(Long value) {
            addCriterion("sn_id <", value, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdLessThanOrEqualTo(Long value) {
            addCriterion("sn_id <=", value, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdIn(List<Long> values) {
            addCriterion("sn_id in", values, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdNotIn(List<Long> values) {
            addCriterion("sn_id not in", values, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdBetween(Long value1, Long value2) {
            addCriterion("sn_id between", value1, value2, "snId");
            return (Criteria) this;
        }

        public Criteria andSnIdNotBetween(Long value1, Long value2) {
            addCriterion("sn_id not between", value1, value2, "snId");
            return (Criteria) this;
        }

        public Criteria andSnNoIsNull() {
            addCriterion("sn_no is null");
            return (Criteria) this;
        }

        public Criteria andSnNoIsNotNull() {
            addCriterion("sn_no is not null");
            return (Criteria) this;
        }

        public Criteria andSnNoEqualTo(String value) {
            addCriterion("sn_no =", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotEqualTo(String value) {
            addCriterion("sn_no <>", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoGreaterThan(String value) {
            addCriterion("sn_no >", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoGreaterThanOrEqualTo(String value) {
            addCriterion("sn_no >=", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLessThan(String value) {
            addCriterion("sn_no <", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLessThanOrEqualTo(String value) {
            addCriterion("sn_no <=", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoLike(String value) {
            addCriterion("sn_no like", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotLike(String value) {
            addCriterion("sn_no not like", value, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoIn(List<String> values) {
            addCriterion("sn_no in", values, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotIn(List<String> values) {
            addCriterion("sn_no not in", values, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoBetween(String value1, String value2) {
            addCriterion("sn_no between", value1, value2, "snNo");
            return (Criteria) this;
        }

        public Criteria andSnNoNotBetween(String value1, String value2) {
            addCriterion("sn_no not between", value1, value2, "snNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoIsNull() {
            addCriterion("inventory_in_plan_no is null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoIsNotNull() {
            addCriterion("inventory_in_plan_no is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoEqualTo(String value) {
            addCriterion("inventory_in_plan_no =", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotEqualTo(String value) {
            addCriterion("inventory_in_plan_no <>", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoGreaterThan(String value) {
            addCriterion("inventory_in_plan_no >", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_in_plan_no >=", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoLessThan(String value) {
            addCriterion("inventory_in_plan_no <", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoLessThanOrEqualTo(String value) {
            addCriterion("inventory_in_plan_no <=", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoLike(String value) {
            addCriterion("inventory_in_plan_no like", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotLike(String value) {
            addCriterion("inventory_in_plan_no not like", value, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoIn(List<String> values) {
            addCriterion("inventory_in_plan_no in", values, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotIn(List<String> values) {
            addCriterion("inventory_in_plan_no not in", values, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoBetween(String value1, String value2) {
            addCriterion("inventory_in_plan_no between", value1, value2, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInPlanNoNotBetween(String value1, String value2) {
            addCriterion("inventory_in_plan_no not between", value1, value2, "inventoryInPlanNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoIsNull() {
            addCriterion("inventory_in_no is null");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoIsNotNull() {
            addCriterion("inventory_in_no is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoEqualTo(String value) {
            addCriterion("inventory_in_no =", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoNotEqualTo(String value) {
            addCriterion("inventory_in_no <>", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoGreaterThan(String value) {
            addCriterion("inventory_in_no >", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_in_no >=", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoLessThan(String value) {
            addCriterion("inventory_in_no <", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoLessThanOrEqualTo(String value) {
            addCriterion("inventory_in_no <=", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoLike(String value) {
            addCriterion("inventory_in_no like", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoNotLike(String value) {
            addCriterion("inventory_in_no not like", value, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoIn(List<String> values) {
            addCriterion("inventory_in_no in", values, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoNotIn(List<String> values) {
            addCriterion("inventory_in_no not in", values, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoBetween(String value1, String value2) {
            addCriterion("inventory_in_no between", value1, value2, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andInventoryInNoNotBetween(String value1, String value2) {
            addCriterion("inventory_in_no not between", value1, value2, "inventoryInNo");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeIsNull() {
            addCriterion("sim_company_code is null");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeIsNotNull() {
            addCriterion("sim_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeEqualTo(String value) {
            addCriterion("sim_company_code =", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeNotEqualTo(String value) {
            addCriterion("sim_company_code <>", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeGreaterThan(String value) {
            addCriterion("sim_company_code >", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sim_company_code >=", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeLessThan(String value) {
            addCriterion("sim_company_code <", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("sim_company_code <=", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeLike(String value) {
            addCriterion("sim_company_code like", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeNotLike(String value) {
            addCriterion("sim_company_code not like", value, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeIn(List<String> values) {
            addCriterion("sim_company_code in", values, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeNotIn(List<String> values) {
            addCriterion("sim_company_code not in", values, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeBetween(String value1, String value2) {
            addCriterion("sim_company_code between", value1, value2, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andSimCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("sim_company_code not between", value1, value2, "simCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeIsNull() {
            addCriterion("serve_company_code is null");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeIsNotNull() {
            addCriterion("serve_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeEqualTo(String value) {
            addCriterion("serve_company_code =", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeNotEqualTo(String value) {
            addCriterion("serve_company_code <>", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeGreaterThan(String value) {
            addCriterion("serve_company_code >", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("serve_company_code >=", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeLessThan(String value) {
            addCriterion("serve_company_code <", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("serve_company_code <=", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeLike(String value) {
            addCriterion("serve_company_code like", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeNotLike(String value) {
            addCriterion("serve_company_code not like", value, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeIn(List<String> values) {
            addCriterion("serve_company_code in", values, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeNotIn(List<String> values) {
            addCriterion("serve_company_code not in", values, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeBetween(String value1, String value2) {
            addCriterion("serve_company_code between", value1, value2, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andServeCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("serve_company_code not between", value1, value2, "serveCompanyCode");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceIsNull() {
            addCriterion("gps_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceIsNotNull() {
            addCriterion("gps_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceEqualTo(BigDecimal value) {
            addCriterion("gps_unit_price =", value, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("gps_unit_price <>", value, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("gps_unit_price >", value, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gps_unit_price >=", value, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceLessThan(BigDecimal value) {
            addCriterion("gps_unit_price <", value, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gps_unit_price <=", value, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceIn(List<BigDecimal> values) {
            addCriterion("gps_unit_price in", values, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("gps_unit_price not in", values, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gps_unit_price between", value1, value2, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gps_unit_price not between", value1, value2, "gpsUnitPrice");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateIsNull() {
            addCriterion("gps_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateIsNotNull() {
            addCriterion("gps_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateEqualTo(BigDecimal value) {
            addCriterion("gps_tax_rate =", value, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("gps_tax_rate <>", value, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateGreaterThan(BigDecimal value) {
            addCriterion("gps_tax_rate >", value, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("gps_tax_rate >=", value, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateLessThan(BigDecimal value) {
            addCriterion("gps_tax_rate <", value, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("gps_tax_rate <=", value, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateIn(List<BigDecimal> values) {
            addCriterion("gps_tax_rate in", values, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("gps_tax_rate not in", values, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gps_tax_rate between", value1, value2, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andGpsTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("gps_tax_rate not between", value1, value2, "gpsTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceIsNull() {
            addCriterion("sim_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceIsNotNull() {
            addCriterion("sim_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceEqualTo(BigDecimal value) {
            addCriterion("sim_unit_price =", value, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("sim_unit_price <>", value, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("sim_unit_price >", value, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_unit_price >=", value, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceLessThan(BigDecimal value) {
            addCriterion("sim_unit_price <", value, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_unit_price <=", value, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceIn(List<BigDecimal> values) {
            addCriterion("sim_unit_price in", values, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("sim_unit_price not in", values, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_unit_price between", value1, value2, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_unit_price not between", value1, value2, "simUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateIsNull() {
            addCriterion("sim_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateIsNotNull() {
            addCriterion("sim_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateEqualTo(BigDecimal value) {
            addCriterion("sim_tax_rate =", value, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("sim_tax_rate <>", value, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateGreaterThan(BigDecimal value) {
            addCriterion("sim_tax_rate >", value, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_tax_rate >=", value, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateLessThan(BigDecimal value) {
            addCriterion("sim_tax_rate <", value, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_tax_rate <=", value, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateIn(List<BigDecimal> values) {
            addCriterion("sim_tax_rate in", values, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("sim_tax_rate not in", values, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_tax_rate between", value1, value2, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_tax_rate not between", value1, value2, "simTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceIsNull() {
            addCriterion("serve_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceIsNotNull() {
            addCriterion("serve_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceEqualTo(BigDecimal value) {
            addCriterion("serve_unit_price =", value, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("serve_unit_price <>", value, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("serve_unit_price >", value, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_unit_price >=", value, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceLessThan(BigDecimal value) {
            addCriterion("serve_unit_price <", value, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_unit_price <=", value, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceIn(List<BigDecimal> values) {
            addCriterion("serve_unit_price in", values, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("serve_unit_price not in", values, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_unit_price between", value1, value2, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_unit_price not between", value1, value2, "serveUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateIsNull() {
            addCriterion("serve_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateIsNotNull() {
            addCriterion("serve_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateEqualTo(BigDecimal value) {
            addCriterion("serve_tax_rate =", value, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("serve_tax_rate <>", value, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateGreaterThan(BigDecimal value) {
            addCriterion("serve_tax_rate >", value, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_tax_rate >=", value, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateLessThan(BigDecimal value) {
            addCriterion("serve_tax_rate <", value, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_tax_rate <=", value, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateIn(List<BigDecimal> values) {
            addCriterion("serve_tax_rate in", values, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("serve_tax_rate not in", values, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_tax_rate between", value1, value2, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_tax_rate not between", value1, value2, "serveTaxRate");
            return (Criteria) this;
        }

        public Criteria andInstallStatusIsNull() {
            addCriterion("install_status is null");
            return (Criteria) this;
        }

        public Criteria andInstallStatusIsNotNull() {
            addCriterion("install_status is not null");
            return (Criteria) this;
        }

        public Criteria andInstallStatusEqualTo(Integer value) {
            addCriterion("install_status =", value, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusNotEqualTo(Integer value) {
            addCriterion("install_status <>", value, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusGreaterThan(Integer value) {
            addCriterion("install_status >", value, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("install_status >=", value, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusLessThan(Integer value) {
            addCriterion("install_status <", value, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusLessThanOrEqualTo(Integer value) {
            addCriterion("install_status <=", value, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusIn(List<Integer> values) {
            addCriterion("install_status in", values, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusNotIn(List<Integer> values) {
            addCriterion("install_status not in", values, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusBetween(Integer value1, Integer value2) {
            addCriterion("install_status between", value1, value2, "installStatus");
            return (Criteria) this;
        }

        public Criteria andInstallStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("install_status not between", value1, value2, "installStatus");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CREATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CREATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CREATED_BY =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CREATED_BY <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CREATED_BY >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CREATED_BY >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CREATED_BY <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CREATED_BY <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CREATED_BY like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CREATED_BY not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CREATED_BY in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CREATED_BY not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CREATED_BY between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CREATED_BY not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("CREATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("CREATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("CREATED_AT =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("CREATED_AT <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("CREATED_AT >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("CREATED_AT <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("CREATED_AT <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("CREATED_AT in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("CREATED_AT not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("CREATED_AT not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("UPDATED_BY is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("UPDATED_BY is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("UPDATED_BY =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("UPDATED_BY <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("UPDATED_BY >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("UPDATED_BY <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("UPDATED_BY <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("UPDATED_BY like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("UPDATED_BY not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("UPDATED_BY in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("UPDATED_BY not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("UPDATED_BY between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("UPDATED_BY not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("UPDATED_AT is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("UPDATED_AT is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("UPDATED_AT =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("UPDATED_AT <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("UPDATED_AT >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("UPDATED_AT <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("UPDATED_AT <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("UPDATED_AT in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("UPDATED_AT not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("UPDATED_AT not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("version like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("version not like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceIsNull() {
            addCriterion("sim_re_new_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceIsNotNull() {
            addCriterion("sim_re_new_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_unit_price =", value, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_unit_price <>", value, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("sim_re_new_unit_price >", value, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_unit_price >=", value, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceLessThan(BigDecimal value) {
            addCriterion("sim_re_new_unit_price <", value, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_unit_price <=", value, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceIn(List<BigDecimal> values) {
            addCriterion("sim_re_new_unit_price in", values, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("sim_re_new_unit_price not in", values, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_re_new_unit_price between", value1, value2, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_re_new_unit_price not between", value1, value2, "simReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateIsNull() {
            addCriterion("sim_re_new_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateIsNotNull() {
            addCriterion("sim_re_new_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_tax_rate =", value, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_tax_rate <>", value, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateGreaterThan(BigDecimal value) {
            addCriterion("sim_re_new_tax_rate >", value, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_tax_rate >=", value, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateLessThan(BigDecimal value) {
            addCriterion("sim_re_new_tax_rate <", value, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_re_new_tax_rate <=", value, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateIn(List<BigDecimal> values) {
            addCriterion("sim_re_new_tax_rate in", values, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("sim_re_new_tax_rate not in", values, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_re_new_tax_rate between", value1, value2, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimReNewTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_re_new_tax_rate not between", value1, value2, "simReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceIsNull() {
            addCriterion("serve_re_new_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceIsNotNull() {
            addCriterion("serve_re_new_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_unit_price =", value, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_unit_price <>", value, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("serve_re_new_unit_price >", value, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_unit_price >=", value, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceLessThan(BigDecimal value) {
            addCriterion("serve_re_new_unit_price <", value, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_unit_price <=", value, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceIn(List<BigDecimal> values) {
            addCriterion("serve_re_new_unit_price in", values, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("serve_re_new_unit_price not in", values, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_re_new_unit_price between", value1, value2, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_re_new_unit_price not between", value1, value2, "serveReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateIsNull() {
            addCriterion("serve_re_new_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateIsNotNull() {
            addCriterion("serve_re_new_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_tax_rate =", value, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_tax_rate <>", value, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateGreaterThan(BigDecimal value) {
            addCriterion("serve_re_new_tax_rate >", value, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_tax_rate >=", value, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateLessThan(BigDecimal value) {
            addCriterion("serve_re_new_tax_rate <", value, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_re_new_tax_rate <=", value, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateIn(List<BigDecimal> values) {
            addCriterion("serve_re_new_tax_rate in", values, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("serve_re_new_tax_rate not in", values, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_re_new_tax_rate between", value1, value2, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeReNewTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_re_new_tax_rate not between", value1, value2, "serveReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceIsNull() {
            addCriterion("sim_group_re_new_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceIsNotNull() {
            addCriterion("sim_group_re_new_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_unit_price =", value, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_unit_price <>", value, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("sim_group_re_new_unit_price >", value, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_unit_price >=", value, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceLessThan(BigDecimal value) {
            addCriterion("sim_group_re_new_unit_price <", value, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_unit_price <=", value, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceIn(List<BigDecimal> values) {
            addCriterion("sim_group_re_new_unit_price in", values, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("sim_group_re_new_unit_price not in", values, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_group_re_new_unit_price between", value1, value2, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_group_re_new_unit_price not between", value1, value2, "simGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateIsNull() {
            addCriterion("sim_group_re_new_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateIsNotNull() {
            addCriterion("sim_group_re_new_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_tax_rate =", value, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_tax_rate <>", value, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateGreaterThan(BigDecimal value) {
            addCriterion("sim_group_re_new_tax_rate >", value, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_tax_rate >=", value, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateLessThan(BigDecimal value) {
            addCriterion("sim_group_re_new_tax_rate <", value, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sim_group_re_new_tax_rate <=", value, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateIn(List<BigDecimal> values) {
            addCriterion("sim_group_re_new_tax_rate in", values, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("sim_group_re_new_tax_rate not in", values, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_group_re_new_tax_rate between", value1, value2, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andSimGroupReNewTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sim_group_re_new_tax_rate not between", value1, value2, "simGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceIsNull() {
            addCriterion("serve_group_re_new_unit_price is null");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceIsNotNull() {
            addCriterion("serve_group_re_new_unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_unit_price =", value, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_unit_price <>", value, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("serve_group_re_new_unit_price >", value, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_unit_price >=", value, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceLessThan(BigDecimal value) {
            addCriterion("serve_group_re_new_unit_price <", value, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_unit_price <=", value, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceIn(List<BigDecimal> values) {
            addCriterion("serve_group_re_new_unit_price in", values, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("serve_group_re_new_unit_price not in", values, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_group_re_new_unit_price between", value1, value2, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_group_re_new_unit_price not between", value1, value2, "serveGroupReNewUnitPrice");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateIsNull() {
            addCriterion("serve_group_re_new_tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateIsNotNull() {
            addCriterion("serve_group_re_new_tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_tax_rate =", value, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_tax_rate <>", value, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateGreaterThan(BigDecimal value) {
            addCriterion("serve_group_re_new_tax_rate >", value, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_tax_rate >=", value, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateLessThan(BigDecimal value) {
            addCriterion("serve_group_re_new_tax_rate <", value, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("serve_group_re_new_tax_rate <=", value, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateIn(List<BigDecimal> values) {
            addCriterion("serve_group_re_new_tax_rate in", values, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("serve_group_re_new_tax_rate not in", values, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_group_re_new_tax_rate between", value1, value2, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }

        public Criteria andServeGroupReNewTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("serve_group_re_new_tax_rate not between", value1, value2, "serveGroupReNewTaxRate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}