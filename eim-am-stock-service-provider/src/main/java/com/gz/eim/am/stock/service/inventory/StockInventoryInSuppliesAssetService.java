package com.gz.eim.am.stock.service.inventory;

import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInSuppliesAsset;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/21
 * @description:
 */
public interface StockInventoryInSuppliesAssetService {
    /**
     * 根据入库单行id查询数量
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    Long selectCountByParam(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 分页查询
     * @param inventoryInPlanLineReqDTO
     * @return
     */
    List<InventoryInPlanLineAssetRespDTO> selectByPage(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO);

    /**
     * 批量插入
     * @param stockInventoryInSuppliesAssetList
     * @return
     */
    boolean batchInsert(List<StockInventoryInSuppliesAsset> stockInventoryInSuppliesAssetList);

    /**
     * 根据入库单行号查询关联资产编号列表
     * @param inSuppliesId
     * @return
     */
    List<StockInventoryInSuppliesAsset> selectAssetsByInSuppliesId(Long inSuppliesId);
}
