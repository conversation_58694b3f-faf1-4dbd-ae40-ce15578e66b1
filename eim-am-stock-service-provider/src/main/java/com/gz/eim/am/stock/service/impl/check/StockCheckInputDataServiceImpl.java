package com.gz.eim.am.stock.service.impl.check;

import com.alibaba.fastjson.JSONObject;
import com.fuu.eim.fin.base.dto.response.ApvTaskRespDTO;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.base.dto.request.file.QueryFileReqDTO;
import com.gz.eim.am.base.dto.request.file.SysAttachReqDTO;
import com.gz.eim.am.base.dto.response.file.CategoryPictureMetaRespDTO;
import com.gz.eim.am.base.dto.response.file.PictureMetaRespDTO;
import com.gz.eim.am.common.util.ConvertUtil;
import com.gz.eim.am.stock.constant.*;
import com.gz.eim.am.stock.dao.base.StockAssetsCheckMethodConfigMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsCheckTaskDetailMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsCheckTaskMapper;
import com.gz.eim.am.stock.dao.base.StockAssetsMapper;
import com.gz.eim.am.stock.dao.check.StockCheckMissionDetailMapper;
import com.gz.eim.am.stock.dao.check.StockCheckMissionMapper;
import com.gz.eim.am.stock.dto.request.ambase.ApvTaskReqDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsDTO;
import com.gz.eim.am.stock.dto.request.assets.AssetsSearchDTO;
import com.gz.eim.am.stock.dto.request.check.*;
import com.gz.eim.am.stock.dto.response.PageRespDTO;
import com.gz.eim.am.stock.dto.response.address.AddressProvinceRespDTO;
import com.gz.eim.am.stock.dto.response.check.*;
import com.gz.eim.am.stock.dto.response.warehouse.WarehouseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.*;
import com.gz.eim.am.stock.entity.vo.StockBeanMap;
import com.gz.eim.am.stock.entity.vo.WflInfo;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.ambase.ApvTaskService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.check.*;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.service.wfl.WflService;
import com.gz.eim.am.stock.util.common.ChineseCharacterUtil;
import com.gz.eim.am.stock.util.common.CommonUtil;
import com.gz.eim.am.stock.util.common.FiledCompareUtil;
import com.gz.eim.am.stock.util.common.WorkFlowUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: wangjing67
 * @Date: 11/20/20 2:37 下午
 * @description
 */
@Slf4j
@Service
public class StockCheckInputDataServiceImpl implements StockCheckInputDataService {


    @Value("${namespace.name}")
    private String nameSpace;
    @Value("${project.file.systemModule}")
    private String systemModule;
    @Value("${project.file.attachModule}")
    private String attachModule;
    @Value("${project.file.filePath}")
    private String folderPath;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private StockCheckMissionDetailMapper stockCheckMissionDetailMapper;
    @Autowired
    private StockAssetsCheckTaskMapper stockAssetsCheckTaskMapper;
    @Autowired
    private StockTakingPlanService stockTakingPlanService;
    @Autowired
    private StockCheckMissionDetailService stockCheckMissionDetailService;
    @Autowired
    private StockAssetsCheckTaskDetailMapper stockAssetsCheckTaskDetailMapper;
    @Autowired
    private StockAssetsMapper stockAssetsMapper;
    @Autowired
    private StockWarehouseService stockWarehouseService;
    @Autowired
    private AmbaseCommonService ambaseCommonService;
    @Autowired
    private StockCheckMissionService stockCheckMissionService;
    @Autowired
    private StockTakingProcessService stockTakingProcessService;
    @Autowired
    private StockAssetsCheckMethodConfigMapper stockAssetsCheckMethodConfigMapper;
    @Autowired
    private StockCheckMissionMapper stockCheckMissionMapper;
    @Autowired
    private FileServiceApi fileServiceApi;
    @Autowired
    private ApvTaskService apvTaskService;
    @Autowired
    private WflService wflService;
    @Autowired
    private StockCheckInputDataServiceHelper stockCheckInputDataServiceHelper;
    @Autowired
    private StockAssetsCheckTaskDetailService stockAssetsCheckTaskDetailService;
    @Autowired
    private StockAssetsService stockAssetsService;


    @Override
    public ResponseData queryEmployeeTask(JwtUser user, Integer pageNum, Long checkTaskId, Integer checkStatus) throws Exception {
        if (pageNum == null) {
            return ResponseData.createFailResult("页数不能为空！");
        }
        if (checkTaskId == null) {
            return ResponseData.createFailResult("任务id不能为空！");
        }
        if (checkStatus == null) {
            return ResponseData.createFailResult("盘点状态类型不能为空！");
        }
        StockAssetsCheckTask stockAssetsCheckTask = stockCheckMissionService.selectMissionById(checkTaskId,null);
        if (stockAssetsCheckTask == null){
            return ResponseData.createFailResult("盘点任务不存在");
        }

        Map<String, Object> resultMap = new HashMap<>(4);
        resultMap.put("takingPlanNo",stockAssetsCheckTask.getTakingPlanNo());
        resultMap.put("checkTaskId",checkTaskId);

        StockCheckCommonReqDTO stockCheckCommonReqDTO = new StockCheckCommonReqDTO();
        stockCheckCommonReqDTO.setCheckTaskId(checkTaskId);
        stockCheckCommonReqDTO.setPageNum(pageNum);
        stockCheckCommonReqDTO.setCheckPeople(user.getEmployeeCode());
        stockCheckCommonReqDTO.initPageDefaultParam();

        //查询进行中待盘点任务下待盘资产数量
        Long waitCheckCount = stockCheckMissionDetailMapper.queryWaitCountCheckDetail(user.getEmployeeCode(),checkTaskId);

        //查询进行中待盘点任务下已盘资产数量
        Long haveCheckCount = stockCheckMissionDetailMapper.queryHaveCountCheckDetail(user.getEmployeeCode(),checkTaskId);

        resultMap.put("waitCheckCount", waitCheckCount);
        resultMap.put("haveCheckCount", haveCheckCount);

        if (StockCheckMissionEnum.CheckFlag.NO.getValue().equals(checkStatus)){
            if (waitCheckCount <= 0) {
                resultMap.put("checkDetailList",new ArrayList<>());
                return ResponseData.createSuccessResult(resultMap);
            }

            List<StockAssetsCheckTaskDetailRespDTO> waitStockAssetsCheckTaskDetailList = changeDetailToRespDTO(user, stockCheckMissionDetailMapper.queryWaitCheckDetail(stockCheckCommonReqDTO));
            resultMap.put("checkDetailList",waitStockAssetsCheckTaskDetailList);
            return ResponseData.createSuccessResult(resultMap);
        }

        if (StockCheckMissionEnum.CheckFlag.YES.getValue().equals(checkStatus)){
            if (haveCheckCount <= 0) {
                resultMap.put("checkDetailList",new ArrayList<>());
                return ResponseData.createSuccessResult(resultMap);
            }

            List<StockAssetsCheckTaskDetailRespDTO> haveStockAssetsCheckTaskDetailList = changeDetailToRespDTO(user, stockCheckMissionDetailMapper.queryHaveCheckDetail(stockCheckCommonReqDTO));
            resultMap.put("checkDetailList",haveStockAssetsCheckTaskDetailList);
            return ResponseData.createSuccessResult(resultMap);
        }

        return ResponseData.createSuccessResult(resultMap);

    }

    /**
     * 组装查询参数
     * @param employeeCode
     * @param CheckTaskStatus
     * @param checkTaskMethod
     * @param pageNum
     * @return
     */
    public StockCheckCommonReqDTO settingCheckValue(String employeeCode, Integer CheckTaskStatus, Integer checkTaskMethod, Integer pageNum) {
        StockCheckCommonReqDTO stockCheckCommonReqDTO = new StockCheckCommonReqDTO();
        stockCheckCommonReqDTO.setCheckPeople(employeeCode);
        //获取盘点人所在的各级部门
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(employeeCode);
        if (sysUserBasicInfo == null) {
            throw new ServiceUncheckedException("员工："+employeeCode+"没有找到对应部门信息");
        }
        String[] deptIdArr = sysUserBasicInfo.getDeptFullId().split("/");
        List<String> deptList = Arrays.stream(deptIdArr).filter(dto->StringUtils.isNoneBlank(dto)).collect(Collectors.toList());
        stockCheckCommonReqDTO.setCheckPeopleInDeptList(deptList);
        stockCheckCommonReqDTO.setCheckTaskStatus(CheckTaskStatus);
        stockCheckCommonReqDTO.setCheckTaskMethod(checkTaskMethod);
        stockCheckCommonReqDTO.initPageDefaultParam();
        stockCheckCommonReqDTO.setStartNum((pageNum - CommonConstant.NUMBER_ONE) * stockCheckCommonReqDTO.getPageSize());
        stockCheckCommonReqDTO.setPageSize(stockCheckCommonReqDTO.getPageSize());
        return stockCheckCommonReqDTO;
    }


    /**
     * 根据盘点任务id集合查询盘点任务信息
     *
     * @param checkTaskIdList
     * @return
     */
    public List<StockAssetsCheckTask> selectByCheckTaskIdList(List<Long> checkTaskIdList) {
        StockAssetsCheckTaskExample example = new StockAssetsCheckTaskExample();
        StockAssetsCheckTaskExample.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(checkTaskIdList)) {
            criteria.andCheckTaskIdIn(checkTaskIdList);
        }
        List<StockAssetsCheckTask> stockAssetsCheckTaskList = stockAssetsCheckTaskMapper.selectByExample(example);
        return stockAssetsCheckTaskList;
    }


    @Override
    public ResponseData queryTaskByParam(JwtUser user, Integer finishFlag, Integer pageNum, Integer checkMethod) throws Exception {
        if (pageNum == null) {
            return ResponseData.createFailResult("页数不能为空！");
        }
        if (finishFlag == null) {
            return ResponseData.createFailResult("标示不能为空！");
        }
        //参数为空时默认为管理员盘点
        if (checkMethod == null) {
            checkMethod = StockCheckMissionEnum.TaskCheckMethod.ADMIN_CHECK.getValue();
        }
        // 如果是员工自助盘点就根据待办是否结束的逻辑进行区分已完成和未完成，如果是管理员盘点就根据任务是否结束的逻辑进行区分
        StockCheckCommonReqDTO stockCheckCommonReqDTO;
        // 如果是员工自助盘点就需要根据待办来进行筛选
        if(StockCheckMissionEnum.TaskCheckMethod.EMP_CHECK.getValue().equals(checkMethod)){
            stockCheckCommonReqDTO = settingCheckValue(user.getEmployeeCode(), null, checkMethod, pageNum);
            String status = "";
            if(StockCheckMissionEnum.TaskStatus.PROGRESSING.getValue().equals(finishFlag)){
                status = ApvTaskConstant.REMIND;
            }else if(StockCheckMissionEnum.TaskStatus.END.getValue().equals(finishFlag)){
                status = ApvTaskConstant.APPROVED;
            }
            // 查询待办中完成的任务
            ApvTaskReqDTO apvTaskReqDTO = new ApvTaskReqDTO();
            apvTaskReqDTO.setCategoryCode(FlowCodeEnum.STOCK_ASSETS_CHECK.getFlowCode());
            apvTaskReqDTO.setStatus(status);
            apvTaskReqDTO.setApproveUser(user.getEmployeeCode());
            List<ApvTask> apvTaskList = apvTaskService.selectApvTaskList(apvTaskReqDTO);
            List<Long> taskIdList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(apvTaskList)){
                for (ApvTask apvTask : apvTaskList) {
                    Long taskId = Long.valueOf(apvTask.getBizId().split(CommonConstant.CHECK_SPLIT_STRING)[0]);
                    taskIdList.add(taskId);
                }
            }
            // 如果是进行中，查询待办进行中的任务
            if(CollectionUtils.isEmpty(taskIdList)){
                if(StockCheckMissionEnum.TaskStatus.PROGRESSING.getValue().equals(finishFlag)){
                    return ResponseData.createSuccessResult("当前盘点人下没有待盘任务！");
                }else if(StockCheckMissionEnum.TaskStatus.END.getValue().equals(finishFlag)) {
                    return ResponseData.createSuccessResult("当前盘点人下没已待盘任务！");
                }
            }
            stockCheckCommonReqDTO.setInCheckTaskIdList(taskIdList);
        }else {
            stockCheckCommonReqDTO = settingCheckValue(user.getEmployeeCode(), finishFlag, checkMethod, pageNum);
        }
        //查询符合条件的总数
        Long totalCount = stockCheckMissionMapper.queryCountCheckTask(stockCheckCommonReqDTO);
        if (totalCount == null || totalCount.equals(CommonConstant.NUMBER_LONG_ZERO)) {
            return ResponseData.createSuccessResult("当前盘点人下没有待盘任务！");
        }
        //查询盘点任务明细
        List<StockAssetsCheckTask> stockAssetsCheckTaskList = stockCheckMissionMapper.queryCheckTask(stockCheckCommonReqDTO);

        List<StockAssetsCheckTaskRespDTO> stockAssetsCheckTaskRespDTOList = handleCheckTask(stockAssetsCheckTaskList);

        Set<String> takingPlanNoSet = new HashSet<>();
        stockAssetsCheckTaskRespDTOList.forEach(dto->takingPlanNoSet.add(dto.getTakingPlanNo()));
        List<String> takingPlanNoList = new ArrayList<>(takingPlanNoSet);

                List<StockTakingPlan> stockTakingPlanList = stockTakingPlanService.getByTakingPlanNoList(takingPlanNoList);
        Map<String, StockTakingPlan> stockTakingPlanMap = stockTakingPlanList.stream().collect(Collectors.toMap(stockTakingPlan ->
                stockTakingPlan.getTakingPlanNo(), Function.identity(), (key1, key2) -> key2));

        Map<String, List<StockAssetsCheckTaskRespDTO>> stringListMap = stockAssetsCheckTaskRespDTOList.stream()
                .collect(Collectors.groupingBy(stockAssetsCheckTaskRespDTO -> stockAssetsCheckTaskRespDTO.getTakingPlanNo()));

        //定义返回结果
        Map<String, Object> resultMap = new HashMap<>();

        List<StockAssetsTakingPlanCheckTaskRespDTO> stockAssetsTakingPlanCheckTaskRespDTOList = new ArrayList<>();
        for (String takingPlanNo : takingPlanNoList) {
            StockAssetsTakingPlanCheckTaskRespDTO stockAssetsTakingPlanCheckTaskRespDTO = new StockAssetsTakingPlanCheckTaskRespDTO();
            stockAssetsTakingPlanCheckTaskRespDTO.setTakingPlanNo(takingPlanNo);
            StockTakingPlan stockTakingPlan = stockTakingPlanMap.get(takingPlanNo);
            stockAssetsTakingPlanCheckTaskRespDTO.setTakingPlanName(stockTakingPlan != null
                    && org.apache.commons.lang3.StringUtils.isNotBlank(stockTakingPlan.getTakingPlanName()) ? stockTakingPlan.getTakingPlanName() : "");

            List<StockAssetsCheckTaskRespDTO> assetsCheckTaskRespDTOList = stringListMap.get(takingPlanNo);
            assetsCheckTaskRespDTOList = assetsCheckTaskRespDTOList.stream()
                    .sorted(Comparator.comparing(StockAssetsCheckTaskRespDTO::getCheckTaskId).reversed()).collect(Collectors.toList());
            stockAssetsTakingPlanCheckTaskRespDTO.setStockAssetsCheckTaskRespDTOList(assetsCheckTaskRespDTOList);
            stockAssetsTakingPlanCheckTaskRespDTOList.add(stockAssetsTakingPlanCheckTaskRespDTO);
        }
        //排序 降序排列
        stockAssetsTakingPlanCheckTaskRespDTOList = stockAssetsTakingPlanCheckTaskRespDTOList.stream()
                .sorted(Comparator.comparing(StockAssetsTakingPlanCheckTaskRespDTO::getTakingPlanNo).reversed()).collect(Collectors.toList());
        resultMap.put("totalCount", totalCount);
        resultMap.put("data", stockAssetsTakingPlanCheckTaskRespDTOList);
        return ResponseData.createSuccessResult(resultMap);
    }


    /**
     * 处理盘点任务数据
     *
     * @param stockAssetsCheckTaskList
     * @return
     * @throws Exception
     */
    public List<StockAssetsCheckTaskRespDTO> handleCheckTask(List<StockAssetsCheckTask> stockAssetsCheckTaskList) throws Exception {
        JwtUser user = SecurityUtil.getJwtUser();
        Set<String> dutyUserSet = new HashSet<>();
        Set<Long> checkTaskIdSet = new HashSet<>();
        Map<Long, List<String>> checkPeopleMap = new HashMap<>(stockAssetsCheckTaskList.size());
        Map<Long, Long> waitCountMap = new HashMap<>(stockAssetsCheckTaskList.size());
        Map<Long, Long> allCountMap = new HashMap<>(stockAssetsCheckTaskList.size());
        for (StockAssetsCheckTask stockAssetsCheckTask : stockAssetsCheckTaskList) {
            dutyUserSet.add(stockAssetsCheckTask.getDutyUser());
            checkTaskIdSet.add(stockAssetsCheckTask.getCheckTaskId());
            Long waitCount = stockCheckMissionDetailMapper.selectWaitCountDetail(stockAssetsCheckTask.getCheckTaskId(),user.getEmployeeCode());
            Long allCount = stockCheckMissionDetailMapper.selectAllCountDetail(stockAssetsCheckTask.getCheckTaskId(),user.getEmployeeCode());
            waitCountMap.put(stockAssetsCheckTask.getCheckTaskId(), waitCount);
            allCountMap.put(stockAssetsCheckTask.getCheckTaskId(), allCount);
        }

        //批量查询盘点任务下的盘点人
        /*List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockCheckMissionDetailMapper.selectByCheckTaskIdList(new ArrayList<>(checkTaskIdSet));
        if (!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)) {
            //如果key相同 value值合并为集合
            checkPeopleMap = stockAssetsCheckTaskDetailList.stream().collect(Collectors.toMap(StockAssetsCheckTaskDetail::getCheckTaskId,
                    e -> new ArrayList<>(Arrays.asList(e.getCheckPeople())),
                    (List<String> oldList, List<String> newList) -> {
                        oldList.addAll(newList);
                        return oldList;
                    }));
        }*/

        Map<String, String> dutyUserMap = new HashMap<>(dutyUserSet.size());
        if (!CollectionUtils.isEmpty(dutyUserSet)) {
            List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(new ArrayList<>(dutyUserSet));
            dutyUserMap = sysUserList.stream().collect(Collectors.toMap(sysUser -> sysUser.getEmpId(), sysUser -> sysUser.getName(), (key1, key2) -> key1));
        }
        //Map<Long, String> checkPeopleNameMap = handleCheckPeople(checkPeopleMap);
        Map<Integer, String> taskStatusMap = StockCheckMissionEnum.taskStatusMap;
        Map<Integer, String> taskCheckMethodEnumMap = StockCheckMissionEnum.taskCheckMethodEnumMap;
        List<StockAssetsCheckTaskRespDTO> stockAssetsCheckTaskRespDTOList = new ArrayList<>();
        for (StockAssetsCheckTask stockAssetsCheckTask : stockAssetsCheckTaskList) {
            StockAssetsCheckTaskRespDTO stockAssetsCheckTaskRespDTO = new StockAssetsCheckTaskRespDTO();
            BeanUtils.copyProperties(stockAssetsCheckTask, stockAssetsCheckTaskRespDTO);
            //判断状态如果是进行中
            if (stockAssetsCheckTaskRespDTO.getCheckTaskStatus().equals(StockCheckMissionEnum.TaskStatus.PROGRESSING.getValue())) {
//                Date taskLastDate = new Date(Long.parseLong(stockAssetsCheckTaskRespDTO.getTaskLastTime()));
                Date taskLastDate = stockAssetsCheckTask.getTaskLastTime();
                //当前时间与任务截止时间比较
                int compareResult = new Date().compareTo(taskLastDate);
                if (compareResult == StringConstant.LEFT) {
                    stockAssetsCheckTaskRespDTO.setCheckTaskStatusName("逾期未完成");
                } else {
                    stockAssetsCheckTaskRespDTO.setCheckTaskStatusName(taskStatusMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskStatus()));
                }
            } else {
                stockAssetsCheckTaskRespDTO.setCheckTaskStatusName(taskStatusMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskStatus()));
            }
            stockAssetsCheckTaskRespDTO.setTaskBeginTimeStr(stockAssetsCheckTask.getTaskBeginTime() != null ? DateUtils.dateFormat(stockAssetsCheckTask.getTaskBeginTime(), DateUtils.DATE_PATTERN) : "");
            stockAssetsCheckTaskRespDTO.setTaskEndTimeStr(stockAssetsCheckTask.getTaskEndTime() != null ? DateUtils.dateFormat(stockAssetsCheckTask.getTaskEndTime(), DateUtils.DATE_PATTERN) : "");
            stockAssetsCheckTaskRespDTO.setTaskLastTimeStr(stockAssetsCheckTask.getTaskLastTime() != null ? DateUtils.dateFormat(stockAssetsCheckTask.getTaskLastTime(), DateUtils.DATE_PATTERN) : "");
            stockAssetsCheckTaskRespDTO.setCheckTaskMethodName(taskCheckMethodEnumMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskMethod()));
            stockAssetsCheckTaskRespDTO.setDutyUserName(StringUtils.isNotBlank(stockAssetsCheckTaskRespDTO.getDutyUser()) ? dutyUserMap.get(stockAssetsCheckTaskRespDTO.getDutyUser()) : "");
            //盘点人
            //stockAssetsCheckTaskRespDTO.setCheckPeopleName(StringUtils.isNotBlank(checkPeopleNameMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskId())) ? checkPeopleNameMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskId()) : "");
            stockAssetsCheckTaskRespDTO.setCheckPeopleName("");
            //待盘数量
            stockAssetsCheckTaskRespDTO.setWaitCheckCount(waitCountMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskId()) != null ? waitCountMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskId()) : CommonConstant.NUMBER_LONG_ZERO);
            stockAssetsCheckTaskRespDTO.setDetailCount(allCountMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskId()) != null ? allCountMap.get(stockAssetsCheckTaskRespDTO.getCheckTaskId()) : CommonConstant.NUMBER_LONG_ZERO);
            stockAssetsCheckTaskRespDTO.setHaveCheckCount(stockAssetsCheckTaskRespDTO.getDetailCount()-stockAssetsCheckTaskRespDTO.getWaitCheckCount());
            stockAssetsCheckTaskRespDTOList.add(stockAssetsCheckTaskRespDTO);
        }

        return stockAssetsCheckTaskRespDTOList;
    }


    /**
     * b
     * 处理盘点人
     *
     * @param checkPeopleMap
     */
    public Map<Long, String> handleCheckPeople(Map<Long, List<String>> checkPeopleMap) {
        Map<Long, String> checkPeopleNameMap = new HashMap<>(checkPeopleMap.size());
        List<String> checkPeoples = new ArrayList<>();
        for (Map.Entry<Long, List<String>> entry : checkPeopleMap.entrySet()) {
            Long checkTaskId = entry.getKey();
            List<String> checkPeopleList = entry.getValue();
            StringBuffer checkPeopleNameBuffer = new StringBuffer();
            String[] arr = checkPeopleList.get(0).split(",");
            //盘点人更新时，更新为多盘点人的情况
            if (arr.length > 1) {
                checkPeoples = Arrays.asList(arr);
            } else {
                checkPeoples = checkPeopleList;
            }
            if (!CollectionUtils.isEmpty(checkPeoples)) {
                List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(new ArrayList<>(checkPeoples));
                for (SysUser sysUser : sysUserList) {
                    checkPeopleNameBuffer.append(sysUser.getEmpId() + " " + sysUser.getName() + ",");
                }
            }
            String checkPeopleName = checkPeopleNameBuffer.toString();
            checkPeopleName = checkPeopleName.substring(0, checkPeopleName.length() - 1);
            checkPeopleNameMap.put(checkTaskId, checkPeopleName);
        }
        return checkPeopleNameMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData reportCheckTaskAsset(JwtUser user, StockAssetsReportReqDTO stockAssetsReportReqDTO) throws Exception {
        //1。参数校验
        if (StringUtils.isBlank(stockAssetsReportReqDTO.getTakingPlanNo())) {
            return ResponseData.createFailResult("盘点计划单号不能为空!");
        }
        // 上报异常资产可能是盘盈资产，涉及入库时以下条件都不能为空
        if (StringUtils.isBlank(stockAssetsReportReqDTO.getAssetsName())) {
            return ResponseData.createFailResult("资产名称不能为空!");
        }
        String assetsCode = stockAssetsReportReqDTO.getAssetsCode();
        String snNo = stockAssetsReportReqDTO.getSnNo();
        if (StringUtils.isBlank(assetsCode) && StringUtils.isBlank(snNo)) {
            return ResponseData.createFailResult("资产编码和设备序列号不能同时为空!");
        }
        if(StringUtils.isNotBlank(assetsCode) && CommonUtil.isContainChinese(assetsCode)){
            return ResponseData.createFailResult("资产编码不能输入中文，如果没有资产无需上报!");
        }
        if(StringUtils.isNotBlank(snNo) && CommonUtil.isContainChinese(snNo)){
            return ResponseData.createFailResult("设备序列化号不能输入中文，如果没有资产无需上报!");
        }
        StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(stockAssetsReportReqDTO.getTakingPlanNo());
        if (null == stockTakingPlan) {
            return ResponseData.createFailResult("当前盘点计划单不存在！");
        }
        Integer isCheckAssets = stockAssetsReportReqDTO.getIsCheckAssets();
        // 如果需要校验资产，根据资产编码和设备序列号去校验
        if (MetaDataEnum.yesOrNo.YES.getValue().equals(isCheckAssets)) {
            StringBuilder errorMessageBuilder = new StringBuilder();
            if (StringUtils.isNotBlank(assetsCode)) {
                AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
                assetsSearchDTO.setAssetsCode(assetsCode);
                StockAssets stockAssets = stockAssetsService.selectOne(assetsSearchDTO);
                if (null == stockAssets) {
                    errorMessageBuilder.append("请确认资产编码是否无误;");
                }
            }
            if (StringUtils.isNotBlank(snNo)) {
                AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
                assetsSearchDTO.setSnCode(snNo);
                StockAssets stockAssets = stockAssetsService.selectOne(assetsSearchDTO);
                if (null == stockAssets) {
                    errorMessageBuilder.append("请确认设备序列号是否无误;");
                }
            }
            if (errorMessageBuilder.length() > CommonConstant.NUMBER_ZERO) {
                return ResponseData.createFailResult(errorMessageBuilder.toString(), ResponseCode.PARAMETER_ERROR.getCode());
            }
        }
        StockAssets stockAssets = null;
        StockAssets snStockAssets = null;
        if(StringUtils.isNotBlank(assetsCode)){
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setAssetsCode(assetsCode);
            stockAssets = stockAssetsService.selectOne(assetsSearchDTO);
        }
        if(StringUtils.isNotBlank(snNo)){
            AssetsSearchDTO assetsSearchDTO = new AssetsSearchDTO();
            assetsSearchDTO.setSnCode(snNo);
            snStockAssets = stockAssetsService.selectOne(assetsSearchDTO);
        }
        if(StringUtils.isNotBlank(assetsCode) && StringUtils.isNotBlank(snNo)){
            if(stockAssets != null && snStockAssets != null){
                if(!StringUtils.equals(assetsCode, snStockAssets.getAssetsCode())){
                    return ResponseData.createFailResult("资产编码和设备序列号不匹配，请检查修改后重试");
                }
            }else if(!(null == stockAssets && null == snStockAssets)){
                return ResponseData.createFailResult("资产编码和设备序列号不匹配，请检查修改后重试");
            }
        }
        String errorMessage = StringConstant.EMPTY;
        String snapshotAssetsHolder = StringConstant.EMPTY;
        Integer snapshotAssetsStatus = null;
        // 根据资产状态，设置异常信息
        stockAssets = null == stockAssets ? snStockAssets : stockAssets;
        if(null == stockAssets){
            errorMessage = "上报未录入系统资产";
        }else if(AssetsEnum.statusType.USED.getValue().equals(stockAssets.getStatus())){
            snapshotAssetsHolder = stockAssets.getHolder();
            snapshotAssetsStatus = AssetsEnum.statusType.USED.getValue();
            errorMessage = "上报他人资产";
        }else if(AssetsEnum.statusType.IDLE.getValue().equals(stockAssets.getStatus())){
            snapshotAssetsStatus = AssetsEnum.statusType.IDLE.getValue();
            errorMessage = "上报在库资产";
        }
        //2。查询该盘点人下是否有该资产
        StockAssetsCheckTaskDetailExample example = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(assetsCode)){
            criteria.andSnapshotAssetsCodeEqualTo(assetsCode);
        }else {
            criteria.andSnapshotSnNoEqualTo(snNo);
        }
        criteria.andCheckPeopleLike(StringConstant.PERCENT + user.getEmployeeCode() + StringConstant.PERCENT);
        if (StringUtils.isNotBlank(stockAssetsReportReqDTO.getTakingPlanNo())) {
            criteria.andTakingPlanNoEqualTo(stockAssetsReportReqDTO.getTakingPlanNo());
        }
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(example);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(stockAssetsCheckTaskDetailList)) {
            return ResponseData.createFailResult("当前盘点人下已存在该资产！");
        }

        //3。增加当前上报资产到盘点任务明细表中
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria1 = stockAssetsCheckTaskDetailExample.createCriteria();
        if(StringUtils.isNotBlank(assetsCode)){
            criteria1.andSnapshotAssetsCodeEqualTo(assetsCode);
        }else {
            criteria1.andSnapshotSnNoEqualTo(snNo);
        }
        if (StringUtils.isNotBlank(stockAssetsReportReqDTO.getTakingPlanNo())) {
            criteria1.andTakingPlanNoEqualTo(stockAssetsReportReqDTO.getTakingPlanNo());
        }
        List<StockAssetsCheckTaskDetail> assetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        Date currentTime = new Date();
        //不存在新增
        if (org.apache.commons.collections.CollectionUtils.isEmpty(assetsCheckTaskDetailList)) {
            StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = new StockAssetsCheckTaskDetail();
            stockAssetsCheckTaskDetail.setCheckTaskId(stockAssetsReportReqDTO.getCheckTaskId() != null ? stockAssetsReportReqDTO.getCheckTaskId() : StockCheckMissionEnum.DetailFlag.NO.getValue().longValue());

            stockAssetsCheckTaskDetail.setTakingPlanNo(stockAssetsReportReqDTO.getTakingPlanNo());

            stockAssetsCheckTaskDetail.setCreatedBy(user.getEmployeeCode());
            stockAssetsCheckTaskDetail.setUpdatedBy(user.getEmployeeCode());
            stockAssetsCheckTaskDetail.setCheckPeople(user.getEmployeeCode());

            stockAssetsCheckTaskDetail.setNeedDept("");
            stockAssetsCheckTaskDetail.setSnapshotAssetsCode(StringUtils.isNotBlank(stockAssetsReportReqDTO.getAssetsCode()) ? stockAssetsReportReqDTO.getAssetsCode() : "");
            stockAssetsCheckTaskDetail.setSnapshotSnNo(StringUtils.isNotBlank(stockAssetsReportReqDTO.getSnNo()) ? stockAssetsReportReqDTO.getSnNo() : "");
            stockAssetsCheckTaskDetail.setSnapshotAssetsName(stockAssetsReportReqDTO.getAssetsName());
            stockAssetsCheckTaskDetail.setSnapshotAssetsHolder(snapshotAssetsHolder);
            stockAssetsCheckTaskDetail.setSnapshotAssetsStatus(snapshotAssetsStatus);
            stockAssetsCheckTaskDetail.setSnapshotHolderAddress("");
            stockAssetsCheckTaskDetail.setSnapshotWarehouseCode("");
            stockAssetsCheckTaskDetail.setSnapshotNumber(CommonConstant.NUMBER_ZERO);
            stockAssetsCheckTaskDetail.setRealAssetsCode(StringUtils.isNotBlank(stockAssetsReportReqDTO.getAssetsCode()) ? stockAssetsReportReqDTO.getAssetsCode() : "");
            //实际领用人为当前上报异常资产的操作人
            stockAssetsCheckTaskDetail.setRealAssetsHolder(SecurityUtil.getJwtUser().getEmployeeCode());
            stockAssetsCheckTaskDetail.setRealAssetsName(StringUtils.isNotBlank(stockAssetsReportReqDTO.getAssetsName()) ? stockAssetsReportReqDTO.getAssetsName() : "");
            stockAssetsCheckTaskDetail.setRealAssetsSnno(StringUtils.isNotBlank(stockAssetsReportReqDTO.getSnNo()) ? stockAssetsReportReqDTO.getSnNo() : "");
            //上报异常资产为员工自助盘点功能，员工盘点到的资产状态为使用中
            stockAssetsCheckTaskDetail.setRealAssetsStatus(AssetsEnum.statusType.USED.getValue());
            stockAssetsCheckTaskDetail.setRealHolderAddress("");
            stockAssetsCheckTaskDetail.setRealNumber(CommonConstant.NUMBER_ONE);
            stockAssetsCheckTaskDetail.setRealWarehouseCode("");
            stockAssetsCheckTaskDetail.setRemark("");
            stockAssetsCheckTaskDetail.setNewInsertFlag(CommonEnum.status.YES.getValue());
            stockAssetsCheckTaskDetail.setCheckFlag(CommonEnum.status.YES.getValue());
            stockAssetsCheckTaskDetail.setDifference(CommonEnum.status.NO.getValue());
            stockAssetsCheckTaskDetail.setErrDesc("");
            stockAssetsCheckTaskDetail.setOwnFlag(StockCheckMissionEnum.OwnFlag.YES.getValue());

            stockAssetsCheckTaskDetail.setRealAssetsBrand(stockAssetsReportReqDTO.getBrand());
            stockAssetsCheckTaskDetail.setRealAssetsModel(stockAssetsReportReqDTO.getModel());
            stockAssetsCheckTaskDetail.setRealHolderAddress(stockAssetsReportReqDTO.getHolderAddress());
            stockAssetsCheckTaskDetail.setRealAssetsCpu(stockAssetsReportReqDTO.getAssetsCpu());
            stockAssetsCheckTaskDetail.setRealRamMemory(stockAssetsReportReqDTO.getRamMemory());
            stockAssetsCheckTaskDetail.setRealHardDisk(stockAssetsReportReqDTO.getHardDisk());
            stockAssetsCheckTaskDetail.setCheckTime(currentTime);
            stockAssetsCheckTaskDetail.setErrMessage(errorMessage);
            stockAssetsCheckTaskDetailMapper.insertSelective(stockAssetsCheckTaskDetail);
        } else {
            //更新当前资产信息
            StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = assetsCheckTaskDetailList.get(0);
            stockAssetsCheckTaskDetail.setDifference(CommonEnum.status.NO.getValue());
            stockAssetsCheckTaskDetail.setErrDesc("");
            stockAssetsCheckTaskDetail.setCheckFlag(StockCheckMissionEnum.OwnFlag.YES.getValue());

            stockAssetsCheckTaskDetail.setRealAssetsCode(stockAssetsReportReqDTO.getAssetsCode());
            stockAssetsCheckTaskDetail.setRealAssetsSnno(stockAssetsReportReqDTO.getSnNo());
            stockAssetsCheckTaskDetail.setRealAssetsName(stockAssetsReportReqDTO.getAssetsName());
            stockAssetsCheckTaskDetail.setRealAssetsBrand(stockAssetsReportReqDTO.getBrand());
            stockAssetsCheckTaskDetail.setRealAssetsModel(stockAssetsReportReqDTO.getModel());
            stockAssetsCheckTaskDetail.setRealHolderAddress(stockAssetsReportReqDTO.getHolderAddress());
            stockAssetsCheckTaskDetail.setRealAssetsCpu(stockAssetsReportReqDTO.getAssetsCpu());
            stockAssetsCheckTaskDetail.setRealRamMemory(stockAssetsReportReqDTO.getRamMemory());
            stockAssetsCheckTaskDetail.setRealHardDisk(stockAssetsReportReqDTO.getHardDisk());
            stockAssetsCheckTaskDetail.setUpdatedAt(currentTime);
            stockAssetsCheckTaskDetail.setUpdatedBy(user.getEmployeeCode());
            //将盘点人更新为实际盘点人+计划盘点人 使两个人都能查到该资产
            stockAssetsCheckTaskDetail.setCheckPeople(stockAssetsCheckTaskDetail.getCheckPeople()+";"+user.getEmployeeCode());
            stockAssetsCheckTaskDetail.setCheckTime(currentTime);
            stockAssetsCheckTaskDetail.setRealNumber(CommonConstant.NUMBER_ONE);
            stockAssetsCheckTaskDetail.setErrMessage(errorMessage);
            stockAssetsCheckTaskDetailMapper.updateByPrimaryKeySelective(stockAssetsCheckTaskDetail);
        }

        return ResponseData.createSuccessResult("上报成功");
    }

    @Override
    public ResponseData selectTaskWarehouseList(JwtUser user, Long checkTaskId, String keyWord) throws Exception {
        //1.参数判断
        if (checkTaskId == null) {
            return ResponseData.createFailResult("盘点任务ID不能为空");
        }
        //2。根据条件查询
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockCheckMissionDetailMapper.selectTaskWarehouseList(checkTaskId, keyWord);
        List<StockAssetsCheckTaskDetailRespDTO> stockAssetsCheckTaskDetailRespDTOList = ConvertUtil.convertToType(StockAssetsCheckTaskDetailRespDTO.class, stockAssetsCheckTaskDetailList);

        List<String> warehouseCodeList = stockAssetsCheckTaskDetailRespDTOList.stream().map(stockAssetsCheckTaskDetailRespDTO -> stockAssetsCheckTaskDetailRespDTO.getSnapshotWarehouseCode()).distinct().collect(Collectors.toList());
        Map<String, String> warehouseNameMap = new HashMap<>(warehouseCodeList.size());
        if (!CollectionUtils.isEmpty(warehouseCodeList)) {
            List<WarehouseRespDTO> warehouseRespDTOList = stockWarehouseService.selectWarehouseDetailByCode(warehouseCodeList);
            warehouseNameMap = warehouseRespDTOList.stream().collect(Collectors.toMap(warehouseRespDTO -> warehouseRespDTO.getCode(), warehouseRespDTO -> warehouseRespDTO.getName(), (key1, key2) -> key2));
        }

        for (StockAssetsCheckTaskDetailRespDTO stockAssetsCheckTaskDetailRespDTO : stockAssetsCheckTaskDetailRespDTOList) {
            stockAssetsCheckTaskDetailRespDTO.setSnapshotWarehouseName
                    (StringUtils.isNotBlank(stockAssetsCheckTaskDetailRespDTO.getSnapshotWarehouseCode())
                            ? warehouseNameMap.get(stockAssetsCheckTaskDetailRespDTO.getSnapshotWarehouseCode()) : "");
            if (StringUtils.isNotBlank(stockAssetsCheckTaskDetailRespDTO.getSnapshotWarehouseName())) {
                String firstCharacter = ChineseCharacterUtil.convertHanzi2Pinyin(stockAssetsCheckTaskDetailRespDTO.getSnapshotWarehouseName(), false)
                        .toUpperCase().substring(StringConstant.RIGHT, StringConstant.LEFT);
                stockAssetsCheckTaskDetailRespDTO.setFirstCharacter(firstCharacter);
            }
        }
        //根据仓库名称首字母升序排列
        stockAssetsCheckTaskDetailRespDTOList = stockAssetsCheckTaskDetailRespDTOList.stream().
                sorted(Comparator.comparing(StockAssetsCheckTaskDetailRespDTO::getFirstCharacter)).collect(Collectors.toList());

        return ResponseData.createSuccessResult(stockAssetsCheckTaskDetailRespDTOList);
    }

    @Override
    public ResponseData selectByAssetsCodeOrSnNo(String assetsCodeOrSnNo,String takingPlanNo,Long taskDetailId) throws Exception {
        //1。参数校验
        assetsCodeOrSnNo = CommonUtil.replaceOldAssetCode(assetsCodeOrSnNo);
        if (StringUtils.isBlank(assetsCodeOrSnNo) || StringUtils.isBlank(takingPlanNo)) {
            return ResponseData.createFailResult("必填参数不能为空");
        }
        // 判断当前输入的资产是否与计划行中的数据一致
        if(taskDetailId != null){
            StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail= stockAssetsCheckTaskDetailMapper.selectByPrimaryKey(taskDetailId);
            if(!StringUtils.equals(assetsCodeOrSnNo, stockAssetsCheckTaskDetail.getSnapshotAssetsCode()) && !StringUtils.equals(assetsCodeOrSnNo, stockAssetsCheckTaskDetail.getSnapshotSnNo())){
                return ResponseData.createFailResult("输入的资产编码和序列号都与当前盘点资产不一致");
            }
        }
        //查询当前资产是否已经盘点
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        StockAssetsCheckTaskDetailExample.Criteria criteria1 = stockAssetsCheckTaskDetailExample.createCriteria();
        criteria.andTakingPlanNoEqualTo(takingPlanNo);
        criteria.andSnapshotAssetsCodeEqualTo(assetsCodeOrSnNo);
        criteria1.andTakingPlanNoEqualTo(takingPlanNo);
        criteria1.andSnapshotSnNoEqualTo(assetsCodeOrSnNo);

        stockAssetsCheckTaskDetailExample.or(criteria1);
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = null;
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        if (!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)) {
            stockAssetsCheckTaskDetail = stockAssetsCheckTaskDetailList.get(0);
            if (stockAssetsCheckTaskDetail.getCheckFlag().equals(StockCheckMissionEnum.CheckFlag.YES.getValue())) {
                return ResponseData.createFailResult("当前资产信息已经被盘点过了,不能重复盘点！");
            }
        }

        //2。查询资产卡片获取相对应的资产信息
        StockAssetsExample stockAssetsExample = new StockAssetsExample();
        StockAssetsExample.Criteria criteria2 = stockAssetsExample.createCriteria();
        StockAssetsExample.Criteria criteria3 = stockAssetsExample.createCriteria();
        criteria2.andAssetsCodeEqualTo(assetsCodeOrSnNo);
        criteria3.andSnCodeEqualTo(assetsCodeOrSnNo);
        stockAssetsExample.or(criteria3);
        List<StockAssets> stockAssetsList = stockAssetsMapper.selectByExample(stockAssetsExample);
        if (!CollectionUtils.isEmpty(stockAssetsList)) {
            StockAssets stockAssets = stockAssetsList.get(0);
            Map<Integer, String> assetsStatusTypeMap = AssetsEnum.assetsStatusTypeMap;
            Map<Integer, String> conditionsMap = AssetsEnum.conditionsMap;

            AssetsDTO assetsDTO = ConvertUtil.convertToType(AssetsDTO.class, stockAssets);
            assetsDTO.setConditionsName(conditionsMap.get(assetsDTO.getConditions()));
            assetsDTO.setStatusText(assetsStatusTypeMap.get(assetsDTO.getStatus()));
            //查询仓库
            if (StringUtils.isNotBlank(assetsDTO.getWarehouseCode())) {
                StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode(assetsDTO.getWarehouseCode(), null);
                assetsDTO.setWarehouseName(stockWarehouse.getName());
            }
            //持有人
            if (StringUtils.isNotBlank(assetsDTO.getHolder())) {
                List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(Arrays.asList(assetsDTO.getHolder()));
                assetsDTO.setHolderName(!CollectionUtils.isEmpty(sysUserList) ? sysUserList.get(0).getName() : "");
            }
            // 如果是不需要拍照直接返回
            if(stockAssetsCheckTaskDetail != null && MetaDataEnum.yesOrNo.NO.getValue().equals(stockAssetsCheckTaskDetail.getIsNeedTakePicture())){
                return ResponseData.createSuccessResult(assetsDTO);
            }
            //获取资产对应分类的对比图配置
            CategoryPictureMetaRespDTO categoryPictureMetaRespDTO = ambaseCommonService.selectMetaDataByKey(assetsDTO.getCategory());
            assetsDTO.setCategoryPictureMetaRespDTO(categoryPictureMetaRespDTO);
            return ResponseData.createSuccessResult(assetsDTO);
        } else {
            return ResponseData.createFailResult("查询当前资产信息不存在!");
        }

    }

    @Override
    public ResponseData selectWaitCheckAssets(StockCheckCommonReqDTO stockCheckCommonReqDTO, JwtUser user) throws Exception {
        final PageRespDTO<StockAssetsCheckTaskDetailRespDTO> pageRespDTO = PageRespDTO.createSuccessRes();
        //1。参数校验
        if (stockCheckCommonReqDTO.getCheckTaskId() == null) {
            return ResponseData.createFailResult("盘点任务id不能为空！");
        }
        if (user != null && StringUtils.isNotBlank(user.getEmployeeCode())) {
            stockCheckCommonReqDTO.setCheckPeople(user.getEmployeeCode());
        }
        //2。根据条件查询
        long count = getWaitCheckCount(stockCheckCommonReqDTO);
        pageRespDTO.setCount(count);
        if (count > CommonConstant.NUMBER_ZERO) {
            stockCheckCommonReqDTO.initPageDefaultParam();
            stockCheckCommonReqDTO.setStartNum((stockCheckCommonReqDTO.getPageNum() - CommonConstant.NUMBER_ONE) * stockCheckCommonReqDTO.getPageSize());
            stockCheckCommonReqDTO.setPageSize(stockCheckCommonReqDTO.getPageSize());
            List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = getWaitCheckList(stockCheckCommonReqDTO);
            //类型转换
            List<StockAssetsCheckTaskDetailRespDTO> stockAssetsCheckTaskDetailRespDTOList = changeDetailToRespDTO(user, stockAssetsCheckTaskDetailList);
            /*List<StockAssetsCheckTaskDetailRespDTO> stockAssetsCheckTaskDetailRespDTOList = ConvertUtil.convertToType(StockAssetsCheckTaskDetailRespDTO.class, stockAssetsCheckTaskDetailList);
            Map<Integer, String> assetsStatusTypeMap = AssetsEnum.assetsStatusTypeMap;
            Set<String> empIdSet = new HashSet<>();
            Set<String> deptIdSet = new HashSet<>();
            stockAssetsCheckTaskDetailRespDTOList.forEach(dto->{
                empIdSet.add(dto.getSnapshotAssetsHolder());
                deptIdSet.add(dto.getCostDept());
            });
            List<String> empIds = new ArrayList<>(empIdSet);
            List<String> deptIds = new ArrayList<>(deptIdSet);
            Map<String,SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIds);
            Map<String, SysDept> sysDeptMap = ambaseCommonService.selectSysDeptMapByIds(deptIds);
            stockAssetsCheckTaskDetailRespDTOList.forEach(dto->{
                dto.setSnapshotAssetsStatusName(assetsStatusTypeMap.get(dto.getSnapshotAssetsStatus()));
                dto.setSnapshotAssetsHolderName(sysUserMap.getOrDefault(dto.getSnapshotAssetsHolder(),new SysUser()).getName());
                dto.setCostDeptName(sysDeptMap.getOrDefault(dto.getCostDept(),new SysDept()).getDeptName());
            });*/
            pageRespDTO.setData(stockAssetsCheckTaskDetailRespDTOList);
        }
        return pageRespDTO;
    }


    /**
     * 查询数量
     *
     * @param stockCheckCommonReqDTO
     * @return
     */
    public long getWaitCheckCount(StockCheckCommonReqDTO stockCheckCommonReqDTO) {
        return stockAssetsCheckTaskDetailMapper.countByExample(settingExample(stockCheckCommonReqDTO));
    }

    /**
     * 列表查询
     *
     * @param stockCheckCommonReqDTO
     * @return
     */
    public List<StockAssetsCheckTaskDetail> getWaitCheckList(StockCheckCommonReqDTO stockCheckCommonReqDTO) {
        StockAssetsCheckTaskDetailExample example = settingExample(stockCheckCommonReqDTO);
        example.setLimit(stockCheckCommonReqDTO.getPageSize());
        example.setOffset(stockCheckCommonReqDTO.getStartNum());
        example.setDistinct(true);
        return stockAssetsCheckTaskDetailMapper.selectByExample(example);
    }

    private StockAssetsCheckTaskDetailExample settingExample(StockCheckCommonReqDTO stockCheckCommonReqDTO) {
        StockAssetsCheckTaskDetailExample example = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = example.createCriteria();
        criteria.andCheckTaskIdEqualTo(stockCheckCommonReqDTO.getCheckTaskId());
        if (!CollectionUtils.isEmpty(stockCheckCommonReqDTO.getWarehouseCodeList())) {
            criteria.andSnapshotWarehouseCodeIn(stockCheckCommonReqDTO.getWarehouseCodeList());
        }
        if (StringUtils.isNotBlank(stockCheckCommonReqDTO.getWarehouseCodes())) {
            criteria.andSnapshotWarehouseCodeEqualTo(stockCheckCommonReqDTO.getWarehouseCodes());
        }
        if (stockCheckCommonReqDTO.getAssetsStatus() != null) {
            criteria.andSnapshotAssetsStatusEqualTo(stockCheckCommonReqDTO.getAssetsStatus());
        }
        if (StringUtils.isNotBlank(stockCheckCommonReqDTO.getAddressProvince())) {
            criteria.andHolderAddressProvinceEqualTo(stockCheckCommonReqDTO.getAddressProvince());
        }
        if (StringUtils.isNotBlank(stockCheckCommonReqDTO.getAddressCity())) {
            criteria.andHolderAddressCityEqualTo(stockCheckCommonReqDTO.getAddressCity());
        }
        if (StringUtils.isNotBlank(stockCheckCommonReqDTO.getAddressCounty())) {
            criteria.andHolderAddressCountyEqualTo(stockCheckCommonReqDTO.getAddressCounty());
        }
        criteria.andCheckFlagEqualTo(MetaDataEnum.yesOrNo.NO.getValue());
        if (StringUtils.isNotBlank(stockCheckCommonReqDTO.getCheckPeople())) {
            criteria.andCheckPeopleLike(StringConstant.PERCENT + stockCheckCommonReqDTO.getCheckPeople() + StringConstant.PERCENT);
        }
        return example;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData submitCheckResult(StockCheckSubmitResultReqDTO stockCheckSubmitResultReqDTO, JwtUser user) throws Exception {
        long startTime = System.currentTimeMillis();
        //1。参数校验
        //获取校验方法里返回的detail对象，后面继续使用，避免再次查询
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = new StockAssetsCheckTaskDetail();
        String checkParamResult = checkParam(stockCheckSubmitResultReqDTO,stockAssetsCheckTaskDetail);
        if (StringUtils.isNotBlank(checkParamResult)) {
            return ResponseData.createFailResult(checkParamResult);
        }
        //查询盘点任务
        /*StockAssetsCheckTaskExample stockAssetsCheckTaskExample = new StockAssetsCheckTaskExample();
        StockAssetsCheckTaskExample.Criteria stockAssetsCheckTaskExampleCriteria = stockAssetsCheckTaskExample.createCriteria();
        stockAssetsCheckTaskExampleCriteria.andCheckTaskIdEqualTo(stockCheckSubmitResultReqDTO.getCheckTaskId());
        List<StockAssetsCheckTask> stockAssetsCheckTaskList = stockAssetsCheckTaskMapper.selectByExample(stockAssetsCheckTaskExample);
        if (CollectionUtils.isEmpty(stockAssetsCheckTaskList)) {
            return ResponseData.createFailResult("当前盘点任务不存在!");
        }*/

        //定义变量，当盘点上传了图片时变更为true
        Boolean flag = false;
        //调用ambase,将业务主键（盘点计划单编码+资产编码）与attachId绑定
        List<Long> attachIdList = stockCheckSubmitResultReqDTO.getAttachIdList();
        if (!CollectionUtils.isEmpty(attachIdList)) {
            List<SysAttachReqDTO> sysAttachReqDTOList = new ArrayList<>(attachIdList.size());
            attachIdList.forEach(dto->{
                SysAttachReqDTO sysAttachReqDTO = new SysAttachReqDTO();
                sysAttachReqDTO.setRelId(stockCheckSubmitResultReqDTO.getTakingPlanNo()+"_"+stockCheckSubmitResultReqDTO.getAssetsCode());
                sysAttachReqDTO.setPageModule("CHECK_PICTURE");
                sysAttachReqDTO.setAttachId(dto);
                sysAttachReqDTOList.add(sysAttachReqDTO);
            });
            ResponseData responseData = fileServiceApi.updateSysAttachList(sysAttachReqDTOList);
            if (!ResponseCode.SUCCESS_CODE.equals(responseData.getCode())){
                //调用接口失败，回滚事物
                log.error("调用ambase绑定图片信息失败："+responseData.getMessage());
                throw new ServiceUncheckedException("图片信息绑定失败");
            }
            flag = true;
        }

        //插入ambase的文件表
//        if(!CollectionUtils.isEmpty(stockCheckSubmitResultReqDTO.getAttachReqDTOS())){
//            stockCheckSubmitResultReqDTO.getAttachReqDTOS().forEach(sysAttachReqDTO -> {
//                sysAttachReqDTO.setAttachSavePath(folderPath);
//                sysAttachReqDTO.setSystemModule(systemModule);
//                sysAttachReqDTO.setAttachModule(attachModule);
//                sysAttachReqDTO.setPageModule("CHECK_PICTURE");
//                sysAttachReqDTO.setRelId(stockCheckSubmitResultReqDTO.getTakingPlanNo()+"_"+stockCheckSubmitResultReqDTO.getAssetsCode());
//                sysAttachReqDTO.setDelFlag(CommonEnum.status.NO.getValue());
//            });
//            ResponseData responseData = fileServiceApi.batchSaveAttach(stockCheckSubmitResultReqDTO.getAttachReqDTOS());
//            if (!ResponseCode.SUCCESS_CODE.equals(responseData.getCode())){
//                //调用接口失败，回滚事物
//                log.error("调用ambase插入图片信息失败："+responseData.getMessage());
//                throw new ServiceUncheckedException("图片信息绑定失败");
//            }
//            flag = true;
//        }
        if(!CollectionUtils.isEmpty(stockCheckSubmitResultReqDTO.getAttachReqDTOS())){
            StringBuilder pictureUrlBuilder = new StringBuilder();
            for (SysAttachReqDTO attachReqDTO : stockCheckSubmitResultReqDTO.getAttachReqDTOS()) {
                String attachSavePath = attachReqDTO.getAttachSavePath();
                if(StringUtils.isNotBlank(attachSavePath)){
                    pictureUrlBuilder.append(attachSavePath);
                    pictureUrlBuilder.append(StringConstant.SEMI_COLON_HALF);
                }
            }
            if(pictureUrlBuilder.length() > CommonConstant.NUMBER_ZERO){
                stockAssetsCheckTaskDetail.setPictureUrlList(pictureUrlBuilder.substring(CommonConstant.NUMBER_ZERO, pictureUrlBuilder.length() - CommonConstant.NUMBER_ONE));
            }
            flag = true;
        }
        Date currentTime = new Date();
        //2。判断盘点资产明细id是否为空
        if (stockAssetsCheckTaskDetail.getTaskDetailId() == null) {
            //不存在该盘点资产
            settingValue(stockAssetsCheckTaskDetail, stockCheckSubmitResultReqDTO);
            stockAssetsCheckTaskDetail.setCheckTaskId(stockCheckSubmitResultReqDTO.getCheckTaskId() != null ? stockCheckSubmitResultReqDTO.getCheckTaskId() : MetaDataEnum.yesOrNo.NO.getValue());
            stockAssetsCheckTaskDetail.setTakingPlanNo(stockCheckSubmitResultReqDTO.getTakingPlanNo());

            stockAssetsCheckTaskDetail.setSnapshotNumber(CommonConstant.NUMBER_ZERO);
            stockAssetsCheckTaskDetail.setNewInsertFlag(MetaDataEnum.yesOrNo.YES.getValue());
            stockAssetsCheckTaskDetail.setDifference(MetaDataEnum.yesOrNo.NO.getValue());
            stockAssetsCheckTaskDetail.setNeedDept("");
            stockAssetsCheckTaskDetail.setSnapshotAssetsCode(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getAssetsCode()) ? stockCheckSubmitResultReqDTO.getAssetsCode() : "");
            stockAssetsCheckTaskDetail.setSnapshotAssetsName(stockCheckSubmitResultReqDTO.getAssetsName());
            stockAssetsCheckTaskDetail.setCheckPeople(user.getEmployeeCode());

            stockAssetsCheckTaskDetail.setCreatedBy(user.getEmployeeCode());
            stockAssetsCheckTaskDetail.setUpdatedBy(user.getEmployeeCode());
            stockAssetsCheckTaskDetail.setCheckTime(currentTime);
            if (flag){
                //PC端根据该字段判断当前资产是否上传了图片，进而调用图片查询接口获取所有图片展示
                stockAssetsCheckTaskDetail.setRealPicturesUrl(CommonConstant.DEFAULT_FILL_STRING);
            }
            stockAssetsCheckTaskDetailMapper.insertSelective(stockAssetsCheckTaskDetail);
        }else {
            //更新
            settingValue(stockAssetsCheckTaskDetail, stockCheckSubmitResultReqDTO);
            compareDifference(stockAssetsCheckTaskDetail, stockCheckSubmitResultReqDTO);
            if (flag) {
                stockAssetsCheckTaskDetail.setRealPicturesUrl(CommonConstant.DEFAULT_FILL_STRING);
            }
            stockAssetsCheckTaskDetail.setUpdatedBy(user.getEmployeeCode());
            stockAssetsCheckTaskDetail.setUpdatedAt(currentTime);
            stockAssetsCheckTaskDetail.setCheckTime(currentTime);
            stockAssetsCheckTaskDetailMapper.updateByPrimaryKey(stockAssetsCheckTaskDetail);
        }



        //3.更新盘点任务状态="已完成"，更新盘点完成比例。将逻辑移到查询盘点任务接口
        //任务下总的盘点资产数量
       /* Long totalCount = stockCheckMissionDetailMapper.countByParams(stockCheckSubmitResultReqDTO.getCheckTaskId(), stockCheckSubmitResultReqDTO.getTakingPlanNo(), null);
        if (totalCount == 0) {
            log.error("盘点任务单：" + stockCheckSubmitResultReqDTO.getCheckTaskId() + "没有查到对应盘点明细数据");
            throw new ServiceUncheckedException("盘点任务单：" + stockCheckSubmitResultReqDTO.getCheckTaskId() + "没有查到对应盘点明细数据");
        }
        //任务下已经盘点过的资产数量
        Long alreadyCheckDetailCount = stockCheckMissionDetailMapper.countByParams(stockCheckSubmitResultReqDTO.getCheckTaskId(), stockCheckSubmitResultReqDTO.getTakingPlanNo(), MetaDataEnum.yesOrNo.YES.getValue());
        double rate = BigDecimal.valueOf((float) alreadyCheckDetailCount / totalCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        StockAssetsCheckTask stockAssetsCheckTaskUpdate = new StockAssetsCheckTask();
        stockAssetsCheckTaskUpdate.setCheckTaskId(stockCheckSubmitResultReqDTO.getCheckTaskId());
        stockAssetsCheckTaskUpdate.setTaskProgressRate(rate);
        stockCheckMissionService.updateCheckMissionById(stockAssetsCheckTaskUpdate);*/
        log.info("资产盘点完成用时"+(System.currentTimeMillis()-startTime)+"ms,盘点人:"+user.getEmployeeCode());
        return ResponseData.createSuccessResult();
    }

    /**
     * 根据条件查询任务下的明细数量
     *
     * @param stockCheckSubmitResultReqDTO
     * @param checkFlag
     * @return
     */
    public long getCheckTaskDetailCountByCheckFlag(StockCheckSubmitResultReqDTO stockCheckSubmitResultReqDTO, Integer checkFlag, Boolean flag) {
        StockAssetsCheckTaskDetailExample example = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = example.createCriteria();
        criteria.andCheckTaskIdEqualTo(stockCheckSubmitResultReqDTO.getCheckTaskId());
        criteria.andNewInsertFlagEqualTo(StockCheckMissionEnum.DetailFlag.NO.getValue());
        if (checkFlag != null) {
            criteria.andCheckFlagEqualTo(checkFlag);
        }
        criteria.andTakingPlanNoEqualTo(stockCheckSubmitResultReqDTO.getTakingPlanNo());
        if (flag) {
            StockAssetsCheckTaskDetailExample.Criteria criteria1 = example.createCriteria();
            criteria1.andCheckTaskIdEqualTo(stockCheckSubmitResultReqDTO.getCheckTaskId());
            criteria1.andNewInsertFlagEqualTo(StockCheckMissionEnum.DetailFlag.NO.getValue());
            criteria1.andTakingPlanNoEqualTo(stockCheckSubmitResultReqDTO.getTakingPlanNo());
            criteria1.andOwnFlagNotEqualTo(MetaDataEnum.yesOrNo.NO.getValue());
            example.or(criteria1);
        }
        long count = stockAssetsCheckTaskDetailMapper.countByExample(example);
        return count;
    }


    /**
     * 比较是否有差异
     *
     * @param stockAssetsCheckTaskDetail
     * @param stockCheckSubmitResultReqDTO
     */
    public void compareDifference(StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail, StockCheckSubmitResultReqDTO stockCheckSubmitResultReqDTO) {
        //如果员工拥有该资产为否 此时认定是有差异的
        if (StockCheckMissionEnum.OwnFlag.NO.getValue().equals(stockCheckSubmitResultReqDTO.getOwnFlag())) {
            stockAssetsCheckTaskDetail.setDifference(MetaDataEnum.yesOrNo.NO.getValue());
        } else {
            boolean flag = true;
            do {
                //资产名称
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsName(), stockCheckSubmitResultReqDTO.getAssetsName());
                if (!flag) {
                    break;
                }
                //资产编码
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsCode(), stockCheckSubmitResultReqDTO.getAssetsCode());
                if (!flag) {
                    break;
                }
                //序列号
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotSnNo(), stockCheckSubmitResultReqDTO.getSnNo());
                if (!flag) {
                    break;
                }
                //品牌
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotBrand(), stockCheckSubmitResultReqDTO.getBrand());
                if (!flag) {
                    break;
                }
                //型号
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotModel(), stockCheckSubmitResultReqDTO.getModel());
                if (!flag) {
                    break;
                }
                //使用人位置
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotHolderAddress(), stockCheckSubmitResultReqDTO.getHolderAddress());
                if (!flag) {
                    break;
                }
                //资产状态
                flag = FiledCompareUtil.compareIntegerUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus(), stockCheckSubmitResultReqDTO.getStatus());
                if (!flag) {
                    break;
                }
                //资产使用情况
                flag = FiledCompareUtil.compareIntegerUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsConditions(), stockCheckSubmitResultReqDTO.getConditions());
                if (!flag) {
                    break;
                }
                //所在仓库
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotWarehouseCode(), stockCheckSubmitResultReqDTO.getWarehouseCode());
                if (!flag) {
                    break;
                }
                //使用人
                flag = FiledCompareUtil.compareStringUtil(stockAssetsCheckTaskDetail.getSnapshotAssetsHolder(), stockCheckSubmitResultReqDTO.getHolder());
                if (!flag) {
                    break;
                }
            } while (false);
            //设置是否有差异 0 有差异 1 无差异
            stockAssetsCheckTaskDetail.setDifference(flag ? MetaDataEnum.yesOrNo.YES.getValue() : MetaDataEnum.yesOrNo.NO.getValue());
        }
    }


    /**
     * 设置参数值
     *
     * @param stockAssetsCheckTaskDetail
     * @param stockCheckSubmitResultReqDTO
     */
    public void settingValue(StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail, StockCheckSubmitResultReqDTO stockCheckSubmitResultReqDTO) throws ParseException {

        //判断是否拥有该资产
        if (StockCheckMissionEnum.OwnFlag.NO.getValue().equals(stockCheckSubmitResultReqDTO.getOwnFlag())) {
            //该资产未被盘点且实际数量应为0
            stockAssetsCheckTaskDetail.setOwnFlag(StockCheckMissionEnum.OwnFlag.NO.getValue());
            // 如果填写的是未拥有该资产也默认为已经盘点
            stockAssetsCheckTaskDetail.setCheckFlag(MetaDataEnum.yesOrNo.YES.getValue());
            //stockAssetsCheckTaskDetail.setCheckFlag(MetaDataEnum.yesOrNo.NO.getValue());
            stockAssetsCheckTaskDetail.setRealNumber(CommonConstant.NUMBER_ZERO);
            stockAssetsCheckTaskDetail.setErrMessage(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getErrMeg()) ? stockCheckSubmitResultReqDTO.getErrMeg() : "");
            stockAssetsCheckTaskDetail.setErrDesc(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getErrDesc()) ? stockCheckSubmitResultReqDTO.getErrDesc() : "");
        }else{
            stockAssetsCheckTaskDetail.setRealAssetsCode(stockCheckSubmitResultReqDTO.getAssetsCode());
            stockAssetsCheckTaskDetail.setRealAssetsName(stockCheckSubmitResultReqDTO.getAssetsName());
            stockAssetsCheckTaskDetail.setRealAssetsStatus(stockCheckSubmitResultReqDTO.getStatus());
            stockAssetsCheckTaskDetail.setRealWarehouseCode(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getWarehouseCode()) ? stockCheckSubmitResultReqDTO.getWarehouseCode() : "");
            stockAssetsCheckTaskDetail.setRealAssetsHolder(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getHolder()) ? stockCheckSubmitResultReqDTO.getHolder() : "");
            if (StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getHolderTime())) {
                Date holderTime = DateUtils.dateParse(stockCheckSubmitResultReqDTO.getHolderTime(),DateUtils.DATE_TIME_PATTERN);
                stockAssetsCheckTaskDetail.setRealHolderTime(holderTime);
            }
            stockAssetsCheckTaskDetail.setRealHolderAddress(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getHolderAddress()) ? stockCheckSubmitResultReqDTO.getHolderAddress() : "");
            stockAssetsCheckTaskDetail.setRealAssetsStatus(stockCheckSubmitResultReqDTO.getStatus() != null ? stockCheckSubmitResultReqDTO.getStatus() : AssetsEnum.statusType.IDLE.getValue());
            stockAssetsCheckTaskDetail.setRealAssetsConditions(stockCheckSubmitResultReqDTO.getConditions() != null ? stockCheckSubmitResultReqDTO.getConditions() : AssetsEnum.Conditions.NORMAL.getValue());
            stockAssetsCheckTaskDetail.setRealAssetsBrand(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getBrand()) ? stockCheckSubmitResultReqDTO.getBrand() : "");
            stockAssetsCheckTaskDetail.setRealAssetsModel(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getModel()) ? stockCheckSubmitResultReqDTO.getModel() : "");
            stockAssetsCheckTaskDetail.setRealAssetsCpu(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getExtCpu()) ? stockCheckSubmitResultReqDTO.getExtCpu() : "");
            stockAssetsCheckTaskDetail.setRealRamMemory(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getExtRamMemory()) ? stockCheckSubmitResultReqDTO.getExtRamMemory() : "");
            stockAssetsCheckTaskDetail.setRealHardDisk(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getExtHardDisk()) ? stockCheckSubmitResultReqDTO.getExtHardDisk() : "");
            stockAssetsCheckTaskDetail.setRealAssetsSnno(StringUtils.isNotBlank(stockCheckSubmitResultReqDTO.getSnNo()) ? stockCheckSubmitResultReqDTO.getSnNo() : "");
            stockAssetsCheckTaskDetail.setErrMessage("");
            stockAssetsCheckTaskDetail.setErrDesc("");
            //如果拥有该资产 设置该资产已经盘点过了
            stockAssetsCheckTaskDetail.setCheckFlag(MetaDataEnum.yesOrNo.YES.getValue());
            //账目数量为1
            stockAssetsCheckTaskDetail.setRealNumber(CommonConstant.NUMBER_ONE);
            stockAssetsCheckTaskDetail.setOwnFlag(StockCheckMissionEnum.OwnFlag.YES.getValue());

        }
    }

    /**
     * 根据盘点任务明细id 查询盘点任务明细
     *
     * @param taskDetailId
     * @return
     */
    public StockAssetsCheckTaskDetail selectByTaskDetailId(Long taskDetailId, String takingPlanNo) {
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        criteria.andTaskDetailIdEqualTo(taskDetailId);
        if (StringUtils.isNotBlank(takingPlanNo)) {
            criteria.andTakingPlanNoEqualTo(takingPlanNo);
        }
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        return !CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList) ? stockAssetsCheckTaskDetailList.get(0) : null;
    }


    public String checkParam(StockCheckSubmitResultReqDTO stockCheckSubmitResultReqDTO,StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail) {
        if (stockCheckSubmitResultReqDTO.getCheckTaskId() == null) {
            return "盘点任务id不能为空!";
        }
        if (StringUtils.isBlank(stockCheckSubmitResultReqDTO.getTakingPlanNo())) {
            return "盘点计划单号不能为空！";
        }

        // 是否持有该资产 2 否 1 是
        if (stockCheckSubmitResultReqDTO.getOwnFlag() != null && stockCheckSubmitResultReqDTO.getOwnFlag().equals(StockCheckMissionEnum.OwnFlag.NO.getValue())) {
            String errDesc = stockCheckSubmitResultReqDTO.getErrDesc();
            if (StringUtils.isBlank(stockCheckSubmitResultReqDTO.getErrMeg()) || StringUtils.isBlank(errDesc)) {
                return "异常信息不能为空!";
            }
            if(errDesc.length() > StockAssetsCheckConstant.ERROR_DESC_MAX_LENGTH){
                return "异常信息描述不能大于150个字!";
            }
        }else {
            if (StringUtils.isBlank(stockCheckSubmitResultReqDTO.getAssetsCode())) {
                return "资产编码不能为空!";
            }
            if (StringUtils.isBlank(stockCheckSubmitResultReqDTO.getAssetsName())) {
                return "资产名称不能为空!";
            }
            if (stockCheckSubmitResultReqDTO.getStatus() == null) {
                return "资产状态不能为空!";
            }
            //当资产当前状态=在库时，仓库不能为空
            if (AssetsEnum.statusType.IDLE.getValue().equals(stockCheckSubmitResultReqDTO.getStatus()) && StringUtils.isBlank(stockCheckSubmitResultReqDTO.getWarehouseCode())) {
                return "资产状态为在库，仓库不能为空";
            }
            //当资产当前状态=使用中时，持有人不能为空
            if (AssetsEnum.statusType.USED.getValue().equals(stockCheckSubmitResultReqDTO.getStatus()) && StringUtils.isBlank(stockCheckSubmitResultReqDTO.getHolder())) {
                return "资产状态为使用中，持有人不能为空";
            }
        }
        // 校验当前资产是否已经被盘点 是则给出提示
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        //存在主键则使用主键查询，提升性能
        if (stockCheckSubmitResultReqDTO.getTaskDetailId() != null){
            criteria.andTaskDetailIdEqualTo(stockCheckSubmitResultReqDTO.getTaskDetailId());
            criteria.andCheckFlagNotEqualTo(StockCheckMissionEnum.CheckFlag.CANCEL.getValue());
        }else {
            criteria.andSnapshotAssetsCodeEqualTo(stockCheckSubmitResultReqDTO.getAssetsCode());
            criteria.andCheckFlagNotEqualTo(StockCheckMissionEnum.CheckFlag.CANCEL.getValue());
            criteria.andTakingPlanNoEqualTo(stockCheckSubmitResultReqDTO.getTakingPlanNo());
        }
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetails = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        if (!CollectionUtils.isEmpty(stockAssetsCheckTaskDetails)) {
            StockAssetsCheckTaskDetail stockAssetsCheckTaskDetailExists = stockAssetsCheckTaskDetails.get(CommonConstant.NUMBER_ZERO);
            if (StockCheckMissionEnum.CheckFlag.YES.getValue().equals(stockAssetsCheckTaskDetailExists.getCheckFlag())) {
                return "当前资产已被他人盘点，请退出盘点任务，重新进入进行盘点！";
            }
            BeanUtils.copyProperties(stockAssetsCheckTaskDetailExists,stockAssetsCheckTaskDetail);
        }


        //校验传入的盘点计划单是否存在
        /*try {
            StockTakingPlan stockTakingPlan = stockTakingPlanService.getByTakingPlanNo(stockCheckSubmitResultReqDTO.getTakingPlanNo());
            if (null == stockTakingPlan) {
                return "当前盘点计划单不存在！";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }*/

        //过滤优化参数
        /*资产状态=在库，持有人、持有人所在位置置灰，所在仓库为必填项
                资产状态=使用中，所在仓库置灰，持有人为必填项，持有人所在位置选填
                资产状态=已发出，持有人、持有人、所在仓库所在位置置灰
                资产状态=已处置，持有人、所在仓库持有人所在位置置灰*/
        if (AssetsEnum.statusType.IDLE.getValue().equals(stockCheckSubmitResultReqDTO.getStatus())) {
            stockCheckSubmitResultReqDTO.setHolder(null);
            stockCheckSubmitResultReqDTO.setHolderAddress(null);
        }
        if (AssetsEnum.statusType.USED.getValue().equals(stockCheckSubmitResultReqDTO.getStatus())) {
            stockCheckSubmitResultReqDTO.setWarehouseCode(null);
        }
        if (AssetsEnum.statusType.SENT.getValue().equals(stockCheckSubmitResultReqDTO.getStatus())) {
            stockCheckSubmitResultReqDTO.setHolder(null);
            stockCheckSubmitResultReqDTO.setHolderAddress(null);
            stockCheckSubmitResultReqDTO.setWarehouseCode(null);
        }
        if (AssetsEnum.statusType.HANDLE.getValue().equals(stockCheckSubmitResultReqDTO.getStatus())) {
            stockCheckSubmitResultReqDTO.setHolder(null);
            stockCheckSubmitResultReqDTO.setHolderAddress(null);
            stockCheckSubmitResultReqDTO.setWarehouseCode(null);
        }
        return null;
    }

    @Override
    public ResponseData selectCheckMethodConfig(Long checkMethod, JwtUser user) throws Exception {
        if (checkMethod == null) {
            return ResponseData.createFailResult("查询参数不能为空");
        }
        StockAssetsCheckMethodConfigExample stockAssetsCheckMethodConfigExample = new StockAssetsCheckMethodConfigExample();
        StockAssetsCheckMethodConfigExample.Criteria criteria = stockAssetsCheckMethodConfigExample.createCriteria();
        criteria.andCheckTaskMethodCodeEqualTo(checkMethod.intValue());
        List<StockAssetsCheckMethodConfig> stockAssetsCheckMethodConfigs = stockAssetsCheckMethodConfigMapper.selectByExample(stockAssetsCheckMethodConfigExample);
        return !CollectionUtils.isEmpty(stockAssetsCheckMethodConfigs) ? ResponseData.createSuccessResult(stockAssetsCheckMethodConfigs.get(0)) : null;
    }

    
    @Override
    public ResponseData showAlreadyCheckAssets(Long taskDetailId) {
        //参数校验
        if (taskDetailId == null){
            return ResponseData.createFailResult("参数不能为空");
        }
        //获取盘点明细数据
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = stockAssetsCheckTaskDetailMapper.selectByPrimaryKey(taskDetailId);
        if (stockAssetsCheckTaskDetail == null){
            return ResponseData.createFailResult("没有找到对应的盘点资产数据");
        }
        CheckAssetsPictureRespDTO checkAssetsPictureRespDTO = new CheckAssetsPictureRespDTO();
        BeanUtils.copyProperties(stockAssetsCheckTaskDetail,checkAssetsPictureRespDTO);
        if(StringUtils.isNotEmpty(checkAssetsPictureRespDTO.getCheckPeople())){
            // 查找人员信息并进行拼接
            StringBuilder empIdAndNameString = new StringBuilder();
            List<String> empIdList = Arrays.asList(checkAssetsPictureRespDTO.getCheckPeople().split(StringConstant.SEMI_COLON_HALF));
            List<SysUser> sysUsers = ambaseCommonService.selectUsersByIds(empIdList);
            for (SysUser sysUser : sysUsers) {
                empIdAndNameString.append(sysUser.getEmpId() + StringConstant.HYPHEN + sysUser.getName() + StringConstant.SEMI_COLON_HALF);
            }
            checkAssetsPictureRespDTO.setCheckPeopleName(empIdAndNameString.substring(0, empIdAndNameString.length() - 1));
        }

        checkAssetsPictureRespDTO.setHolderTime(stockAssetsCheckTaskDetail.getRealHolderTime());

        //返回快照字段
        if (StockCheckMissionEnum.OwnFlag.NO.getValue().equals(stockAssetsCheckTaskDetail.getOwnFlag())) {
            checkAssetsPictureRespDTO.setRealAssetsHolder(stockAssetsCheckTaskDetail.getSnapshotAssetsHolder());
            checkAssetsPictureRespDTO.setHolderTime(stockAssetsCheckTaskDetail.getSnapshotHolderTime());
            checkAssetsPictureRespDTO.setRealHolderAddress(stockAssetsCheckTaskDetail.getSnapshotHolderAddress());
            checkAssetsPictureRespDTO.setRealAssetsStatus(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus());
            checkAssetsPictureRespDTO.setRealAssetsCode(stockAssetsCheckTaskDetail.getSnapshotAssetsCode());
            checkAssetsPictureRespDTO.setRealAssetsModel(stockAssetsCheckTaskDetail.getSnapshotModel());
            checkAssetsPictureRespDTO.setRealAssetsCpu(stockAssetsCheckTaskDetail.getSnapshotAssetsCpu());
            checkAssetsPictureRespDTO.setRealRamMemory(stockAssetsCheckTaskDetail.getSnapshotRamMemory());
            checkAssetsPictureRespDTO.setRealHardDisk(stockAssetsCheckTaskDetail.getSnapshotHardDisk());
            checkAssetsPictureRespDTO.setRealAssetsName(stockAssetsCheckTaskDetail.getSnapshotAssetsName());
            checkAssetsPictureRespDTO.setRealAssetsSnno(stockAssetsCheckTaskDetail.getSnapshotSnNo());
        }

        checkAssetsPictureRespDTO.setIsAutoCheck(stockAssetsCheckTaskDetail.getIsAutoCheck());
        checkAssetsPictureRespDTO.setGuaguaLoginSn(stockAssetsCheckTaskDetail.getGuaguaLoginSn());
        checkAssetsPictureRespDTO.setCheckTime(stockAssetsCheckTaskDetail.getUpdatedAt());
        Map<Integer, String> assetsStatusTypeMap = AssetsEnum.assetsStatusTypeMap;
        checkAssetsPictureRespDTO.setRealAssetsStatusName(assetsStatusTypeMap.get(checkAssetsPictureRespDTO.getRealAssetsStatus()));
        //持有人
        if (StringUtils.isNotBlank(checkAssetsPictureRespDTO.getRealAssetsHolder())) {
            List<SysUser> sysUserList = ambaseCommonService.selectUsersByIds(Arrays.asList(checkAssetsPictureRespDTO.getRealAssetsHolder()));
            checkAssetsPictureRespDTO.setRealAssetsHolderName(!CollectionUtils.isEmpty(sysUserList) ? sysUserList.get(0).getName() : "");
        }

        //获取资产分类图片配置
        CategoryPictureMetaRespDTO categoryPictureMetaRespDTO = ambaseCommonService.selectMetaDataByKey(checkAssetsPictureRespDTO.getSnapshotAssetsCategory());
        if (categoryPictureMetaRespDTO == null || CommonConstant.NUMBER_ZERO.equals(categoryPictureMetaRespDTO.getCheckUploadFlag())){
            checkAssetsPictureRespDTO.setCheckUploadFlag(CommonConstant.NUMBER_ZERO);
            return ResponseData.createSuccessResult(checkAssetsPictureRespDTO);
        }
        //资产不在员工名下，不进行图片展示
        if (StockCheckMissionEnum.OwnFlag.NO.getValue().equals(stockAssetsCheckTaskDetail.getOwnFlag())) {
            checkAssetsPictureRespDTO.setCheckUploadFlag(CommonConstant.NUMBER_ZERO);
            return ResponseData.createSuccessResult(checkAssetsPictureRespDTO);
        }

        checkAssetsPictureRespDTO.setCheckUploadFlag(CommonConstant.NUMBER_ONE);
        checkAssetsPictureRespDTO.setShowPicList(categoryPictureMetaRespDTO.getPictureMetaInfoList());
        String pictureUrlList = stockAssetsCheckTaskDetail.getPictureUrlList();
        // 如果表中存在图片地址直接使用
        if(StringUtils.isNotBlank(pictureUrlList)){
            String[] pictureUrlArray = pictureUrlList.split(StringConstant.SEMI_COLON_HALF);
            List<PictureMetaRespDTO> pictureMetaRespDTOList = new ArrayList<>(pictureUrlArray.length);
            for (String pictureUrl : pictureUrlArray) {
                PictureMetaRespDTO pictureMetaRespDTO = new PictureMetaRespDTO();
                pictureMetaRespDTO.setShowPicUrl(pictureUrl);
                pictureMetaRespDTOList.add(pictureMetaRespDTO);
            }
            checkAssetsPictureRespDTO.setActualPicList(pictureMetaRespDTOList);
        }else {
            //获取已上传图片信息
            QueryFileReqDTO queryFileReqDTO = new QueryFileReqDTO();
            queryFileReqDTO.setSystemModule(systemModule);
            queryFileReqDTO.setAttachModule(attachModule);
            queryFileReqDTO.setPageModule("CHECK_PICTURE");
            queryFileReqDTO.setRelId(stockAssetsCheckTaskDetail.getTakingPlanNo()+"_"+stockAssetsCheckTaskDetail.getSnapshotAssetsCode());
            ResponseData<List<PictureMetaRespDTO>> responseData = fileServiceApi.getFileUrlListByRelId(queryFileReqDTO);
            checkAssetsPictureRespDTO.setActualPicList(responseData.getData());
        }
        //返回结果
        return ResponseData.createSuccessResult(checkAssetsPictureRespDTO);
    }

    @Override
    public ResponseData queryAllAddress() {
        List<AddressProvinceRespDTO> addressProvinceRespDTOS = ambaseCommonService.queryAllAddress();
        return ResponseData.createSuccessResult(addressProvinceRespDTOS);
    }

    @Override
    public ResponseData queryTaskWarehouseAll(Long checkTaskId, Integer pageNum, String keyWord) {
        //参数校验
        if (checkTaskId == null){
            return ResponseData.createFailResult("参数不能为空");
        }
        Map<String, Object> resultMap = new HashMap<>(4);
        //设置参数
        StockCheckCommonReqDTO stockCheckCommonReqDTO = new StockCheckCommonReqDTO();
        stockCheckCommonReqDTO.setCheckTaskId(checkTaskId);
        stockCheckCommonReqDTO.setKeyWord(keyWord);
        /*stockCheckCommonReqDTO.setPageNum(pageNum);
        stockCheckCommonReqDTO.initPageDefaultParam();
        int count = stockCheckMissionDetailMapper.queryTaskWarehouseCount(stockCheckCommonReqDTO);
        resultMap.put("warehouseCount",count);
        if (count<=0) {
            resultMap.put("warehouseList",new HashMap<>());
        }*/
        //分页查询
        List<Map<String,String>> warehouseMap = stockCheckMissionDetailMapper.queryTaskWarehousePage(stockCheckCommonReqDTO);
        resultMap.put("warehouseList",warehouseMap);

        return ResponseData.createSuccessResult(resultMap);
    }

    /**
     * @param:
     * @description: 员工手动结束盘点
     * @return:
     * @author: <EMAIL>
     * @date: 2022/1/20
     */
    @Override
    public ResponseData employeeFinishCheck(Long checkTaskId) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //参数校验
        if (null == checkTaskId) {
            return ResponseData.createFailResult("盘点任务id不能为空");
        }
        // 获取盘点任务
        StockAssetsCheckTask stockAssetsCheckTask = stockAssetsCheckTaskMapper.selectByPrimaryKey(checkTaskId);
        if(null == stockAssetsCheckTask){
            return ResponseData.createFailResult("盘点任务不存在");
        }
        if(!StockCheckMissionEnum.TaskStatus.PROGRESSING.getValue().equals(stockAssetsCheckTask.getCheckTaskStatus())){
            return ResponseData.createFailResult("盘点任务不是进行中的状态");
        }
        // 首先在盘点任务中查询是否有当前任务人的任务
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        criteria.andCheckTaskIdEqualTo(checkTaskId);
        criteria.andCheckPeopleLike(jwtUser.getEmployeeCode() + StringConstant.PERCENT);
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        // 只有存在任务才需要判断盘点任务是否完成
        if(!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            List<StockAssetsCheckTaskDetail> unCheckStockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailList.stream().filter(stockAssetsCheckTaskDetail -> stockAssetsCheckTaskDetail.getCheckFlag().equals(CommonConstant.NUMBER_ZERO)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(unCheckStockAssetsCheckTaskDetailList)){
                return ResponseData.createFailResult("您还有未盘点资产，请盘点完成后再手动结束盘点");
            }
        }
        // 查询未完成的待办任务并结束
        List<ApvTask> taskList = wflService.endWflBusinessReturnTaskList(checkTaskId + stockAssetsCheckTask.getTakingPlanNo(), FlowCodeEnum.STOCK_ASSETS_CHECK, jwtUser.getEmployeeCode());
        if(CollectionUtils.isEmpty(taskList)){
            return ResponseData.createFailResult("不存在未完成的待办任务，无需结束待办");
        }
        // 因为结束个人待办，集合中只会有一个值，返回给前端刷新页面使用
        ApvTaskRespDTO apvTaskRespDTO = ConvertUtil.convertToType(ApvTaskRespDTO.class, taskList.get(CommonConstant.NUMBER_ZERO));
        // 只有存在任务才需要更新
        Date currentTime = new Date();
        if(!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            // 批量更新数据库中的待办状态
            stockAssetsCheckTaskDetailList.forEach(stockAssetsCheckTaskDetail -> {
                stockAssetsCheckTaskDetail.setApvStatus(CommonConstant.NUMBER_ONE);
                stockAssetsCheckTaskDetail.setApproveTime(currentTime);
            });
            stockCheckMissionDetailMapper.batchUpdate(stockAssetsCheckTaskDetailList);
        }
        return ResponseData.createSuccessResult(apvTaskRespDTO);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData employeeFinishCheckSuper(Long checkTaskId) {
        JwtUser jwtUser = SecurityUtil.getJwtUser();
        //参数校验
        if (null == checkTaskId) {
            return ResponseData.createFailResult("盘点任务id不能为空");
        }
        // 获取盘点任务
        StockAssetsCheckTask stockAssetsCheckTask = stockAssetsCheckTaskMapper.selectByPrimaryKey(checkTaskId);
        if(null == stockAssetsCheckTask){
            return ResponseData.createFailResult("盘点任务不存在");
        }
        if(!StockCheckMissionEnum.TaskStatus.PROGRESSING.getValue().equals(stockAssetsCheckTask.getCheckTaskStatus())){
            return ResponseData.createFailResult("盘点任务不是进行中的状态");
        }
        String checkPerson = jwtUser.getEmployeeCode();
        // 首先在盘点任务中查询是否有当前任务人的任务
        StockAssetsCheckTaskDetailExample stockAssetsCheckTaskDetailExample = new StockAssetsCheckTaskDetailExample();
        StockAssetsCheckTaskDetailExample.Criteria criteria = stockAssetsCheckTaskDetailExample.createCriteria();
        criteria.andCheckTaskIdEqualTo(checkTaskId);
        criteria.andCheckPeopleLike(StringConstant.PERCENT + checkPerson + StringConstant.PERCENT);
        List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailMapper.selectByExample(stockAssetsCheckTaskDetailExample);
        // 只有存在任务才需要判断盘点任务是否完成
        if(!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            List<StockAssetsCheckTaskDetail> unCheckStockAssetsCheckTaskDetailList = stockAssetsCheckTaskDetailList.stream().filter(stockAssetsCheckTaskDetail -> stockAssetsCheckTaskDetail.getCheckFlag().equals(CommonConstant.NUMBER_ZERO)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(unCheckStockAssetsCheckTaskDetailList)){
                return ResponseData.createFailResult("您还有未盘点资产，请盘点完成后再手动结束盘点");
            }
        }
        // 查询未完成的待办任务并结束
        List<ApvTask> taskList = wflService.endWflBusinessReturnTaskList(checkTaskId + stockAssetsCheckTask.getTakingPlanNo(), FlowCodeEnum.STOCK_ASSETS_CHECK, jwtUser.getEmployeeCode());
        if(CollectionUtils.isEmpty(taskList)){
            return ResponseData.createFailResult("不存在未完成的待办任务，无需结束待办");
        }
        // 因为结束个人待办，集合中只会有一个值，返回给前端刷新页面使用
        ApvTaskRespDTO apvTaskRespDTO = ConvertUtil.convertToType(ApvTaskRespDTO.class, taskList.get(CommonConstant.NUMBER_ZERO));
        // 只有存在任务才需要更新
        Date currentTime = new Date();
        if(!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            // 批量更新数据库中的待办状态
            boolean isHaveErrorAssets = false;
            for (StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail : stockAssetsCheckTaskDetailList) {
                stockAssetsCheckTaskDetail.setApvStatus(CommonConstant.NUMBER_ONE);
                stockAssetsCheckTaskDetail.setApproveTime(currentTime);
                if(!StockCheckMissionEnum.OwnFlag.YES.getValue().equals(stockAssetsCheckTaskDetail.getOwnFlag())){
                    isHaveErrorAssets = true;
                }
            }
            stockCheckMissionDetailMapper.batchUpdate(stockAssetsCheckTaskDetailList);
            // 同时判断如果有异常资产，发送代办
            if(isHaveErrorAssets){
                List<Map<String, Object>> dateItemListExtend = new ArrayList<>(CommonConstant.NUMBER_ONE);
                // 添加盘点人
                dateItemListExtend.add(StockBeanMap.putKeyValueAndGetMap(WorkflowConstants.CHECK_PERSON, checkPerson));
                WflInfo wflInfo = WorkFlowUtil.createWlfInfo(FlowCodeEnum.CHECK_ABNORMAL_ASSETS, stockAssetsCheckTask.getCheckTaskNo(), dateItemListExtend);
                //异步发起工作流发
                wflService.beginAct(wflInfo);
            }
        }
        return ResponseData.createSuccessResult(apvTaskRespDTO);
    }

    @Override
    public ResponseData queryEmployeeAbnormalCheckAssets(String checkTaskNo, String checkPerson) {
        if(StringUtils.isBlank(checkTaskNo)){
            return ResponseData.createFailResult("盘点任务号不能为空");
        }
        if(StringUtils.isBlank(checkPerson)){
            return ResponseData.createFailResult("盘点人不能为空");
        }
        // 判断盘点任务是否存在
        StockAssetsCheckTaskExample stockAssetsCheckTaskExample = new StockAssetsCheckTaskExample();
        StockAssetsCheckTaskExample.Criteria stockAssetsCheckTaskExampleCriteria = stockAssetsCheckTaskExample.createCriteria();
        stockAssetsCheckTaskExampleCriteria.andCheckTaskNoEqualTo(checkTaskNo);
        List<StockAssetsCheckTask> stockAssetsCheckTaskList = stockAssetsCheckTaskMapper.selectByExample(stockAssetsCheckTaskExample);
        if(CollectionUtils.isEmpty(stockAssetsCheckTaskList)){
            return ResponseData.createFailResult("盘点任务不存在");
        }
        StockAssetsCheckTask stockAssetsCheckTask = stockAssetsCheckTaskList.get(CommonConstant.NUMBER_ZERO);
        SysUserBasicInfo sysUserBasicInfo = ambaseCommonService.queryUserBasicInfoByEmpId(checkPerson);
        if(null == sysUserBasicInfo){
            return ResponseData.createFailResult("盘点人不存在");
        }
        StockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO = stockCheckInputDataServiceHelper.getStockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO(sysUserBasicInfo, stockAssetsCheckTask);
        return ResponseData.createSuccessResult(stockCheckAssetsEmployeeAbnormalAssetsHeadRespDTO);
    }

    @Override
    public ResponseData submitCheckResultSuper(StockCheckSubmitResultSuperReqDTO stockCheckSubmitResultSuperReqDTO, JwtUser user) {
        String loginUser= user.getEmployeeCode();
        // 参数校验
        StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail = new StockAssetsCheckTaskDetail();
        String errorMessage = stockCheckInputDataServiceHelper.checkSubmitCheckResultSuperParams(stockCheckSubmitResultSuperReqDTO, stockAssetsCheckTaskDetail);
        if(StringUtils.isNotBlank(errorMessage)){
            return ResponseData.createFailResult(errorMessage);
        }
        // 获取stockAssetsCheckTaskDetail
        stockAssetsCheckTaskDetail = stockCheckInputDataServiceHelper.getUpdateStockAssetsCheckTaskDetail(stockCheckSubmitResultSuperReqDTO, stockAssetsCheckTaskDetail, loginUser);
        stockAssetsCheckTaskDetailMapper.updateByPrimaryKey(stockAssetsCheckTaskDetail);
        return ResponseData.createSuccessResult();
    }

    /**
     * 转换为dto
     * @param stockAssetsCheckTaskDetailList
     */
    private List<StockAssetsCheckTaskDetailRespDTO> changeDetailToRespDTO(JwtUser user, List<StockAssetsCheckTaskDetail> stockAssetsCheckTaskDetailList) {
        List<StockAssetsCheckTaskDetailRespDTO> stockAssetsCheckTaskDetailRespDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stockAssetsCheckTaskDetailList)){
            Map<Integer, String> statusTypeMap = AssetsEnum.assetsStatusTypeMap;
            Map<Integer, String> conditionsMap = AssetsEnum.conditionsMap;

            //用来将仓库编码和人员工号去重
            Set<String> warehouseCodes = new HashSet<>();
            Set<String> empIds = new HashSet<>();
            Set<String> deptIdSet = new HashSet<>();
            Set<String> addressCodeSet = new HashSet<>();
            //查询接口入参数
            List<String> warehouseCodesParam = new ArrayList<>();
            List<String> empIdsParam = new ArrayList<>();
            List<String> deptIdsParam = new ArrayList<>();
            List<String> addressCodeParam = new ArrayList<>();
            stockAssetsCheckTaskDetailList.stream().forEach(dto -> {
                empIds.add(dto.getSnapshotAssetsHolder());
                empIds.add(dto.getRealAssetsHolder());
                String checkPeople = dto.getCheckPeople();
                if (!org.springframework.util.StringUtils.isEmpty(checkPeople)) {
                    String[] checkPeooples = checkPeople.split(StringConstant.SEMI_COLON_HALF);
                    empIds.addAll(Arrays.asList(checkPeooples));
                }
                empIds.add(dto.getAssetsKeeper());
                warehouseCodes.add(dto.getSnapshotWarehouseCode());
                warehouseCodes.add(dto.getRealWarehouseCode());
                deptIdSet.add(dto.getCostDept());
                addressCodeSet.add(dto.getHolderAddressProvince());
                addressCodeSet.add(dto.getHolderAddressCity());
                addressCodeSet.add(dto.getHolderAddressCounty());
            });
            warehouseCodesParam.addAll(warehouseCodes);
            empIdsParam.addAll(empIds);
            deptIdsParam.addAll(deptIdSet);
            addressCodeParam.addAll(addressCodeSet);

            Map<String, WarehouseRespDTO> warehouseRespDTOMap = stockWarehouseService.selectWarehouseDetailMapByCode(warehouseCodesParam);
            if (warehouseRespDTOMap == null) {
                warehouseRespDTOMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
            }
            Map<String, SysUser> sysUserMap = ambaseCommonService.selectSysUserMapByIds(empIdsParam);
            if (sysUserMap == null){
                sysUserMap = new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
            }
            Map<String, SysDept> sysDeptMap = ambaseCommonService.selectSysDeptMapByIds(deptIdsParam);
            Map<String,String> addressMap = ambaseCommonService.queryAddressName(addressCodeParam);

            for(StockAssetsCheckTaskDetail stockAssetsCheckTaskDetail : stockAssetsCheckTaskDetailList){
                StockAssetsCheckTaskDetailRespDTO stockAssetsCheckTaskDetailRespDTO = new StockAssetsCheckTaskDetailRespDTO();
                // 如果当前持有人持有资产
                if(StringUtils.equals(user.getEmployeeCode(), stockAssetsCheckTaskDetail.getSnapshotAssetsHolder())){
                    // 如果是否拥有资产没有值，但是又是我的资产，而且盘点过了，那就是非本人盘点
                    if(StockCheckMissionEnum.OwnFlag.DEFAT.getValue().equals(stockAssetsCheckTaskDetail.getOwnFlag())){
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethod(StockAssetsCheckTaskDetailEnum.CheckMethod.NOT_MYSELF_CHECK.getValue());
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethodName(StockAssetsCheckTaskDetailEnum.CheckMethod.NOT_MYSELF_CHECK.getDesc());
                    // 这里是本人盘点拥有该资产
                    }else if(StockCheckMissionEnum.OwnFlag.YES.getValue().equals(stockAssetsCheckTaskDetail.getOwnFlag())){
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethod(StockAssetsCheckTaskDetailEnum.CheckMethod.UNDER_MY_NAME.getValue());
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethodName(StockAssetsCheckTaskDetailEnum.CheckMethod.UNDER_MY_NAME.getDesc());
                    // 这里是本人盘点未拥有该资产
                    }else if(StockCheckMissionEnum.OwnFlag.NO.getValue().equals(stockAssetsCheckTaskDetail.getOwnFlag())){
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethod(StockAssetsCheckTaskDetailEnum.CheckMethod.NOT_HAVE_ASSETS.getValue());
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethodName(StockAssetsCheckTaskDetailEnum.CheckMethod.NOT_HAVE_ASSETS.getDesc());
                    }
                // 就是上报资产
                }else {
                    // 如果资产表中不存在就是在我名下
                    StockAssetsExample stockAssetsExample = new StockAssetsExample();
                    StockAssetsExample.Criteria criteria = stockAssetsExample.createCriteria();
                    String realAssetsCode = stockAssetsCheckTaskDetail.getRealAssetsCode();
                    if(StringUtils.isNotBlank(realAssetsCode)){
                        criteria.andAssetsCodeEqualTo(realAssetsCode);
                    }else {
                        criteria.andSnCodeEqualTo(stockAssetsCheckTaskDetail.getRealAssetsSnno());
                    }
                    List<StockAssets> stockAssetsList = stockAssetsMapper.selectByExample(stockAssetsExample);
                    if(CollectionUtils.isEmpty(stockAssetsList)){
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethod(StockAssetsCheckTaskDetailEnum.CheckMethod.UNDER_MY_NAME.getValue());
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethodName(StockAssetsCheckTaskDetailEnum.CheckMethod.UNDER_MY_NAME.getDesc());
                    }else {
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethod(StockAssetsCheckTaskDetailEnum.CheckMethod.REPORT_OTHER_ASSETS.getValue());
                        stockAssetsCheckTaskDetailRespDTO.setCheckMethodName(StockAssetsCheckTaskDetailEnum.CheckMethod.REPORT_OTHER_ASSETS.getDesc());
                    }
                }
                BeanUtils.copyProperties(stockAssetsCheckTaskDetail,stockAssetsCheckTaskDetailRespDTO);
                //给人员类字段赋值
                stockAssetsCheckTaskDetailRespDTO.setSnapshotAssetsHolderName(sysUserMap.getOrDefault(stockAssetsCheckTaskDetail.getSnapshotAssetsHolder(),new SysUser()).getName());
                stockAssetsCheckTaskDetailRespDTO.setRealAssetsHolderName(sysUserMap.getOrDefault(stockAssetsCheckTaskDetail.getRealAssetsHolder(),new SysUser()).getName());
                stockAssetsCheckTaskDetailRespDTO.setAssetsKeeperName(sysUserMap.getOrDefault(stockAssetsCheckTaskDetail.getAssetsKeeper(),new SysUser()).getName());
                stockAssetsCheckTaskDetailRespDTO.setCostDeptName(sysDeptMap.getOrDefault(stockAssetsCheckTaskDetailRespDTO.getCostDept(),new SysDept()).getDeptName());
                //领用人省市县地址
                stockAssetsCheckTaskDetailRespDTO.setHolderAddressProvinceName(addressMap.get(stockAssetsCheckTaskDetail.getHolderAddressProvince()));
                stockAssetsCheckTaskDetailRespDTO.setHolderAddressCityName(addressMap.get(stockAssetsCheckTaskDetail.getHolderAddressCity()));
                stockAssetsCheckTaskDetailRespDTO.setHolderAddressCountyName(addressMap.get(stockAssetsCheckTaskDetail.getHolderAddressCounty()));
                //盘点人字段特殊处理
                StringBuffer checkPeopleBuffer = new StringBuffer();
                if (!org.springframework.util.StringUtils.isEmpty(stockAssetsCheckTaskDetail.getCheckPeople())){
                    String[] peoplesCodes = stockAssetsCheckTaskDetail.getCheckPeople().split(StringConstant.SEMI_COLON_HALF);
                    for(String people : peoplesCodes){
                        checkPeopleBuffer.append(sysUserMap.getOrDefault(people,new SysUser()).getName() + StringConstant.SPACE + people + StringConstant.SEMI_COLON_HALF);
                    }
                }
                String checkPeopleResult = checkPeopleBuffer.toString();
                if (!org.springframework.util.StringUtils.isEmpty(checkPeopleResult)){
                    checkPeopleResult = checkPeopleResult.substring(0,checkPeopleResult.length() - 1);
                }
                stockAssetsCheckTaskDetailRespDTO.setCheckPeopleName(checkPeopleResult);
                //给仓库类字段赋值
                stockAssetsCheckTaskDetailRespDTO.setSnapshotWarehouseName(warehouseRespDTOMap.getOrDefault(stockAssetsCheckTaskDetail.getSnapshotWarehouseCode(),new WarehouseRespDTO()).getName());
                stockAssetsCheckTaskDetailRespDTO.setRealWarehouseName(warehouseRespDTOMap.getOrDefault(stockAssetsCheckTaskDetail.getRealWarehouseCode(),new WarehouseRespDTO()).getName());
                //给状态类字段赋值
                stockAssetsCheckTaskDetailRespDTO.setSnapshotAssetsStatusName(statusTypeMap.get(stockAssetsCheckTaskDetail.getSnapshotAssetsStatus()));
                stockAssetsCheckTaskDetailRespDTO.setSnapshotAssetsConditionsName(conditionsMap.get(stockAssetsCheckTaskDetailRespDTO.getSnapshotAssetsConditions()));
                //待盘点状态，将数据库的盘点字段默认值置空
                if (StockCheckMissionEnum.DetailFlag.NO.getValue().equals(stockAssetsCheckTaskDetail.getCheckFlag())){
                    stockAssetsCheckTaskDetailRespDTO.setRealAssetsStatus(null);
                    stockAssetsCheckTaskDetailRespDTO.setRealAssetsConditions(null);
                    stockAssetsCheckTaskDetailRespDTO.setDifference(null);
                }else{
                    stockAssetsCheckTaskDetailRespDTO.setRealAssetsStatusName(statusTypeMap.get(stockAssetsCheckTaskDetail.getRealAssetsStatus()));
                    stockAssetsCheckTaskDetailRespDTO.setRealAssetsConditionsName(conditionsMap.get(stockAssetsCheckTaskDetailRespDTO.getRealAssetsConditions()));
                    stockAssetsCheckTaskDetailRespDTO.setDifferenceName(StockCheckMissionEnum.DetailFlag.NO.getValue().equals(stockAssetsCheckTaskDetail.getDifference()) ? "是" : "否");
                }
                if (StockCheckMissionEnum.CheckFlag.NO.getValue().equals(stockAssetsCheckTaskDetail.getCheckFlag())){
                    stockAssetsCheckTaskDetailRespDTO.setCheckFlagName("待盘点");
                }else if (StockCheckMissionEnum.CheckFlag.YES.getValue().equals(stockAssetsCheckTaskDetail.getCheckFlag())){
                    stockAssetsCheckTaskDetailRespDTO.setCheckFlagName("已盘点");
                }else{
                    stockAssetsCheckTaskDetailRespDTO.setCheckFlagName("已取消");
                }
                stockAssetsCheckTaskDetailRespDTOList.add(stockAssetsCheckTaskDetailRespDTO);
            }
        }
        return stockAssetsCheckTaskDetailRespDTOList;
    }

    @Override
    public ResponseData updateEmployeeAbnormalCheckAssets(StockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO, JwtUser loginUser) {
        // 校验请求参数是否合法
        List<StockAssetsCheckTaskDetail> updateStockAssetsCheckTaskDetailList = new ArrayList<>();
        String errorMessage = stockCheckInputDataServiceHelper.checkUpdateEmployeeAbnormalCheckAssetsParams(stockCheckAssetsEmployeeAbnormalAssetsHeadReqDTO, updateStockAssetsCheckTaskDetailList, loginUser);
        if(StringUtils.isNotBlank(errorMessage)){
            return ResponseData.createFailResult(errorMessage);
        }
        // 批量更新异常资产列表
        stockAssetsCheckTaskDetailService.batchUpdate(updateStockAssetsCheckTaskDetailList);
        return ResponseData.createSuccessResult();
    }
}
