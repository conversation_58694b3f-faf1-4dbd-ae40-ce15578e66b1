package com.gz.eim.am.stock.entity;

import java.util.Date;

public class StockTakingSnapshotHistory {
    private Long id;

    private String assetsName;

    private String assetsCode;

    private String category;

    private Integer conditions;

    private Integer status;

    private String holder;

    private Date holderTime;

    private String warehouseCode;

    private String assetsKeeper;

    private String holderAddress;

    private String snCode;

    private String deviceCode;

    private String takingPlanNo;

    private String needDept;

    private String costDept;

    private String purchaseNo;

    private String busLargeRegion;

    private String busCity;

    private String brand;

    private String model;

    private String cpu;

    private String hardDisk;

    private String ramMemory;

    private String holderAddressProvince;

    private String holderAddressCity;

    private String holderAddressCounty;

    private Integer isConfirm;

    private Integer isDelete;

    private Integer isAssignTask;

    private String createdBy;

    private Date createdAt;

    private String updatedBy;

    private Date updatedAt;

    private Date copyAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName == null ? null : assetsName.trim();
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode == null ? null : assetsCode.trim();
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    public Integer getConditions() {
        return conditions;
    }

    public void setConditions(Integer conditions) {
        this.conditions = conditions;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getHolder() {
        return holder;
    }

    public void setHolder(String holder) {
        this.holder = holder == null ? null : holder.trim();
    }

    public Date getHolderTime() {
        return holderTime;
    }

    public void setHolderTime(Date holderTime) {
        this.holderTime = holderTime;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode == null ? null : warehouseCode.trim();
    }

    public String getAssetsKeeper() {
        return assetsKeeper;
    }

    public void setAssetsKeeper(String assetsKeeper) {
        this.assetsKeeper = assetsKeeper == null ? null : assetsKeeper.trim();
    }

    public String getHolderAddress() {
        return holderAddress;
    }

    public void setHolderAddress(String holderAddress) {
        this.holderAddress = holderAddress == null ? null : holderAddress.trim();
    }

    public String getSnCode() {
        return snCode;
    }

    public void setSnCode(String snCode) {
        this.snCode = snCode == null ? null : snCode.trim();
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode == null ? null : deviceCode.trim();
    }

    public String getTakingPlanNo() {
        return takingPlanNo;
    }

    public void setTakingPlanNo(String takingPlanNo) {
        this.takingPlanNo = takingPlanNo == null ? null : takingPlanNo.trim();
    }

    public String getNeedDept() {
        return needDept;
    }

    public void setNeedDept(String needDept) {
        this.needDept = needDept == null ? null : needDept.trim();
    }

    public String getCostDept() {
        return costDept;
    }

    public void setCostDept(String costDept) {
        this.costDept = costDept == null ? null : costDept.trim();
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo == null ? null : purchaseNo.trim();
    }

    public String getBusLargeRegion() {
        return busLargeRegion;
    }

    public void setBusLargeRegion(String busLargeRegion) {
        this.busLargeRegion = busLargeRegion == null ? null : busLargeRegion.trim();
    }

    public String getBusCity() {
        return busCity;
    }

    public void setBusCity(String busCity) {
        this.busCity = busCity == null ? null : busCity.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getCpu() {
        return cpu;
    }

    public void setCpu(String cpu) {
        this.cpu = cpu == null ? null : cpu.trim();
    }

    public String getHardDisk() {
        return hardDisk;
    }

    public void setHardDisk(String hardDisk) {
        this.hardDisk = hardDisk == null ? null : hardDisk.trim();
    }

    public String getRamMemory() {
        return ramMemory;
    }

    public void setRamMemory(String ramMemory) {
        this.ramMemory = ramMemory == null ? null : ramMemory.trim();
    }

    public String getHolderAddressProvince() {
        return holderAddressProvince;
    }

    public void setHolderAddressProvince(String holderAddressProvince) {
        this.holderAddressProvince = holderAddressProvince == null ? null : holderAddressProvince.trim();
    }

    public String getHolderAddressCity() {
        return holderAddressCity;
    }

    public void setHolderAddressCity(String holderAddressCity) {
        this.holderAddressCity = holderAddressCity == null ? null : holderAddressCity.trim();
    }

    public String getHolderAddressCounty() {
        return holderAddressCounty;
    }

    public void setHolderAddressCounty(String holderAddressCounty) {
        this.holderAddressCounty = holderAddressCounty == null ? null : holderAddressCounty.trim();
    }

    public Integer getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Integer isConfirm) {
        this.isConfirm = isConfirm;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsAssignTask() {
        return isAssignTask;
    }

    public void setIsAssignTask(Integer isAssignTask) {
        this.isAssignTask = isAssignTask;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getCopyAt() {
        return copyAt;
    }

    public void setCopyAt(Date copyAt) {
        this.copyAt = copyAt;
    }
}