package com.gz.eim.am.stock.constant;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/3/16
 * @description 字符串公用
 */
public class StringConstant {

    /**
     * 字符："&"
     */
    public static final String AMPERSAND = "&";
    /**
     * 字符："="
     */
    public static final String EQUAL = "=";
    /**
     * 字符："?"
     */
    public static final String QUESTION = "?";
    /**
     * 空字符：""
     */
    public static final String EMPTY = "";
    /**
     * 字符：":"
     */
    public static final String COLON = ":";
    /**
     * 中文字符：":"
     */
    public static final String CHINESE_COLON = "：";
    /**
     * 字符：";"
     */
    public static final String SEMI_COLON_HALF = ";";
    /**
     * 字符："；"
     */
    public static final String SEMI_COLON_FULL = "；";
    /**
     * 字符："-"
     */
    public static final String HYPHEN = "-";
    /**
     * 字符：","
     */
    public static final String COMMA = ",";
    /**
     * 字符："."
     */
    public static final String DOT = ".";
    /**
     * 字符：" "
     */
    public static final String SPACE = " ";
    /**
     * 字符："_"
     */
    public static final String UNDER_LINE = "_";
    /**
     * 换行符："\n"
     */
    public static final String NEW_LINE = "\n";
    /**
     * 字符: "0"
     */
    public static final String ZERO = "0";

    /**
     * 方向（右侧）
     */
    public static final int RIGHT = 0;

    /**
     * 方向（左侧）
     */
    public static final int LEFT = 1;

    /**
     * 字符："、"
     */
    public static final String DUN_HAO = "、";

    /**
     * 字符："/"
     */
    public static final String SLASH = "/";
    /**
     * 字符："#"
     */
    public static final String PAND = "#";
    /**
     * 字符："%"
     */
    public static final String PERCENT = "%";
    /**
     * 字段
     */
    public static final String FILED = "field";

    /**
     * 字符："，"
     */
    public static final String CHINESE_COMMA = "，";

    /**
     * 字符："。"
     */
    public static final String CHINESE_DOT = "。";

    /**
     * 字符："*"
     */
    public static final String ASTERISK = "*";

    /**
     * 下划线
     */
    public static final String UNDERLINE = "_";
    /**
     * @
     */
    public static final String AT = "@";
    /**
     * 英文(
     */
    public static final String ENGLISH_LEFT_PARENTHESIS = "(";
    /**
     * 英文)
     */
    public static final String ENGLISH_RIGHT_PARENTHESIS = ")";

}
