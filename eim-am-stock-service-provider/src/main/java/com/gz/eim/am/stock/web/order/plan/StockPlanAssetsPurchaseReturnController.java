package com.gz.eim.am.stock.web.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.JsonUtil;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.order.plan.StockPlanAssetsPurchaseReturnApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.order.plan.*;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsPurchaseReturnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: weijunjie
 * @date: 2020/12/21
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/purchase-return")
public class StockPlanAssetsPurchaseReturnController implements StockPlanAssetsPurchaseReturnApi {

    @Value("${namespace.name}")
    private String nameSpace;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StockPlanAssetsPurchaseReturnService stockPlanAssetsPurchaseReturnService;

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_RETURN)
    public ResponseData savePlanPurchaseReturn(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info("/api/am/stock/purchase-return/save {}", deliveryPlanHeadReqDTO.toString());
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsPurchaseReturnService.savePlanPurchaseReturn(deliveryPlanHeadReqDTO);
        } catch (ServiceUncheckedException e) {
            log.info("采购退货申请单保存失败{}", e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("采购退货申请单保存报错{}", e.getMessage());
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_RETURN)
    public ResponseData selectPlanPurchaseReturn(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO) {
        log.info("/api/am/stock/purchase-return/search {}", deliveryPlanSearchReqDTO.toString());
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsPurchaseReturnService.selectPlanPurchaseReturn(deliveryPlanSearchReqDTO);
        } catch (ServiceUncheckedException e) {
            log.error("采购退货申请单分页查询失败{}", e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("采购退货申请单分页查询报错{}", e.getMessage());
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanPurchaseReturnById(Long deliveryPlanHeadId) {
        log.info("/api/am/stock/purchase-return/search/{}", deliveryPlanHeadId);
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsPurchaseReturnService.selectPlanPurchaseReturnById(deliveryPlanHeadId);
        } catch (Exception e) {
            log.error("采购退货申请单详情查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData planPurchaseReturnOutBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info("/api/am/stock/purchase-return/outBound {}", deliveryPlanHeadReqDTO.toString());
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_PURCHASE_RETURN_BOUND + deliveryPlanHeadReqDTO.getDeliveryPlanHeadId();
        try {
            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                JwtUser user = SecurityUtil.getJwtUser();
                res = this.stockPlanAssetsPurchaseReturnService.planAssetsTransferOutBound(deliveryPlanHeadReqDTO, user);
                redisUtil.expire(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            } else {
                res = ResponseData.createFailResult("系统繁忙,请稍后尝试...");
            }
        } catch (Exception e) {
            redisUtil.deleteByKey(nameSpace, lockKey);
            log.error("采购退货申请单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_PLAN_RETURN)
    public ResponseData cancelPlanPurchaseReturnById(Long deliveryPlanHeadId) {
        log.info("/api/am/stock/purchase-return/cancel/{}", deliveryPlanHeadId);
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsPurchaseReturnService.cancelPlanPurchaseReturnById(deliveryPlanHeadId);
        } catch (Exception e) {
            log.error("采购退货申请单取消错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData savePlanPurchase(List<PurchaseDeliveryHeadDTO> purchaseDeliveryHeadDTOS) {
        log.info("/api/am/stock/purchase-return/return/save {}", JsonUtil.getJsonString(purchaseDeliveryHeadDTOS));
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsPurchaseReturnService.savePlanPurchase(purchaseDeliveryHeadDTOS);
        } catch (Exception e) {
            log.error("采购系统对接生成退货申请单错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData cancelPlanPurchaseLines(List<PurchaseOrderCancelDTO> purchaseOrderCancelDTOS) {
        log.info("/api/am/stock/purchase-return/return/cancel {}", JsonUtil.getJsonString(purchaseOrderCancelDTOS));
        ResponseData res = null;
        try {
            res = this.stockPlanAssetsPurchaseReturnService.cancelPlanPurchaseLines(purchaseOrderCancelDTOS);
        } catch (Exception e) {
            log.error("采购系统对接单据取消接口错误", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
