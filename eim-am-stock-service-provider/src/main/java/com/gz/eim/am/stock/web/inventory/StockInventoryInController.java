package com.gz.eim.am.stock.web.inventory;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.inventory.StockInventoryInApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.inventory.*;
import com.gz.eim.am.stock.entity.vo.download.ExportInventoryFlowEntity;
import com.gz.eim.am.stock.service.inventory.StockInventoryInService;
import com.gz.eim.am.stock.util.em.DeliveryPlanHeadEnum;
import com.gz.eim.am.stock.util.em.InventoryEnum;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 10:47
 */
@RestController
@RequestMapping("/api/am/stock")
public class StockInventoryInController implements StockInventoryInApi {

    private final Logger logger = LoggerFactory.getLogger(StockInventoryInController.class);

    @Autowired
    private StockInventoryInService service;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${namespace.name}")
    private String nameSpace;

    @Override
    public ResponseData batchInventoryIn(InventoryInBatchReqDTO dto) {
        logger.info("/api/am/stock/inventory/batch {}", dto.toString());
        ResponseData res = null;
//        String lockKey = RedisKeyConstants.INVENTORY_IN;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
//            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                res = this.service.addInventoryInBatch(dto, user);
//                redisUtil.expire(nameSpace,lockKey, 1, TimeUnit.SECONDS);
//            } else {
//                res = ResponseData.createFailResult("系统繁忙,请稍后尝试...");
//            }
        }catch (ServiceUncheckedException e){
//            redisUtil.deleteByKey(nameSpace,lockKey);
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
//            redisUtil.deleteByKey(nameSpace,lockKey);
            logger.error("批量新增入库单", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryInventoryFlow(InventoryFlowReqDTO dto) {
        logger.info("/api/am/stock/inventory/flow {}", dto.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.inventoryFlow(dto, user);
        } catch (Exception e){
            logger.error("出入库流水查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData saveInventoryIn(InventoryInDTO inventoryInDTO) {
        logger.info("/api/am/stock/inventory {}", inventoryInDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.saveInventoryIn (inventoryInDTO, user);
        } catch (Exception e){
            logger.error("保存入库单", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectInventoryIn(InventoryInSearchReqDTO inventorySearchReqDTO) {
        logger.info("/api/am/stock/inventory {}", inventorySearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            List<Integer> inventoryInTypeList = Stream.of(InventoryEnum.InType.CARD_PURCHASE.getCode(),InventoryEnum.InType.PRESENT_PURCHASE.getCode()).collect(Collectors.toList());
            inventorySearchReqDTO.setInventoryInTypeList(inventoryInTypeList);
            res = this.service.selectInventoryIn (inventorySearchReqDTO, user);
        } catch (Exception e){
            logger.error("入库单分页查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.PURCHASE_IN)
    public ResponseData selectInventoryInByCode(Long inventoryInId) {
        logger.info("/api/am/stock/inventory/{}", inventoryInId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.selectInventoryInByCode (inventoryInId, user);
        } catch (Exception e){
            logger.error("根据编号查询入库单详情", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.GIFT_CARD_IN)
    public ResponseData selectInventoryInById(Long inventoryInId) {
        logger.info("/api/am/stock/inventory/card/ }", inventoryInId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.selectInventoryInById(inventoryInId, user);
        } catch (Exception e){
            logger.error("根据编号查询入库单详情", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    @Override
    public ResponseData inventoryInStorage(InventoryInDTO inventoryInDTO) {
        logger.info("/api/am/stock/inventory {}", inventoryInDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.inventoryInStorage (inventoryInDTO, user);
        } catch (Exception e){
            logger.error("入库单入库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectAccessibleInventoryIn(InventoryInSearchReqDTO inventorySearchReqDTO) {
        logger.info("/api/am/stock/inventory/accessible {}", inventorySearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.selectAccessibleInventoryIn (inventorySearchReqDTO, user);
        } catch (Exception e){
            logger.error("可入库的入库单分页查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectInventoryInSn(InventoryInSnSearchReqDTO inventoryInSnSearchReqDTO) {
        logger.info("/api/am/stock/inventory/sn/search {}", inventoryInSnSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.selectInventoryInSn (inventoryInSnSearchReqDTO, user);
        } catch (Exception e){
            logger.error("入库单序列号分页查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public void exportInventoryIn(InventoryFlowReqDTO inventoryFlowReqDTO, HttpServletRequest request, HttpServletResponse response) {
        logger.info("/api/am/stock/inventory/flow/export {}", inventoryFlowReqDTO.toString());
        try {

             JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------", user.getEmployeeCode());
            List<ExportInventoryFlowEntity> exportInventoryFlowEntityList = this.service.exportInventoryInFlow(inventoryFlowReqDTO, user);
            if (CollectionUtils.isEmpty(exportInventoryFlowEntityList)) {
                return;
            }

            final String fileName = "入库单流水" + DateUtils.dateFormat(new Date(), DateUtils.HOUR_PATTERN) + ".xlsx";
            ExcelUtil.createExcelWithBuffer(exportInventoryFlowEntityList, fileName, request, response);

        } catch (Exception e) {
            logger.error("入库单流水导出异常" + e.getMessage());
            return;
        }

        return;
    }

}
