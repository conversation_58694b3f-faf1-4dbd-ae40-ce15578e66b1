package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockAssetSn;
import com.gz.eim.am.stock.entity.StockAssetSnExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockAssetSnMapper {
    long countByExample(StockAssetSnExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockAssetSn record);

    int insertSelective(StockAssetSn record);

    List<StockAssetSn> selectByExample(StockAssetSnExample example);

    StockAssetSn selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockAssetSn record, @Param("example") StockAssetSnExample example);

    int updateByExample(@Param("record") StockAssetSn record, @Param("example") StockAssetSnExample example);

    int updateByPrimaryKeySelective(StockAssetSn record);

    int updateByPrimaryKey(StockAssetSn record);
}