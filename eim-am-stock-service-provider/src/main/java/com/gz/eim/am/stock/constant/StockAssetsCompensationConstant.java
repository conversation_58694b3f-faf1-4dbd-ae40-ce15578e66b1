package com.gz.eim.am.stock.constant;

/**
 * @className: StockAssetsCompensationConstant
 * @description: 资产赔偿常量值
 * @author: <EMAIL>
 * @date: 2022/2/17
 **/
public class StockAssetsCompensationConstant {

    /**
     * 处理方法
     * 1：资产无误
     */
    public static final Integer ASSETS_CORRECT = 1;

    /**
     * 处理方法
     * 2：资产扣款
     */
    public static final Integer ASSETS_COMPENSATION = 2;

    /**
     * 工卡扣款
     * 0：否
     */
    public static final Integer NO_WORK_CARD_DEDUCTION = 0;

    /**
     * 工卡扣款
     * 1：是
     */
    public static final Integer YES_WORK_CARD_DEDUCTION = 1;

    /**
     * 工卡默认资产编码
     */
    public static final String WORK_CARD_DEFAULT_ASSETS_CODE = "workCard";


    /**
     * 工卡默认资产分类编码
     */
    public static final String WORK_CARD_DEFAULT_ASSETS_CATEGORY_CODE = "workCard";

    /**
     * 系统生成离职归还单据备注
     */
    public static final String SYSTEM_CREATE_LEAVE_INVENTORY_REMARK = "离职审批系统生成单据";

    /**
     * 默认入职地址
     */
    public static final String DEFAULT_ENTRY_LOCATION = "11000001";

    /**
     * 推送wfl审批人集合名称
     */
    public static final String SEND_WFL_APPROVE_USER_LIST = "approveUserList";


}
