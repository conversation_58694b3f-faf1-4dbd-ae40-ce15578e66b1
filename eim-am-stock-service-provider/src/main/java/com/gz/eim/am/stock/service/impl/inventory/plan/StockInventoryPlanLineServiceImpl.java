package com.gz.eim.am.stock.service.impl.inventory.plan;

import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockInventoryInPlanLineMapper;
import com.gz.eim.am.stock.dao.inventory.plan.InventoryInPlanLineMapper;
import com.gz.eim.am.stock.dto.request.order.plan.PurchaseOrderCancelLineDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryPlanAssetRespDTO;
import com.gz.eim.am.stock.entity.StockAssetsCompensationRecord;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLine;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLineExample;
import com.gz.eim.am.stock.entity.vo.StockInventoryInPlanLineInfo;
import com.gz.eim.am.stock.service.inventory.plan.StockInventoryInPlanLineService;
import com.gz.eim.am.stock.util.common.ListUtil;
import com.gz.eim.am.stock.util.em.InventoryInPlanLineEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/12/10
 * @description:
 */
@Service
public class StockInventoryPlanLineServiceImpl implements StockInventoryInPlanLineService {
    @Autowired
    private InventoryInPlanLineMapper inventoryInPlanLineMapper;

    @Autowired
    private StockInventoryInPlanLineMapper stockInventoryInPlanLineMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchInsertStockInventoryPlanLine(List<StockInventoryInPlanLine> stockInventoryInPlanLineList) {
        if (CollectionUtils.isEmpty(stockInventoryInPlanLineList)) {
            return new Integer(0);
        }

        int count = 0;
        List<StockInventoryInPlanLine> newStockInventoryInPlanLineList = new ArrayList<>();
        if(stockInventoryInPlanLineList.size() > 0){
            int toIndex= CommonConstant.MAX_INSERT_COUNT;
            for(int i = 0;i<stockInventoryInPlanLineList.size();i+=CommonConstant.MAX_INSERT_COUNT){
                //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                if(i+CommonConstant.MAX_INSERT_COUNT>stockInventoryInPlanLineList.size()){
                    toIndex=stockInventoryInPlanLineList.size()-i;
                }
                List<StockInventoryInPlanLine> newSubDetailList = stockInventoryInPlanLineList.subList(i,i+toIndex);
                int insertCount = inventoryInPlanLineMapper.batchInsertStockInventoryPlanLine(newSubDetailList);
                newStockInventoryInPlanLineList.addAll(newSubDetailList);
                count = count + insertCount;
            }
        }

        stockInventoryInPlanLineList = new ArrayList<>();
        stockInventoryInPlanLineList.addAll(newStockInventoryInPlanLineList);
        return count;
    }

    @Override
    public List<InventoryInPlanLineRespDTO> selectBySelective(StockInventoryInPlanLine stockInventoryInPlanLine) {
        if(null == stockInventoryInPlanLine){
            return new ArrayList<> ();
        }
        return inventoryInPlanLineMapper.selectBySelective(stockInventoryInPlanLine);
    }

    @Override
    public StockInventoryInPlanLine selectByPrimaryKey(Long inventoryInPlanLineId) {
        return stockInventoryInPlanLineMapper.selectByPrimaryKey (inventoryInPlanLineId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrimaryKeySelective(StockInventoryInPlanLine stockInventoryInPlanLine) {
        if(null == stockInventoryInPlanLine){
            return false;
        }
        stockInventoryInPlanLineMapper.updateByPrimaryKeySelective (stockInventoryInPlanLine);
        return true;
    }

    @Override
    public List<InventoryPlanAssetRespDTO> selectInventoryPlanAssetRespDTOBySelective(StockInventoryInPlanLine stockInventoryInPlanLine) {
        if(null == stockInventoryInPlanLine){
            return new ArrayList<> ();
        }
        return inventoryInPlanLineMapper.selectInventoryPlanAssetRespDTOBySelective(stockInventoryInPlanLine);
    }

    @Override
    public boolean updateByExampleSelective(StockInventoryInPlanLine stockInventoryInPlanLine, StockInventoryInPlanLineExample stockInventoryInPlanLineExample) {
        if(null == stockInventoryInPlanLine || null == stockInventoryInPlanLineExample){
            return false;
        }

        stockInventoryInPlanLineMapper.updateByExampleSelective (stockInventoryInPlanLine, stockInventoryInPlanLineExample);
        return true;
    }

    @Override
    public List<StockInventoryInPlanLine> selectNotAlreadyIn(Long inventoryInPlanHeadId) {
        if(null == inventoryInPlanHeadId){
            return new ArrayList<> ();
        }

        StockInventoryInPlanLineExample example = new StockInventoryInPlanLineExample ();
        StockInventoryInPlanLineExample.Criteria criteria =  example.createCriteria ();
        criteria.andInventoryInPlanHeadIdEqualTo (inventoryInPlanHeadId);
        List<Integer> statusList = new ArrayList<>(2);
        statusList.add(InventoryInPlanLineEnum.Status.ALREADY_IN.getStatus ());
        statusList.add(InventoryInPlanLineEnum.Status.CANCEL.getStatus ());
        criteria.andStatusNotIn(statusList);

        return stockInventoryInPlanLineMapper.selectByExample (example);
    }

    @Override
    public Integer batchInsertStockInventoryPlanLineInfo(List<StockInventoryInPlanLineInfo> stockInventoryInPlanLineInfoList) {
        if (CollectionUtils.isEmpty(stockInventoryInPlanLineInfoList)) {
            return new Integer(0);
        }

        int count = 0;
        List<StockInventoryInPlanLineInfo> newStockInventoryInPlanLineInfoList = new ArrayList<>();
        if(stockInventoryInPlanLineInfoList.size() > 0){
            int toIndex= CommonConstant.MAX_INSERT_COUNT;
            for(int i = 0;i<stockInventoryInPlanLineInfoList.size();i+=CommonConstant.MAX_INSERT_COUNT){
                //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                if(i+CommonConstant.MAX_INSERT_COUNT>stockInventoryInPlanLineInfoList.size()){
                    toIndex=stockInventoryInPlanLineInfoList.size()-i;
                }
                List<StockInventoryInPlanLineInfo> newSubDetailList = stockInventoryInPlanLineInfoList.subList(i,i+toIndex);
                int insertCount = inventoryInPlanLineMapper.batchInsertStockInventoryPlanLineInfo(newSubDetailList);
                newStockInventoryInPlanLineInfoList.addAll(newSubDetailList);
                count = count + insertCount;
            }
        }

        stockInventoryInPlanLineInfoList = new ArrayList<>();
        stockInventoryInPlanLineInfoList.addAll(newStockInventoryInPlanLineInfoList);
        return count;
    }

    @Override
    public List<StockInventoryInPlanLine> selectLinesGroupByIdAndStatus(Long inventoryHeadId) {
        if (inventoryHeadId == null){
            return new ArrayList<>();
        }
        return inventoryInPlanLineMapper.selectLinesGroupByIdAndStatus(inventoryHeadId);
    }

    @Override
    public List<StockInventoryInPlanLine> selectLinesByIds(List<Long> lineIds) {
        StockInventoryInPlanLineExample example = new StockInventoryInPlanLineExample();
        StockInventoryInPlanLineExample.Criteria criteria = example.createCriteria();
        criteria.andInventoryInPlanLineIdIn(lineIds);
        return stockInventoryInPlanLineMapper.selectByExample(example);
    }

    @Override
    public List<StockInventoryInPlanLine> selectLinesByHeadIds(List<Long> lineIds) {
        StockInventoryInPlanLineExample example = new StockInventoryInPlanLineExample();
        StockInventoryInPlanLineExample.Criteria criteria = example.createCriteria();
        criteria.andInventoryInPlanHeadIdIn(lineIds);
        return stockInventoryInPlanLineMapper.selectByExample(example);
    }

    @Override
    public List<PurchaseOrderCancelLineDTO> selectNoCancelLines(List<PurchaseOrderCancelLineDTO> inventoryPurchaseOrderCancelLineDTOs, Integer type) {
        return inventoryInPlanLineMapper.selectNoCancelLines(inventoryPurchaseOrderCancelLineDTOs,type);
    }

    @Override
    public Integer updateStatusByReceiveItemNos(List<String> receiveItemNos, Integer status) {
        return inventoryInPlanLineMapper.updateStatusByReceiveItemNos(receiveItemNos,status);
    }

    @Override
    public Integer updateLineStatusByPurchaseDto(List<PurchaseOrderCancelLineDTO> inventoryPurchaseOrderCancelLineDTOs) {
        return inventoryInPlanLineMapper.updateLineStatusByPurchaseDto(inventoryPurchaseOrderCancelLineDTOs, SecurityUtil.getJwtUser().getEmployeeCode());
    }

    @Override
    public Integer insertStockInventoryPlanLine(StockInventoryInPlanLine stockInventoryInPlanLine) {
        return stockInventoryInPlanLineMapper.insertSelective(stockInventoryInPlanLine);
    }

    @Override
    public int batchUpdate(List<StockInventoryInPlanLine> list) {
        if(CollectionUtils.isEmpty(list)){
            return CommonConstant.NUMBER_ZERO;
        }
        // 如果大于1000条就分批次插入
        if(list.size() > CommonConstant.MAX_INSERT_COUNT){
            int count = CommonConstant.NUMBER_ZERO;
            List<List<StockInventoryInPlanLine>> stockInventoryInPlanLineListList = ListUtil.splitList(list, CommonConstant.MAX_INSERT_COUNT);
            for (List<StockInventoryInPlanLine> stockInventoryInPlanLines : stockInventoryInPlanLineListList) {
                count += inventoryInPlanLineMapper.batchUpdate(stockInventoryInPlanLines);
            }
            return count;
        }else {
            return inventoryInPlanLineMapper.batchUpdate(list);
        }
    }

    @Override
    public List<StockInventoryInPlanLine> selectLinesByHeadId(Long inventoryInPlanHeadId) {
        StockInventoryInPlanLineExample example = new StockInventoryInPlanLineExample();
        StockInventoryInPlanLineExample.Criteria criteria = example.createCriteria();
        if(null != inventoryInPlanHeadId){
            criteria.andInventoryInPlanHeadIdEqualTo(inventoryInPlanHeadId);
        }
        return stockInventoryInPlanLineMapper.selectByExample(example);
    }
}
