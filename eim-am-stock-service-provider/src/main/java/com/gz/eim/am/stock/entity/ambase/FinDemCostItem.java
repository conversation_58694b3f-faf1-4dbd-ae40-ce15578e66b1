package com.gz.eim.am.stock.entity.ambase;

import java.util.Date;

public class FinDemCostItem {
    private Long costItemId;

    private String itemCode;

    private String itemName;

    private String itemFullCode;

    private String itemFullName;

    private String calculateDimension;

    private Boolean isClassificationItem;

    private String parentCode;

    private Date createTime;

    private Date updateTime;

    private Boolean delFlag;

    private String accountCode;

    private String accountName;

    private String managementAccountCode;

    private String managementAccountName;

    private String cashFlowCode;

    private String cashFlowName;

    private String payCashFlowCode;

    private String payCashFlowName;

    public Long getCostItemId() {
        return costItemId;
    }

    public void setCostItemId(Long costItemId) {
        this.costItemId = costItemId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode == null ? null : itemCode.trim();
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    public String getItemFullCode() {
        return itemFullCode;
    }

    public void setItemFullCode(String itemFullCode) {
        this.itemFullCode = itemFullCode == null ? null : itemFullCode.trim();
    }

    public String getItemFullName() {
        return itemFullName;
    }

    public void setItemFullName(String itemFullName) {
        this.itemFullName = itemFullName == null ? null : itemFullName.trim();
    }

    public String getCalculateDimension() {
        return calculateDimension;
    }

    public void setCalculateDimension(String calculateDimension) {
        this.calculateDimension = calculateDimension == null ? null : calculateDimension.trim();
    }

    public Boolean getIsClassificationItem() {
        return isClassificationItem;
    }

    public void setIsClassificationItem(Boolean isClassificationItem) {
        this.isClassificationItem = isClassificationItem;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode == null ? null : accountCode.trim();
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName == null ? null : accountName.trim();
    }

    public String getManagementAccountCode() {
        return managementAccountCode;
    }

    public void setManagementAccountCode(String managementAccountCode) {
        this.managementAccountCode = managementAccountCode == null ? null : managementAccountCode.trim();
    }

    public String getManagementAccountName() {
        return managementAccountName;
    }

    public void setManagementAccountName(String managementAccountName) {
        this.managementAccountName = managementAccountName == null ? null : managementAccountName.trim();
    }

    public String getCashFlowCode() {
        return cashFlowCode;
    }

    public void setCashFlowCode(String cashFlowCode) {
        this.cashFlowCode = cashFlowCode == null ? null : cashFlowCode.trim();
    }

    public String getCashFlowName() {
        return cashFlowName;
    }

    public void setCashFlowName(String cashFlowName) {
        this.cashFlowName = cashFlowName == null ? null : cashFlowName.trim();
    }

    public String getPayCashFlowCode() {
        return payCashFlowCode;
    }

    public void setPayCashFlowCode(String payCashFlowCode) {
        this.payCashFlowCode = payCashFlowCode == null ? null : payCashFlowCode.trim();
    }

    public String getPayCashFlowName() {
        return payCashFlowName;
    }

    public void setPayCashFlowName(String payCashFlowName) {
        this.payCashFlowName = payCashFlowName == null ? null : payCashFlowName.trim();
    }
}