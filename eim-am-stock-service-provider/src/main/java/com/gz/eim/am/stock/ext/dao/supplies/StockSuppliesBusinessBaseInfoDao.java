package com.gz.eim.am.stock.ext.dao.supplies;

import com.gz.eim.am.stock.entity.StockSuppliesBusinessBaseInfo;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/7/13
 * <p>
 *   物料业务属性基本信息dao
 * </p>
 */
public interface StockSuppliesBusinessBaseInfoDao {
    /**
     * 根据物料编码和仓库类别获取物料业务属性基本信息
     * @param suppliesCode
     * @param warehouseType
     * @return
     */
    StockSuppliesBusinessBaseInfo getBySuppliesCodeAndWarehouseType(String suppliesCode , Integer warehouseType);

    /**
     * 根据仓库类别获取物料编码业务信息
     * @param warehouseType
     * @return
     */
    List<StockSuppliesBusinessBaseInfo> listByWarehouseType(Integer warehouseType);
}
