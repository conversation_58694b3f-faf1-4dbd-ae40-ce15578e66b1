package com.gz.eim.am.stock.util.em;

/**
 * @author: wei<PERSON><PERSON>e
 * @date: 2021/2/23
 * @description
 */
public class StockCheckAdjustEnum {

    public enum CheckAdjustType{
        /**
         * 盘盈亏调整
         */
        MORE_OR_LESS(1, "盘盈亏调整"),
        /**
         * 差异数据调整
         */
        ADJUST_DIFF(2, "差异数据调整"),;


        CheckAdjustType(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    public enum CheckApproveStatus {
        /**
         * 待审批
         */
        WAIT_APPROVE(0, "待审批"),
        /**
         * 审批中
         */
        APPROVE_ING(1, "审批中"),
        /**
         * 审批通过
         */
        ALREADY_APPROVE(2, "审批通过"),
        ;


        CheckApproveStatus(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    public enum ApproveCheckFlag{
        /**
         * 校验未通过
         */
        NO(0, "否"),
        /**
         * 校验通过
         */
        YES(1, "是"),;


        ApproveCheckFlag(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }
    }

    /**
     * 文件绑定表业务主键后缀
     */
    public enum ApproveCheckType{
        /**
         * 盘盈亏数据附件
         */
        V1("-CHECK-V1", "盘盈亏数据附件"),
        /**
         * 差异调整数据附件
         */
        V2("-CHECK-V2", "差异调整数据附件"),;


        ApproveCheckType(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private String value;
        private String desc;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
