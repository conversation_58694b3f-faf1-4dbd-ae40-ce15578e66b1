package com.gz.eim.am.stock.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StockLicenseChangeItemsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public StockLicenseChangeItemsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andChangeNoIsNull() {
            addCriterion("change_no is null");
            return (Criteria) this;
        }

        public Criteria andChangeNoIsNotNull() {
            addCriterion("change_no is not null");
            return (Criteria) this;
        }

        public Criteria andChangeNoEqualTo(String value) {
            addCriterion("change_no =", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoNotEqualTo(String value) {
            addCriterion("change_no <>", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoGreaterThan(String value) {
            addCriterion("change_no >", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoGreaterThanOrEqualTo(String value) {
            addCriterion("change_no >=", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoLessThan(String value) {
            addCriterion("change_no <", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoLessThanOrEqualTo(String value) {
            addCriterion("change_no <=", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoLike(String value) {
            addCriterion("change_no like", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoNotLike(String value) {
            addCriterion("change_no not like", value, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoIn(List<String> values) {
            addCriterion("change_no in", values, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoNotIn(List<String> values) {
            addCriterion("change_no not in", values, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoBetween(String value1, String value2) {
            addCriterion("change_no between", value1, value2, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeNoNotBetween(String value1, String value2) {
            addCriterion("change_no not between", value1, value2, "changeNo");
            return (Criteria) this;
        }

        public Criteria andChangeTypeIsNull() {
            addCriterion("change_type is null");
            return (Criteria) this;
        }

        public Criteria andChangeTypeIsNotNull() {
            addCriterion("change_type is not null");
            return (Criteria) this;
        }

        public Criteria andChangeTypeEqualTo(Integer value) {
            addCriterion("change_type =", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeNotEqualTo(Integer value) {
            addCriterion("change_type <>", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeGreaterThan(Integer value) {
            addCriterion("change_type >", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("change_type >=", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeLessThan(Integer value) {
            addCriterion("change_type <", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("change_type <=", value, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeIn(List<Integer> values) {
            addCriterion("change_type in", values, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeNotIn(List<Integer> values) {
            addCriterion("change_type not in", values, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeBetween(Integer value1, Integer value2) {
            addCriterion("change_type between", value1, value2, "changeType");
            return (Criteria) this;
        }

        public Criteria andChangeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("change_type not between", value1, value2, "changeType");
            return (Criteria) this;
        }

        public Criteria andBeforeValueIsNull() {
            addCriterion("before_value is null");
            return (Criteria) this;
        }

        public Criteria andBeforeValueIsNotNull() {
            addCriterion("before_value is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeValueEqualTo(String value) {
            addCriterion("before_value =", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueNotEqualTo(String value) {
            addCriterion("before_value <>", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueGreaterThan(String value) {
            addCriterion("before_value >", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueGreaterThanOrEqualTo(String value) {
            addCriterion("before_value >=", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueLessThan(String value) {
            addCriterion("before_value <", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueLessThanOrEqualTo(String value) {
            addCriterion("before_value <=", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueLike(String value) {
            addCriterion("before_value like", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueNotLike(String value) {
            addCriterion("before_value not like", value, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueIn(List<String> values) {
            addCriterion("before_value in", values, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueNotIn(List<String> values) {
            addCriterion("before_value not in", values, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueBetween(String value1, String value2) {
            addCriterion("before_value between", value1, value2, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andBeforeValueNotBetween(String value1, String value2) {
            addCriterion("before_value not between", value1, value2, "beforeValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueIsNull() {
            addCriterion("after_value is null");
            return (Criteria) this;
        }

        public Criteria andAfterValueIsNotNull() {
            addCriterion("after_value is not null");
            return (Criteria) this;
        }

        public Criteria andAfterValueEqualTo(String value) {
            addCriterion("after_value =", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotEqualTo(String value) {
            addCriterion("after_value <>", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueGreaterThan(String value) {
            addCriterion("after_value >", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueGreaterThanOrEqualTo(String value) {
            addCriterion("after_value >=", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueLessThan(String value) {
            addCriterion("after_value <", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueLessThanOrEqualTo(String value) {
            addCriterion("after_value <=", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueLike(String value) {
            addCriterion("after_value like", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotLike(String value) {
            addCriterion("after_value not like", value, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueIn(List<String> values) {
            addCriterion("after_value in", values, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotIn(List<String> values) {
            addCriterion("after_value not in", values, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueBetween(String value1, String value2) {
            addCriterion("after_value between", value1, value2, "afterValue");
            return (Criteria) this;
        }

        public Criteria andAfterValueNotBetween(String value1, String value2) {
            addCriterion("after_value not between", value1, value2, "afterValue");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardIsNull() {
            addCriterion("before_id_card is null");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardIsNotNull() {
            addCriterion("before_id_card is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardEqualTo(String value) {
            addCriterion("before_id_card =", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardNotEqualTo(String value) {
            addCriterion("before_id_card <>", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardGreaterThan(String value) {
            addCriterion("before_id_card >", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardGreaterThanOrEqualTo(String value) {
            addCriterion("before_id_card >=", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardLessThan(String value) {
            addCriterion("before_id_card <", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardLessThanOrEqualTo(String value) {
            addCriterion("before_id_card <=", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardLike(String value) {
            addCriterion("before_id_card like", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardNotLike(String value) {
            addCriterion("before_id_card not like", value, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardIn(List<String> values) {
            addCriterion("before_id_card in", values, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardNotIn(List<String> values) {
            addCriterion("before_id_card not in", values, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardBetween(String value1, String value2) {
            addCriterion("before_id_card between", value1, value2, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andBeforeIdCardNotBetween(String value1, String value2) {
            addCriterion("before_id_card not between", value1, value2, "beforeIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardIsNull() {
            addCriterion("after_id_card is null");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardIsNotNull() {
            addCriterion("after_id_card is not null");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardEqualTo(String value) {
            addCriterion("after_id_card =", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardNotEqualTo(String value) {
            addCriterion("after_id_card <>", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardGreaterThan(String value) {
            addCriterion("after_id_card >", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardGreaterThanOrEqualTo(String value) {
            addCriterion("after_id_card >=", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardLessThan(String value) {
            addCriterion("after_id_card <", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardLessThanOrEqualTo(String value) {
            addCriterion("after_id_card <=", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardLike(String value) {
            addCriterion("after_id_card like", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardNotLike(String value) {
            addCriterion("after_id_card not like", value, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardIn(List<String> values) {
            addCriterion("after_id_card in", values, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardNotIn(List<String> values) {
            addCriterion("after_id_card not in", values, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardBetween(String value1, String value2) {
            addCriterion("after_id_card between", value1, value2, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andAfterIdCardNotBetween(String value1, String value2) {
            addCriterion("after_id_card not between", value1, value2, "afterIdCard");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}