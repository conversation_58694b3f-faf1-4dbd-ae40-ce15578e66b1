package com.gz.eim.am.stock.web.order;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.fuu.eim.tool.excel.ExcelUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.order.StockDeliveryApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutBatchReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO;
import com.gz.eim.am.stock.dto.request.order.InventoryOutSnSearchReqDTO;
import com.gz.eim.am.stock.entity.vo.download.ExportInventoryFlowEntity;
import com.gz.eim.am.stock.entity.vo.download.ExportInventoryOutFlowEntity;
import com.gz.eim.am.stock.service.order.StockDeliveryService;
import com.gz.eim.am.stock.util.em.DeliveryEnum;
import org.apache.commons.collections.list.AbstractLinkedList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019-09-24 上午 10:47
 */
@RestController
@RequestMapping("/api/am/stock")
public class StockDeliveryController implements StockDeliveryApi {

    private final Logger logger = LoggerFactory.getLogger(StockDeliveryController.class);

    @Autowired
    private StockDeliveryService service;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${namespace.name}")
    private String nameSpace;

    @Override
    public ResponseData batchInventoryOut(InventoryOutBatchReqDTO dto) {
        logger.info("/api/am/stock/delivery/batch {}", dto.toString());
        ResponseData res = null;
//        String lockKey = RedisKeyConstants.DELIVERY_OUT;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
//            if (redisUtil.setNx(nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                res = this.service.batchAddDelivery(dto, user);
//                redisUtil.expire(nameSpace, lockKey, 1, TimeUnit.SECONDS);
//            } else {
//                res = ResponseData.createFailResult("系统繁忙,请稍后尝试...");
//            }
        } catch (RuntimeException e){
            logger.info("批量新增出库单 errorMessage = {}",e.getMessage());
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e){
//            redisUtil.deleteByKey(nameSpace,lockKey);
            logger.error("批量新增出库单", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData stockQuantityMonthEnd(Integer limit) {
        logger.info("/api/am/stock/delivery/stockQuantityMonthEnd limit={}", limit);
        ResponseData res = null;
        String lockKey = "";
        try {
            lockKey = RedisKeyConstants.BATCH_ASSETS_TRANSFER_OUT_IMPORT_SAVE + DateUtils.dateFormat (new Date (), CommonConstant.DATE_FORMAT_YYYY_MM_DD_HH);
            logger.info("当前库存批量月结的锁：lockKey={}", lockKey);

           // lockKey = RedisKeyConstants.BATCH_ASSETS_TRANSFER_OUT_IMPORT_SAVE + 2;
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                JwtUser user = SecurityUtil.getJwtUser ();
                res = this.service.stockQuantityMonthlyEnd (limit, user);
                redisUtil.expire (nameSpace, lockKey, RedisKeyConstants.SETTLEMENT_DELIVERY_LOCK_SECOND, TimeUnit.SECONDS);;
            }else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (Exception e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            logger.error("库存批量月结异常", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData saveInventoryOut(InventoryOutReqDTO inventoryOutDTO) {
        logger.info("/api/am/stock/delivery {}", inventoryOutDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.saveInventoryOut (inventoryOutDTO, user);
        }catch (RuntimeException e){
            logger.info("出库单保存", e);
            res = ResponseData.createFailResult(e.getMessage());
        }catch (Exception e){
            logger.error("出库单保存", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    /**
     * @param: file,empId
     * @description: 手动执行GPS出库
     * @return: ResponseData
     * @author: <EMAIL>
     * @date: 2022/4/26
     */
    @Override
    public ResponseData manualGpsDeliveryByImportExcel(@RequestParam("file") MultipartFile file, @RequestParam("empId") String empId) {
        logger.info("/api/am/stock/delivery/manualGpsDeliveryByImportExcel：{}", empId);
        ResponseData res = null;
        try {
            res = this.service.manualGpsDeliveryByImportExcel (file, empId);
        }catch (Exception e){
            logger.error("手动执行GPS出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public  ResponseData selectInventoryOut(InventoryOutSearchReqDTO inventoryOutSearchReqDTO) {
        logger.info("/api/am/stock/delivery {}", inventoryOutSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            List<Integer> typeList = Stream.of(DeliveryEnum.OutType.CARD_USE.getCode(),DeliveryEnum.OutType.PRESENT_OUT.getCode()).collect(Collectors.toList());
            inventoryOutSearchReqDTO.setOutStockTypeList(typeList);
            res = this.service.selectInventoryOut (inventoryOutSearchReqDTO, user);
        } catch (Exception e){
            logger.error("出库单分页查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectInventoryOutByDeliveryId(Long deliveryId) {

        logger.info("/api/am/stock/delivery/{}", deliveryId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.selectInventoryOutByDeliveryId (deliveryId, user);
        } catch (Exception e){
            logger.error("出库单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectInventoryOutByDeliveryNo(String bizId) {
        logger.info("/api/am/stock/delivery/wfl/{}", bizId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.selectInventoryOutByDeliveryNo (bizId, user);
        } catch (Exception e){
            logger.error("出库单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    @Override
    public ResponseData inventoryOutInStorage(InventoryOutReqDTO inventoryOutDTO) {
        logger.info("/api/am/stock/delivery {}", inventoryOutDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.inventoryOutStorage (inventoryOutDTO, user);
        } catch (Exception e){
            logger.error("出库单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectAccessibleInventoryOut(InventoryOutSearchReqDTO inventoryOutSearchReqDTO) {
        logger.info("/api/am/stock/delivery {}", inventoryOutSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            res = this.service.selectAccessibleInventoryOut (inventoryOutSearchReqDTO, user);
        } catch (Exception e){
            logger.error("出库单出库", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectInventoryOutSn(InventoryOutSnSearchReqDTO inventoryOutSnSearchReqDTO) {
        logger.info("/api/am/stock/delivery/sn/search {}", inventoryOutSnSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.service.selectInventoryOutSn (inventoryOutSnSearchReqDTO, user);
        } catch (Exception e){
            logger.error("出库单序列号分页查询", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public void exportInventoryOut(InventoryFlowReqDTO inventoryFlowReqDTO, HttpServletRequest request, HttpServletResponse response) {
        logger.info("/api/am/stock/inventory/flow/export {}", inventoryFlowReqDTO.toString());
        try {

            JwtUser user = SecurityUtil.getJwtUser();
            logger.info("---------------------login-user--{}---------------------", user.getEmployeeCode());
            List<ExportInventoryOutFlowEntity> exportInventoryOutFlowEntityList = this.service.exportInventoryOutFlow(inventoryFlowReqDTO, user);
            if (CollectionUtils.isEmpty(exportInventoryOutFlowEntityList)) {
                return;
            }

            final String fileName = "出库单流水" + DateUtils.dateFormat(new Date(), DateUtils.HOUR_PATTERN) + ".xlsx";
            ExcelUtil.createExcelWithBuffer(exportInventoryOutFlowEntityList, fileName, request, response);

        } catch (Exception e) {
            logger.error("出库单流水导出异常" + e.getMessage());
            return;
        }
        return;
    }

}
