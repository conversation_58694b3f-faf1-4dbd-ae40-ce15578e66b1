package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;

/**
 * @author: weijunjie
 * @date: 2021/2/26
 * @description
 */
public interface StockCheckResultExecuteService {
    /**
     * 执行盘盈亏调整
     * @param checkDifferenceListHeadReqDTO
     * @return
     */
    ResponseData profitAndLossExecute(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO) throws Exception;

    /**
     * 盘点调整更新
     * @param takingPlanNo
     * @param lineId
     * @return
     */
    ResponseData checkDiffAdjustExecute(String takingPlanNo, Long lineId) throws Exception;
}
