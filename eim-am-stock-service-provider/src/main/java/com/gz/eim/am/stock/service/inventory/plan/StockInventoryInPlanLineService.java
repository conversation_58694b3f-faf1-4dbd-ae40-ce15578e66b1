package com.gz.eim.am.stock.service.inventory.plan;

import com.gz.eim.am.stock.dto.request.order.plan.PurchaseOrderCancelLineDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineRespDTO;
import com.gz.eim.am.stock.dto.response.inventory.plan.InventoryPlanAssetRespDTO;
import com.gz.eim.am.stock.entity.StockInventoryInPlanHead;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLine;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLineExample;
import com.gz.eim.am.stock.entity.vo.StockInventoryInPlanLineInfo;

import java.util.List;

/**
 * @author: lishuyang
 * @date: 2019/12/10
 * @description:
 */
public interface StockInventoryInPlanLineService {
    /**
     * 批量插入计划入库单行
     * @param stockInventoryInPlanLineList
     * @return
     */
    Integer batchInsertStockInventoryPlanLine(List<StockInventoryInPlanLine> stockInventoryInPlanLineList);

    /**
     * 根据参数获取入库单行
     * @param stockInventoryInPlanLine
     * @return
     */
    List<InventoryInPlanLineRespDTO> selectBySelective(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * 获取计划入库单行根据主键
     * @param inventoryInPlanLineId
     * @return
     */
    StockInventoryInPlanLine selectByPrimaryKey(Long inventoryInPlanLineId);

    /**
     * 根据主键更新行
     * @param stockInventoryInPlanLine
     * @return
     */
    boolean updatePrimaryKeySelective(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * 根据参数获取入库单行关联资产
     * @param stockInventoryInPlanLine
     * @return
     */
    List<InventoryPlanAssetRespDTO> selectInventoryPlanAssetRespDTOBySelective(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * 根据参数更新计划入库单行
     * @param stockInventoryInPlanLine
     * @param stockInventoryInPlanLineExample
     * @return
     */
    boolean updateByExampleSelective(StockInventoryInPlanLine stockInventoryInPlanLine, StockInventoryInPlanLineExample stockInventoryInPlanLineExample);

    /**
     * 查询计划入库单下还未已入库的行
     * @param inventoryInPlanHeadId
     * @return
     */
    List<StockInventoryInPlanLine> selectNotAlreadyIn(Long inventoryInPlanHeadId );

    /**
     * 批量插入计划入库单行Info
     * @param stockInventoryInPlanLineInfoList
     */
    Integer batchInsertStockInventoryPlanLineInfo(List<StockInventoryInPlanLineInfo> stockInventoryInPlanLineInfoList);

    /**
     * 分组行id，入库状态，count，查询到每个行已入库的数量
     */
    List<StockInventoryInPlanLine> selectLinesGroupByIdAndStatus(Long inventoryHeadId);

    /**
     * 根据id查询计划入库物料集合
     * @param lineIds
     * @return
     */
    List<StockInventoryInPlanLine> selectLinesByIds(List<Long> lineIds);

    /**
     * 根据headId查询计划入库物料集合
     * @param lineIds
     * @return
     */
    List<StockInventoryInPlanLine> selectLinesByHeadIds(List<Long> lineIds);

    /**
     * 根据条件查询不能被取消的申请单行数据
     * @param inventoryPurchaseOrderCancelLineDTOs
     * @param type 空值：获取行表状态为已入库或已取消的数据，非空：获取头表状态为已入库或已取消的数据
     * @return
     */
    List<PurchaseOrderCancelLineDTO> selectNoCancelLines(List<PurchaseOrderCancelLineDTO> inventoryPurchaseOrderCancelLineDTOs,Integer type);

    /**
     * 根据验收单行编号更新状态
     * @param receiveItemNos 验收单行编号集合
     * @param status 指定更新的状态
     * @return
     */
    Integer updateStatusByReceiveItemNos(List<String> receiveItemNos, Integer status);
    /**
     * 根据条件更新申请单行数据
     * @param inventoryPurchaseOrderCancelLineDTOs
     * @return
     */
    Integer updateLineStatusByPurchaseDto(List<PurchaseOrderCancelLineDTO> inventoryPurchaseOrderCancelLineDTOs);


    /**
     * 根据inventoryHeadId 查询所有计划入库物料集合
     * @param inventoryInPlanHeadId
     * @return
     */
    List<StockInventoryInPlanLine> selectLinesByHeadId(Long inventoryInPlanHeadId);

    /**
     * 插入计划入库单行
     * @param stockInventoryInPlanLine
     * @return
     */
    Integer insertStockInventoryPlanLine(StockInventoryInPlanLine stockInventoryInPlanLine);

    /**
     * @param: list
     * @description: 批量更新逻辑
     * @return: int
     * @author: <EMAIL>
     * @date: 2023/3/3
     */
    int batchUpdate(List<StockInventoryInPlanLine> list);
}
