package com.gz.eim.am.stock.service.check;

import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.check.AssetQueryScopeReqDTO;
import com.gz.eim.am.stock.dto.request.check.CheckDifferenceListHeadReqDTO;
import com.gz.eim.am.stock.dto.response.check.CheckDifferenceListLineRespDTO;
import com.gz.eim.am.stock.entity.CheckDifferenceListLine;
import com.gz.eim.am.stock.entity.vo.CheckDifferenceLineInfo;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/11/22
 * @description
 */
public interface CheckDifferenceListLineService {

    /**
     * 批量插入异常差异清单
     *
     * @param checkDifferenceListLines
     * @return
     */
    Integer batchInsertCheckDifference(List<CheckDifferenceListLine> checkDifferenceListLines);

    /**
     * 根据头id获取所有的异常差异明细
     *
     * @param assetQueryScopeReqDTO
     * @return
     */
    List<CheckDifferenceListLineRespDTO> getLineRespDTOs(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 批量更新异常差异清单备注
     *
     * @param checkDifferenceListLines
     * @return
     */
    Integer batchUpdateSelective(List<CheckDifferenceListLine> checkDifferenceListLines);

    /**
     * 查询差异清单行总数
     *
     * @param assetQueryScopeReqDTO
     * @return
     */
    long getLineCount(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 根据条件批量生成差异清单行数据
     *
     * @param assetQueryScopeReqDTO
     * @return
     */
    Integer batchInsertByConditions(AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 异步生成盘点差异清单数据
     *
     * @param checkDifferenceListHeadReqDTO
     * @param user
     */
    void createCheckDifferenceList(CheckDifferenceListHeadReqDTO checkDifferenceListHeadReqDTO, JwtUser user) throws Exception;

    /**
     * 根据头id获取差异类型分组后的数据
     *
     * @param headId
     * @return
     */
    List<CheckDifferenceLineInfo> getLineInfoGroupByHandlerMethod(long headId);

    /**
     * 查询差异清单行总数
     *
     * @param checkDifferenceListLine
     * @param assetQueryScopeReqDTO
     * @return
     */
    long batchUpdateByConditions(CheckDifferenceListLine checkDifferenceListLine, AssetQueryScopeReqDTO assetQueryScopeReqDTO);

    /**
     * 查询不同状态下调整数据的主键id集合
     *
     * @param headId
     * @param lineIds
     * @param adjustFlag
     * @param adjustType
     * @return
     */
    List<CheckDifferenceListLine> getLineIdsByAdjustFlag(Long headId, List<Long> lineIds, Integer adjustFlag,Integer adjustType);

    /**
     * 查询盘盈亏数据的行id集合
     * @param headId
     * @param adjustFlg
     * @return
     */
    List<Long> getLineIdsByHeadId(Long headId,Integer adjustFlg);

}
