package com.gz.eim.am.stock.util.common;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import okhttp3.FormBody.Builder;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName: 对外提供http get post请求接口
 * @Description:
 * <AUTHOR>
 * @date 2020年9月14日
 */
public class OkHttpClientUtils {

    private static final OkHttpClient httpClient = new OkHttpClient();
    /**
     * 默认连接超时时常
     */
    private static int DEFAULT_CONNECT_TIMEOUT = 30000;
    /**
     * 连接之后默认每次read的时常
     */
    private static int DEFAULT_READ_TIMEOUT = 30000;

    /**
     * EBS每次read的时常
     */
    private static int EBS_READ_TIMEOUT = 30000;

    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public static final MediaType FORM = MediaType.parse("application/x-www-form-urlencoded; charset=utf-8");

    private final static Logger logger = LoggerFactory.getLogger(OkHttpClientUtils.class);

    /**
     * get请求，使用默认超时时间，使用时尽量根据不同的请求自定义超时时间
     *
     * @param url
     * @param map
     * @return
     */
    public static String get(String url, Map<String, Object> map) {
        return get(url, map, DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    /**
     * get请求
     *
     * @param url
     * @param map
     * @param connectTimeout
     * @param readTimeout
     * @return
     */
    public static String get(String url, Map<String, Object> map, int connectTimeout, int readTimeout) {
        long start = System.currentTimeMillis();
        //参数信息
        String params = map2GetString(map);
        //url为空
        if (!StringUtils.isNotBlank(url)) {
            return null;
        }
        //拼接的参数params参数不为空，
        if(StringUtils.isNotEmpty(params)){
            //string.contains("star")方法是判断字符串中是否有star字符，有就执行else语句， 没有就执if中的语句
            if (!url.contains("?")) {
                url += "?" + params;
            } else {
                url += "&" + params;
            }
        }

        Request request = new Request.Builder().url(url).build();
        String result = null;
        try {
            OkHttpClient th = httpClient.newBuilder().connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(readTimeout, TimeUnit.MILLISECONDS).build();
            //对请求给出回应
            Response response = th.newCall(request).execute();
            //响应成功，将响应体返回
            if (response.isSuccessful()) {
                result = response.body().string();
            }
        } catch (Exception e) {
            logger.error("okhttp error",e);
        }
        logger.info(url+"####request:"+result+",####response:"+result+",####time:"+(System.currentTimeMillis() - start));
        return result;
    }

    /**
     * 参数map转化成get接口参数格式（map中存储的是要连接的url请求后边的参数，将map中的内容转化成请求过程中url后边拼接参数的string类型）
     * @param map
     * @return
     */
    public static String map2GetString(Map<String, Object> map) {
        if (map == null || map.size() < 1) {
            return "";
        }
        StringBuilder params = new StringBuilder();
        //get请求url后参数的拼接
        for (Entry<String, Object> entry : map.entrySet()) {
            params.append(entry.getKey()).append("=");
            if (entry.getValue() != null) {
                params.append(entry.getValue());
            }
            params.append("&");
        }
        //最后一个&下标
        params.deleteCharAt(params.lastIndexOf("&"));
        return params.toString();
    }

    /**
     * post请求，使用默认超时时间，使用时尽量根据不同的请求自定义超时时间
     *
     * @param url
     * @param map
     * @return
     */
    public static String post(String url, Map<String, Object> map) {
        return post(url, map, DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    /**
     * post请求，传输格式用application/json,使用默认超时时间，使用时尽量根据不同的请求自定义超时时间
     *
     * @param url
     * @param jsonData
     * @return
     */
    public static String postJson(String url, String jsonData) {
        return postJson(url, jsonData, DEFAULT_CONNECT_TIMEOUT, EBS_READ_TIMEOUT);
    }
    /**
     * 混合穿参数，一部分在json，一部分在参数里面
     * @param url
     * @param map
     * @param jsonData
     * @return
     */
    public static String postMulti(String url, Map<String, Object> map, String jsonData) {
        return postMulti(url, map, jsonData, DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    /**
     * post请求
     *
     * @param url
     * @param map
     * @param connectTimeout
     * @param readTimeout
     * @return
     */
    public static String post(String url, Map<String, Object> map, int connectTimeout, int readTimeout) {
        long start = System.currentTimeMillis();
        String result = null;
        RequestBody formBody = null;
        Builder builder = new FormBody.Builder();
        if (map != null && map.size() > 0) {
            for (Entry<String, Object> entry : map.entrySet()) {
                builder.add(entry.getKey(), entry.getValue() == null ? "" : entry.getValue().toString());
            }
        }
        formBody = builder.build();
        try {
            OkHttpClient th = httpClient.newBuilder().connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(readTimeout, TimeUnit.MILLISECONDS).build();
            Request request = new Request.Builder().url(url).post(formBody).build();
            Response response = th.newCall(request).execute();

            if (response.isSuccessful()) {
                result = response.body().string();
            }
        } catch (Exception e) {
            logger.error("post error", e);
        }
        String logUrl = url + "?" + map2GetString(map);
        logger.info("url:"+logUrl+"####request:"+result+",####response:"+result+",####time:"+(System.currentTimeMillis() - start));
        return result;
    }

    /**
     * @Description: 传输application/json格式
     * @param url
     *            url地址
     * @param jsonData
     *            json字符串
     * @param connectTimeout
     * @param readTimeout
     * @return String
     */
    public static String postJson(String url, String jsonData, int connectTimeout, int readTimeout) {
        long start = System.currentTimeMillis();
        String result = null;
        RequestBody requestBody = RequestBody.create(JSON, jsonData);
        try {
            OkHttpClient th = httpClient.newBuilder().connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(readTimeout, TimeUnit.MILLISECONDS).build();
            Request request = new Request.Builder().url(url).post(requestBody).build();
            Response response = th.newCall(request).execute();

            if (response.isSuccessful()) {
                result = response.body().string();
            }
        } catch (Exception e) {
            logger.error("postJson error", e);
        }
        String logUrl = url + "?" + jsonData;
        logger.info("url:"+logUrl+"####request:"+result+",####response:"+result+",####time:"+(System.currentTimeMillis() - start));
        return result;
    }

    /**
     * @Description: 传输application/json和requestParam格式
     * @param url
     *            url地址
     * @param jsonData
     *            json字符串
     * @param connectTimeout
     * @param readTimeout
     * @return String
     */
    public static String postMulti(String url, Map<String, Object> map, String jsonData, int connectTimeout, int readTimeout) {
        long start = System.currentTimeMillis();
        String result = null;

        //参数信息
        String params = map2GetString(map);
        //url为空
        if (!StringUtils.isNotBlank(url)) {
            return null;
        }
        //拼接的参数params参数不为空，
        if(StringUtils.isNotEmpty(params)){
            //string.contains("star")方法是判断字符串中是否有star字符，有就执行else语句， 没有就执if中的语句
            if (!url.contains("?")) {
                url += "?" + params;
            } else {
                url += "&" + params;
            }
        }

        RequestBody requestBody = RequestBody.create(JSON, jsonData);
        try {
            OkHttpClient th = httpClient.newBuilder().connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(readTimeout, TimeUnit.MILLISECONDS).build();
            Request request = new Request.Builder().url(url).post(requestBody).build();
            Response response = th.newCall(request).execute();

            if (response.isSuccessful()) {
                result = response.body().string();
            }
        } catch (Exception e) {
            logger.error("postJson error", e);
        }
        logger.info("url:"+url+"####requestParam:"+map+",####requestParam:"+jsonData+",####response:"+result+",####time:"+(System.currentTimeMillis() - start));
        return result;
    }

    /**
     * get请求，使用默认超时时间，使用时尽量根据不同的请求自定义超时时间
     *
     * @param url
     * @param map
     * @return
     */
    public static String getR(String url, LinkedHashMap<String, Object> map ,Map<String, Object> paraMap) {
        return getR(url, map, paraMap,DEFAULT_CONNECT_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    /**
     * get请求
     *
     * @param url
     * @param map
     * @param connectTimeout
     * @param readTimeout
     * @return
     */
    public static String getR(String url, LinkedHashMap<String, Object> map,Map<String, Object> paraMap, int connectTimeout, int readTimeout) {
        long start = System.currentTimeMillis();
        //将map中的内容转换成字符串参数拼接
        String paramRs = map2GetStringR(map);
        if (!StringUtils.isNotBlank(url)) {
            return null;
        }
        if(StringUtils.isNotEmpty(paramRs)){
            if (url.substring(url.length()-1, url.length()).equals("/")) {
                url=(url.substring(0, url.length()-1)) + paramRs;
            }else {
                url += paramRs;
            }
        }
        String params = map2GetString(paraMap);
        if(StringUtils.isNotEmpty(params)){
            if (!url.contains("?")) {
                url += "?" + params;
            } else {
                url += "&" + params;
            }
        }

        Request request = new Request.Builder().url(url).build();
        String result = null;
        try {
            OkHttpClient th = httpClient.newBuilder().connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(readTimeout, TimeUnit.MILLISECONDS).build();
            Response response = th.newCall(request).execute();
            if (response.isSuccessful()) {
                result = response.body().string();
            }
        } catch (Exception e) {
            logger.error("okhttp error", e);
        }
        logger.info("url:"+url+"####request:"+result+",####response:"+result+",####time:"+(System.currentTimeMillis() - start));
        return result;
    }

    /**
     * 参数map转化成get接口参数格式
     * @param map
     * @return
     */
    public static String map2GetStringR(LinkedHashMap<String, Object> map) {
        if (map == null || map.size() < 1) {
            return "";
        }
        StringBuilder params = new StringBuilder();
        for (Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                params.append("/").append(entry.getValue());
            }
        }
        return params.toString();
    }

}

