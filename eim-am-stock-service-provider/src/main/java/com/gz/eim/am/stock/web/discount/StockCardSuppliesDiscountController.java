package com.gz.eim.am.stock.web.discount;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.gz.eim.am.stock.api.discount.StockCardSuppliesDiscountApi;
import com.gz.eim.am.stock.dto.request.discount.StockCardSuppliesDiscountQueryDTO;
import com.gz.eim.am.stock.dto.request.discount.StockCardSuppliesDiscountReqDTO;
import com.gz.eim.am.stock.service.discount.StockCardSuppliesDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wangjing67
 * @Date: 1/27/21 6:09 下午
 * @description 折现相关controller
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/cardSuppliesDiscount")
public class StockCardSuppliesDiscountController implements StockCardSuppliesDiscountApi {

    @Autowired
    private StockCardSuppliesDiscountService stockCardSuppliesDiscountService;

    @Override
    public ResponseData save(StockCardSuppliesDiscountReqDTO stockCardSuppliesDiscountReqDTO) throws Exception {
        log.info("/api/am/stock/cardSuppliesDiscount/save {}" + JSON.toJSONString(stockCardSuppliesDiscountReqDTO));
        ResponseData res = null;
        try {
            res = this.stockCardSuppliesDiscountService.save(stockCardSuppliesDiscountReqDTO);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (RuntimeException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("新建折现申请单报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryByPage(StockCardSuppliesDiscountQueryDTO stockCardSuppliesDiscountQueryDTO) throws Exception {
        log.info("/api/am/stock/cardSuppliesDiscount/list {}" + JSON.toJSONString(stockCardSuppliesDiscountQueryDTO));
        ResponseData res = null;
        try {
            res = this.stockCardSuppliesDiscountService.queryByPage(stockCardSuppliesDiscountQueryDTO);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("查询折现申请单报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData queryDetail(String bizId) throws Exception {
        log.info("/api/am/stock/cardSuppliesDiscount/detail {}" + bizId);
        ResponseData res = null;
        try {
            res = this.stockCardSuppliesDiscountService.queryDetail(bizId);
        } catch (ServiceUncheckedException e) {
            res = ResponseData.createFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("查询折现申请单详情报错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

}
