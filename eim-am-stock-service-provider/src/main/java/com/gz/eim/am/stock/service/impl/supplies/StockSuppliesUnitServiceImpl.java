package com.gz.eim.am.stock.service.impl.supplies;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.base.StockSuppliesUnitMapper;
import com.gz.eim.am.stock.dto.request.supplies.SuppliesUnitReqDTO;
import com.gz.eim.am.stock.entity.StockSuppliesUnit;
import com.gz.eim.am.stock.entity.StockSuppliesUnitExample;
import com.gz.eim.am.stock.service.supplies.StockSuppliesUnitService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2019-12-11 AM 10:44
 */
@Service
public class StockSuppliesUnitServiceImpl implements StockSuppliesUnitService {

    @Autowired
    private StockSuppliesUnitMapper mapper;

    @Override
    public ResponseData selectSuppliesUnit(final SuppliesUnitReqDTO unitReqDTO) {
        StockSuppliesUnitExample example = new StockSuppliesUnitExample();
        StockSuppliesUnitExample.Criteria criteria = example.createCriteria();
        criteria.andDelFlagLessThanOrEqualTo(CommonConstant.NUMBER_ZERO);
        List<StockSuppliesUnit> unitList = mapper.selectByExample(example);
        ResponseData resp = ResponseData.createSuccessResult(unitList);
        return resp;
    }

    @Override
    public List<StockSuppliesUnit> selectSuppliesUnit(final List<Long> ids, final List<String> codes) {
        StockSuppliesUnitExample example = new StockSuppliesUnitExample();
        StockSuppliesUnitExample.Criteria criteria = example.createCriteria();
        if (ids != null && ids.size() > 0) {
            criteria.andIdIn(ids);
            return this.mapper.selectByExample(example);
        } else if (codes != null && codes.size() > 0) {
            criteria.andUnitCodeIn(codes);
            return this.mapper.selectByExample(example);
        }
        return null;
    }

    @Override
    public List<StockSuppliesUnit> selectSuppliesUnitList(final SuppliesUnitReqDTO unitReqDTO) {
        return null;
    }

    @Override
    public StockSuppliesUnit selectSuppliesUnitByCode(final String code) {
        final StockSuppliesUnitExample example = new StockSuppliesUnitExample();
        example.createCriteria().andUnitCodeEqualTo(code);
        final List<StockSuppliesUnit> unitList = this.mapper.selectByExample(example);
        if (unitList != null && unitList.size() > 0) {
            return unitList.get(0);
        }
        return null;
    }

    @Override
    public StockSuppliesUnit selectSuppliesUnitById(final Long id) {
        final StockSuppliesUnitExample example = new StockSuppliesUnitExample();
        example.createCriteria().andIdEqualTo(id);
        final List<StockSuppliesUnit> unitList = this.mapper.selectByExample(example);
        if (unitList != null && unitList.size() > 0) {
            return unitList.get(0);
        }
        return null;
    }
}
