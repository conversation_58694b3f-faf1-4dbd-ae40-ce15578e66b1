package com.gz.eim.am.stock.handler;

import com.gz.eim.am.stock.entity.StockAssetsRepairLine;
import com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend;
import com.gz.eim.am.stock.util.em.StockAssetsRepairHeadEnum;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public abstract class AbstractStockAssetsRepairAssetsDetailExportExcelHandler {

    /**
     * @param:
     * @description: 获取维修类型
     * @return: StockAssetsRepairHeadEnum.RepairType
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    public abstract StockAssetsRepairHeadEnum.RepairType getRepairType();

    /**
     * @param: fileName,stockAssetsRepairLineList
     * @param: stockAssetsRepairLineExtendList,request
     * @param: response,statusEnum
     * @description: 导出资产明细excel
     * @return:
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    public void exportAssetsDetailExcel(String fileName, List<StockAssetsRepairLine> stockAssetsRepairLineList,
                                 Map<String, StockAssetsRepairLineExtend> stockAssetsRepairLineExtendMap, HttpServletRequest request,
                                 HttpServletResponse response, StockAssetsRepairHeadEnum.Status statusEnum){
        // 导出验收状态的excel
        if(statusEnum == StockAssetsRepairHeadEnum.Status.IN_CHECK || statusEnum == StockAssetsRepairHeadEnum.Status.CHECK_COMPLETE){
            exportCheckAssetsExcel(fileName, stockAssetsRepairLineList, stockAssetsRepairLineExtendMap, request, response);
        // 导出验收前的excel
        }else {
            exportAssetsDetailExcel(fileName, stockAssetsRepairLineList, stockAssetsRepairLineExtendMap, request, response);
        }
    }

    /**
     * @param: fileName,stockAssetsRepairLineList
     * @param: stockAssetsRepairLineExtendList,request
     * @param: response
     * @description: 导出验收前资产明细excel
     * @return:
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    public abstract void exportAssetsDetailExcel(String fileName, List<StockAssetsRepairLine> stockAssetsRepairLineList,
                                        Map<String, StockAssetsRepairLineExtend> stockAssetsRepairLineExtendMap, HttpServletRequest request,
                                        HttpServletResponse response);


    /**
     * @param: fileName,stockAssetsRepairLineList
     * @param: stockAssetsRepairLineExtendList,request
     * @param: response
     * @description: 导出验收后资产明细excel
     * @return:
     * @author: <EMAIL>
     * @date: 2022/12/20
     */
    public abstract void exportCheckAssetsExcel(String fileName, List<StockAssetsRepairLine> stockAssetsRepairLineList,
                                        Map<String, StockAssetsRepairLineExtend> stockAssetsRepairLineExtendMap, HttpServletRequest request,
                                        HttpServletResponse response);
}
