package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockRoleWarehouse;
import com.gz.eim.am.stock.entity.StockRoleWarehouseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockRoleWarehouseMapper {
    long countByExample(StockRoleWarehouseExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StockRoleWarehouse record);

    int insertSelective(StockRoleWarehouse record);

    List<StockRoleWarehouse> selectByExample(StockRoleWarehouseExample example);

    StockRoleWarehouse selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StockRoleWarehouse record, @Param("example") StockRoleWarehouseExample example);

    int updateByExample(@Param("record") StockRoleWarehouse record, @Param("example") StockRoleWarehouseExample example);

    int updateByPrimaryKeySelective(StockRoleWarehouse record);

    int updateByPrimaryKey(StockRoleWarehouse record);
}