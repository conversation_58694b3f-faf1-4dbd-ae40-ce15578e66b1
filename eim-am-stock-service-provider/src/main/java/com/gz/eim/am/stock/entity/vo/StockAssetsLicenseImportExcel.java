package com.gz.eim.am.stock.entity.vo;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * @Author: wangjing67
 * @Date: 3/30/21 4:55 下午
 * @description
 */
@Slf4j
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class StockAssetsLicenseImportExcel  implements ExportModel {

    @ExportField(name = "执照编码")
    private String assetsCode;

    @ExportField(name = "执照名称")
    private String assetsName;

    @ExportField(name = "执照法人")
    private String licenseLegalPerson;

    @ExportField(name = "法人身份证")
    private String corporateIdNumber;

    @ExportField(name = "执照注册地址")
    private String registeredAddress;

    @ExportField(name = "执照类型")
    private String licenseType;

    @ExportField(name = "注册资本")
    private String registeredCapital;

    @ExportField(name = "执照经营范围")
    private String businessScope;

    @Override
    public String getSheetName() {
        return null;
    }

    @Override
    public LinkedHashMap<String, String> getExtAttr() {
        return null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        StockAssetsLicenseImportExcel that = (StockAssetsLicenseImportExcel) o;
        return Objects.equals(assetsCode, that.assetsCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(assetsCode);
    }

    public String getAssetsCode() {
        return assetsCode;
    }

    public void setAssetsCode(String assetsCode) {
        this.assetsCode = assetsCode;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public String getLicenseLegalPerson() {
        return licenseLegalPerson;
    }

    public void setLicenseLegalPerson(String licenseLegalPerson) {
        this.licenseLegalPerson = licenseLegalPerson;
    }

    public String getCorporateIdNumber() {
        return corporateIdNumber;
    }

    public void setCorporateIdNumber(String corporateIdNumber) {
        this.corporateIdNumber = corporateIdNumber;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public static Logger getLog() {
        return log;
    }
}
