package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/2/6
 * @description 导出出入库单流水
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportInventoryOutFlowEntity implements ExportModel {
    /**
     * 物料编码
     */
    @ExportField(name = "物料编码")
    private String suppliesCode;
    /**
     * 物料描述
     */
    @ExportField(name = "物料名称")
    private String suppliesRemark;
    /**
     * 仓库编码
     */
    @ExportField(name = "仓库编码")
    private String warehouseCode;
    /**
     * 仓库名称
     */
    @ExportField(name = "仓库名称")
    private String warehouseName;
    /**
     * 结算中心名称
     */
    @ExportField(name = "所属门店")
    private String deptName;
    /**
     * 物料数量
     */
    @ExportField(name = "数量")
    private String number;

    /**
     * 业务类型
     */
    @ExportField(name = "业务类型")
    private String bizTypeName;
    /**
     * 物料单位
     */
    @ExportField(name = "单位")
    private String unit;
    /**
     * 录入人名称
     */
    @ExportField(name = "录入人")
    private String createUserName;
    /**
     * 责任人名称
     */
    @ExportField(name = "责任人")
    private String dutyUserName;

    @ExportField(name = "批次号")
    private String batchNo;
    @ExportField(name = "序列号")
    private String snNo;
    /**
     * 备注
     */
    @ExportField(name = "备注")
    private String remark;
    @ExportField(name = "出库时间")
    private String flowTime;

    @Override
    public String getSheetName() {
        return "流水";
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesRemark() {
        return suppliesRemark;
    }

    public void setSuppliesRemark(String suppliesRemark) {
        this.suppliesRemark = suppliesRemark;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getBizTypeName() {
        return bizTypeName;
    }

    public void setBizTypeName(String bizTypeName) {
        this.bizTypeName = bizTypeName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getDutyUserName() {
        return dutyUserName;
    }

    public void setDutyUserName(String dutyUserName) {
        this.dutyUserName = dutyUserName;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFlowTime() {
        return flowTime;
    }

    public void setFlowTime(String flowTime) {
        this.flowTime = flowTime;
    }

    @Override
    public String toString() {
        return "ExportInventoryFlowEntity{" +
                "suppliesCode='" + suppliesCode + '\'' +
                ", suppliesRemark='" + suppliesRemark + '\'' +
                ", warehouseCode='" + warehouseCode + '\'' +
                ", warehouseName='" + warehouseName + '\'' +
                ", deptName='" + deptName + '\'' +
                ", number='" + number + '\'' +
                ", bizTypeName='" + bizTypeName + '\'' +
                ", unit='" + unit + '\'' +
                ", createUserName='" + createUserName + '\'' +
                ", dutyUserName='" + dutyUserName + '\'' +
                ", batchNo='" + batchNo + '\'' +
                ", snNo='" + snNo + '\'' +
                ", remark='" + remark + '\'' +
                ", flowTime='" + flowTime + '\'' +
                '}';
    }
}