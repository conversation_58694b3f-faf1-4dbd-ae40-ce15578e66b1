package com.gz.eim.am.stock.service.inventory.plan;

import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;

/**
 * @Author: wangjing67
 * @Date: 3/25/21 2:28 下午
 * @description
 */
public interface StockPlanLicenseInService {

    /**
     * 新增执照入库
     *
     * @param inventoryInPlanHeadReqDTO
     * @param jwtUser
     * @return
     */
    ResponseData save(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO, JwtUser jwtUser);

    /**
     * 执照入库查询列表
     *
     * @param inventoryInPlanSearchReqDTO
     * @param jwtUser
     * @return
     */
    ResponseData selectByQuery(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO, JwtUser jwtUser);


    /**
     * 执照入库详情查询
     *
     * @param inventoryInPlanHeadId
     * @param jwtUser
     * @return
     */
    ResponseData queryDetail(Long inventoryInPlanHeadId, JwtUser jwtUser);


    /**
     * 根据行信息查询行信息下的资产信息
     *
     * @param inventoryInPlanLineReqDTO
     * @param jwtUser
     * @return
     */
    ResponseData selectLicenseLineAssetByLineId(InventoryInPlanLineReqDTO inventoryInPlanLineReqDTO, JwtUser jwtUser);
}
