package com.gz.eim.am.stock.dao.ambase;

import com.gz.eim.am.stock.entity.ambase.PsBusJobdataInactiveTbl;
import com.gz.eim.am.stock.entity.ambase.PsBusJobdataInactiveTblExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PsBusJobdataInactiveTblMapper {
    long countByExample(PsBusJobdataInactiveTblExample example);

    int deleteByPrimaryKey(String emplid);

    int insert(PsBusJobdataInactiveTbl record);

    int insertSelective(PsBusJobdataInactiveTbl record);

    List<PsBusJobdataInactiveTbl> selectByExample(PsBusJobdataInactiveTblExample example);

    PsBusJobdataInactiveTbl selectByPrimaryKey(String emplid);

    int updateByExampleSelective(@Param("record") PsBusJobdataInactiveTbl record, @Param("example") PsBusJobdataInactiveTblExample example);

    int updateByExample(@Param("record") PsBusJobdataInactiveTbl record, @Param("example") PsBusJobdataInactiveTblExample example);

    int updateByPrimaryKeySelective(PsBusJobdataInactiveTbl record);

    int updateByPrimaryKey(PsBusJobdataInactiveTbl record);
}