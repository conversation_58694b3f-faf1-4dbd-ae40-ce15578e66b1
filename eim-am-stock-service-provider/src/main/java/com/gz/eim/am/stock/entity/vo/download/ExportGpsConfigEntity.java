package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;

/**
 * @author: weijunjie
 * @date: 2021/1/20
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class ExportGpsConfigEntity implements ExportModel  {
    @ExportField(name = "序列号")
    private String snNo;
    @ExportField(name = "物料编码")
    private String suppliesCode;
    @ExportField(name = "物料名称")
    private String suppliesRemark;
    @ExportField(name = "仓库编码")
    private String warehouseCode;
    @ExportField(name = "仓库名称")
    private String warehouseName;
    @ExportField(name = "所属门店")
    private String costCenterName;
    @ExportField(name = "单位")
    private String unit;
    @ExportField(name = "库存状态")
    private String configStatusName;
    @ExportField(name = "安装状态")
    private String installStatusName;

    @Override
    public String getSheetName() {
        return "GPS明细信息";
    }


    public String getSnNo() {
        return snNo;
    }

    public void setSnNo(String snNo) {
        this.snNo = snNo;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public String getSuppliesRemark() {
        return suppliesRemark;
    }

    public void setSuppliesRemark(String suppliesRemark) {
        this.suppliesRemark = suppliesRemark;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getConfigStatusName() {
        return configStatusName;
    }

    public void setConfigStatusName(String configStatusName) {
        this.configStatusName = configStatusName;
    }

    public String getInstallStatusName() {
        return installStatusName;
    }

    public void setInstallStatusName(String installStatusName) {
        this.installStatusName = installStatusName;
    }
}
