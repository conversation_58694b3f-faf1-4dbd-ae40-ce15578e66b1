package com.gz.eim.am.stock.util.common;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.util.file.NewFileUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: l<PERSON>uyang
 * @date: 2020/3/18
 * @description
 */
@Slf4j
@Data
@Component
public class FileUtil {

    public static String  DATA_PARAM_DOWNLOAD_URL = "downloadUrl";

    @Value("${project.file.systemCode}")
    private String systemCode;

    @Value("${project.file.privateKey}")
    private String privateKey;

    @Value("${project.file.filePath}")
    private String folderPath;

    @Value("${project.file.logoPath}")
    private String logoPath;

    @Autowired
    private NewFileUtil newFileUtil;

    /**
     * 上传文件
     * @param multipartFile
     * @return
     */
    public Map<String, String> uploadFile(MultipartFile multipartFile) {
        if(null == multipartFile){
            return null;
        }
        Map<String, String> fileParam= new HashMap<>(CommonConstant.DEFAULT_MAP_SIZE);
        fileParam.put("systemCode", systemCode);
        fileParam.put("privateKey", privateKey);
        fileParam.put("filePath", folderPath);

        ResponseData<Map<String, String>> responseData = newFileUtil.upload(multipartFile, fileParam);
        if(null == responseData || !responseData.getCode().equals(ResponseCode.SUCCESS_CODE)){
            log.error("上传文件失败, filePath:{},param:{}, result:{}", multipartFile.getName(), fileParam.toString(), null == responseData?null:responseData.toString());
            return null;
        }
        log.info("FileUtil.uploadFile上传文件成功，响应值为:{}", JSON.toJSONString(responseData));
        return responseData.getData();
    }
}
