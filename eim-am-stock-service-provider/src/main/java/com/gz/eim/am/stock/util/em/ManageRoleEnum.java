package com.gz.eim.am.stock.util.em;

import com.gz.eim.am.stock.util.common.CodeEnumUtil;

/**
 * <AUTHOR>
 * @date 2019-09-25 下午 9:36
 */
public class ManageRoleEnum {

    /**
     * 权限类型
     */
    public enum Type implements ICodeEnum{
        /**
         * 所用的仓库
         */
        ALL(0, "ALL"),
        /**
         * 部门等级下的仓库
         */
        LEVEL(1, "LEVEL"),
        /**
         * 本部门下的仓库
         */
        SINGLE(2,"SINGLE"),
        /**
         * 仓管员
         */
        MANAGER(3, "MANAGER")
        ;
        Type(Integer code, String value){
            this.code = code;
            this.value = value;
        }
        private Integer code;
        private String value;


        @Override
        public Integer getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        public static Type fromCode(Integer code) {
            return CodeEnumUtil.fromCode(Type.class, code);
        }
    }

    /**
     * 角色状态
     */
    public enum Status{
        /**
         * 禁用
         */
        FORBID(0),
        /**
         * 正常
         */
        NORMAL(1)
        ;
        Status(Integer status){
            this.status = status;
        }
        private Integer status;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }

    /**
     * 管理员状态
     */
    public enum KeeperStatus{
        /**
         * 禁用
         */
        FORBID(0),
        /**
         * 正常
         */
        NORMAL(1)
        ;
        KeeperStatus(Integer status){
            this.status = status;
        }
        private Integer status;

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
