package com.gz.eim.am.stock.dao.ambase;

import com.gz.eim.am.stock.entity.ambase.SysDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/9/3
 * @description
 */
public interface SysDictMapper {

    /**
     * 从字典表查询数据
     * @param code
     * @param values
     * @return
     */
    List<SysDict> selectDictListByValue(@Param("code") String code, @Param("values") List<String> values);
}
