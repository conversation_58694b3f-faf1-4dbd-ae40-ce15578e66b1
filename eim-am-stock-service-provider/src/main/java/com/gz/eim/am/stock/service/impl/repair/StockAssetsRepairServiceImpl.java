package com.gz.eim.am.stock.service.impl.repair;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.jwt.JwtUser;
import com.github.pagehelper.PageInfo;
import com.gz.eim.am.base.api.file.FileServiceApi;
import com.gz.eim.am.pdm.api.external.SupplierDataApi;
import com.gz.eim.am.pdm.dto.external.supplier.SupplierDataSearchReqDTO;
import com.gz.eim.am.pdm.dto.response.supplier.SupplierBaseInfoRespDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.StringConstant;
import com.gz.eim.am.stock.dao.repair.AssetsRepairHeadMapper;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairApproveReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairAssetsRepairLineReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairHeadReqDTO;
import com.gz.eim.am.stock.dto.request.repair.StockAssetsRepairLineBaseReqDTO;
import com.gz.eim.am.stock.dto.response.repair.StockAssetsRepairHeadRespDTO;
import com.gz.eim.am.stock.dto.response.repair.StockAssetsRepairLineBaseRespDTO;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.handler.manager.StockAssetsRepairAddAssetsImportExcelManager;
import com.gz.eim.am.stock.handler.manager.StockAssetsRepairAssetsDetailExportExcelManager;
import com.gz.eim.am.stock.handler.manager.StockAssetsRepairBatchUpdateAssetsImportExcelManager;
import com.gz.eim.am.stock.handler.manager.StockAssetsRepairGetRepairRespLineManager;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.assets.StockAssetsCompensationRecordService;
import com.gz.eim.am.stock.service.assets.StockAssetsService;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairHeadService;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairLineExtendService;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairLineService;
import com.gz.eim.am.stock.service.repair.StockAssetsRepairService;
import com.gz.eim.am.stock.service.warehouse.StockWarehouseService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.common.ObjectUtils;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: StockAssetsRepairServiceImpl
 * @description: 资产维修ServiceImpl
 * @author: <EMAIL>
 * @date: 2022/12/6
 **/
@Slf4j
@Service
public class StockAssetsRepairServiceImpl implements StockAssetsRepairService {

    @Autowired
    private StockAssetsService stockAssetsService;
    @Autowired
    private StockWarehouseService stockWarehouseService;
    @Autowired
    private StockAssetsCompensationRecordService stockAssetsCompensationRecordService;
    @Autowired
    private StockAssetsRepairServiceHelper stockAssetsRepairServiceHelper;
    @Autowired
    private StockAssetsRepairAddAssetsImportExcelManager stockRepairAddAssetsImportExcelManager;
    @Autowired
    private StockAssetsRepairHeadService stockAssetsRepairHeadService;
    @Autowired
    private StockAssetsRepairLineService stockAssetsRepairLineService;
    @Autowired
    private StockAssetsRepairLineExtendService stockAssetsRepairLineExtendService;
    @Autowired
    private FileServiceApi fileServiceApi;
    @Autowired
    private StockAssetsRepairGetRepairRespLineManager stockAssetsRepairGetRepairRespLineManager;
    @Autowired
    private SupplierDataApi supplierDataApi;
    @Autowired
    private StockAssetsRepairAssetsDetailExportExcelManager stockAssetsRepairAssetsDetailExportExcelManager;
    @Autowired
    private StockAssetsRepairBatchUpdateAssetsImportExcelManager stockAssetsRepairBatchUpdateAssetsImportExcelManager;
    @Autowired
    private AssetsRepairHeadMapper assetsRepairHeadMapper;
    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Override
    public ResponseData ITApprove(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO) {
        if (null == stockAssetsRepairApproveReqDTO) {
            return ResponseData.createFailResult("请求参数不能为空");
        }
        String approver = stockAssetsRepairApproveReqDTO.getItApprover();
        if (StringUtils.isBlank(approver)) {
            return ResponseData.createFailResult("审批人不能为空");
        }
        Integer approveStatus = stockAssetsRepairApproveReqDTO.getApproveStatus();
        if (null == approveStatus) {
            return ResponseData.createFailResult("审批状态不能为空");
        }
        if (!CommonEnum.approveStatusMap.containsKey(approveStatus)) {
            return ResponseData.createFailResult("审批状态不正确");
        }
        // 如果是审批拒绝直接返回
        if (CommonEnum.approveStatus.REJECT.getValue().equals(approveStatus)) {
            return ResponseData.createSuccessResult();
        }
        String assetsCode = stockAssetsRepairApproveReqDTO.getAssetsCode();
        if (StringUtils.isBlank(assetsCode)) {
            return ResponseData.createFailResult("资产编码不能为空");
        }
        // 查询资产信息
        StockAssets stockAssets = stockAssetsService.selectAssetsByCode(assetsCode);
        if (null == stockAssets) {
            return ResponseData.createFailResult("资产编码不存在");
        }
        if (!AssetsEnum.statusType.USED.getValue().equals(stockAssets.getStatus())) {
            return ResponseData.createFailResult("资产编码的使用状态不是使用中，不能审批通过");
        }
        if (!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAssets.getApproveStatus())) {
            return ResponseData.createFailResult("资产编码在其他单据的审批中，不能审批通过");
        }
        stockAssets.setApproveStatus(AssetsEnum.ApproveStatus.SUBMIT.getValue());
        stockAssets.setUpdatedAt(new Date());
        stockAssets.setUpdatedBy(approver);
        stockAssetsService.updateOne(stockAssets);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData adminApprove(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO) {
        if (null == stockAssetsRepairApproveReqDTO) {
            return ResponseData.createFailResult("请求参数不能为空");
        }
        String approver = stockAssetsRepairApproveReqDTO.getAdminApprover();
        if (StringUtils.isBlank(approver)) {
            return ResponseData.createFailResult("审批人不能为空");
        }
        Integer approveStatus = stockAssetsRepairApproveReqDTO.getApproveStatus();
        if (null == approveStatus) {
            return ResponseData.createFailResult("审批状态不能为空");
        }
        if (!CommonEnum.approveStatusMap.containsKey(approveStatus)) {
            return ResponseData.createFailResult("审批状态不正确");
        }
        // 如果是审批拒绝，直接返回
        if (CommonEnum.approveStatus.REJECT.getValue().equals(approveStatus)) {
            return ResponseData.createSuccessResult();
        }
        String assetsCode = stockAssetsRepairApproveReqDTO.getAssetsCode();
        if (StringUtils.isBlank(assetsCode)) {
            return ResponseData.createFailResult("旧资产编码不能为空");
        }
        // 查询资产信息
        StockAssets stockAssets = stockAssetsService.selectAssetsByCode(assetsCode);
        if (null == stockAssets) {
            return ResponseData.createFailResult("旧资产编码不存在");
        }
        if (!AssetsEnum.statusType.USED.getValue().equals(stockAssets.getStatus())) {
            return ResponseData.createFailResult("旧资产编码的使用状态不是使用中，不能审批通过");
        }
        if (!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockAssets.getApproveStatus())) {
            return ResponseData.createFailResult("旧资产编码在其他单据的审批中，不能审批通过");
        }
        Date currentDate = new Date();
        List<StockAssets> updateStockAssetsList = new ArrayList<>(CommonConstant.NUMBER_TWO);
        Integer damageReason = stockAssetsRepairApproveReqDTO.getDamageReason();
        if ((StockAssetsRepairApproveEnum.DamageReason.NORMAL_DAMAGE.getValue().equals(damageReason) || StockAssetsRepairApproveEnum.AssetsRepairDealMethod.EMPLOYEE_COMPENSATION.getValue().equals(stockAssetsRepairApproveReqDTO.getAssetsRepairDealMethod()))) {
            // 设置资产使用状态为已损坏
            stockAssets.setConditions(AssetsEnum.Conditions.BREAK.getValue());
        } else {
            // 设置资产使用状态为待报废
            stockAssets.setConditions(AssetsEnum.Conditions.WAIT_DISCAERD.getValue());
        }
        // 设置资产状态为审批中
        stockAssets.setApproveStatus(AssetsEnum.ApproveStatus.SUBMIT.getValue());
        stockAssets.setUpdatedAt(currentDate);
        stockAssets.setUpdatedBy(approver);
        updateStockAssetsList.add(stockAssets);
        String newAssetsCode = stockAssetsRepairApproveReqDTO.getNewAssetsCode();
        if (StringUtils.isNotBlank(newAssetsCode)) {
            // 查询资产信息
            StockAssets stockNewAssets = stockAssetsService.selectAssetsByCode(newAssetsCode);
            if (null == stockNewAssets) {
                return ResponseData.createFailResult("新资产编码不存在");
            }
            if (!AssetsEnum.statusType.IDLE.getValue().equals(stockNewAssets.getStatus())) {
                return ResponseData.createFailResult("新资产编码的使用状态不是在库，不能审批通过");
            }
            if (!AssetsEnum.ApproveStatus.NO_APPROVE.getValue().equals(stockNewAssets.getApproveStatus())) {
                return ResponseData.createFailResult("新资产编码在其他单据的审批中，不能审批通过");
            }
            stockNewAssets.setApproveStatus(AssetsEnum.ApproveStatus.SUBMIT.getValue());
            stockNewAssets.setUpdatedAt(currentDate);
            stockNewAssets.setUpdatedBy(approver);
            updateStockAssetsList.add(stockNewAssets);
        }
        stockAssetsService.batchUpdate(updateStockAssetsList);
        return ResponseData.createSuccessResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData assetsHolderApprove(StockAssetsRepairApproveReqDTO stockAssetsRepairApproveReqDTO) throws Exception {
        Map<String, StockAssets> stockAssetsMap = new HashMap<>(CommonConstant.NUMBER_TWO);
        String errorMessage = stockAssetsRepairServiceHelper.checkAssetsHolderApproveCommonData(stockAssetsRepairApproveReqDTO, stockAssetsMap);
        if (StringUtils.isNotBlank(errorMessage)) {
            return ResponseData.createFailResult(errorMessage);
        }
        StockAssets stockAssets = stockAssetsMap.get(stockAssetsRepairApproveReqDTO.getAssetsCode());
        StockAssets newStockAssets = stockAssetsMap.get(stockAssetsRepairApproveReqDTO.getNewAssetsCode());
        String approveUser = stockAssetsRepairApproveReqDTO.getAssetsHolder();
        // 如果是审批通过
        if (CommonEnum.approveStatus.PASS.getValue().equals(stockAssetsRepairApproveReqDTO.getApproveStatus())) {
            if (StringUtils.isBlank(stockAssetsRepairApproveReqDTO.getAdminApprover())) {
                return ResponseData.createFailResult("行政审批人不能为空");
            }
            if (StringUtils.isBlank(stockAssetsRepairApproveReqDTO.getAssetsHolder())) {
                return ResponseData.createFailResult("资产持有人不能为空");
            }
            BigDecimal lastCost = stockAssetsRepairApproveReqDTO.getLastCost();
            if (null == lastCost) {
                return ResponseData.createFailResult("当前净值不能为空");
            }
            Integer damageReason = stockAssetsRepairApproveReqDTO.getDamageReason();
            if (null == damageReason) {
                return ResponseData.createFailResult("损坏原因不能为空");
            }
            if (!StockAssetsRepairApproveEnum.damageReasonMap.containsKey(damageReason)) {
                return ResponseData.createFailResult("损坏原因不存在");
            }
            // 如果损坏原因是人为损坏，必须要有资产赔偿信息
            if (StockAssetsRepairApproveEnum.DamageReason.ARTIFICIAL_DAMAGE.getValue().equals(damageReason)) {
                StockAssetsCompensationRecord stockAssetsCompensationRecord = new StockAssetsCompensationRecord();
                // 校验资产赔偿记录信息
                errorMessage = stockAssetsRepairServiceHelper.checkAndGetAssetsCompensation(stockAssetsRepairApproveReqDTO, stockAssetsCompensationRecord);
                if (StringUtils.isNotBlank(errorMessage)) {
                    return ResponseData.createFailResult(errorMessage);
                }
                // 插入资产赔偿记录表
                stockAssetsCompensationRecordService.insertOne(stockAssetsCompensationRecord);
                // 暂时不对接薪酬
            }
            // 只要不是员工回购而且资产状态是使用中就需要自动生成归还单
            if ((StockAssetsRepairApproveEnum.DamageReason.NORMAL_DAMAGE.getValue().equals(damageReason) || StockAssetsRepairApproveEnum.AssetsRepairDealMethod.EMPLOYEE_COMPENSATION.getValue().equals(stockAssetsRepairApproveReqDTO.getAssetsRepairDealMethod()))) {
                if (AssetsEnum.statusType.USED.getValue().equals(stockAssets.getStatus())) {
                    String returnWarehouseCode = stockAssetsRepairApproveReqDTO.getReturnWarehouseCode();
                    if (StringUtils.isBlank(returnWarehouseCode)) {
                        return ResponseData.createFailResult("归还仓库不能为空");
                    }
                    StockWarehouse stockWarehouse = stockWarehouseService.selectByWarehouseCode(returnWarehouseCode, null);
                    if (null == stockWarehouse) {
                        return ResponseData.createFailResult("归还仓库不存在");
                    }
                    // 生成归还单
                    stockAssetsRepairServiceHelper.createAssetsReturnInventoryRecordList(stockAssetsRepairApproveReqDTO, stockAssets);
                }
            }
            Integer isReceiveNewAssets = stockAssetsRepairApproveReqDTO.getIsReceiveNewAssets();
            if (null == isReceiveNewAssets) {
                return ResponseData.createFailResult("是否领用不能为空");
            }
            // 如果领用新的资产，自动生成资产领用单
            if (CommonEnum.status.YES.getValue().equals(isReceiveNewAssets)) {
                if (null == newStockAssets) {
                    return ResponseData.createFailResult("如果选择了需要领用资产，必须要选择新资产编码");
                }
                if(StringUtils.isBlank(stockAssetsRepairApproveReqDTO.getUseAddress())){
                    return ResponseData.createFailResult("如果选择了需要领用资产，必须填写新持有人位置");
                }
                // 生成领用单
                stockAssetsRepairServiceHelper.createAssetsReceiveDeliveryRecordList(stockAssetsRepairApproveReqDTO, newStockAssets);
            }
            // 更新资产编码未审批，使用状态为正常
            List<StockAssets> stockAssetsList = new ArrayList<>(CommonConstant.NUMBER_TWO);
            StockAssets updateOldStockAssets = new StockAssets();
            updateOldStockAssets.setAssetsId(stockAssets.getAssetsId());
            updateOldStockAssets.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
            updateOldStockAssets.setUpdatedBy(approveUser);
            updateOldStockAssets.setUpdatedAt(new Date());
            stockAssetsList.add(updateOldStockAssets);
            if (newStockAssets != null) {
                StockAssets updateNewStockAssets = new StockAssets();
                updateNewStockAssets.setAssetsId(newStockAssets.getAssetsId());
                updateNewStockAssets.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
                updateNewStockAssets.setUpdatedBy(approveUser);
                updateNewStockAssets.setUpdatedAt(new Date());
                stockAssetsList.add(updateNewStockAssets);
            }
            stockAssetsService.batchUpdate(stockAssetsList);
        }else {
            // 更新资产编码未审批，使用状态为正常
            List<StockAssets> stockAssetsList = new ArrayList<>(CommonConstant.NUMBER_TWO);
            StockAssets updateOldStockAssets = new StockAssets();
            updateOldStockAssets.setAssetsId(stockAssets.getAssetsId());
            updateOldStockAssets.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
            updateOldStockAssets.setConditions(AssetsEnum.Conditions.NORMAL.getValue());
            updateOldStockAssets.setUpdatedBy(approveUser);
            updateOldStockAssets.setUpdatedAt(new Date());
            stockAssetsList.add(updateOldStockAssets);
            if (newStockAssets != null) {
                StockAssets updateNewStockAssets = new StockAssets();
                updateNewStockAssets.setAssetsId(newStockAssets.getAssetsId());
                updateNewStockAssets.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
                updateNewStockAssets.setConditions(AssetsEnum.Conditions.NORMAL.getValue());
                updateNewStockAssets.setUpdatedBy(approveUser);
                updateNewStockAssets.setUpdatedAt(new Date());
                stockAssetsList.add(updateNewStockAssets);
            }
            stockAssetsService.batchUpdate(stockAssetsList);
        }
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData importAssetsExcel(MultipartFile file, Integer repairType) throws Exception {
        if (null == file) {
            return ResponseData.createFailResult("上传文件不能为空");
        }
        if (null == repairType) {
            return ResponseData.createFailResult("维修类型不能为空");
        }
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(repairType);
        if (null == repairTypeEnum) {
            return ResponseData.createFailResult("维修类型不正确");
        }
        return stockRepairAddAssetsImportExcelManager.getStockAssetsRepairLineRespList(repairTypeEnum, file);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData repairSaveOrSubmit(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user) throws Exception {
        List<StockAssetsRepairLine> insertStockAssetsRepairLineList = new ArrayList<>();
        List<StockAssetsRepairLine> updateStockAssetsRepairLineList = new ArrayList<>();
        List<StockAssetsRepairLineExtend> insertStockAssetsRepairLineExtendList = new ArrayList<>();
        List<StockAssetsRepairLineExtend> updateStockAssetsRepairLineExtendList = new ArrayList<>();
        StockAssetsRepairHead stockAssetsRepairHead = new StockAssetsRepairHead();
        List<StockAssets> updateStockAssetsList = new ArrayList<>();
        // 检查信息正确性，并获取基础数据
        String errorMessage = stockAssetsRepairServiceHelper.checkRepairSaveOrSubmitParamAndGetData(
                stockAssetsRepairHeadReqDTO, user,
                stockAssetsRepairHead, insertStockAssetsRepairLineList,
                updateStockAssetsRepairLineList, insertStockAssetsRepairLineExtendList,
                updateStockAssetsRepairLineExtendList, updateStockAssetsList
        );
        if (StringUtils.isNotBlank(errorMessage)) {
            return ResponseData.createFailResult(errorMessage);
        }
        // 处理附件信息
        stockAssetsRepairServiceHelper.dealAttachList(stockAssetsRepairHead.getRepairNo(), stockAssetsRepairHeadReqDTO.getAttachIdList());
        // 插入头单或者更新头单
        if (stockAssetsRepairHead.getId() != null) {
            stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        } else {
            stockAssetsRepairHeadService.insertOne(stockAssetsRepairHead);
        }
        // 插入维修行信息
        if (CollectionUtils.isNotEmpty(insertStockAssetsRepairLineList)) {
            stockAssetsRepairLineService.batchInsert(insertStockAssetsRepairLineList);
        }
        // 更新维修行信息
        if (CollectionUtils.isNotEmpty(updateStockAssetsRepairLineList)) {
            stockAssetsRepairLineService.batchUpdate(updateStockAssetsRepairLineList);
        }
        // 插入维修行尾表信息
        if (CollectionUtils.isNotEmpty(insertStockAssetsRepairLineExtendList)) {
            stockAssetsRepairLineExtendService.batchInsert(insertStockAssetsRepairLineExtendList);
        }
        // 更新维修行尾表信息
        if (CollectionUtils.isNotEmpty(updateStockAssetsRepairLineExtendList)) {
            stockAssetsRepairLineExtendService.batchUpdate(updateStockAssetsRepairLineExtendList);
        }
        // 如果是提交信息需要发起审批流
        if (StockAssetsRepairHeadEnum.OperationType.SUBMIT.getValue().equals(stockAssetsRepairHeadReqDTO.getOperationType())) {
            // 更新资产的审批状态
            if (CollectionUtils.isNotEmpty(updateStockAssetsList)) {
                stockAssetsService.batchUpdate(updateStockAssetsList);
            }
            stockAssetsRepairServiceHelper.repairSaveOrSubmitCallWfl(stockAssetsRepairHead);
        }
        return ResponseData.createSuccessResult();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void repairApprove(String repairNo, Integer status) {
        if (StringUtils.isBlank(repairNo)) {
            log.error("StockAssetsRepairServiceImpl.repairApprove，维修单号为空");
            return;
        }
        if (null == status) {
            log.error("StockAssetsRepairServiceImpl.repairApprove，status为空，repairNo:{}", repairNo);
            return;
        }
        // 查询维修单数据
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO = new StockAssetsRepairHeadReqDTO();
        stockAssetsRepairHeadReqDTO.setRepairNo(repairNo);
        StockAssetsRepairHead stockAssetsRepairHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadReqDTO);
        if (null == stockAssetsRepairHead) {
            log.error("StockAssetsRepairServiceImpl.repairApprove，单据不存在，repairNo:{},status:{}", repairNo, status);
            return;
        }
        if (!StockAssetsRepairHeadEnum.Status.IN_APPROVAL.getValue().equals(stockAssetsRepairHead.getStatus())) {
            log.error("StockAssetsRepairServiceImpl.repairApprove，单据状态不是审批中，repairNo:{},status:{}", repairNo, status);
            return;
        }
        stockAssetsRepairHead.setStatus(status);
        stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO = new StockAssetsRepairAssetsRepairLineReqDTO();
        stockAssetsRepairLineBaseReqDTO.setRepairNo(repairNo);
        List<StockAssetsRepairLine> stockAssetsRepairLineList = stockAssetsRepairLineService.selectList(stockAssetsRepairLineBaseReqDTO);
        List<StockAssets> updateStockAssetsList = new ArrayList<>(stockAssetsRepairLineList.size());
        // 如果是驳回需要把资产的状态更新为未审批
        if (StockAssetsRepairHeadEnum.Status.REJECTED.getValue().equals(status)) {
            if (CollectionUtils.isNotEmpty(stockAssetsRepairLineList)) {
                for (StockAssetsRepairLine stockAssetsRepairLine : stockAssetsRepairLineList) {
                    StockAssets stockAssets = new StockAssets();
                    stockAssets.setAssetsCode(stockAssetsRepairLine.getAssetsCode());
                    stockAssets.setApproveStatus(AssetsEnum.ApproveStatus.NO_APPROVE.getValue());
                    updateStockAssetsList.add(stockAssets);
                }
            }
            // 如果是审批通过而且单据类型为资产维修单，需要把资产状态改为待维修
        } else {
            if (StockAssetsRepairHeadEnum.RepairType.ASSETS_REPAIR.getValue().equals(stockAssetsRepairHead.getRepairType()) && CollectionUtils.isNotEmpty(stockAssetsRepairLineList)) {
                for (StockAssetsRepairLine stockAssetsRepairLine : stockAssetsRepairLineList) {
                    StockAssets stockAssets = new StockAssets();
                    stockAssets.setAssetsCode(stockAssetsRepairLine.getAssetsCode());
                    stockAssets.setConditions(AssetsEnum.Conditions.WAIT_REPAIR.getValue());
                    updateStockAssetsList.add(stockAssets);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateStockAssetsList)) {
            stockAssetsService.batchUpdateByAssetsCodeList(updateStockAssetsList);
        }
    }

    @Override
    public ResponseData queryRepairDetail(Long id, String repairNo) throws Exception {
        if (null == id && StringUtils.isBlank(repairNo)) {
            return ResponseData.createFailResult("id和repairNo不能同时为空");
        }
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO = new StockAssetsRepairHeadReqDTO();
        if (id != null) {
            stockAssetsRepairHeadReqDTO.setId(id);
        } else {
            stockAssetsRepairHeadReqDTO.setRepairNo(repairNo);
        }
        StockAssetsRepairHead stockAssetsRepairHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadReqDTO);
        if (null == stockAssetsRepairHead) {
            return ResponseData.createFailResult("维修单不存在");
        }
        // 获取头单信息
        StockAssetsRepairHeadRespDTO stockAssetsRepairHeadRespDTO = stockAssetsRepairServiceHelper.getStockAssetsRepairHeadRespDTO(stockAssetsRepairHead);
        StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO = new StockAssetsRepairAssetsRepairLineReqDTO();
        stockAssetsRepairLineBaseReqDTO.setRepairNo(stockAssetsRepairHead.getRepairNo());
        stockAssetsRepairLineBaseReqDTO.setIsValid(CommonEnum.status.YES.getValue());
        List<StockAssetsRepairLine> stockAssetsRepairLineList = stockAssetsRepairLineService.selectList(stockAssetsRepairLineBaseReqDTO);
        if (CollectionUtils.isEmpty(stockAssetsRepairLineList)) {
            return ResponseData.createFailResult("维修单行信息不存在");
        }
        List<String> repairNoList = stockAssetsRepairLineList.stream().map(StockAssetsRepairLine::getRepairItemNo).collect(Collectors.toList());
        stockAssetsRepairLineBaseReqDTO.setRepairItemNoList(repairNoList);
        Map<String, StockAssetsRepairLineExtend> stockAssetsRepairLineExtendMap = stockAssetsRepairLineExtendService.selectMapByItemNo(stockAssetsRepairLineBaseReqDTO);
        if (MapUtils.isEmpty(stockAssetsRepairLineExtendMap)) {
            return ResponseData.createFailResult("维修单扩展行信息不存在");
        }
        for (int i = 0; i < stockAssetsRepairLineList.size(); i++) {
            StockAssetsRepairLine stockAssetsRepairLine = stockAssetsRepairLineList.get(i);
            if (!stockAssetsRepairLineExtendMap.containsKey(stockAssetsRepairLine.getRepairItemNo())) {
                return ResponseData.createFailResult("第" + (i + 1) + "行的资产维修扩展行信息不存在");
            }
        }
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(stockAssetsRepairHead.getRepairType());
        if (null == repairTypeEnum) {
            return ResponseData.createFailResult("维修单维修类型不正确");
        }
        // 获取行单信息
        List<StockAssetsRepairLineBaseRespDTO> stockAssetsRepairLineRespList = stockAssetsRepairGetRepairRespLineManager.getStockAssetsRepairLineRespList(repairTypeEnum, stockAssetsRepairLineList, stockAssetsRepairLineExtendMap);
        if (CollectionUtils.isEmpty(stockAssetsRepairLineRespList)) {
            return ResponseData.createFailResult("维修单行信息不存在");
        }
        stockAssetsRepairHeadRespDTO.setStockAssetsRepairLineRespList(stockAssetsRepairLineRespList);
        return ResponseData.createSuccessResult(stockAssetsRepairHeadRespDTO);
    }

    @Override
    public ResponseData exportAssetsExcel(String repairNo, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isBlank(repairNo)) {
            return ResponseData.createFailResult("维修单号不能为空");
        }
        // 查询维修单数据
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO = new StockAssetsRepairHeadReqDTO();
        stockAssetsRepairHeadReqDTO.setRepairNo(repairNo);
        StockAssetsRepairHead stockAssetsRepairHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadReqDTO);
        if (null == stockAssetsRepairHead) {
            return ResponseData.createFailResult("维修单不存在");
        }
        // 查询维修单行信息
        StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO = new StockAssetsRepairAssetsRepairLineReqDTO();
        stockAssetsRepairLineBaseReqDTO.setRepairNo(repairNo);
        stockAssetsRepairLineBaseReqDTO.setIsValid(CommonConstant.NUMBER_ONE);
        List<StockAssetsRepairLine> stockAssetsRepairLineList = stockAssetsRepairLineService.selectList(stockAssetsRepairLineBaseReqDTO);
        if (CollectionUtils.isEmpty(stockAssetsRepairLineList)) {
            return ResponseData.createFailResult("维修单行信息不存在");
        }
        List<String> repairItemNoList = stockAssetsRepairLineList.stream().map(StockAssetsRepairLine::getRepairItemNo).collect(Collectors.toList());
        stockAssetsRepairLineBaseReqDTO.setRepairItemNoList(repairItemNoList);
        Map<String, StockAssetsRepairLineExtend> stringStockAssetsRepairLineExtendMap = stockAssetsRepairLineExtendService.selectMapByItemNo(stockAssetsRepairLineBaseReqDTO);
        if (MapUtils.isEmpty(stringStockAssetsRepairLineExtendMap)) {
            return ResponseData.createFailResult("维修单行扩展信息不存在");
        }
        LocalDateTime dateTime = LocalDateTime.now();
        String fileName = "资产明细_" + dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        StockAssetsRepairHeadEnum.Status statusEnum = StockAssetsRepairHeadEnum.statusMap.get(stockAssetsRepairHead.getStatus());
        if (null == statusEnum) {
            return ResponseData.createFailResult("单据状态不正确");
        }
        // 根据不同的维修类型生成不同的excel
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(stockAssetsRepairHead.getRepairType());
        if (repairTypeEnum != null) {
            stockAssetsRepairAssetsDetailExportExcelManager.exportAssetsDetailExcel(
                    repairTypeEnum, fileName,
                    stockAssetsRepairLineList, stringStockAssetsRepairLineExtendMap,
                    request, response, statusEnum);
        }
        return ResponseData.createSuccessResult("导出成功");
    }

    @Override
    public ResponseData importBatchUpdateAssetsExcel(MultipartFile file, Integer repairType, Integer status) throws Exception {
        if (null == file) {
            return ResponseData.createFailResult("文件内容不能为空");
        }
        if (null == repairType) {
            return ResponseData.createFailResult("维修类型不能为空");
        }
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(repairType);
        if (null == repairTypeEnum) {
            return ResponseData.createFailResult("维修类型不存在");
        }
        if (null == status) {
            return ResponseData.createFailResult("单据状态不能为空");
        }
        StockAssetsRepairHeadEnum.Status statusEnum = StockAssetsRepairHeadEnum.statusMap.get(status);
        if (null == statusEnum) {
            return ResponseData.createFailResult("单据状态不存在");
        }
        return stockAssetsRepairBatchUpdateAssetsImportExcelManager.getStockAssetsRepairLineRespList(repairTypeEnum, file, statusEnum);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData repairCheckSubmit(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user) {
        StockAssetsRepairHead stockAssetsRepairHead = new StockAssetsRepairHead();
        List<StockAssetsRepairLineExtend> updateStockAssetsRepairLineExtendList = new ArrayList<>();
        // 检查参数并获取拼接值
        String errorMessage = stockAssetsRepairServiceHelper.checkRepairCheckSubmitParamAndGetData(stockAssetsRepairHeadReqDTO, user,
                stockAssetsRepairHead, updateStockAssetsRepairLineExtendList);
        if (StringUtils.isNotBlank(errorMessage)) {
            return ResponseData.createFailResult(errorMessage);
        }
        // 更新头单信息
        stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        // 更新行单信息
        stockAssetsRepairLineExtendService.batchUpdate(updateStockAssetsRepairLineExtendList);
        // 发起审批流
        stockAssetsRepairServiceHelper.repairCheckSubmitCallWfl(stockAssetsRepairHead);
        return ResponseData.createSuccessResult();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData repairCheckApprove(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user) {
        StockAssetsRepairHead stockAssetsRepairHead = new StockAssetsRepairHead();
        List<StockAssetsRepairLine> updateStockAssetsRepairLineList = new ArrayList<>();
        List<StockAssets> updateAssetsList = new ArrayList<>();
        // 检查资产验收审批的请求参数
        String errorMessage = stockAssetsRepairServiceHelper.checkRepairCheckApproveParamAndGetData(
                stockAssetsRepairHeadReqDTO, stockAssetsRepairHead,
                updateStockAssetsRepairLineList, updateAssetsList,
                user);
        if (StringUtils.isNotBlank(errorMessage)) {
            return ResponseData.createFailResult(errorMessage);
        }
        // 更新资产维修头信息
        stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        // 更新资产维修行信息
        stockAssetsRepairLineService.batchUpdate(updateStockAssetsRepairLineList);
        // 更新资产的信息
        stockAssetsService.batchUpdateByAssetsCodeList(updateAssetsList);
        // 如果单据类型为配置升级，更新资产尾表信息
        StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(stockAssetsRepairHead.getRepairType());
        if(StockAssetsRepairHeadEnum.RepairType.CONFIGURATION_UPGRADE == repairTypeEnum){
            stockAssetsRepairServiceHelper.batchUpdateAssetsExtendListByAssetsList(updateAssetsList);
        }
        return ResponseData.createSuccessResult();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseData repairDelete(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO, JwtUser user) {
        StockAssetsRepairHeadReqDTO stockAssetsRepairHeadSearchReqDTO = new StockAssetsRepairHeadReqDTO();
        stockAssetsRepairHeadSearchReqDTO.setId(stockAssetsRepairHeadReqDTO.getId());
        StockAssetsRepairHead stockAssetsRepairHead = stockAssetsRepairHeadService.selectOne(stockAssetsRepairHeadSearchReqDTO);
        if (null == stockAssetsRepairHead) {
            return ResponseData.createFailResult("单据不存在");
        }
        if (!StockAssetsRepairHeadEnum.Status.SAVE.getValue().equals(stockAssetsRepairHead.getStatus())) {
            return ResponseData.createFailResult("单据不是保存状态不能删除");
        }
        // 查询对应的行信息
        StockAssetsRepairLineBaseReqDTO stockAssetsRepairLineBaseReqDTO = new StockAssetsRepairAssetsRepairLineReqDTO();
        stockAssetsRepairLineBaseReqDTO.setRepairNo(stockAssetsRepairHead.getRepairNo());
        List<StockAssetsRepairLine> stockAssetsRepairLineList = stockAssetsRepairLineService.selectList(stockAssetsRepairLineBaseReqDTO);
        if (CollectionUtils.isEmpty(stockAssetsRepairLineList)) {
            return ResponseData.createFailResult("资产维修行信息不能为空");
        }
        for (StockAssetsRepairLine stockAssetsRepairLine : stockAssetsRepairLineList) {
            stockAssetsRepairLine.setIsValid(CommonEnum.status.NO.getValue());
        }
        stockAssetsRepairHead.setIsValid(CommonEnum.status.NO.getValue());
        // 删除头信息
        stockAssetsRepairHeadService.updateOne(stockAssetsRepairHead);
        // 删除行信息
        stockAssetsRepairLineService.batchUpdate(stockAssetsRepairLineList);
        return ResponseData.createSuccessResult();
    }

    @Override
    public ResponseData queryRepairList(StockAssetsRepairHeadReqDTO stockAssetsRepairHeadReqDTO) {
        if (null == stockAssetsRepairHeadReqDTO) {
            return ResponseData.createFailResult("请求参数不能为空");
        }
        StockAssetsRepairHeadListReqDO stockAssetsRepairHeadListReqDO = new StockAssetsRepairHeadListReqDO();
        String billingStartDateAndEndDate = stockAssetsRepairHeadReqDTO.getBillingStartDateAndEndDate();
        if (StringUtils.isNotBlank(billingStartDateAndEndDate)) {
            String[] billingDateArray = billingStartDateAndEndDate.split(StringConstant.COMMA);
            if (billingDateArray.length == CommonConstant.NUMBER_TWO) {
                stockAssetsRepairHeadListReqDO.setBillingStartDate(DateTimeUtil.addTimeStartEnd(billingDateArray[CommonConstant.NUMBER_ZERO]));
                stockAssetsRepairHeadListReqDO.setBillingEndDate(DateTimeUtil.addTimeEndEnd(billingDateArray[CommonConstant.NUMBER_ONE]));
            }
        }
        BeanUtils.copyProperties(stockAssetsRepairHeadReqDTO, stockAssetsRepairHeadListReqDO);
        stockAssetsRepairHeadReqDTO.initPageDefaultParam();
        long count = assetsRepairHeadMapper.countRepairList(stockAssetsRepairHeadListReqDO);
        if (count <= CommonConstant.NUMBER_ZERO) {
            PageInfo pageInfo = new PageInfo(new ArrayList());
            pageInfo.setPageNum(stockAssetsRepairHeadReqDTO.getPageNum());
            pageInfo.setPageSize(stockAssetsRepairHeadReqDTO.getPageSize());
            return ResponseData.createSuccessResult(pageInfo);
        }
        List<StockAssetsRepairHeadListRespDO> stockAssetsRepairHeadListRespDOList = assetsRepairHeadMapper.selectRepairList(stockAssetsRepairHeadListReqDO);
        if (CollectionUtils.isEmpty(stockAssetsRepairHeadListRespDOList)) {
            return ResponseData.createSuccessResult();
        }
        Set<String> supplierCodeSet = new HashSet<>();
        Set<String> billingUserSet = new HashSet<>();
        List<StockAssetsRepairHeadRespDTO> stockAssetsRepairHeadRespDTOList = new ArrayList<>(stockAssetsRepairHeadListRespDOList.size());
        for (StockAssetsRepairHeadListRespDO stockAssetsRepairHeadListRespDO : stockAssetsRepairHeadListRespDOList) {
            StockAssetsRepairHeadRespDTO stockAssetsRepairHeadRespDTO = new StockAssetsRepairHeadRespDTO();
            BeanUtils.copyProperties(stockAssetsRepairHeadListRespDO, stockAssetsRepairHeadRespDTO);
            supplierCodeSet.add(stockAssetsRepairHeadListRespDO.getSupplierCode());
            billingUserSet.add(stockAssetsRepairHeadListRespDO.getBillingUser());
            Integer repairType = stockAssetsRepairHeadListRespDO.getRepairType();
            // 设置维修类型
            if (repairType != null) {
                StockAssetsRepairHeadEnum.RepairType repairTypeEnum = StockAssetsRepairHeadEnum.repairTypeMap.get(repairType);
                if (repairTypeEnum != null) {
                    stockAssetsRepairHeadRespDTO.setRepairTypeDesc(repairTypeEnum.getDesc());
                }
            }
            BigDecimal repairNumber = stockAssetsRepairHeadListRespDO.getRepairNumber();
            if (repairNumber != null) {
                stockAssetsRepairHeadRespDTO.setRepairNumber(repairNumber.setScale(CommonConstant.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
            }
            BigDecimal repairTotalMoney = stockAssetsRepairHeadListRespDO.getRepairTotalMoney();
            if (repairTotalMoney != null) {
                stockAssetsRepairHeadRespDTO.setRepairTotalMoney(repairTotalMoney.setScale(CommonConstant.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
            }
            // 设置单据状态
            Integer status = stockAssetsRepairHeadListRespDO.getStatus();
            if (status != null) {
                StockAssetsRepairHeadEnum.Status statusEnum = StockAssetsRepairHeadEnum.statusMap.get(status);
                if (statusEnum != null) {
                    stockAssetsRepairHeadRespDTO.setStatusDesc(statusEnum.getDesc());
                }
            }
            stockAssetsRepairHeadRespDTOList.add(stockAssetsRepairHeadRespDTO);
        }
        // 查询供应商集合
        SupplierDataSearchReqDTO supplierDataSearchReqDTO = new SupplierDataSearchReqDTO();
        supplierDataSearchReqDTO.setCodeList(new ArrayList<>(supplierCodeSet));
        Map<String, SupplierBaseInfoRespDTO> supplierBaseInfoRespDTOMap = null;
        try {
            com.gz.eim.am.pdm.dto.ResponseData<List<Map<String, String>>> responseData = supplierDataApi.querySupplier(supplierDataSearchReqDTO);
            if (responseData != null && responseData.isSuccess()) {
                List<Map<String, String>> supplierBaseInfoRespDTOList = responseData.getData();
                if (CollectionUtils.isNotEmpty(supplierBaseInfoRespDTOList)) {
                    supplierBaseInfoRespDTOMap = new HashMap<>(supplierBaseInfoRespDTOList.size());
                    for (Map<String, String> supplierBaseInfoRespDTOTemp : supplierBaseInfoRespDTOList) {
                        SupplierBaseInfoRespDTO supplierBaseInfoRespDTO = ObjectUtils.mapToBean(supplierBaseInfoRespDTOTemp, SupplierBaseInfoRespDTO.class);
                        supplierBaseInfoRespDTOMap.put(supplierBaseInfoRespDTO.getSupplierCode(), supplierBaseInfoRespDTO);
                    }
                }
            } else {
                log.error("StockAssetsRepairServiceImpl.queryRepairList.supplierDataApi.querySupplier查询供应商失败，supplierDataSearchReqDTO:{}，失败信息为：{}", JSON.toJSONString(supplierDataSearchReqDTO), JSON.toJSONString(responseData));
            }
        } catch (Exception e) {
            log.error("StockAssetsRepairServiceImpl.queryRepairList.supplierDataApi.querySupplier查询供应商异常，supplierDataSearchReqDTO:{}，异常信息为：{}", JSON.toJSONString(supplierDataSearchReqDTO), e);
        }
        Map<String, SysUser> sysUserMap = null;
        if (CollectionUtils.isNotEmpty(billingUserSet)) {
            sysUserMap = ambaseCommonService.selectSysUserMapByIds(new ArrayList<>(billingUserSet));
        }
        for (StockAssetsRepairHeadRespDTO stockAssetsRepairHeadRespDTO : stockAssetsRepairHeadRespDTOList) {
            if (MapUtils.isNotEmpty(supplierBaseInfoRespDTOMap)) {
                SupplierBaseInfoRespDTO supplierBaseInfoRespDTO = supplierBaseInfoRespDTOMap.get(stockAssetsRepairHeadRespDTO.getSupplierCode());
                if (supplierBaseInfoRespDTO != null) {
                    stockAssetsRepairHeadRespDTO.setSuppliesName(supplierBaseInfoRespDTO.getSupplierName());
                }
            }
            if (MapUtils.isNotEmpty(sysUserMap)) {
                SysUser sysUser = sysUserMap.get(stockAssetsRepairHeadRespDTO.getBillingUser());
                if (sysUser != null) {
                    stockAssetsRepairHeadRespDTO.setBillingUserName(sysUser.getName());
                }
            }
        }
        PageInfo<StockAssetsRepairHeadRespDTO> pageInfo = new PageInfo<>(stockAssetsRepairHeadRespDTOList);
        pageInfo.setPageNum(stockAssetsRepairHeadReqDTO.getPageNum());
        pageInfo.setPageSize(stockAssetsRepairHeadReqDTO.getPageSize());
        pageInfo.setTotal(count);
        return ResponseData.createSuccessResult(pageInfo);
    }
}
