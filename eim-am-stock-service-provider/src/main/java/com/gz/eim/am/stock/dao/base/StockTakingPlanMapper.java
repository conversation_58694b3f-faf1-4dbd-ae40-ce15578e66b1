package com.gz.eim.am.stock.dao.base;

import com.gz.eim.am.stock.entity.StockTakingPlan;
import com.gz.eim.am.stock.entity.StockTakingPlanExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockTakingPlanMapper {
    long countByExample(StockTakingPlanExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StockTakingPlan record);

    int insertSelective(StockTakingPlan record);

    List<StockTakingPlan> selectByExample(StockTakingPlanExample example);

    StockTakingPlan selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") StockTakingPlan record, @Param("example") StockTakingPlanExample example);

    int updateByExample(@Param("record") StockTakingPlan record, @Param("example") StockTakingPlanExample example);

    int updateByPrimaryKeySelective(StockTakingPlan record);

    int updateByPrimaryKey(StockTakingPlan record);
}