package com.gz.eim.am.stock.service.impl.inventory;

import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.dao.inventory.InventoryInAssetsConditionsMapper;
import com.gz.eim.am.stock.entity.StockInventoryInAssetsConditions;
import com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets;
import com.gz.eim.am.stock.service.inventory.StockInventoryInAssetsConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: weijunjie
 * @date: 2020/7/24
 * @description
 */
@Service
public class StockInventoryInAssetsConditionsImpl implements StockInventoryInAssetsConditionService {

    @Autowired
    InventoryInAssetsConditionsMapper inventoryInAssetsConditionsMapper;

    @Override
    public Integer batchInsertAssetConditions(List<StockInventoryInAssetsConditions> stockInventoryInAssetsConditionsList) {
        if (CollectionUtils.isEmpty(stockInventoryInAssetsConditionsList)) {
            return new Integer(0);
        }

        int count = 0;
        List<StockInventoryInAssetsConditions> newStockInventoryInPlanLinesAssetsList = new ArrayList<>();
        if(stockInventoryInAssetsConditionsList.size() > 0){
            int toIndex= CommonConstant.MAX_INSERT_COUNT;
            for(int i = 0;i<stockInventoryInAssetsConditionsList.size();i+=CommonConstant.MAX_INSERT_COUNT){
                //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                if(i+CommonConstant.MAX_INSERT_COUNT>stockInventoryInAssetsConditionsList.size()){
                    toIndex=stockInventoryInAssetsConditionsList.size()-i;
                }
                List<StockInventoryInAssetsConditions> newSubDetailList = stockInventoryInAssetsConditionsList.subList(i,i+toIndex);
                int insertCount = inventoryInAssetsConditionsMapper.batchInsertAssetConditions(newSubDetailList);
                newStockInventoryInPlanLinesAssetsList.addAll(newSubDetailList);
                count = count + insertCount;
            }
        }

        stockInventoryInAssetsConditionsList = new ArrayList<>();
        stockInventoryInAssetsConditionsList.addAll(newStockInventoryInPlanLinesAssetsList);
        return count;
    }
}
