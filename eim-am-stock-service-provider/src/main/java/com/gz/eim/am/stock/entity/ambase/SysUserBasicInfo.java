package com.gz.eim.am.stock.entity.ambase;

import lombok.Data;

/**
 * 用户基本信息
 */
@Data
public class SysUserBasicInfo {
    /**
     * 员工电话
     */
    private String phone;
    /**
     * 员工姓名
     */
    private String name;
    /**
     *员工编号
     */
    private String empId;
    /**
     *员工邮箱
     */
    private String email;
    /**
     *所属部门编号
     */
    private String deptId;
    /**
     *所属部门名称
     */
    private String deptName;
    /**
     * 所属部门全路径部门id
     */
    private String deptFullId;
    /**
     * 所属部门全路径名称
     */
    private String deptFullName;
    /**
     * 职级名称
     */
    private String hpsJobcdDescr;
    /**
     * 员工类型
     */
    private Integer empType;
    /**
     * 职级code
     */
    private String jobFunction;
    /**
     * 入职工区
     */
    private String entryLocation;
    /**
     * 入职工区名称
     */
    private String entryLocationName;
    /**
     * 所属公司名称
     */
    private String companyFullName;
    /**
     * 职位编码
     */
    private String jobCode;
    /**
     * 业务线
     */
    private String cstBusinessLine;
    /**
     * 员工状态 A 正常 , I 离职
     */
    private String status;
    /**
     * 省编码
     */
    private String cstStageId;
    /**
     * 市编码
     */
    private String  cstCityId1;
    /**
     * 直属领导工号
     */
    private String supervisorId;
    /**
     * 首次入职时间
     */
    private String firstHireDt;
    /**
     * 标准职级
     */
    private String supvLvlId;

}
