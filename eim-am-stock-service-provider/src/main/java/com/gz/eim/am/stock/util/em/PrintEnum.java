package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2020/3/26
 * @description
 */
public class PrintEnum {
    /**
     * 文件类型
     */
    public enum fileType {

        /**
         * pdf
         */
        PDF(1, "pdf"),
        /**
         * img
         */
        img(2, "img")
        ;

        fileType(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<Integer, String> fileTypeEnumMap =
            Arrays.stream(PrintEnum.fileType.values()).collect(
                    Collectors.toMap(PrintEnum.fileType::getValue, PrintEnum.fileType::getDesc));


    /**
     * 文件类型
     */
    public enum direction {

        /**
         * 竖向
         */
        VERTICAL(0, "竖向"),
        /**
         * 横向
         */
        HORIZONTAL(1, "横向")
        ;

        direction(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        private Integer value;
        private String desc;

        public Integer getValue() {
            return this.value;
        }

        public void setValue(final Integer value) {
            this.value = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public void setDesc(final String desc) {
            this.desc = desc;
        }

    }

    public static Map<Integer, String> directionEnumMap =
            Arrays.stream(PrintEnum.direction.values()).collect(
                    Collectors.toMap(PrintEnum.direction::getValue, PrintEnum.direction::getDesc));
}
