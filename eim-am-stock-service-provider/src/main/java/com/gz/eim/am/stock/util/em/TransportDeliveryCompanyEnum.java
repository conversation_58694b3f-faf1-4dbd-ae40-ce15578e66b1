package com.gz.eim.am.stock.util.em;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: TransportDeliveryCompanyEnum
 * @description: 快递公司配置
 * @author: <EMAIL>
 * @date: 2023/6/14
 **/
public enum TransportDeliveryCompanyEnum {
    ZTO("zto", "中通");
    TransportDeliveryCompanyEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static Map<String, TransportDeliveryCompanyEnum> transportDeliveryCompanyEnumMap = Arrays.stream(TransportDeliveryCompanyEnum.values()).collect(Collectors.toMap(TransportDeliveryCompanyEnum :: getCode, transportDeliveryCompanyEnum -> transportDeliveryCompanyEnum, (k1, k2) -> k2));
}
