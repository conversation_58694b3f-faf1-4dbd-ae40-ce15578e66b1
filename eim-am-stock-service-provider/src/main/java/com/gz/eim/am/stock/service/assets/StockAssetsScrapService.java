package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.assets.StockAssetsScrapHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportBatchReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * @author: weijunjie
 * @date: 2021/3/24
 * @description
 */
public interface StockAssetsScrapService {

    /**
     * 资产报废保存页面录入数据到临时表
     *
     * @param inventoryAssetImportBatchReqDTO
     * @return
     */
    ResponseData manualAddAssets(InventoryAssetImportBatchReqDTO inventoryAssetImportBatchReqDTO);

    /**
     * excel导入报废资产信息
     *
     * @param file
     * @param batchCode
     * @return
     * @throws IOException
     */
    ResponseData excelAddAssets(MultipartFile file, String batchCode) throws IOException;

    /**
     * 查询临时表添加的报废资产
     *
     * @param inventoryAssetImportSearchReqDTO
     * @return
     */
    ResponseData queryTempAddAssets(InventoryAssetImportSearchReqDTO inventoryAssetImportSearchReqDTO);

    /**
     * 删除临时表数据
     *
     * @param lineId
     * @return
     */
    ResponseData deleteTempAssets(Integer lineId);

    /**
     * 更新报废原因
     *
     * @param lineId
     * @param scrapReason
     * @return
     */
    ResponseData updateTempAssets(Integer lineId, Integer scrapReason);

    /**
     * 资产报废单据创建
     *
     * @param stockAssetsScrapHeadReqDTO
     * @return
     * @throws ParseException
     */
    ResponseData createScrap(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException;

    /**
     * 报废单提交审批
     *
     * @param stockAssetsScrapHeadReqDTO
     * @return
     * @throws ParseException
     */
    ResponseData submitScrap(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException;

    /**
     * 分页查询报废单据
     *
     * @param stockAssetsScrapHeadReqDTO
     * @return
     * @throws ParseException
     */
    ResponseData queryScrap(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException;

    /**
     * 报废单详情查询
     *
     * @param stockAssetsScrapHeadReqDTO
     * @return
     * @throws ParseException
     */
    ResponseData queryScrapDetail(StockAssetsScrapHeadReqDTO stockAssetsScrapHeadReqDTO) throws ParseException;

    /**
     * 取消报废单据
     *
     * @param headId
     * @return
     */
    ResponseData cancelScrap(Long headId);

    /**
     * 报废审批回调
     *
     * @param bizId
     * @param status
     */
    void handleApproveResult(String bizId, Integer status);
}
