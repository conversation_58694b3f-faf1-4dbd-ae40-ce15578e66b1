package com.gz.eim.am.stock.service.ambase;

import com.gz.eim.am.stock.dto.request.ambase.CuxGetfadetailTblReqDTO;
import com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl;

import java.util.List;
import java.util.Map;

/**
   * @description: 资产残值操作Service
   * @author: <EMAIL>
   * @date: 2022/2/15
   */
public interface CuxGetfadetailTblService {

     /**
       * @description: 查询资产残值集合
       * @author: <EMAIL>
       * @date: 2022/2/15
       */
     List<CuxGetfadetailTbl> selectCuxGetfadetailTblList(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO);

     /**
      * @description: 查询资产残值
      * @author: <EMAIL>
      * @date: 2022/12/06
      */
     CuxGetfadetailTbl selectCuxGetfadetailTbl(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO);

     /**
      * @description: 查询资产残值
      * @author: <EMAIL>
      * @date: 2022/12/07
      */
     Map<String, CuxGetfadetailTbl> selectCuxGetfadetailTblMap(CuxGetfadetailTblReqDTO cuxGetfadetailTblReqDTO);
}
