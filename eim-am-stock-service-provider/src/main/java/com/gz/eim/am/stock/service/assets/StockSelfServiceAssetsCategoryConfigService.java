package com.gz.eim.am.stock.service.assets;

import com.fuu.eim.support.base.ResponseData;
import com.gz.eim.am.stock.dto.request.supplies.StockSelfServiceAssetsCategoryConfigReqDTO;

/**
 * @className: StockSelfServiceSuppliesConfigController
 * @description: 搜索资产类别和部门或职级配置信息查询
 * @author: <EMAIL>
 * @date: 2021/10/11
 **/

public interface StockSelfServiceAssetsCategoryConfigService {
    /**
     * @param: suppliesReqDTO
     * @description: 搜索资产类别，资产领用使用
     * @return:
     * @author: <EMAIL>
     * @date: 2021/10/9
     */
    ResponseData selectAssetsCategoryByAssetsReceive(StockSelfServiceAssetsCategoryConfigReqDTO stockSelfServiceAssetsCategoryConfigReqDTO) throws Exception;
}
