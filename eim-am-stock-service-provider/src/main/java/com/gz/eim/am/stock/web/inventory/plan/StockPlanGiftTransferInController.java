package com.gz.eim.am.stock.web.inventory.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.api.inventory.plan.StockPlanGiftTransferInApi;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO;
import com.gz.eim.am.stock.service.inventory.plan.StockPlanGiftTransferInService;

import com.gz.eim.am.stock.util.em.DeliveryPlanHeadEnum;
import com.gz.eim.am.stock.util.em.InventoryInPlanHeadEnum;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: lishuyang
 * @date: 2019/12/13
 * @description: 礼品调拨入库单
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/planGiftTransferIn")
public class StockPlanGiftTransferInController implements StockPlanGiftTransferInApi {

    @Autowired
    private StockPlanGiftTransferInService stockPlanGiftTransferInService;

    @Override
    public ResponseData selectCardOrGiftPurchasePlanInventoryIn(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO) {
        log.info("/api/am/stock/planGiftTransferIn/select {}", inventoryInPlanSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            inventoryInPlanSearchReqDTO.setInventoryInPlanType(InventoryInPlanHeadEnum.InType.CARD_PURCHASE.getCode());
            res = this.stockPlanGiftTransferInService.selectCardOrGiftPurchasePlanInventoryIn(inventoryInPlanSearchReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("京东卡/油卡采购计划入库单分页查询出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectCardOrGiftPurchasePlanInventoryInById(Long inventoryInPlanHeadId) {
        log.info("/api/am/stock/planGiftTransferIn/select {}", inventoryInPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanGiftTransferInService.selectCardOrGiftPurchasePlanInventoryInById(inventoryInPlanHeadId, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("京东卡/油卡采购计划入库单分页查询出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }


    @Override
    public ResponseData selectPlanGiftTransferIn(InventoryInPlanSearchReqDTO inventoryInPlanSearchReqDTO) {
        log.info("/api/am/stock/planGiftTransferIn/search {}", inventoryInPlanSearchReqDTO.toString());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            List<Integer> inventoryInPlanTypeList = Stream.of(InventoryInPlanHeadEnum.InType.TRANSFER.getCode(),InventoryInPlanHeadEnum.InType.CARD_TRANSFER.getCode()).collect(Collectors.toList());
            inventoryInPlanSearchReqDTO.setInventoryInPlanTypeList(inventoryInPlanTypeList);
            res = this.stockPlanGiftTransferInService.selectPlanGiftTransferIn (inventoryInPlanSearchReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划调拨入库单分页查询出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanGiftTransferInById(Long inventoryInPlanHeadId) {
        log.info("/api/am/stock/planGiftTransferIn/search/{}", inventoryInPlanHeadId);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanGiftTransferInService.selectPlanGiftTransferInById (inventoryInPlanHeadId, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划调拨入库单详情查询出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData planGiftTransferInInBound(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info("/api/am/stock/planGiftTransferIn/inBound {}", inventoryInPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanGiftTransferInService.planGiftTransferInInBound (inventoryInPlanHeadReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划调拨入库单入库出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }

    @Override
    public ResponseData planGiftTransferInReject(InventoryInPlanHeadReqDTO inventoryInPlanHeadReqDTO) {
        log.info("/api/am/stock/planGiftTransferIn/reject {}", inventoryInPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser();
            log.info("---------------------login-user--{}---------------------",user.getEmployeeCode());
            res = this.stockPlanGiftTransferInService.planGiftTransferInReject (inventoryInPlanHeadReqDTO, user);
        }catch (ServiceUncheckedException e){
            res = ResponseData.createFailResult(e.getMessage());
        } catch(Exception e){
            log.error("计划调拨入库单驳回出错", e);
            res = ResponseData.createFailResult(ResponseCode.SYSTEM_ERROR.getMessage());
        }
        return res;
    }
}
