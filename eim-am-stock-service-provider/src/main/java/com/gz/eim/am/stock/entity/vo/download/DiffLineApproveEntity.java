package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;


/**
 * @author: weijunjie
 * @date: 2021/2/24
 * @description 盘点调整数据
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class DiffLineApproveEntity implements ExportModel {
    @ExportField(name = "资产编码")
    private String snapshotAssetsCode;
    @ExportField(name = "资产名称")
    private String snapshotAssetsName;
    @ExportField(name = "账目状态")
    private String snapshotAssetsStatusName;
    @ExportField(name = "账目资产使用情况")
    private String snapshotAssetsConditionsName;
    @ExportField(name = "账目使用人")
    private String snapshotAssetsHolderName;
    @ExportField(name = "账目仓库信息")
    private String snapshotWarehouseName;
    @ExportField(name = "实际使用人")
    private String realAssetsHolderName;
    @ExportField(name = "实际状态")
    private String realAssetsStatusName;
    @ExportField(name = "实际资产使用情况")
    private String realAssetsConditionsName;
    @ExportField(name = "实际所在仓库")
    private String realWarehouseName;
    @ExportField(name = "账目数目")
    private Integer snapshotNumber;
    @ExportField(name = "实际数目")
    private Integer realNumber;
    @ExportField(name = "系统建议处理方式")
    private String adviseHandleMethodName;
    @ExportField(name = "实际处理方式")
    private String actualHandleMethodName;
    @ExportField(name = "备注")
    private String remark;

    @Override
    public String getSheetName() {
        return "调整数据明细清单";
    }

    public String getSnapshotAssetsCode() {
        return snapshotAssetsCode;
    }

    public void setSnapshotAssetsCode(String snapshotAssetsCode) {
        this.snapshotAssetsCode = snapshotAssetsCode;
    }

    public String getSnapshotAssetsName() {
        return snapshotAssetsName;
    }

    public void setSnapshotAssetsName(String snapshotAssetsName) {
        this.snapshotAssetsName = snapshotAssetsName;
    }

    public String getSnapshotAssetsStatusName() {
        return snapshotAssetsStatusName;
    }

    public void setSnapshotAssetsStatusName(String snapshotAssetsStatusName) {
        this.snapshotAssetsStatusName = snapshotAssetsStatusName;
    }

    public String getSnapshotAssetsConditionsName() {
        return snapshotAssetsConditionsName;
    }

    public void setSnapshotAssetsConditionsName(String snapshotAssetsConditionsName) {
        this.snapshotAssetsConditionsName = snapshotAssetsConditionsName;
    }

    public String getSnapshotAssetsHolderName() {
        return snapshotAssetsHolderName;
    }

    public void setSnapshotAssetsHolderName(String snapshotAssetsHolderName) {
        this.snapshotAssetsHolderName = snapshotAssetsHolderName;
    }

    public String getSnapshotWarehouseName() {
        return snapshotWarehouseName;
    }

    public void setSnapshotWarehouseName(String snapshotWarehouseName) {
        this.snapshotWarehouseName = snapshotWarehouseName;
    }

    public String getRealAssetsHolderName() {
        return realAssetsHolderName;
    }

    public void setRealAssetsHolderName(String realAssetsHolderName) {
        this.realAssetsHolderName = realAssetsHolderName;
    }

    public String getRealAssetsStatusName() {
        return realAssetsStatusName;
    }

    public void setRealAssetsStatusName(String realAssetsStatusName) {
        this.realAssetsStatusName = realAssetsStatusName;
    }

    public String getRealAssetsConditionsName() {
        return realAssetsConditionsName;
    }

    public void setRealAssetsConditionsName(String realAssetsConditionsName) {
        this.realAssetsConditionsName = realAssetsConditionsName;
    }

    public String getActualHandleMethodName() {
        return actualHandleMethodName;
    }

    public void setActualHandleMethodName(String actualHandleMethodName) {
        this.actualHandleMethodName = actualHandleMethodName;
    }

    public String getRealWarehouseName() {
        return realWarehouseName;
    }

    public void setRealWarehouseName(String realWarehouseName) {
        this.realWarehouseName = realWarehouseName;
    }

    public Integer getSnapshotNumber() {
        return snapshotNumber;
    }

    public void setSnapshotNumber(Integer snapshotNumber) {
        this.snapshotNumber = snapshotNumber;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public String getAdviseHandleMethodName() {
        return adviseHandleMethodName;
    }

    public void setAdviseHandleMethodName(String adviseHandleMethodName) {
        this.adviseHandleMethodName = adviseHandleMethodName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
