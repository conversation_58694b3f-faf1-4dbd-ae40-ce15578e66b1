package com.gz.eim.am.stock.web.order.plan;

import com.fuu.eim.support.base.ResponseCode;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.exception.ServiceUncheckedException;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.RedisUtil;
import com.fuu.eim.support.util.SecurityUtil;
import com.gz.eim.am.stock.annotation.DocTypeAnnotation;
import com.gz.eim.am.stock.api.order.plan.StockPlanAssetsDisposeApi;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.DocTypeConstant;
import com.gz.eim.am.stock.constant.RedisKeyConstants;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanHeadReqDTO;
import com.gz.eim.am.stock.dto.request.order.plan.DeliveryPlanSearchReqDTO;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsDisposeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * @author: lishuyang
 * @date: 2020/5/6
 * @description: 资产计划处置单Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/am/stock/plan-assets-dispose")
public class StockPlanAssetsDisposeController implements StockPlanAssetsDisposeApi {
    @Value("${namespace.name}")
    private String nameSpace;
    @Autowired
    private StockPlanAssetsDisposeService stockPlanAssetsDisposeService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    @DocTypeAnnotation(DocTypeConstant.DISPOSE_PLAN_OUT)
    public ResponseData savePlanAssetsDispose(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info ("/api/am/stock/plan-assets-dispose/save {}", deliveryPlanHeadReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsDisposeService.savePlanAssetsDispose (deliveryPlanHeadReqDTO, user);
        } catch (ServiceUncheckedException e) {
            log.info ("计划资产处置单保存返回错误: {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("计划资产处置单保存报错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.DISPOSE_PLAN_OUT)
    public ResponseData selectPlanAssetsDispose(DeliveryPlanSearchReqDTO deliveryPlanSearchReqDTO) {
        log.info ("/api/am/stock/plan-assets-dispose/search {}", deliveryPlanSearchReqDTO.toString ());
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsDisposeService.selectPlanAssetsDispose (deliveryPlanSearchReqDTO, user);
        } catch (ServiceUncheckedException e) {
            log.info ("计划资产处置单分页查询返回错误: {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("计划资产处置单分页查询报错", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    public ResponseData selectPlanAssetsDisposeByNo(String deliveryPlanNo, Integer operationType) {
        log.info ("/api/am/stock/plan-assets-dispose/search/detail, deliveryPlanNo={}, operationType={} ", deliveryPlanNo, operationType);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsDisposeService.selectPlanAssetsDisposeByNo (deliveryPlanNo, operationType, user);
        } catch (ServiceUncheckedException e) {
            log.info ("计划资产处置单详情查询错误: {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("计划资产处置单详情查询", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }


    @Override
    @DocTypeAnnotation(DocTypeConstant.DISPOSE_PLAN_OUT)
    public ResponseData planAssetsDisposeBound(DeliveryPlanHeadReqDTO deliveryPlanHeadReqDTO) {
        log.info ("/api/am/stock/plan-assets-dispose/outBound {}", deliveryPlanHeadReqDTO);
        ResponseData res = null;
        String lockKey = RedisKeyConstants.PLAN_ASSET_DISPOSE_BOUND + deliveryPlanHeadReqDTO.getDeliveryPlanNo ();
        try {
            if (redisUtil.setNx (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_NAME)) {
                JwtUser user = SecurityUtil.getJwtUser ();
                res = this.stockPlanAssetsDisposeService.planAssetsDisposeBound (deliveryPlanHeadReqDTO, user);
                redisUtil.expire (nameSpace, lockKey, CommonConstant.DEFAULT_LOCK_SECOND, TimeUnit.SECONDS);
            } else {
                res = ResponseData.createFailResult ("系统繁忙,请稍后尝试...");
            }
        } catch (ServiceUncheckedException e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info ("计划资产处置单出库错误: {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (RuntimeException e){
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.info ("计划资产处置单出库错误: {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            redisUtil.deleteByKey (nameSpace, lockKey);
            log.error ("计划资产处置单出库查询", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }

    @Override
    @DocTypeAnnotation(DocTypeConstant.DISPOSE_PLAN_OUT)
    public ResponseData cancelPlanAssetsDisposeByNo(String deliveryPlanNo) {
        log.info ("/api/am/stock/plan-assets-dispose/cancel, deliveryPlanNo={}, operationType={} ", deliveryPlanNo);
        ResponseData res = null;
        try {
            JwtUser user = SecurityUtil.getJwtUser ();
            res = this.stockPlanAssetsDisposeService.cancelPlanAssetsDisposeByNo (deliveryPlanNo);
        } catch (ServiceUncheckedException e) {
            log.info ("计划资产处置单取消错误: {}", e.getMessage ());
            res = ResponseData.createFailResult (e.getMessage ());
        } catch (Exception e) {
            log.error ("计划资产处置单取消错误", e);
            res = ResponseData.createFailResult (ResponseCode.SYSTEM_ERROR.getMessage ());
        }
        return res;
    }
}
