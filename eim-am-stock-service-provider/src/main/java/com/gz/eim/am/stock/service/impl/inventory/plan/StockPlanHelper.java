package com.gz.eim.am.stock.service.impl.inventory.plan;

import com.alibaba.fastjson.JSON;
import com.fuu.eim.support.activity.MessageUtil;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.activity.ActivityRespDTO;
import com.fuu.eim.support.jwt.JwtUser;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.prm.api.external.stock.PurchaseReceiptExternalApi;
import com.gz.eim.am.prm.dto.request.prm.stock.StockPurchaseReceiptItemsReqDTO;
import com.gz.eim.am.prm.dto.request.prm.stock.StockPurchaseReceiptReqDTO;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.PropertyConstants;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.server.prm.PurchaseReceiptApi;
import com.gz.eim.am.stock.service.inventory.plan.StockInventoryInPlanHeadService;
import com.gz.eim.am.stock.service.inventory.plan.StockInventoryInPlanLineService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanHeadService;
import com.gz.eim.am.stock.service.order.plan.StockDeliveryPlanLineService;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: wangjing67
 * @Date: 3/8/21 7:59 下午
 * @description
 */
@Slf4j
@Component
public class StockPlanHelper {

    @Value("${project.wfl.systemId}")
    private String systemId;

    @Value("${project.wfl.checkCode}")
    private String checkCode;

    @Autowired
    private PurchaseReceiptExternalApi purchaseReceiptExternalApi;

    @Resource
    private PurchaseReceiptApi purchaseReceiptApi;

    @Lazy
    @Autowired
    private StockInventoryInPlanHeadService stockInventoryInPlanHeadService;

    @Lazy
    @Autowired
    private StockInventoryInPlanLineService stockInventoryInPlanLineService;

    @Lazy
    @Autowired
    private StockDeliveryPlanHeadService stockDeliveryPlanHeadService;

    @Lazy
    @Autowired
    private StockDeliveryPlanLineService stockDeliveryPlanLineService;


    @Autowired
    MessageUtil messageUtil;


    /**
     * 入库对接采购 更新采购入库验收单状态
     *
     * @param inventoryInPlanHeadId
     * @param user
     */
    public void submitPurchaseInventoryInPlan(Long inventoryInPlanHeadId, JwtUser user) {
        List<StockPurchaseReceiptReqDTO> stockPurchaseReceiptReqDTOList = new ArrayList<>();
        //查询计划单头信息
        StockInventoryInPlanHead stockInventoryInPlanHead = stockInventoryInPlanHeadService.selectById(inventoryInPlanHeadId);
        if (Objects.nonNull(stockInventoryInPlanHead)){
            // 来源租户判断
            String fromSystem = stockInventoryInPlanHead.getSystemCode();
            Set<String> validSystems = new HashSet<>(Arrays.asList(PropertyConstants.SYSTEM_CODE, PropertyConstants.TENANT_GP, PropertyConstants.TENANT_MP));
            if (StringUtils.isNotBlank(fromSystem) && validSystems.contains(fromSystem)){
                List<StockInventoryInPlanLine> stockInventoryInPlanLineList = stockInventoryInPlanLineService.selectLinesByHeadId(inventoryInPlanHeadId);

                StockPurchaseReceiptReqDTO stockPurchaseReceiptReqDTO = new StockPurchaseReceiptReqDTO();
                stockPurchaseReceiptReqDTO.setReceiptNo(stockInventoryInPlanHead.getBizNo());
                stockPurchaseReceiptReqDTO.setUpdatedBy(user.getEmployeeCode());
                List<StockPurchaseReceiptItemsReqDTO> stockPurchaseReceiptItemsReqDTOList = new ArrayList<>();
                for (StockInventoryInPlanLine stockInventoryInPlanLine : stockInventoryInPlanLineList) {
                    // 过滤调待入库的单据 不用 对接采购系统
                    if (stockInventoryInPlanLine.getStatus().equals(InventoryInPlanLineEnum.Status.WAIT_IN.getStatus())) {
                        continue;
                    }else{
                        StockPurchaseReceiptItemsReqDTO stockPurchaseReceiptItemsReqDTO = new StockPurchaseReceiptItemsReqDTO();
                        stockPurchaseReceiptItemsReqDTO.setLineStatus(stockInventoryInPlanLine.getStatus());
                        stockPurchaseReceiptItemsReqDTO.setReceiptItemNo(stockInventoryInPlanLine.getReceiveItemNo());
                        stockPurchaseReceiptItemsReqDTO.setUpdatedBy(user.getEmployeeCode());
                        stockPurchaseReceiptItemsReqDTOList.add(stockPurchaseReceiptItemsReqDTO);
                    }
                }
                stockPurchaseReceiptReqDTO.setStockPurchaseReceiptItemsReqDTOList(stockPurchaseReceiptItemsReqDTOList);
                stockPurchaseReceiptReqDTOList.add(stockPurchaseReceiptReqDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(stockPurchaseReceiptReqDTOList)) {
            log.info("计划入库单入库时对接采购同步更新状态请求参数{}" + JSON.toJSONString(stockPurchaseReceiptReqDTOList));
            try {
                // 新增systemId
                String tid = StringUtils.isNotBlank(stockInventoryInPlanHead.getSystemCode()) ? stockInventoryInPlanHead.getSystemCode() : StringUtils.EMPTY;
                log.info("tid: {}",tid);
                com.gz.eim.am.prm.dto.ResponseData responseData = purchaseReceiptApi.updateReceiptStatus(stockPurchaseReceiptReqDTOList,tid);
                if (!responseData.isSuccess()) {
                    //不成功时需发邮件
                    sendEmail(PropertyConstants.ERROR, CommonConstant.RECEIVE_USER,"计划入库单入库",inventoryInPlanHeadId,responseData.getMessage());
                }
            } catch (Throwable throwable) {
                log.info("调用采购系统同步更新状态失败{}", throwable);
                // 异常时也发送邮件
                sendEmail(PropertyConstants.ERROR,CommonConstant.RECEIVE_USER,"计划入库单入库",inventoryInPlanHeadId,throwable.getMessage());
                throwable.printStackTrace();
            }
        }
    }


    /**
     * 退货出库 对接采购退货单 更新采购退货单据状态
     *
     * @param deliveryPlanHeadId
     * @param user
     */
    public void submitPurchaseDeliveryPlan(Long deliveryPlanHeadId, JwtUser user) {
        List<StockPurchaseReceiptReqDTO> stockPurchaseReceiptReqDTOList = new ArrayList<>();
        //查询计划单头信息
        StockDeliveryPlanHead stockDeliveryPlanHead = stockDeliveryPlanHeadService.selectDeliveryPlanById(deliveryPlanHeadId);
        if (Objects.nonNull(stockDeliveryPlanHead)){
            // 租户判断
            String fromSystem = stockDeliveryPlanHead.getFromSystem();
            Set<String> validSystems = new HashSet<>(Arrays.asList(PropertyConstants.SYSTEM_CODE, PropertyConstants.TENANT_GP, PropertyConstants.TENANT_MP));
            if (StringUtils.isNotBlank(fromSystem) && validSystems.contains(fromSystem)){
                List<StockDeliveryPlanLine> stockDeliveryPlanLineList = stockDeliveryPlanLineService.selectByDeliveryPlanHeadId(deliveryPlanHeadId);

                StockPurchaseReceiptReqDTO stockPurchaseReceiptReqDTO = new StockPurchaseReceiptReqDTO();
                stockPurchaseReceiptReqDTO.setReceiptNo(stockDeliveryPlanHead.getBizNo());
                stockPurchaseReceiptReqDTO.setUpdatedBy(user.getEmployeeCode());
                List<StockPurchaseReceiptItemsReqDTO> stockPurchaseReceiptItemsReqDTOList = new ArrayList<>();
                for (StockDeliveryPlanLine stockDeliveryPlanLine : stockDeliveryPlanLineList) {
                    // 过滤出待出库的单子 不用对接采购
                    if (DeliveryPlanLineEnum.Status.WAIT_OUT.equals(stockDeliveryPlanLine.getStatus())) {
                        continue;
                    }else{
                        StockPurchaseReceiptItemsReqDTO stockPurchaseReceiptItemsReqDTO = new StockPurchaseReceiptItemsReqDTO();
                        stockPurchaseReceiptItemsReqDTO.setLineStatus(stockDeliveryPlanLine.getStatus());
                        stockPurchaseReceiptItemsReqDTO.setReceiptItemNo(stockDeliveryPlanLine.getReceiveItemNo());
                        stockPurchaseReceiptItemsReqDTO.setUpdatedBy(user.getEmployeeCode());
                        stockPurchaseReceiptItemsReqDTOList.add(stockPurchaseReceiptItemsReqDTO);
                    }
                }
                stockPurchaseReceiptReqDTO.setStockPurchaseReceiptItemsReqDTOList(stockPurchaseReceiptItemsReqDTOList);
                stockPurchaseReceiptReqDTOList.add(stockPurchaseReceiptReqDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(stockPurchaseReceiptReqDTOList)) {
            log.info("固定资产退货出库时对接采购同步更新状态请求参数{}" + JSON.toJSONString(stockPurchaseReceiptReqDTOList));
            try {
                String tid = StringUtils.isNotBlank(stockDeliveryPlanHead.getFromSystem()) ? stockDeliveryPlanHead.getFromSystem() : StringUtils.EMPTY;
                log.info("tid: {}",tid);
                com.gz.eim.am.prm.dto.ResponseData responseData = purchaseReceiptApi.updateReceiptStatus(stockPurchaseReceiptReqDTOList,tid);
                if (!responseData.isSuccess()) {
                    //不成功时需发邮件
                    sendEmail(PropertyConstants.ERROR,CommonConstant.RECEIVE_USER,"计划出库单出库",deliveryPlanHeadId,responseData.getMessage());
                }
            } catch (Throwable throwable) {
                log.info("调用采购系统同步更新状态失败{}", throwable);
                //异常时需发邮件
                sendEmail(PropertyConstants.ERROR,CommonConstant.RECEIVE_USER,"计划出库单出库",deliveryPlanHeadId,throwable.getMessage());
                throwable.printStackTrace();
            }
        }
    }


    /**
     * 同步状态更新异常时发送邮件
     * @param taskCode
     * @param users
     * @param headId
     * @param title
     * @param errorMsg
     * @return
     */
    public ResponseData sendEmail(String taskCode, String users, String title, Long headId, String errorMsg) {
        Map<String, Object> param = new HashMap<>(4);
        try {
            List<Map<String, String>> list = new ArrayList<>();
            //邮件接收人
            Map<String, String> emailMap = new HashMap<>(2);
            emailMap.put("key", "recipients");
            emailMap.put("value", users);
            list.add(emailMap);
            //单据编号
            Map<String, String> map = new HashMap<>(2);
            map.put("key","bizId");
            map.put("value",String.valueOf(headId));
            list.add(map);
            //异常原因
            Map<String, String> map1 = new HashMap<>(2);
            map1.put("key","errorMsg");
            map1.put("value",errorMsg);
            list.add(map1);

            //操作单据类型
            Map<String, String> map2 = new HashMap<>(2);
            map2.put("key","title");
            map2.put("value",title);
            list.add(map2);

            param.put("SystemId", systemId);
            param.put("CheckCode", checkCode);
            param.put("TaskCode", taskCode);
            param.put("TaskRequestJson", JsonUtil.getJsonString(list));

            log.info("send email params:{}", param);
            ActivityRespDTO respDTO = messageUtil.addTaskRequest(param);

            if (!AllocateImportLineEnum.Status.SUCCESS.getCode().equals(respDTO.getStatus())) {
                log.info("发送邮件失败：{}", respDTO);
                ResponseData responseData = ResponseData.createFailResult(respDTO.getMessage());
                responseData.setData(respDTO.getContent());
                return responseData;
            } else {
                log.info("发送邮件成功：{}", respDTO);
                ResponseData responseData = ResponseData.createSuccessResult();
                responseData.setMessage(respDTO.getMessage());
                responseData.setData(respDTO.getContent());
                return responseData;
            }
        } catch (Exception e) {
            log.error("发送邮件异常：" + param, e);
            return ResponseData.createFailResult("发送邮件异常：" + e.getMessage());
        }
    }
}
