package com.gz.eim.am.stock.entity.vo.download;

import com.fuu.eim.tool.excel.ExportModel;
import com.fuu.eim.tool.excel.annotation.ExportField;
import com.fuu.eim.tool.excel.annotation.ImportModel;
import java.util.LinkedHashMap;

/**
 * @author: weijunjie
 * @date: 2020/11/24
 * @description
 */
@ImportModel(headerIndex = 0, ignoreRows = 1)
public class CheckDifferenceListLineEntity implements ExportModel {
    @ExportField(name = "资产编码")
    private String snapshotAssetsCode;
    @ExportField(name = "资产名称")
    private String snapshotAssetsName;
    @ExportField(name = "账目状态")
    private String snapshotAssetsStatusName;
    @ExportField(name = "账目资产使用情况")
    private String snapshotAssetsConditionsName;
    @ExportField(name = "账目使用人")
    private String snapshotAssetsHolderName;
    @ExportField(name = "账目使用人部门")
    private String snapshotAssetsHolderDept;
    @ExportField(name = "账目使用人位置")
    private String snapshotAssetsHolderAddress;
    @ExportField(name = "账目仓库信息")
    private String snapshotWarehouseName;
    @ExportField(name = "实际使用人")
    private String realAssetsHolderName;
    @ExportField(name = "实际使用人部门")
    private String realAssetsHolderDept;
    @ExportField(name = "实际使用人位置")
    private String realAssetsHolderAddress;
    @ExportField(name = "实际状态")
    private String realAssetsStatusName;
    @ExportField(name = "实际资产使用情况")
    private String realAssetsConditionsName;
    @ExportField(name = "实际所在仓库")
    private String realWarehouseName;
    @ExportField(name = "资产图片")
    private String realPicturesUrl;
    @ExportField(name = "账目数量")
    private Integer snapshotNumber;
    @ExportField(name = "实际数量")
    private Integer realNumber;
    @ExportField(name = "是否异常")
    private String differenceName;
    @ExportField(name = "异常信息")
    private String errMessage;
    @ExportField(name = "盘点结果")
    private String checkResult;
    @ExportField(name = "盘点日期")
    private String checkDate;
    @ExportField(name = "备注")
    private String remark;

    @Override
    public String getSheetName() {
        return "盘点差异明细信息";
    }

    public String getSnapshotAssetsCode() {
        return snapshotAssetsCode;
    }

    public void setSnapshotAssetsCode(String snapshotAssetsCode) {
        this.snapshotAssetsCode = snapshotAssetsCode;
    }

    public String getSnapshotAssetsName() {
        return snapshotAssetsName;
    }

    public void setSnapshotAssetsName(String snapshotAssetsName) {
        this.snapshotAssetsName = snapshotAssetsName;
    }

    public String getSnapshotAssetsStatusName() {
        return snapshotAssetsStatusName;
    }

    public void setSnapshotAssetsStatusName(String snapshotAssetsStatusName) {
        this.snapshotAssetsStatusName = snapshotAssetsStatusName;
    }

    public String getSnapshotAssetsConditionsName() {
        return snapshotAssetsConditionsName;
    }

    public void setSnapshotAssetsConditionsName(String snapshotAssetsConditionsName) {
        this.snapshotAssetsConditionsName = snapshotAssetsConditionsName;
    }

    public String getSnapshotAssetsHolderName() {
        return snapshotAssetsHolderName;
    }

    public void setSnapshotAssetsHolderName(String snapshotAssetsHolderName) {
        this.snapshotAssetsHolderName = snapshotAssetsHolderName;
    }

    public String getSnapshotWarehouseName() {
        return snapshotWarehouseName;
    }

    public void setSnapshotWarehouseName(String snapshotWarehouseName) {
        this.snapshotWarehouseName = snapshotWarehouseName;
    }

    public String getRealAssetsHolderName() {
        return realAssetsHolderName;
    }

    public void setRealAssetsHolderName(String realAssetsHolderName) {
        this.realAssetsHolderName = realAssetsHolderName;
    }

    public String getRealAssetsStatusName() {
        return realAssetsStatusName;
    }

    public void setRealAssetsStatusName(String realAssetsStatusName) {
        this.realAssetsStatusName = realAssetsStatusName;
    }

    public String getRealAssetsConditionsName() {
        return realAssetsConditionsName;
    }

    public void setRealAssetsConditionsName(String realAssetsConditionsName) {
        this.realAssetsConditionsName = realAssetsConditionsName;
    }

    public String getRealWarehouseName() {
        return realWarehouseName;
    }

    public void setRealWarehouseName(String realWarehouseName) {
        this.realWarehouseName = realWarehouseName;
    }

    public String getRealPicturesUrl() {
        return realPicturesUrl;
    }

    public void setRealPicturesUrl(String realPicturesUrl) {
        this.realPicturesUrl = realPicturesUrl;
    }

    public Integer getSnapshotNumber() {
        return snapshotNumber;
    }

    public void setSnapshotNumber(Integer snapshotNumber) {
        this.snapshotNumber = snapshotNumber;
    }

    public Integer getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Integer realNumber) {
        this.realNumber = realNumber;
    }

    public String getDifferenceName() {
        return differenceName;
    }

    public void setDifferenceName(String differenceName) {
        this.differenceName = differenceName;
    }

    public String getErrMessage() {
        return errMessage;
    }

    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSnapshotAssetsHolderDept() {
        return snapshotAssetsHolderDept;
    }

    public void setSnapshotAssetsHolderDept(String snapshotAssetsHolderDept) {
        this.snapshotAssetsHolderDept = snapshotAssetsHolderDept;
    }

    public String getSnapshotAssetsHolderAddress() {
        return snapshotAssetsHolderAddress;
    }

    public void setSnapshotAssetsHolderAddress(String snapshotAssetsHolderAddress) {
        this.snapshotAssetsHolderAddress = snapshotAssetsHolderAddress;
    }

    public String getRealAssetsHolderDept() {
        return realAssetsHolderDept;
    }

    public void setRealAssetsHolderDept(String realAssetsHolderDept) {
        this.realAssetsHolderDept = realAssetsHolderDept;
    }

    public String getRealAssetsHolderAddress() {
        return realAssetsHolderAddress;
    }

    public void setRealAssetsHolderAddress(String realAssetsHolderAddress) {
        this.realAssetsHolderAddress = realAssetsHolderAddress;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }
}
