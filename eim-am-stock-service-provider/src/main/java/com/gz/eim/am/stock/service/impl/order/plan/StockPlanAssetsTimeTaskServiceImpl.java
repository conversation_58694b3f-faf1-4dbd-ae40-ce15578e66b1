package com.gz.eim.am.stock.service.impl.order.plan;

import com.fuu.eim.support.activity.MessageUtil;
import com.fuu.eim.support.base.ResponseData;
import com.fuu.eim.support.base.activity.ActivityRespDTO;
import com.fuu.eim.support.util.DateUtils;
import com.fuu.eim.support.util.JsonUtil;
import com.gz.eim.am.stock.constant.CommonConstant;
import com.gz.eim.am.stock.constant.PropertyConstants;
import com.gz.eim.am.stock.dao.assets.AssetsMapper;
import com.gz.eim.am.stock.entity.*;
import com.gz.eim.am.stock.entity.ambase.SysUser;
import com.gz.eim.am.stock.service.ambase.AmbaseCommonService;
import com.gz.eim.am.stock.service.order.plan.StockPlanAssetsTimeTaskService;
import com.gz.eim.am.stock.util.common.DateTimeUtil;
import com.gz.eim.am.stock.util.em.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangjing67
 * @Date: 6/21/21 3:37 下午
 * @description
 */
@Slf4j
@Service
public class StockPlanAssetsTimeTaskServiceImpl implements StockPlanAssetsTimeTaskService {


    @Value("${project.wfl.systemId}")
    private String systemId;

    @Value("${project.wfl.checkCode}")
    private String checkCode;

//    /**
//     * 员工续借跳转页面地址
//     */
//    @Value("${project.licenseEmployeeUrl.renew}")
//    private String employeeRenewUrl;
//
//    /**
//     * 员工归还跳转页面地址
//     */
//    @Value("${project.licenseEmployeeUrl.return}")
//    private String employeeReturnUrl;
//
//    /**
//     * 管理员归还跳转页面地址
//     */
//    @Value("${project.licenseAdminUrl.return}")
//    private String adminReturnUrl;

    @Autowired
    private MessageUtil messageUtil;

    @Autowired
    private AmbaseCommonService ambaseCommonService;

    @Autowired
    private AssetsMapper assetsMapper;

    public static void main(String[] args) {
        Date beginDate = DateTimeUtil.getDayStart(new Date());
        Date queryDate = DateUtils.dateAddDays(beginDate, -3);
        Long days = DateTimeUtil.getDay(queryDate, beginDate);
        System.out.println(days);
    }
    @Override
    public ResponseData mtPushSendMessage() throws Exception {
        //获取当前时间的开始时间
        Date beginDate = DateTimeUtil.getDayStart(new Date());
        Date queryDate = DateUtils.dateAddDays(beginDate, -3);
        //1。查询使用中的固定资产信息
        List<StockAssets> stockAssetsList = assetsMapper.selectOverReturn(AssetsEnum.statusType.USED.getValue(), Arrays.asList(WarehouseEnum.Type.SOLID_CAPITAL.getType()),queryDate);
        //筛选出逾期需要发消息的资产信息
        List<StockAssets> needHandelAssets = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stockAssetsList)) {
            for (StockAssets stockAssets : stockAssetsList) {
                Date planReturnTime = stockAssets.getPlanReturnTime();
                if (null != planReturnTime) {
                    Long days = DateTimeUtil.getDay(beginDate, planReturnTime);
                    //逾期归还前10天 即 当前时间 - 预计规划时间 = 10
                    if (CommonConstant.DEFAULT_LIMIT.intValue() == days.intValue()) {
                        needHandelAssets.add(stockAssets);
                    }
                    //归还日期当天 即 当前时间等于预计归还时间
                    if (CommonConstant.NUMBER_ZERO.intValue() == days.intValue()) {
                        needHandelAssets.add(stockAssets);
                    }
                    //已逾期 即 当前时间大于预计归还时间
                    if (days.intValue() > CommonConstant.NUMBER_ZERO.intValue()) {
                        needHandelAssets.add(stockAssets);
                    }
                }
            }
        }

        //根据资产持有人分组
        Map<String, List<StockAssets>> stringStockAssetsMap = new HashMap<>();
        //获取所有的资产持有人
        List<String> holderList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(needHandelAssets)) {
            //根据资产持有人分组
            for (StockAssets stockAssets : needHandelAssets) {
                if (!holderList.contains(stockAssets.getHolder())) {
                    holderList.add(stockAssets.getHolder());
                }
                if (!stringStockAssetsMap.containsKey(stockAssets.getHolder())) {
                    stringStockAssetsMap.put(stockAssets.getHolder(), new ArrayList());
                }
                stringStockAssetsMap.get(stockAssets.getHolder()).add(stockAssets);
            }
        }

        //根据资产管理员分组
        Map<String, List<StockAssets>> stockAssetsMap = new HashMap<>();
        //获取所有的资产管理员
        List<String> assetsKeeperList = new ArrayList<>();

        for (StockAssets stockAssets : needHandelAssets) {
            if (StringUtils.isNotBlank(stockAssets.getAssetsKeeper())) {
                if (!assetsKeeperList.contains(stockAssets.getAssetsKeeper())) {
                    assetsKeeperList.add(stockAssets.getAssetsKeeper());
                }
                if (!stockAssetsMap.containsKey(stockAssets.getAssetsKeeper())) {
                    stockAssetsMap.put(stockAssets.getAssetsKeeper(), new ArrayList());
                }
                stockAssetsMap.get(stockAssets.getAssetsKeeper()).add(stockAssets);
            }
        }

        //查询sys_user人员信息
        assetsKeeperList.addAll(holderList);
        Map<String,SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(assetsKeeperList);

        //资产管理员发送消息
        if (CollectionUtils.isNotEmpty(assetsKeeperList)) {
            for (String assetsKeeper : assetsKeeperList) {
                SysUser sysUser = stringSysUserMap.get(assetsKeeper);
                if(null != sysUser && AssetTransferEnum.EMP_JOB.JOB.getCode().equals(sysUser.getStatus())){
                    List<StockAssets> dtoList = stringStockAssetsMap.get(assetsKeeper);
                    if (CollectionUtils.isNotEmpty(dtoList)) {
                        adminSendMessage(assetsKeeper, dtoList);
                    }
                }
            }
        }

        //资产持有人发送消息
        if (CollectionUtils.isNotEmpty(holderList)) {
            for (String holder : holderList) {
                SysUser sysUser = stringSysUserMap.get(holder);
                if(null != sysUser && AssetTransferEnum.EMP_JOB.JOB.getCode().equals(sysUser.getStatus())){
                    List<StockAssets> dtoList = stringStockAssetsMap.get(holder);
                    if (CollectionUtils.isNotEmpty(dtoList)) {
                        employeeSendMessage(holder, dtoList);
                    }
                }
            }
        }

        //返回结果
        return ResponseData.createSuccessResult();
    }

    /**
     * 拼接消息内容
     *
     * @param assetsDTOList
     * @throws Exception
     */
    private String handelMessageContent(List<StockAssets> assetsDTOList, Boolean flag) throws Exception {
        StringBuffer stringBuffer = new StringBuffer();
        List<String> holderList = assetsDTOList.stream().distinct().map(stockAssets -> stockAssets.getHolder()).collect(Collectors.toList());
        Map<String, SysUser> stringSysUserMap = ambaseCommonService.selectSysUserMapByIds(holderList);
        for (StockAssets assetsDTO : assetsDTOList) {
            //管理员发送消息内容
            if (flag) {
                if (StringUtils.isNotBlank(assetsDTO.getHolder())) {
                    SysUser sysUser = stringSysUserMap.get(assetsDTO.getHolder());
                    if (null != sysUser) {
                        stringBuffer.append("资产名称:").append(assetsDTO.getAssetsName()).append(",")
                                .append("资产编码:").append(assetsDTO.getAssetsCode()).append(",")
                                .append("资产借用人:").append(assetsDTO.getHolder()).append(" ").append(sysUser.getName()).append(";")
                                .append("归还时间:").append(DateUtils.dateFormat(assetsDTO.getPlanReturnTime(), DateUtils.DATE_PATTERN))
                                .append(";").append("<br>");
                    }
                }
            } else {
                //资产使用人发送消息内容
                stringBuffer.append("资产名称:").append(assetsDTO.getAssetsName()).append(",")
                        .append("资产编码:").append(assetsDTO.getAssetsCode()).append(",")
                        .append("归还时间:").append(DateUtils.dateFormat(assetsDTO.getPlanReturnTime(), DateUtils.DATE_PATTERN))
                        .append(";").append("<br>");
            }
        }
        if (stringBuffer.length() > 0) {
            return stringBuffer.toString();
        }
        return null;
    }


    /**
     * 当前使用人发送呱呱消息
     *
     * @param holder
     * @param assetsDTOList
     */
    private void employeeSendMessage(String holder, List<StockAssets> assetsDTOList) throws Exception {
        log.info("StockPlanAssetsTimeTaskServiceImpl.employeeSendMessage holder={},assetsDTOList={}",holder,assetsDTOList);
        //拼接发送消息内容
        String messageContent = handelMessageContent(assetsDTOList, false);
        List<String> employEmailList = ambaseCommonService.selectUsersEmailByIds(Arrays.asList(holder));
        if (CollectionUtils.isNotEmpty(employEmailList)) {
            log.info("StockPlanAssetsTimeTaskServiceImpl.employeeSendMessage employEmailList={},messageContent={}",employEmailList,messageContent);
            //将数组拼接为字符串
            String peopleResult = employEmailList.stream().collect(Collectors.joining(";"));
            //发送呱呱消息
            sendMessage(PropertyConstants.ASSETS_LOAN_MODEL, peopleResult, messageContent, false);
            //发送邮件
            sendMessage(PropertyConstants.ASSETS_LOAN_EMAIL_MODEL, peopleResult, messageContent, false);
        }
    }


    /**
     * 管理员发送消息
     *
     * @param assetsDTOList
     * @param assetsKeeper
     */
    private void adminSendMessage(String assetsKeeper, List<StockAssets> assetsDTOList) throws Exception {
        log.info("StockPlanAssetsTimeTaskServiceImpl.adminSendMessage assetsKeeper={},assetsDTOList={}",assetsKeeper,assetsDTOList);
        //拼接发送消息内容
        String messageContent = handelMessageContent(assetsDTOList, true);
        List<String> employEmailList = ambaseCommonService.selectUsersEmailByIds(Arrays.asList(assetsKeeper));
        if (CollectionUtils.isNotEmpty(employEmailList)) {
            log.info("StockPlanAssetsTimeTaskServiceImpl.adminSendMessage employEmailList={},messageContent={}",employEmailList,messageContent);
            //将数组拼接为字符串
            String peopleResult = employEmailList.stream().collect(Collectors.joining(";"));
            //发送呱呱消息
            sendMessage(PropertyConstants.ASSETS_LOAN_MODEL, peopleResult, messageContent, true);
            //发送邮件
            sendMessage(PropertyConstants.ASSETS_LOAN_EMAIL_MODEL, peopleResult, messageContent, true);
        }
    }


    /**
     * 发送呱呱消息
     *
     * @param taskCode
     * @param users
     */
    private void sendMessage(String taskCode, String users, String messageContent, Boolean flag) {
        Map<String, Object> param = new HashMap<>(4);
        try {
            List<Map<String, String>> content = new ArrayList<>(4);
            //消息内容
            Map<String, String> map1 = new HashMap<>(2);
            map1.put("key", "messageContent");
            map1.put("value", messageContent);
            content.add(map1);
            //收件人
            Map<String, String> map2 = new HashMap<>(2);
            map2.put("key", "recipients");
            map2.put("value", users);
            content.add(map2);

//            //管理员发送消息
//            if (flag) {
//                //执照归还url
//                Map<String, String> map5 = new HashMap<>(2);
//                map5.put("key", "returnUrl");
//                map5.put("value", adminReturnUrl);
//                content.add(map5);
//            } else {
//                //使用人发送消息
//                //执照续借url
//                Map<String, String> map3 = new HashMap<>(2);
//                map3.put("key", "renewUrl");
//                map3.put("value", employeeRenewUrl);
//                content.add(map3);
//
//                //执照归还url
//                Map<String, String> map4 = new HashMap<>(2);
//                map4.put("key", "returnUrl");
//                map4.put("value", employeeReturnUrl);
//                content.add(map4);
//            }

            //组织调用消息平台参数
            param.put("SystemId", systemId);
            param.put("CheckCode", checkCode);
            param.put("TaskCode", taskCode);
            param.put("TaskRequestJson", JsonUtil.getJsonString(content));

            log.info("send email params:{}", param);
            ActivityRespDTO respDTO = messageUtil.addTaskRequest(param);
            if (!AllocateImportLineEnum.Status.SUCCESS.getCode().equals(respDTO.getStatus())) {
                log.info("发送呱呱消息失败：{}", respDTO);
            } else {
                log.info("发送呱呱消息成功：{}", respDTO);
            }
        } catch (Exception e) {
            log.error("逾期归还提醒发送消息异常：{}", param, e);
        }
    }


//    public static void main(String[] args) {
//        StringBuffer stringBuffer = new StringBuffer();
//        stringBuffer.append("11111")
//                .append("\n\r")
//                .append("222322424");
//
//        System.out.println(stringBuffer.toString());
//
//    }

}
