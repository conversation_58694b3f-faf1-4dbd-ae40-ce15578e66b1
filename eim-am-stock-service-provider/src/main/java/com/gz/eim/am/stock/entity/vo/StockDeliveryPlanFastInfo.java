package com.gz.eim.am.stock.entity.vo;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2019/12/17
 * @description:
 */
public class StockDeliveryPlanFastInfo {
    /**
     * 调出仓库
     */
    private String outWarehouseCode;
    /**
     * 物料编码
     */
    private String suppliesCode;
    /**
     * 总数量
     */
    private Integer sumNumber;

    public String getOutWarehouseCode() {
        return outWarehouseCode;
    }

    public void setOutWarehouseCode(String outWarehouseCode) {
        this.outWarehouseCode = outWarehouseCode;
    }

    public String getSuppliesCode() {
        return suppliesCode;
    }

    public void setSuppliesCode(String suppliesCode) {
        this.suppliesCode = suppliesCode;
    }

    public Integer getSumNumber() {
        return sumNumber;
    }

    public void setSumNumber(Integer sumNumber) {
        this.sumNumber = sumNumber;
    }

    @Override
    public String toString() {
        return "StockDeliveryPlanFastInfo{" +
                "outWarehouseCode='" + outWarehouseCode + '\'' +
                ", suppliesCode='" + suppliesCode + '\'' +
                ", sumNumber=" + sumNumber +
                '}';
    }
}
