<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <property name="LOG_HOME" value="/Users/<USER>/med/log/apps/"/>
    <property name="APP_NAME" value="imc-am/eim-am-stock"/>
    <property name="FILE_NAME_PREFIX" value="eim-am-stock"/>
    <property name="COMMON_PATTERN"
              value="%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ} [%level] ${APP_NAME} %logger{50} - %X{requestId} %msg%n"/>
    <!-- 读取spring.application.name中的属性来生成日志文件名 -->
    <springProperty  name="appName" source="spring.application.name" defaultValue="localhost"/>
    <property name="encoding" value="UTF-8"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${COMMON_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 日志文件 -->
    <appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${FILE_NAME_PREFIX}.%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${COMMON_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 默认日志输出级别 -->
    <root>
        <level value="INFO"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="logFile"/>
    </root>

    <logger name="org.springframework" level="INFO"/>
    <logger name="com.guazi.fe" level="DEBUG"/>

</configuration>