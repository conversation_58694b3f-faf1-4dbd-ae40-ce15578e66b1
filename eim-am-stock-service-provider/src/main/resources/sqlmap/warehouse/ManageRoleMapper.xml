<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.warehouse.ManageRoleMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockManageRole">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_type" jdbcType="TINYINT" property="roleType" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    role_id, role_name, role_type, warehouse_code, status, created_by, created_at, updated_by, 
    updated_at
  </sql>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockManageRole"
          useGeneratedKeys="true"
          keyProperty="roleId">
    insert into stock_manage_role (role_id, role_name, role_type, 
      warehouse_code, status, created_by, 
      created_at, updated_by, updated_at
      )
    values (#{roleId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, #{roleType,jdbcType=TINYINT}, 
      #{warehouseCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="selectRwRole" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM stock_manage_role
    WHERE warehouse_code = #{warehouseCode} AND role_type = 1
  </select>
</mapper>