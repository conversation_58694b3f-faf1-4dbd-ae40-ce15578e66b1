<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.warehouse.RoleKeeperMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockRoleKeeper">
        <id column="role_keeper_id" jdbcType="BIGINT" property="roleKeeperId"/>
        <result column="keeper_code" jdbcType="VARCHAR" property="keeperCode"/>
        <result column="contact_way" jdbcType="VARCHAR" property="contactWay"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
    role_keeper_id, keeper_code,contact_way, role_id, created_by, created_at, updated_by, updated_at,
    status
  </sql>
    <select id="checkSuperRole" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM stock_role_keeper keeper
    LEFT JOIN stock_manage_role role ON keeper.role_id = role.role_id
    WHERE keeper.keeper_code = #{keeperCode} AND keeper.status = 1 AND role.role_type = 0 AND role.status = 1
  </select>
    <select id="selectKeepWarehouse" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT role.warehouse_code
    FROM stock_role_keeper keeper
    LEFT JOIN stock_manage_role role ON keeper.role_id = role.role_id
    WHERE keeper.keeper_code = #{keeperCode} AND keeper.status = 1 AND role.role_type = 4 AND role.status = 1
  </select>
    <select id="selectWarehouseKeeper" parameterType="java.lang.String"
            resultType="com.gz.eim.am.stock.dto.response.RoleKeeperResp">
    SELECT
    keeper.keeper_code keeperCode,
    keeper.contact_way contactWay,
    user.name keeperName
    FROM stock_role_keeper keeper
    LEFT JOIN stock_manage_role role ON keeper.role_id = role.role_id
    LEFT JOIN ambase.sys_user user ON keeper.keeper_code = user.emp_id
    WHERE role.warehouse_code = #{warehouseCode} AND keeper.status = 1 AND role.role_type = 3 AND role.status = 1
  </select>
    <delete id="deleteByRoleId" parameterType="java.lang.Long">
    DELETE FROM stock_role_keeper
    WHERE role_id = #{roleId}
  </delete>

    <select id="selectKeepWarehouseByParam" resultType="java.lang.String">
       <!-- SELECT
        wa.code
        FROM stock_warehouse wa
        WHERE 1 = 1
        <choose>
            <when test="roleType == null">
                AND (
                EXISTS ( SELECT 1 FROM stock_role_keeper srk, stock_manage_role smr WHERE srk.role_id = smr.role_id AND
                smr.role_type = 0 AND srk.keeper_code = #{keeperCode})
                OR cost_center_code IN (
                SELECT
                asd.dept_id
                FROM
                ambase.sys_dept asd,
                stock.stock_manage_role smr,
                stock.stock_role_keeper srk,
                stock.stock_warehouse sw
                WHERE
                sw.CODE = smr.warehouse_code
                and smr.role_id = srk.role_id
                AND smr.role_type = 1
                AND srk.keeper_code = #{keeperCode}
                and FIND_IN_SET(sw.cost_center_code,REPLACE(asd.dept_full_id,'/',','))
                )
                OR code in(
                SELECT
                sw.code
                FROM
                stock.stock_manage_role smr,
                stock.stock_role_keeper srk,
                stock.stock_warehouse sw
                WHERE
                sw.CODE = smr.warehouse_code
                AND smr.role_id = srk.role_id
                AND smr.role_type in (2,3)
                AND srk.keeper_code = #{keeperCode}
                <if test="warehouseCode != null">
                    and smr.warehouse_code = #{warehouseCode}
                </if>
                )
                )
            </when>
            <when test="roleType == 0">
                AND
                EXISTS ( SELECT 1 FROM stock_role_keeper srk, stock_manage_role smr WHERE srk.role_id = smr.role_id AND
                smr.role_type = 0 AND srk.keeper_code = #{keeperCode}
                )
            </when>
            <when test="roleType == 1">
                AND cost_center_code in (SELECT
                asd.dept_id
                FROM
                ambase.sys_dept asd,
                stock.stock_manage_role smr,
                stock.stock_role_keeper srk,
                stock.stock_warehouse sw
                WHERE
                sw.CODE = smr.warehouse_code
                AND smr.role_id = srk.role_id
                AND smr.role_type = 1
                AND srk.keeper_code = #{keeperCode}
                and FIND_IN_SET(sw.cost_center_code,REPLACE(asd.dept_full_id,'/',','))
                )
            </when>
            <when test="roleType in (2,3)">
                AND code in(
                SELECT
                sw.code
                FROM
                stock.stock_manage_role smr,
                stock.stock_role_keeper srk,
                stock.stock_warehouse sw
                WHERE
                sw.CODE = smr.warehouse_code
                AND smr.role_id = srk.role_id
                AND smr.role_type in (2,3)
                AND srk.keeper_code = #{keeperCode}
                <if test="warehouseCode != null">
                    and smr.warehouse_code = #{warehouseCode}
                </if>
                )
            </when>
        </choose>-->
        select f.code
        from ambase.sys_user a
        INNER JOIN stock_role_keeper b on ((b.keeper_code='' and a.dept_id=b.dept_id) or (b.keeper_code &lt;&gt; '' and a.emp_id=b.keeper_code))
        INNER JOIN stock_manage_role c on b.role_id=c.role_id
        INNER JOIN stock_role_warehouse d on c.role_id=d.role_id
        INNER JOIN stock_warehouse f on d.warehouse_code='' and d.warehouse_type=f.type
        where a.emp_id=#{keeperCode} and c.start_date &lt;= SYSDATE() and c.end_date>=SYSDATE()
        and b.status=1 and b.del_flag=0 and d.role_status=1 and d.del_flag=0
        <if test="warehouseCode != null">
            and f.code = #{warehouseCode}
        </if>
        union
        select f.code
        from ambase.sys_user a
        INNER JOIN stock_role_keeper b on ((b.keeper_code='' and a.dept_id=b.dept_id) or (b.keeper_code &lt;&gt; '' and a.emp_id=b.keeper_code))
        INNER JOIN stock_manage_role c on b.role_id=c.role_id
        INNER JOIN stock_role_warehouse d on c.role_id=d.role_id
        INNER JOIN stock_warehouse f on d.warehouse_code &lt;&gt; '' and d.warehouse_code=f.code
        where a.emp_id=#{keeperCode} and c.start_date &lt;= SYSDATE() and c.end_date>=SYSDATE()
        and b.status=1 and b.del_flag=0 and d.role_status=1 and d.del_flag=0
        <if test="warehouseCode != null">
            and f.code = #{warehouseCode}
        </if>
    </select>

    <select id="selectKeepWarehouseByParamVo" parameterType ="com.gz.eim.am.stock.entity.vo.StockKeeperWareSearchVo" resultType="java.lang.String">
        select f.code
        from ambase.sys_user a
        INNER JOIN stock_role_keeper b on ((b.keeper_code='' and a.dept_id=b.dept_id) or (b.keeper_code &lt;&gt; '' and a.emp_id=b.keeper_code))
        INNER JOIN stock_manage_role c on b.role_id=c.role_id
        INNER JOIN stock_role_warehouse d on c.role_id=d.role_id
        INNER JOIN stock_warehouse f on d.warehouse_code='' and d.warehouse_type=f.type
        where a.emp_id=#{keeperCode} and c.start_date &lt;= SYSDATE() and c.end_date>=SYSDATE()
        and b.status=1 and b.del_flag=0 and d.role_status=1 and d.del_flag=0
        <if test="warehouseCode != null and warehouseCode != ''">
            and f.code = #{warehouseCode}
        </if>
        <if test="warehouseTypes != null and warehouseTypes.size()>0">
            and f.type in
            <foreach collection="warehouseTypes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
<!--        <if test="warehouseTypes == null">-->
<!--            and f.type not in (12)-->
<!--        </if>-->
        union
        select f.code
        from ambase.sys_user a
        INNER JOIN stock_role_keeper b on ((b.keeper_code='' and a.dept_id=b.dept_id) or (b.keeper_code &lt;&gt; '' and a.emp_id=b.keeper_code))
        INNER JOIN stock_manage_role c on b.role_id=c.role_id
        INNER JOIN stock_role_warehouse d on c.role_id=d.role_id
        INNER JOIN stock_warehouse f on d.warehouse_code &lt;&gt; '' and d.warehouse_code=f.code
        where a.emp_id=#{keeperCode} and c.start_date &lt;= SYSDATE() and c.end_date>=SYSDATE()
        and b.status=1 and b.del_flag=0 and d.role_status=1 and d.del_flag=0
        <if test="warehouseCode != null and warehouseCode != ''">
            and f.code = #{warehouseCode}
        </if>
        <if test="warehouseTypes != null and warehouseTypes.size()>0">
            and f.type in
            <foreach collection="warehouseTypes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
<!--        <if test="warehouseTypes == null">-->
<!--            and f.type not in (12)-->
<!--        </if>-->
    </select>

    <select id="isAll" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(1)
        from
        stock_role_keeper srk, stock_manage_role smr
        where srk.role_id = smr.role_id
        and smr.role_type = 0
        and srk.keeper_code = #{keeperCode}
    </select>
</mapper>
