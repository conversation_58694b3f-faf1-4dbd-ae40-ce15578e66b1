<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.check.TakingPlanUserDeviceMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockTakingPlanUserDevice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="taking_plan_no" jdbcType="VARCHAR" property="takingPlanNo" />
    <result column="holder" jdbcType="VARCHAR" property="holder" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="system_type" jdbcType="VARCHAR" property="systemType" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockTakingPlanUserDevice">
   insert into stock_taking_plan_user_device (id, taking_plan_no, holder,
                                              serial_no, device_name, system_type,
                                              created_at)
   values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.takingPlanNo,jdbcType=VARCHAR}, #{item.holder,jdbcType=VARCHAR},
      #{item.serialNo,jdbcType=VARCHAR}, #{item.deviceName,jdbcType=VARCHAR}, #{item.systemType,jdbcType=VARCHAR},
      #{item.createdAt,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>