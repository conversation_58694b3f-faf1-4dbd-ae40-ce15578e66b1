<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.check.StockCheckRemindMapper">
    <select id="selectCheckInDoubtDataList" resultType="com.gz.eim.am.stock.entity.StockCheckInDoubtDataRespDO">
        select sactd.snapshot_assets_code assetsCode, sactd.snapshot_assets_name assetsName, sactd.taking_plan_no takingPlanNo, temp_stp.taking_plan_name takingPlanName, sactd.check_people checkPeople, sactd.snapshot_holder_time holderTime, sactd.approve_time approveTime,
        sactd.err_message errMessage, sactd.err_desc errDesc, sactd.check_time checkTime, sactd.snapshot_assets_holder snapshotAssetsHolder, su.name snapshotAssetsHolderName, su.email snapshotAssetsHolderEmail, sactd.snapshot_assets_status snapshotAssetsStatus, sactd.snapshot_sn_no snapshotSnNo
        from stock_assets_check_task_detail sactd
        inner join stock_assets_check_task sact on sactd.check_task_id = sact.check_task_id
        inner join
        (
        select distinct stp.taking_plan_no, stp.taking_plan_name from stock_taking_plan stp
        where stp.taking_deadline_date > now() and stp.bill_status = 0
        ) temp_stp on sactd.taking_plan_no = temp_stp.taking_plan_no
        left join ambase.sys_user su on sactd.snapshot_assets_holder = su.emp_id
<!--        where (sactd.own_flag = 2 or sactd.new_insert_flag = 1)-->
        where sactd.err_message is not null and sactd.err_message != ''
        and sact.check_task_method = 1
        and sactd.check_time between #{checkStartDate} and #{checkEndDate}
    </select>
    <select id="selectNotCheckPeopleList" parameterType="com.gz.eim.am.stock.entity.StockCheckNotCheckPeopleReqDO" resultType="com.gz.eim.am.stock.entity.StockCheckNotCheckPeopleRespDO">
        select temp_sactd.task_Last_time taskLastTime, temp_sactd.taking_plan_no takingPlanNo, temp_sactd.taking_plan_name takingPlanName, temp_sactd.taking_deadline_date takingDeadlineDate, temp_sactd.check_people checkPeople, su.email checkPeopleEmail, su.phone checkPeoplePhone, su.supervisor_id supervisorId from
        (select distinct temp_stp.task_Last_time, sactd.taking_plan_no, temp_stp.taking_plan_name, temp_stp.taking_deadline_date, sactd.check_people from stock_assets_check_task_detail sactd
        inner join stock_assets_check_task sact on sactd.check_task_id = sact.check_task_id
        inner join
            (
            select max(sact.task_Last_time) task_Last_time, stp.taking_plan_no, stp.taking_plan_name, stp.taking_deadline_date from stock_taking_plan stp
            inner join
            stock_assets_check_task sact on stp.taking_plan_no = sact.taking_plan_no
            <where>
                <if test="checkStartDate != null">
                    and stp.taking_deadline_date &gt;= #{checkStartDate}
                </if>
                <if test="checkEndDate != null">
                    and stp.taking_deadline_date &lt;= #{checkEndDate}
                </if>
                and stp.bill_status = 0
            </where>
            group by stp.taking_plan_no
            ) temp_stp
        on temp_stp.taking_plan_no = sactd.taking_plan_no
        where sactd.check_flag = 0 and sact.check_task_status = 2
        and sact.check_task_method = 1
        ) temp_sactd
        inner join ambase.sys_user su on temp_sactd.check_people = su.emp_id and su.status = 'A'
    </select>
    <select id="selectCheckPeopleDataList" parameterType="com.gz.eim.am.stock.entity.StockCheckSelectCheckPeopleDataReqDO" resultType="com.gz.eim.am.stock.entity.StockCheckSelectCheckPeopleDataRespDO">
        select sactd.snapshot_assets_code assetsCode, sactd.taking_plan_no takingPlanNo, SUBSTRING_INDEX(sactd.check_people, ';', 1) checkPeople,
        sactd.check_flag checkFlag, max(sact.task_Last_time) taskLastTime from stock_assets_check_task_detail sactd
        inner join stock_assets_check_task sact on sactd.check_task_id = sact.check_task_id
        where sactd.taking_plan_no = #{takingPlanNo}
        and
        <foreach collection="checkPeopleList" item="item" open="(" close=")" separator="or">
            sactd.check_people like concat(#{item}, '%')
        </foreach>
        <if test="checkFlag != null">
            and sactd.check_flag = #{checkFlag}
        </if>
        and sact.check_task_method = 1
        group by sactd.snapshot_assets_code
    </select>
    <select id="selectNotCheckPeopleUserIdList" resultType="java.lang.String">
        select pbpdt.userid userId from
        (select distinct check_people from stock_assets_check_task_detail sactd
        inner join stock_assets_check_task sact on sactd.check_task_id = sact.check_task_id
        where sactd.taking_plan_no in
        (
        select distinct stp.taking_plan_no from stock_taking_plan stp
        where stp.taking_deadline_date > now() and stp.bill_status = 0
        )
        and sactd.check_flag = 0 and sact.check_task_status = 2
        and sact.check_task_method = 1
        ) temp_sactd
        inner join ambase.ps_bus_personal_data_tbl pbpdt on temp_sactd.check_people = pbpdt.emplid
    </select>
</mapper>
