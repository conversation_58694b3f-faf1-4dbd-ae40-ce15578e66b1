<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.check.CheckResultAdjustMapper">


    <select id="queryCheckInventoryCount" parameterType="com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO" resultType="java.lang.Long">
        select count(1) from stock_inventory_in_plan_heads a inner join stock_taking_plan c on a.biz_no=c.taking_plan_no
        Where a.inventory_in_plan_type=16
        <if test="logonUser !=null and logonUser !=''">
            and c.created_by=#{logonUser}
        </if>
        <if test="billNo !=null and billNo !=''">
            and a.inventory_in_plan_no=#{billNo}
        </if>
        <if test="bizId !=null and bizId !=''">
            and a.biz_no=#{bizId}
        </if>
        <if test="billStatus !=null">
            and a.status = #{billStatus}
        </if>
        <if test="billingUser !=null and billingUser !=''">
            and a.billing_user=#{billingUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &gt;= #{beginAdjustTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &lt;= #{endAdjustTime}
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and assetsCode != null and assetsCode != ''">
            And exists (select 1 from check_difference_list_line b where a.inventory_in_plan_no=b.biz_no and
            b.real_warehouse_code=#{warehouseCode} and b.snapshot_assets_code=#{assetsCode})
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and (assetsCode == null or assetsCode == '')">
            And exists (select 1 from check_difference_list_line b where a.inventory_in_plan_no=b.biz_no and
            b.real_warehouse_code=#{warehouseCode})
        </if>
        <if test="(warehouseCode == null or warehouseCode =='') and assetsCode != null and assetsCode != ''">
            And exists (select 1 from check_difference_list_line b where a.inventory_in_plan_no=b.biz_no and
            b.snapshot_assets_code=#{assetsCode})
        </if>
    </select>

    <select id="queryCheckInventory" parameterType="com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO" resultType="com.gz.eim.am.stock.dto.response.check.AdjustResultHeadRespDTO">
        select '盘盈入库单' as billType,
        a.inventory_in_plan_no as billNo,
        a.biz_no as bizId,
        a.billing_user as billingUser,
        a.in_warehouse_code as warehouseCode,
        DATE_FORMAT(a.billing_time, '%Y-%m-%d') as billingTime,
        a.status as billStatus,
        DATE_FORMAT(a.adjust_date, '%Y-%m-%d') as adjustTime
        from stock_inventory_in_plan_heads a inner join stock_taking_plan c on a.biz_no=c.taking_plan_no
        Where a.inventory_in_plan_type=16
        <if test="logonUser !=null and logonUser !=''">
            and c.created_by=#{logonUser}
        </if>
        <if test="billNo !=null and billNo !=''">
            and a.inventory_in_plan_no=#{billNo}
        </if>
        <if test="bizId !=null and bizId !=''">
            and a.biz_no=#{bizId}
        </if>
        <if test="billStatus !=null">
            and a.status = #{billStatus}
        </if>
        <if test="billingUser !=null and billingUser !=''">
            and a.billing_user=#{billingUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &gt;= #{beginAdjustTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &lt;= #{endAdjustTime}
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and assetsCode != null and assetsCode != '' ">
            And exists (select 1 from check_difference_list_line b where a.inventory_in_plan_no=b.biz_no and
            b.real_warehouse_code=#{warehouseCode} and b.snapshot_assets_code=#{assetsCode})
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and (assetsCode == null or assetsCode == '') ">
            And exists (select 1 from check_difference_list_line b where a.inventory_in_plan_no=b.biz_no and
            b.real_warehouse_code=#{warehouseCode})
        </if>
        <if test="(warehouseCode == null or warehouseCode =='') and assetsCode != null and assetsCode != '' ">
            And exists (select 1 from check_difference_list_line b where a.inventory_in_plan_no=b.biz_no and
            b.snapshot_assets_code=#{assetsCode})
        </if>
        ORDER BY a.inventory_in_plan_head_id desc
        <if test="noPaging == null ">
            LIMIT #{pageSize} OFFSET #{startNum}
        </if>

    </select>


    <select id="queryCheckDeliveryCount" parameterType="com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO" resultType="java.lang.Long">
        select count(1) from stock_delivery_plan_heads a inner join stock_taking_plan c on a.biz_no=c.taking_plan_no
        Where a.out_stock_type=4
        <if test="logonUser !=null and logonUser !=''">
            and c.created_by=#{logonUser}
        </if>
        <if test="billNo !=null and billNo !=''">
            and a.delivery_plan_no=#{billNo}
        </if>
        <if test="bizId !=null and bizId !=''">
            and a.biz_no=#{bizId}
        </if>
        <if test="billStatus !=null">
            and a.status = #{billStatus}
        </if>
        <if test="billingUser !=null and billingUser !=''">
            and a.billing_user=#{billingUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &gt;= #{beginAdjustTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &lt;= #{endAdjustTime}
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and assetsCode != null and assetsCode != ''">
            And exists (select 1 from check_difference_list_line b where a.delivery_plan_no=b.biz_no and
            b.snapshot_warehouse_code=#{warehouseCode} and b.snapshot_assets_code=#{assetsCode})
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and (assetsCode == null or assetsCode == '')">
            And exists (select 1 from check_difference_list_line b where a.delivery_plan_no=b.biz_no and
            b.snapshot_warehouse_code=#{warehouseCode})
        </if>
        <if test="(warehouseCode == null or warehouseCode =='') and assetsCode != null and assetsCode != ''">
            And exists (select 1 from check_difference_list_line b where a.delivery_plan_no=b.biz_no and
            b.snapshot_warehouse_code=#{assetsCode})
        </if>
    </select>

    <select id="queryCheckDelivery" parameterType="com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO" resultType="com.gz.eim.am.stock.dto.response.check.AdjustResultHeadRespDTO">
        select '盘亏出库单' as billType,
        a.delivery_plan_no as billNo,
        a.biz_no as bizId,
        a.billing_user as billingUser,
        a.out_warehouse_code as warehouseCode,
        DATE_FORMAT(a.billing_time, '%Y-%m-%d') as billingTime,
        a.status as billStatus,
        DATE_FORMAT(a.adjust_date, '%Y-%m-%d') as adjustTime
        from stock_delivery_plan_heads a inner join stock_taking_plan c on a.biz_no=c.taking_plan_no
        Where a.out_stock_type=4
        <if test="logonUser !=null and logonUser !=''">
            and c.created_by=#{logonUser}
        </if>
        <if test="billNo !=null and billNo !=''">
            and a.delivery_plan_no=#{billNo}
        </if>
        <if test="bizId !=null and bizId !=''">
            and a.biz_no=#{bizId}
        </if>
        <if test="billStatus !=null">
            and a.status = #{billStatus}
        </if>
        <if test="billingUser !=null and billingUser !=''">
            and a.billing_user=#{billingUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &gt;= #{beginAdjustTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.adjust_date, '%Y-%m-%d') &lt;= #{endAdjustTime}
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and assetsCode != null and assetsCode != ''">
            And exists (select 1 from check_difference_list_line b where a.delivery_plan_no=b.biz_no and
            b.snapshot_warehouse_code=#{warehouseCode} and b.snapshot_assets_code=#{assetsCode})
        </if>
        <if test="warehouseCode != null and warehouseCode != '' and (assetsCode == null or assetsCode == '')">
            And exists (select 1 from check_difference_list_line b where a.delivery_plan_no=b.biz_no and
            b.snapshot_warehouse_code=#{warehouseCode})
        </if>
        <if test="(warehouseCode == null or warehouseCode =='') and assetsCode != null and assetsCode != ''">
            And exists (select 1 from check_difference_list_line b where a.delivery_plan_no=b.biz_no and
            b.snapshot_warehouse_code=#{assetsCode})
        </if>
        ORDER BY a.delivery_plan_head_id desc
        <if test="noPaging == null ">
            LIMIT #{pageSize} OFFSET #{startNum}
        </if>
    </select>

    <select id="queryCheckAdjustDetailCount" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(1)
        from check_difference_list_line a
        where a.biz_no=#{billNo}
    </select>

    <select id="queryCheckAdjustDetail" parameterType="com.gz.eim.am.stock.dto.request.check.AdjustResultReqDTO" resultType="com.gz.eim.am.stock.dto.response.check.AdjustResultLineRespDTO">
        select
        <choose>
            <!-- 盘盈入库单查询资产编码 -->
            <when test="type == 1">
                if(a.adjust_assets_code is null,a.snapshot_assets_code,a.adjust_assets_code) as assetsCode,
            </when>
            <!-- 盘亏出库单查询资产编码 -->
            <otherwise>
                a.snapshot_assets_code as assetsCode,
            </otherwise>
        </choose>
        a.snapshot_assets_name as assetsName,
        a.real_assets_status as assetsStatus,
        b.name as warehouseCodeName,
        a.assets_keeper as assetsKeeperCode,
        d.name as assetsKeeperName,
        c.company_full_name as companyName,
        a.time_left as timeLeft,
        a.estimated_amount as estimatedAmount
        from check_difference_list_line a
        left join stock_warehouse b on a.real_warehouse_code=b.code
        left join ambase.sys_lgl_company c on a.company_code = c.company_code
        left join ambase.sys_user d on a.assets_keeper=d.emp_id
        where a.biz_no=#{billNo}
        ORDER BY a.line_id
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>
</mapper>