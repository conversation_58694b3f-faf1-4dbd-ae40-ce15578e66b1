<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.check.TakingPlanMapper">


    <select id="selectListByQuery" parameterType="com.gz.eim.am.stock.dto.request.check.StockTakingPlanQueryReqDTO"
            resultType="com.gz.eim.am.stock.entity.StockTakingPlan">
        select * from stock_taking_plan stp
        left join stock_assets_check_task sct on stp.taking_plan_no = sct.taking_plan_no
        where 1=1
        <if test="billStatus !=null">
            and stp.bill_status = #{billStatus}
        </if>
        <if test="startDate !=null">
            and stp.created_at <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate !=null">
            and stp.created_at <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="takingPlanNo !=null and takingPlanNo !=''">
            and stp.taking_plan_no = #{takingPlanNo}
        </if>
        <if test="takingPlanName !=null and takingPlanName !=''">
            and stp.taking_plan_name like concat('%',#{takingPlanName},'%')
        </if>
        <!--<choose>
            <when test="initFlag != null and initFlag ==1  and takingPlanNo == null ">
                <if test="createdByList !=null and createdByList.size()>0 ">
                    and stp.created_by in
                    <foreach collection="createdByList" index="index" item="createdBy" open="("
                             separator="," close=")">
                        #{createdBy}
                    </foreach>
                    or sct.duty_user in
                    <foreach collection="createdByList" index="index" item="dutyUser" open="("
                             separator="," close=")">
                        #{dutyUser}
                    </foreach>

                </if>
            </when>
            <when test="takingPlanNo !=null and takingPlanNo !='' ">
                <if test="createdByList !=null and createdByList.size()>0 ">
                    and ( stp.created_by in
                    <foreach collection="createdByList" index="index" item="createdBy" open="("
                             separator="," close=")">
                        #{createdBy}
                    </foreach>
                    or sct.duty_user in
                    <foreach collection="createdByList" index="index" item="dutyUser" open="("
                             separator="," close=")">
                        #{dutyUser}
                    </foreach>
                    )
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>-->
        <if test="currentLoginUser !=null and currentLoginUser !=''">
            and ( stp.created_by = #{currentLoginUser} or sct.duty_user = #{currentLoginUser} )
        </if>
        group by stp.taking_plan_no
        order by stp.id desc
        <if test="pageSize != null">
            <if test="startNum != null">
                limit ${startNum}, ${pageSize}
            </if>
            <if test="startNum == null">
                limit ${pageSize}
            </if>
        </if>
    </select>


    <select id="countByQuery" parameterType="com.gz.eim.am.stock.dto.request.check.StockTakingPlanQueryReqDTO"
            resultType="java.lang.Long">
        select count(DISTINCT(stp.taking_plan_no)) from stock_taking_plan stp
        left join stock_assets_check_task sct on stp.taking_plan_no = sct.taking_plan_no
        where 1=1

        <if test="billStatus !=null">
            and stp.bill_status = #{billStatus}
        </if>
        <if test="startDate !=null">
            and stp.created_at <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate !=null">
            and stp.created_at <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="takingPlanNo !=null and takingPlanNo !=''">
            and stp.taking_plan_no = #{takingPlanNo}
        </if>
        <if test="takingPlanName !=null and takingPlanName !=''">
            and stp.taking_plan_name like concat('%',#{takingPlanName},'%')
        </if>
        <!--<choose>
            <when test="initFlag != null and initFlag ==1 and  takingPlanNo == null  ">
                <if test="createdByList !=null and createdByList.size()>0 ">
                    and stp.created_by in
                    <foreach collection="createdByList" index="index" item="createdBy" open="("
                             separator="," close=")">
                        #{createdBy}
                    </foreach>
                    or sct.duty_user in
                    <foreach collection="createdByList" index="index" item="dutyUser" open="("
                             separator="," close=")">
                        #{dutyUser}
                    </foreach>

                </if>
            </when>
            <when test="takingPlanNo !=null and takingPlanNo !='' ">
                <if test="createdByList !=null and createdByList.size()>0 ">
                    and ( stp.created_by in
                    <foreach collection="createdByList" index="index" item="createdBy" open="("
                             separator="," close=")">
                        #{createdBy}
                    </foreach>
                    or sct.duty_user in
                    <foreach collection="createdByList" index="index" item="dutyUser" open="("
                             separator="," close=")">
                        #{dutyUser}
                    </foreach>
                    )
                </if>
            </when>
            <otherwise></otherwise>
        </choose>-->
        <if test="currentLoginUser !=null and currentLoginUser !=''">
            and ( stp.created_by = #{currentLoginUser} or sct.duty_user = #{currentLoginUser} )
        </if>
    </select>
    <select id="selectStockTakingPlanByTakingPlanNoForUpdate" parameterType="java.lang.String" resultType="com.gz.eim.am.stock.entity.StockTakingPlan">
    select * from stock_taking_plan where taking_plan_no = #{takingPlanNo} for update
    </select>
</mapper>
