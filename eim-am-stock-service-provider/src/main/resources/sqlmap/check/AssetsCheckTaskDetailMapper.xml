<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.check.AssetsCheckTaskDetailMapper">
  <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetail">
    <foreach collection="list" item="record" separator=";">
      update stock_assets_check_task_detail
      <set>
        <if test="record.taskDetailId != null">
          task_detail_id = #{record.taskDetailId,jdbcType=BIGINT},
        </if>
        <if test="record.checkTaskId != null">
          check_task_id = #{record.checkTaskId,jdbcType=BIGINT},
        </if>
        <if test="record.takingPlanNo != null">
          taking_plan_no = #{record.takingPlanNo,jdbcType=VARCHAR},
        </if>
        <if test="record.checkPeople != null">
          check_people = #{record.checkPeople,jdbcType=VARCHAR},
        </if>
        <if test="record.busLargeRegion != null">
          bus_large_region = #{record.busLargeRegion,jdbcType=VARCHAR},
        </if>
        <if test="record.busCity != null">
          bus_city = #{record.busCity,jdbcType=VARCHAR},
        </if>
        <if test="record.assetsKeeper != null">
          assets_keeper = #{record.assetsKeeper,jdbcType=VARCHAR},
        </if>
        <if test="record.holderAddressProvince != null">
          holder_address_province = #{record.holderAddressProvince,jdbcType=VARCHAR},
        </if>
        <if test="record.holderAddressCity != null">
          holder_address_city = #{record.holderAddressCity,jdbcType=VARCHAR},
        </if>
        <if test="record.holderAddressCounty != null">
          holder_address_county = #{record.holderAddressCounty,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotAssetsCode != null">
          snapshot_assets_code = #{record.snapshotAssetsCode,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotAssetsName != null">
          snapshot_assets_name = #{record.snapshotAssetsName,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotSnNo != null">
          snapshot_sn_no = #{record.snapshotSnNo,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotAssetsCategory != null">
          snapshot_assets_category = #{record.snapshotAssetsCategory,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotAssetsStatus != null">
          snapshot_assets_status = #{record.snapshotAssetsStatus,jdbcType=TINYINT},
        </if>
        <if test="record.snapshotAssetsConditions != null">
          snapshot_assets_conditions = #{record.snapshotAssetsConditions,jdbcType=TINYINT},
        </if>
        <if test="record.snapshotAssetsHolder != null">
          snapshot_assets_holder = #{record.snapshotAssetsHolder,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotHolderTime != null">
          snapshot_holder_time = #{record.snapshotHolderTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.snapshotHolderAddress != null">
          snapshot_holder_address = #{record.snapshotHolderAddress,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotWarehouseCode != null">
          snapshot_warehouse_code = #{record.snapshotWarehouseCode,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotModel != null">
          snapshot_model = #{record.snapshotModel,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotBrand != null">
          snapshot_brand = #{record.snapshotBrand,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotAssetsCpu != null">
          snapshot_assets_cpu = #{record.snapshotAssetsCpu,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotHardDisk != null">
          snapshot_hard_disk = #{record.snapshotHardDisk,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotRamMemory != null">
          snapshot_ram_memory = #{record.snapshotRamMemory,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsCpu != null">
          real_assets_cpu = #{record.realAssetsCpu,jdbcType=VARCHAR},
        </if>
        <if test="record.realHardDisk != null">
          real_hard_disk = #{record.realHardDisk,jdbcType=VARCHAR},
        </if>
        <if test="record.realRamMemory != null">
          real_ram_memory = #{record.realRamMemory,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsCode != null">
          real_assets_code = #{record.realAssetsCode,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsName != null">
          real_assets_name = #{record.realAssetsName,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsStatus != null">
          real_assets_status = #{record.realAssetsStatus,jdbcType=TINYINT},
        </if>
        <if test="record.realAssetsConditions != null">
          real_assets_conditions = #{record.realAssetsConditions,jdbcType=TINYINT},
        </if>
        <if test="record.realAssetsSnno != null">
          real_assets_snno = #{record.realAssetsSnno,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsModel != null">
          real_assets_model = #{record.realAssetsModel,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsBrand != null">
          real_assets_brand = #{record.realAssetsBrand,jdbcType=VARCHAR},
        </if>
        <if test="record.realAssetsHolder != null">
          real_assets_holder = #{record.realAssetsHolder,jdbcType=VARCHAR},
        </if>
        <if test="record.realHolderTime != null">
          real_holder_time = #{record.realHolderTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.realHolderAddress != null">
          real_holder_address = #{record.realHolderAddress,jdbcType=VARCHAR},
        </if>
        <if test="record.realWarehouseCode != null">
          real_warehouse_code = #{record.realWarehouseCode,jdbcType=VARCHAR},
        </if>
        <if test="record.realPicturesUrl != null">
          real_pictures_url = #{record.realPicturesUrl,jdbcType=VARCHAR},
        </if>
        <if test="record.needDept != null">
          need_dept = #{record.needDept,jdbcType=VARCHAR},
        </if>
        <if test="record.costDept != null">
          cost_dept = #{record.costDept,jdbcType=VARCHAR},
        </if>
        <if test="record.holderDept != null">
          holder_dept = #{record.holderDept,jdbcType=VARCHAR},
        </if>
        <if test="record.snapshotNumber != null">
          snapshot_number = #{record.snapshotNumber,jdbcType=INTEGER},
        </if>
        <if test="record.realNumber != null">
          real_number = #{record.realNumber,jdbcType=INTEGER},
        </if>
        <if test="record.difference != null">
          difference = #{record.difference,jdbcType=TINYINT},
        </if>
        <if test="record.ownFlag != null">
          own_flag = #{record.ownFlag,jdbcType=TINYINT},
        </if>
        <if test="record.errMessage != null">
          err_message = #{record.errMessage,jdbcType=VARCHAR},
        </if>
        <if test="record.errDesc != null">
          err_desc = #{record.errDesc,jdbcType=VARCHAR},
        </if>
        <if test="record.newInsertFlag != null">
          new_insert_flag = #{record.newInsertFlag,jdbcType=TINYINT},
        </if>
        <if test="record.checkFlag != null">
          check_flag = #{record.checkFlag,jdbcType=TINYINT},
        </if>
        <if test="record.apvFormId != null">
          apv_form_id = #{record.apvFormId,jdbcType=VARCHAR},
        </if>
        <if test="record.apvTaskId != null">
          apv_task_id = #{record.apvTaskId,jdbcType=VARCHAR},
        </if>
        <if test="record.remark != null">
          remark = #{record.remark,jdbcType=VARCHAR},
        </if>
        <if test="record.createdBy != null">
          created_by = #{record.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="record.createdAt != null">
          created_at = #{record.createdAt,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updatedBy != null">
          updated_by = #{record.updatedBy,jdbcType=VARCHAR},
        </if>
        <if test="record.updatedAt != null">
          updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
        </if>
        <if test="record.apvStatus != null">
          apv_status = #{record.apvStatus,jdbcType=INTEGER},
        </if>
        <if test="record.approveTime != null">
          approve_time = #{record.approveTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.dealResult != null">
          deal_result = #{record.dealResult,jdbcType=VARCHAR},
        </if>
        <if test="record.checkTime != null">
          check_time = #{record.checkTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.isNeedTakePicture != null">
          is_need_take_picture = #{record.isNeedTakePicture,jdbcType=INTEGER},
        </if>
        <if test="record.pictureUrlList != null">
          picture_url_list = #{record.pictureUrlList,jdbcType=VARCHAR},
        </if>
        <if test="record.isAutoCheck != null">
          is_auto_check = #{record.isAutoCheck,jdbcType=INTEGER},
        </if>
        <if test="record.guaguaLoginSn != null">
          guagua_login_sn = #{record.guaguaLoginSn,jdbcType=VARCHAR},
        </if>
      </set>
      where task_detail_id = #{record.taskDetailId,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>