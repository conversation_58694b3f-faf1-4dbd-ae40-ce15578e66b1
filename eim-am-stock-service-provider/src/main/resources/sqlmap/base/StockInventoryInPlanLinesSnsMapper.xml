<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockInventoryInPlanLinesSnsMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSns">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inventory_in_plan_head_id" jdbcType="BIGINT" property="inventoryInPlanHeadId" />
    <result column="inventory_in_plan_line_id" jdbcType="BIGINT" property="inventoryInPlanLineId" />
    <result column="sn_no" jdbcType="VARCHAR" property="snNo" />
    <result column="in_stock_status" jdbcType="TINYINT" property="inStockStatus" />
    <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, inventory_in_plan_head_id, inventory_in_plan_line_id, sn_no, in_stock_status, 
    data_status, del_flag, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSnsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_inventory_in_plan_lines_sns
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_inventory_in_plan_lines_sns
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_inventory_in_plan_lines_sns
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSns">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_inventory_in_plan_lines_sns (inventory_in_plan_head_id, inventory_in_plan_line_id, 
      sn_no, in_stock_status, data_status, 
      del_flag, CREATED_BY, CREATED_AT, 
      UPDATED_BY, UPDATED_AT)
    values (#{inventoryInPlanHeadId,jdbcType=BIGINT}, #{inventoryInPlanLineId,jdbcType=BIGINT}, 
      #{snNo,jdbcType=VARCHAR}, #{inStockStatus,jdbcType=TINYINT}, #{dataStatus,jdbcType=TINYINT}, 
      #{delFlag,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSns">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_inventory_in_plan_lines_sns
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="inventoryInPlanHeadId != null">
        inventory_in_plan_head_id,
      </if>
      <if test="inventoryInPlanLineId != null">
        inventory_in_plan_line_id,
      </if>
      <if test="snNo != null">
        sn_no,
      </if>
      <if test="inStockStatus != null">
        in_stock_status,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="inventoryInPlanHeadId != null">
        #{inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="inventoryInPlanLineId != null">
        #{inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
      <if test="snNo != null">
        #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="inStockStatus != null">
        #{inStockStatus,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSnsExample" resultType="java.lang.Long">
    select count(*) from stock_inventory_in_plan_lines_sns
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_inventory_in_plan_lines_sns
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.inventoryInPlanHeadId != null">
        inventory_in_plan_head_id = #{record.inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="record.inventoryInPlanLineId != null">
        inventory_in_plan_line_id = #{record.inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
      <if test="record.snNo != null">
        sn_no = #{record.snNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inStockStatus != null">
        in_stock_status = #{record.inStockStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dataStatus != null">
        data_status = #{record.dataStatus,jdbcType=TINYINT},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_inventory_in_plan_lines_sns
    set id = #{record.id,jdbcType=BIGINT},
      inventory_in_plan_head_id = #{record.inventoryInPlanHeadId,jdbcType=BIGINT},
      inventory_in_plan_line_id = #{record.inventoryInPlanLineId,jdbcType=BIGINT},
      sn_no = #{record.snNo,jdbcType=VARCHAR},
      in_stock_status = #{record.inStockStatus,jdbcType=TINYINT},
      data_status = #{record.dataStatus,jdbcType=TINYINT},
      del_flag = #{record.delFlag,jdbcType=TINYINT},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSns">
    update stock_inventory_in_plan_lines_sns
    <set>
      <if test="inventoryInPlanHeadId != null">
        inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="inventoryInPlanLineId != null">
        inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
      <if test="snNo != null">
        sn_no = #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="inStockStatus != null">
        in_stock_status = #{inStockStatus,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesSns">
    update stock_inventory_in_plan_lines_sns
    set inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT},
      inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT},
      sn_no = #{snNo,jdbcType=VARCHAR},
      in_stock_status = #{inStockStatus,jdbcType=TINYINT},
      data_status = #{dataStatus,jdbcType=TINYINT},
      del_flag = #{delFlag,jdbcType=TINYINT},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>