<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssets">
    <id column="assets_id" jdbcType="BIGINT" property="assetsId" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="assets_name" jdbcType="VARCHAR" property="assetsName" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="sn_code" jdbcType="VARCHAR" property="snCode" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="assets_deploy" jdbcType="VARCHAR" property="assetsDeploy" />
    <result column="has_sub" jdbcType="TINYINT" property="hasSub" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="conditions" jdbcType="TINYINT" property="conditions" />
    <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
    <result column="net_value" jdbcType="DECIMAL" property="netValue" />
    <result column="estimated_amount" jdbcType="DECIMAL" property="estimatedAmount" />
    <result column="initial_value" jdbcType="DECIMAL" property="initialValue" />
    <result column="scrap_value" jdbcType="DECIMAL" property="scrapValue" />
    <result column="purchase_type" jdbcType="TINYINT" property="purchaseType" />
    <result column="purchase_no" jdbcType="VARCHAR" property="purchaseNo" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
    <result column="depreciation_way" jdbcType="TINYINT" property="depreciationWay" />
    <result column="cost_dept" jdbcType="VARCHAR" property="costDept" />
    <result column="need_dept" jdbcType="VARCHAR" property="needDept" />
    <result column="holder" jdbcType="VARCHAR" property="holder" />
    <result column="holder_dept" jdbcType="VARCHAR" property="holderDept" />
    <result column="holder_address" jdbcType="VARCHAR" property="holderAddress" />
    <result column="holder_address_province" jdbcType="VARCHAR" property="holderAddressProvince" />
    <result column="holder_address_city" jdbcType="VARCHAR" property="holderAddressCity" />
    <result column="holder_address_county" jdbcType="VARCHAR" property="holderAddressCounty" />
    <result column="holder_time" jdbcType="TIMESTAMP" property="holderTime" />
    <result column="purchase_time" jdbcType="TIMESTAMP" property="purchaseTime" />
    <result column="start_using_time" jdbcType="TIMESTAMP" property="startUsingTime" />
    <result column="storage_time" jdbcType="TIMESTAMP" property="storageTime" />
    <result column="plan_handle_time" jdbcType="TIMESTAMP" property="planHandleTime" />
    <result column="plan_return_time" jdbcType="TIMESTAMP" property="planReturnTime" />
    <result column="use_year_limit" jdbcType="INTEGER" property="useYearLimit" />
    <result column="regular_maintain" jdbcType="TINYINT" property="regularMaintain" />
    <result column="maintain_cycle" jdbcType="VARCHAR" property="maintainCycle" />
    <result column="assets_keeper" jdbcType="VARCHAR" property="assetsKeeper" />
    <result column="assets_pic" jdbcType="VARCHAR" property="assetsPic" />
    <result column="saas_assets_code" jdbcType="VARCHAR" property="saasAssetsCode" />
    <result column="saas_address" jdbcType="VARCHAR" property="saasAddress" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="label_url" jdbcType="VARCHAR" property="labelUrl" />
    <result column="Purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="Purchase_order_line_id" jdbcType="BIGINT" property="purchaseOrderLineId" />
    <result column="Receive_item_no" jdbcType="VARCHAR" property="receiveItemNo" />
    <result column="accountant_code" jdbcType="VARCHAR" property="accountantCode" />
    <result column="accountant_line_code" jdbcType="VARCHAR" property="accountantLineCode" />
    <result column="pay_date" jdbcType="TIMESTAMP" property="payDate" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="Currency" jdbcType="VARCHAR" property="currency" />
    <result column="Default_dispose_cost" jdbcType="DECIMAL" property="defaultDisposeCost" />
    <result column="Unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="Price_excluding_tax" jdbcType="DECIMAL" property="priceExcludingTax" />
    <result column="statement_unit_price" jdbcType="DECIMAL" property="statementUnitPrice" />
    <result column="statement_price_excluding_tax" jdbcType="DECIMAL" property="statementPriceExcludingTax" />
    <result column="Extract_status" jdbcType="TINYINT" property="extractStatus" />
    <result column="ext_cpu" jdbcType="VARCHAR" property="extCpu" />
    <result column="ext_hard_disk" jdbcType="VARCHAR" property="extHardDisk" />
    <result column="ext_ram_memory" jdbcType="VARCHAR" property="extRamMemory" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="explain_remark" jdbcType="VARCHAR" property="explainRemark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    assets_id, assets_code, assets_name, supplies_code, warehouse_code, sn_code, device_code, 
    brand, model, unit, category, category_code, assets_deploy, has_sub, company_code, 
    company_name, status, conditions, approve_status, net_value, estimated_amount, initial_value, 
    scrap_value, purchase_type, purchase_no, vendor_code, vendor_name, depreciation_way, 
    cost_dept, need_dept, holder, holder_dept, holder_address, holder_address_province, 
    holder_address_city, holder_address_county, holder_time, purchase_time, start_using_time, 
    storage_time, plan_handle_time, plan_return_time, use_year_limit, regular_maintain, 
    maintain_cycle, assets_keeper, assets_pic, saas_assets_code, saas_address, remark, 
    label_url, Purchase_order_no, Purchase_order_line_id, Receive_item_no, accountant_code, 
    accountant_line_code, pay_date, bill_code, invoice_type, Currency, Default_dispose_cost, 
    Unit_price, Price_excluding_tax, statement_unit_price, statement_price_excluding_tax, 
    Extract_status, ext_cpu, ext_hard_disk, ext_ram_memory, created_by, created_at, updated_by, 
    updated_at, explain_remark
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets
    where assets_id = #{assetsId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets
    where assets_id = #{assetsId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssets">
    <selectKey keyProperty="assetsId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets (assets_code, assets_name, supplies_code, 
      warehouse_code, sn_code, device_code, 
      brand, model, unit, 
      category, category_code, assets_deploy, 
      has_sub, company_code, company_name, 
      status, conditions, approve_status, 
      net_value, estimated_amount, initial_value, 
      scrap_value, purchase_type, purchase_no, 
      vendor_code, vendor_name, depreciation_way, 
      cost_dept, need_dept, holder, 
      holder_dept, holder_address, holder_address_province, 
      holder_address_city, holder_address_county, 
      holder_time, purchase_time, start_using_time, 
      storage_time, plan_handle_time, plan_return_time, 
      use_year_limit, regular_maintain, maintain_cycle, 
      assets_keeper, assets_pic, saas_assets_code, 
      saas_address, remark, label_url, 
      Purchase_order_no, Purchase_order_line_id, Receive_item_no, 
      accountant_code, accountant_line_code, pay_date, 
      bill_code, invoice_type, Currency, 
      Default_dispose_cost, Unit_price, Price_excluding_tax, 
      statement_unit_price, statement_price_excluding_tax, 
      Extract_status, ext_cpu, ext_hard_disk, 
      ext_ram_memory, created_by, created_at, 
      updated_by, updated_at, explain_remark
      )
    values (#{assetsCode,jdbcType=VARCHAR}, #{assetsName,jdbcType=VARCHAR}, #{suppliesCode,jdbcType=VARCHAR}, 
      #{warehouseCode,jdbcType=VARCHAR}, #{snCode,jdbcType=VARCHAR}, #{deviceCode,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, 
      #{category,jdbcType=VARCHAR}, #{categoryCode,jdbcType=VARCHAR}, #{assetsDeploy,jdbcType=VARCHAR}, 
      #{hasSub,jdbcType=TINYINT}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{conditions,jdbcType=TINYINT}, #{approveStatus,jdbcType=TINYINT}, 
      #{netValue,jdbcType=DECIMAL}, #{estimatedAmount,jdbcType=DECIMAL}, #{initialValue,jdbcType=DECIMAL}, 
      #{scrapValue,jdbcType=DECIMAL}, #{purchaseType,jdbcType=TINYINT}, #{purchaseNo,jdbcType=VARCHAR}, 
      #{vendorCode,jdbcType=VARCHAR}, #{vendorName,jdbcType=VARCHAR}, #{depreciationWay,jdbcType=TINYINT}, 
      #{costDept,jdbcType=VARCHAR}, #{needDept,jdbcType=VARCHAR}, #{holder,jdbcType=VARCHAR}, 
      #{holderDept,jdbcType=VARCHAR}, #{holderAddress,jdbcType=VARCHAR}, #{holderAddressProvince,jdbcType=VARCHAR}, 
      #{holderAddressCity,jdbcType=VARCHAR}, #{holderAddressCounty,jdbcType=VARCHAR}, 
      #{holderTime,jdbcType=TIMESTAMP}, #{purchaseTime,jdbcType=TIMESTAMP}, #{startUsingTime,jdbcType=TIMESTAMP}, 
      #{storageTime,jdbcType=TIMESTAMP}, #{planHandleTime,jdbcType=TIMESTAMP}, #{planReturnTime,jdbcType=TIMESTAMP}, 
      #{useYearLimit,jdbcType=INTEGER}, #{regularMaintain,jdbcType=TINYINT}, #{maintainCycle,jdbcType=VARCHAR}, 
      #{assetsKeeper,jdbcType=VARCHAR}, #{assetsPic,jdbcType=VARCHAR}, #{saasAssetsCode,jdbcType=VARCHAR}, 
      #{saasAddress,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{labelUrl,jdbcType=VARCHAR}, 
      #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderLineId,jdbcType=BIGINT}, #{receiveItemNo,jdbcType=VARCHAR}, 
      #{accountantCode,jdbcType=VARCHAR}, #{accountantLineCode,jdbcType=VARCHAR}, #{payDate,jdbcType=TIMESTAMP}, 
      #{billCode,jdbcType=VARCHAR}, #{invoiceType,jdbcType=INTEGER}, #{currency,jdbcType=VARCHAR}, 
      #{defaultDisposeCost,jdbcType=DECIMAL}, #{unitPrice,jdbcType=DECIMAL}, #{priceExcludingTax,jdbcType=DECIMAL}, 
      #{statementUnitPrice,jdbcType=DECIMAL}, #{statementPriceExcludingTax,jdbcType=DECIMAL}, 
      #{extractStatus,jdbcType=TINYINT}, #{extCpu,jdbcType=VARCHAR}, #{extHardDisk,jdbcType=VARCHAR}, 
      #{extRamMemory,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, #{explainRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssets">
    <selectKey keyProperty="assetsId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="assetsName != null">
        assets_name,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="snCode != null">
        sn_code,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="assetsDeploy != null">
        assets_deploy,
      </if>
      <if test="hasSub != null">
        has_sub,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="conditions != null">
        conditions,
      </if>
      <if test="approveStatus != null">
        approve_status,
      </if>
      <if test="netValue != null">
        net_value,
      </if>
      <if test="estimatedAmount != null">
        estimated_amount,
      </if>
      <if test="initialValue != null">
        initial_value,
      </if>
      <if test="scrapValue != null">
        scrap_value,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="purchaseNo != null">
        purchase_no,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="vendorName != null">
        vendor_name,
      </if>
      <if test="depreciationWay != null">
        depreciation_way,
      </if>
      <if test="costDept != null">
        cost_dept,
      </if>
      <if test="needDept != null">
        need_dept,
      </if>
      <if test="holder != null">
        holder,
      </if>
      <if test="holderDept != null">
        holder_dept,
      </if>
      <if test="holderAddress != null">
        holder_address,
      </if>
      <if test="holderAddressProvince != null">
        holder_address_province,
      </if>
      <if test="holderAddressCity != null">
        holder_address_city,
      </if>
      <if test="holderAddressCounty != null">
        holder_address_county,
      </if>
      <if test="holderTime != null">
        holder_time,
      </if>
      <if test="purchaseTime != null">
        purchase_time,
      </if>
      <if test="startUsingTime != null">
        start_using_time,
      </if>
      <if test="storageTime != null">
        storage_time,
      </if>
      <if test="planHandleTime != null">
        plan_handle_time,
      </if>
      <if test="planReturnTime != null">
        plan_return_time,
      </if>
      <if test="useYearLimit != null">
        use_year_limit,
      </if>
      <if test="regularMaintain != null">
        regular_maintain,
      </if>
      <if test="maintainCycle != null">
        maintain_cycle,
      </if>
      <if test="assetsKeeper != null">
        assets_keeper,
      </if>
      <if test="assetsPic != null">
        assets_pic,
      </if>
      <if test="saasAssetsCode != null">
        saas_assets_code,
      </if>
      <if test="saasAddress != null">
        saas_address,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="labelUrl != null">
        label_url,
      </if>
      <if test="purchaseOrderNo != null">
        Purchase_order_no,
      </if>
      <if test="purchaseOrderLineId != null">
        Purchase_order_line_id,
      </if>
      <if test="receiveItemNo != null">
        Receive_item_no,
      </if>
      <if test="accountantCode != null">
        accountant_code,
      </if>
      <if test="accountantLineCode != null">
        accountant_line_code,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="billCode != null">
        bill_code,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="currency != null">
        Currency,
      </if>
      <if test="defaultDisposeCost != null">
        Default_dispose_cost,
      </if>
      <if test="unitPrice != null">
        Unit_price,
      </if>
      <if test="priceExcludingTax != null">
        Price_excluding_tax,
      </if>
      <if test="statementUnitPrice != null">
        statement_unit_price,
      </if>
      <if test="statementPriceExcludingTax != null">
        statement_price_excluding_tax,
      </if>
      <if test="extractStatus != null">
        Extract_status,
      </if>
      <if test="extCpu != null">
        ext_cpu,
      </if>
      <if test="extHardDisk != null">
        ext_hard_disk,
      </if>
      <if test="extRamMemory != null">
        ext_ram_memory,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="explainRemark != null">
        explain_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="assetsName != null">
        #{assetsName,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="snCode != null">
        #{snCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="assetsDeploy != null">
        #{assetsDeploy,jdbcType=VARCHAR},
      </if>
      <if test="hasSub != null">
        #{hasSub,jdbcType=TINYINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="conditions != null">
        #{conditions,jdbcType=TINYINT},
      </if>
      <if test="approveStatus != null">
        #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="netValue != null">
        #{netValue,jdbcType=DECIMAL},
      </if>
      <if test="estimatedAmount != null">
        #{estimatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="initialValue != null">
        #{initialValue,jdbcType=DECIMAL},
      </if>
      <if test="scrapValue != null">
        #{scrapValue,jdbcType=DECIMAL},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=TINYINT},
      </if>
      <if test="purchaseNo != null">
        #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="depreciationWay != null">
        #{depreciationWay,jdbcType=TINYINT},
      </if>
      <if test="costDept != null">
        #{costDept,jdbcType=VARCHAR},
      </if>
      <if test="needDept != null">
        #{needDept,jdbcType=VARCHAR},
      </if>
      <if test="holder != null">
        #{holder,jdbcType=VARCHAR},
      </if>
      <if test="holderDept != null">
        #{holderDept,jdbcType=VARCHAR},
      </if>
      <if test="holderAddress != null">
        #{holderAddress,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressProvince != null">
        #{holderAddressProvince,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCity != null">
        #{holderAddressCity,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCounty != null">
        #{holderAddressCounty,jdbcType=VARCHAR},
      </if>
      <if test="holderTime != null">
        #{holderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startUsingTime != null">
        #{startUsingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storageTime != null">
        #{storageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planHandleTime != null">
        #{planHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planReturnTime != null">
        #{planReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useYearLimit != null">
        #{useYearLimit,jdbcType=INTEGER},
      </if>
      <if test="regularMaintain != null">
        #{regularMaintain,jdbcType=TINYINT},
      </if>
      <if test="maintainCycle != null">
        #{maintainCycle,jdbcType=VARCHAR},
      </if>
      <if test="assetsKeeper != null">
        #{assetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="assetsPic != null">
        #{assetsPic,jdbcType=VARCHAR},
      </if>
      <if test="saasAssetsCode != null">
        #{saasAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="saasAddress != null">
        #{saasAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="labelUrl != null">
        #{labelUrl,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderLineId != null">
        #{purchaseOrderLineId,jdbcType=BIGINT},
      </if>
      <if test="receiveItemNo != null">
        #{receiveItemNo,jdbcType=VARCHAR},
      </if>
      <if test="accountantCode != null">
        #{accountantCode,jdbcType=VARCHAR},
      </if>
      <if test="accountantLineCode != null">
        #{accountantLineCode,jdbcType=VARCHAR},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="billCode != null">
        #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="defaultDisposeCost != null">
        #{defaultDisposeCost,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="priceExcludingTax != null">
        #{priceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="statementUnitPrice != null">
        #{statementUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="statementPriceExcludingTax != null">
        #{statementPriceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="extractStatus != null">
        #{extractStatus,jdbcType=TINYINT},
      </if>
      <if test="extCpu != null">
        #{extCpu,jdbcType=VARCHAR},
      </if>
      <if test="extHardDisk != null">
        #{extHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="extRamMemory != null">
        #{extRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="explainRemark != null">
        #{explainRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsExample" resultType="java.lang.Long">
    select count(*) from stock_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets
    <set>
      <if test="record.assetsId != null">
        assets_id = #{record.assetsId,jdbcType=BIGINT},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsName != null">
        assets_name = #{record.assetsName,jdbcType=VARCHAR},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.snCode != null">
        sn_code = #{record.snCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceCode != null">
        device_code = #{record.deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCode != null">
        category_code = #{record.categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsDeploy != null">
        assets_deploy = #{record.assetsDeploy,jdbcType=VARCHAR},
      </if>
      <if test="record.hasSub != null">
        has_sub = #{record.hasSub,jdbcType=TINYINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.conditions != null">
        conditions = #{record.conditions,jdbcType=TINYINT},
      </if>
      <if test="record.approveStatus != null">
        approve_status = #{record.approveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.netValue != null">
        net_value = #{record.netValue,jdbcType=DECIMAL},
      </if>
      <if test="record.estimatedAmount != null">
        estimated_amount = #{record.estimatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.initialValue != null">
        initial_value = #{record.initialValue,jdbcType=DECIMAL},
      </if>
      <if test="record.scrapValue != null">
        scrap_value = #{record.scrapValue,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=TINYINT},
      </if>
      <if test="record.purchaseNo != null">
        purchase_no = #{record.purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorCode != null">
        vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorName != null">
        vendor_name = #{record.vendorName,jdbcType=VARCHAR},
      </if>
      <if test="record.depreciationWay != null">
        depreciation_way = #{record.depreciationWay,jdbcType=TINYINT},
      </if>
      <if test="record.costDept != null">
        cost_dept = #{record.costDept,jdbcType=VARCHAR},
      </if>
      <if test="record.needDept != null">
        need_dept = #{record.needDept,jdbcType=VARCHAR},
      </if>
      <if test="record.holder != null">
        holder = #{record.holder,jdbcType=VARCHAR},
      </if>
      <if test="record.holderDept != null">
        holder_dept = #{record.holderDept,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddress != null">
        holder_address = #{record.holderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddressProvince != null">
        holder_address_province = #{record.holderAddressProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddressCity != null">
        holder_address_city = #{record.holderAddressCity,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddressCounty != null">
        holder_address_county = #{record.holderAddressCounty,jdbcType=VARCHAR},
      </if>
      <if test="record.holderTime != null">
        holder_time = #{record.holderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseTime != null">
        purchase_time = #{record.purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startUsingTime != null">
        start_using_time = #{record.startUsingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.storageTime != null">
        storage_time = #{record.storageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planHandleTime != null">
        plan_handle_time = #{record.planHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planReturnTime != null">
        plan_return_time = #{record.planReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.useYearLimit != null">
        use_year_limit = #{record.useYearLimit,jdbcType=INTEGER},
      </if>
      <if test="record.regularMaintain != null">
        regular_maintain = #{record.regularMaintain,jdbcType=TINYINT},
      </if>
      <if test="record.maintainCycle != null">
        maintain_cycle = #{record.maintainCycle,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsKeeper != null">
        assets_keeper = #{record.assetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsPic != null">
        assets_pic = #{record.assetsPic,jdbcType=VARCHAR},
      </if>
      <if test="record.saasAssetsCode != null">
        saas_assets_code = #{record.saasAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.saasAddress != null">
        saas_address = #{record.saasAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.labelUrl != null">
        label_url = #{record.labelUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        Purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderLineId != null">
        Purchase_order_line_id = #{record.purchaseOrderLineId,jdbcType=BIGINT},
      </if>
      <if test="record.receiveItemNo != null">
        Receive_item_no = #{record.receiveItemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accountantCode != null">
        accountant_code = #{record.accountantCode,jdbcType=VARCHAR},
      </if>
      <if test="record.accountantLineCode != null">
        accountant_line_code = #{record.accountantLineCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payDate != null">
        pay_date = #{record.payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.billCode != null">
        bill_code = #{record.billCode,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceType != null">
        invoice_type = #{record.invoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.currency != null">
        Currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultDisposeCost != null">
        Default_dispose_cost = #{record.defaultDisposeCost,jdbcType=DECIMAL},
      </if>
      <if test="record.unitPrice != null">
        Unit_price = #{record.unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.priceExcludingTax != null">
        Price_excluding_tax = #{record.priceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="record.statementUnitPrice != null">
        statement_unit_price = #{record.statementUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.statementPriceExcludingTax != null">
        statement_price_excluding_tax = #{record.statementPriceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="record.extractStatus != null">
        Extract_status = #{record.extractStatus,jdbcType=TINYINT},
      </if>
      <if test="record.extCpu != null">
        ext_cpu = #{record.extCpu,jdbcType=VARCHAR},
      </if>
      <if test="record.extHardDisk != null">
        ext_hard_disk = #{record.extHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="record.extRamMemory != null">
        ext_ram_memory = #{record.extRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.explainRemark != null">
        explain_remark = #{record.explainRemark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets
    set assets_id = #{record.assetsId,jdbcType=BIGINT},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      assets_name = #{record.assetsName,jdbcType=VARCHAR},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      sn_code = #{record.snCode,jdbcType=VARCHAR},
      device_code = #{record.deviceCode,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      category_code = #{record.categoryCode,jdbcType=VARCHAR},
      assets_deploy = #{record.assetsDeploy,jdbcType=VARCHAR},
      has_sub = #{record.hasSub,jdbcType=TINYINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      conditions = #{record.conditions,jdbcType=TINYINT},
      approve_status = #{record.approveStatus,jdbcType=TINYINT},
      net_value = #{record.netValue,jdbcType=DECIMAL},
      estimated_amount = #{record.estimatedAmount,jdbcType=DECIMAL},
      initial_value = #{record.initialValue,jdbcType=DECIMAL},
      scrap_value = #{record.scrapValue,jdbcType=DECIMAL},
      purchase_type = #{record.purchaseType,jdbcType=TINYINT},
      purchase_no = #{record.purchaseNo,jdbcType=VARCHAR},
      vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      vendor_name = #{record.vendorName,jdbcType=VARCHAR},
      depreciation_way = #{record.depreciationWay,jdbcType=TINYINT},
      cost_dept = #{record.costDept,jdbcType=VARCHAR},
      need_dept = #{record.needDept,jdbcType=VARCHAR},
      holder = #{record.holder,jdbcType=VARCHAR},
      holder_dept = #{record.holderDept,jdbcType=VARCHAR},
      holder_address = #{record.holderAddress,jdbcType=VARCHAR},
      holder_address_province = #{record.holderAddressProvince,jdbcType=VARCHAR},
      holder_address_city = #{record.holderAddressCity,jdbcType=VARCHAR},
      holder_address_county = #{record.holderAddressCounty,jdbcType=VARCHAR},
      holder_time = #{record.holderTime,jdbcType=TIMESTAMP},
      purchase_time = #{record.purchaseTime,jdbcType=TIMESTAMP},
      start_using_time = #{record.startUsingTime,jdbcType=TIMESTAMP},
      storage_time = #{record.storageTime,jdbcType=TIMESTAMP},
      plan_handle_time = #{record.planHandleTime,jdbcType=TIMESTAMP},
      plan_return_time = #{record.planReturnTime,jdbcType=TIMESTAMP},
      use_year_limit = #{record.useYearLimit,jdbcType=INTEGER},
      regular_maintain = #{record.regularMaintain,jdbcType=TINYINT},
      maintain_cycle = #{record.maintainCycle,jdbcType=VARCHAR},
      assets_keeper = #{record.assetsKeeper,jdbcType=VARCHAR},
      assets_pic = #{record.assetsPic,jdbcType=VARCHAR},
      saas_assets_code = #{record.saasAssetsCode,jdbcType=VARCHAR},
      saas_address = #{record.saasAddress,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      label_url = #{record.labelUrl,jdbcType=VARCHAR},
      Purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      Purchase_order_line_id = #{record.purchaseOrderLineId,jdbcType=BIGINT},
      Receive_item_no = #{record.receiveItemNo,jdbcType=VARCHAR},
      accountant_code = #{record.accountantCode,jdbcType=VARCHAR},
      accountant_line_code = #{record.accountantLineCode,jdbcType=VARCHAR},
      pay_date = #{record.payDate,jdbcType=TIMESTAMP},
      bill_code = #{record.billCode,jdbcType=VARCHAR},
      invoice_type = #{record.invoiceType,jdbcType=INTEGER},
      Currency = #{record.currency,jdbcType=VARCHAR},
      Default_dispose_cost = #{record.defaultDisposeCost,jdbcType=DECIMAL},
      Unit_price = #{record.unitPrice,jdbcType=DECIMAL},
      Price_excluding_tax = #{record.priceExcludingTax,jdbcType=DECIMAL},
      statement_unit_price = #{record.statementUnitPrice,jdbcType=DECIMAL},
      statement_price_excluding_tax = #{record.statementPriceExcludingTax,jdbcType=DECIMAL},
      Extract_status = #{record.extractStatus,jdbcType=TINYINT},
      ext_cpu = #{record.extCpu,jdbcType=VARCHAR},
      ext_hard_disk = #{record.extHardDisk,jdbcType=VARCHAR},
      ext_ram_memory = #{record.extRamMemory,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      explain_remark = #{record.explainRemark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssets">
    update stock_assets
    <set>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="assetsName != null">
        assets_name = #{assetsName,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="snCode != null">
        sn_code = #{snCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="assetsDeploy != null">
        assets_deploy = #{assetsDeploy,jdbcType=VARCHAR},
      </if>
      <if test="hasSub != null">
        has_sub = #{hasSub,jdbcType=TINYINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="conditions != null">
        conditions = #{conditions,jdbcType=TINYINT},
      </if>
      <if test="approveStatus != null">
        approve_status = #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="netValue != null">
        net_value = #{netValue,jdbcType=DECIMAL},
      </if>
      <if test="estimatedAmount != null">
        estimated_amount = #{estimatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="initialValue != null">
        initial_value = #{initialValue,jdbcType=DECIMAL},
      </if>
      <if test="scrapValue != null">
        scrap_value = #{scrapValue,jdbcType=DECIMAL},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=TINYINT},
      </if>
      <if test="purchaseNo != null">
        purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        vendor_name = #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="depreciationWay != null">
        depreciation_way = #{depreciationWay,jdbcType=TINYINT},
      </if>
      <if test="costDept != null">
        cost_dept = #{costDept,jdbcType=VARCHAR},
      </if>
      <if test="needDept != null">
        need_dept = #{needDept,jdbcType=VARCHAR},
      </if>
      <if test="holder != null">
        holder = #{holder,jdbcType=VARCHAR},
      </if>
      <if test="holderDept != null">
        holder_dept = #{holderDept,jdbcType=VARCHAR},
      </if>
      <if test="holderAddress != null">
        holder_address = #{holderAddress,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressProvince != null">
        holder_address_province = #{holderAddressProvince,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCity != null">
        holder_address_city = #{holderAddressCity,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCounty != null">
        holder_address_county = #{holderAddressCounty,jdbcType=VARCHAR},
      </if>
      <if test="holderTime != null">
        holder_time = #{holderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseTime != null">
        purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startUsingTime != null">
        start_using_time = #{startUsingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storageTime != null">
        storage_time = #{storageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planHandleTime != null">
        plan_handle_time = #{planHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planReturnTime != null">
        plan_return_time = #{planReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="useYearLimit != null">
        use_year_limit = #{useYearLimit,jdbcType=INTEGER},
      </if>
      <if test="regularMaintain != null">
        regular_maintain = #{regularMaintain,jdbcType=TINYINT},
      </if>
      <if test="maintainCycle != null">
        maintain_cycle = #{maintainCycle,jdbcType=VARCHAR},
      </if>
      <if test="assetsKeeper != null">
        assets_keeper = #{assetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="assetsPic != null">
        assets_pic = #{assetsPic,jdbcType=VARCHAR},
      </if>
      <if test="saasAssetsCode != null">
        saas_assets_code = #{saasAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="saasAddress != null">
        saas_address = #{saasAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="labelUrl != null">
        label_url = #{labelUrl,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        Purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderLineId != null">
        Purchase_order_line_id = #{purchaseOrderLineId,jdbcType=BIGINT},
      </if>
      <if test="receiveItemNo != null">
        Receive_item_no = #{receiveItemNo,jdbcType=VARCHAR},
      </if>
      <if test="accountantCode != null">
        accountant_code = #{accountantCode,jdbcType=VARCHAR},
      </if>
      <if test="accountantLineCode != null">
        accountant_line_code = #{accountantLineCode,jdbcType=VARCHAR},
      </if>
      <if test="payDate != null">
        pay_date = #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="billCode != null">
        bill_code = #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="currency != null">
        Currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="defaultDisposeCost != null">
        Default_dispose_cost = #{defaultDisposeCost,jdbcType=DECIMAL},
      </if>
      <if test="unitPrice != null">
        Unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="priceExcludingTax != null">
        Price_excluding_tax = #{priceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="statementUnitPrice != null">
        statement_unit_price = #{statementUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="statementPriceExcludingTax != null">
        statement_price_excluding_tax = #{statementPriceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="extractStatus != null">
        Extract_status = #{extractStatus,jdbcType=TINYINT},
      </if>
      <if test="extCpu != null">
        ext_cpu = #{extCpu,jdbcType=VARCHAR},
      </if>
      <if test="extHardDisk != null">
        ext_hard_disk = #{extHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="extRamMemory != null">
        ext_ram_memory = #{extRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="explainRemark != null">
        explain_remark = #{explainRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where assets_id = #{assetsId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssets">
    update stock_assets
    set assets_code = #{assetsCode,jdbcType=VARCHAR},
      assets_name = #{assetsName,jdbcType=VARCHAR},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      sn_code = #{snCode,jdbcType=VARCHAR},
      device_code = #{deviceCode,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      category_code = #{categoryCode,jdbcType=VARCHAR},
      assets_deploy = #{assetsDeploy,jdbcType=VARCHAR},
      has_sub = #{hasSub,jdbcType=TINYINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      conditions = #{conditions,jdbcType=TINYINT},
      approve_status = #{approveStatus,jdbcType=TINYINT},
      net_value = #{netValue,jdbcType=DECIMAL},
      estimated_amount = #{estimatedAmount,jdbcType=DECIMAL},
      initial_value = #{initialValue,jdbcType=DECIMAL},
      scrap_value = #{scrapValue,jdbcType=DECIMAL},
      purchase_type = #{purchaseType,jdbcType=TINYINT},
      purchase_no = #{purchaseNo,jdbcType=VARCHAR},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      depreciation_way = #{depreciationWay,jdbcType=TINYINT},
      cost_dept = #{costDept,jdbcType=VARCHAR},
      need_dept = #{needDept,jdbcType=VARCHAR},
      holder = #{holder,jdbcType=VARCHAR},
      holder_dept = #{holderDept,jdbcType=VARCHAR},
      holder_address = #{holderAddress,jdbcType=VARCHAR},
      holder_address_province = #{holderAddressProvince,jdbcType=VARCHAR},
      holder_address_city = #{holderAddressCity,jdbcType=VARCHAR},
      holder_address_county = #{holderAddressCounty,jdbcType=VARCHAR},
      holder_time = #{holderTime,jdbcType=TIMESTAMP},
      purchase_time = #{purchaseTime,jdbcType=TIMESTAMP},
      start_using_time = #{startUsingTime,jdbcType=TIMESTAMP},
      storage_time = #{storageTime,jdbcType=TIMESTAMP},
      plan_handle_time = #{planHandleTime,jdbcType=TIMESTAMP},
      plan_return_time = #{planReturnTime,jdbcType=TIMESTAMP},
      use_year_limit = #{useYearLimit,jdbcType=INTEGER},
      regular_maintain = #{regularMaintain,jdbcType=TINYINT},
      maintain_cycle = #{maintainCycle,jdbcType=VARCHAR},
      assets_keeper = #{assetsKeeper,jdbcType=VARCHAR},
      assets_pic = #{assetsPic,jdbcType=VARCHAR},
      saas_assets_code = #{saasAssetsCode,jdbcType=VARCHAR},
      saas_address = #{saasAddress,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      label_url = #{labelUrl,jdbcType=VARCHAR},
      Purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      Purchase_order_line_id = #{purchaseOrderLineId,jdbcType=BIGINT},
      Receive_item_no = #{receiveItemNo,jdbcType=VARCHAR},
      accountant_code = #{accountantCode,jdbcType=VARCHAR},
      accountant_line_code = #{accountantLineCode,jdbcType=VARCHAR},
      pay_date = #{payDate,jdbcType=TIMESTAMP},
      bill_code = #{billCode,jdbcType=VARCHAR},
      invoice_type = #{invoiceType,jdbcType=INTEGER},
      Currency = #{currency,jdbcType=VARCHAR},
      Default_dispose_cost = #{defaultDisposeCost,jdbcType=DECIMAL},
      Unit_price = #{unitPrice,jdbcType=DECIMAL},
      Price_excluding_tax = #{priceExcludingTax,jdbcType=DECIMAL},
      statement_unit_price = #{statementUnitPrice,jdbcType=DECIMAL},
      statement_price_excluding_tax = #{statementPriceExcludingTax,jdbcType=DECIMAL},
      Extract_status = #{extractStatus,jdbcType=TINYINT},
      ext_cpu = #{extCpu,jdbcType=VARCHAR},
      ext_hard_disk = #{extHardDisk,jdbcType=VARCHAR},
      ext_ram_memory = #{extRamMemory,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      explain_remark = #{explainRemark,jdbcType=VARCHAR}
    where assets_id = #{assetsId,jdbcType=BIGINT}
  </update>
</mapper>