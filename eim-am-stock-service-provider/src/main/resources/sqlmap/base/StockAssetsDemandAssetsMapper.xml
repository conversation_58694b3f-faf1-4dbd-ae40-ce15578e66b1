<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsDemandAssetsMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsDemandAssets">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="demand_no" jdbcType="VARCHAR" property="demandNo" />
    <result column="delivery_plan_no" jdbcType="VARCHAR" property="deliveryPlanNo" />
    <result column="delivery_method" jdbcType="INTEGER" property="deliveryMethod" />
    <result column="delivery_type_code" jdbcType="VARCHAR" property="deliveryTypeCode" />
    <result column="send_date" jdbcType="TIMESTAMP" property="sendDate" />
    <result column="receive_date" jdbcType="TIMESTAMP" property="receiveDate" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="tracking_number" jdbcType="VARCHAR" property="trackingNumber" />
    <result column="delivery_status" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, demand_no, delivery_plan_no, delivery_method, delivery_type_code, send_date, 
    receive_date, assets_code, tracking_number, delivery_status, created_by, created_at, 
    updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandAssetsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_demand_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_demand_assets
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_demand_assets
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandAssets">
    insert into stock_assets_demand_assets (id, demand_no, delivery_plan_no, 
      delivery_method, delivery_type_code, send_date, 
      receive_date, assets_code, tracking_number, 
      delivery_status, created_by, created_at, 
      updated_by, updated_at)
    values (#{id,jdbcType=BIGINT}, #{demandNo,jdbcType=VARCHAR}, #{deliveryPlanNo,jdbcType=VARCHAR}, 
      #{deliveryMethod,jdbcType=INTEGER}, #{deliveryTypeCode,jdbcType=VARCHAR}, #{sendDate,jdbcType=TIMESTAMP}, 
      #{receiveDate,jdbcType=TIMESTAMP}, #{assetsCode,jdbcType=VARCHAR}, #{trackingNumber,jdbcType=VARCHAR}, 
      #{deliveryStatus,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandAssets">
    insert into stock_assets_demand_assets
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="demandNo != null">
        demand_no,
      </if>
      <if test="deliveryPlanNo != null">
        delivery_plan_no,
      </if>
      <if test="deliveryMethod != null">
        delivery_method,
      </if>
      <if test="deliveryTypeCode != null">
        delivery_type_code,
      </if>
      <if test="sendDate != null">
        send_date,
      </if>
      <if test="receiveDate != null">
        receive_date,
      </if>
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="trackingNumber != null">
        tracking_number,
      </if>
      <if test="deliveryStatus != null">
        delivery_status,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandNo != null">
        #{demandNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanNo != null">
        #{deliveryPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryMethod != null">
        #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="deliveryTypeCode != null">
        #{deliveryTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="sendDate != null">
        #{sendDate,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveDate != null">
        #{receiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="trackingNumber != null">
        #{trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandAssetsExample" resultType="java.lang.Long">
    select count(*) from stock_assets_demand_assets
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_demand_assets
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.demandNo != null">
        demand_no = #{record.demandNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryPlanNo != null">
        delivery_plan_no = #{record.deliveryPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryMethod != null">
        delivery_method = #{record.deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryTypeCode != null">
        delivery_type_code = #{record.deliveryTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sendDate != null">
        send_date = #{record.sendDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.receiveDate != null">
        receive_date = #{record.receiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.trackingNumber != null">
        tracking_number = #{record.trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryStatus != null">
        delivery_status = #{record.deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_demand_assets
    set id = #{record.id,jdbcType=BIGINT},
      demand_no = #{record.demandNo,jdbcType=VARCHAR},
      delivery_plan_no = #{record.deliveryPlanNo,jdbcType=VARCHAR},
      delivery_method = #{record.deliveryMethod,jdbcType=INTEGER},
      delivery_type_code = #{record.deliveryTypeCode,jdbcType=VARCHAR},
      send_date = #{record.sendDate,jdbcType=TIMESTAMP},
      receive_date = #{record.receiveDate,jdbcType=TIMESTAMP},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      tracking_number = #{record.trackingNumber,jdbcType=VARCHAR},
      delivery_status = #{record.deliveryStatus,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandAssets">
    update stock_assets_demand_assets
    <set>
      <if test="demandNo != null">
        demand_no = #{demandNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPlanNo != null">
        delivery_plan_no = #{deliveryPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryMethod != null">
        delivery_method = #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="deliveryTypeCode != null">
        delivery_type_code = #{deliveryTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="sendDate != null">
        send_date = #{sendDate,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveDate != null">
        receive_date = #{receiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="trackingNumber != null">
        tracking_number = #{trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStatus != null">
        delivery_status = #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandAssets">
    update stock_assets_demand_assets
    set demand_no = #{demandNo,jdbcType=VARCHAR},
      delivery_plan_no = #{deliveryPlanNo,jdbcType=VARCHAR},
      delivery_method = #{deliveryMethod,jdbcType=INTEGER},
      delivery_type_code = #{deliveryTypeCode,jdbcType=VARCHAR},
      send_date = #{sendDate,jdbcType=TIMESTAMP},
      receive_date = #{receiveDate,jdbcType=TIMESTAMP},
      assets_code = #{assetsCode,jdbcType=VARCHAR},
      tracking_number = #{trackingNumber,jdbcType=VARCHAR},
      delivery_status = #{deliveryStatus,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>