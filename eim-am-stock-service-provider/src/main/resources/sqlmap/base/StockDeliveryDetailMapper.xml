<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockDeliveryDetailMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockDeliveryDetail">
    <id column="delivery_detail_id" jdbcType="BIGINT" property="deliveryDetailId" />
    <result column="delivery_id" jdbcType="BIGINT" property="deliveryId" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="quality" jdbcType="INTEGER" property="quality" />
    <result column="real_number" jdbcType="INTEGER" property="realNumber" />
    <result column="difference_reason" jdbcType="VARCHAR" property="differenceReason" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="sn_no" jdbcType="VARCHAR" property="snNo" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="inventory_out_time" jdbcType="TIMESTAMP" property="inventoryOutTime" />
    <result column="plan_out_time" jdbcType="TIMESTAMP" property="planOutTime" />
    <result column="real_warehouse_code" jdbcType="VARCHAR" property="realWarehouseCode" />
    <result column="is_send" jdbcType="INTEGER" property="isSend" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="delivery_plan_line_id" jdbcType="BIGINT" property="deliveryPlanLineId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    delivery_detail_id, delivery_id, supplies_code, number, quality, real_number, difference_reason, 
    batch_no, sn_no, created_by, created_at, updated_by, updated_at, inventory_out_time, 
    plan_out_time, real_warehouse_code, is_send, status, delivery_plan_line_id
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_delivery_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_delivery_detail
    where delivery_detail_id = #{deliveryDetailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_delivery_detail
    where delivery_detail_id = #{deliveryDetailId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetail">
    insert into stock_delivery_detail (delivery_detail_id, delivery_id, supplies_code, 
      number, quality, real_number, 
      difference_reason, batch_no, sn_no, 
      created_by, created_at, updated_by, 
      updated_at, inventory_out_time, plan_out_time, 
      real_warehouse_code, is_send, status, 
      delivery_plan_line_id)
    values (#{deliveryDetailId,jdbcType=BIGINT}, #{deliveryId,jdbcType=BIGINT}, #{suppliesCode,jdbcType=VARCHAR}, 
      #{number,jdbcType=INTEGER}, #{quality,jdbcType=INTEGER}, #{realNumber,jdbcType=INTEGER}, 
      #{differenceReason,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{snNo,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{inventoryOutTime,jdbcType=TIMESTAMP}, #{planOutTime,jdbcType=TIMESTAMP}, 
      #{realWarehouseCode,jdbcType=VARCHAR}, #{isSend,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{deliveryPlanLineId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetail">
    insert into stock_delivery_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryDetailId != null">
        delivery_detail_id,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="quality != null">
        quality,
      </if>
      <if test="realNumber != null">
        real_number,
      </if>
      <if test="differenceReason != null">
        difference_reason,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="snNo != null">
        sn_no,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="inventoryOutTime != null">
        inventory_out_time,
      </if>
      <if test="planOutTime != null">
        plan_out_time,
      </if>
      <if test="realWarehouseCode != null">
        real_warehouse_code,
      </if>
      <if test="isSend != null">
        is_send,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="deliveryPlanLineId != null">
        delivery_plan_line_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryDetailId != null">
        #{deliveryDetailId,jdbcType=BIGINT},
      </if>
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=BIGINT},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="quality != null">
        #{quality,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="differenceReason != null">
        #{differenceReason,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="snNo != null">
        #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryOutTime != null">
        #{inventoryOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planOutTime != null">
        #{planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realWarehouseCode != null">
        #{realWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deliveryPlanLineId != null">
        #{deliveryPlanLineId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetailExample" resultType="java.lang.Long">
    select count(*) from stock_delivery_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_delivery_detail
    <set>
      <if test="record.deliveryDetailId != null">
        delivery_detail_id = #{record.deliveryDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=BIGINT},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.quality != null">
        quality = #{record.quality,jdbcType=INTEGER},
      </if>
      <if test="record.realNumber != null">
        real_number = #{record.realNumber,jdbcType=INTEGER},
      </if>
      <if test="record.differenceReason != null">
        difference_reason = #{record.differenceReason,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.snNo != null">
        sn_no = #{record.snNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inventoryOutTime != null">
        inventory_out_time = #{record.inventoryOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planOutTime != null">
        plan_out_time = #{record.planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realWarehouseCode != null">
        real_warehouse_code = #{record.realWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isSend != null">
        is_send = #{record.isSend,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryPlanLineId != null">
        delivery_plan_line_id = #{record.deliveryPlanLineId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_delivery_detail
    set delivery_detail_id = #{record.deliveryDetailId,jdbcType=BIGINT},
      delivery_id = #{record.deliveryId,jdbcType=BIGINT},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=INTEGER},
      quality = #{record.quality,jdbcType=INTEGER},
      real_number = #{record.realNumber,jdbcType=INTEGER},
      difference_reason = #{record.differenceReason,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      sn_no = #{record.snNo,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      inventory_out_time = #{record.inventoryOutTime,jdbcType=TIMESTAMP},
      plan_out_time = #{record.planOutTime,jdbcType=TIMESTAMP},
      real_warehouse_code = #{record.realWarehouseCode,jdbcType=VARCHAR},
      is_send = #{record.isSend,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      delivery_plan_line_id = #{record.deliveryPlanLineId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetail">
    update stock_delivery_detail
    <set>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=BIGINT},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="quality != null">
        quality = #{quality,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        real_number = #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="differenceReason != null">
        difference_reason = #{differenceReason,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="snNo != null">
        sn_no = #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryOutTime != null">
        inventory_out_time = #{inventoryOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planOutTime != null">
        plan_out_time = #{planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realWarehouseCode != null">
        real_warehouse_code = #{realWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        is_send = #{isSend,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="deliveryPlanLineId != null">
        delivery_plan_line_id = #{deliveryPlanLineId,jdbcType=BIGINT},
      </if>
    </set>
    where delivery_detail_id = #{deliveryDetailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetail">
    update stock_delivery_detail
    set delivery_id = #{deliveryId,jdbcType=BIGINT},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      number = #{number,jdbcType=INTEGER},
      quality = #{quality,jdbcType=INTEGER},
      real_number = #{realNumber,jdbcType=INTEGER},
      difference_reason = #{differenceReason,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      sn_no = #{snNo,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      inventory_out_time = #{inventoryOutTime,jdbcType=TIMESTAMP},
      plan_out_time = #{planOutTime,jdbcType=TIMESTAMP},
      real_warehouse_code = #{realWarehouseCode,jdbcType=VARCHAR},
      is_send = #{isSend,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      delivery_plan_line_id = #{deliveryPlanLineId,jdbcType=BIGINT}
    where delivery_detail_id = #{deliveryDetailId,jdbcType=BIGINT}
  </update>
</mapper>