<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockDeliveryMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockDelivery">
    <id column="delivery_id" jdbcType="BIGINT" property="deliveryId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="from_system" jdbcType="VARCHAR" property="fromSystem" />
    <result column="from_model" jdbcType="VARCHAR" property="fromModel" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="linkman" jdbcType="VARCHAR" property="linkman" />
    <result column="contact_way" jdbcType="VARCHAR" property="contactWay" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="logistics_channel" jdbcType="VARCHAR" property="logisticsChannel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode" />
    <result column="out_stock_time" jdbcType="TIMESTAMP" property="outStockTime" />
    <result column="out_stock_type" jdbcType="INTEGER" property="outStockType" />
    <result column="is_send" jdbcType="INTEGER" property="isSend" />
    <result column="send_channel" jdbcType="VARCHAR" property="sendChannel" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
    <result column="volume" jdbcType="INTEGER" property="volume" />
    <result column="approval_status" jdbcType="TINYINT" property="approvalStatus" />
    <result column="duty_user" jdbcType="VARCHAR" property="dutyUser" />
    <result column="use_user" jdbcType="VARCHAR" property="useUser" />
    <result column="use_user_dept" jdbcType="VARCHAR" property="useUserDept" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
    <result column="billing_time" jdbcType="TIMESTAMP" property="billingTime" />
    <result column="plan_out_time" jdbcType="TIMESTAMP" property="planOutTime" />
    <result column="reason_code" jdbcType="INTEGER" property="reasonCode" />
    <result column="delivery_plan_head_id" jdbcType="BIGINT" property="deliveryPlanHeadId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    delivery_id, order_id, order_no, delivery_no, from_system, from_model, receive_time, 
    country, province, city, area, address, linkman, contact_way, zip_code, remark, dept_code, 
    activity_code, logistics_channel, status, warehouse_code, in_warehouse_code, out_stock_time, 
    out_stock_type, is_send, send_channel, send_time, delivery_time, weight, volume, 
    approval_status, duty_user, use_user, use_user_dept, biz_no, created_by, created_at, 
    updated_by, updated_at, billing_user, billing_time, plan_out_time, reason_code, delivery_plan_head_id
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockDeliveryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_delivery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_delivery
    where delivery_id = #{deliveryId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_delivery
    where delivery_id = #{deliveryId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockDelivery">
    insert into stock_delivery (delivery_id, order_id, order_no, 
      delivery_no, from_system, from_model, 
      receive_time, country, province, 
      city, area, address, 
      linkman, contact_way, zip_code, 
      remark, dept_code, activity_code, 
      logistics_channel, status, warehouse_code, 
      in_warehouse_code, out_stock_time, out_stock_type, 
      is_send, send_channel, send_time, 
      delivery_time, weight, volume, 
      approval_status, duty_user, use_user, 
      use_user_dept, biz_no, created_by, 
      created_at, updated_by, updated_at, 
      billing_user, billing_time, plan_out_time, 
      reason_code, delivery_plan_head_id)
    values (#{deliveryId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, 
      #{deliveryNo,jdbcType=VARCHAR}, #{fromSystem,jdbcType=VARCHAR}, #{fromModel,jdbcType=VARCHAR}, 
      #{receiveTime,jdbcType=TIMESTAMP}, #{country,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{linkman,jdbcType=VARCHAR}, #{contactWay,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{deptCode,jdbcType=VARCHAR}, #{activityCode,jdbcType=VARCHAR}, 
      #{logisticsChannel,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{inWarehouseCode,jdbcType=VARCHAR}, #{outStockTime,jdbcType=TIMESTAMP}, #{outStockType,jdbcType=INTEGER}, 
      #{isSend,jdbcType=INTEGER}, #{sendChannel,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, 
      #{deliveryTime,jdbcType=TIMESTAMP}, #{weight,jdbcType=INTEGER}, #{volume,jdbcType=INTEGER}, 
      #{approvalStatus,jdbcType=TINYINT}, #{dutyUser,jdbcType=VARCHAR}, #{useUser,jdbcType=VARCHAR}, 
      #{useUserDept,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=TIMESTAMP}, #{planOutTime,jdbcType=TIMESTAMP}, 
      #{reasonCode,jdbcType=INTEGER}, #{deliveryPlanHeadId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockDelivery">
    insert into stock_delivery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="fromSystem != null">
        from_system,
      </if>
      <if test="fromModel != null">
        from_model,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="linkman != null">
        linkman,
      </if>
      <if test="contactWay != null">
        contact_way,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deptCode != null">
        dept_code,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="logisticsChannel != null">
        logistics_channel,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code,
      </if>
      <if test="outStockTime != null">
        out_stock_time,
      </if>
      <if test="outStockType != null">
        out_stock_type,
      </if>
      <if test="isSend != null">
        is_send,
      </if>
      <if test="sendChannel != null">
        send_channel,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="approvalStatus != null">
        approval_status,
      </if>
      <if test="dutyUser != null">
        duty_user,
      </if>
      <if test="useUser != null">
        use_user,
      </if>
      <if test="useUserDept != null">
        use_user_dept,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="billingUser != null">
        billing_user,
      </if>
      <if test="billingTime != null">
        billing_time,
      </if>
      <if test="planOutTime != null">
        plan_out_time,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="deliveryPlanHeadId != null">
        delivery_plan_head_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="fromSystem != null">
        #{fromSystem,jdbcType=VARCHAR},
      </if>
      <if test="fromModel != null">
        #{fromModel,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="contactWay != null">
        #{contactWay,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deptCode != null">
        #{deptCode,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="logisticsChannel != null">
        #{logisticsChannel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseCode != null">
        #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="outStockTime != null">
        #{outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outStockType != null">
        #{outStockType,jdbcType=INTEGER},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=INTEGER},
      </if>
      <if test="sendChannel != null">
        #{sendChannel,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=INTEGER},
      </if>
      <if test="volume != null">
        #{volume,jdbcType=INTEGER},
      </if>
      <if test="approvalStatus != null">
        #{approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="dutyUser != null">
        #{dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="useUser != null">
        #{useUser,jdbcType=VARCHAR},
      </if>
      <if test="useUserDept != null">
        #{useUserDept,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="billingUser != null">
        #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planOutTime != null">
        #{planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="deliveryPlanHeadId != null">
        #{deliveryPlanHeadId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockDeliveryExample" resultType="java.lang.Long">
    select count(*) from stock_delivery
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_delivery
    <set>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fromSystem != null">
        from_system = #{record.fromSystem,jdbcType=VARCHAR},
      </if>
      <if test="record.fromModel != null">
        from_model = #{record.fromModel,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveTime != null">
        receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.linkman != null">
        linkman = #{record.linkman,jdbcType=VARCHAR},
      </if>
      <if test="record.contactWay != null">
        contact_way = #{record.contactWay,jdbcType=VARCHAR},
      </if>
      <if test="record.zipCode != null">
        zip_code = #{record.zipCode,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.deptCode != null">
        dept_code = #{record.deptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.activityCode != null">
        activity_code = #{record.activityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.logisticsChannel != null">
        logistics_channel = #{record.logisticsChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inWarehouseCode != null">
        in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outStockTime != null">
        out_stock_time = #{record.outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.outStockType != null">
        out_stock_type = #{record.outStockType,jdbcType=INTEGER},
      </if>
      <if test="record.isSend != null">
        is_send = #{record.isSend,jdbcType=INTEGER},
      </if>
      <if test="record.sendChannel != null">
        send_channel = #{record.sendChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryTime != null">
        delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.weight != null">
        weight = #{record.weight,jdbcType=INTEGER},
      </if>
      <if test="record.volume != null">
        volume = #{record.volume,jdbcType=INTEGER},
      </if>
      <if test="record.approvalStatus != null">
        approval_status = #{record.approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dutyUser != null">
        duty_user = #{record.dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="record.useUser != null">
        use_user = #{record.useUser,jdbcType=VARCHAR},
      </if>
      <if test="record.useUserDept != null">
        use_user_dept = #{record.useUserDept,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.billingUser != null">
        billing_user = #{record.billingUser,jdbcType=VARCHAR},
      </if>
      <if test="record.billingTime != null">
        billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planOutTime != null">
        plan_out_time = #{record.planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reasonCode != null">
        reason_code = #{record.reasonCode,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryPlanHeadId != null">
        delivery_plan_head_id = #{record.deliveryPlanHeadId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_delivery
    set delivery_id = #{record.deliveryId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      from_system = #{record.fromSystem,jdbcType=VARCHAR},
      from_model = #{record.fromModel,jdbcType=VARCHAR},
      receive_time = #{record.receiveTime,jdbcType=TIMESTAMP},
      country = #{record.country,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      linkman = #{record.linkman,jdbcType=VARCHAR},
      contact_way = #{record.contactWay,jdbcType=VARCHAR},
      zip_code = #{record.zipCode,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      dept_code = #{record.deptCode,jdbcType=VARCHAR},
      activity_code = #{record.activityCode,jdbcType=VARCHAR},
      logistics_channel = #{record.logisticsChannel,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      out_stock_time = #{record.outStockTime,jdbcType=TIMESTAMP},
      out_stock_type = #{record.outStockType,jdbcType=INTEGER},
      is_send = #{record.isSend,jdbcType=INTEGER},
      send_channel = #{record.sendChannel,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      delivery_time = #{record.deliveryTime,jdbcType=TIMESTAMP},
      weight = #{record.weight,jdbcType=INTEGER},
      volume = #{record.volume,jdbcType=INTEGER},
      approval_status = #{record.approvalStatus,jdbcType=TINYINT},
      duty_user = #{record.dutyUser,jdbcType=VARCHAR},
      use_user = #{record.useUser,jdbcType=VARCHAR},
      use_user_dept = #{record.useUserDept,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      billing_user = #{record.billingUser,jdbcType=VARCHAR},
      billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      plan_out_time = #{record.planOutTime,jdbcType=TIMESTAMP},
      reason_code = #{record.reasonCode,jdbcType=INTEGER},
      delivery_plan_head_id = #{record.deliveryPlanHeadId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockDelivery">
    update stock_delivery
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="fromSystem != null">
        from_system = #{fromSystem,jdbcType=VARCHAR},
      </if>
      <if test="fromModel != null">
        from_model = #{fromModel,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        linkman = #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="contactWay != null">
        contact_way = #{contactWay,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        zip_code = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deptCode != null">
        dept_code = #{deptCode,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="logisticsChannel != null">
        logistics_channel = #{logisticsChannel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="outStockTime != null">
        out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outStockType != null">
        out_stock_type = #{outStockType,jdbcType=INTEGER},
      </if>
      <if test="isSend != null">
        is_send = #{isSend,jdbcType=INTEGER},
      </if>
      <if test="sendChannel != null">
        send_channel = #{sendChannel,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=INTEGER},
      </if>
      <if test="volume != null">
        volume = #{volume,jdbcType=INTEGER},
      </if>
      <if test="approvalStatus != null">
        approval_status = #{approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="dutyUser != null">
        duty_user = #{dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="useUser != null">
        use_user = #{useUser,jdbcType=VARCHAR},
      </if>
      <if test="useUserDept != null">
        use_user_dept = #{useUserDept,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="billingUser != null">
        billing_user = #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        billing_time = #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planOutTime != null">
        plan_out_time = #{planOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="deliveryPlanHeadId != null">
        delivery_plan_head_id = #{deliveryPlanHeadId,jdbcType=BIGINT},
      </if>
    </set>
    where delivery_id = #{deliveryId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockDelivery">
    update stock_delivery
    set order_id = #{orderId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      from_system = #{fromSystem,jdbcType=VARCHAR},
      from_model = #{fromModel,jdbcType=VARCHAR},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      country = #{country,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      linkman = #{linkman,jdbcType=VARCHAR},
      contact_way = #{contactWay,jdbcType=VARCHAR},
      zip_code = #{zipCode,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      dept_code = #{deptCode,jdbcType=VARCHAR},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      logistics_channel = #{logisticsChannel,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
      out_stock_type = #{outStockType,jdbcType=INTEGER},
      is_send = #{isSend,jdbcType=INTEGER},
      send_channel = #{sendChannel,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      weight = #{weight,jdbcType=INTEGER},
      volume = #{volume,jdbcType=INTEGER},
      approval_status = #{approvalStatus,jdbcType=TINYINT},
      duty_user = #{dutyUser,jdbcType=VARCHAR},
      use_user = #{useUser,jdbcType=VARCHAR},
      use_user_dept = #{useUserDept,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      billing_user = #{billingUser,jdbcType=VARCHAR},
      billing_time = #{billingTime,jdbcType=TIMESTAMP},
      plan_out_time = #{planOutTime,jdbcType=TIMESTAMP},
      reason_code = #{reasonCode,jdbcType=INTEGER},
      delivery_plan_head_id = #{deliveryPlanHeadId,jdbcType=BIGINT}
    where delivery_id = #{deliveryId,jdbcType=BIGINT}
  </update>
</mapper>