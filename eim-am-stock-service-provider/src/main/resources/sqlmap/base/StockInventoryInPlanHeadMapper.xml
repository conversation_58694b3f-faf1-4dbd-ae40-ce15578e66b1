<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockInventoryInPlanHeadMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInPlanHead">
    <id column="inventory_in_plan_head_id" jdbcType="BIGINT" property="inventoryInPlanHeadId" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="inventory_in_plan_no" jdbcType="VARCHAR" property="inventoryInPlanNo" />
    <result column="Warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode" />
    <result column="out_warehouse_code" jdbcType="VARCHAR" property="outWarehouseCode" />
    <result column="inventory_in_plan_type" jdbcType="INTEGER" property="inventoryInPlanType" />
    <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
    <result column="billing_time" jdbcType="TIMESTAMP" property="billingTime" />
    <result column="reason_code" jdbcType="INTEGER" property="reasonCode" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
    <result column="Company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="system_code" jdbcType="VARCHAR" property="systemCode" />
    <result column="receive_user" jdbcType="VARCHAR" property="receiveUser" />
    <result column="plan_in_time" jdbcType="TIMESTAMP" property="planInTime" />
    <result column="adjust_date" jdbcType="TIMESTAMP" property="adjustDate" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="Purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="duty_user" jdbcType="VARCHAR" property="dutyUser" />
    <result column="duty_dept" jdbcType="VARCHAR" property="dutyDept" />
    <result column="duty_address" jdbcType="VARCHAR" property="dutyAddress" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="demand_dept_code" jdbcType="VARCHAR" property="demandDeptCode" />
    <result column="demand_dept_id" jdbcType="VARCHAR" property="demandDeptId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    inventory_in_plan_head_id, biz_no, inventory_in_plan_no, Warehouse_type, in_warehouse_code, 
    out_warehouse_code, inventory_in_plan_type, billing_user, billing_time, reason_code, 
    vendor_code, vendor_name, Company_code, system_code, receive_user, plan_in_time, 
    adjust_date, delivery_no, Purchase_order_no, status, remark, duty_user, duty_dept, 
    duty_address, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT, demand_dept_code, demand_dept_id
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHeadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_inventory_in_plan_heads
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_inventory_in_plan_heads
    where inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_inventory_in_plan_heads
    where inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHead">
    insert into stock_inventory_in_plan_heads (inventory_in_plan_head_id, biz_no, inventory_in_plan_no, 
      Warehouse_type, in_warehouse_code, out_warehouse_code, 
      inventory_in_plan_type, billing_user, billing_time, 
      reason_code, vendor_code, vendor_name, 
      Company_code, system_code, receive_user, 
      plan_in_time, adjust_date, delivery_no, 
      Purchase_order_no, status, remark, 
      duty_user, duty_dept, duty_address, 
      CREATED_BY, CREATED_AT, UPDATED_BY, 
      UPDATED_AT, demand_dept_code, demand_dept_id
      )
    values (#{inventoryInPlanHeadId,jdbcType=BIGINT}, #{bizNo,jdbcType=VARCHAR}, #{inventoryInPlanNo,jdbcType=VARCHAR}, 
      #{warehouseType,jdbcType=TINYINT}, #{inWarehouseCode,jdbcType=VARCHAR}, #{outWarehouseCode,jdbcType=VARCHAR}, 
      #{inventoryInPlanType,jdbcType=INTEGER}, #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=TIMESTAMP}, 
      #{reasonCode,jdbcType=INTEGER}, #{vendorCode,jdbcType=VARCHAR}, #{vendorName,jdbcType=VARCHAR}, 
      #{companyCode,jdbcType=VARCHAR}, #{systemCode,jdbcType=VARCHAR}, #{receiveUser,jdbcType=VARCHAR}, 
      #{planInTime,jdbcType=TIMESTAMP}, #{adjustDate,jdbcType=TIMESTAMP}, #{deliveryNo,jdbcType=VARCHAR}, 
      #{purchaseOrderNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{dutyUser,jdbcType=VARCHAR}, #{dutyDept,jdbcType=VARCHAR}, #{dutyAddress,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{demandDeptCode,jdbcType=VARCHAR}, #{demandDeptId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHead">
    insert into stock_inventory_in_plan_heads
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="inventoryInPlanHeadId != null">
        inventory_in_plan_head_id,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="inventoryInPlanNo != null">
        inventory_in_plan_no,
      </if>
      <if test="warehouseType != null">
        Warehouse_type,
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code,
      </if>
      <if test="outWarehouseCode != null">
        out_warehouse_code,
      </if>
      <if test="inventoryInPlanType != null">
        inventory_in_plan_type,
      </if>
      <if test="billingUser != null">
        billing_user,
      </if>
      <if test="billingTime != null">
        billing_time,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="vendorName != null">
        vendor_name,
      </if>
      <if test="companyCode != null">
        Company_code,
      </if>
      <if test="systemCode != null">
        system_code,
      </if>
      <if test="receiveUser != null">
        receive_user,
      </if>
      <if test="planInTime != null">
        plan_in_time,
      </if>
      <if test="adjustDate != null">
        adjust_date,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="purchaseOrderNo != null">
        Purchase_order_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="dutyUser != null">
        duty_user,
      </if>
      <if test="dutyDept != null">
        duty_dept,
      </if>
      <if test="dutyAddress != null">
        duty_address,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
      <if test="demandDeptCode != null">
        demand_dept_code,
      </if>
      <if test="demandDeptId != null">
        demand_dept_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="inventoryInPlanHeadId != null">
        #{inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInPlanNo != null">
        #{inventoryInPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseType != null">
        #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="inWarehouseCode != null">
        #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseCode != null">
        #{outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInPlanType != null">
        #{inventoryInPlanType,jdbcType=INTEGER},
      </if>
      <if test="billingUser != null">
        #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="systemCode != null">
        #{systemCode,jdbcType=VARCHAR},
      </if>
      <if test="receiveUser != null">
        #{receiveUser,jdbcType=VARCHAR},
      </if>
      <if test="planInTime != null">
        #{planInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustDate != null">
        #{adjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dutyUser != null">
        #{dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="dutyDept != null">
        #{dutyDept,jdbcType=VARCHAR},
      </if>
      <if test="dutyAddress != null">
        #{dutyAddress,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="demandDeptCode != null">
        #{demandDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="demandDeptId != null">
        #{demandDeptId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHeadExample" resultType="java.lang.Long">
    select count(*) from stock_inventory_in_plan_heads
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_inventory_in_plan_heads
    <set>
      <if test="record.inventoryInPlanHeadId != null">
        inventory_in_plan_head_id = #{record.inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryInPlanNo != null">
        inventory_in_plan_no = #{record.inventoryInPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseType != null">
        Warehouse_type = #{record.warehouseType,jdbcType=TINYINT},
      </if>
      <if test="record.inWarehouseCode != null">
        in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outWarehouseCode != null">
        out_warehouse_code = #{record.outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryInPlanType != null">
        inventory_in_plan_type = #{record.inventoryInPlanType,jdbcType=INTEGER},
      </if>
      <if test="record.billingUser != null">
        billing_user = #{record.billingUser,jdbcType=VARCHAR},
      </if>
      <if test="record.billingTime != null">
        billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reasonCode != null">
        reason_code = #{record.reasonCode,jdbcType=INTEGER},
      </if>
      <if test="record.vendorCode != null">
        vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorName != null">
        vendor_name = #{record.vendorName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        Company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.systemCode != null">
        system_code = #{record.systemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveUser != null">
        receive_user = #{record.receiveUser,jdbcType=VARCHAR},
      </if>
      <if test="record.planInTime != null">
        plan_in_time = #{record.planInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adjustDate != null">
        adjust_date = #{record.adjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        Purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.dutyUser != null">
        duty_user = #{record.dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="record.dutyDept != null">
        duty_dept = #{record.dutyDept,jdbcType=VARCHAR},
      </if>
      <if test="record.dutyAddress != null">
        duty_address = #{record.dutyAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.demandDeptCode != null">
        demand_dept_code = #{record.demandDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.demandDeptId != null">
        demand_dept_id = #{record.demandDeptId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_inventory_in_plan_heads
    set inventory_in_plan_head_id = #{record.inventoryInPlanHeadId,jdbcType=BIGINT},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      inventory_in_plan_no = #{record.inventoryInPlanNo,jdbcType=VARCHAR},
      Warehouse_type = #{record.warehouseType,jdbcType=TINYINT},
      in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      out_warehouse_code = #{record.outWarehouseCode,jdbcType=VARCHAR},
      inventory_in_plan_type = #{record.inventoryInPlanType,jdbcType=INTEGER},
      billing_user = #{record.billingUser,jdbcType=VARCHAR},
      billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      reason_code = #{record.reasonCode,jdbcType=INTEGER},
      vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      vendor_name = #{record.vendorName,jdbcType=VARCHAR},
      Company_code = #{record.companyCode,jdbcType=VARCHAR},
      system_code = #{record.systemCode,jdbcType=VARCHAR},
      receive_user = #{record.receiveUser,jdbcType=VARCHAR},
      plan_in_time = #{record.planInTime,jdbcType=TIMESTAMP},
      adjust_date = #{record.adjustDate,jdbcType=TIMESTAMP},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      Purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      duty_user = #{record.dutyUser,jdbcType=VARCHAR},
      duty_dept = #{record.dutyDept,jdbcType=VARCHAR},
      duty_address = #{record.dutyAddress,jdbcType=VARCHAR},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      demand_dept_code = #{record.demandDeptCode,jdbcType=VARCHAR},
      demand_dept_id = #{record.demandDeptId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHead">
    update stock_inventory_in_plan_heads
    <set>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInPlanNo != null">
        inventory_in_plan_no = #{inventoryInPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseType != null">
        Warehouse_type = #{warehouseType,jdbcType=TINYINT},
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseCode != null">
        out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInPlanType != null">
        inventory_in_plan_type = #{inventoryInPlanType,jdbcType=INTEGER},
      </if>
      <if test="billingUser != null">
        billing_user = #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        billing_time = #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        vendor_name = #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        Company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="systemCode != null">
        system_code = #{systemCode,jdbcType=VARCHAR},
      </if>
      <if test="receiveUser != null">
        receive_user = #{receiveUser,jdbcType=VARCHAR},
      </if>
      <if test="planInTime != null">
        plan_in_time = #{planInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustDate != null">
        adjust_date = #{adjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        Purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="dutyUser != null">
        duty_user = #{dutyUser,jdbcType=VARCHAR},
      </if>
      <if test="dutyDept != null">
        duty_dept = #{dutyDept,jdbcType=VARCHAR},
      </if>
      <if test="dutyAddress != null">
        duty_address = #{dutyAddress,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="demandDeptCode != null">
        demand_dept_code = #{demandDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="demandDeptId != null">
        demand_dept_id = #{demandDeptId,jdbcType=VARCHAR},
      </if>
    </set>
    where inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHead">
    update stock_inventory_in_plan_heads
    set biz_no = #{bizNo,jdbcType=VARCHAR},
      inventory_in_plan_no = #{inventoryInPlanNo,jdbcType=VARCHAR},
      Warehouse_type = #{warehouseType,jdbcType=TINYINT},
      in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
      inventory_in_plan_type = #{inventoryInPlanType,jdbcType=INTEGER},
      billing_user = #{billingUser,jdbcType=VARCHAR},
      billing_time = #{billingTime,jdbcType=TIMESTAMP},
      reason_code = #{reasonCode,jdbcType=INTEGER},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      Company_code = #{companyCode,jdbcType=VARCHAR},
      system_code = #{systemCode,jdbcType=VARCHAR},
      receive_user = #{receiveUser,jdbcType=VARCHAR},
      plan_in_time = #{planInTime,jdbcType=TIMESTAMP},
      adjust_date = #{adjustDate,jdbcType=TIMESTAMP},
      delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      Purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      duty_user = #{dutyUser,jdbcType=VARCHAR},
      duty_dept = #{dutyDept,jdbcType=VARCHAR},
      duty_address = #{dutyAddress,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      demand_dept_code = #{demandDeptCode,jdbcType=VARCHAR},
      demand_dept_id = #{demandDeptId,jdbcType=VARCHAR}
    where inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT}
  </update>
</mapper>