<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockTakingProcessMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockTakingProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="taking_plan_no" jdbcType="VARCHAR" property="takingPlanNo" />
    <result column="start_taking_plan" jdbcType="INTEGER" property="startTakingPlan" />
    <result column="start_taking_plan_status" jdbcType="INTEGER" property="startTakingPlanStatus" />
    <result column="start_taking_plan_finish_date" jdbcType="TIMESTAMP" property="startTakingPlanFinishDate" />
    <result column="generate_taking_snapshot" jdbcType="INTEGER" property="generateTakingSnapshot" />
    <result column="generate_taking_snapshot_status" jdbcType="INTEGER" property="generateTakingSnapshotStatus" />
    <result column="generate_taking_snapshot_date" jdbcType="TIMESTAMP" property="generateTakingSnapshotDate" />
    <result column="confirm_taking_snapshot" jdbcType="INTEGER" property="confirmTakingSnapshot" />
    <result column="confirm_taking_snapshot_status" jdbcType="INTEGER" property="confirmTakingSnapshotStatus" />
    <result column="confirm_taking_snapshot_date" jdbcType="TIMESTAMP" property="confirmTakingSnapshotDate" />
    <result column="create_taking_task" jdbcType="INTEGER" property="createTakingTask" />
    <result column="create_taking_task_status" jdbcType="INTEGER" property="createTakingTaskStatus" />
    <result column="create_taking_task_date" jdbcType="TIMESTAMP" property="createTakingTaskDate" />
    <result column="assign_taking_task" jdbcType="INTEGER" property="assignTakingTask" />
    <result column="assign_taking_task_status" jdbcType="INTEGER" property="assignTakingTaskStatus" />
    <result column="assign_taking_task_date" jdbcType="TIMESTAMP" property="assignTakingTaskDate" />
    <result column="input_taking_data" jdbcType="INTEGER" property="inputTakingData" />
    <result column="input_taking_data_status" jdbcType="INTEGER" property="inputTakingDataStatus" />
    <result column="input_taking_data_date" jdbcType="TIMESTAMP" property="inputTakingDataDate" />
    <result column="generate_taking_result" jdbcType="INTEGER" property="generateTakingResult" />
    <result column="generate_taking_result_status" jdbcType="INTEGER" property="generateTakingResultStatus" />
    <result column="generate_taking_result_date" jdbcType="TIMESTAMP" property="generateTakingResultDate" />
    <result column="approval_taking" jdbcType="INTEGER" property="approvalTaking" />
    <result column="approval_taking_status" jdbcType="INTEGER" property="approvalTakingStatus" />
    <result column="approval_taking_date" jdbcType="TIMESTAMP" property="approvalTakingDate" />
    <result column="perform_taking_adjust" jdbcType="INTEGER" property="performTakingAdjust" />
    <result column="perform_taking_adjust_status" jdbcType="INTEGER" property="performTakingAdjustStatus" />
    <result column="perform_taking_adjust_date" jdbcType="TIMESTAMP" property="performTakingAdjustDate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, taking_plan_no, start_taking_plan, start_taking_plan_status, start_taking_plan_finish_date, 
    generate_taking_snapshot, generate_taking_snapshot_status, generate_taking_snapshot_date, 
    confirm_taking_snapshot, confirm_taking_snapshot_status, confirm_taking_snapshot_date, 
    create_taking_task, create_taking_task_status, create_taking_task_date, assign_taking_task, 
    assign_taking_task_status, assign_taking_task_date, input_taking_data, input_taking_data_status, 
    input_taking_data_date, generate_taking_result, generate_taking_result_status, generate_taking_result_date, 
    approval_taking, approval_taking_status, approval_taking_date, perform_taking_adjust, 
    perform_taking_adjust_status, perform_taking_adjust_date, created_by, created_at, 
    updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockTakingProcessExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_taking_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_taking_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_taking_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockTakingProcess">
    insert into stock_taking_process (id, taking_plan_no, start_taking_plan, 
      start_taking_plan_status, start_taking_plan_finish_date, 
      generate_taking_snapshot, generate_taking_snapshot_status, 
      generate_taking_snapshot_date, confirm_taking_snapshot, 
      confirm_taking_snapshot_status, confirm_taking_snapshot_date, 
      create_taking_task, create_taking_task_status, 
      create_taking_task_date, assign_taking_task, 
      assign_taking_task_status, assign_taking_task_date, 
      input_taking_data, input_taking_data_status, 
      input_taking_data_date, generate_taking_result, 
      generate_taking_result_status, generate_taking_result_date, 
      approval_taking, approval_taking_status, approval_taking_date, 
      perform_taking_adjust, perform_taking_adjust_status, 
      perform_taking_adjust_date, created_by, created_at, 
      updated_by, updated_at)
    values (#{id,jdbcType=BIGINT}, #{takingPlanNo,jdbcType=VARCHAR}, #{startTakingPlan,jdbcType=INTEGER}, 
      #{startTakingPlanStatus,jdbcType=INTEGER}, #{startTakingPlanFinishDate,jdbcType=TIMESTAMP}, 
      #{generateTakingSnapshot,jdbcType=INTEGER}, #{generateTakingSnapshotStatus,jdbcType=INTEGER}, 
      #{generateTakingSnapshotDate,jdbcType=TIMESTAMP}, #{confirmTakingSnapshot,jdbcType=INTEGER}, 
      #{confirmTakingSnapshotStatus,jdbcType=INTEGER}, #{confirmTakingSnapshotDate,jdbcType=TIMESTAMP}, 
      #{createTakingTask,jdbcType=INTEGER}, #{createTakingTaskStatus,jdbcType=INTEGER}, 
      #{createTakingTaskDate,jdbcType=TIMESTAMP}, #{assignTakingTask,jdbcType=INTEGER}, 
      #{assignTakingTaskStatus,jdbcType=INTEGER}, #{assignTakingTaskDate,jdbcType=TIMESTAMP}, 
      #{inputTakingData,jdbcType=INTEGER}, #{inputTakingDataStatus,jdbcType=INTEGER}, 
      #{inputTakingDataDate,jdbcType=TIMESTAMP}, #{generateTakingResult,jdbcType=INTEGER}, 
      #{generateTakingResultStatus,jdbcType=INTEGER}, #{generateTakingResultDate,jdbcType=TIMESTAMP}, 
      #{approvalTaking,jdbcType=INTEGER}, #{approvalTakingStatus,jdbcType=INTEGER}, #{approvalTakingDate,jdbcType=TIMESTAMP}, 
      #{performTakingAdjust,jdbcType=INTEGER}, #{performTakingAdjustStatus,jdbcType=INTEGER}, 
      #{performTakingAdjustDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockTakingProcess">
    insert into stock_taking_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="takingPlanNo != null">
        taking_plan_no,
      </if>
      <if test="startTakingPlan != null">
        start_taking_plan,
      </if>
      <if test="startTakingPlanStatus != null">
        start_taking_plan_status,
      </if>
      <if test="startTakingPlanFinishDate != null">
        start_taking_plan_finish_date,
      </if>
      <if test="generateTakingSnapshot != null">
        generate_taking_snapshot,
      </if>
      <if test="generateTakingSnapshotStatus != null">
        generate_taking_snapshot_status,
      </if>
      <if test="generateTakingSnapshotDate != null">
        generate_taking_snapshot_date,
      </if>
      <if test="confirmTakingSnapshot != null">
        confirm_taking_snapshot,
      </if>
      <if test="confirmTakingSnapshotStatus != null">
        confirm_taking_snapshot_status,
      </if>
      <if test="confirmTakingSnapshotDate != null">
        confirm_taking_snapshot_date,
      </if>
      <if test="createTakingTask != null">
        create_taking_task,
      </if>
      <if test="createTakingTaskStatus != null">
        create_taking_task_status,
      </if>
      <if test="createTakingTaskDate != null">
        create_taking_task_date,
      </if>
      <if test="assignTakingTask != null">
        assign_taking_task,
      </if>
      <if test="assignTakingTaskStatus != null">
        assign_taking_task_status,
      </if>
      <if test="assignTakingTaskDate != null">
        assign_taking_task_date,
      </if>
      <if test="inputTakingData != null">
        input_taking_data,
      </if>
      <if test="inputTakingDataStatus != null">
        input_taking_data_status,
      </if>
      <if test="inputTakingDataDate != null">
        input_taking_data_date,
      </if>
      <if test="generateTakingResult != null">
        generate_taking_result,
      </if>
      <if test="generateTakingResultStatus != null">
        generate_taking_result_status,
      </if>
      <if test="generateTakingResultDate != null">
        generate_taking_result_date,
      </if>
      <if test="approvalTaking != null">
        approval_taking,
      </if>
      <if test="approvalTakingStatus != null">
        approval_taking_status,
      </if>
      <if test="approvalTakingDate != null">
        approval_taking_date,
      </if>
      <if test="performTakingAdjust != null">
        perform_taking_adjust,
      </if>
      <if test="performTakingAdjustStatus != null">
        perform_taking_adjust_status,
      </if>
      <if test="performTakingAdjustDate != null">
        perform_taking_adjust_date,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="takingPlanNo != null">
        #{takingPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="startTakingPlan != null">
        #{startTakingPlan,jdbcType=INTEGER},
      </if>
      <if test="startTakingPlanStatus != null">
        #{startTakingPlanStatus,jdbcType=INTEGER},
      </if>
      <if test="startTakingPlanFinishDate != null">
        #{startTakingPlanFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generateTakingSnapshot != null">
        #{generateTakingSnapshot,jdbcType=INTEGER},
      </if>
      <if test="generateTakingSnapshotStatus != null">
        #{generateTakingSnapshotStatus,jdbcType=INTEGER},
      </if>
      <if test="generateTakingSnapshotDate != null">
        #{generateTakingSnapshotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTakingSnapshot != null">
        #{confirmTakingSnapshot,jdbcType=INTEGER},
      </if>
      <if test="confirmTakingSnapshotStatus != null">
        #{confirmTakingSnapshotStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTakingSnapshotDate != null">
        #{confirmTakingSnapshotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTakingTask != null">
        #{createTakingTask,jdbcType=INTEGER},
      </if>
      <if test="createTakingTaskStatus != null">
        #{createTakingTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="createTakingTaskDate != null">
        #{createTakingTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assignTakingTask != null">
        #{assignTakingTask,jdbcType=INTEGER},
      </if>
      <if test="assignTakingTaskStatus != null">
        #{assignTakingTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="assignTakingTaskDate != null">
        #{assignTakingTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="inputTakingData != null">
        #{inputTakingData,jdbcType=INTEGER},
      </if>
      <if test="inputTakingDataStatus != null">
        #{inputTakingDataStatus,jdbcType=INTEGER},
      </if>
      <if test="inputTakingDataDate != null">
        #{inputTakingDataDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generateTakingResult != null">
        #{generateTakingResult,jdbcType=INTEGER},
      </if>
      <if test="generateTakingResultStatus != null">
        #{generateTakingResultStatus,jdbcType=INTEGER},
      </if>
      <if test="generateTakingResultDate != null">
        #{generateTakingResultDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalTaking != null">
        #{approvalTaking,jdbcType=INTEGER},
      </if>
      <if test="approvalTakingStatus != null">
        #{approvalTakingStatus,jdbcType=INTEGER},
      </if>
      <if test="approvalTakingDate != null">
        #{approvalTakingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="performTakingAdjust != null">
        #{performTakingAdjust,jdbcType=INTEGER},
      </if>
      <if test="performTakingAdjustStatus != null">
        #{performTakingAdjustStatus,jdbcType=INTEGER},
      </if>
      <if test="performTakingAdjustDate != null">
        #{performTakingAdjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockTakingProcessExample" resultType="java.lang.Long">
    select count(*) from stock_taking_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_taking_process
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.takingPlanNo != null">
        taking_plan_no = #{record.takingPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.startTakingPlan != null">
        start_taking_plan = #{record.startTakingPlan,jdbcType=INTEGER},
      </if>
      <if test="record.startTakingPlanStatus != null">
        start_taking_plan_status = #{record.startTakingPlanStatus,jdbcType=INTEGER},
      </if>
      <if test="record.startTakingPlanFinishDate != null">
        start_taking_plan_finish_date = #{record.startTakingPlanFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.generateTakingSnapshot != null">
        generate_taking_snapshot = #{record.generateTakingSnapshot,jdbcType=INTEGER},
      </if>
      <if test="record.generateTakingSnapshotStatus != null">
        generate_taking_snapshot_status = #{record.generateTakingSnapshotStatus,jdbcType=INTEGER},
      </if>
      <if test="record.generateTakingSnapshotDate != null">
        generate_taking_snapshot_date = #{record.generateTakingSnapshotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmTakingSnapshot != null">
        confirm_taking_snapshot = #{record.confirmTakingSnapshot,jdbcType=INTEGER},
      </if>
      <if test="record.confirmTakingSnapshotStatus != null">
        confirm_taking_snapshot_status = #{record.confirmTakingSnapshotStatus,jdbcType=INTEGER},
      </if>
      <if test="record.confirmTakingSnapshotDate != null">
        confirm_taking_snapshot_date = #{record.confirmTakingSnapshotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTakingTask != null">
        create_taking_task = #{record.createTakingTask,jdbcType=INTEGER},
      </if>
      <if test="record.createTakingTaskStatus != null">
        create_taking_task_status = #{record.createTakingTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTakingTaskDate != null">
        create_taking_task_date = #{record.createTakingTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.assignTakingTask != null">
        assign_taking_task = #{record.assignTakingTask,jdbcType=INTEGER},
      </if>
      <if test="record.assignTakingTaskStatus != null">
        assign_taking_task_status = #{record.assignTakingTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.assignTakingTaskDate != null">
        assign_taking_task_date = #{record.assignTakingTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inputTakingData != null">
        input_taking_data = #{record.inputTakingData,jdbcType=INTEGER},
      </if>
      <if test="record.inputTakingDataStatus != null">
        input_taking_data_status = #{record.inputTakingDataStatus,jdbcType=INTEGER},
      </if>
      <if test="record.inputTakingDataDate != null">
        input_taking_data_date = #{record.inputTakingDataDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.generateTakingResult != null">
        generate_taking_result = #{record.generateTakingResult,jdbcType=INTEGER},
      </if>
      <if test="record.generateTakingResultStatus != null">
        generate_taking_result_status = #{record.generateTakingResultStatus,jdbcType=INTEGER},
      </if>
      <if test="record.generateTakingResultDate != null">
        generate_taking_result_date = #{record.generateTakingResultDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approvalTaking != null">
        approval_taking = #{record.approvalTaking,jdbcType=INTEGER},
      </if>
      <if test="record.approvalTakingStatus != null">
        approval_taking_status = #{record.approvalTakingStatus,jdbcType=INTEGER},
      </if>
      <if test="record.approvalTakingDate != null">
        approval_taking_date = #{record.approvalTakingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.performTakingAdjust != null">
        perform_taking_adjust = #{record.performTakingAdjust,jdbcType=INTEGER},
      </if>
      <if test="record.performTakingAdjustStatus != null">
        perform_taking_adjust_status = #{record.performTakingAdjustStatus,jdbcType=INTEGER},
      </if>
      <if test="record.performTakingAdjustDate != null">
        perform_taking_adjust_date = #{record.performTakingAdjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_taking_process
    set id = #{record.id,jdbcType=BIGINT},
      taking_plan_no = #{record.takingPlanNo,jdbcType=VARCHAR},
      start_taking_plan = #{record.startTakingPlan,jdbcType=INTEGER},
      start_taking_plan_status = #{record.startTakingPlanStatus,jdbcType=INTEGER},
      start_taking_plan_finish_date = #{record.startTakingPlanFinishDate,jdbcType=TIMESTAMP},
      generate_taking_snapshot = #{record.generateTakingSnapshot,jdbcType=INTEGER},
      generate_taking_snapshot_status = #{record.generateTakingSnapshotStatus,jdbcType=INTEGER},
      generate_taking_snapshot_date = #{record.generateTakingSnapshotDate,jdbcType=TIMESTAMP},
      confirm_taking_snapshot = #{record.confirmTakingSnapshot,jdbcType=INTEGER},
      confirm_taking_snapshot_status = #{record.confirmTakingSnapshotStatus,jdbcType=INTEGER},
      confirm_taking_snapshot_date = #{record.confirmTakingSnapshotDate,jdbcType=TIMESTAMP},
      create_taking_task = #{record.createTakingTask,jdbcType=INTEGER},
      create_taking_task_status = #{record.createTakingTaskStatus,jdbcType=INTEGER},
      create_taking_task_date = #{record.createTakingTaskDate,jdbcType=TIMESTAMP},
      assign_taking_task = #{record.assignTakingTask,jdbcType=INTEGER},
      assign_taking_task_status = #{record.assignTakingTaskStatus,jdbcType=INTEGER},
      assign_taking_task_date = #{record.assignTakingTaskDate,jdbcType=TIMESTAMP},
      input_taking_data = #{record.inputTakingData,jdbcType=INTEGER},
      input_taking_data_status = #{record.inputTakingDataStatus,jdbcType=INTEGER},
      input_taking_data_date = #{record.inputTakingDataDate,jdbcType=TIMESTAMP},
      generate_taking_result = #{record.generateTakingResult,jdbcType=INTEGER},
      generate_taking_result_status = #{record.generateTakingResultStatus,jdbcType=INTEGER},
      generate_taking_result_date = #{record.generateTakingResultDate,jdbcType=TIMESTAMP},
      approval_taking = #{record.approvalTaking,jdbcType=INTEGER},
      approval_taking_status = #{record.approvalTakingStatus,jdbcType=INTEGER},
      approval_taking_date = #{record.approvalTakingDate,jdbcType=TIMESTAMP},
      perform_taking_adjust = #{record.performTakingAdjust,jdbcType=INTEGER},
      perform_taking_adjust_status = #{record.performTakingAdjustStatus,jdbcType=INTEGER},
      perform_taking_adjust_date = #{record.performTakingAdjustDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockTakingProcess">
    update stock_taking_process
    <set>
      <if test="takingPlanNo != null">
        taking_plan_no = #{takingPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="startTakingPlan != null">
        start_taking_plan = #{startTakingPlan,jdbcType=INTEGER},
      </if>
      <if test="startTakingPlanStatus != null">
        start_taking_plan_status = #{startTakingPlanStatus,jdbcType=INTEGER},
      </if>
      <if test="startTakingPlanFinishDate != null">
        start_taking_plan_finish_date = #{startTakingPlanFinishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generateTakingSnapshot != null">
        generate_taking_snapshot = #{generateTakingSnapshot,jdbcType=INTEGER},
      </if>
      <if test="generateTakingSnapshotStatus != null">
        generate_taking_snapshot_status = #{generateTakingSnapshotStatus,jdbcType=INTEGER},
      </if>
      <if test="generateTakingSnapshotDate != null">
        generate_taking_snapshot_date = #{generateTakingSnapshotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTakingSnapshot != null">
        confirm_taking_snapshot = #{confirmTakingSnapshot,jdbcType=INTEGER},
      </if>
      <if test="confirmTakingSnapshotStatus != null">
        confirm_taking_snapshot_status = #{confirmTakingSnapshotStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTakingSnapshotDate != null">
        confirm_taking_snapshot_date = #{confirmTakingSnapshotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTakingTask != null">
        create_taking_task = #{createTakingTask,jdbcType=INTEGER},
      </if>
      <if test="createTakingTaskStatus != null">
        create_taking_task_status = #{createTakingTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="createTakingTaskDate != null">
        create_taking_task_date = #{createTakingTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="assignTakingTask != null">
        assign_taking_task = #{assignTakingTask,jdbcType=INTEGER},
      </if>
      <if test="assignTakingTaskStatus != null">
        assign_taking_task_status = #{assignTakingTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="assignTakingTaskDate != null">
        assign_taking_task_date = #{assignTakingTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="inputTakingData != null">
        input_taking_data = #{inputTakingData,jdbcType=INTEGER},
      </if>
      <if test="inputTakingDataStatus != null">
        input_taking_data_status = #{inputTakingDataStatus,jdbcType=INTEGER},
      </if>
      <if test="inputTakingDataDate != null">
        input_taking_data_date = #{inputTakingDataDate,jdbcType=TIMESTAMP},
      </if>
      <if test="generateTakingResult != null">
        generate_taking_result = #{generateTakingResult,jdbcType=INTEGER},
      </if>
      <if test="generateTakingResultStatus != null">
        generate_taking_result_status = #{generateTakingResultStatus,jdbcType=INTEGER},
      </if>
      <if test="generateTakingResultDate != null">
        generate_taking_result_date = #{generateTakingResultDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalTaking != null">
        approval_taking = #{approvalTaking,jdbcType=INTEGER},
      </if>
      <if test="approvalTakingStatus != null">
        approval_taking_status = #{approvalTakingStatus,jdbcType=INTEGER},
      </if>
      <if test="approvalTakingDate != null">
        approval_taking_date = #{approvalTakingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="performTakingAdjust != null">
        perform_taking_adjust = #{performTakingAdjust,jdbcType=INTEGER},
      </if>
      <if test="performTakingAdjustStatus != null">
        perform_taking_adjust_status = #{performTakingAdjustStatus,jdbcType=INTEGER},
      </if>
      <if test="performTakingAdjustDate != null">
        perform_taking_adjust_date = #{performTakingAdjustDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockTakingProcess">
    update stock_taking_process
    set taking_plan_no = #{takingPlanNo,jdbcType=VARCHAR},
      start_taking_plan = #{startTakingPlan,jdbcType=INTEGER},
      start_taking_plan_status = #{startTakingPlanStatus,jdbcType=INTEGER},
      start_taking_plan_finish_date = #{startTakingPlanFinishDate,jdbcType=TIMESTAMP},
      generate_taking_snapshot = #{generateTakingSnapshot,jdbcType=INTEGER},
      generate_taking_snapshot_status = #{generateTakingSnapshotStatus,jdbcType=INTEGER},
      generate_taking_snapshot_date = #{generateTakingSnapshotDate,jdbcType=TIMESTAMP},
      confirm_taking_snapshot = #{confirmTakingSnapshot,jdbcType=INTEGER},
      confirm_taking_snapshot_status = #{confirmTakingSnapshotStatus,jdbcType=INTEGER},
      confirm_taking_snapshot_date = #{confirmTakingSnapshotDate,jdbcType=TIMESTAMP},
      create_taking_task = #{createTakingTask,jdbcType=INTEGER},
      create_taking_task_status = #{createTakingTaskStatus,jdbcType=INTEGER},
      create_taking_task_date = #{createTakingTaskDate,jdbcType=TIMESTAMP},
      assign_taking_task = #{assignTakingTask,jdbcType=INTEGER},
      assign_taking_task_status = #{assignTakingTaskStatus,jdbcType=INTEGER},
      assign_taking_task_date = #{assignTakingTaskDate,jdbcType=TIMESTAMP},
      input_taking_data = #{inputTakingData,jdbcType=INTEGER},
      input_taking_data_status = #{inputTakingDataStatus,jdbcType=INTEGER},
      input_taking_data_date = #{inputTakingDataDate,jdbcType=TIMESTAMP},
      generate_taking_result = #{generateTakingResult,jdbcType=INTEGER},
      generate_taking_result_status = #{generateTakingResultStatus,jdbcType=INTEGER},
      generate_taking_result_date = #{generateTakingResultDate,jdbcType=TIMESTAMP},
      approval_taking = #{approvalTaking,jdbcType=INTEGER},
      approval_taking_status = #{approvalTakingStatus,jdbcType=INTEGER},
      approval_taking_date = #{approvalTakingDate,jdbcType=TIMESTAMP},
      perform_taking_adjust = #{performTakingAdjust,jdbcType=INTEGER},
      perform_taking_adjust_status = #{performTakingAdjustStatus,jdbcType=INTEGER},
      perform_taking_adjust_date = #{performTakingAdjustDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>