<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsRepairHeadMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsRepairHead">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="repair_no" jdbcType="VARCHAR" property="repairNo" />
    <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
    <result column="billing_date" jdbcType="TIMESTAMP" property="billingDate" />
    <result column="repair_type" jdbcType="INTEGER" property="repairType" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="repair_number" jdbcType="DECIMAL" property="repairNumber" />
    <result column="repair_total_money" jdbcType="DECIMAL" property="repairTotalMoney" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="form_id" jdbcType="VARCHAR" property="formId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, repair_no, billing_user, billing_date, repair_type, supplier_code, status, repair_number, 
    repair_total_money, remark, form_id, created_by, created_at, updated_by, updated_at, 
    is_valid
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairHeadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_repair_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_repair_head
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_repair_head
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairHead">
    insert into stock_assets_repair_head (id, repair_no, billing_user, 
      billing_date, repair_type, supplier_code, 
      status, repair_number, repair_total_money, 
      remark, form_id, created_by, 
      created_at, updated_by, updated_at, 
      is_valid)
    values (#{id,jdbcType=BIGINT}, #{repairNo,jdbcType=VARCHAR}, #{billingUser,jdbcType=VARCHAR}, 
      #{billingDate,jdbcType=TIMESTAMP}, #{repairType,jdbcType=INTEGER}, #{supplierCode,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{repairNumber,jdbcType=DECIMAL}, #{repairTotalMoney,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{formId,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{isValid,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairHead">
    insert into stock_assets_repair_head
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="repairNo != null">
        repair_no,
      </if>
      <if test="billingUser != null">
        billing_user,
      </if>
      <if test="billingDate != null">
        billing_date,
      </if>
      <if test="repairType != null">
        repair_type,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="repairNumber != null">
        repair_number,
      </if>
      <if test="repairTotalMoney != null">
        repair_total_money,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="repairNo != null">
        #{repairNo,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingDate != null">
        #{billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="repairType != null">
        #{repairType,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="repairNumber != null">
        #{repairNumber,jdbcType=DECIMAL},
      </if>
      <if test="repairTotalMoney != null">
        #{repairTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairHeadExample" resultType="java.lang.Long">
    select count(*) from stock_assets_repair_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_repair_head
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.repairNo != null">
        repair_no = #{record.repairNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billingUser != null">
        billing_user = #{record.billingUser,jdbcType=VARCHAR},
      </if>
      <if test="record.billingDate != null">
        billing_date = #{record.billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.repairType != null">
        repair_type = #{record.repairType,jdbcType=INTEGER},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.repairNumber != null">
        repair_number = #{record.repairNumber,jdbcType=DECIMAL},
      </if>
      <if test="record.repairTotalMoney != null">
        repair_total_money = #{record.repairTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_repair_head
    set id = #{record.id,jdbcType=BIGINT},
      repair_no = #{record.repairNo,jdbcType=VARCHAR},
      billing_user = #{record.billingUser,jdbcType=VARCHAR},
      billing_date = #{record.billingDate,jdbcType=TIMESTAMP},
      repair_type = #{record.repairType,jdbcType=INTEGER},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      repair_number = #{record.repairNumber,jdbcType=DECIMAL},
      repair_total_money = #{record.repairTotalMoney,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      is_valid = #{record.isValid,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairHead">
    update stock_assets_repair_head
    <set>
      <if test="repairNo != null">
        repair_no = #{repairNo,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        billing_user = #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingDate != null">
        billing_date = #{billingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="repairType != null">
        repair_type = #{repairType,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="repairNumber != null">
        repair_number = #{repairNumber,jdbcType=DECIMAL},
      </if>
      <if test="repairTotalMoney != null">
        repair_total_money = #{repairTotalMoney,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairHead">
    update stock_assets_repair_head
    set repair_no = #{repairNo,jdbcType=VARCHAR},
      billing_user = #{billingUser,jdbcType=VARCHAR},
      billing_date = #{billingDate,jdbcType=TIMESTAMP},
      repair_type = #{repairType,jdbcType=INTEGER},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      repair_number = #{repairNumber,jdbcType=DECIMAL},
      repair_total_money = #{repairTotalMoney,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      is_valid = #{isValid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>