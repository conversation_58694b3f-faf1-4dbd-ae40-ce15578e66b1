<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockGpsSnsTailMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockGpsSnsTail">
    <id column="sn_id" jdbcType="BIGINT" property="snId" />
    <result column="sn_no" jdbcType="VARCHAR" property="snNo" />
    <result column="inventory_in_plan_no" jdbcType="VARCHAR" property="inventoryInPlanNo" />
    <result column="inventory_in_no" jdbcType="VARCHAR" property="inventoryInNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="sim_company_code" jdbcType="VARCHAR" property="simCompanyCode" />
    <result column="serve_company_code" jdbcType="VARCHAR" property="serveCompanyCode" />
    <result column="gps_unit_price" jdbcType="DECIMAL" property="gpsUnitPrice" />
    <result column="gps_tax_rate" jdbcType="DECIMAL" property="gpsTaxRate" />
    <result column="sim_unit_price" jdbcType="DECIMAL" property="simUnitPrice" />
    <result column="sim_tax_rate" jdbcType="DECIMAL" property="simTaxRate" />
    <result column="serve_unit_price" jdbcType="DECIMAL" property="serveUnitPrice" />
    <result column="serve_tax_rate" jdbcType="DECIMAL" property="serveTaxRate" />
    <result column="install_status" jdbcType="INTEGER" property="installStatus" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="sim_re_new_unit_price" jdbcType="DECIMAL" property="simReNewUnitPrice" />
    <result column="sim_re_new_tax_rate" jdbcType="DECIMAL" property="simReNewTaxRate" />
    <result column="serve_re_new_unit_price" jdbcType="DECIMAL" property="serveReNewUnitPrice" />
    <result column="serve_re_new_tax_rate" jdbcType="DECIMAL" property="serveReNewTaxRate" />
    <result column="sim_group_re_new_unit_price" jdbcType="DECIMAL" property="simGroupReNewUnitPrice" />
    <result column="sim_group_re_new_tax_rate" jdbcType="DECIMAL" property="simGroupReNewTaxRate" />
    <result column="serve_group_re_new_unit_price" jdbcType="DECIMAL" property="serveGroupReNewUnitPrice" />
    <result column="serve_group_re_new_tax_rate" jdbcType="DECIMAL" property="serveGroupReNewTaxRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    sn_id, sn_no, inventory_in_plan_no, inventory_in_no, company_code, sim_company_code, 
    serve_company_code, gps_unit_price, gps_tax_rate, sim_unit_price, sim_tax_rate, serve_unit_price, 
    serve_tax_rate, install_status, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT, version, 
    sim_re_new_unit_price, sim_re_new_tax_rate, serve_re_new_unit_price, serve_re_new_tax_rate, 
    sim_group_re_new_unit_price, sim_group_re_new_tax_rate, serve_group_re_new_unit_price, 
    serve_group_re_new_tax_rate
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockGpsSnsTailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_gps_sns_tail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_gps_sns_tail
    where sn_id = #{snId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_gps_sns_tail
    where sn_id = #{snId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockGpsSnsTail">
    insert into stock_gps_sns_tail (sn_id, sn_no, inventory_in_plan_no, 
      inventory_in_no, company_code, sim_company_code, 
      serve_company_code, gps_unit_price, gps_tax_rate, 
      sim_unit_price, sim_tax_rate, serve_unit_price, 
      serve_tax_rate, install_status, CREATED_BY, 
      CREATED_AT, UPDATED_BY, UPDATED_AT, 
      version, sim_re_new_unit_price, sim_re_new_tax_rate, 
      serve_re_new_unit_price, serve_re_new_tax_rate, 
      sim_group_re_new_unit_price, sim_group_re_new_tax_rate, 
      serve_group_re_new_unit_price, serve_group_re_new_tax_rate
      )
    values (#{snId,jdbcType=BIGINT}, #{snNo,jdbcType=VARCHAR}, #{inventoryInPlanNo,jdbcType=VARCHAR}, 
      #{inventoryInNo,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{simCompanyCode,jdbcType=VARCHAR}, 
      #{serveCompanyCode,jdbcType=VARCHAR}, #{gpsUnitPrice,jdbcType=DECIMAL}, #{gpsTaxRate,jdbcType=DECIMAL}, 
      #{simUnitPrice,jdbcType=DECIMAL}, #{simTaxRate,jdbcType=DECIMAL}, #{serveUnitPrice,jdbcType=DECIMAL}, 
      #{serveTaxRate,jdbcType=DECIMAL}, #{installStatus,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=VARCHAR}, #{simReNewUnitPrice,jdbcType=DECIMAL}, #{simReNewTaxRate,jdbcType=DECIMAL}, 
      #{serveReNewUnitPrice,jdbcType=DECIMAL}, #{serveReNewTaxRate,jdbcType=DECIMAL}, 
      #{simGroupReNewUnitPrice,jdbcType=DECIMAL}, #{simGroupReNewTaxRate,jdbcType=DECIMAL}, 
      #{serveGroupReNewUnitPrice,jdbcType=DECIMAL}, #{serveGroupReNewTaxRate,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockGpsSnsTail">
    insert into stock_gps_sns_tail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="snId != null">
        sn_id,
      </if>
      <if test="snNo != null">
        sn_no,
      </if>
      <if test="inventoryInPlanNo != null">
        inventory_in_plan_no,
      </if>
      <if test="inventoryInNo != null">
        inventory_in_no,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="simCompanyCode != null">
        sim_company_code,
      </if>
      <if test="serveCompanyCode != null">
        serve_company_code,
      </if>
      <if test="gpsUnitPrice != null">
        gps_unit_price,
      </if>
      <if test="gpsTaxRate != null">
        gps_tax_rate,
      </if>
      <if test="simUnitPrice != null">
        sim_unit_price,
      </if>
      <if test="simTaxRate != null">
        sim_tax_rate,
      </if>
      <if test="serveUnitPrice != null">
        serve_unit_price,
      </if>
      <if test="serveTaxRate != null">
        serve_tax_rate,
      </if>
      <if test="installStatus != null">
        install_status,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="simReNewUnitPrice != null">
        sim_re_new_unit_price,
      </if>
      <if test="simReNewTaxRate != null">
        sim_re_new_tax_rate,
      </if>
      <if test="serveReNewUnitPrice != null">
        serve_re_new_unit_price,
      </if>
      <if test="serveReNewTaxRate != null">
        serve_re_new_tax_rate,
      </if>
      <if test="simGroupReNewUnitPrice != null">
        sim_group_re_new_unit_price,
      </if>
      <if test="simGroupReNewTaxRate != null">
        sim_group_re_new_tax_rate,
      </if>
      <if test="serveGroupReNewUnitPrice != null">
        serve_group_re_new_unit_price,
      </if>
      <if test="serveGroupReNewTaxRate != null">
        serve_group_re_new_tax_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="snId != null">
        #{snId,jdbcType=BIGINT},
      </if>
      <if test="snNo != null">
        #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInPlanNo != null">
        #{inventoryInPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInNo != null">
        #{inventoryInNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="simCompanyCode != null">
        #{simCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="serveCompanyCode != null">
        #{serveCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="gpsUnitPrice != null">
        #{gpsUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="gpsTaxRate != null">
        #{gpsTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simUnitPrice != null">
        #{simUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simTaxRate != null">
        #{simTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveUnitPrice != null">
        #{serveUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveTaxRate != null">
        #{serveTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="installStatus != null">
        #{installStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="simReNewUnitPrice != null">
        #{simReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simReNewTaxRate != null">
        #{simReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewUnitPrice != null">
        #{serveReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewTaxRate != null">
        #{serveReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewUnitPrice != null">
        #{simGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewTaxRate != null">
        #{simGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewUnitPrice != null">
        #{serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewTaxRate != null">
        #{serveGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockGpsSnsTailExample" resultType="java.lang.Long">
    select count(*) from stock_gps_sns_tail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_gps_sns_tail
    <set>
      <if test="record.snId != null">
        sn_id = #{record.snId,jdbcType=BIGINT},
      </if>
      <if test="record.snNo != null">
        sn_no = #{record.snNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryInPlanNo != null">
        inventory_in_plan_no = #{record.inventoryInPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryInNo != null">
        inventory_in_no = #{record.inventoryInNo,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.simCompanyCode != null">
        sim_company_code = #{record.simCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.serveCompanyCode != null">
        serve_company_code = #{record.serveCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gpsUnitPrice != null">
        gps_unit_price = #{record.gpsUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.gpsTaxRate != null">
        gps_tax_rate = #{record.gpsTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.simUnitPrice != null">
        sim_unit_price = #{record.simUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.simTaxRate != null">
        sim_tax_rate = #{record.simTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.serveUnitPrice != null">
        serve_unit_price = #{record.serveUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serveTaxRate != null">
        serve_tax_rate = #{record.serveTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.installStatus != null">
        install_status = #{record.installStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.simReNewUnitPrice != null">
        sim_re_new_unit_price = #{record.simReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.simReNewTaxRate != null">
        sim_re_new_tax_rate = #{record.simReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.serveReNewUnitPrice != null">
        serve_re_new_unit_price = #{record.serveReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serveReNewTaxRate != null">
        serve_re_new_tax_rate = #{record.serveReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.simGroupReNewUnitPrice != null">
        sim_group_re_new_unit_price = #{record.simGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.simGroupReNewTaxRate != null">
        sim_group_re_new_tax_rate = #{record.simGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.serveGroupReNewUnitPrice != null">
        serve_group_re_new_unit_price = #{record.serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serveGroupReNewTaxRate != null">
        serve_group_re_new_tax_rate = #{record.serveGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_gps_sns_tail
    set sn_id = #{record.snId,jdbcType=BIGINT},
      sn_no = #{record.snNo,jdbcType=VARCHAR},
      inventory_in_plan_no = #{record.inventoryInPlanNo,jdbcType=VARCHAR},
      inventory_in_no = #{record.inventoryInNo,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      sim_company_code = #{record.simCompanyCode,jdbcType=VARCHAR},
      serve_company_code = #{record.serveCompanyCode,jdbcType=VARCHAR},
      gps_unit_price = #{record.gpsUnitPrice,jdbcType=DECIMAL},
      gps_tax_rate = #{record.gpsTaxRate,jdbcType=DECIMAL},
      sim_unit_price = #{record.simUnitPrice,jdbcType=DECIMAL},
      sim_tax_rate = #{record.simTaxRate,jdbcType=DECIMAL},
      serve_unit_price = #{record.serveUnitPrice,jdbcType=DECIMAL},
      serve_tax_rate = #{record.serveTaxRate,jdbcType=DECIMAL},
      install_status = #{record.installStatus,jdbcType=INTEGER},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=VARCHAR},
      sim_re_new_unit_price = #{record.simReNewUnitPrice,jdbcType=DECIMAL},
      sim_re_new_tax_rate = #{record.simReNewTaxRate,jdbcType=DECIMAL},
      serve_re_new_unit_price = #{record.serveReNewUnitPrice,jdbcType=DECIMAL},
      serve_re_new_tax_rate = #{record.serveReNewTaxRate,jdbcType=DECIMAL},
      sim_group_re_new_unit_price = #{record.simGroupReNewUnitPrice,jdbcType=DECIMAL},
      sim_group_re_new_tax_rate = #{record.simGroupReNewTaxRate,jdbcType=DECIMAL},
      serve_group_re_new_unit_price = #{record.serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      serve_group_re_new_tax_rate = #{record.serveGroupReNewTaxRate,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockGpsSnsTail">
    update stock_gps_sns_tail
    <set>
      <if test="snNo != null">
        sn_no = #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInPlanNo != null">
        inventory_in_plan_no = #{inventoryInPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="inventoryInNo != null">
        inventory_in_no = #{inventoryInNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="simCompanyCode != null">
        sim_company_code = #{simCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="serveCompanyCode != null">
        serve_company_code = #{serveCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="gpsUnitPrice != null">
        gps_unit_price = #{gpsUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="gpsTaxRate != null">
        gps_tax_rate = #{gpsTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simUnitPrice != null">
        sim_unit_price = #{simUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simTaxRate != null">
        sim_tax_rate = #{simTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveUnitPrice != null">
        serve_unit_price = #{serveUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveTaxRate != null">
        serve_tax_rate = #{serveTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="installStatus != null">
        install_status = #{installStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="simReNewUnitPrice != null">
        sim_re_new_unit_price = #{simReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simReNewTaxRate != null">
        sim_re_new_tax_rate = #{simReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewUnitPrice != null">
        serve_re_new_unit_price = #{serveReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewTaxRate != null">
        serve_re_new_tax_rate = #{serveReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewUnitPrice != null">
        sim_group_re_new_unit_price = #{simGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewTaxRate != null">
        sim_group_re_new_tax_rate = #{simGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewUnitPrice != null">
        serve_group_re_new_unit_price = #{serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewTaxRate != null">
        serve_group_re_new_tax_rate = #{serveGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
    </set>
    where sn_id = #{snId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockGpsSnsTail">
    update stock_gps_sns_tail
    set sn_no = #{snNo,jdbcType=VARCHAR},
      inventory_in_plan_no = #{inventoryInPlanNo,jdbcType=VARCHAR},
      inventory_in_no = #{inventoryInNo,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      sim_company_code = #{simCompanyCode,jdbcType=VARCHAR},
      serve_company_code = #{serveCompanyCode,jdbcType=VARCHAR},
      gps_unit_price = #{gpsUnitPrice,jdbcType=DECIMAL},
      gps_tax_rate = #{gpsTaxRate,jdbcType=DECIMAL},
      sim_unit_price = #{simUnitPrice,jdbcType=DECIMAL},
      sim_tax_rate = #{simTaxRate,jdbcType=DECIMAL},
      serve_unit_price = #{serveUnitPrice,jdbcType=DECIMAL},
      serve_tax_rate = #{serveTaxRate,jdbcType=DECIMAL},
      install_status = #{installStatus,jdbcType=INTEGER},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=VARCHAR},
      sim_re_new_unit_price = #{simReNewUnitPrice,jdbcType=DECIMAL},
      sim_re_new_tax_rate = #{simReNewTaxRate,jdbcType=DECIMAL},
      serve_re_new_unit_price = #{serveReNewUnitPrice,jdbcType=DECIMAL},
      serve_re_new_tax_rate = #{serveReNewTaxRate,jdbcType=DECIMAL},
      sim_group_re_new_unit_price = #{simGroupReNewUnitPrice,jdbcType=DECIMAL},
      sim_group_re_new_tax_rate = #{simGroupReNewTaxRate,jdbcType=DECIMAL},
      serve_group_re_new_unit_price = #{serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      serve_group_re_new_tax_rate = #{serveGroupReNewTaxRate,jdbcType=DECIMAL}
    where sn_id = #{snId,jdbcType=BIGINT}
  </update>
</mapper>