<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAllocateImportLineMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAllocateImportLine">
    <id column="line_id" jdbcType="BIGINT" property="lineId" />
    <result column="head_id" jdbcType="BIGINT" property="headId" />
    <result column="out_import_warehouse_name" jdbcType="VARCHAR" property="outImportWarehouseName" />
    <result column="out_warehouse_code" jdbcType="VARCHAR" property="outWarehouseCode" />
    <result column="in_import_warehouse_name" jdbcType="VARCHAR" property="inImportWarehouseName" />
    <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="reason_code" jdbcType="INTEGER" property="reasonCode" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    line_id, head_id, out_import_warehouse_name, out_warehouse_code, in_import_warehouse_name, 
    in_warehouse_code, supplies_code, number, error_message, status, reason_code, del_flag, 
    CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT, assets_code
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_allocate_import_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_allocate_import_lines
    where line_id = #{lineId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_allocate_import_lines
    where line_id = #{lineId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLine">
    insert into stock_allocate_import_lines (line_id, head_id, out_import_warehouse_name, 
      out_warehouse_code, in_import_warehouse_name, 
      in_warehouse_code, supplies_code, number, 
      error_message, status, reason_code, 
      del_flag, CREATED_BY, CREATED_AT, 
      UPDATED_BY, UPDATED_AT, assets_code
      )
    values (#{lineId,jdbcType=BIGINT}, #{headId,jdbcType=BIGINT}, #{outImportWarehouseName,jdbcType=VARCHAR}, 
      #{outWarehouseCode,jdbcType=VARCHAR}, #{inImportWarehouseName,jdbcType=VARCHAR}, 
      #{inWarehouseCode,jdbcType=VARCHAR}, #{suppliesCode,jdbcType=VARCHAR}, #{number,jdbcType=INTEGER}, 
      #{errorMessage,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{reasonCode,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, #{assetsCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLine">
    insert into stock_allocate_import_lines
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lineId != null">
        line_id,
      </if>
      <if test="headId != null">
        head_id,
      </if>
      <if test="outImportWarehouseName != null">
        out_import_warehouse_name,
      </if>
      <if test="outWarehouseCode != null">
        out_warehouse_code,
      </if>
      <if test="inImportWarehouseName != null">
        in_import_warehouse_name,
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="errorMessage != null">
        error_message,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
      <if test="assetsCode != null">
        assets_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lineId != null">
        #{lineId,jdbcType=BIGINT},
      </if>
      <if test="headId != null">
        #{headId,jdbcType=BIGINT},
      </if>
      <if test="outImportWarehouseName != null">
        #{outImportWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseCode != null">
        #{outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inImportWarehouseName != null">
        #{inImportWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseCode != null">
        #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="errorMessage != null">
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLineExample" resultType="java.lang.Long">
    select count(*) from stock_allocate_import_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_allocate_import_lines
    <set>
      <if test="record.lineId != null">
        line_id = #{record.lineId,jdbcType=BIGINT},
      </if>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=BIGINT},
      </if>
      <if test="record.outImportWarehouseName != null">
        out_import_warehouse_name = #{record.outImportWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.outWarehouseCode != null">
        out_warehouse_code = #{record.outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inImportWarehouseName != null">
        in_import_warehouse_name = #{record.inImportWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.inWarehouseCode != null">
        in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.errorMessage != null">
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.reasonCode != null">
        reason_code = #{record.reasonCode,jdbcType=INTEGER},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_allocate_import_lines
    set line_id = #{record.lineId,jdbcType=BIGINT},
      head_id = #{record.headId,jdbcType=BIGINT},
      out_import_warehouse_name = #{record.outImportWarehouseName,jdbcType=VARCHAR},
      out_warehouse_code = #{record.outWarehouseCode,jdbcType=VARCHAR},
      in_import_warehouse_name = #{record.inImportWarehouseName,jdbcType=VARCHAR},
      in_warehouse_code = #{record.inWarehouseCode,jdbcType=VARCHAR},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=INTEGER},
      error_message = #{record.errorMessage,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      reason_code = #{record.reasonCode,jdbcType=INTEGER},
      del_flag = #{record.delFlag,jdbcType=INTEGER},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLine">
    update stock_allocate_import_lines
    <set>
      <if test="headId != null">
        head_id = #{headId,jdbcType=BIGINT},
      </if>
      <if test="outImportWarehouseName != null">
        out_import_warehouse_name = #{outImportWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="outWarehouseCode != null">
        out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="inImportWarehouseName != null">
        in_import_warehouse_name = #{inImportWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="inWarehouseCode != null">
        in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="errorMessage != null">
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
    </set>
    where line_id = #{lineId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLine">
    update stock_allocate_import_lines
    set head_id = #{headId,jdbcType=BIGINT},
      out_import_warehouse_name = #{outImportWarehouseName,jdbcType=VARCHAR},
      out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
      in_import_warehouse_name = #{inImportWarehouseName,jdbcType=VARCHAR},
      in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      number = #{number,jdbcType=INTEGER},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      reason_code = #{reasonCode,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      assets_code = #{assetsCode,jdbcType=VARCHAR}
    where line_id = #{lineId,jdbcType=BIGINT}
  </update>
</mapper>