<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsAttrConfigMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsAttrConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="attr_name" jdbcType="VARCHAR" property="attrName" />
    <result column="attr_code" jdbcType="VARCHAR" property="attrCode" />
    <result column="attr_type" jdbcType="VARCHAR" property="attrType" />
    <result column="in_required" jdbcType="TINYINT" property="inRequired" />
    <result column="mapping_attr" jdbcType="VARCHAR" property="mappingAttr" />
    <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_code, category, attr_name, attr_code, attr_type, in_required, mapping_attr, 
    order_number, del_flag, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsAttrConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_attr_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_attr_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_attr_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsAttrConfig">
    insert into stock_assets_attr_config (id, category_code, category, 
      attr_name, attr_code, attr_type, 
      in_required, mapping_attr, order_number, 
      del_flag, created_by, created_at, 
      updated_by, updated_at)
    values (#{id,jdbcType=BIGINT}, #{categoryCode,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, 
      #{attrName,jdbcType=VARCHAR}, #{attrCode,jdbcType=VARCHAR}, #{attrType,jdbcType=VARCHAR}, 
      #{inRequired,jdbcType=TINYINT}, #{mappingAttr,jdbcType=VARCHAR}, #{orderNumber,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsAttrConfig">
    insert into stock_assets_attr_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="attrName != null">
        attr_name,
      </if>
      <if test="attrCode != null">
        attr_code,
      </if>
      <if test="attrType != null">
        attr_type,
      </if>
      <if test="inRequired != null">
        in_required,
      </if>
      <if test="mappingAttr != null">
        mapping_attr,
      </if>
      <if test="orderNumber != null">
        order_number,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="attrName != null">
        #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrCode != null">
        #{attrCode,jdbcType=VARCHAR},
      </if>
      <if test="attrType != null">
        #{attrType,jdbcType=VARCHAR},
      </if>
      <if test="inRequired != null">
        #{inRequired,jdbcType=TINYINT},
      </if>
      <if test="mappingAttr != null">
        #{mappingAttr,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null">
        #{orderNumber,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsAttrConfigExample" resultType="java.lang.Long">
    select count(*) from stock_assets_attr_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_attr_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryCode != null">
        category_code = #{record.categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.attrName != null">
        attr_name = #{record.attrName,jdbcType=VARCHAR},
      </if>
      <if test="record.attrCode != null">
        attr_code = #{record.attrCode,jdbcType=VARCHAR},
      </if>
      <if test="record.attrType != null">
        attr_type = #{record.attrType,jdbcType=VARCHAR},
      </if>
      <if test="record.inRequired != null">
        in_required = #{record.inRequired,jdbcType=TINYINT},
      </if>
      <if test="record.mappingAttr != null">
        mapping_attr = #{record.mappingAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNumber != null">
        order_number = #{record.orderNumber,jdbcType=INTEGER},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_attr_config
    set id = #{record.id,jdbcType=BIGINT},
      category_code = #{record.categoryCode,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      attr_name = #{record.attrName,jdbcType=VARCHAR},
      attr_code = #{record.attrCode,jdbcType=VARCHAR},
      attr_type = #{record.attrType,jdbcType=VARCHAR},
      in_required = #{record.inRequired,jdbcType=TINYINT},
      mapping_attr = #{record.mappingAttr,jdbcType=VARCHAR},
      order_number = #{record.orderNumber,jdbcType=INTEGER},
      del_flag = #{record.delFlag,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsAttrConfig">
    update stock_assets_attr_config
    <set>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="attrName != null">
        attr_name = #{attrName,jdbcType=VARCHAR},
      </if>
      <if test="attrCode != null">
        attr_code = #{attrCode,jdbcType=VARCHAR},
      </if>
      <if test="attrType != null">
        attr_type = #{attrType,jdbcType=VARCHAR},
      </if>
      <if test="inRequired != null">
        in_required = #{inRequired,jdbcType=TINYINT},
      </if>
      <if test="mappingAttr != null">
        mapping_attr = #{mappingAttr,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null">
        order_number = #{orderNumber,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsAttrConfig">
    update stock_assets_attr_config
    set category_code = #{categoryCode,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      attr_name = #{attrName,jdbcType=VARCHAR},
      attr_code = #{attrCode,jdbcType=VARCHAR},
      attr_type = #{attrType,jdbcType=VARCHAR},
      in_required = #{inRequired,jdbcType=TINYINT},
      mapping_attr = #{mappingAttr,jdbcType=VARCHAR},
      order_number = #{orderNumber,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>