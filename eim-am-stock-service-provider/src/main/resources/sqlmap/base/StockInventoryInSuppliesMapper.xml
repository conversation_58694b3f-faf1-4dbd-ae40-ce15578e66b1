<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockInventoryInSuppliesMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInSupplies">
    <id column="in_supplies_id" jdbcType="BIGINT" property="inSuppliesId" />
    <result column="inventory_in_id" jdbcType="BIGINT" property="inventoryInId" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="real_number" jdbcType="INTEGER" property="realNumber" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="quality" jdbcType="INTEGER" property="quality" />
    <result column="difference_reason" jdbcType="VARCHAR" property="differenceReason" />
    <result column="volume" jdbcType="DECIMAL" property="volume" />
    <result column="volume_amount" jdbcType="DECIMAL" property="volumeAmount" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="sn_no" jdbcType="VARCHAR" property="snNo" />
    <result column="print_label_type" jdbcType="INTEGER" property="printLabelType" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="inventory_in_time" jdbcType="TIMESTAMP" property="inventoryInTime" />
    <result column="inventory_in_plan_line_id" jdbcType="BIGINT" property="inventoryInPlanLineId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    in_supplies_id, inventory_in_id, supplies_code, number, real_number, status, price, 
    quality, difference_reason, volume, volume_amount, batch_no, sn_no, print_label_type, 
    created_by, created_at, updated_by, updated_at, inventory_in_time, inventory_in_plan_line_id
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSuppliesExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_inventory_in_supplies
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_inventory_in_supplies
    where in_supplies_id = #{inSuppliesId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_inventory_in_supplies
    where in_supplies_id = #{inSuppliesId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSupplies">
    insert into stock_inventory_in_supplies (in_supplies_id, inventory_in_id, supplies_code, 
      number, real_number, status, 
      price, quality, difference_reason, 
      volume, volume_amount, batch_no, 
      sn_no, print_label_type, created_by, 
      created_at, updated_by, updated_at, 
      inventory_in_time, inventory_in_plan_line_id
      )
    values (#{inSuppliesId,jdbcType=BIGINT}, #{inventoryInId,jdbcType=BIGINT}, #{suppliesCode,jdbcType=VARCHAR}, 
      #{number,jdbcType=INTEGER}, #{realNumber,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{price,jdbcType=INTEGER}, #{quality,jdbcType=INTEGER}, #{differenceReason,jdbcType=VARCHAR}, 
      #{volume,jdbcType=DECIMAL}, #{volumeAmount,jdbcType=DECIMAL}, #{batchNo,jdbcType=VARCHAR}, 
      #{snNo,jdbcType=VARCHAR}, #{printLabelType,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{inventoryInTime,jdbcType=TIMESTAMP}, #{inventoryInPlanLineId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSupplies">
    insert into stock_inventory_in_supplies
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="inSuppliesId != null">
        in_supplies_id,
      </if>
      <if test="inventoryInId != null">
        inventory_in_id,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="realNumber != null">
        real_number,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="quality != null">
        quality,
      </if>
      <if test="differenceReason != null">
        difference_reason,
      </if>
      <if test="volume != null">
        volume,
      </if>
      <if test="volumeAmount != null">
        volume_amount,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="snNo != null">
        sn_no,
      </if>
      <if test="printLabelType != null">
        print_label_type,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="inventoryInTime != null">
        inventory_in_time,
      </if>
      <if test="inventoryInPlanLineId != null">
        inventory_in_plan_line_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="inSuppliesId != null">
        #{inSuppliesId,jdbcType=BIGINT},
      </if>
      <if test="inventoryInId != null">
        #{inventoryInId,jdbcType=BIGINT},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=INTEGER},
      </if>
      <if test="quality != null">
        #{quality,jdbcType=INTEGER},
      </if>
      <if test="differenceReason != null">
        #{differenceReason,jdbcType=VARCHAR},
      </if>
      <if test="volume != null">
        #{volume,jdbcType=DECIMAL},
      </if>
      <if test="volumeAmount != null">
        #{volumeAmount,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="snNo != null">
        #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="printLabelType != null">
        #{printLabelType,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryInTime != null">
        #{inventoryInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryInPlanLineId != null">
        #{inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSuppliesExample" resultType="java.lang.Long">
    select count(*) from stock_inventory_in_supplies
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_inventory_in_supplies
    <set>
      <if test="record.inSuppliesId != null">
        in_supplies_id = #{record.inSuppliesId,jdbcType=BIGINT},
      </if>
      <if test="record.inventoryInId != null">
        inventory_in_id = #{record.inventoryInId,jdbcType=BIGINT},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.realNumber != null">
        real_number = #{record.realNumber,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=INTEGER},
      </if>
      <if test="record.quality != null">
        quality = #{record.quality,jdbcType=INTEGER},
      </if>
      <if test="record.differenceReason != null">
        difference_reason = #{record.differenceReason,jdbcType=VARCHAR},
      </if>
      <if test="record.volume != null">
        volume = #{record.volume,jdbcType=DECIMAL},
      </if>
      <if test="record.volumeAmount != null">
        volume_amount = #{record.volumeAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.snNo != null">
        sn_no = #{record.snNo,jdbcType=VARCHAR},
      </if>
      <if test="record.printLabelType != null">
        print_label_type = #{record.printLabelType,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inventoryInTime != null">
        inventory_in_time = #{record.inventoryInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inventoryInPlanLineId != null">
        inventory_in_plan_line_id = #{record.inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_inventory_in_supplies
    set in_supplies_id = #{record.inSuppliesId,jdbcType=BIGINT},
      inventory_in_id = #{record.inventoryInId,jdbcType=BIGINT},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=INTEGER},
      real_number = #{record.realNumber,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      price = #{record.price,jdbcType=INTEGER},
      quality = #{record.quality,jdbcType=INTEGER},
      difference_reason = #{record.differenceReason,jdbcType=VARCHAR},
      volume = #{record.volume,jdbcType=DECIMAL},
      volume_amount = #{record.volumeAmount,jdbcType=DECIMAL},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      sn_no = #{record.snNo,jdbcType=VARCHAR},
      print_label_type = #{record.printLabelType,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      inventory_in_time = #{record.inventoryInTime,jdbcType=TIMESTAMP},
      inventory_in_plan_line_id = #{record.inventoryInPlanLineId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSupplies">
    update stock_inventory_in_supplies
    <set>
      <if test="inventoryInId != null">
        inventory_in_id = #{inventoryInId,jdbcType=BIGINT},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        real_number = #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="quality != null">
        quality = #{quality,jdbcType=INTEGER},
      </if>
      <if test="differenceReason != null">
        difference_reason = #{differenceReason,jdbcType=VARCHAR},
      </if>
      <if test="volume != null">
        volume = #{volume,jdbcType=DECIMAL},
      </if>
      <if test="volumeAmount != null">
        volume_amount = #{volumeAmount,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="snNo != null">
        sn_no = #{snNo,jdbcType=VARCHAR},
      </if>
      <if test="printLabelType != null">
        print_label_type = #{printLabelType,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryInTime != null">
        inventory_in_time = #{inventoryInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryInPlanLineId != null">
        inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
    </set>
    where in_supplies_id = #{inSuppliesId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSupplies">
    update stock_inventory_in_supplies
    set inventory_in_id = #{inventoryInId,jdbcType=BIGINT},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      number = #{number,jdbcType=INTEGER},
      real_number = #{realNumber,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      price = #{price,jdbcType=INTEGER},
      quality = #{quality,jdbcType=INTEGER},
      difference_reason = #{differenceReason,jdbcType=VARCHAR},
      volume = #{volume,jdbcType=DECIMAL},
      volume_amount = #{volumeAmount,jdbcType=DECIMAL},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      sn_no = #{snNo,jdbcType=VARCHAR},
      print_label_type = #{printLabelType,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      inventory_in_time = #{inventoryInTime,jdbcType=TIMESTAMP},
      inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT}
    where in_supplies_id = #{inSuppliesId,jdbcType=BIGINT}
  </update>
</mapper>