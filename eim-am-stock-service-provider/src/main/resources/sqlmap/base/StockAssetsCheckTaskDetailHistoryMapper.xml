<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsCheckTaskDetailHistoryMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_detail_id" jdbcType="BIGINT" property="taskDetailId" />
    <result column="check_task_id" jdbcType="BIGINT" property="checkTaskId" />
    <result column="taking_plan_no" jdbcType="VARCHAR" property="takingPlanNo" />
    <result column="check_people" jdbcType="VARCHAR" property="checkPeople" />
    <result column="bus_large_region" jdbcType="VARCHAR" property="busLargeRegion" />
    <result column="bus_city" jdbcType="VARCHAR" property="busCity" />
    <result column="assets_keeper" jdbcType="VARCHAR" property="assetsKeeper" />
    <result column="holder_address_province" jdbcType="VARCHAR" property="holderAddressProvince" />
    <result column="holder_address_city" jdbcType="VARCHAR" property="holderAddressCity" />
    <result column="holder_address_county" jdbcType="VARCHAR" property="holderAddressCounty" />
    <result column="snapshot_assets_code" jdbcType="VARCHAR" property="snapshotAssetsCode" />
    <result column="snapshot_assets_name" jdbcType="VARCHAR" property="snapshotAssetsName" />
    <result column="snapshot_sn_no" jdbcType="VARCHAR" property="snapshotSnNo" />
    <result column="snapshot_assets_category" jdbcType="VARCHAR" property="snapshotAssetsCategory" />
    <result column="snapshot_assets_status" jdbcType="TINYINT" property="snapshotAssetsStatus" />
    <result column="snapshot_assets_conditions" jdbcType="TINYINT" property="snapshotAssetsConditions" />
    <result column="snapshot_assets_holder" jdbcType="VARCHAR" property="snapshotAssetsHolder" />
    <result column="snapshot_holder_time" jdbcType="TIMESTAMP" property="snapshotHolderTime" />
    <result column="snapshot_holder_address" jdbcType="VARCHAR" property="snapshotHolderAddress" />
    <result column="snapshot_warehouse_code" jdbcType="VARCHAR" property="snapshotWarehouseCode" />
    <result column="snapshot_model" jdbcType="VARCHAR" property="snapshotModel" />
    <result column="snapshot_brand" jdbcType="VARCHAR" property="snapshotBrand" />
    <result column="snapshot_assets_cpu" jdbcType="VARCHAR" property="snapshotAssetsCpu" />
    <result column="snapshot_hard_disk" jdbcType="VARCHAR" property="snapshotHardDisk" />
    <result column="snapshot_ram_memory" jdbcType="VARCHAR" property="snapshotRamMemory" />
    <result column="real_assets_cpu" jdbcType="VARCHAR" property="realAssetsCpu" />
    <result column="real_hard_disk" jdbcType="VARCHAR" property="realHardDisk" />
    <result column="real_ram_memory" jdbcType="VARCHAR" property="realRamMemory" />
    <result column="real_assets_code" jdbcType="VARCHAR" property="realAssetsCode" />
    <result column="real_assets_name" jdbcType="VARCHAR" property="realAssetsName" />
    <result column="real_assets_status" jdbcType="TINYINT" property="realAssetsStatus" />
    <result column="real_assets_conditions" jdbcType="TINYINT" property="realAssetsConditions" />
    <result column="real_assets_snno" jdbcType="VARCHAR" property="realAssetsSnno" />
    <result column="real_assets_model" jdbcType="VARCHAR" property="realAssetsModel" />
    <result column="real_assets_brand" jdbcType="VARCHAR" property="realAssetsBrand" />
    <result column="real_assets_holder" jdbcType="VARCHAR" property="realAssetsHolder" />
    <result column="real_holder_time" jdbcType="TIMESTAMP" property="realHolderTime" />
    <result column="real_holder_address" jdbcType="VARCHAR" property="realHolderAddress" />
    <result column="real_warehouse_code" jdbcType="VARCHAR" property="realWarehouseCode" />
    <result column="real_pictures_url" jdbcType="VARCHAR" property="realPicturesUrl" />
    <result column="need_dept" jdbcType="VARCHAR" property="needDept" />
    <result column="cost_dept" jdbcType="VARCHAR" property="costDept" />
    <result column="holder_dept" jdbcType="VARCHAR" property="holderDept" />
    <result column="snapshot_number" jdbcType="INTEGER" property="snapshotNumber" />
    <result column="real_number" jdbcType="INTEGER" property="realNumber" />
    <result column="difference" jdbcType="TINYINT" property="difference" />
    <result column="own_flag" jdbcType="TINYINT" property="ownFlag" />
    <result column="err_message" jdbcType="VARCHAR" property="errMessage" />
    <result column="err_desc" jdbcType="VARCHAR" property="errDesc" />
    <result column="new_insert_flag" jdbcType="TINYINT" property="newInsertFlag" />
    <result column="check_flag" jdbcType="TINYINT" property="checkFlag" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="copy_at" jdbcType="TIMESTAMP" property="copyAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_detail_id, check_task_id, taking_plan_no, check_people, bus_large_region, 
    bus_city, assets_keeper, holder_address_province, holder_address_city, holder_address_county, 
    snapshot_assets_code, snapshot_assets_name, snapshot_sn_no, snapshot_assets_category, 
    snapshot_assets_status, snapshot_assets_conditions, snapshot_assets_holder, snapshot_holder_time, 
    snapshot_holder_address, snapshot_warehouse_code, snapshot_model, snapshot_brand, 
    snapshot_assets_cpu, snapshot_hard_disk, snapshot_ram_memory, real_assets_cpu, real_hard_disk, 
    real_ram_memory, real_assets_code, real_assets_name, real_assets_status, real_assets_conditions, 
    real_assets_snno, real_assets_model, real_assets_brand, real_assets_holder, real_holder_time, 
    real_holder_address, real_warehouse_code, real_pictures_url, need_dept, cost_dept, 
    holder_dept, snapshot_number, real_number, difference, own_flag, err_message, err_desc, 
    new_insert_flag, check_flag, remark, created_by, created_at, updated_by, updated_at, 
    copy_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_check_task_detail_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_check_task_detail_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_check_task_detail_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_check_task_detail_history (task_detail_id, check_task_id, taking_plan_no, 
      check_people, bus_large_region, bus_city, 
      assets_keeper, holder_address_province, holder_address_city, 
      holder_address_county, snapshot_assets_code, 
      snapshot_assets_name, snapshot_sn_no, snapshot_assets_category, 
      snapshot_assets_status, snapshot_assets_conditions, 
      snapshot_assets_holder, snapshot_holder_time, 
      snapshot_holder_address, snapshot_warehouse_code, 
      snapshot_model, snapshot_brand, snapshot_assets_cpu, 
      snapshot_hard_disk, snapshot_ram_memory, real_assets_cpu, 
      real_hard_disk, real_ram_memory, real_assets_code, 
      real_assets_name, real_assets_status, real_assets_conditions, 
      real_assets_snno, real_assets_model, real_assets_brand, 
      real_assets_holder, real_holder_time, real_holder_address, 
      real_warehouse_code, real_pictures_url, need_dept, 
      cost_dept, holder_dept, snapshot_number, 
      real_number, difference, own_flag, 
      err_message, err_desc, new_insert_flag, 
      check_flag, remark, created_by, 
      created_at, updated_by, updated_at, 
      copy_at)
    values (#{taskDetailId,jdbcType=BIGINT}, #{checkTaskId,jdbcType=BIGINT}, #{takingPlanNo,jdbcType=VARCHAR}, 
      #{checkPeople,jdbcType=VARCHAR}, #{busLargeRegion,jdbcType=VARCHAR}, #{busCity,jdbcType=VARCHAR}, 
      #{assetsKeeper,jdbcType=VARCHAR}, #{holderAddressProvince,jdbcType=VARCHAR}, #{holderAddressCity,jdbcType=VARCHAR}, 
      #{holderAddressCounty,jdbcType=VARCHAR}, #{snapshotAssetsCode,jdbcType=VARCHAR}, 
      #{snapshotAssetsName,jdbcType=VARCHAR}, #{snapshotSnNo,jdbcType=VARCHAR}, #{snapshotAssetsCategory,jdbcType=VARCHAR}, 
      #{snapshotAssetsStatus,jdbcType=TINYINT}, #{snapshotAssetsConditions,jdbcType=TINYINT}, 
      #{snapshotAssetsHolder,jdbcType=VARCHAR}, #{snapshotHolderTime,jdbcType=TIMESTAMP}, 
      #{snapshotHolderAddress,jdbcType=VARCHAR}, #{snapshotWarehouseCode,jdbcType=VARCHAR}, 
      #{snapshotModel,jdbcType=VARCHAR}, #{snapshotBrand,jdbcType=VARCHAR}, #{snapshotAssetsCpu,jdbcType=VARCHAR}, 
      #{snapshotHardDisk,jdbcType=VARCHAR}, #{snapshotRamMemory,jdbcType=VARCHAR}, #{realAssetsCpu,jdbcType=VARCHAR}, 
      #{realHardDisk,jdbcType=VARCHAR}, #{realRamMemory,jdbcType=VARCHAR}, #{realAssetsCode,jdbcType=VARCHAR}, 
      #{realAssetsName,jdbcType=VARCHAR}, #{realAssetsStatus,jdbcType=TINYINT}, #{realAssetsConditions,jdbcType=TINYINT}, 
      #{realAssetsSnno,jdbcType=VARCHAR}, #{realAssetsModel,jdbcType=VARCHAR}, #{realAssetsBrand,jdbcType=VARCHAR}, 
      #{realAssetsHolder,jdbcType=VARCHAR}, #{realHolderTime,jdbcType=TIMESTAMP}, #{realHolderAddress,jdbcType=VARCHAR}, 
      #{realWarehouseCode,jdbcType=VARCHAR}, #{realPicturesUrl,jdbcType=VARCHAR}, #{needDept,jdbcType=VARCHAR}, 
      #{costDept,jdbcType=VARCHAR}, #{holderDept,jdbcType=VARCHAR}, #{snapshotNumber,jdbcType=INTEGER}, 
      #{realNumber,jdbcType=INTEGER}, #{difference,jdbcType=TINYINT}, #{ownFlag,jdbcType=TINYINT}, 
      #{errMessage,jdbcType=VARCHAR}, #{errDesc,jdbcType=VARCHAR}, #{newInsertFlag,jdbcType=TINYINT}, 
      #{checkFlag,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{copyAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_check_task_detail_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskDetailId != null">
        task_detail_id,
      </if>
      <if test="checkTaskId != null">
        check_task_id,
      </if>
      <if test="takingPlanNo != null">
        taking_plan_no,
      </if>
      <if test="checkPeople != null">
        check_people,
      </if>
      <if test="busLargeRegion != null">
        bus_large_region,
      </if>
      <if test="busCity != null">
        bus_city,
      </if>
      <if test="assetsKeeper != null">
        assets_keeper,
      </if>
      <if test="holderAddressProvince != null">
        holder_address_province,
      </if>
      <if test="holderAddressCity != null">
        holder_address_city,
      </if>
      <if test="holderAddressCounty != null">
        holder_address_county,
      </if>
      <if test="snapshotAssetsCode != null">
        snapshot_assets_code,
      </if>
      <if test="snapshotAssetsName != null">
        snapshot_assets_name,
      </if>
      <if test="snapshotSnNo != null">
        snapshot_sn_no,
      </if>
      <if test="snapshotAssetsCategory != null">
        snapshot_assets_category,
      </if>
      <if test="snapshotAssetsStatus != null">
        snapshot_assets_status,
      </if>
      <if test="snapshotAssetsConditions != null">
        snapshot_assets_conditions,
      </if>
      <if test="snapshotAssetsHolder != null">
        snapshot_assets_holder,
      </if>
      <if test="snapshotHolderTime != null">
        snapshot_holder_time,
      </if>
      <if test="snapshotHolderAddress != null">
        snapshot_holder_address,
      </if>
      <if test="snapshotWarehouseCode != null">
        snapshot_warehouse_code,
      </if>
      <if test="snapshotModel != null">
        snapshot_model,
      </if>
      <if test="snapshotBrand != null">
        snapshot_brand,
      </if>
      <if test="snapshotAssetsCpu != null">
        snapshot_assets_cpu,
      </if>
      <if test="snapshotHardDisk != null">
        snapshot_hard_disk,
      </if>
      <if test="snapshotRamMemory != null">
        snapshot_ram_memory,
      </if>
      <if test="realAssetsCpu != null">
        real_assets_cpu,
      </if>
      <if test="realHardDisk != null">
        real_hard_disk,
      </if>
      <if test="realRamMemory != null">
        real_ram_memory,
      </if>
      <if test="realAssetsCode != null">
        real_assets_code,
      </if>
      <if test="realAssetsName != null">
        real_assets_name,
      </if>
      <if test="realAssetsStatus != null">
        real_assets_status,
      </if>
      <if test="realAssetsConditions != null">
        real_assets_conditions,
      </if>
      <if test="realAssetsSnno != null">
        real_assets_snno,
      </if>
      <if test="realAssetsModel != null">
        real_assets_model,
      </if>
      <if test="realAssetsBrand != null">
        real_assets_brand,
      </if>
      <if test="realAssetsHolder != null">
        real_assets_holder,
      </if>
      <if test="realHolderTime != null">
        real_holder_time,
      </if>
      <if test="realHolderAddress != null">
        real_holder_address,
      </if>
      <if test="realWarehouseCode != null">
        real_warehouse_code,
      </if>
      <if test="realPicturesUrl != null">
        real_pictures_url,
      </if>
      <if test="needDept != null">
        need_dept,
      </if>
      <if test="costDept != null">
        cost_dept,
      </if>
      <if test="holderDept != null">
        holder_dept,
      </if>
      <if test="snapshotNumber != null">
        snapshot_number,
      </if>
      <if test="realNumber != null">
        real_number,
      </if>
      <if test="difference != null">
        difference,
      </if>
      <if test="ownFlag != null">
        own_flag,
      </if>
      <if test="errMessage != null">
        err_message,
      </if>
      <if test="errDesc != null">
        err_desc,
      </if>
      <if test="newInsertFlag != null">
        new_insert_flag,
      </if>
      <if test="checkFlag != null">
        check_flag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="copyAt != null">
        copy_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskDetailId != null">
        #{taskDetailId,jdbcType=BIGINT},
      </if>
      <if test="checkTaskId != null">
        #{checkTaskId,jdbcType=BIGINT},
      </if>
      <if test="takingPlanNo != null">
        #{takingPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="checkPeople != null">
        #{checkPeople,jdbcType=VARCHAR},
      </if>
      <if test="busLargeRegion != null">
        #{busLargeRegion,jdbcType=VARCHAR},
      </if>
      <if test="busCity != null">
        #{busCity,jdbcType=VARCHAR},
      </if>
      <if test="assetsKeeper != null">
        #{assetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressProvince != null">
        #{holderAddressProvince,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCity != null">
        #{holderAddressCity,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCounty != null">
        #{holderAddressCounty,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsCode != null">
        #{snapshotAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsName != null">
        #{snapshotAssetsName,jdbcType=VARCHAR},
      </if>
      <if test="snapshotSnNo != null">
        #{snapshotSnNo,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsCategory != null">
        #{snapshotAssetsCategory,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsStatus != null">
        #{snapshotAssetsStatus,jdbcType=TINYINT},
      </if>
      <if test="snapshotAssetsConditions != null">
        #{snapshotAssetsConditions,jdbcType=TINYINT},
      </if>
      <if test="snapshotAssetsHolder != null">
        #{snapshotAssetsHolder,jdbcType=VARCHAR},
      </if>
      <if test="snapshotHolderTime != null">
        #{snapshotHolderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="snapshotHolderAddress != null">
        #{snapshotHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="snapshotWarehouseCode != null">
        #{snapshotWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="snapshotModel != null">
        #{snapshotModel,jdbcType=VARCHAR},
      </if>
      <if test="snapshotBrand != null">
        #{snapshotBrand,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsCpu != null">
        #{snapshotAssetsCpu,jdbcType=VARCHAR},
      </if>
      <if test="snapshotHardDisk != null">
        #{snapshotHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="snapshotRamMemory != null">
        #{snapshotRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsCpu != null">
        #{realAssetsCpu,jdbcType=VARCHAR},
      </if>
      <if test="realHardDisk != null">
        #{realHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="realRamMemory != null">
        #{realRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsCode != null">
        #{realAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsName != null">
        #{realAssetsName,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsStatus != null">
        #{realAssetsStatus,jdbcType=TINYINT},
      </if>
      <if test="realAssetsConditions != null">
        #{realAssetsConditions,jdbcType=TINYINT},
      </if>
      <if test="realAssetsSnno != null">
        #{realAssetsSnno,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsModel != null">
        #{realAssetsModel,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsBrand != null">
        #{realAssetsBrand,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsHolder != null">
        #{realAssetsHolder,jdbcType=VARCHAR},
      </if>
      <if test="realHolderTime != null">
        #{realHolderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realHolderAddress != null">
        #{realHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="realWarehouseCode != null">
        #{realWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="realPicturesUrl != null">
        #{realPicturesUrl,jdbcType=VARCHAR},
      </if>
      <if test="needDept != null">
        #{needDept,jdbcType=VARCHAR},
      </if>
      <if test="costDept != null">
        #{costDept,jdbcType=VARCHAR},
      </if>
      <if test="holderDept != null">
        #{holderDept,jdbcType=VARCHAR},
      </if>
      <if test="snapshotNumber != null">
        #{snapshotNumber,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="difference != null">
        #{difference,jdbcType=TINYINT},
      </if>
      <if test="ownFlag != null">
        #{ownFlag,jdbcType=TINYINT},
      </if>
      <if test="errMessage != null">
        #{errMessage,jdbcType=VARCHAR},
      </if>
      <if test="errDesc != null">
        #{errDesc,jdbcType=VARCHAR},
      </if>
      <if test="newInsertFlag != null">
        #{newInsertFlag,jdbcType=TINYINT},
      </if>
      <if test="checkFlag != null">
        #{checkFlag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="copyAt != null">
        #{copyAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistoryExample" resultType="java.lang.Long">
    select count(*) from stock_assets_check_task_detail_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_check_task_detail_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskDetailId != null">
        task_detail_id = #{record.taskDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.checkTaskId != null">
        check_task_id = #{record.checkTaskId,jdbcType=BIGINT},
      </if>
      <if test="record.takingPlanNo != null">
        taking_plan_no = #{record.takingPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.checkPeople != null">
        check_people = #{record.checkPeople,jdbcType=VARCHAR},
      </if>
      <if test="record.busLargeRegion != null">
        bus_large_region = #{record.busLargeRegion,jdbcType=VARCHAR},
      </if>
      <if test="record.busCity != null">
        bus_city = #{record.busCity,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsKeeper != null">
        assets_keeper = #{record.assetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddressProvince != null">
        holder_address_province = #{record.holderAddressProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddressCity != null">
        holder_address_city = #{record.holderAddressCity,jdbcType=VARCHAR},
      </if>
      <if test="record.holderAddressCounty != null">
        holder_address_county = #{record.holderAddressCounty,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotAssetsCode != null">
        snapshot_assets_code = #{record.snapshotAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotAssetsName != null">
        snapshot_assets_name = #{record.snapshotAssetsName,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotSnNo != null">
        snapshot_sn_no = #{record.snapshotSnNo,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotAssetsCategory != null">
        snapshot_assets_category = #{record.snapshotAssetsCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotAssetsStatus != null">
        snapshot_assets_status = #{record.snapshotAssetsStatus,jdbcType=TINYINT},
      </if>
      <if test="record.snapshotAssetsConditions != null">
        snapshot_assets_conditions = #{record.snapshotAssetsConditions,jdbcType=TINYINT},
      </if>
      <if test="record.snapshotAssetsHolder != null">
        snapshot_assets_holder = #{record.snapshotAssetsHolder,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotHolderTime != null">
        snapshot_holder_time = #{record.snapshotHolderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.snapshotHolderAddress != null">
        snapshot_holder_address = #{record.snapshotHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotWarehouseCode != null">
        snapshot_warehouse_code = #{record.snapshotWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotModel != null">
        snapshot_model = #{record.snapshotModel,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotBrand != null">
        snapshot_brand = #{record.snapshotBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotAssetsCpu != null">
        snapshot_assets_cpu = #{record.snapshotAssetsCpu,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotHardDisk != null">
        snapshot_hard_disk = #{record.snapshotHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotRamMemory != null">
        snapshot_ram_memory = #{record.snapshotRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsCpu != null">
        real_assets_cpu = #{record.realAssetsCpu,jdbcType=VARCHAR},
      </if>
      <if test="record.realHardDisk != null">
        real_hard_disk = #{record.realHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="record.realRamMemory != null">
        real_ram_memory = #{record.realRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsCode != null">
        real_assets_code = #{record.realAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsName != null">
        real_assets_name = #{record.realAssetsName,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsStatus != null">
        real_assets_status = #{record.realAssetsStatus,jdbcType=TINYINT},
      </if>
      <if test="record.realAssetsConditions != null">
        real_assets_conditions = #{record.realAssetsConditions,jdbcType=TINYINT},
      </if>
      <if test="record.realAssetsSnno != null">
        real_assets_snno = #{record.realAssetsSnno,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsModel != null">
        real_assets_model = #{record.realAssetsModel,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsBrand != null">
        real_assets_brand = #{record.realAssetsBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.realAssetsHolder != null">
        real_assets_holder = #{record.realAssetsHolder,jdbcType=VARCHAR},
      </if>
      <if test="record.realHolderTime != null">
        real_holder_time = #{record.realHolderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realHolderAddress != null">
        real_holder_address = #{record.realHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.realWarehouseCode != null">
        real_warehouse_code = #{record.realWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.realPicturesUrl != null">
        real_pictures_url = #{record.realPicturesUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.needDept != null">
        need_dept = #{record.needDept,jdbcType=VARCHAR},
      </if>
      <if test="record.costDept != null">
        cost_dept = #{record.costDept,jdbcType=VARCHAR},
      </if>
      <if test="record.holderDept != null">
        holder_dept = #{record.holderDept,jdbcType=VARCHAR},
      </if>
      <if test="record.snapshotNumber != null">
        snapshot_number = #{record.snapshotNumber,jdbcType=INTEGER},
      </if>
      <if test="record.realNumber != null">
        real_number = #{record.realNumber,jdbcType=INTEGER},
      </if>
      <if test="record.difference != null">
        difference = #{record.difference,jdbcType=TINYINT},
      </if>
      <if test="record.ownFlag != null">
        own_flag = #{record.ownFlag,jdbcType=TINYINT},
      </if>
      <if test="record.errMessage != null">
        err_message = #{record.errMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.errDesc != null">
        err_desc = #{record.errDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.newInsertFlag != null">
        new_insert_flag = #{record.newInsertFlag,jdbcType=TINYINT},
      </if>
      <if test="record.checkFlag != null">
        check_flag = #{record.checkFlag,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.copyAt != null">
        copy_at = #{record.copyAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_check_task_detail_history
    set id = #{record.id,jdbcType=BIGINT},
      task_detail_id = #{record.taskDetailId,jdbcType=BIGINT},
      check_task_id = #{record.checkTaskId,jdbcType=BIGINT},
      taking_plan_no = #{record.takingPlanNo,jdbcType=VARCHAR},
      check_people = #{record.checkPeople,jdbcType=VARCHAR},
      bus_large_region = #{record.busLargeRegion,jdbcType=VARCHAR},
      bus_city = #{record.busCity,jdbcType=VARCHAR},
      assets_keeper = #{record.assetsKeeper,jdbcType=VARCHAR},
      holder_address_province = #{record.holderAddressProvince,jdbcType=VARCHAR},
      holder_address_city = #{record.holderAddressCity,jdbcType=VARCHAR},
      holder_address_county = #{record.holderAddressCounty,jdbcType=VARCHAR},
      snapshot_assets_code = #{record.snapshotAssetsCode,jdbcType=VARCHAR},
      snapshot_assets_name = #{record.snapshotAssetsName,jdbcType=VARCHAR},
      snapshot_sn_no = #{record.snapshotSnNo,jdbcType=VARCHAR},
      snapshot_assets_category = #{record.snapshotAssetsCategory,jdbcType=VARCHAR},
      snapshot_assets_status = #{record.snapshotAssetsStatus,jdbcType=TINYINT},
      snapshot_assets_conditions = #{record.snapshotAssetsConditions,jdbcType=TINYINT},
      snapshot_assets_holder = #{record.snapshotAssetsHolder,jdbcType=VARCHAR},
      snapshot_holder_time = #{record.snapshotHolderTime,jdbcType=TIMESTAMP},
      snapshot_holder_address = #{record.snapshotHolderAddress,jdbcType=VARCHAR},
      snapshot_warehouse_code = #{record.snapshotWarehouseCode,jdbcType=VARCHAR},
      snapshot_model = #{record.snapshotModel,jdbcType=VARCHAR},
      snapshot_brand = #{record.snapshotBrand,jdbcType=VARCHAR},
      snapshot_assets_cpu = #{record.snapshotAssetsCpu,jdbcType=VARCHAR},
      snapshot_hard_disk = #{record.snapshotHardDisk,jdbcType=VARCHAR},
      snapshot_ram_memory = #{record.snapshotRamMemory,jdbcType=VARCHAR},
      real_assets_cpu = #{record.realAssetsCpu,jdbcType=VARCHAR},
      real_hard_disk = #{record.realHardDisk,jdbcType=VARCHAR},
      real_ram_memory = #{record.realRamMemory,jdbcType=VARCHAR},
      real_assets_code = #{record.realAssetsCode,jdbcType=VARCHAR},
      real_assets_name = #{record.realAssetsName,jdbcType=VARCHAR},
      real_assets_status = #{record.realAssetsStatus,jdbcType=TINYINT},
      real_assets_conditions = #{record.realAssetsConditions,jdbcType=TINYINT},
      real_assets_snno = #{record.realAssetsSnno,jdbcType=VARCHAR},
      real_assets_model = #{record.realAssetsModel,jdbcType=VARCHAR},
      real_assets_brand = #{record.realAssetsBrand,jdbcType=VARCHAR},
      real_assets_holder = #{record.realAssetsHolder,jdbcType=VARCHAR},
      real_holder_time = #{record.realHolderTime,jdbcType=TIMESTAMP},
      real_holder_address = #{record.realHolderAddress,jdbcType=VARCHAR},
      real_warehouse_code = #{record.realWarehouseCode,jdbcType=VARCHAR},
      real_pictures_url = #{record.realPicturesUrl,jdbcType=VARCHAR},
      need_dept = #{record.needDept,jdbcType=VARCHAR},
      cost_dept = #{record.costDept,jdbcType=VARCHAR},
      holder_dept = #{record.holderDept,jdbcType=VARCHAR},
      snapshot_number = #{record.snapshotNumber,jdbcType=INTEGER},
      real_number = #{record.realNumber,jdbcType=INTEGER},
      difference = #{record.difference,jdbcType=TINYINT},
      own_flag = #{record.ownFlag,jdbcType=TINYINT},
      err_message = #{record.errMessage,jdbcType=VARCHAR},
      err_desc = #{record.errDesc,jdbcType=VARCHAR},
      new_insert_flag = #{record.newInsertFlag,jdbcType=TINYINT},
      check_flag = #{record.checkFlag,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      copy_at = #{record.copyAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistory">
    update stock_assets_check_task_detail_history
    <set>
      <if test="taskDetailId != null">
        task_detail_id = #{taskDetailId,jdbcType=BIGINT},
      </if>
      <if test="checkTaskId != null">
        check_task_id = #{checkTaskId,jdbcType=BIGINT},
      </if>
      <if test="takingPlanNo != null">
        taking_plan_no = #{takingPlanNo,jdbcType=VARCHAR},
      </if>
      <if test="checkPeople != null">
        check_people = #{checkPeople,jdbcType=VARCHAR},
      </if>
      <if test="busLargeRegion != null">
        bus_large_region = #{busLargeRegion,jdbcType=VARCHAR},
      </if>
      <if test="busCity != null">
        bus_city = #{busCity,jdbcType=VARCHAR},
      </if>
      <if test="assetsKeeper != null">
        assets_keeper = #{assetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressProvince != null">
        holder_address_province = #{holderAddressProvince,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCity != null">
        holder_address_city = #{holderAddressCity,jdbcType=VARCHAR},
      </if>
      <if test="holderAddressCounty != null">
        holder_address_county = #{holderAddressCounty,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsCode != null">
        snapshot_assets_code = #{snapshotAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsName != null">
        snapshot_assets_name = #{snapshotAssetsName,jdbcType=VARCHAR},
      </if>
      <if test="snapshotSnNo != null">
        snapshot_sn_no = #{snapshotSnNo,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsCategory != null">
        snapshot_assets_category = #{snapshotAssetsCategory,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsStatus != null">
        snapshot_assets_status = #{snapshotAssetsStatus,jdbcType=TINYINT},
      </if>
      <if test="snapshotAssetsConditions != null">
        snapshot_assets_conditions = #{snapshotAssetsConditions,jdbcType=TINYINT},
      </if>
      <if test="snapshotAssetsHolder != null">
        snapshot_assets_holder = #{snapshotAssetsHolder,jdbcType=VARCHAR},
      </if>
      <if test="snapshotHolderTime != null">
        snapshot_holder_time = #{snapshotHolderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="snapshotHolderAddress != null">
        snapshot_holder_address = #{snapshotHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="snapshotWarehouseCode != null">
        snapshot_warehouse_code = #{snapshotWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="snapshotModel != null">
        snapshot_model = #{snapshotModel,jdbcType=VARCHAR},
      </if>
      <if test="snapshotBrand != null">
        snapshot_brand = #{snapshotBrand,jdbcType=VARCHAR},
      </if>
      <if test="snapshotAssetsCpu != null">
        snapshot_assets_cpu = #{snapshotAssetsCpu,jdbcType=VARCHAR},
      </if>
      <if test="snapshotHardDisk != null">
        snapshot_hard_disk = #{snapshotHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="snapshotRamMemory != null">
        snapshot_ram_memory = #{snapshotRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsCpu != null">
        real_assets_cpu = #{realAssetsCpu,jdbcType=VARCHAR},
      </if>
      <if test="realHardDisk != null">
        real_hard_disk = #{realHardDisk,jdbcType=VARCHAR},
      </if>
      <if test="realRamMemory != null">
        real_ram_memory = #{realRamMemory,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsCode != null">
        real_assets_code = #{realAssetsCode,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsName != null">
        real_assets_name = #{realAssetsName,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsStatus != null">
        real_assets_status = #{realAssetsStatus,jdbcType=TINYINT},
      </if>
      <if test="realAssetsConditions != null">
        real_assets_conditions = #{realAssetsConditions,jdbcType=TINYINT},
      </if>
      <if test="realAssetsSnno != null">
        real_assets_snno = #{realAssetsSnno,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsModel != null">
        real_assets_model = #{realAssetsModel,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsBrand != null">
        real_assets_brand = #{realAssetsBrand,jdbcType=VARCHAR},
      </if>
      <if test="realAssetsHolder != null">
        real_assets_holder = #{realAssetsHolder,jdbcType=VARCHAR},
      </if>
      <if test="realHolderTime != null">
        real_holder_time = #{realHolderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realHolderAddress != null">
        real_holder_address = #{realHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="realWarehouseCode != null">
        real_warehouse_code = #{realWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="realPicturesUrl != null">
        real_pictures_url = #{realPicturesUrl,jdbcType=VARCHAR},
      </if>
      <if test="needDept != null">
        need_dept = #{needDept,jdbcType=VARCHAR},
      </if>
      <if test="costDept != null">
        cost_dept = #{costDept,jdbcType=VARCHAR},
      </if>
      <if test="holderDept != null">
        holder_dept = #{holderDept,jdbcType=VARCHAR},
      </if>
      <if test="snapshotNumber != null">
        snapshot_number = #{snapshotNumber,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        real_number = #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="difference != null">
        difference = #{difference,jdbcType=TINYINT},
      </if>
      <if test="ownFlag != null">
        own_flag = #{ownFlag,jdbcType=TINYINT},
      </if>
      <if test="errMessage != null">
        err_message = #{errMessage,jdbcType=VARCHAR},
      </if>
      <if test="errDesc != null">
        err_desc = #{errDesc,jdbcType=VARCHAR},
      </if>
      <if test="newInsertFlag != null">
        new_insert_flag = #{newInsertFlag,jdbcType=TINYINT},
      </if>
      <if test="checkFlag != null">
        check_flag = #{checkFlag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="copyAt != null">
        copy_at = #{copyAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckTaskDetailHistory">
    update stock_assets_check_task_detail_history
    set task_detail_id = #{taskDetailId,jdbcType=BIGINT},
      check_task_id = #{checkTaskId,jdbcType=BIGINT},
      taking_plan_no = #{takingPlanNo,jdbcType=VARCHAR},
      check_people = #{checkPeople,jdbcType=VARCHAR},
      bus_large_region = #{busLargeRegion,jdbcType=VARCHAR},
      bus_city = #{busCity,jdbcType=VARCHAR},
      assets_keeper = #{assetsKeeper,jdbcType=VARCHAR},
      holder_address_province = #{holderAddressProvince,jdbcType=VARCHAR},
      holder_address_city = #{holderAddressCity,jdbcType=VARCHAR},
      holder_address_county = #{holderAddressCounty,jdbcType=VARCHAR},
      snapshot_assets_code = #{snapshotAssetsCode,jdbcType=VARCHAR},
      snapshot_assets_name = #{snapshotAssetsName,jdbcType=VARCHAR},
      snapshot_sn_no = #{snapshotSnNo,jdbcType=VARCHAR},
      snapshot_assets_category = #{snapshotAssetsCategory,jdbcType=VARCHAR},
      snapshot_assets_status = #{snapshotAssetsStatus,jdbcType=TINYINT},
      snapshot_assets_conditions = #{snapshotAssetsConditions,jdbcType=TINYINT},
      snapshot_assets_holder = #{snapshotAssetsHolder,jdbcType=VARCHAR},
      snapshot_holder_time = #{snapshotHolderTime,jdbcType=TIMESTAMP},
      snapshot_holder_address = #{snapshotHolderAddress,jdbcType=VARCHAR},
      snapshot_warehouse_code = #{snapshotWarehouseCode,jdbcType=VARCHAR},
      snapshot_model = #{snapshotModel,jdbcType=VARCHAR},
      snapshot_brand = #{snapshotBrand,jdbcType=VARCHAR},
      snapshot_assets_cpu = #{snapshotAssetsCpu,jdbcType=VARCHAR},
      snapshot_hard_disk = #{snapshotHardDisk,jdbcType=VARCHAR},
      snapshot_ram_memory = #{snapshotRamMemory,jdbcType=VARCHAR},
      real_assets_cpu = #{realAssetsCpu,jdbcType=VARCHAR},
      real_hard_disk = #{realHardDisk,jdbcType=VARCHAR},
      real_ram_memory = #{realRamMemory,jdbcType=VARCHAR},
      real_assets_code = #{realAssetsCode,jdbcType=VARCHAR},
      real_assets_name = #{realAssetsName,jdbcType=VARCHAR},
      real_assets_status = #{realAssetsStatus,jdbcType=TINYINT},
      real_assets_conditions = #{realAssetsConditions,jdbcType=TINYINT},
      real_assets_snno = #{realAssetsSnno,jdbcType=VARCHAR},
      real_assets_model = #{realAssetsModel,jdbcType=VARCHAR},
      real_assets_brand = #{realAssetsBrand,jdbcType=VARCHAR},
      real_assets_holder = #{realAssetsHolder,jdbcType=VARCHAR},
      real_holder_time = #{realHolderTime,jdbcType=TIMESTAMP},
      real_holder_address = #{realHolderAddress,jdbcType=VARCHAR},
      real_warehouse_code = #{realWarehouseCode,jdbcType=VARCHAR},
      real_pictures_url = #{realPicturesUrl,jdbcType=VARCHAR},
      need_dept = #{needDept,jdbcType=VARCHAR},
      cost_dept = #{costDept,jdbcType=VARCHAR},
      holder_dept = #{holderDept,jdbcType=VARCHAR},
      snapshot_number = #{snapshotNumber,jdbcType=INTEGER},
      real_number = #{realNumber,jdbcType=INTEGER},
      difference = #{difference,jdbcType=TINYINT},
      own_flag = #{ownFlag,jdbcType=TINYINT},
      err_message = #{errMessage,jdbcType=VARCHAR},
      err_desc = #{errDesc,jdbcType=VARCHAR},
      new_insert_flag = #{newInsertFlag,jdbcType=TINYINT},
      check_flag = #{checkFlag,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      copy_at = #{copyAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>