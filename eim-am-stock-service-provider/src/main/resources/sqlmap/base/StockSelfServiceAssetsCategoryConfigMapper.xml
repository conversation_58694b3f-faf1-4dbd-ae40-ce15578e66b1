<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockSelfServiceAssetsCategoryConfigMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="matching_filed" jdbcType="VARCHAR" property="matchingFiled" />
    <result column="matching_filed_desr" jdbcType="VARCHAR" property="matchingFiledDesr" />
    <result column="matching_relation" jdbcType="VARCHAR" property="matchingRelation" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="job_type" jdbcType="VARCHAR" property="jobType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, rule_type, matching_filed, matching_filed_desr, matching_relation, code, priority, 
    job_type, status, created_by, created_at, updated_by, updated_at, del_flag
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_self_service_assets_category_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_self_service_assets_category_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_self_service_assets_category_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfig">
    insert into stock_self_service_assets_category_config (id, rule_type, matching_filed, 
      matching_filed_desr, matching_relation, code, 
      priority, job_type, status, 
      created_by, created_at, updated_by, 
      updated_at, del_flag)
    values (#{id,jdbcType=BIGINT}, #{ruleType,jdbcType=VARCHAR}, #{matchingFiled,jdbcType=VARCHAR}, 
      #{matchingFiledDesr,jdbcType=VARCHAR}, #{matchingRelation,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{priority,jdbcType=INTEGER}, #{jobType,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfig">
    insert into stock_self_service_assets_category_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="matchingFiled != null">
        matching_filed,
      </if>
      <if test="matchingFiledDesr != null">
        matching_filed_desr,
      </if>
      <if test="matchingRelation != null">
        matching_relation,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="jobType != null">
        job_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="matchingFiled != null">
        #{matchingFiled,jdbcType=VARCHAR},
      </if>
      <if test="matchingFiledDesr != null">
        #{matchingFiledDesr,jdbcType=VARCHAR},
      </if>
      <if test="matchingRelation != null">
        #{matchingRelation,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="jobType != null">
        #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfigExample" resultType="java.lang.Long">
    select count(*) from stock_self_service_assets_category_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_self_service_assets_category_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=VARCHAR},
      </if>
      <if test="record.matchingFiled != null">
        matching_filed = #{record.matchingFiled,jdbcType=VARCHAR},
      </if>
      <if test="record.matchingFiledDesr != null">
        matching_filed_desr = #{record.matchingFiledDesr,jdbcType=VARCHAR},
      </if>
      <if test="record.matchingRelation != null">
        matching_relation = #{record.matchingRelation,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.priority != null">
        priority = #{record.priority,jdbcType=INTEGER},
      </if>
      <if test="record.jobType != null">
        job_type = #{record.jobType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_self_service_assets_category_config
    set id = #{record.id,jdbcType=BIGINT},
      rule_type = #{record.ruleType,jdbcType=VARCHAR},
      matching_filed = #{record.matchingFiled,jdbcType=VARCHAR},
      matching_filed_desr = #{record.matchingFiledDesr,jdbcType=VARCHAR},
      matching_relation = #{record.matchingRelation,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      priority = #{record.priority,jdbcType=INTEGER},
      job_type = #{record.jobType,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      del_flag = #{record.delFlag,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfig">
    update stock_self_service_assets_category_config
    <set>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=VARCHAR},
      </if>
      <if test="matchingFiled != null">
        matching_filed = #{matchingFiled,jdbcType=VARCHAR},
      </if>
      <if test="matchingFiledDesr != null">
        matching_filed_desr = #{matchingFiledDesr,jdbcType=VARCHAR},
      </if>
      <if test="matchingRelation != null">
        matching_relation = #{matchingRelation,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="jobType != null">
        job_type = #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockSelfServiceAssetsCategoryConfig">
    update stock_self_service_assets_category_config
    set rule_type = #{ruleType,jdbcType=VARCHAR},
      matching_filed = #{matchingFiled,jdbcType=VARCHAR},
      matching_filed_desr = #{matchingFiledDesr,jdbcType=VARCHAR},
      matching_relation = #{matchingRelation,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      priority = #{priority,jdbcType=INTEGER},
      job_type = #{jobType,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>