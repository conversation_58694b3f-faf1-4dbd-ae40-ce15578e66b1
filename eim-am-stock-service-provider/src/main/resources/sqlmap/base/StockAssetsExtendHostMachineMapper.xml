<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsExtendHostMachineMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachine">
    <id column="assets_host_id" jdbcType="BIGINT" property="assetsHostId" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="processor" jdbcType="VARCHAR" property="processor" />
    <result column="ram_memory" jdbcType="VARCHAR" property="ramMemory" />
    <result column="hard_disk_capacity" jdbcType="VARCHAR" property="hardDiskCapacity" />
    <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    assets_host_id, assets_code, brand, model, processor, ram_memory, hard_disk_capacity, 
    data_status, del_flag, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_extend_host_machine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_extend_host_machine
    where assets_host_id = #{assetsHostId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_extend_host_machine
    where assets_host_id = #{assetsHostId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachine">
    insert into stock_assets_extend_host_machine (assets_host_id, assets_code, brand, 
      model, processor, ram_memory, 
      hard_disk_capacity, data_status, del_flag, 
      created_by, created_at, updated_by, 
      updated_at)
    values (#{assetsHostId,jdbcType=BIGINT}, #{assetsCode,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{processor,jdbcType=VARCHAR}, #{ramMemory,jdbcType=VARCHAR}, 
      #{hardDiskCapacity,jdbcType=VARCHAR}, #{dataStatus,jdbcType=TINYINT}, #{delFlag,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachine">
    insert into stock_assets_extend_host_machine
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetsHostId != null">
        assets_host_id,
      </if>
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="processor != null">
        processor,
      </if>
      <if test="ramMemory != null">
        ram_memory,
      </if>
      <if test="hardDiskCapacity != null">
        hard_disk_capacity,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetsHostId != null">
        #{assetsHostId,jdbcType=BIGINT},
      </if>
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="processor != null">
        #{processor,jdbcType=VARCHAR},
      </if>
      <if test="ramMemory != null">
        #{ramMemory,jdbcType=VARCHAR},
      </if>
      <if test="hardDiskCapacity != null">
        #{hardDiskCapacity,jdbcType=VARCHAR},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachineExample" resultType="java.lang.Long">
    select count(*) from stock_assets_extend_host_machine
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_extend_host_machine
    <set>
      <if test="record.assetsHostId != null">
        assets_host_id = #{record.assetsHostId,jdbcType=BIGINT},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.processor != null">
        processor = #{record.processor,jdbcType=VARCHAR},
      </if>
      <if test="record.ramMemory != null">
        ram_memory = #{record.ramMemory,jdbcType=VARCHAR},
      </if>
      <if test="record.hardDiskCapacity != null">
        hard_disk_capacity = #{record.hardDiskCapacity,jdbcType=VARCHAR},
      </if>
      <if test="record.dataStatus != null">
        data_status = #{record.dataStatus,jdbcType=TINYINT},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_extend_host_machine
    set assets_host_id = #{record.assetsHostId,jdbcType=BIGINT},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      processor = #{record.processor,jdbcType=VARCHAR},
      ram_memory = #{record.ramMemory,jdbcType=VARCHAR},
      hard_disk_capacity = #{record.hardDiskCapacity,jdbcType=VARCHAR},
      data_status = #{record.dataStatus,jdbcType=TINYINT},
      del_flag = #{record.delFlag,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachine">
    update stock_assets_extend_host_machine
    <set>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="processor != null">
        processor = #{processor,jdbcType=VARCHAR},
      </if>
      <if test="ramMemory != null">
        ram_memory = #{ramMemory,jdbcType=VARCHAR},
      </if>
      <if test="hardDiskCapacity != null">
        hard_disk_capacity = #{hardDiskCapacity,jdbcType=VARCHAR},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where assets_host_id = #{assetsHostId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachine">
    update stock_assets_extend_host_machine
    set assets_code = #{assetsCode,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      processor = #{processor,jdbcType=VARCHAR},
      ram_memory = #{ramMemory,jdbcType=VARCHAR},
      hard_disk_capacity = #{hardDiskCapacity,jdbcType=VARCHAR},
      data_status = #{dataStatus,jdbcType=TINYINT},
      del_flag = #{delFlag,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where assets_host_id = #{assetsHostId,jdbcType=BIGINT}
  </update>
</mapper>