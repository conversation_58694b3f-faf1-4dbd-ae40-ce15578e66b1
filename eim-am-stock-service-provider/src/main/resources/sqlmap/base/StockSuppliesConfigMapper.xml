<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockSuppliesConfigMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockSuppliesConfig">
    <id column="supplies_config_id" jdbcType="BIGINT" property="suppliesConfigId" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="max_number" jdbcType="BIGINT" property="maxNumber" />
    <result column="all_number" jdbcType="BIGINT" property="allNumber" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="is_default" jdbcType="TINYINT" property="isDefault" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    supplies_config_id, warehouse_code, supplies_code, max_number, all_number, version, 
    is_default, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_supplies_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_supplies_config
    where supplies_config_id = #{suppliesConfigId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_supplies_config
    where supplies_config_id = #{suppliesConfigId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfig">
    insert into stock_supplies_config (supplies_config_id, warehouse_code, supplies_code, 
      max_number, all_number, version, 
      is_default, created_by, created_at, 
      updated_by, updated_at)
    values (#{suppliesConfigId,jdbcType=BIGINT}, #{warehouseCode,jdbcType=VARCHAR}, #{suppliesCode,jdbcType=VARCHAR}, 
      #{maxNumber,jdbcType=BIGINT}, #{allNumber,jdbcType=BIGINT}, #{version,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfig">
    insert into stock_supplies_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="suppliesConfigId != null">
        supplies_config_id,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="maxNumber != null">
        max_number,
      </if>
      <if test="allNumber != null">
        all_number,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="suppliesConfigId != null">
        #{suppliesConfigId,jdbcType=BIGINT},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="maxNumber != null">
        #{maxNumber,jdbcType=BIGINT},
      </if>
      <if test="allNumber != null">
        #{allNumber,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfigExample" resultType="java.lang.Long">
    select count(*) from stock_supplies_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_supplies_config
    <set>
      <if test="record.suppliesConfigId != null">
        supplies_config_id = #{record.suppliesConfigId,jdbcType=BIGINT},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.maxNumber != null">
        max_number = #{record.maxNumber,jdbcType=BIGINT},
      </if>
      <if test="record.allNumber != null">
        all_number = #{record.allNumber,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.isDefault != null">
        is_default = #{record.isDefault,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_supplies_config
    set supplies_config_id = #{record.suppliesConfigId,jdbcType=BIGINT},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      max_number = #{record.maxNumber,jdbcType=BIGINT},
      all_number = #{record.allNumber,jdbcType=BIGINT},
      version = #{record.version,jdbcType=VARCHAR},
      is_default = #{record.isDefault,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfig">
    update stock_supplies_config
    <set>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="maxNumber != null">
        max_number = #{maxNumber,jdbcType=BIGINT},
      </if>
      <if test="allNumber != null">
        all_number = #{allNumber,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where supplies_config_id = #{suppliesConfigId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfig">
    update stock_supplies_config
    set warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      max_number = #{maxNumber,jdbcType=BIGINT},
      all_number = #{allNumber,jdbcType=BIGINT},
      version = #{version,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where supplies_config_id = #{suppliesConfigId,jdbcType=BIGINT}
  </update>
</mapper>