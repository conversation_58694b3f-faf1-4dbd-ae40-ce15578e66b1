<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockInventoryInPlanLineMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInPlanLine">
    <id column="inventory_in_plan_line_id" jdbcType="BIGINT" property="inventoryInPlanLineId" />
    <result column="inventory_in_plan_head_id" jdbcType="BIGINT" property="inventoryInPlanHeadId" />
    <result column="sim_company_code" jdbcType="VARCHAR" property="simCompanyCode" />
    <result column="serve_company_code" jdbcType="VARCHAR" property="serveCompanyCode" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="real_number" jdbcType="INTEGER" property="realNumber" />
    <result column="reject_number" jdbcType="INTEGER" property="rejectNumber" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="plan_in_time" jdbcType="TIMESTAMP" property="planInTime" />
    <result column="Purchase_order_line_id" jdbcType="BIGINT" property="purchaseOrderLineId" />
    <result column="Receive_item_no" jdbcType="VARCHAR" property="receiveItemNo" />
    <result column="Unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="unit_tax_rate" jdbcType="DECIMAL" property="unitTaxRate" />
    <result column="sim_unit_price" jdbcType="DECIMAL" property="simUnitPrice" />
    <result column="sim_tax_rate" jdbcType="DECIMAL" property="simTaxRate" />
    <result column="serve_unit_price" jdbcType="DECIMAL" property="serveUnitPrice" />
    <result column="serve_tax_rate" jdbcType="DECIMAL" property="serveTaxRate" />
    <result column="Price_excluding_tax" jdbcType="DECIMAL" property="priceExcludingTax" />
    <result column="Total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="Currency" jdbcType="VARCHAR" property="currency" />
    <result column="cancel_remark" jdbcType="VARCHAR" property="cancelRemark" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="sim_re_new_unit_price" jdbcType="DECIMAL" property="simReNewUnitPrice" />
    <result column="sim_re_new_tax_rate" jdbcType="DECIMAL" property="simReNewTaxRate" />
    <result column="serve_re_new_unit_price" jdbcType="DECIMAL" property="serveReNewUnitPrice" />
    <result column="serve_re_new_tax_rate" jdbcType="DECIMAL" property="serveReNewTaxRate" />
    <result column="sim_group_re_new_unit_price" jdbcType="DECIMAL" property="simGroupReNewUnitPrice" />
    <result column="sim_group_re_new_tax_rate" jdbcType="DECIMAL" property="simGroupReNewTaxRate" />
    <result column="serve_group_re_new_unit_price" jdbcType="DECIMAL" property="serveGroupReNewUnitPrice" />
    <result column="serve_group_re_new_tax_rate" jdbcType="DECIMAL" property="serveGroupReNewTaxRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    inventory_in_plan_line_id, inventory_in_plan_head_id, sim_company_code, serve_company_code, 
    supplies_code, number, real_number, reject_number, assets_code, status, plan_in_time, 
    Purchase_order_line_id, Receive_item_no, Unit_price, unit_tax_rate, sim_unit_price, 
    sim_tax_rate, serve_unit_price, serve_tax_rate, Price_excluding_tax, Total_price, 
    Currency, cancel_remark, CREATED_BY, CREATED_AT, UPDATED_BY, UPDATED_AT, version, 
    sim_re_new_unit_price, sim_re_new_tax_rate, serve_re_new_unit_price, serve_re_new_tax_rate, 
    sim_group_re_new_unit_price, sim_group_re_new_tax_rate, serve_group_re_new_unit_price, 
    serve_group_re_new_tax_rate
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_inventory_in_plan_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_inventory_in_plan_lines
    where inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_inventory_in_plan_lines
    where inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLine">
    <selectKey keyProperty="inventoryInPlanLineId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_inventory_in_plan_lines (inventory_in_plan_line_id, inventory_in_plan_head_id, 
      sim_company_code, serve_company_code, supplies_code, 
      number, real_number, reject_number, 
      assets_code, status, plan_in_time, 
      Purchase_order_line_id, Receive_item_no, Unit_price, 
      unit_tax_rate, sim_unit_price, sim_tax_rate, 
      serve_unit_price, serve_tax_rate, Price_excluding_tax, 
      Total_price, Currency, cancel_remark, 
      CREATED_BY, CREATED_AT, UPDATED_BY, 
      UPDATED_AT, version, sim_re_new_unit_price, 
      sim_re_new_tax_rate, serve_re_new_unit_price, serve_re_new_tax_rate, 
      sim_group_re_new_unit_price, sim_group_re_new_tax_rate, 
      serve_group_re_new_unit_price, serve_group_re_new_tax_rate
      )
    values (#{inventoryInPlanLineId,jdbcType=BIGINT}, #{inventoryInPlanHeadId,jdbcType=BIGINT}, 
      #{simCompanyCode,jdbcType=VARCHAR}, #{serveCompanyCode,jdbcType=VARCHAR}, #{suppliesCode,jdbcType=VARCHAR}, 
      #{number,jdbcType=INTEGER}, #{realNumber,jdbcType=INTEGER}, #{rejectNumber,jdbcType=INTEGER}, 
      #{assetsCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{planInTime,jdbcType=TIMESTAMP}, 
      #{purchaseOrderLineId,jdbcType=BIGINT}, #{receiveItemNo,jdbcType=VARCHAR}, #{unitPrice,jdbcType=DECIMAL}, 
      #{unitTaxRate,jdbcType=DECIMAL}, #{simUnitPrice,jdbcType=DECIMAL}, #{simTaxRate,jdbcType=DECIMAL}, 
      #{serveUnitPrice,jdbcType=DECIMAL}, #{serveTaxRate,jdbcType=DECIMAL}, #{priceExcludingTax,jdbcType=DECIMAL}, 
      #{totalPrice,jdbcType=DECIMAL}, #{currency,jdbcType=VARCHAR}, #{cancelRemark,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{version,jdbcType=VARCHAR}, #{simReNewUnitPrice,jdbcType=DECIMAL}, 
      #{simReNewTaxRate,jdbcType=DECIMAL}, #{serveReNewUnitPrice,jdbcType=DECIMAL}, #{serveReNewTaxRate,jdbcType=DECIMAL}, 
      #{simGroupReNewUnitPrice,jdbcType=DECIMAL}, #{simGroupReNewTaxRate,jdbcType=DECIMAL}, 
      #{serveGroupReNewUnitPrice,jdbcType=DECIMAL}, #{serveGroupReNewTaxRate,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLine">
    <selectKey keyProperty="inventoryInPlanLineId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_inventory_in_plan_lines
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="inventoryInPlanLineId != null">
        inventory_in_plan_line_id,
      </if>
      <if test="inventoryInPlanHeadId != null">
        inventory_in_plan_head_id,
      </if>
      <if test="simCompanyCode != null">
        sim_company_code,
      </if>
      <if test="serveCompanyCode != null">
        serve_company_code,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="realNumber != null">
        real_number,
      </if>
      <if test="rejectNumber != null">
        reject_number,
      </if>
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="planInTime != null">
        plan_in_time,
      </if>
      <if test="purchaseOrderLineId != null">
        Purchase_order_line_id,
      </if>
      <if test="receiveItemNo != null">
        Receive_item_no,
      </if>
      <if test="unitPrice != null">
        Unit_price,
      </if>
      <if test="unitTaxRate != null">
        unit_tax_rate,
      </if>
      <if test="simUnitPrice != null">
        sim_unit_price,
      </if>
      <if test="simTaxRate != null">
        sim_tax_rate,
      </if>
      <if test="serveUnitPrice != null">
        serve_unit_price,
      </if>
      <if test="serveTaxRate != null">
        serve_tax_rate,
      </if>
      <if test="priceExcludingTax != null">
        Price_excluding_tax,
      </if>
      <if test="totalPrice != null">
        Total_price,
      </if>
      <if test="currency != null">
        Currency,
      </if>
      <if test="cancelRemark != null">
        cancel_remark,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="createdAt != null">
        CREATED_AT,
      </if>
      <if test="updatedBy != null">
        UPDATED_BY,
      </if>
      <if test="updatedAt != null">
        UPDATED_AT,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="simReNewUnitPrice != null">
        sim_re_new_unit_price,
      </if>
      <if test="simReNewTaxRate != null">
        sim_re_new_tax_rate,
      </if>
      <if test="serveReNewUnitPrice != null">
        serve_re_new_unit_price,
      </if>
      <if test="serveReNewTaxRate != null">
        serve_re_new_tax_rate,
      </if>
      <if test="simGroupReNewUnitPrice != null">
        sim_group_re_new_unit_price,
      </if>
      <if test="simGroupReNewTaxRate != null">
        sim_group_re_new_tax_rate,
      </if>
      <if test="serveGroupReNewUnitPrice != null">
        serve_group_re_new_unit_price,
      </if>
      <if test="serveGroupReNewTaxRate != null">
        serve_group_re_new_tax_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="inventoryInPlanLineId != null">
        #{inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
      <if test="inventoryInPlanHeadId != null">
        #{inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="simCompanyCode != null">
        #{simCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="serveCompanyCode != null">
        #{serveCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="rejectNumber != null">
        #{rejectNumber,jdbcType=INTEGER},
      </if>
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="planInTime != null">
        #{planInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrderLineId != null">
        #{purchaseOrderLineId,jdbcType=BIGINT},
      </if>
      <if test="receiveItemNo != null">
        #{receiveItemNo,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="unitTaxRate != null">
        #{unitTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simUnitPrice != null">
        #{simUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simTaxRate != null">
        #{simTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveUnitPrice != null">
        #{serveUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveTaxRate != null">
        #{serveTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="priceExcludingTax != null">
        #{priceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="cancelRemark != null">
        #{cancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="simReNewUnitPrice != null">
        #{simReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simReNewTaxRate != null">
        #{simReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewUnitPrice != null">
        #{serveReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewTaxRate != null">
        #{serveReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewUnitPrice != null">
        #{simGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewTaxRate != null">
        #{simGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewUnitPrice != null">
        #{serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewTaxRate != null">
        #{serveGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLineExample" resultType="java.lang.Long">
    select count(*) from stock_inventory_in_plan_lines
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_inventory_in_plan_lines
    <set>
      <if test="record.inventoryInPlanLineId != null">
        inventory_in_plan_line_id = #{record.inventoryInPlanLineId,jdbcType=BIGINT},
      </if>
      <if test="record.inventoryInPlanHeadId != null">
        inventory_in_plan_head_id = #{record.inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="record.simCompanyCode != null">
        sim_company_code = #{record.simCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.serveCompanyCode != null">
        serve_company_code = #{record.serveCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.realNumber != null">
        real_number = #{record.realNumber,jdbcType=INTEGER},
      </if>
      <if test="record.rejectNumber != null">
        reject_number = #{record.rejectNumber,jdbcType=INTEGER},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.planInTime != null">
        plan_in_time = #{record.planInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseOrderLineId != null">
        Purchase_order_line_id = #{record.purchaseOrderLineId,jdbcType=BIGINT},
      </if>
      <if test="record.receiveItemNo != null">
        Receive_item_no = #{record.receiveItemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.unitPrice != null">
        Unit_price = #{record.unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.unitTaxRate != null">
        unit_tax_rate = #{record.unitTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.simUnitPrice != null">
        sim_unit_price = #{record.simUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.simTaxRate != null">
        sim_tax_rate = #{record.simTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.serveUnitPrice != null">
        serve_unit_price = #{record.serveUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serveTaxRate != null">
        serve_tax_rate = #{record.serveTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.priceExcludingTax != null">
        Price_excluding_tax = #{record.priceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="record.totalPrice != null">
        Total_price = #{record.totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.currency != null">
        Currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelRemark != null">
        cancel_remark = #{record.cancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.simReNewUnitPrice != null">
        sim_re_new_unit_price = #{record.simReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.simReNewTaxRate != null">
        sim_re_new_tax_rate = #{record.simReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.serveReNewUnitPrice != null">
        serve_re_new_unit_price = #{record.serveReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serveReNewTaxRate != null">
        serve_re_new_tax_rate = #{record.serveReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.simGroupReNewUnitPrice != null">
        sim_group_re_new_unit_price = #{record.simGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.simGroupReNewTaxRate != null">
        sim_group_re_new_tax_rate = #{record.simGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.serveGroupReNewUnitPrice != null">
        serve_group_re_new_unit_price = #{record.serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serveGroupReNewTaxRate != null">
        serve_group_re_new_tax_rate = #{record.serveGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_inventory_in_plan_lines
    set inventory_in_plan_line_id = #{record.inventoryInPlanLineId,jdbcType=BIGINT},
      inventory_in_plan_head_id = #{record.inventoryInPlanHeadId,jdbcType=BIGINT},
      sim_company_code = #{record.simCompanyCode,jdbcType=VARCHAR},
      serve_company_code = #{record.serveCompanyCode,jdbcType=VARCHAR},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=INTEGER},
      real_number = #{record.realNumber,jdbcType=INTEGER},
      reject_number = #{record.rejectNumber,jdbcType=INTEGER},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      plan_in_time = #{record.planInTime,jdbcType=TIMESTAMP},
      Purchase_order_line_id = #{record.purchaseOrderLineId,jdbcType=BIGINT},
      Receive_item_no = #{record.receiveItemNo,jdbcType=VARCHAR},
      Unit_price = #{record.unitPrice,jdbcType=DECIMAL},
      unit_tax_rate = #{record.unitTaxRate,jdbcType=DECIMAL},
      sim_unit_price = #{record.simUnitPrice,jdbcType=DECIMAL},
      sim_tax_rate = #{record.simTaxRate,jdbcType=DECIMAL},
      serve_unit_price = #{record.serveUnitPrice,jdbcType=DECIMAL},
      serve_tax_rate = #{record.serveTaxRate,jdbcType=DECIMAL},
      Price_excluding_tax = #{record.priceExcludingTax,jdbcType=DECIMAL},
      Total_price = #{record.totalPrice,jdbcType=DECIMAL},
      Currency = #{record.currency,jdbcType=VARCHAR},
      cancel_remark = #{record.cancelRemark,jdbcType=VARCHAR},
      CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{record.createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{record.updatedAt,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=VARCHAR},
      sim_re_new_unit_price = #{record.simReNewUnitPrice,jdbcType=DECIMAL},
      sim_re_new_tax_rate = #{record.simReNewTaxRate,jdbcType=DECIMAL},
      serve_re_new_unit_price = #{record.serveReNewUnitPrice,jdbcType=DECIMAL},
      serve_re_new_tax_rate = #{record.serveReNewTaxRate,jdbcType=DECIMAL},
      sim_group_re_new_unit_price = #{record.simGroupReNewUnitPrice,jdbcType=DECIMAL},
      sim_group_re_new_tax_rate = #{record.simGroupReNewTaxRate,jdbcType=DECIMAL},
      serve_group_re_new_unit_price = #{record.serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      serve_group_re_new_tax_rate = #{record.serveGroupReNewTaxRate,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLine">
    update stock_inventory_in_plan_lines
    <set>
      <if test="inventoryInPlanHeadId != null">
        inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT},
      </if>
      <if test="simCompanyCode != null">
        sim_company_code = #{simCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="serveCompanyCode != null">
        serve_company_code = #{serveCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="realNumber != null">
        real_number = #{realNumber,jdbcType=INTEGER},
      </if>
      <if test="rejectNumber != null">
        reject_number = #{rejectNumber,jdbcType=INTEGER},
      </if>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="planInTime != null">
        plan_in_time = #{planInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrderLineId != null">
        Purchase_order_line_id = #{purchaseOrderLineId,jdbcType=BIGINT},
      </if>
      <if test="receiveItemNo != null">
        Receive_item_no = #{receiveItemNo,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        Unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="unitTaxRate != null">
        unit_tax_rate = #{unitTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simUnitPrice != null">
        sim_unit_price = #{simUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simTaxRate != null">
        sim_tax_rate = #{simTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveUnitPrice != null">
        serve_unit_price = #{serveUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveTaxRate != null">
        serve_tax_rate = #{serveTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="priceExcludingTax != null">
        Price_excluding_tax = #{priceExcludingTax,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        Total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        Currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="cancelRemark != null">
        cancel_remark = #{cancelRemark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="simReNewUnitPrice != null">
        sim_re_new_unit_price = #{simReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simReNewTaxRate != null">
        sim_re_new_tax_rate = #{simReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewUnitPrice != null">
        serve_re_new_unit_price = #{serveReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveReNewTaxRate != null">
        serve_re_new_tax_rate = #{serveReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewUnitPrice != null">
        sim_group_re_new_unit_price = #{simGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="simGroupReNewTaxRate != null">
        sim_group_re_new_tax_rate = #{simGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewUnitPrice != null">
        serve_group_re_new_unit_price = #{serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="serveGroupReNewTaxRate != null">
        serve_group_re_new_tax_rate = #{serveGroupReNewTaxRate,jdbcType=DECIMAL},
      </if>
    </set>
    where inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLine">
    update stock_inventory_in_plan_lines
    set inventory_in_plan_head_id = #{inventoryInPlanHeadId,jdbcType=BIGINT},
      sim_company_code = #{simCompanyCode,jdbcType=VARCHAR},
      serve_company_code = #{serveCompanyCode,jdbcType=VARCHAR},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      number = #{number,jdbcType=INTEGER},
      real_number = #{realNumber,jdbcType=INTEGER},
      reject_number = #{rejectNumber,jdbcType=INTEGER},
      assets_code = #{assetsCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      plan_in_time = #{planInTime,jdbcType=TIMESTAMP},
      Purchase_order_line_id = #{purchaseOrderLineId,jdbcType=BIGINT},
      Receive_item_no = #{receiveItemNo,jdbcType=VARCHAR},
      Unit_price = #{unitPrice,jdbcType=DECIMAL},
      unit_tax_rate = #{unitTaxRate,jdbcType=DECIMAL},
      sim_unit_price = #{simUnitPrice,jdbcType=DECIMAL},
      sim_tax_rate = #{simTaxRate,jdbcType=DECIMAL},
      serve_unit_price = #{serveUnitPrice,jdbcType=DECIMAL},
      serve_tax_rate = #{serveTaxRate,jdbcType=DECIMAL},
      Price_excluding_tax = #{priceExcludingTax,jdbcType=DECIMAL},
      Total_price = #{totalPrice,jdbcType=DECIMAL},
      Currency = #{currency,jdbcType=VARCHAR},
      cancel_remark = #{cancelRemark,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=VARCHAR},
      sim_re_new_unit_price = #{simReNewUnitPrice,jdbcType=DECIMAL},
      sim_re_new_tax_rate = #{simReNewTaxRate,jdbcType=DECIMAL},
      serve_re_new_unit_price = #{serveReNewUnitPrice,jdbcType=DECIMAL},
      serve_re_new_tax_rate = #{serveReNewTaxRate,jdbcType=DECIMAL},
      sim_group_re_new_unit_price = #{simGroupReNewUnitPrice,jdbcType=DECIMAL},
      sim_group_re_new_tax_rate = #{simGroupReNewTaxRate,jdbcType=DECIMAL},
      serve_group_re_new_unit_price = #{serveGroupReNewUnitPrice,jdbcType=DECIMAL},
      serve_group_re_new_tax_rate = #{serveGroupReNewTaxRate,jdbcType=DECIMAL}
    where inventory_in_plan_line_id = #{inventoryInPlanLineId,jdbcType=BIGINT}
  </update>
</mapper>