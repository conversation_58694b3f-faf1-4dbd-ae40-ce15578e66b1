<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsEbsSyncChangeMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="query_code" jdbcType="VARCHAR" property="queryCode" />
    <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="extract_start_date" jdbcType="TIMESTAMP" property="extractStartDate" />
    <result column="extract_end_date" jdbcType="TIMESTAMP" property="extractEndDate" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
    <result column="business_line_no" jdbcType="VARCHAR" property="businessLineNo" />
    <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
    <result column="billing_time" jdbcType="VARCHAR" property="billingTime" />
    <result column="assets_name" jdbcType="VARCHAR" property="assetsName" />
    <result column="last_book_type_code" jdbcType="VARCHAR" property="lastBookTypeCode" />
    <result column="last_company_code" jdbcType="VARCHAR" property="lastCompanyCode" />
    <result column="last_dept_code" jdbcType="VARCHAR" property="lastDeptCode" />
    <result column="last_address" jdbcType="VARCHAR" property="lastAddress" />
    <result column="new_book_type_code" jdbcType="VARCHAR" property="newBookTypeCode" />
    <result column="new_company_code" jdbcType="VARCHAR" property="newCompanyCode" />
    <result column="new_dept_code" jdbcType="VARCHAR" property="newDeptCode" />
    <result column="new_address" jdbcType="VARCHAR" property="newAddress" />
    <result column="last_price" jdbcType="VARCHAR" property="lastPrice" />
    <result column="new_price" jdbcType="VARCHAR" property="newPrice" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="version_id" jdbcType="INTEGER" property="versionId" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="fa_code" jdbcType="VARCHAR" property="faCode" />
    <result column="asset_id" jdbcType="INTEGER" property="assetId" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, assets_code, batch_no, query_code, sync_status, error_message, extract_start_date, 
    extract_end_date, business_type, business_no, business_line_no, billing_user, billing_time, 
    assets_name, last_book_type_code, last_company_code, last_dept_code, last_address, 
    new_book_type_code, new_company_code, new_dept_code, new_address, last_price, new_price, 
    bill_code, version_id, change_reason, fa_code, asset_id, created_by, created_at, 
    updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChangeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_ebs_sync_change
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_ebs_sync_change
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_ebs_sync_change
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_ebs_sync_change (assets_code, batch_no, query_code, 
      sync_status, error_message, extract_start_date, 
      extract_end_date, business_type, business_no, 
      business_line_no, billing_user, billing_time, 
      assets_name, last_book_type_code, last_company_code, 
      last_dept_code, last_address, new_book_type_code, 
      new_company_code, new_dept_code, new_address, 
      last_price, new_price, bill_code, 
      version_id, change_reason, fa_code, 
      asset_id, created_by, created_at, 
      updated_by, updated_at)
    values (#{assetsCode,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{queryCode,jdbcType=VARCHAR}, 
      #{syncStatus,jdbcType=TINYINT}, #{errorMessage,jdbcType=VARCHAR}, #{extractStartDate,jdbcType=TIMESTAMP}, 
      #{extractEndDate,jdbcType=TIMESTAMP}, #{businessType,jdbcType=INTEGER}, #{businessNo,jdbcType=VARCHAR}, 
      #{businessLineNo,jdbcType=VARCHAR}, #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=VARCHAR}, 
      #{assetsName,jdbcType=VARCHAR}, #{lastBookTypeCode,jdbcType=VARCHAR}, #{lastCompanyCode,jdbcType=VARCHAR}, 
      #{lastDeptCode,jdbcType=VARCHAR}, #{lastAddress,jdbcType=VARCHAR}, #{newBookTypeCode,jdbcType=VARCHAR}, 
      #{newCompanyCode,jdbcType=VARCHAR}, #{newDeptCode,jdbcType=VARCHAR}, #{newAddress,jdbcType=VARCHAR}, 
      #{lastPrice,jdbcType=VARCHAR}, #{newPrice,jdbcType=VARCHAR}, #{billCode,jdbcType=VARCHAR}, 
      #{versionId,jdbcType=INTEGER}, #{changeReason,jdbcType=VARCHAR}, #{faCode,jdbcType=VARCHAR}, 
      #{assetId,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_ebs_sync_change
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="queryCode != null">
        query_code,
      </if>
      <if test="syncStatus != null">
        sync_status,
      </if>
      <if test="errorMessage != null">
        error_message,
      </if>
      <if test="extractStartDate != null">
        extract_start_date,
      </if>
      <if test="extractEndDate != null">
        extract_end_date,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="businessNo != null">
        business_no,
      </if>
      <if test="businessLineNo != null">
        business_line_no,
      </if>
      <if test="billingUser != null">
        billing_user,
      </if>
      <if test="billingTime != null">
        billing_time,
      </if>
      <if test="assetsName != null">
        assets_name,
      </if>
      <if test="lastBookTypeCode != null">
        last_book_type_code,
      </if>
      <if test="lastCompanyCode != null">
        last_company_code,
      </if>
      <if test="lastDeptCode != null">
        last_dept_code,
      </if>
      <if test="lastAddress != null">
        last_address,
      </if>
      <if test="newBookTypeCode != null">
        new_book_type_code,
      </if>
      <if test="newCompanyCode != null">
        new_company_code,
      </if>
      <if test="newDeptCode != null">
        new_dept_code,
      </if>
      <if test="newAddress != null">
        new_address,
      </if>
      <if test="lastPrice != null">
        last_price,
      </if>
      <if test="newPrice != null">
        new_price,
      </if>
      <if test="billCode != null">
        bill_code,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
      <if test="changeReason != null">
        change_reason,
      </if>
      <if test="faCode != null">
        fa_code,
      </if>
      <if test="assetId != null">
        asset_id,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="queryCode != null">
        #{queryCode,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=TINYINT},
      </if>
      <if test="errorMessage != null">
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="extractStartDate != null">
        #{extractStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extractEndDate != null">
        #{extractEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessLineNo != null">
        #{businessLineNo,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        #{billingTime,jdbcType=VARCHAR},
      </if>
      <if test="assetsName != null">
        #{assetsName,jdbcType=VARCHAR},
      </if>
      <if test="lastBookTypeCode != null">
        #{lastBookTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="lastCompanyCode != null">
        #{lastCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="lastDeptCode != null">
        #{lastDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="lastAddress != null">
        #{lastAddress,jdbcType=VARCHAR},
      </if>
      <if test="newBookTypeCode != null">
        #{newBookTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="newCompanyCode != null">
        #{newCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="newDeptCode != null">
        #{newDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="newAddress != null">
        #{newAddress,jdbcType=VARCHAR},
      </if>
      <if test="lastPrice != null">
        #{lastPrice,jdbcType=VARCHAR},
      </if>
      <if test="newPrice != null">
        #{newPrice,jdbcType=VARCHAR},
      </if>
      <if test="billCode != null">
        #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=INTEGER},
      </if>
      <if test="changeReason != null">
        #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="faCode != null">
        #{faCode,jdbcType=VARCHAR},
      </if>
      <if test="assetId != null">
        #{assetId,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChangeExample" resultType="java.lang.Long">
    select count(*) from stock_assets_ebs_sync_change
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_ebs_sync_change
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.queryCode != null">
        query_code = #{record.queryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.syncStatus != null">
        sync_status = #{record.syncStatus,jdbcType=TINYINT},
      </if>
      <if test="record.errorMessage != null">
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.extractStartDate != null">
        extract_start_date = #{record.extractStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extractEndDate != null">
        extract_end_date = #{record.extractEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.businessNo != null">
        business_no = #{record.businessNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessLineNo != null">
        business_line_no = #{record.businessLineNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billingUser != null">
        billing_user = #{record.billingUser,jdbcType=VARCHAR},
      </if>
      <if test="record.billingTime != null">
        billing_time = #{record.billingTime,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsName != null">
        assets_name = #{record.assetsName,jdbcType=VARCHAR},
      </if>
      <if test="record.lastBookTypeCode != null">
        last_book_type_code = #{record.lastBookTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lastCompanyCode != null">
        last_company_code = #{record.lastCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lastDeptCode != null">
        last_dept_code = #{record.lastDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lastAddress != null">
        last_address = #{record.lastAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.newBookTypeCode != null">
        new_book_type_code = #{record.newBookTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.newCompanyCode != null">
        new_company_code = #{record.newCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.newDeptCode != null">
        new_dept_code = #{record.newDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.newAddress != null">
        new_address = #{record.newAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.lastPrice != null">
        last_price = #{record.lastPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.newPrice != null">
        new_price = #{record.newPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.billCode != null">
        bill_code = #{record.billCode,jdbcType=VARCHAR},
      </if>
      <if test="record.versionId != null">
        version_id = #{record.versionId,jdbcType=INTEGER},
      </if>
      <if test="record.changeReason != null">
        change_reason = #{record.changeReason,jdbcType=VARCHAR},
      </if>
      <if test="record.faCode != null">
        fa_code = #{record.faCode,jdbcType=VARCHAR},
      </if>
      <if test="record.assetId != null">
        asset_id = #{record.assetId,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_ebs_sync_change
    set id = #{record.id,jdbcType=BIGINT},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      query_code = #{record.queryCode,jdbcType=VARCHAR},
      sync_status = #{record.syncStatus,jdbcType=TINYINT},
      error_message = #{record.errorMessage,jdbcType=VARCHAR},
      extract_start_date = #{record.extractStartDate,jdbcType=TIMESTAMP},
      extract_end_date = #{record.extractEndDate,jdbcType=TIMESTAMP},
      business_type = #{record.businessType,jdbcType=INTEGER},
      business_no = #{record.businessNo,jdbcType=VARCHAR},
      business_line_no = #{record.businessLineNo,jdbcType=VARCHAR},
      billing_user = #{record.billingUser,jdbcType=VARCHAR},
      billing_time = #{record.billingTime,jdbcType=VARCHAR},
      assets_name = #{record.assetsName,jdbcType=VARCHAR},
      last_book_type_code = #{record.lastBookTypeCode,jdbcType=VARCHAR},
      last_company_code = #{record.lastCompanyCode,jdbcType=VARCHAR},
      last_dept_code = #{record.lastDeptCode,jdbcType=VARCHAR},
      last_address = #{record.lastAddress,jdbcType=VARCHAR},
      new_book_type_code = #{record.newBookTypeCode,jdbcType=VARCHAR},
      new_company_code = #{record.newCompanyCode,jdbcType=VARCHAR},
      new_dept_code = #{record.newDeptCode,jdbcType=VARCHAR},
      new_address = #{record.newAddress,jdbcType=VARCHAR},
      last_price = #{record.lastPrice,jdbcType=VARCHAR},
      new_price = #{record.newPrice,jdbcType=VARCHAR},
      bill_code = #{record.billCode,jdbcType=VARCHAR},
      version_id = #{record.versionId,jdbcType=INTEGER},
      change_reason = #{record.changeReason,jdbcType=VARCHAR},
      fa_code = #{record.faCode,jdbcType=VARCHAR},
      asset_id = #{record.assetId,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
    update stock_assets_ebs_sync_change
    <set>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="queryCode != null">
        query_code = #{queryCode,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        sync_status = #{syncStatus,jdbcType=TINYINT},
      </if>
      <if test="errorMessage != null">
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="extractStartDate != null">
        extract_start_date = #{extractStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extractEndDate != null">
        extract_end_date = #{extractEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null">
        business_no = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessLineNo != null">
        business_line_no = #{businessLineNo,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        billing_user = #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        billing_time = #{billingTime,jdbcType=VARCHAR},
      </if>
      <if test="assetsName != null">
        assets_name = #{assetsName,jdbcType=VARCHAR},
      </if>
      <if test="lastBookTypeCode != null">
        last_book_type_code = #{lastBookTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="lastCompanyCode != null">
        last_company_code = #{lastCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="lastDeptCode != null">
        last_dept_code = #{lastDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="lastAddress != null">
        last_address = #{lastAddress,jdbcType=VARCHAR},
      </if>
      <if test="newBookTypeCode != null">
        new_book_type_code = #{newBookTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="newCompanyCode != null">
        new_company_code = #{newCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="newDeptCode != null">
        new_dept_code = #{newDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="newAddress != null">
        new_address = #{newAddress,jdbcType=VARCHAR},
      </if>
      <if test="lastPrice != null">
        last_price = #{lastPrice,jdbcType=VARCHAR},
      </if>
      <if test="newPrice != null">
        new_price = #{newPrice,jdbcType=VARCHAR},
      </if>
      <if test="billCode != null">
        bill_code = #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=INTEGER},
      </if>
      <if test="changeReason != null">
        change_reason = #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="faCode != null">
        fa_code = #{faCode,jdbcType=VARCHAR},
      </if>
      <if test="assetId != null">
        asset_id = #{assetId,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
    update stock_assets_ebs_sync_change
    set assets_code = #{assetsCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      query_code = #{queryCode,jdbcType=VARCHAR},
      sync_status = #{syncStatus,jdbcType=TINYINT},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      extract_start_date = #{extractStartDate,jdbcType=TIMESTAMP},
      extract_end_date = #{extractEndDate,jdbcType=TIMESTAMP},
      business_type = #{businessType,jdbcType=INTEGER},
      business_no = #{businessNo,jdbcType=VARCHAR},
      business_line_no = #{businessLineNo,jdbcType=VARCHAR},
      billing_user = #{billingUser,jdbcType=VARCHAR},
      billing_time = #{billingTime,jdbcType=VARCHAR},
      assets_name = #{assetsName,jdbcType=VARCHAR},
      last_book_type_code = #{lastBookTypeCode,jdbcType=VARCHAR},
      last_company_code = #{lastCompanyCode,jdbcType=VARCHAR},
      last_dept_code = #{lastDeptCode,jdbcType=VARCHAR},
      last_address = #{lastAddress,jdbcType=VARCHAR},
      new_book_type_code = #{newBookTypeCode,jdbcType=VARCHAR},
      new_company_code = #{newCompanyCode,jdbcType=VARCHAR},
      new_dept_code = #{newDeptCode,jdbcType=VARCHAR},
      new_address = #{newAddress,jdbcType=VARCHAR},
      last_price = #{lastPrice,jdbcType=VARCHAR},
      new_price = #{newPrice,jdbcType=VARCHAR},
      bill_code = #{billCode,jdbcType=VARCHAR},
      version_id = #{versionId,jdbcType=INTEGER},
      change_reason = #{changeReason,jdbcType=VARCHAR},
      fa_code = #{faCode,jdbcType=VARCHAR},
      asset_id = #{assetId,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>