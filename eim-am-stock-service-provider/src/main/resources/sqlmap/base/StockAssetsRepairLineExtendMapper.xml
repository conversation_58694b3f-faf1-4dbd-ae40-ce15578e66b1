<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsRepairLineExtendMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="repair_item_no" jdbcType="VARCHAR" property="repairItemNo" />
    <result column="field1" jdbcType="VARCHAR" property="field1" />
    <result column="field2" jdbcType="VARCHAR" property="field2" />
    <result column="field3" jdbcType="VARCHAR" property="field3" />
    <result column="field4" jdbcType="VARCHAR" property="field4" />
    <result column="field5" jdbcType="VARCHAR" property="field5" />
    <result column="field6" jdbcType="VARCHAR" property="field6" />
    <result column="field7" jdbcType="VARCHAR" property="field7" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, repair_item_no, field1, field2, field3, field4, field5, field6, field7, created_by, 
    created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_repair_line_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_repair_line_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_repair_line_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    insert into stock_assets_repair_line_extend (id, repair_item_no, field1, 
      field2, field3, field4, 
      field5, field6, field7, 
      created_by, created_at, updated_by, 
      updated_at)
    values (#{id,jdbcType=BIGINT}, #{repairItemNo,jdbcType=VARCHAR}, #{field1,jdbcType=VARCHAR}, 
      #{field2,jdbcType=VARCHAR}, #{field3,jdbcType=VARCHAR}, #{field4,jdbcType=VARCHAR}, 
      #{field5,jdbcType=VARCHAR}, #{field6,jdbcType=VARCHAR}, #{field7,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    insert into stock_assets_repair_line_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="repairItemNo != null">
        repair_item_no,
      </if>
      <if test="field1 != null">
        field1,
      </if>
      <if test="field2 != null">
        field2,
      </if>
      <if test="field3 != null">
        field3,
      </if>
      <if test="field4 != null">
        field4,
      </if>
      <if test="field5 != null">
        field5,
      </if>
      <if test="field6 != null">
        field6,
      </if>
      <if test="field7 != null">
        field7,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="repairItemNo != null">
        #{repairItemNo,jdbcType=VARCHAR},
      </if>
      <if test="field1 != null">
        #{field1,jdbcType=VARCHAR},
      </if>
      <if test="field2 != null">
        #{field2,jdbcType=VARCHAR},
      </if>
      <if test="field3 != null">
        #{field3,jdbcType=VARCHAR},
      </if>
      <if test="field4 != null">
        #{field4,jdbcType=VARCHAR},
      </if>
      <if test="field5 != null">
        #{field5,jdbcType=VARCHAR},
      </if>
      <if test="field6 != null">
        #{field6,jdbcType=VARCHAR},
      </if>
      <if test="field7 != null">
        #{field7,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtendExample" resultType="java.lang.Long">
    select count(*) from stock_assets_repair_line_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_repair_line_extend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.repairItemNo != null">
        repair_item_no = #{record.repairItemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.field1 != null">
        field1 = #{record.field1,jdbcType=VARCHAR},
      </if>
      <if test="record.field2 != null">
        field2 = #{record.field2,jdbcType=VARCHAR},
      </if>
      <if test="record.field3 != null">
        field3 = #{record.field3,jdbcType=VARCHAR},
      </if>
      <if test="record.field4 != null">
        field4 = #{record.field4,jdbcType=VARCHAR},
      </if>
      <if test="record.field5 != null">
        field5 = #{record.field5,jdbcType=VARCHAR},
      </if>
      <if test="record.field6 != null">
        field6 = #{record.field6,jdbcType=VARCHAR},
      </if>
      <if test="record.field7 != null">
        field7 = #{record.field7,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_repair_line_extend
    set id = #{record.id,jdbcType=BIGINT},
      repair_item_no = #{record.repairItemNo,jdbcType=VARCHAR},
      field1 = #{record.field1,jdbcType=VARCHAR},
      field2 = #{record.field2,jdbcType=VARCHAR},
      field3 = #{record.field3,jdbcType=VARCHAR},
      field4 = #{record.field4,jdbcType=VARCHAR},
      field5 = #{record.field5,jdbcType=VARCHAR},
      field6 = #{record.field6,jdbcType=VARCHAR},
      field7 = #{record.field7,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    update stock_assets_repair_line_extend
    <set>
      <if test="repairItemNo != null">
        repair_item_no = #{repairItemNo,jdbcType=VARCHAR},
      </if>
      <if test="field1 != null">
        field1 = #{field1,jdbcType=VARCHAR},
      </if>
      <if test="field2 != null">
        field2 = #{field2,jdbcType=VARCHAR},
      </if>
      <if test="field3 != null">
        field3 = #{field3,jdbcType=VARCHAR},
      </if>
      <if test="field4 != null">
        field4 = #{field4,jdbcType=VARCHAR},
      </if>
      <if test="field5 != null">
        field5 = #{field5,jdbcType=VARCHAR},
      </if>
      <if test="field6 != null">
        field6 = #{field6,jdbcType=VARCHAR},
      </if>
      <if test="field7 != null">
        field7 = #{field7,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    update stock_assets_repair_line_extend
    set repair_item_no = #{repairItemNo,jdbcType=VARCHAR},
      field1 = #{field1,jdbcType=VARCHAR},
      field2 = #{field2,jdbcType=VARCHAR},
      field3 = #{field3,jdbcType=VARCHAR},
      field4 = #{field4,jdbcType=VARCHAR},
      field5 = #{field5,jdbcType=VARCHAR},
      field6 = #{field6,jdbcType=VARCHAR},
      field7 = #{field7,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>