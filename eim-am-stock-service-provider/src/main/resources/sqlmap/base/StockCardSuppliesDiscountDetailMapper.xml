<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockCardSuppliesDiscountDetailMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="discount_no" jdbcType="VARCHAR" property="discountNo" />
    <result column="convert_supplies_code" jdbcType="VARCHAR" property="convertSuppliesCode" />
    <result column="switch_supplies_code" jdbcType="VARCHAR" property="switchSuppliesCode" />
    <result column="convert_number" jdbcType="INTEGER" property="convertNumber" />
    <result column="convert_result" jdbcType="DECIMAL" property="convertResult" />
    <result column="discount_scale" jdbcType="DECIMAL" property="discountScale" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, discount_no, convert_supplies_code, switch_supplies_code, convert_number, convert_result, 
    discount_scale, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_card_supplies_discount_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_card_supplies_discount_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_card_supplies_discount_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail">
    insert into stock_card_supplies_discount_detail (id, discount_no, convert_supplies_code, 
      switch_supplies_code, convert_number, convert_result, 
      discount_scale, created_by, created_at, 
      updated_by, updated_at)
    values (#{id,jdbcType=BIGINT}, #{discountNo,jdbcType=VARCHAR}, #{convertSuppliesCode,jdbcType=VARCHAR}, 
      #{switchSuppliesCode,jdbcType=VARCHAR}, #{convertNumber,jdbcType=INTEGER}, #{convertResult,jdbcType=DECIMAL}, 
      #{discountScale,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail">
    insert into stock_card_supplies_discount_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="discountNo != null">
        discount_no,
      </if>
      <if test="convertSuppliesCode != null">
        convert_supplies_code,
      </if>
      <if test="switchSuppliesCode != null">
        switch_supplies_code,
      </if>
      <if test="convertNumber != null">
        convert_number,
      </if>
      <if test="convertResult != null">
        convert_result,
      </if>
      <if test="discountScale != null">
        discount_scale,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="discountNo != null">
        #{discountNo,jdbcType=VARCHAR},
      </if>
      <if test="convertSuppliesCode != null">
        #{convertSuppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="switchSuppliesCode != null">
        #{switchSuppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="convertNumber != null">
        #{convertNumber,jdbcType=INTEGER},
      </if>
      <if test="convertResult != null">
        #{convertResult,jdbcType=DECIMAL},
      </if>
      <if test="discountScale != null">
        #{discountScale,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetailExample" resultType="java.lang.Long">
    select count(*) from stock_card_supplies_discount_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_card_supplies_discount_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.discountNo != null">
        discount_no = #{record.discountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.convertSuppliesCode != null">
        convert_supplies_code = #{record.convertSuppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.switchSuppliesCode != null">
        switch_supplies_code = #{record.switchSuppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.convertNumber != null">
        convert_number = #{record.convertNumber,jdbcType=INTEGER},
      </if>
      <if test="record.convertResult != null">
        convert_result = #{record.convertResult,jdbcType=DECIMAL},
      </if>
      <if test="record.discountScale != null">
        discount_scale = #{record.discountScale,jdbcType=DECIMAL},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_card_supplies_discount_detail
    set id = #{record.id,jdbcType=BIGINT},
      discount_no = #{record.discountNo,jdbcType=VARCHAR},
      convert_supplies_code = #{record.convertSuppliesCode,jdbcType=VARCHAR},
      switch_supplies_code = #{record.switchSuppliesCode,jdbcType=VARCHAR},
      convert_number = #{record.convertNumber,jdbcType=INTEGER},
      convert_result = #{record.convertResult,jdbcType=DECIMAL},
      discount_scale = #{record.discountScale,jdbcType=DECIMAL},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail">
    update stock_card_supplies_discount_detail
    <set>
      <if test="discountNo != null">
        discount_no = #{discountNo,jdbcType=VARCHAR},
      </if>
      <if test="convertSuppliesCode != null">
        convert_supplies_code = #{convertSuppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="switchSuppliesCode != null">
        switch_supplies_code = #{switchSuppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="convertNumber != null">
        convert_number = #{convertNumber,jdbcType=INTEGER},
      </if>
      <if test="convertResult != null">
        convert_result = #{convertResult,jdbcType=DECIMAL},
      </if>
      <if test="discountScale != null">
        discount_scale = #{discountScale,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail">
    update stock_card_supplies_discount_detail
    set discount_no = #{discountNo,jdbcType=VARCHAR},
      convert_supplies_code = #{convertSuppliesCode,jdbcType=VARCHAR},
      switch_supplies_code = #{switchSuppliesCode,jdbcType=VARCHAR},
      convert_number = #{convertNumber,jdbcType=INTEGER},
      convert_result = #{convertResult,jdbcType=DECIMAL},
      discount_scale = #{discountScale,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>