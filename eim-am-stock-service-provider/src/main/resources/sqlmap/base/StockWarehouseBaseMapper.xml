<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockWarehouseBaseMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockWarehouseBase">
    <id column="base_id" jdbcType="BIGINT" property="baseId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="acreage" jdbcType="DECIMAL" property="acreage" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="linkman" jdbcType="VARCHAR" property="linkman" />
    <result column="contact_way" jdbcType="VARCHAR" property="contactWay" />
    <result column="bus_large_region" jdbcType="VARCHAR" property="busLargeRegion" />
    <result column="bus_city" jdbcType="VARCHAR" property="busCity" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    base_id, name, warehouse_code, acreage, country, country_code, province, province_code, 
    city, city_code, area, area_code, address, linkman, contact_way, bus_large_region, 
    bus_city, status, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockWarehouseBaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_warehouse_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_warehouse_base
    where base_id = #{baseId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_warehouse_base
    where base_id = #{baseId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockWarehouseBase">
    insert into stock_warehouse_base (base_id, name, warehouse_code, 
      acreage, country, country_code, 
      province, province_code, city, 
      city_code, area, area_code, 
      address, linkman, contact_way, 
      bus_large_region, bus_city, status, 
      created_by, created_at, updated_by, 
      updated_at)
    values (#{baseId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{acreage,jdbcType=DECIMAL}, #{country,jdbcType=VARCHAR}, #{countryCode,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{linkman,jdbcType=VARCHAR}, #{contactWay,jdbcType=VARCHAR}, 
      #{busLargeRegion,jdbcType=VARCHAR}, #{busCity,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockWarehouseBase">
    insert into stock_warehouse_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="baseId != null">
        base_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="acreage != null">
        acreage,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="countryCode != null">
        country_code,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="linkman != null">
        linkman,
      </if>
      <if test="contactWay != null">
        contact_way,
      </if>
      <if test="busLargeRegion != null">
        bus_large_region,
      </if>
      <if test="busCity != null">
        bus_city,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="baseId != null">
        #{baseId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="acreage != null">
        #{acreage,jdbcType=DECIMAL},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null">
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="contactWay != null">
        #{contactWay,jdbcType=VARCHAR},
      </if>
      <if test="busLargeRegion != null">
        #{busLargeRegion,jdbcType=VARCHAR},
      </if>
      <if test="busCity != null">
        #{busCity,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockWarehouseBaseExample" resultType="java.lang.Long">
    select count(*) from stock_warehouse_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_warehouse_base
    <set>
      <if test="record.baseId != null">
        base_id = #{record.baseId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.acreage != null">
        acreage = #{record.acreage,jdbcType=DECIMAL},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.countryCode != null">
        country_code = #{record.countryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.areaCode != null">
        area_code = #{record.areaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.linkman != null">
        linkman = #{record.linkman,jdbcType=VARCHAR},
      </if>
      <if test="record.contactWay != null">
        contact_way = #{record.contactWay,jdbcType=VARCHAR},
      </if>
      <if test="record.busLargeRegion != null">
        bus_large_region = #{record.busLargeRegion,jdbcType=VARCHAR},
      </if>
      <if test="record.busCity != null">
        bus_city = #{record.busCity,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_warehouse_base
    set base_id = #{record.baseId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      acreage = #{record.acreage,jdbcType=DECIMAL},
      country = #{record.country,jdbcType=VARCHAR},
      country_code = #{record.countryCode,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      area_code = #{record.areaCode,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      linkman = #{record.linkman,jdbcType=VARCHAR},
      contact_way = #{record.contactWay,jdbcType=VARCHAR},
      bus_large_region = #{record.busLargeRegion,jdbcType=VARCHAR},
      bus_city = #{record.busCity,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockWarehouseBase">
    update stock_warehouse_base
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="acreage != null">
        acreage = #{acreage,jdbcType=DECIMAL},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="countryCode != null">
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        linkman = #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="contactWay != null">
        contact_way = #{contactWay,jdbcType=VARCHAR},
      </if>
      <if test="busLargeRegion != null">
        bus_large_region = #{busLargeRegion,jdbcType=VARCHAR},
      </if>
      <if test="busCity != null">
        bus_city = #{busCity,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where base_id = #{baseId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockWarehouseBase">
    update stock_warehouse_base
    set name = #{name,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      acreage = #{acreage,jdbcType=DECIMAL},
      country = #{country,jdbcType=VARCHAR},
      country_code = #{countryCode,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      linkman = #{linkman,jdbcType=VARCHAR},
      contact_way = #{contactWay,jdbcType=VARCHAR},
      bus_large_region = #{busLargeRegion,jdbcType=VARCHAR},
      bus_city = #{busCity,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where base_id = #{baseId,jdbcType=BIGINT}
  </update>
</mapper>