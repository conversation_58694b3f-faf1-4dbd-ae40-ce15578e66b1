<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsCheckMethodConfigMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="check_task_method_code" jdbcType="TINYINT" property="checkTaskMethodCode" />
    <result column="check_task_method_name" jdbcType="VARCHAR" property="checkTaskMethodName" />
    <result column="default_check_people" jdbcType="VARCHAR" property="defaultCheckPeople" />
    <result column="change_check_people_flag" jdbcType="TINYINT" property="changeCheckPeopleFlag" />
    <result column="photo_check_flag" jdbcType="TINYINT" property="photoCheckFlag" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, check_task_method_code, check_task_method_name, default_check_people, change_check_people_flag, 
    photo_check_flag, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_check_method_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_check_method_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_check_method_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfig">
    insert into stock_assets_check_method_config (id, check_task_method_code, check_task_method_name, 
      default_check_people, change_check_people_flag, 
      photo_check_flag, created_by, created_at, 
      updated_by, updated_at)
    values (#{id,jdbcType=BIGINT}, #{checkTaskMethodCode,jdbcType=TINYINT}, #{checkTaskMethodName,jdbcType=VARCHAR}, 
      #{defaultCheckPeople,jdbcType=VARCHAR}, #{changeCheckPeopleFlag,jdbcType=TINYINT}, 
      #{photoCheckFlag,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfig">
    insert into stock_assets_check_method_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="checkTaskMethodCode != null">
        check_task_method_code,
      </if>
      <if test="checkTaskMethodName != null">
        check_task_method_name,
      </if>
      <if test="defaultCheckPeople != null">
        default_check_people,
      </if>
      <if test="changeCheckPeopleFlag != null">
        change_check_people_flag,
      </if>
      <if test="photoCheckFlag != null">
        photo_check_flag,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="checkTaskMethodCode != null">
        #{checkTaskMethodCode,jdbcType=TINYINT},
      </if>
      <if test="checkTaskMethodName != null">
        #{checkTaskMethodName,jdbcType=VARCHAR},
      </if>
      <if test="defaultCheckPeople != null">
        #{defaultCheckPeople,jdbcType=VARCHAR},
      </if>
      <if test="changeCheckPeopleFlag != null">
        #{changeCheckPeopleFlag,jdbcType=TINYINT},
      </if>
      <if test="photoCheckFlag != null">
        #{photoCheckFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfigExample" resultType="java.lang.Long">
    select count(*) from stock_assets_check_method_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_check_method_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.checkTaskMethodCode != null">
        check_task_method_code = #{record.checkTaskMethodCode,jdbcType=TINYINT},
      </if>
      <if test="record.checkTaskMethodName != null">
        check_task_method_name = #{record.checkTaskMethodName,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultCheckPeople != null">
        default_check_people = #{record.defaultCheckPeople,jdbcType=VARCHAR},
      </if>
      <if test="record.changeCheckPeopleFlag != null">
        change_check_people_flag = #{record.changeCheckPeopleFlag,jdbcType=TINYINT},
      </if>
      <if test="record.photoCheckFlag != null">
        photo_check_flag = #{record.photoCheckFlag,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_check_method_config
    set id = #{record.id,jdbcType=BIGINT},
      check_task_method_code = #{record.checkTaskMethodCode,jdbcType=TINYINT},
      check_task_method_name = #{record.checkTaskMethodName,jdbcType=VARCHAR},
      default_check_people = #{record.defaultCheckPeople,jdbcType=VARCHAR},
      change_check_people_flag = #{record.changeCheckPeopleFlag,jdbcType=TINYINT},
      photo_check_flag = #{record.photoCheckFlag,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfig">
    update stock_assets_check_method_config
    <set>
      <if test="checkTaskMethodCode != null">
        check_task_method_code = #{checkTaskMethodCode,jdbcType=TINYINT},
      </if>
      <if test="checkTaskMethodName != null">
        check_task_method_name = #{checkTaskMethodName,jdbcType=VARCHAR},
      </if>
      <if test="defaultCheckPeople != null">
        default_check_people = #{defaultCheckPeople,jdbcType=VARCHAR},
      </if>
      <if test="changeCheckPeopleFlag != null">
        change_check_people_flag = #{changeCheckPeopleFlag,jdbcType=TINYINT},
      </if>
      <if test="photoCheckFlag != null">
        photo_check_flag = #{photoCheckFlag,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsCheckMethodConfig">
    update stock_assets_check_method_config
    set check_task_method_code = #{checkTaskMethodCode,jdbcType=TINYINT},
      check_task_method_name = #{checkTaskMethodName,jdbcType=VARCHAR},
      default_check_people = #{defaultCheckPeople,jdbcType=VARCHAR},
      change_check_people_flag = #{changeCheckPeopleFlag,jdbcType=TINYINT},
      photo_check_flag = #{photoCheckFlag,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>