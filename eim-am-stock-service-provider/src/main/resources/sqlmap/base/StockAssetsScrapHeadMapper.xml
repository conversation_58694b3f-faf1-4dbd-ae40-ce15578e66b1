<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsScrapHeadMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsScrapHead">
    <id column="head_id" jdbcType="BIGINT" property="headId" />
    <result column="scrap_no" jdbcType="VARCHAR" property="scrapNo" />
    <result column="plan_scrap_count" jdbcType="INTEGER" property="planScrapCount" />
    <result column="real_scrap_count" jdbcType="INTEGER" property="realScrapCount" />
    <result column="plan_scrap_value" jdbcType="DECIMAL" property="planScrapValue" />
    <result column="real_scrap_value" jdbcType="DECIMAL" property="realScrapValue" />
    <result column="apply_dept" jdbcType="VARCHAR" property="applyDept" />
    <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
    <result column="billing_time" jdbcType="TIMESTAMP" property="billingTime" />
    <result column="scrap_status" jdbcType="TINYINT" property="scrapStatus" />
    <result column="exist_attach_flag" jdbcType="TINYINT" property="existAttachFlag" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bind_batch_code" jdbcType="VARCHAR" property="bindBatchCode" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    head_id, scrap_no, plan_scrap_count, real_scrap_count, plan_scrap_value, real_scrap_value, 
    apply_dept, billing_user, billing_time, scrap_status, exist_attach_flag, remark, 
    bind_batch_code, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapHeadExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_scrap_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_scrap_head
    where head_id = #{headId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_scrap_head
    where head_id = #{headId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapHead">
    <selectKey keyProperty="headId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_scrap_head (scrap_no, plan_scrap_count, real_scrap_count, 
      plan_scrap_value, real_scrap_value, apply_dept, 
      billing_user, billing_time, scrap_status, 
      exist_attach_flag, remark, bind_batch_code, 
      created_by, created_at, updated_by, 
      updated_at)
    values (#{scrapNo,jdbcType=VARCHAR}, #{planScrapCount,jdbcType=INTEGER}, #{realScrapCount,jdbcType=INTEGER}, 
      #{planScrapValue,jdbcType=DECIMAL}, #{realScrapValue,jdbcType=DECIMAL}, #{applyDept,jdbcType=VARCHAR}, 
      #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=TIMESTAMP}, #{scrapStatus,jdbcType=TINYINT}, 
      #{existAttachFlag,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{bindBatchCode,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapHead">
    <selectKey keyProperty="headId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_scrap_head
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scrapNo != null">
        scrap_no,
      </if>
      <if test="planScrapCount != null">
        plan_scrap_count,
      </if>
      <if test="realScrapCount != null">
        real_scrap_count,
      </if>
      <if test="planScrapValue != null">
        plan_scrap_value,
      </if>
      <if test="realScrapValue != null">
        real_scrap_value,
      </if>
      <if test="applyDept != null">
        apply_dept,
      </if>
      <if test="billingUser != null">
        billing_user,
      </if>
      <if test="billingTime != null">
        billing_time,
      </if>
      <if test="scrapStatus != null">
        scrap_status,
      </if>
      <if test="existAttachFlag != null">
        exist_attach_flag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="bindBatchCode != null">
        bind_batch_code,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scrapNo != null">
        #{scrapNo,jdbcType=VARCHAR},
      </if>
      <if test="planScrapCount != null">
        #{planScrapCount,jdbcType=INTEGER},
      </if>
      <if test="realScrapCount != null">
        #{realScrapCount,jdbcType=INTEGER},
      </if>
      <if test="planScrapValue != null">
        #{planScrapValue,jdbcType=DECIMAL},
      </if>
      <if test="realScrapValue != null">
        #{realScrapValue,jdbcType=DECIMAL},
      </if>
      <if test="applyDept != null">
        #{applyDept,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scrapStatus != null">
        #{scrapStatus,jdbcType=TINYINT},
      </if>
      <if test="existAttachFlag != null">
        #{existAttachFlag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bindBatchCode != null">
        #{bindBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapHeadExample" resultType="java.lang.Long">
    select count(*) from stock_assets_scrap_head
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_scrap_head
    <set>
      <if test="record.headId != null">
        head_id = #{record.headId,jdbcType=BIGINT},
      </if>
      <if test="record.scrapNo != null">
        scrap_no = #{record.scrapNo,jdbcType=VARCHAR},
      </if>
      <if test="record.planScrapCount != null">
        plan_scrap_count = #{record.planScrapCount,jdbcType=INTEGER},
      </if>
      <if test="record.realScrapCount != null">
        real_scrap_count = #{record.realScrapCount,jdbcType=INTEGER},
      </if>
      <if test="record.planScrapValue != null">
        plan_scrap_value = #{record.planScrapValue,jdbcType=DECIMAL},
      </if>
      <if test="record.realScrapValue != null">
        real_scrap_value = #{record.realScrapValue,jdbcType=DECIMAL},
      </if>
      <if test="record.applyDept != null">
        apply_dept = #{record.applyDept,jdbcType=VARCHAR},
      </if>
      <if test="record.billingUser != null">
        billing_user = #{record.billingUser,jdbcType=VARCHAR},
      </if>
      <if test="record.billingTime != null">
        billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scrapStatus != null">
        scrap_status = #{record.scrapStatus,jdbcType=TINYINT},
      </if>
      <if test="record.existAttachFlag != null">
        exist_attach_flag = #{record.existAttachFlag,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.bindBatchCode != null">
        bind_batch_code = #{record.bindBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_scrap_head
    set head_id = #{record.headId,jdbcType=BIGINT},
      scrap_no = #{record.scrapNo,jdbcType=VARCHAR},
      plan_scrap_count = #{record.planScrapCount,jdbcType=INTEGER},
      real_scrap_count = #{record.realScrapCount,jdbcType=INTEGER},
      plan_scrap_value = #{record.planScrapValue,jdbcType=DECIMAL},
      real_scrap_value = #{record.realScrapValue,jdbcType=DECIMAL},
      apply_dept = #{record.applyDept,jdbcType=VARCHAR},
      billing_user = #{record.billingUser,jdbcType=VARCHAR},
      billing_time = #{record.billingTime,jdbcType=TIMESTAMP},
      scrap_status = #{record.scrapStatus,jdbcType=TINYINT},
      exist_attach_flag = #{record.existAttachFlag,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      bind_batch_code = #{record.bindBatchCode,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapHead">
    update stock_assets_scrap_head
    <set>
      <if test="scrapNo != null">
        scrap_no = #{scrapNo,jdbcType=VARCHAR},
      </if>
      <if test="planScrapCount != null">
        plan_scrap_count = #{planScrapCount,jdbcType=INTEGER},
      </if>
      <if test="realScrapCount != null">
        real_scrap_count = #{realScrapCount,jdbcType=INTEGER},
      </if>
      <if test="planScrapValue != null">
        plan_scrap_value = #{planScrapValue,jdbcType=DECIMAL},
      </if>
      <if test="realScrapValue != null">
        real_scrap_value = #{realScrapValue,jdbcType=DECIMAL},
      </if>
      <if test="applyDept != null">
        apply_dept = #{applyDept,jdbcType=VARCHAR},
      </if>
      <if test="billingUser != null">
        billing_user = #{billingUser,jdbcType=VARCHAR},
      </if>
      <if test="billingTime != null">
        billing_time = #{billingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scrapStatus != null">
        scrap_status = #{scrapStatus,jdbcType=TINYINT},
      </if>
      <if test="existAttachFlag != null">
        exist_attach_flag = #{existAttachFlag,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bindBatchCode != null">
        bind_batch_code = #{bindBatchCode,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where head_id = #{headId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapHead">
    update stock_assets_scrap_head
    set scrap_no = #{scrapNo,jdbcType=VARCHAR},
      plan_scrap_count = #{planScrapCount,jdbcType=INTEGER},
      real_scrap_count = #{realScrapCount,jdbcType=INTEGER},
      plan_scrap_value = #{planScrapValue,jdbcType=DECIMAL},
      real_scrap_value = #{realScrapValue,jdbcType=DECIMAL},
      apply_dept = #{applyDept,jdbcType=VARCHAR},
      billing_user = #{billingUser,jdbcType=VARCHAR},
      billing_time = #{billingTime,jdbcType=TIMESTAMP},
      scrap_status = #{scrapStatus,jdbcType=TINYINT},
      exist_attach_flag = #{existAttachFlag,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      bind_batch_code = #{bindBatchCode,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where head_id = #{headId,jdbcType=BIGINT}
  </update>
</mapper>