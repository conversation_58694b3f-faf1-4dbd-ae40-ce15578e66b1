<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetCodeMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="asset_code" jdbcType="VARCHAR" property="assetCode" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="is_used" jdbcType="INTEGER" property="isUsed" />
    <result column="operating" jdbcType="VARCHAR" property="operating" />
    <result column="operate_user" jdbcType="VARCHAR" property="operateUser" />
    <result column="is_in" jdbcType="INTEGER" property="isIn" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="yes_low_value" jdbcType="INTEGER" property="yesLowValue" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, asset_code, category_code, company_code, is_used, operating, operate_user, is_in, 
    status, yes_low_value, del_flag, created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_asset_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_asset_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_asset_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetCode">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_asset_code (asset_code, category_code, company_code, 
      is_used, operating, operate_user, 
      is_in, status, yes_low_value, 
      del_flag, created_by, created_at, 
      updated_by, updated_at)
    values (#{assetCode,jdbcType=VARCHAR}, #{categoryCode,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{isUsed,jdbcType=INTEGER}, #{operating,jdbcType=VARCHAR}, #{operateUser,jdbcType=VARCHAR}, 
      #{isIn,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{yesLowValue,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetCode">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_asset_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetCode != null">
        asset_code,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="isUsed != null">
        is_used,
      </if>
      <if test="operating != null">
        operating,
      </if>
      <if test="operateUser != null">
        operate_user,
      </if>
      <if test="isIn != null">
        is_in,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="yesLowValue != null">
        yes_low_value,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetCode != null">
        #{assetCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="isUsed != null">
        #{isUsed,jdbcType=INTEGER},
      </if>
      <if test="operating != null">
        #{operating,jdbcType=VARCHAR},
      </if>
      <if test="operateUser != null">
        #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="isIn != null">
        #{isIn,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="yesLowValue != null">
        #{yesLowValue,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetCodeExample" resultType="java.lang.Long">
    select count(*) from stock_asset_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_asset_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.assetCode != null">
        asset_code = #{record.assetCode,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryCode != null">
        category_code = #{record.categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isUsed != null">
        is_used = #{record.isUsed,jdbcType=INTEGER},
      </if>
      <if test="record.operating != null">
        operating = #{record.operating,jdbcType=VARCHAR},
      </if>
      <if test="record.operateUser != null">
        operate_user = #{record.operateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.isIn != null">
        is_in = #{record.isIn,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.yesLowValue != null">
        yes_low_value = #{record.yesLowValue,jdbcType=INTEGER},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_asset_code
    set id = #{record.id,jdbcType=BIGINT},
      asset_code = #{record.assetCode,jdbcType=VARCHAR},
      category_code = #{record.categoryCode,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      is_used = #{record.isUsed,jdbcType=INTEGER},
      operating = #{record.operating,jdbcType=VARCHAR},
      operate_user = #{record.operateUser,jdbcType=VARCHAR},
      is_in = #{record.isIn,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      yes_low_value = #{record.yesLowValue,jdbcType=INTEGER},
      del_flag = #{record.delFlag,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetCode">
    update stock_asset_code
    <set>
      <if test="assetCode != null">
        asset_code = #{assetCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="isUsed != null">
        is_used = #{isUsed,jdbcType=INTEGER},
      </if>
      <if test="operating != null">
        operating = #{operating,jdbcType=VARCHAR},
      </if>
      <if test="operateUser != null">
        operate_user = #{operateUser,jdbcType=VARCHAR},
      </if>
      <if test="isIn != null">
        is_in = #{isIn,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="yesLowValue != null">
        yes_low_value = #{yesLowValue,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetCode">
    update stock_asset_code
    set asset_code = #{assetCode,jdbcType=VARCHAR},
      category_code = #{categoryCode,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      is_used = #{isUsed,jdbcType=INTEGER},
      operating = #{operating,jdbcType=VARCHAR},
      operate_user = #{operateUser,jdbcType=VARCHAR},
      is_in = #{isIn,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      yes_low_value = #{yesLowValue,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>