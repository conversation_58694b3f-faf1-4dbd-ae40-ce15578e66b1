<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockLicenseChangeMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockLicenseChange">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="change_no" jdbcType="VARCHAR" property="changeNo" />
    <result column="supplies_name" jdbcType="VARCHAR" property="suppliesName" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="change_time" jdbcType="TIMESTAMP" property="changeTime" />
    <result column="change_status" jdbcType="INTEGER" property="changeStatus" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, change_no, supplies_name, supplies_code, change_reason, change_time, change_status,
    created_by, created_at, updated_by, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockLicenseChangeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_license_change
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock_license_change
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_license_change
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockLicenseChange">
    insert into stock_license_change (id, change_no, supplies_name,
      supplies_code, change_reason, change_time,
      change_status, created_by, created_at,
      updated_by, updated_at)
    values (#{id,jdbcType=BIGINT}, #{changeNo,jdbcType=VARCHAR}, #{suppliesName,jdbcType=VARCHAR},
      #{suppliesCode,jdbcType=VARCHAR}, #{changeReason,jdbcType=VARCHAR}, #{changeTime,jdbcType=TIMESTAMP},
      #{changeStatus,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP},
      #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockLicenseChange">
    insert into stock_license_change
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="changeNo != null">
        change_no,
      </if>
      <if test="suppliesName != null">
        supplies_name,
      </if>
      <if test="suppliesCode != null">
        supplies_code,
      </if>
      <if test="changeReason != null">
        change_reason,
      </if>
      <if test="changeTime != null">
        change_time,
      </if>
      <if test="changeStatus != null">
        change_status,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="changeNo != null">
        #{changeNo,jdbcType=VARCHAR},
      </if>
      <if test="suppliesName != null">
        #{suppliesName,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="changeReason != null">
        #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="changeTime != null">
        #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeStatus != null">
        #{changeStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockLicenseChangeExample" resultType="java.lang.Long">
    select count(*) from stock_license_change
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_license_change
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.changeNo != null">
        change_no = #{record.changeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.suppliesName != null">
        supplies_name = #{record.suppliesName,jdbcType=VARCHAR},
      </if>
      <if test="record.suppliesCode != null">
        supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="record.changeReason != null">
        change_reason = #{record.changeReason,jdbcType=VARCHAR},
      </if>
      <if test="record.changeTime != null">
        change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.changeStatus != null">
        change_status = #{record.changeStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_license_change
    set id = #{record.id,jdbcType=BIGINT},
      change_no = #{record.changeNo,jdbcType=VARCHAR},
      supplies_name = #{record.suppliesName,jdbcType=VARCHAR},
      supplies_code = #{record.suppliesCode,jdbcType=VARCHAR},
      change_reason = #{record.changeReason,jdbcType=VARCHAR},
      change_time = #{record.changeTime,jdbcType=TIMESTAMP},
      change_status = #{record.changeStatus,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockLicenseChange">
    update stock_license_change
    <set>
      <if test="changeNo != null">
        change_no = #{changeNo,jdbcType=VARCHAR},
      </if>
      <if test="suppliesName != null">
        supplies_name = #{suppliesName,jdbcType=VARCHAR},
      </if>
      <if test="suppliesCode != null">
        supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      </if>
      <if test="changeReason != null">
        change_reason = #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="changeTime != null">
        change_time = #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeStatus != null">
        change_status = #{changeStatus,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockLicenseChange">
    update stock_license_change
    set change_no = #{changeNo,jdbcType=VARCHAR},
      supplies_name = #{suppliesName,jdbcType=VARCHAR},
      supplies_code = #{suppliesCode,jdbcType=VARCHAR},
      change_reason = #{changeReason,jdbcType=VARCHAR},
      change_time = #{changeTime,jdbcType=TIMESTAMP},
      change_status = #{changeStatus,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
