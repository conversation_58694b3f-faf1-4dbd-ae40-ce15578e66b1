<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsDemandLineMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsDemandLine">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="demand_no" jdbcType="VARCHAR" property="demandNo" />
    <result column="demand_item_no" jdbcType="VARCHAR" property="demandItemNo" />
    <result column="assets_category_code" jdbcType="VARCHAR" property="assetsCategoryCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="demand_quantity" jdbcType="DECIMAL" property="demandQuantity" />
    <result column="notice_quantity" jdbcType="DECIMAL" property="noticeQuantity" />
    <result column="receive_quantity" jdbcType="DECIMAL" property="receiveQuantity" />
    <result column="reject_quantity" jdbcType="DECIMAL" property="rejectQuantity" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="send_quantity" jdbcType="DECIMAL" property="sendQuantity" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, demand_no, demand_item_no, assets_category_code, status, demand_quantity, notice_quantity, 
    receive_quantity, reject_quantity, created_by, created_at, updated_by, updated_at, 
    del_flag, send_quantity
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandLineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_demand_line
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_demand_line
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_demand_line
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandLine">
    insert into stock_assets_demand_line (id, demand_no, demand_item_no, 
      assets_category_code, status, demand_quantity, 
      notice_quantity, receive_quantity, reject_quantity, 
      created_by, created_at, updated_by, 
      updated_at, del_flag, send_quantity
      )
    values (#{id,jdbcType=BIGINT}, #{demandNo,jdbcType=VARCHAR}, #{demandItemNo,jdbcType=VARCHAR}, 
      #{assetsCategoryCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{demandQuantity,jdbcType=DECIMAL}, 
      #{noticeQuantity,jdbcType=DECIMAL}, #{receiveQuantity,jdbcType=DECIMAL}, #{rejectQuantity,jdbcType=DECIMAL}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=INTEGER}, #{sendQuantity,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandLine">
    insert into stock_assets_demand_line
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="demandNo != null">
        demand_no,
      </if>
      <if test="demandItemNo != null">
        demand_item_no,
      </if>
      <if test="assetsCategoryCode != null">
        assets_category_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="demandQuantity != null">
        demand_quantity,
      </if>
      <if test="noticeQuantity != null">
        notice_quantity,
      </if>
      <if test="receiveQuantity != null">
        receive_quantity,
      </if>
      <if test="rejectQuantity != null">
        reject_quantity,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="sendQuantity != null">
        send_quantity,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="demandNo != null">
        #{demandNo,jdbcType=VARCHAR},
      </if>
      <if test="demandItemNo != null">
        #{demandItemNo,jdbcType=VARCHAR},
      </if>
      <if test="assetsCategoryCode != null">
        #{assetsCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="demandQuantity != null">
        #{demandQuantity,jdbcType=DECIMAL},
      </if>
      <if test="noticeQuantity != null">
        #{noticeQuantity,jdbcType=DECIMAL},
      </if>
      <if test="receiveQuantity != null">
        #{receiveQuantity,jdbcType=DECIMAL},
      </if>
      <if test="rejectQuantity != null">
        #{rejectQuantity,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="sendQuantity != null">
        #{sendQuantity,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandLineExample" resultType="java.lang.Long">
    select count(*) from stock_assets_demand_line
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_demand_line
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.demandNo != null">
        demand_no = #{record.demandNo,jdbcType=VARCHAR},
      </if>
      <if test="record.demandItemNo != null">
        demand_item_no = #{record.demandItemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsCategoryCode != null">
        assets_category_code = #{record.assetsCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.demandQuantity != null">
        demand_quantity = #{record.demandQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.noticeQuantity != null">
        notice_quantity = #{record.noticeQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.receiveQuantity != null">
        receive_quantity = #{record.receiveQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.rejectQuantity != null">
        reject_quantity = #{record.rejectQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=INTEGER},
      </if>
      <if test="record.sendQuantity != null">
        send_quantity = #{record.sendQuantity,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_demand_line
    set id = #{record.id,jdbcType=BIGINT},
      demand_no = #{record.demandNo,jdbcType=VARCHAR},
      demand_item_no = #{record.demandItemNo,jdbcType=VARCHAR},
      assets_category_code = #{record.assetsCategoryCode,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      demand_quantity = #{record.demandQuantity,jdbcType=DECIMAL},
      notice_quantity = #{record.noticeQuantity,jdbcType=DECIMAL},
      receive_quantity = #{record.receiveQuantity,jdbcType=DECIMAL},
      reject_quantity = #{record.rejectQuantity,jdbcType=DECIMAL},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      del_flag = #{record.delFlag,jdbcType=INTEGER},
      send_quantity = #{record.sendQuantity,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandLine">
    update stock_assets_demand_line
    <set>
      <if test="demandNo != null">
        demand_no = #{demandNo,jdbcType=VARCHAR},
      </if>
      <if test="demandItemNo != null">
        demand_item_no = #{demandItemNo,jdbcType=VARCHAR},
      </if>
      <if test="assetsCategoryCode != null">
        assets_category_code = #{assetsCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="demandQuantity != null">
        demand_quantity = #{demandQuantity,jdbcType=DECIMAL},
      </if>
      <if test="noticeQuantity != null">
        notice_quantity = #{noticeQuantity,jdbcType=DECIMAL},
      </if>
      <if test="receiveQuantity != null">
        receive_quantity = #{receiveQuantity,jdbcType=DECIMAL},
      </if>
      <if test="rejectQuantity != null">
        reject_quantity = #{rejectQuantity,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="sendQuantity != null">
        send_quantity = #{sendQuantity,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsDemandLine">
    update stock_assets_demand_line
    set demand_no = #{demandNo,jdbcType=VARCHAR},
      demand_item_no = #{demandItemNo,jdbcType=VARCHAR},
      assets_category_code = #{assetsCategoryCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      demand_quantity = #{demandQuantity,jdbcType=DECIMAL},
      notice_quantity = #{noticeQuantity,jdbcType=DECIMAL},
      receive_quantity = #{receiveQuantity,jdbcType=DECIMAL},
      reject_quantity = #{rejectQuantity,jdbcType=DECIMAL},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=INTEGER},
      send_quantity = #{sendQuantity,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>