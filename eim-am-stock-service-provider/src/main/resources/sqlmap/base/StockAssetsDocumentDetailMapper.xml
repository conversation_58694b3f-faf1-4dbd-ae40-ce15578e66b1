<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.base.StockAssetsDocumentDetailMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="document_no" jdbcType="VARCHAR" property="documentNo" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="old_assets_keeper" jdbcType="VARCHAR" property="oldAssetsKeeper" />
    <result column="new_assets_keeper" jdbcType="VARCHAR" property="newAssetsKeeper" />
    <result column="old_company_code" jdbcType="VARCHAR" property="oldCompanyCode" />
    <result column="new_company_code" jdbcType="VARCHAR" property="newCompanyCode" />
    <result column="old_dept_code" jdbcType="VARCHAR" property="oldDeptCode" />
    <result column="new_dept_code" jdbcType="VARCHAR" property="newDeptCode" />
    <result column="old_holder" jdbcType="VARCHAR" property="oldHolder" />
    <result column="new_holder" jdbcType="VARCHAR" property="newHolder" />
    <result column="old_holder_address" jdbcType="VARCHAR" property="oldHolderAddress" />
    <result column="new_holder_address" jdbcType="VARCHAR" property="newHolderAddress" />
    <result column="line_status" jdbcType="INTEGER" property="lineStatus" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    stock_assets_document_detail.id, stock_assets_document_detail.document_no, stock_assets_document_detail.assets_code, stock_assets_document_detail.old_assets_keeper, stock_assets_document_detail.new_assets_keeper, stock_assets_document_detail.old_company_code,
    stock_assets_document_detail.new_company_code, stock_assets_document_detail.old_dept_code, stock_assets_document_detail.new_dept_code, stock_assets_document_detail.old_holder, stock_assets_document_detail.new_holder, stock_assets_document_detail.old_holder_address,
    stock_assets_document_detail.new_holder_address, stock_assets_document_detail.line_status, stock_assets_document_detail.del_flag, stock_assets_document_detail.created_by, stock_assets_document_detail.created_at, stock_assets_document_detail.updated_by, stock_assets_document_detail.updated_at
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from stock_assets_document_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_assets_document_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByStockAssetsDocumentLineReq" parameterType="com.gz.eim.am.stock.dto.response.assets.StockAssetsDocumentLineRespDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock_assets_document_detail inner join stock_assets_document on stock_assets_document_detail.document_no = stock_assets_document.document_no
    <where>
      and stock_assets_document.head_status = #{headStatus,jdbcType=INTEGER}
      <if test="oldHolder != null and oldHolder != ''">
        and stock_assets_document_detail.old_holder = #{oldHolder,jdbcType=VARCHAR}
      </if>
      <if test="assetsCode != null and assetsCode != ''">
        and stock_assets_document_detail.assets_code = #{assetsCode,jdbcType=VARCHAR}
      </if>
      <if test="null != assetsCodeList and assetsCodeList.size > 0">
        and stock_assets_document_detail.assets_code in
        <foreach collection="assetsCodeList" item="assetsCode" open="(" separator="," close=")">
          #{assetsCode,jdbcType=VARCHAR}
        </foreach>
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from stock_assets_document_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_document_detail (document_no, assets_code, old_assets_keeper, 
      new_assets_keeper, old_company_code, new_company_code, 
      old_dept_code, new_dept_code, old_holder, 
      new_holder, old_holder_address, new_holder_address, 
      line_status, del_flag, created_by, 
      created_at, updated_by, updated_at
      )
    values (#{documentNo,jdbcType=VARCHAR}, #{assetsCode,jdbcType=VARCHAR}, #{oldAssetsKeeper,jdbcType=VARCHAR}, 
      #{newAssetsKeeper,jdbcType=VARCHAR}, #{oldCompanyCode,jdbcType=VARCHAR}, #{newCompanyCode,jdbcType=VARCHAR}, 
      #{oldDeptCode,jdbcType=VARCHAR}, #{newDeptCode,jdbcType=VARCHAR}, #{oldHolder,jdbcType=VARCHAR}, 
      #{newHolder,jdbcType=VARCHAR}, #{oldHolderAddress,jdbcType=VARCHAR}, #{newHolderAddress,jdbcType=VARCHAR}, 
      #{lineStatus,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_assets_document_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="documentNo != null">
        document_no,
      </if>
      <if test="assetsCode != null">
        assets_code,
      </if>
      <if test="oldAssetsKeeper != null">
        old_assets_keeper,
      </if>
      <if test="newAssetsKeeper != null">
        new_assets_keeper,
      </if>
      <if test="oldCompanyCode != null">
        old_company_code,
      </if>
      <if test="newCompanyCode != null">
        new_company_code,
      </if>
      <if test="oldDeptCode != null">
        old_dept_code,
      </if>
      <if test="newDeptCode != null">
        new_dept_code,
      </if>
      <if test="oldHolder != null">
        old_holder,
      </if>
      <if test="newHolder != null">
        new_holder,
      </if>
      <if test="oldHolderAddress != null">
        old_holder_address,
      </if>
      <if test="newHolderAddress != null">
        new_holder_address,
      </if>
      <if test="lineStatus != null">
        line_status,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="documentNo != null">
        #{documentNo,jdbcType=VARCHAR},
      </if>
      <if test="assetsCode != null">
        #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="oldAssetsKeeper != null">
        #{oldAssetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="newAssetsKeeper != null">
        #{newAssetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="oldCompanyCode != null">
        #{oldCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="newCompanyCode != null">
        #{newCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="oldDeptCode != null">
        #{oldDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="newDeptCode != null">
        #{newDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="oldHolder != null">
        #{oldHolder,jdbcType=VARCHAR},
      </if>
      <if test="newHolder != null">
        #{newHolder,jdbcType=VARCHAR},
      </if>
      <if test="oldHolderAddress != null">
        #{oldHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="newHolderAddress != null">
        #{newHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="lineStatus != null">
        #{lineStatus,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetailExample" resultType="java.lang.Long">
    select count(*) from stock_assets_document_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update stock_assets_document_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.documentNo != null">
        document_no = #{record.documentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.assetsCode != null">
        assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.oldAssetsKeeper != null">
        old_assets_keeper = #{record.oldAssetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="record.newAssetsKeeper != null">
        new_assets_keeper = #{record.newAssetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="record.oldCompanyCode != null">
        old_company_code = #{record.oldCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.newCompanyCode != null">
        new_company_code = #{record.newCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.oldDeptCode != null">
        old_dept_code = #{record.oldDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.newDeptCode != null">
        new_dept_code = #{record.newDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.oldHolder != null">
        old_holder = #{record.oldHolder,jdbcType=VARCHAR},
      </if>
      <if test="record.newHolder != null">
        new_holder = #{record.newHolder,jdbcType=VARCHAR},
      </if>
      <if test="record.oldHolderAddress != null">
        old_holder_address = #{record.oldHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.newHolderAddress != null">
        new_holder_address = #{record.newHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.lineStatus != null">
        line_status = #{record.lineStatus,jdbcType=INTEGER},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update stock_assets_document_detail
    set id = #{record.id,jdbcType=BIGINT},
      document_no = #{record.documentNo,jdbcType=VARCHAR},
      assets_code = #{record.assetsCode,jdbcType=VARCHAR},
      old_assets_keeper = #{record.oldAssetsKeeper,jdbcType=VARCHAR},
      new_assets_keeper = #{record.newAssetsKeeper,jdbcType=VARCHAR},
      old_company_code = #{record.oldCompanyCode,jdbcType=VARCHAR},
      new_company_code = #{record.newCompanyCode,jdbcType=VARCHAR},
      old_dept_code = #{record.oldDeptCode,jdbcType=VARCHAR},
      new_dept_code = #{record.newDeptCode,jdbcType=VARCHAR},
      old_holder = #{record.oldHolder,jdbcType=VARCHAR},
      new_holder = #{record.newHolder,jdbcType=VARCHAR},
      old_holder_address = #{record.oldHolderAddress,jdbcType=VARCHAR},
      new_holder_address = #{record.newHolderAddress,jdbcType=VARCHAR},
      line_status = #{record.lineStatus,jdbcType=INTEGER},
      del_flag = #{record.delFlag,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail">
    update stock_assets_document_detail
    <set>
      <if test="documentNo != null">
        document_no = #{documentNo,jdbcType=VARCHAR},
      </if>
      <if test="assetsCode != null">
        assets_code = #{assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="oldAssetsKeeper != null">
        old_assets_keeper = #{oldAssetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="newAssetsKeeper != null">
        new_assets_keeper = #{newAssetsKeeper,jdbcType=VARCHAR},
      </if>
      <if test="oldCompanyCode != null">
        old_company_code = #{oldCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="newCompanyCode != null">
        new_company_code = #{newCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="oldDeptCode != null">
        old_dept_code = #{oldDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="newDeptCode != null">
        new_dept_code = #{newDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="oldHolder != null">
        old_holder = #{oldHolder,jdbcType=VARCHAR},
      </if>
      <if test="newHolder != null">
        new_holder = #{newHolder,jdbcType=VARCHAR},
      </if>
      <if test="oldHolderAddress != null">
        old_holder_address = #{oldHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="newHolderAddress != null">
        new_holder_address = #{newHolderAddress,jdbcType=VARCHAR},
      </if>
      <if test="lineStatus != null">
        line_status = #{lineStatus,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail">
    update stock_assets_document_detail
    set document_no = #{documentNo,jdbcType=VARCHAR},
      assets_code = #{assetsCode,jdbcType=VARCHAR},
      old_assets_keeper = #{oldAssetsKeeper,jdbcType=VARCHAR},
      new_assets_keeper = #{newAssetsKeeper,jdbcType=VARCHAR},
      old_company_code = #{oldCompanyCode,jdbcType=VARCHAR},
      new_company_code = #{newCompanyCode,jdbcType=VARCHAR},
      old_dept_code = #{oldDeptCode,jdbcType=VARCHAR},
      new_dept_code = #{newDeptCode,jdbcType=VARCHAR},
      old_holder = #{oldHolder,jdbcType=VARCHAR},
      new_holder = #{newHolder,jdbcType=VARCHAR},
      old_holder_address = #{oldHolderAddress,jdbcType=VARCHAR},
      new_holder_address = #{newHolderAddress,jdbcType=VARCHAR},
      line_status = #{lineStatus,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>