<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.authority.StockRoleKeeperExtendMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockRoleKeeper">
        <id column="role_keeper_id" jdbcType="BIGINT" property="roleKeeperId" />
        <result column="keeper_code" jdbcType="VARCHAR" property="keeperCode" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="contact_way" jdbcType="VARCHAR" property="contactWay" />
        <result column="role_id" jdbcType="BIGINT" property="roleId" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>


    <sql id="Base_Column_List">
        role_keeper_id, keeper_code, dept_id, contact_way, role_id, status, del_flag, created_by,
        created_at, updated_by, updated_at
    </sql>

    <select id="selectExistDeptRole"  resultType="String">
        select a.dept_id from stock_role_keeper a INNER JOIN ambase.sys_user b on
        a.dept_id=b.dept_id where a.status=1 and a.del_flag=0 and b.emp_id=#{empId,jdbcType=VARCHAR} and a.role_id=#{roleId,jdbcType=BIGINT}
        and (a.keeper_code is null or a.keeper_code = '')
    </select>

    <select id="selectExistEmpIdRoleList" parameterType="com.gz.eim.am.stock.entity.vo.StockRoleKeeperImportExcel" resultType="com.gz.eim.am.stock.entity.vo.StockRoleKeeperImportExcel">
        select a.keeper_code as empid,b.role_name as rolename from stock_role_keeper a inner JOIN stock_manage_role b on a.role_id=b.role_id
        where a.status=1 and a.del_flag=0
        <foreach collection="list" item="item" index="index" open=" and (" close=")"  separator="or">
            (a.keeper_code= #{item.empId,jdbcType=VARCHAR} and b.role_name= #{item.roleName,jdbcType=VARCHAR})
        </foreach>
    </select>

    <select id="selectExistDeptRoleList" parameterType="com.gz.eim.am.stock.entity.vo.StockRoleKeeperImportExcel" resultType="com.gz.eim.am.stock.entity.vo.StockRoleKeeperImportExcel">
        SELECT c.emp_id as empid,	b.role_name AS rolename
        FROM	stock_role_keeper a INNER JOIN stock_manage_role b ON a.role_id = b.role_id INNER JOIN ambase.sys_user c on a.dept_id=c.dept_id
        WHERE	a.STATUS = 1 AND a.del_flag = 0 and a.keeper_code=''
        <foreach collection="list" item="item" index="index" open=" and (" close=")"  separator="or">
            (c.emp_id= #{item.empId,jdbcType=VARCHAR} and b.role_name= #{item.roleName,jdbcType=VARCHAR})
        </foreach>
    </select>

    <insert id="batchInsertStockRoleKeeper" parameterType="com.gz.eim.am.stock.entity.StockRoleKeeper"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into stock_role_keeper (role_keeper_id, keeper_code, dept_id,
        contact_way, role_id, status,
        del_flag, created_by, created_at,
        updated_by, updated_at) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleKeeperId,jdbcType=BIGINT}, #{item.keeperCode,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR},
            #{item.contactWay,jdbcType=VARCHAR}, #{item.roleId,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER},
            #{item.delFlag,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>