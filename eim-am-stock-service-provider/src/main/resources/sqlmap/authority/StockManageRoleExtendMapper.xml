<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.authority.StockManageRoleExtendMapper">

    <insert id="batchInsertStockManageRole" parameterType="com.gz.eim.am.stock.entity.StockRoleKeeper"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into
        stock_manage_role (role_name, role_type,
        warehouse_code, start_date, end_date,
        status, del_flag, created_by,
        created_at, updated_by, updated_at
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.roleName,jdbcType=VARCHAR}, #{item.roleType,jdbcType=INTEGER},
            #{item.warehouseCode,jdbcType=VARCHAR}, #{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP},
            #{item.status,jdbcType=INTEGER}, #{item.delFlag,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
