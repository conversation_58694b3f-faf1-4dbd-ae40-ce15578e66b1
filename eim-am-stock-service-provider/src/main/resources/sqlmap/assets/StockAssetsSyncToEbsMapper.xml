<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.StockAssetsSyncToEbsMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsEbsSync">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
        <result column="query_code" jdbcType="VARCHAR" property="queryCode" />
        <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
        <result column="SYS_CODE" jdbcType="VARCHAR" property="sysCode" />
        <result column="ASSET_CATEGORY_SEGMENT1" jdbcType="VARCHAR" property="assetCategorySegment1" />
        <result column="ASSET_CATEGORY_SEGMENT2" jdbcType="VARCHAR" property="assetCategorySegment2" />
        <result column="ASSET_KEY_SEGMENT1" jdbcType="VARCHAR" property="assetKeySegment1" />
        <result column="ASSET_KEY_SEGMENT2" jdbcType="VARCHAR" property="assetKeySegment2" />
        <result column="ASSET_KEY_SEGMENT3" jdbcType="VARCHAR" property="assetKeySegment3" />
        <result column="ASSET_TYPE" jdbcType="VARCHAR" property="assetType" />
        <result column="BOOK_TYPE_CODE" jdbcType="VARCHAR" property="bookTypeCode" />
        <result column="DATE_PLACED_IN_SERVICE" jdbcType="VARCHAR" property="datePlacedInService" />
        <result column="DEPRECIATE_FLAG" jdbcType="VARCHAR" property="depreciateFlag" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="DEPRN_METHOD_CODE" jdbcType="VARCHAR" property="deprnMethodCode" />
        <result column="FIXED_ASSETS_COST" jdbcType="DECIMAL" property="fixedAssetsCost" />
        <result column="FIXED_ASSETS_UNITS" jdbcType="INTEGER" property="fixedAssetsUnits" />
        <result column="INVOICE_NUMBER" jdbcType="VARCHAR" property="invoiceNumber" />
        <result column="LOC_SEGMENT1" jdbcType="VARCHAR" property="locSegment1" />
        <result column="LOC_SEGMENT2" jdbcType="VARCHAR" property="locSegment2" />
        <result column="LOC_SEGMENT3" jdbcType="VARCHAR" property="locSegment3" />
        <result column="LIFE_IN_MONTHS" jdbcType="INTEGER" property="lifeInMonths" />
        <result column="MODEL_NUMBER" jdbcType="VARCHAR" property="modelNumber" />
        <result column="QUEUE_NAME" jdbcType="VARCHAR" property="queueName" />
        <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
        <result column="ACC_SEGMENT1" jdbcType="VARCHAR" property="accSegment1" />
        <result column="ACC_SEGMENT2" jdbcType="VARCHAR" property="accSegment2" />
        <result column="ACC_SEGMENT3" jdbcType="VARCHAR" property="accSegment3" />
        <result column="ACC_SEGMENT4" jdbcType="VARCHAR" property="accSegment4" />
        <result column="ACC_SEGMENT5" jdbcType="VARCHAR" property="accSegment5" />
        <result column="ACC_SEGMENT6" jdbcType="VARCHAR" property="accSegment6" />
        <result column="ACC_SEGMENT7" jdbcType="VARCHAR" property="accSegment7" />
        <result column="ACC_SEGMENT8" jdbcType="VARCHAR" property="accSegment8" />
        <result column="ACC_SEGMENT9" jdbcType="VARCHAR" property="accSegment9" />
        <result column="ACC_SEGMENT10" jdbcType="VARCHAR" property="accSegment10" />
        <result column="ACC_SEGMENT11" jdbcType="VARCHAR" property="accSegment11" />
        <result column="TAG_NUMBER" jdbcType="VARCHAR" property="tagNumber" />
        <result column="FA_CODE" jdbcType="VARCHAR" property="faCode" />
        <result column="ASSET_ID" jdbcType="INTEGER" property="assetId" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, assets_code, batch_no, query_code, sync_status, error_message, SYS_CODE, ASSET_CATEGORY_SEGMENT1,
        ASSET_CATEGORY_SEGMENT2, ASSET_KEY_SEGMENT1, ASSET_KEY_SEGMENT2, ASSET_KEY_SEGMENT3,
        ASSET_TYPE, BOOK_TYPE_CODE, DATE_PLACED_IN_SERVICE, DEPRECIATE_FLAG, DESCRIPTION,
        DEPRN_METHOD_CODE, FIXED_ASSETS_COST, FIXED_ASSETS_UNITS, INVOICE_NUMBER, LOC_SEGMENT1,
        LOC_SEGMENT2, LOC_SEGMENT3, LIFE_IN_MONTHS, MODEL_NUMBER, QUEUE_NAME, SERIAL_NUMBER,
        ACC_SEGMENT1, ACC_SEGMENT2, ACC_SEGMENT3, ACC_SEGMENT4, ACC_SEGMENT5, ACC_SEGMENT6,
        ACC_SEGMENT7, ACC_SEGMENT8, ACC_SEGMENT9, ACC_SEGMENT10, ACC_SEGMENT11, TAG_NUMBER,
        FA_CODE, ASSET_ID, created_by, created_at, updated_by, updated_at
    </sql>


    <select id="extractAssets" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSync">
        SELECT
        a.assets_code as assetsCode,
        '电子表格' as  sysCode,
        (select d.default_cost_item_code from stock_supplies_purchase d where d.supplies_code=a.supplies_code and d.warehouse_type_code=c.type) as assetCategorySegment1,
        '0' as assetCategorySegment2,
        '购买' as assetKeySegment1,
        (select d.default_cost_item_code from stock_supplies_purchase d where d.supplies_code=a.supplies_code and d.warehouse_type_code=c.type) as assetKeySegment2,
        (select d.category_code from stock_assets_category d where d.category_name=a.category) as assetKeySegment3,
        '资本化' as assetType,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=a.cost_dept) as bookTypeCode,
        DATE_FORMAT(a.pay_date, '%Y-%m-%d') as datePlacedInService,
        'Y' as depreciateFlag,
        a.assets_name as description,
        'STL' as deprnMethodCode,
        a.Price_excluding_tax as fixedAssetsCost,
        1 as fixedAssetsUnits,
        a.bill_code as invoiceNumber,
        a.company_code as locSegment1,
        a.cost_dept as locSegment2,
        left(c.name,10) as locSegment3,
        (select d.Default_used_time from stock_assets_category d where d.category_name=a.category) as lifeInMonths,
        a.model as modelNumber,
        '过账' as queueName,
        a.company_code as accSegment1,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=a.cost_dept) as accSegment2,
        a.cost_dept as accSegment3,
        '0' as accSegment4,
        '0' as accSegment5,
        '0' as accSegment6,
        '0' as accSegment7,
        '0' as accSegment8,
        '0' as accSegment9,
        '0' as accSegment10,
        '0' as accSegment11,
        a.assets_code as tagNumber
        FROM
        stock_assets a
        <!--INNER JOIN stock_inventory_in_plan_heads b ON a.Purchase_order_no = b.Purchase_order_no
        INNER JOIN stock_warehouse c ON b.in_warehouse_code = c.CODE-->
        INNER JOIN stock_inventory_in b ON a.purchase_no = b.inventory_in_no
        INNER JOIN stock_warehouse c ON b.in_warehouse_code = c.CODE
        WHERE
        a.Extract_status = 1
        AND c.type = 2
        <!--AND a.Purchase_order_no IS NOT NULL-->
        limit #{limitNum}
    </select>


    <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSync">
        insert into stock_assets_ebs_sync (assets_code, batch_no, query_code,
        sync_status, error_message, extract_end_date,
        extract_start_date, billing_time, billing_user,
        business_line_no, business_no, business_type,
        SYS_CODE, ASSET_CATEGORY_SEGMENT1, ASSET_CATEGORY_SEGMENT2,
        ASSET_KEY_SEGMENT1, ASSET_KEY_SEGMENT2, ASSET_KEY_SEGMENT3,
        ASSET_TYPE, BOOK_TYPE_CODE, DATE_PLACED_IN_SERVICE,
        DEPRECIATE_FLAG, DESCRIPTION, DEPRN_METHOD_CODE,
        FIXED_ASSETS_COST, FIXED_ASSETS_UNITS, INVOICE_NUMBER,
        LOC_SEGMENT1, LOC_SEGMENT2, LOC_SEGMENT3,
        LIFE_IN_MONTHS, MODEL_NUMBER, QUEUE_NAME,
        SERIAL_NUMBER, ACC_SEGMENT1, ACC_SEGMENT2,
        ACC_SEGMENT3, ACC_SEGMENT4, ACC_SEGMENT5,
        ACC_SEGMENT6, ACC_SEGMENT7, ACC_SEGMENT8,
        ACC_SEGMENT9, ACC_SEGMENT10, ACC_SEGMENT11,
        version_id, TAG_NUMBER, FA_CODE,
        ASSET_ID, created_by, created_at,
        updated_by, updated_at)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.assetsCode,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.queryCode,jdbcType=VARCHAR},
            #{item.syncStatus,jdbcType=TINYINT}, #{item.errorMessage,jdbcType=VARCHAR}, #{item.extractEndDate,jdbcType=TIMESTAMP},
            #{item.extractStartDate,jdbcType=TIMESTAMP}, #{item.billingTime,jdbcType=VARCHAR}, #{item.billingUser,jdbcType=VARCHAR},
            #{item.businessLineNo,jdbcType=VARCHAR}, #{item.businessNo,jdbcType=VARCHAR}, #{item.businessType,jdbcType=INTEGER},
            #{item.sysCode,jdbcType=VARCHAR}, #{item.assetCategorySegment1,jdbcType=VARCHAR}, #{item.assetCategorySegment2,jdbcType=VARCHAR},
            #{item.assetKeySegment1,jdbcType=VARCHAR}, #{item.assetKeySegment2,jdbcType=VARCHAR}, #{item.assetKeySegment3,jdbcType=VARCHAR},
            #{item.assetType,jdbcType=VARCHAR}, #{item.bookTypeCode,jdbcType=VARCHAR}, #{item.datePlacedInService,jdbcType=VARCHAR},
            #{item.depreciateFlag,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.deprnMethodCode,jdbcType=VARCHAR},
            #{item.fixedAssetsCost,jdbcType=DECIMAL}, #{item.fixedAssetsUnits,jdbcType=INTEGER}, #{item.invoiceNumber,jdbcType=VARCHAR},
            #{item.locSegment1,jdbcType=VARCHAR}, #{item.locSegment2,jdbcType=VARCHAR}, #{item.locSegment3,jdbcType=VARCHAR},
            #{item.lifeInMonths,jdbcType=INTEGER}, #{item.modelNumber,jdbcType=VARCHAR}, #{item.queueName,jdbcType=VARCHAR},
            #{item.serialNumber,jdbcType=VARCHAR}, #{item.accSegment1,jdbcType=VARCHAR}, #{item.accSegment2,jdbcType=VARCHAR},
            #{item.accSegment3,jdbcType=VARCHAR}, #{item.accSegment4,jdbcType=VARCHAR}, #{item.accSegment5,jdbcType=VARCHAR},
            #{item.accSegment6,jdbcType=VARCHAR}, #{item.accSegment7,jdbcType=VARCHAR}, #{item.accSegment8,jdbcType=VARCHAR},
            #{item.accSegment9,jdbcType=VARCHAR}, #{item.accSegment10,jdbcType=VARCHAR}, #{item.accSegment11,jdbcType=VARCHAR},
            #{item.versionId,jdbcType=INTEGER}, #{item.tagNumber,jdbcType=VARCHAR}, #{item.faCode,jdbcType=VARCHAR},
            #{item.assetId,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="getQueryCodeList" resultType="java.lang.String">
        select distinct query_code from stock_assets_ebs_sync where sync_status in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <!-- 部分字段不支持修改、条件可以资产主键或者资产编码 -->
    <update id="updateMultipleSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSync">
        <foreach collection="list" item="item" index="index" separator=";">
            update stock_assets_ebs_sync
            <set>
                <if test="item.bookTypeCode != null">
                    BOOK_TYPE_CODE = #{item.bookTypeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.faCode != null">
                    FA_CODE = #{item.faCode,jdbcType=VARCHAR},
                </if>
                <if test="item.syncStatus != null">
                    sync_status = #{item.syncStatus,jdbcType=TINYINT},
                </if>
                <if test="item.errorMessage != null">
                    ERROR_MESSAGE = #{item.errorMessage,jdbcType=VARCHAR},
                </if>
                <if test="item.assetId != null">
                    ASSET_ID = #{item.assetId,jdbcType=VARCHAR},
                </if>
                <if test="item.updatedAt != null">
                    updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
                </if>
            </set>
            <where>
                query_code = #{item.queryCode} and assets_code = #{item.assetsCode} and business_no = #{item.businessNo}
            </where>
        </foreach>
    </update>

    <select id="extractNewAssets" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSync">
        SELECT
        sii.inventory_in_type as businessType,
        sii.inventory_in_no as businessNo,
        siis.in_supplies_id as businessLineNo,
        sii.billing_user as billingUser,
        DATE_FORMAT(sii.CREATED_AT, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        '电子表格' as  sysCode,
        <!--(select d.default_cost_item_code from stock_supplies_purchase d where d.supplies_code=a.supplies_code and d.warehouse_type_code=c.type) as assetCategorySegment1,-->
        ifnull(sac.parent_category_code,'') as assetCategorySegment1,
        '0' as assetCategorySegment2,
        '购买' as assetKeySegment1,
        <!--(select d.default_cost_item_code from stock_supplies_purchase d where d.supplies_code=a.supplies_code and d.warehouse_type_code=c.type) as assetKeySegment2,-->
        ifnull(sac.parent_category_code,'') as assetKeySegment2,
        sac.category_code as assetKeySegment3,
        '资本化' as assetType,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=c.cost_center_code) as bookTypeCode,
        DATE_FORMAT(a.start_using_time, '%Y-%m-%d %H:%i:%s') as datePlacedInService,
        'Y' as depreciateFlag,
        a.assets_name as description,
        'STL' as deprnMethodCode,
        a.Price_excluding_tax as fixedAssetsCost,
        1 as fixedAssetsUnits,
        a.bill_code as invoiceNumber,
        a.company_code as locSegment1,
        c.cost_center_code as locSegment2,
        left(swb.address,10) as locSegment3,
        sac.Default_used_time as lifeInMonths,
        a.model as modelNumber,
        '过账' as queueName,
        a.company_code as accSegment1,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=c.cost_center_code) as accSegment2,
        c.cost_center_code as accSegment3,
        '0' as accSegment4,
        '0' as accSegment5,
        '0' as accSegment6,
        '0' as accSegment7,
        '0' as accSegment8,
        '0' as accSegment9,
        '0' as accSegment10,
        '0' as accSegment11,
        <!-- 第一次报送ebs失败后，修正数据，版本号+1，再重新报送 -->
        1 as version_id,
        a.assets_code as tagNumber
        FROM
        stock_assets a
        inner join stock_inventory_in_supplies_assets siisa on a.assets_code=siisa.assets_code
        inner join stock_inventory_in_supplies siis on siisa.in_supplies_id=siis.in_supplies_id
        inner join stock_inventory_in sii on sii.inventory_in_id=siis.inventory_in_id
        inner join stock_assets_category sac on a.category=sac.category_name
        INNER JOIN stock_warehouse c ON sii.in_warehouse_code = c.CODE
        inner join stock_warehouse_base swb on c.code=swb.warehouse_code
        WHERE
        not exists (select 1 from stock_assets_ebs_sync saes where saes.business_no=sii.inventory_in_no)
        <!-- 8资产采购入库单  16资产盘盈入库单 -->
        and sii.inventory_in_type in (8,16)
        and sii.created_at > #{startDate}
        and sac.sync_ebs_flag=1
        and sac.lowest_amount &lt;= a.unit_price
        <!-- 将不需要同步的资产初始化为1，新增资产默认值为0 -->
        and a.Extract_status=0
        and c.type = 2
    </select>

</mapper>