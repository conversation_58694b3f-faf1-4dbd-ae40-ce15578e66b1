<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.AssetsScrapLineMapper">

    <insert id="batchInsertScrapLines" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapLine">
        insert into stock_assets_scrap_line (head_id, scrap_no, assets_code,
        assets_name, sn_code, category,
        assets_keeper, warehouse_code, holder,
        assets_warehouse_address, assets_status, need_dept,
        assets_cost, assets_last_cost, assets_life,
        assets_surplus_life, scrap_reason, scrap_flag,
        created_by, created_at, updated_by,
        updated_at)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.headId,jdbcType=BIGINT}, #{item.scrapNo,jdbcType=VARCHAR}, #{item.assetsCode,jdbcType=VARCHAR},
            #{item.assetsName,jdbcType=VARCHAR}, #{item.snCode,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR},
            #{item.assetsKeeper,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR}, #{item.holder,jdbcType=VARCHAR},
            #{item.assetsWarehouseAddress,jdbcType=VARCHAR}, #{item.assetsStatus,jdbcType=TINYINT}, #{item.needDept,jdbcType=VARCHAR},
            #{item.assetsCost,jdbcType=DECIMAL}, #{item.assetsLastCost,jdbcType=DECIMAL}, #{item.assetsLife,jdbcType=INTEGER},
            #{item.assetsSurplusLife,jdbcType=INTEGER}, #{item.scrapReason,jdbcType=TINYINT}, #{item.scrapFlag,jdbcType=TINYINT},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>


    <select id="queryAssetsCodesByHeadId" parameterType="java.lang.Long"
            resultType="java.lang.String">
        select assets_code from stock_assets_scrap_line where head_id = #{headId}
    </select>

    <select id="queryLinesByHeadId" parameterType="java.lang.Long"
            resultType="com.gz.eim.am.stock.entity.StockAssetsScrapLine">
        select * from stock_assets_scrap_line where head_id = #{headId}
    </select>
    <update id="batchUpdateScrapLines" parameterType="com.gz.eim.am.stock.entity.StockAssetsScrapLine">
        <foreach collection="list" item="item" separator=";">
        update stock_assets_scrap_line
        <set>
            <if test="item.headId != null">
                head_id = #{item.headId,jdbcType=BIGINT},
            </if>
            <if test="item.scrapNo != null">
                scrap_no = #{item.scrapNo,jdbcType=VARCHAR},
            </if>
            <if test="item.assetsCode != null">
                assets_code = #{item.assetsCode,jdbcType=VARCHAR},
            </if>
            <if test="item.assetsName != null">
                assets_name = #{item.assetsName,jdbcType=VARCHAR},
            </if>
            <if test="item.snCode != null">
                sn_code = #{item.snCode,jdbcType=VARCHAR},
            </if>
            <if test="item.costDept != null and item.costDept != ''">
                cost_dept = #{item.costDept,jdbcType=VARCHAR},
            </if>
            <if test="item.category != null">
                category = #{item.category,jdbcType=VARCHAR},
            </if>
            <if test="item.assetsKeeper != null">
                assets_keeper = #{item.assetsKeeper,jdbcType=VARCHAR},
            </if>
            <if test="item.warehouseCode != null">
                warehouse_code = #{item.warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="item.holder != null">
                holder = #{item.holder,jdbcType=VARCHAR},
            </if>
            <if test="item.assetsWarehouseAddress != null">
                assets_warehouse_address = #{item.assetsWarehouseAddress,jdbcType=VARCHAR},
            </if>
            <if test="item.assetsStatus != null">
                assets_status = #{item.assetsStatus,jdbcType=TINYINT},
            </if>
            <if test="item.needDept != null">
                need_dept = #{item.needDept,jdbcType=VARCHAR},
            </if>
            <if test="item.assetsCost != null">
                assets_cost = #{item.assetsCost,jdbcType=DECIMAL},
            </if>
            <if test="item.assetsLastCost != null">
                assets_last_cost = #{item.assetsLastCost,jdbcType=DECIMAL},
            </if>
            <if test="item.assetsLife != null">
                assets_life = #{item.assetsLife,jdbcType=INTEGER},
            </if>
            <if test="item.assetsSurplusLife != null">
                assets_surplus_life = #{item.assetsSurplusLife,jdbcType=INTEGER},
            </if>
            <if test="item.scrapReason != null">
                scrap_reason = #{item.scrapReason,jdbcType=TINYINT},
            </if>
            <if test="item.scrapFlag != null">
                scrap_flag = #{item.scrapFlag,jdbcType=TINYINT},
            </if>
            <if test="item.createdBy != null">
                created_by = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdAt != null">
                created_at = #{item.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.updatedBy != null">
                updated_by = #{item.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.updatedAt != null">
                updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
            </if>
        </set>
        where line_id = #{item.lineId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="queryLinesByAssetsCodeList" resultType="com.gz.eim.am.stock.entity.StockAssetsScrapLine">
        select * from stock_assets_scrap_line asl
        join stock_assets_scrap_head ash on asl.head_id = ash.head_id
        where ash.scrap_status in (1,2)
        <if test="assetsCodeList != null and assetsCodeList.size()>0">
            and asl.assets_code in
            <foreach collection="assetsCodeList" item="item" index="index" open=" (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
