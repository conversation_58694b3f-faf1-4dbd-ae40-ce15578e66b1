<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.AssetsDocumentExtendMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="document_no" jdbcType="VARCHAR" property="documentNo"/>
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode"/>
        <result column="old_assets_keeper" jdbcType="VARCHAR" property="oldAssetsKeeper"/>
        <result column="new_assets_keeper" jdbcType="VARCHAR" property="newAssetsKeeper"/>
        <result column="line_status" jdbcType="INTEGER" property="lineStatus"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , document_no, assets_code, old_assets_keeper, new_assets_keeper, line_status,
        del_flag, created_by, created_at, updated_by, updated_at
    </sql>

    <insert id="batchInsertStockAssetsDocumentDetail"
            parameterType="com.gz.eim.am.stock.entity.StockAssetsDocumentDetail"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into stock_assets_document_detail (document_no, assets_code, old_assets_keeper,
        new_assets_keeper, line_status, del_flag,
        created_by, created_at, updated_by,
        updated_at, old_holder, new_holder, old_dept_code, new_dept_code,old_holder_address,new_holder_address,old_company_code,new_company_code) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.documentNo,jdbcType=VARCHAR}, #{item.assetsCode,jdbcType=VARCHAR},
            #{item.oldAssetsKeeper,jdbcType=VARCHAR},
            #{item.newAssetsKeeper,jdbcType=VARCHAR}, #{item.lineStatus,jdbcType=INTEGER},
            #{item.delFlag,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedAt,jdbcType=TIMESTAMP},
            #{item.oldHolder,jdbcType=VARCHAR},
            #{item.newHolder,jdbcType=VARCHAR},
            #{item.oldDeptCode,jdbcType=VARCHAR},
            #{item.newDeptCode,jdbcType=VARCHAR},
            #{item.oldHolderAddress,jdbcType=VARCHAR},
            #{item.newHolderAddress,jdbcType=VARCHAR},
            #{item.oldCompanyCode,jdbcType=VARCHAR},
            #{item.newCompanyCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="selectAssetsDocumentDetailByDocumentNo" parameterType="String" resultType="com.gz.eim.am.stock.dto.response.assets.StockAssetsDocumentLineRespDTO">
        select b.assets_code as assetscode, b.sn_code as snCode, b.supplies_code as suppliesCode, b.assets_name as assetsName,b.status as assetStatus,
        (select c.dept_name from ambase.sys_dept c where c.dept_id=b.need_dept) as needDept,
        a.old_assets_keeper as oldAssetsKeeper,
        a.old_holder as oldHolder,
        b.holder as holder,
        b.holder_time as holderTime,
        a.new_assets_keeper as newAssetsKeeper,
        a.line_status as transferStatus,
        su.name as suppliesName
        from stock_assets_document_detail a
        inner join stock_assets b on a.assets_code=b.assets_code
        inner join stock_supplies su on b.supplies_code = su.code
        where a.document_no = #{documentNo,jdbcType=VARCHAR}
    </select>
    <update id="batchUpdateAssetKeeperByDocumentNo">
        update stock_assets a
        set a.assets_keeper=(select b.new_assets_keeper
                             from stock_assets_document_detail b
                             where a.assets_code = b.assets_code
                               and b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1),
            a.updated_by=(
        select b.created_by
        from stock_assets_document_detail b
        where a.assets_code=b.assets_code
          and b.document_no=#{documentNo,jdbcType=VARCHAR} limit 1)
            , a.updated_at=SYSDATE()
        where exists (select 1 from stock_assets_document_detail c
            where a.assets_code= c.assets_code
          and c.document_no=#{documentNo,jdbcType=VARCHAR})
    </update>

    <update id="batchUpdateAssetHolderByDocumentNo">
        UPDATE stock_assets a
        SET a.holder =(
            SELECT b.new_holder
            FROM stock_assets_document_detail b
            WHERE a.assets_code = b.assets_code
              AND b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1),
            a.holder_time = now(),
            a.holder_dept =(
        SELECT
            b.new_dept_code
        FROM
            stock_assets_document_detail b
        WHERE
            a.assets_code = b.assets_code
            AND b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1)
            , a.holder_address = (
            SELECT
            b.new_holder_address
            FROM
            stock_assets_document_detail b
            WHERE
            a.assets_code = b.assets_code
            AND b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1)
            , a.cost_dept = (
        SELECT
            b.new_dept_code
        FROM
            stock_assets_document_detail b
        WHERE
            a.assets_code = b.assets_code
          AND b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1)
            , a.need_dept = (
        SELECT
            b.new_dept_code
        FROM
            stock_assets_document_detail b
        WHERE
            a.assets_code = b.assets_code
          AND b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1)
            , a.updated_by =(
        SELECT
            b.created_by
        FROM
            stock_assets_document_detail b
        WHERE
            a.assets_code = b.assets_code
          AND b.document_no = #{documentNo,jdbcType=VARCHAR} limit 1)
            , a.updated_at = SYSDATE()
        WHERE
            EXISTS (
            SELECT
            1
            FROM
            stock_assets_document_detail c
            WHERE
            a.assets_code = c.assets_code
          AND c.document_no = #{documentNo,jdbcType=VARCHAR})
    </update>

    <update id="batchInsertAssetOperatorLog">
        insert into stock_assets_operation_log (select null,
                                                       2,
                                                       a.assets_id,
                                                       a.assets_code,
                                                       a.assets_name,
                                                       a.supplies_code,
                                                       a.warehouse_code,
                                                       a.sn_code,
                                                       a.device_code,
                                                       a.brand,
                                                       a.model,
                                                       a.unit,
                                                       a.category,
                                                       a.assets_deploy,
                                                       a.has_sub,
                                                       a.company_code,
                                                       a.company_name,
                                                       a.status,
                                                       a.net_value,
                                                       a.initial_value,
                                                       a.scrap_value,
                                                       a.purchase_type,
                                                       a.purchase_no,
                                                       a.depreciation_way,
                                                       a.cost_dept,
                                                       a.need_dept,
                                                       holder,
                                                       a.holder_address,
                                                       a.holder_time,
                                                       a.purchase_time,
                                                       a.storage_time,
                                                       a.plan_handle_time,
                                                       a.use_year_limit,
                                                       a.regular_maintain,
                                                       a.maintain_cycle,
                                                       a.assets_keeper,
                                                       a.assets_pic,
                                                       a.saas_assets_code,
                                                       a.saas_address,
                                                       a.remark,
                                                       a.label_url,
                                                       a.created_by,
                                                       a.created_at,
                                                       c.CREATED_BY,
                                                       SYSDATE()
                                                from stock_assets a
                                                         INNER JOIN stock_assets_document_detail c
                                                                    on a.assets_code = c.assets_code
                                                where c.document_no = #{documentNo,jdbcType=VARCHAR})
    </update>

    <select id="selectWflWlfTransferLineByDocumentNo" parameterType="String"
            resultType="com.gz.eim.am.stock.dto.response.wfl.WlfAssetsTransferLineRespDTO">
        select CONCAT_WS(' ', (select b.name from ambase.sys_user b where a.old_assets_keeper = b.emp_id),
                         a.old_assets_keeper) as oldAssetKeeper,
               CONCAT_WS(' ', (select b.name from ambase.sys_user b where a.new_assets_keeper = b.emp_id),
                         a.new_assets_keeper) as newAssetKeeper,
               count(1)                       as transferCount
        from stock_assets_document_detail a
        where a.document_no = #{documentNo,jdbcType=VARCHAR}
        GROUP BY a.old_assets_keeper, a.new_assets_keeper
    </select>

    <select id="selectPageCount" parameterType="com.gz.eim.am.stock.dto.request.assets.AssetTransferHeadReqDTO"
            resultType="java.lang.Long">
        select count(1) from stock_assets_document a where 1=1
        <if test="reasonCode != null ">
            and a.reason_code = #{reasonCode}
        </if>
        <if test="headStatus != null">
            and a.head_status = #{headStatus}
        </if>
        <if test="type != null">
            and a.type = #{type}
        </if>
        <if test="newAssetsHolder != null and newAssetsHolder!= ''">
            and a.new_assets_holder = #{newAssetsHolder}
        </if>
        <if test="documentNo != null and documentNo!= ''">
            and a.document_no = #{documentNo}
        </if>
        <if test="billingUser != null and billingUser!= ''">
            and a.billing_user = #{billingUser}
        </if>
        <if test="billingUser != null and billingUser!= ''">
            and a.billing_user = #{billingUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.created_at, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.created_at, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="assetsCode != null and assetsCode!= ''">
            and exists (select 1 from stock_assets_document_detail b where a.document_no = b.document_no
            and b.assets_code = #{assetsCode})
        </if>
    </select>

    <select id="selectPage" parameterType="com.gz.eim.am.stock.dto.request.assets.AssetTransferHeadReqDTO" resultType="com.gz.eim.am.stock.entity.StockAssetsDocument">
        select a.id,a.document_no,a.type,a.reason_code,a.billing_user,a.billing_time,a.remark,a.new_assets_keeper,a.del_flag,a.created_by,a.created_at,a.updated_by,a.updated_at, a.head_status, a.new_assets_holder, a.need_time from stock_assets_document a where 1=1
        <if test="reasonCode != null ">
            and a.reason_code = #{reasonCode}
        </if>
        <if test="headStatus != null">
            and a.head_status = #{headStatus}
        </if>
        <if test="type != null">
            and a.type = #{type}
        </if>
        <if test="newAssetsHolder != null and newAssetsHolder!= ''">
            and a.new_assets_holder = #{newAssetsHolder}
        </if>
        <if test="documentNo != null and documentNo!= ''">
            and a.document_no = #{documentNo}
        </if>
        <if test="billingUser != null and billingUser!= ''">
            and a.billing_user = #{billingUser}
        </if>
        <if test="billingUser != null and billingUser!= ''">
            and a.billing_user = #{billingUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(a.created_at, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(a.created_at, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="assetsCode != null and assetsCode!= ''">
            and exists (select 1 from stock_assets_document_detail b where a.document_no = b.document_no
            and b.assets_code = #{assetsCode})
        </if>
        order by a.id desc
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

</mapper>
