<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.StockAssetsSyncToEbsScrapMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsEbsSyncScrap">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
        <result column="query_code" jdbcType="VARCHAR" property="queryCode" />
        <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
        <result column="extract_start_date" jdbcType="TIMESTAMP" property="extractStartDate" />
        <result column="extract_end_date" jdbcType="TIMESTAMP" property="extractEndDate" />
        <result column="business_type" jdbcType="INTEGER" property="businessType" />
        <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
        <result column="business_line_no" jdbcType="VARCHAR" property="businessLineNo" />
        <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
        <result column="billing_time" jdbcType="VARCHAR" property="billingTime" />
        <result column="assets_name" jdbcType="VARCHAR" property="assetsName" />
        <result column="scrap_num" jdbcType="INTEGER" property="scrapNum" />
        <result column="version_id" jdbcType="INTEGER" property="versionId" />
        <result column="last_cost" jdbcType="DECIMAL" property="lastCost" />
        <result column="business_reason" jdbcType="VARCHAR" property="businessReason" />
        <result column="fa_code" jdbcType="VARCHAR" property="faCode" />
        <result column="asset_id" jdbcType="INTEGER" property="assetId" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>

    <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncScrap">
        insert into stock_assets_ebs_sync_scrap (assets_code, batch_no, query_code,
        sync_status, error_message, extract_start_date,
        extract_end_date, business_type, business_no,
        business_line_no, billing_user, billing_time,scrap_time,
        assets_name,book_type_code, scrap_num, version_id,
        last_cost, business_reason, fa_code,
        asset_id, created_by, created_at,
        updated_by, updated_at)
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.assetsCode,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.queryCode,jdbcType=VARCHAR},
        #{item.syncStatus,jdbcType=TINYINT}, #{item.errorMessage,jdbcType=VARCHAR}, #{item.extractStartDate,jdbcType=TIMESTAMP},
        #{item.extractEndDate,jdbcType=TIMESTAMP}, #{item.businessType,jdbcType=INTEGER}, #{item.businessNo,jdbcType=VARCHAR},
        #{item.businessLineNo,jdbcType=VARCHAR}, #{item.billingUser,jdbcType=VARCHAR}, #{item.billingTime,jdbcType=VARCHAR},#{item.scrapTime,jdbcType=VARCHAR},
        #{item.assetsName,jdbcType=VARCHAR},#{item.bookTypeCode,jdbcType=VARCHAR}, #{item.scrapNum,jdbcType=INTEGER}, #{item.versionId,jdbcType=INTEGER},
        #{item.lastCost,jdbcType=DECIMAL}, #{item.businessReason,jdbcType=VARCHAR}, #{item.faCode,jdbcType=VARCHAR},
        #{item.assetId,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>


    <select id="extractScrapAssets" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncScrap">
        <!--报废数据提取-->
        SELECT
        '9' as businessType,
        sash.scrap_no as businessNo,
        sasl.line_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.billing_time, '%Y-%m-%d') as billingTime,
        DATE_FORMAT(sash.updated_at, '%Y-%m-%d') as scrapTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sasl.cost_dept) as bookTypeCode,
        1 as scrapNum,
        1 as version_id,
        acgt.last_cost as lastCost,
        sash.remark as businessReason
        FROM
        stock_assets a
        inner join stock_assets_scrap_line sasl on a.assets_code=sasl.assets_code
        inner join stock_assets_scrap_head sash on sash.head_id=sasl.head_id
        left join ambase.cux_getfadetail_tbl acgt on a.assets_code = acgt.TAG_NUMBER
        WHERE not exists (select 1 from stock_assets_ebs_sync_scrap saes where saes.business_no=sash.scrap_no)
        and a.Extract_status=1
        and sash.updated_at > #{startDate}
        and sash.scrap_status=2
        union all
        <!-- 盘亏出库 -->
        SELECT
        '4' as businessType,
        sash.delivery_no as businessNo,
        sasl.delivery_detail_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.billing_time, '%Y-%m-%d') as billingTime,
        DATE_FORMAT(sash.created_at, '%Y-%m-%d') as scrapTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=a.cost_dept) as bookTypeCode,
        1 as scrapNum,
        1 as version_id,
        acgt.last_cost as lastCost,
        sash.remark as businessReason
        FROM
        stock_assets a
        inner join stock_delivery_detail_assets sasl on a.assets_code=sasl.assets_code
        inner join stock_delivery sash on sash.delivery_id=sasl.delivery_id
        left join ambase.cux_getfadetail_tbl acgt on a.assets_code = acgt.TAG_NUMBER
        WHERE not exists (select 1 from stock_assets_ebs_sync_scrap saes where saes.business_no=sash.delivery_no)
        and a.Extract_status=1
        and sash.created_at > #{startDate}
        and sash.out_stock_type=4
        union all
        SELECT
        '4' as businessType,
        sash.delivery_no as businessNo,
        sasl.delivery_detail_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.billing_time, '%Y-%m-%d') as billingTime,
        DATE_FORMAT(sash.created_at, '%Y-%m-%d') as scrapTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=a.cost_dept) as bookTypeCode,
        1 as scrapNum,
        1 as version_id,
        acgt.last_cost as lastCost,
        sash.remark as businessReason
        FROM
        stock_assets a
        inner join stock_delivery_detail_assets sasl on a.assets_code=sasl.assets_code
        inner join stock_delivery_history sash on sash.delivery_id=sasl.delivery_id
        left join ambase.cux_getfadetail_tbl acgt on a.assets_code = acgt.TAG_NUMBER
        WHERE not exists (select 1 from stock_assets_ebs_sync_scrap saes where saes.business_no=sash.delivery_no)
        and a.Extract_status=1
        and sash.created_at > #{startDate}
        and sash.out_stock_type=4
    </select>

    <!-- 部分字段不支持修改、条件可以资产主键或者资产编码 -->
    <update id="updateMultipleSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncScrap">
        <foreach collection="list" item="item" index="index" separator=";">
            update stock_assets_ebs_sync_scrap
            <set>
                <if test="item.faCode != null">
                    FA_CODE = #{item.faCode,jdbcType=VARCHAR},
                </if>
                <if test="item.syncStatus != null">
                    sync_status = #{item.syncStatus,jdbcType=TINYINT},
                </if>
                <if test="item.errorMessage != null">
                    ERROR_MESSAGE = #{item.errorMessage,jdbcType=VARCHAR},
                </if>
                <if test="item.assetId != null">
                    ASSET_ID = #{item.assetId,jdbcType=VARCHAR},
                </if>
                <if test="item.updatedAt != null">
                    updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
                </if>
            </set>
            <where>
                query_code = #{item.queryCode} and assets_code = #{item.assetsCode} and business_no = #{item.businessNo}
            </where>
        </foreach>
    </update>

    <select id="getQueryCodeList" resultType="java.lang.String">
        select distinct query_code from stock_assets_ebs_sync_scrap where sync_status in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>