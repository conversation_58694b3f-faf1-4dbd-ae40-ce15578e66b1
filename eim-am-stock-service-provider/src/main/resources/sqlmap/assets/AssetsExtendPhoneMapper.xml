<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.AssetsExtendPhoneMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
        <id column="assets_phone_id" jdbcType="BIGINT" property="assetsPhoneId" />
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
        <result column="memory_size" jdbcType="VARCHAR" property="memorySize" />
        <result column="storage_spaces_size" jdbcType="VARCHAR" property="storageSpacesSize" />
        <result column="cpu_model" jdbcType="VARCHAR" property="cpuModel" />
        <result column="sys_version" jdbcType="VARCHAR" property="sysVersion" />
        <result column="screen_size" jdbcType="VARCHAR" property="screenSize" />
        <result column="phone_brand" jdbcType="VARCHAR" property="phoneBrand" />
        <result column="phone_model" jdbcType="VARCHAR" property="phoneModel" />
        <result column="phone_colour" jdbcType="VARCHAR" property="phoneColour" />
        <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
        <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        assets_phone_id, assets_code, memory_size, storage_spaces_size, cpu_model, sys_version,
        screen_size, phone_brand, phone_model, phone_colour, data_status, del_flag, created_by,
        created_at, updated_by, updated_at
    </sql>

    <insert id="batchInsertAssetsPhone" parameterType="com.gz.eim.am.stock.entity.StockInventoryAssetImport">
        insert into stock_assets_extend_phone (assets_code, memory_size, storage_spaces_size,
        cpu_model, sys_version, screen_size,
        phone_brand, phone_model, phone_colour,
        data_status, del_flag, created_by,
        created_at, updated_by, updated_at
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.assetCode,jdbcType=VARCHAR},#{item.attr1,jdbcType=VARCHAR}, #{item.attr2,jdbcType=VARCHAR},
            #{item.attr3,jdbcType=VARCHAR}, #{item.attr4,jdbcType=VARCHAR}, #{item.attr5,jdbcType=VARCHAR},
            #{item.attr6,jdbcType=VARCHAR}, #{item.attr7,jdbcType=VARCHAR}, #{item.attr8,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER},#{item.status,jdbcType=INTEGER},#{item.createdBy,jdbcType=VARCHAR},
            #{item.createdAt,jdbcType=TIMESTAMP},#{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="selectTailDataByCode" parameterType="java.lang.String" resultType="java.util.Map">
        select
        phone_brand 品牌,
        phone_model 型号,
        phone_colour 颜色,
        sys_version 系统版本,
        memory_size 内存,
        storage_spaces_size 存储空间,
        cpu_model CPU型号,
        screen_size 屏幕尺寸
        from
        stock_assets_extend_phone
        where assets_code = #{assetsCode}
    </select>

    <update id="batchUpdateByAssetsCode" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendPhone">
        <foreach collection="list" item="item" separator=";">
        update stock_assets_extend_phone
        <set>
            <if test="item.memorySize != null">
                memory_size = #{item.memorySize,jdbcType=VARCHAR},
            </if>
            <if test="item.storageSpacesSize != null">
                storage_spaces_size = #{item.storageSpacesSize,jdbcType=VARCHAR},
            </if>
            <if test="item.cpuModel != null">
                cpu_model = #{item.cpuModel,jdbcType=VARCHAR},
            </if>
            <if test="item.sysVersion != null">
                sys_version = #{item.sysVersion,jdbcType=VARCHAR},
            </if>
            <if test="item.screenSize != null">
                screen_size = #{item.screenSize,jdbcType=VARCHAR},
            </if>
            <if test="item.phoneBrand != null">
                phone_brand = #{item.phoneBrand,jdbcType=VARCHAR},
            </if>
            <if test="item.phoneModel != null">
                phone_model = #{item.phoneModel,jdbcType=VARCHAR},
            </if>
            <if test="item.phoneColour != null">
                phone_colour = #{item.phoneColour,jdbcType=VARCHAR},
            </if>
            <if test="item.dataStatus != null">
                data_status = #{item.dataStatus,jdbcType=TINYINT},
            </if>
            <if test="item.delFlag != null">
                del_flag = #{item.delFlag,jdbcType=TINYINT},
            </if>
            <if test="item.createdBy != null">
                created_by = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdAt != null">
                created_at = #{item.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.updatedBy != null">
                updated_by = #{item.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.updatedAt != null">
                updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
            </if>
        </set>
        where assets_code = #{item.assetsCode,jdbcType=VARCHAR}
        </foreach>
    </update>

</mapper>