<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.StockAssetsSyncToEbsChangeMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
        <result column="query_code" jdbcType="VARCHAR" property="queryCode" />
        <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
        <result column="extract_start_date" jdbcType="TIMESTAMP" property="extractStartDate" />
        <result column="extract_end_date" jdbcType="TIMESTAMP" property="extractEndDate" />
        <result column="business_type" jdbcType="INTEGER" property="businessType" />
        <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
        <result column="business_line_no" jdbcType="VARCHAR" property="businessLineNo" />
        <result column="billing_user" jdbcType="VARCHAR" property="billingUser" />
        <result column="billing_time" jdbcType="VARCHAR" property="billingTime" />
        <result column="assets_name" jdbcType="VARCHAR" property="assetsName" />
        <result column="last_book_typeCode" jdbcType="VARCHAR" property="lastBookTypecode" />
        <result column="last_company_code" jdbcType="VARCHAR" property="lastCompanyCode" />
        <result column="last_dept_code" jdbcType="VARCHAR" property="lastDeptCode" />
        <result column="last_address" jdbcType="VARCHAR" property="lastAddress" />
        <result column="new_book_typeCode" jdbcType="VARCHAR" property="newBookTypecode" />
        <result column="new_company_code" jdbcType="VARCHAR" property="newCompanyCode" />
        <result column="new_dept_code" jdbcType="VARCHAR" property="newDeptCode" />
        <result column="new_address" jdbcType="VARCHAR" property="newAddress" />
        <result column="last_price" jdbcType="VARCHAR" property="lastPrice" />
        <result column="new_price" jdbcType="VARCHAR" property="newPrice" />
        <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
        <result column="version_id" jdbcType="INTEGER" property="versionId" />
        <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
        <result column="fa_code" jdbcType="VARCHAR" property="faCode" />
        <result column="asset_id" jdbcType="INTEGER" property="assetId" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>

    <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        insert into stock_assets_ebs_sync_change (assets_code, batch_no, query_code,
        sync_status, error_message, extract_start_date,
        extract_end_date, business_type, business_no,
        business_line_no, billing_user, billing_time,
        assets_name, last_book_type_code, last_company_code,
        last_dept_code, last_address, new_book_type_code,
        new_company_code, new_dept_code, new_address,
        last_price, new_price, bill_code,
        version_id, change_reason, fa_code,
        asset_id, created_by, created_at,
        updated_by, updated_at)
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.assetsCode,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.queryCode,jdbcType=VARCHAR},
        #{item.syncStatus,jdbcType=TINYINT}, #{item.errorMessage,jdbcType=VARCHAR}, #{item.extractStartDate,jdbcType=TIMESTAMP},
        #{item.extractEndDate,jdbcType=TIMESTAMP}, #{item.businessType,jdbcType=INTEGER}, #{item.businessNo,jdbcType=VARCHAR},
        #{item.businessLineNo,jdbcType=VARCHAR}, #{item.billingUser,jdbcType=VARCHAR}, #{item.billingTime,jdbcType=VARCHAR},
        #{item.assetsName,jdbcType=VARCHAR}, #{item.lastBookTypeCode,jdbcType=VARCHAR}, #{item.lastCompanyCode,jdbcType=VARCHAR},
        #{item.lastDeptCode,jdbcType=VARCHAR}, #{item.lastAddress,jdbcType=VARCHAR}, #{item.newBookTypeCode,jdbcType=VARCHAR},
        #{item.newCompanyCode,jdbcType=VARCHAR}, #{item.newDeptCode,jdbcType=VARCHAR}, #{item.newAddress,jdbcType=VARCHAR},
        #{item.lastPrice,jdbcType=VARCHAR}, #{item.newPrice,jdbcType=VARCHAR}, #{item.billCode,jdbcType=VARCHAR},
        #{item.versionId,jdbcType=INTEGER}, #{item.changeReason,jdbcType=VARCHAR}, #{item.faCode,jdbcType=VARCHAR},
        #{item.assetId,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="extractReceiveAssets" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        SELECT
        1 as businessType,
        sash.delivery_no as businessNo,
        sasl.delivery_detail_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.CREATED_AT, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sw.cost_center_code) as lastBookTypeCode,
        a.company_code as lastCompanyCode,
        sw.cost_center_code as lastDeptCode,
        left(swb.address,10) as lastAddress,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sash.use_user_dept) as newBookTypeCode,
        a.company_code as newCompanyCode,
        sash.use_user_dept as newDeptCode,
        left(a.holder_address,10) as newAddress,
        <!--a.Price_excluding_tax as lastPrice,
        a.statement_price_excluding_tax as newPrice,
        a.bill_code as billCode,-->
        1 as version_id,
        1 as change_reason
        FROM
        stock_assets a
        inner join stock_delivery_detail_assets sasl on a.assets_code=sasl.assets_code
        inner join stock_delivery sash on sash.delivery_id=sasl.delivery_id
        inner join stock_warehouse sw on sash.warehouse_code=sw.code
        inner join stock_warehouse_base swb on sw.code=swb.warehouse_code
        WHERE not exists (select 1 from stock_assets_ebs_sync_change saes where saes.business_no=sash.delivery_no)
        <!--  只提取新增时同步到ebs的资产  -->
        and a.Extract_status=1
        and sw.type=2
        and sash.created_at > #{startDate}
        and sash.out_stock_type=7
    </select>

    <select id="extractReturnAssets" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        SELECT
        5 as businessType,
        sash.inventory_in_no as businessNo,
        sasl.in_supplies_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.CREATED_AT, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id = sasl.use_user_dept) as lastBookTypeCode,
        a.company_code as lastCompanyCode,
        sasl.use_user_dept as lastDeptCode,
        left(siiph.duty_address,10) as lastAddress,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sw.cost_center_code) as newBookTypeCode,
        a.company_code as newCompanyCode,
        sw.cost_center_code as newDeptCode,
        left(swb.address,10) as newAddress,
        <!--a.Price_excluding_tax as lastPrice,
        a.statement_price_excluding_tax as newPrice,
        a.bill_code as billCode,-->
        1 as version_id,
        5 as change_reason
        FROM
        stock_assets a
        inner join stock_inventory_in_supplies_assets sasl on a.assets_code=sasl.assets_code
        inner join stock_inventory_in_supplies siis on sasl.in_supplies_id=siis.in_supplies_id
        inner join stock_inventory_in sash on sash.inventory_in_id=siis.inventory_in_id
        inner join stock_inventory_in_plan_heads siiph on siiph.inventory_in_plan_head_id = sash.inventory_in_plan_head_id
        inner join stock_warehouse sw on sash.in_warehouse_code=sw.code
        inner join stock_warehouse_base swb on sw.code=swb.warehouse_code
--         inner join ambase.sys_user asu on sash.duty_user=asu.emp_id
        WHERE not exists (select 1 from stock_assets_ebs_sync_change saes where saes.business_no=sash.inventory_in_no)
        and a.Extract_status=1
        and sw.type=2
        and sash.created_at > #{startDate}
        and sash.inventory_in_type=7
    </select>

    <select id="extractTransOut" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        SELECT
        2 as businessType,
        sash.delivery_no as businessNo,
        sasl.delivery_detail_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.CREATED_AT, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sw1.cost_center_code) as lastBookTypeCode,
        a.company_code as lastCompanyCode,
        sw1.cost_center_code as lastDeptCode,
        left(swb.address,10) as lastAddress,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sw1.cost_center_code) as newBookTypeCode,
        a.company_code as newCompanyCode,
        sw1.cost_center_code as newDeptCode,
        '在途' as newAddress,
        <!--a.Price_excluding_tax as lastPrice,
        a.statement_price_excluding_tax as newPrice,
        a.bill_code as billCode,-->
        1 as version_id,
        2 as change_reason
        FROM
        stock_assets a
        inner join stock_delivery_detail_assets sasl on a.assets_code=sasl.assets_code
        inner join stock_delivery sash on sash.delivery_id=sasl.delivery_id
        inner join stock_warehouse sw1 on sash.warehouse_code=sw1.code
        <!--inner join stock_warehouse sw2 on sash.in_warehouse_code=sw2.code-->
        inner join stock_warehouse_base swb on sw1.code=swb.warehouse_code
        WHERE not exists (select 1 from stock_assets_ebs_sync_change saes where saes.business_no=sash.delivery_no)
        and a.Extract_status=1
        and sw1.type=2
        and sash.created_at > #{startDate}
        and sash.out_stock_type=8
    </select>

    <select id="extractTransIn" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        SELECT
        3 as businessType,
        sash.inventory_in_no as businessNo,
        sasl.in_supplies_id as businessLineNo,
        sash.billing_user as billingUser,
        DATE_FORMAT(sash.CREATED_AT, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sw2.cost_center_code) as lastBookTypeCode,
        a.company_code as lastCompanyCode,
        sw2.cost_center_code as lastDeptCode,
        '在途' as lastAddress,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=sw1.cost_center_code) as newBookTypeCode,
        a.company_code as newCompanyCode,
        sw1.cost_center_code as newDeptCode,
        left(swb.address,10) as newAddress,
        a.Price_excluding_tax as lastPrice,
        a.statement_price_excluding_tax as newPrice,
        a.bill_code as billCode,
        1 as version_id,
        3 as change_reason
        FROM
        stock_assets a
        inner join stock_inventory_in_supplies_assets sasl on a.assets_code=sasl.assets_code
        inner join stock_inventory_in_supplies siis on sasl.in_supplies_id=siis.in_supplies_id
        inner join stock_inventory_in sash on sash.inventory_in_id=siis.inventory_in_id
        inner join stock_inventory_in_plan_heads siiph on siiph.inventory_in_plan_head_id=sash.inventory_in_plan_head_id
        inner join stock_warehouse sw1 on sash.in_warehouse_code=sw1.code
        inner join stock_warehouse sw2 on siiph.out_warehouse_code=sw2.code
        inner join stock_warehouse_base swb on sw1.code=swb.warehouse_code
        WHERE not exists (select 1 from stock_assets_ebs_sync_change saes where saes.business_no=sash.inventory_in_no)
        and a.Extract_status=1
        and sw1.type=2
        and sash.created_at > #{startDate}
        and sash.inventory_in_type=6
    </select>

    <select id="extractAccount" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        SELECT
        6 as businessType,
        psan.accountant_code as businessNo,
        psan.accountant_line_code as businessLineNo,
        psan.created_by as billingUser,
        DATE_FORMAT(psan.CREATED_AT, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=a.cost_dept) as lastBookTypeCode,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=a.cost_dept) as newBookTypeCode,
        a.Price_excluding_tax as lastPrice,
        a.statement_price_excluding_tax as newPrice,
        a.bill_code as billCode,
        1 as version_id,
        6 as change_reason
        FROM
        stock_assets a
        inner join purchase_statement_assets_note psan on a.assets_code=psan.assets_code
        WHERE not exists (select 1 from stock_assets_ebs_sync_change saes where saes.business_no=psan.accountant_code)
        and a.Extract_status=1
        and psan.created_at > #{startDate}
    </select>

    <select id="extractHolderChange" resultType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        SELECT
        10 as businessType,
        sad.document_no as businessNo,
        psan.id as businessLineNo,
        sad.billing_user as billingUser,
        DATE_FORMAT(sad.updated_at, '%Y-%m-%d %H:%i:%s') as billingTime,
        a.assets_code as assetsCode,
        a.assets_name as assetsName,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=psan.old_dept_code) as lastBookTypeCode,
        psan.old_company_code as lastCompanyCode,
        psan.old_dept_code as lastDeptCode,
        left(psan.old_holder_address,10) as lastAddress,
        (select d.cst_business_line from ambase.sys_dept d where d.dept_id=psan.new_dept_code) as newBookTypeCode,
        psan.new_company_code as newCompanyCode,
        psan.new_dept_code as newDeptCode,
        left(psan.new_holder_address,10) as newAddress,
        1 as version_id,
        10 as change_reason
        FROM
        stock_assets a
        inner join stock_assets_document_detail psan on a.assets_code=psan.assets_code
        inner join stock_assets_document sad on sad.document_no=psan.document_no
        WHERE not exists (select 1 from stock_assets_ebs_sync_change saes where saes.business_no=sad.document_no)
        and a.Extract_status=1
        and sad.type=2
        and sad.head_status = 4
        and sad.updated_at > #{startDate}
    </select>

    <!-- 部分字段不支持修改、条件可以资产主键或者资产编码 -->
    <update id="updateMultipleSelective" parameterType="com.gz.eim.am.stock.entity.StockAssetsEbsSyncChange">
        <foreach collection="list" item="item" index="index" separator=";">
            update stock_assets_ebs_sync_change
            <set>
                <if test="item.faCode != null">
                    FA_CODE = #{item.faCode,jdbcType=VARCHAR},
                </if>
                <if test="item.syncStatus != null">
                    sync_status = #{item.syncStatus,jdbcType=TINYINT},
                </if>
                <if test="item.errorMessage != null">
                    ERROR_MESSAGE = #{item.errorMessage,jdbcType=VARCHAR},
                </if>
                <if test="item.assetId != null">
                    ASSET_ID = #{item.assetId,jdbcType=VARCHAR},
                </if>
                <if test="item.updatedAt != null">
                    updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
                </if>
            </set>
            <where>
                query_code = #{item.queryCode} and assets_code = #{item.assetsCode} and business_no = #{item.businessNo}
            </where>
        </foreach>
    </update>

    <select id="getQueryCodeList" resultType="java.lang.String">
        select distinct query_code from stock_assets_ebs_sync_change where sync_status in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>