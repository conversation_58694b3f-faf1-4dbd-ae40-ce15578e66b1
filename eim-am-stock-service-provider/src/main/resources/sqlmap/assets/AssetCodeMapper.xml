<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.AssetCodeMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetCode">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="asset_code" jdbcType="VARCHAR" property="assetCode" />
        <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="is_used" jdbcType="INTEGER" property="isUsed" />
        <result column="operating" jdbcType="VARCHAR" property="operating" />
        <result column="operate_user" jdbcType="VARCHAR" property="operateUser" />
        <result column="is_in" jdbcType="INTEGER" property="isIn" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>


    <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockAssetCode">
        insert into stock_asset_code (asset_code, category_code, company_code,
        is_used, operating, operate_user,
        is_in, status, del_flag,
        created_by, updated_by, yes_low_value
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.assetCode,jdbcType=VARCHAR}, #{item.categoryCode,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR},
        #{item.isUsed,jdbcType=INTEGER}, #{item.operating,jdbcType=VARCHAR}, #{item.operateUser,jdbcType=VARCHAR},
        #{item.isIn,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, #{item.delFlag,jdbcType=INTEGER},
        #{item.createdBy,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=VARCHAR}, #{item.yesLowValue,jdbcType=INTEGER})
        </foreach>

    </insert>

    <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockAssetCode">
        <foreach collection="list" item="item" separator=";">
            update stock_asset_code
            <set>
                <if test="item.assetCode != null">
                    asset_code = #{item.assetCode,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryCode != null">
                    category_code = #{item.categoryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.companyCode != null">
                    company_code = #{item.companyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isUsed != null">
                    is_used = #{item.isUsed,jdbcType=INTEGER},
                </if>
                <if test="item.operating != null">
                    operating = #{item.operating,jdbcType=VARCHAR},
                </if>
                <if test="item.operateUser != null">
                    operate_user = #{item.operateUser,jdbcType=VARCHAR},
                </if>
                <if test="item.isIn != null">
                    is_in = #{item.isIn,jdbcType=INTEGER},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.yesLowValue != null">
                    yes_low_value = #{item.yesLowValue,jdbcType=INTEGER},
                </if>
                <if test="item.delFlag != null">
                    del_flag = #{item.delFlag,jdbcType=INTEGER},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdAt != null">
                    created_at = #{item.createdAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updatedBy != null">
                    updated_by = #{item.updatedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.updatedAt != null">
                    updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>

    </update>



</mapper>