<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.assets.AssetsExtendHostMachineMapper">


    <insert id="batchInsertAssetsHostMachine" parameterType="com.gz.eim.am.stock.entity.StockInventoryAssetImport">
        insert into stock_assets_extend_host_machine ( assets_code, brand,
        model, processor, ram_memory,
        hard_disk_capacity, data_status, del_flag,
        created_by, created_at, updated_by,
        updated_at)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.assetCode,jdbcType=VARCHAR}, #{item.attr1,jdbcType=VARCHAR}, #{item.attr2,jdbcType=VARCHAR},
            #{item.attr3,jdbcType=VARCHAR}, #{item.attr4,jdbcType=VARCHAR}, #{item.attr5,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER},#{item.status,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="selectTailDataByCode" parameterType="java.lang.String" resultType="java.util.Map">
        select
        brand 品牌,
        model 型号,
        processor 处理器,
        ram_memory 内存容量,
        hard_disk_capacity 硬盘容量
        from
        stock_assets_extend_host_machine
        where assets_code = #{assetsCode}
    </select>

    <update id="batchUpdateByAssetsCode" parameterType="com.gz.eim.am.stock.entity.StockAssetsExtendHostMachine">
        <foreach collection="list" item="item" separator=";">
        update stock_assets_extend_host_machine
        <set>
            <if test="item.assetsCode != null">
                assets_code = #{item.assetsCode,jdbcType=VARCHAR},
            </if>
            <if test="item.brand != null">
                brand = #{item.brand,jdbcType=VARCHAR},
            </if>
            <if test="item.model != null">
                model = #{item.model,jdbcType=VARCHAR},
            </if>
            <if test="item.processor != null">
                processor = #{item.processor,jdbcType=VARCHAR},
            </if>
            <if test="item.ramMemory != null">
                ram_memory = #{item.ramMemory,jdbcType=VARCHAR},
            </if>
            <if test="item.hardDiskCapacity != null">
                hard_disk_capacity = #{item.hardDiskCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.dataStatus != null">
                data_status = #{item.dataStatus,jdbcType=TINYINT},
            </if>
            <if test="item.delFlag != null">
                del_flag = #{item.delFlag,jdbcType=TINYINT},
            </if>
            <if test="item.createdBy != null">
                created_by = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdAt != null">
                created_at = #{item.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.updatedBy != null">
                updated_by = #{item.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.updatedAt != null">
                updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
            </if>
        </set>
        where assets_code = #{item.assetsCode,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>