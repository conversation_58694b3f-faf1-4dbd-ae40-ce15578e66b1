<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.manage.SuppliesConfigDetailMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockSuppliesConfigDetail">
        <id column="config_detail_id" jdbcType="BIGINT" property="configDetailId"/>
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode"/>
        <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="config_type" jdbcType="INTEGER" property="configType"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="sn_no" jdbcType="VARCHAR" property="snNo"/>
        <result column="all_number" jdbcType="BIGINT" property="allNumber"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
    config_detail_id, warehouse_code, supplies_code, version, config_type, batch_no,
    sn_no, all_number, status, created_by, created_at, updated_by, updated_at
    </sql>
    <insert id="insertList" parameterType="java.util.List">
        insert into stock_supplies_config_detail (warehouse_code, supplies_code,
        version, config_type, batch_no,
        sn_no, all_number, status,
        created_by, created_at, updated_by,
        updated_at)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.warehouseCode,jdbcType=VARCHAR}, #{item.suppliesCode,jdbcType=VARCHAR},
            #{item.version,jdbcType=VARCHAR}, #{item.configType,jdbcType=INTEGER}, #{item.batchNo,jdbcType=VARCHAR},
            #{item.snNo,jdbcType=VARCHAR}, #{item.allNumber,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <update id="updateConfigDetailNumber" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfigDetail">
    UPDATE stock_supplies_config_detail
    SET all_number = all_number + #{allNumber,jdbcType=BIGINT}
    WHERE config_detail_id = #{configDetailId,jdbcType=BIGINT}
    </update>
    <select id="getConfigDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO">
        SELECT
        a.supplies_code suppliesCode,
        ss.name suppliesName,
        a.warehouse_code warehouseCode,
        a.warehouse_name warehouseName,
        a.cost_center_code costCenterCode,
        a.all_number - if(b.sendCount is null,0,b.sendCount) AS totalNumber,
        a.all_number - if(b.sendCount is null,0,b.sendCount) AS useNumber,
        ssu.unit_name unit,
        dept.dept_name costCenterName,
        ss.remark suppliesRemark
        FROM (SELECT ssc.*,sw.cost_center_code,sw.name warehouse_name
        FROM stock_supplies_config_detail ssc
        JOIN stock_warehouse sw ON ssc.warehouse_code = sw.code
        WHERE sw.cost_center_type = 1
        AND ssc.version = #{version} AND (ssc.batch_no = #{manageNo} OR ssc.sn_no = #{manageNo})
        <if test="suppliesCode != null and suppliesCode != ''">
            AND ssc.supplies_code = #{suppliesCode}
        </if>
        <if test="costCenterCode != null and costCenterCode != ''">
            AND sw.cost_center_code = #{costCenterCode}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND ssc.warehouse_code = #{warehouseCode}
        </if>
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and sw.code in
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        ) a LEFT JOIN ( SELECT m.supplies_code,m.batch_no,m.sn_no,m.warehouse_code,sum( m.number ) AS sendCount
        FROM (
        SELECT sdd.real_warehouse_code warehouse_code, sdd.supplies_code,sdd.batch_no,null sn_no, if(sdd.real_number is
        null,0,sdd.real_number) as number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN(2, 3)
        and sdd.batch_no is not null
        and sdd.batch_no != ''
        AND sdd.batch_no = #{manageNo}
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sdd.supplies_code = #{suppliesCode}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sdd.real_warehouse_code = #{warehouseCode}
        </if>
        union all
        SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        null batch_no,
        sdds.SERIAL_NUMBER sn_no,
        1 number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        and sdd.status in (2, 3)
        and (sdd.batch_no is null or sdd.batch_no = '')
        and sdds.SERIAL_NUMBER = #{manageNo}
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sdd.supplies_code = #{suppliesCode}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sdd.real_warehouse_code = #{warehouseCode}
        </if>) m GROUP BY m.supplies_code,m.warehouse_code) b
        ON a.supplies_code = b.supplies_code
        AND a.warehouse_code = b.warehouse_code AND (a.batch_no = b.batch_no OR a.sn_no = b.sn_no)
        LEFT JOIN stock_supplies ss ON a.supplies_code = ss.code
        LEFT JOIN stock_supplies_unit ssu ON ss.unit_code = ssu.unit_code
        LEFT JOIN ambase.sys_dept dept ON a.cost_center_code = dept.dept_id
    </select>
    <select id="selectUseConfigDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="com.gz.eim.am.stock.entity.StockSuppliesConfigDetail">
        SELECT
        a.supplies_code suppliesCode,
        a.warehouse_code warehouseCode,
        a.config_type configType,
        a.version version,
        a.batch_no batchNo,
        a.sn_no snNo,
        a.all_number - if(b.sendCount is null,0,b.sendCount) AS allNumber
        FROM (SELECT *
        FROM stock_supplies_config_detail ssc
        WHERE version = #{version}
        AND supplies_code = #{suppliesCode}
        AND warehouse_code = #{warehouseCode}
        <if test="batchNo != null and batchNo != ''">
            AND batch_no = #{batchNo}
        </if>
        <if test="snNo != null and snNo != ''">
            AND sn_no = #{snNo}
        </if>
        ) a LEFT JOIN ( SELECT m.supplies_code,m.warehouse_code,m.batch_no,m.sn_no,sum( m.number ) AS sendCount
        FROM ( SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        sdd.batch_no,
        null sn_no,
        if(sdd.real_number is null, 0,sdd.real_number) as number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        WHERE
        sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN(2, 3)
        and sdd.batch_no is not null
        and sdd.batch_no != ''
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="batchNo != null and batchNo != ''">
            AND sdd.batch_no = #{batchNo}
        </if>
        union all
        SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        null batch_no,
        sdds.SERIAL_NUMBER sn_no,
        1 number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        and sdd.status in (2, 3)
        and (sdd.batch_no is null or sdd.batch_no = '')
        <if test="snNo != null and snNo != ''">
            and sdds.SERIAL_NUMBER = #{snNo}
        </if>
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        ) m GROUP BY m.supplies_code,m.warehouse_code
        <if test="batchNo != null and batchNo != ''">
            ,m.batch_no
        </if>
        <if test="snNo != null and snNo != ''">
            ,m.sn_no
        </if>) b
        ON a.supplies_code = b.supplies_code
        AND a.warehouse_code = b.warehouse_code
        <if test="batchNo != null and batchNo != ''">
            AND a.batch_no = b.batch_no
        </if>
        <if test="snNo != null and snNo != ''">
            AND a.sn_no = b.sn_no
        </if>
    </select>
    <select id="selectUseManageDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="com.gz.eim.am.stock.entity.StockSuppliesConfigDetail">
      SELECT
          a.supplies_code suppliesCode,
          a.warehouse_code warehouseCode,
          a.config_type configType,
          a.version version,
          a.batch_no batchNo,
          a.sn_no snNo,
          a.all_number - if(b.sendCount is null,0,b.sendCount) AS allNumber
      FROM (SELECT *
            FROM stock_supplies_config_detail ssc
            WHERE version = #{version}
              AND supplies_code = #{suppliesCode}
              AND warehouse_code = #{warehouseCode}) a
               LEFT JOIN ( SELECT m.supplies_code,m.warehouse_code,m.batch_no,m.sn_no,sum( m.number ) AS sendCount
                           FROM ( SELECT
                                   sdd.real_warehouse_code warehouse_code,
                                   sdd.supplies_code,
                                   sdd.batch_no,
                                   sdd.sn_no,
                                   if(sdd.real_number is null,sdd.number,sdd.real_number) as number
                                  FROM stock_delivery sd
                                    JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
                                  WHERE sd.status IN (7, 8, 9, 10, 30)
                                    and sdd.status in (2, 3)
                                    and sdd.batch_no is not null
                                    and sdd.batch_no != ''
                                    AND sdd.supplies_code = #{suppliesCode}
                                    AND sdd.real_warehouse_code = #{warehouseCode}
                                  union all
                                  SELECT
                                    sdd.real_warehouse_code warehouse_code,
                                    sdd.supplies_code,
                                    null batch_no,
                                    sdds.SERIAL_NUMBER sn_no,
                                    1 number
                                  FROM stock_delivery sd
                                    JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
                                    join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
                                  WHERE sd.status IN (7, 8, 9, 10, 30)
                                    and sdd.status in (2, 3)
                                    and (sdd.batch_no is null or sdd.batch_no = '')
                                    AND sdd.supplies_code = #{suppliesCode}
                                    AND sdd.real_warehouse_code = #{warehouseCode}
                                    ) m
                           GROUP BY m.supplies_code,m.warehouse_code
                                  ,m.batch_no,m.sn_no) b
                         ON a.supplies_code = b.supplies_code
                             AND a.warehouse_code = b.warehouse_code
                             AND if(a.batch_no is null,0,a.batch_no) = if(b.batch_no is null,0,b.batch_no)
                             AND if(a.sn_no is null,0,a.sn_no) = if(b.sn_no is null,0,b.sn_no)
    </select>

    <select id="selectUseConfigDetailByBatch"
            parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="com.gz.eim.am.stock.entity.StockSuppliesConfigDetail">
        SELECT
        a.supplies_code suppliesCode,
        a.warehouse_code warehouseCode,
        a.config_type configType,
        a.version version,
        a.batch_no batchNo,
        a.sn_no snNo,
        a.all_number - if(b.sendCount is null,0,b.sendCount) AS allNumber
        FROM (SELECT *
        FROM stock_supplies_config_detail ssc
        WHERE version = #{version}
        AND supplies_code = #{suppliesCode}
        AND warehouse_code = #{warehouseCode}
        <if test="batchNoList != null and batchNoList.size() > 0">
            <foreach collection="batchNoList" item="batchNo" index="index" open="(" separator="," close=")">
                AND batch_no in #{batchNo}
            </foreach>
        </if>

        <if test="snNoList != null and snNoList.size() > 0">
            <foreach collection="snNoList" item="snNo" index="index" open="(" separator="," close=")">
                AND sn_no in #{snNo}
            </foreach>
        </if>

        ) a LEFT JOIN (
        <if test="batchNoList != null and batchNoList.size() > 0">
            SELECT m.supplies_code,m.warehouse_code,m.batch_no,m.sn_no,sum( m.number ) AS sendCount
            FROM ( SELECT
            sdd.real_warehouse_code warehouse_code,
            sdd.supplies_code,
            sdd.batch_no,
            null sn_no,
            if(sdd.real_number is null, 0,sdd.real_number) as number
            FROM stock_delivery sd
            JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
            WHERE
            sd.status IN (7, 8, 9, 10, 30)
            AND sdd.status IN(2, 3)
            and sdd.batch_no is not null
            and sdd.batch_no != ''
            AND sdd.supplies_code = #{suppliesCode}
            AND sdd.real_warehouse_code = #{warehouseCode}
            <foreach collection="batchNoList" item="batchNo" index="index" open="(" separator="," close=")">
                AND sdd.batch_no in #{batchNo}
            </foreach>
        </if>
        <if test="snNoList != null and snNoList.size() > 0">
            SELECT
            sdd.real_warehouse_code warehouse_code,
            sdd.supplies_code,
            null batch_no,
            sdds.SERIAL_NUMBER sn_no,
            1 number
            FROM stock_delivery sd
            JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
            join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
            WHERE sd.status IN (7, 8, 9, 10, 30)
            and sdd.status in (2, 3)
            and (sdd.batch_no is null or sdd.batch_no = '')
            <foreach collection="snNoList" item="snNo" index="index" open="(" separator="," close=")">
                AND sdds.SERIAL_NUMBER in #{snNo}
            </foreach>
            AND sdd.supplies_code = #{suppliesCode}
            AND sdd.real_warehouse_code = #{warehouseCode}
        </if>
        ) m GROUP BY m.supplies_code,m.warehouse_code
        <if test="batchNoList != null and batchNoList.size() > 0">
            ,m.batch_no
        </if>
        <if test="snNoList != null and snNoList.size() > 0">
            ,m.sn_no
        </if>) b
        ON a.supplies_code = b.supplies_code
        AND a.warehouse_code = b.warehouse_code
        <if test="batchNoList != null and batchNoList.size() > 0">
            AND a.batch_no = b.batch_no
        </if>
        <if test="snNoList != null and snNoList.size() > 0">
            AND a.sn_no = b.sn_no
        </if>
    </select>
    <select id="countSuppliesConfigDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO" resultType="java.lang.Long">
        SELECT COUNT(1) from (SELECT
        a.supplies_code suppliesCode,
        a.warehouse_code warehouseCode,
        a.config_type configType,
        a.version VERSION,
        a.batch_no batchNo,
        a.sn_no snNo,
        a.all_number - IF(b.sendCount IS NULL,0,b.sendCount) AS allNumber
        from(
        SELECT *
        FROM stock_supplies_config_detail ssc
        WHERE VERSION = #{version}
        AND supplies_code = #{suppliesCode}
        AND warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
            AND (ssc.batch_no = #{manageNo} OR ssc.sn_no = #{manageNo})
        </if>
        ) a
        LEFT JOIN ( SELECT m.supplies_code,m.warehouse_code,m.batch_no,m.sn_no,SUM( m.number ) AS sendCount
        FROM ( SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        sdd.batch_no,
        sdd.sn_no,
        IF(sdd.real_number IS NULL,sdd.number,sdd.real_number) AS number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN (2, 3)
        AND sdd.batch_no IS NOT NULL
        AND sdd.batch_no != ''
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
            AND sdd.batch_no = #{manageNo}
        </if>
        UNION ALL
        SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        NULL batch_no,
        sdds.SERIAL_NUMBER sn_no,
        1 number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        JOIN stock_delivery_detail_sns sdds ON sdds.delivery_detail_id = sdd.delivery_detail_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN (2, 3)
        AND (sdd.batch_no IS NULL OR sdd.batch_no = '')
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
            AND sdds.SERIAL_NUMBER  = #{manageNo}
        </if>
        ) m
        GROUP BY m.supplies_code,m.warehouse_code
        ,m.batch_no,m.sn_no) b
        ON a.supplies_code = b.supplies_code
        AND a.warehouse_code = b.warehouse_code
        AND IF(a.batch_no IS NULL,0,a.batch_no) = IF(b.batch_no IS NULL,0,b.batch_no)
        AND IF(a.sn_no IS NULL,0,a.sn_no) = IF(b.sn_no IS NULL,0,b.sn_no)) t
        WHERE t.allNumber > 0
    </select>
    <select id="getSuppliesConfigDetailByPage" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO" resultType="com.gz.eim.am.stock.dto.response.manage.StockSuppliesConfigDetailRespDTO">
        SELECT t.suppliesCode,
        t.warehouseCode,
        t.VERSION,
        t.configType,
        t.allNumber,
        CASE WHEN t.batchNo IS NULL AND t.snNo IS NULL THEN
        ''
        WHEN t.snNo IS NOT NULL THEN
        t.snNo
        ELSE
        t.batchNo
        END manageNo
        from (SELECT
        a.supplies_code suppliesCode,
        a.warehouse_code warehouseCode,
        a.config_type configType,
        a.version VERSION,
        a.batch_no batchNo,
        a.sn_no snNo,
        a.all_number - IF(b.sendCount IS NULL,0,b.sendCount) AS allNumber
        from(
        SELECT *
        FROM stock_supplies_config_detail ssc
        WHERE VERSION = #{version}
        AND supplies_code = #{suppliesCode}
        AND warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
        AND (ssc.batch_no = #{manageNo} OR ssc.sn_no = #{manageNo})
        </if>
        ) a
        LEFT JOIN ( SELECT m.supplies_code,m.warehouse_code,m.batch_no,m.sn_no,SUM( m.number ) AS sendCount
        FROM ( SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        sdd.batch_no,
        sdd.sn_no,
        IF(sdd.real_number IS NULL,sdd.number,sdd.real_number) AS number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN (2, 3)
        AND sdd.batch_no IS NOT NULL
        AND sdd.batch_no != ''
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
        AND sdd.batch_no = #{manageNo}
        </if>
        UNION ALL
        SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        NULL batch_no,
        sdds.SERIAL_NUMBER sn_no,
        1 number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        JOIN stock_delivery_detail_sns sdds ON sdds.delivery_detail_id = sdd.delivery_detail_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN (2, 3)
        AND (sdd.batch_no IS NULL OR sdd.batch_no = '')
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
        AND sdds.SERIAL_NUMBER  = #{manageNo}
        </if>
        ) m
        GROUP BY m.supplies_code,m.warehouse_code
        ,m.batch_no,m.sn_no) b
        ON a.supplies_code = b.supplies_code
        AND a.warehouse_code = b.warehouse_code
        AND IF(a.batch_no IS NULL,0,a.batch_no) = IF(b.batch_no IS NULL,0,b.batch_no)
        AND IF(a.sn_no IS NULL,0,a.sn_no) = IF(b.sn_no IS NULL,0,b.sn_no)) t
        WHERE t.allNumber > 0
        ORDER BY manageNo
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="exportSuppliesConfigDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO" resultType="com.gz.eim.am.stock.entity.vo.download.ExportStockConfigDetailEntity">
        SELECT t.suppliesCode,
        t.warehouseCode,
        t.VERSION,
        t.configType,
        t.allNumber,
        CASE WHEN t.batchNo IS NULL AND t.snNo IS NULL THEN
        ''
        WHEN t.snNo IS NOT NULL THEN
        t.snNo
        ELSE
        t.batchNo
        END manageNo,
        SW.name warehouseName
        from (SELECT
        a.supplies_code suppliesCode,
        a.warehouse_code warehouseCode,
        a.config_type configType,
        a.version VERSION,
        a.batch_no batchNo,
        a.sn_no snNo,
        a.all_number - IF(b.sendCount IS NULL,0,b.sendCount) AS allNumber
        from(
        SELECT *
        FROM stock_supplies_config_detail ssc
        WHERE VERSION = #{version}
        AND supplies_code = #{suppliesCode}
        AND warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
            AND (ssc.batch_no = #{manageNo} OR ssc.sn_no = #{manageNo})
        </if>
        ) a
        LEFT JOIN ( SELECT m.supplies_code,m.warehouse_code,m.batch_no,m.sn_no,SUM( m.number ) AS sendCount
        FROM ( SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        sdd.batch_no,
        sdd.sn_no,
        IF(sdd.real_number IS NULL,sdd.number,sdd.real_number) AS number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN (2, 3)
        AND sdd.batch_no IS NOT NULL
        AND sdd.batch_no != ''
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
            AND sdd.batch_no = #{manageNo}
        </if>
        UNION ALL
        SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        NULL batch_no,
        sdds.SERIAL_NUMBER sn_no,
        1 number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        JOIN stock_delivery_detail_sns sdds ON sdds.delivery_detail_id = sdd.delivery_detail_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN (2, 3)
        AND (sdd.batch_no IS NULL OR sdd.batch_no = '')
        AND sdd.supplies_code = #{suppliesCode}
        AND sdd.real_warehouse_code = #{warehouseCode}
        <if test="manageNo != null and manageNo != ''">
            AND sdds.SERIAL_NUMBER  = #{manageNo}
        </if>
        ) m
        GROUP BY m.supplies_code,m.warehouse_code
        ,m.batch_no,m.sn_no) b
        ON a.supplies_code = b.supplies_code
        AND a.warehouse_code = b.warehouse_code
        AND IF(a.batch_no IS NULL,0,a.batch_no) = IF(b.batch_no IS NULL,0,b.batch_no)
        AND IF(a.sn_no IS NULL,0,a.sn_no) = IF(b.sn_no IS NULL,0,b.sn_no)) t
        LEFT JOIN stock_warehouse sw ON sw.code = t.warehouseCode
        WHERE t.allNumber > 0
        ORDER BY manageNo
    </select>

    <select id="getConfigDetailArrange" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO" resultType="com.gz.eim.am.stock.dto.response.manage.StockSuppliesConfigDetailRespDTO">
        SELECT ssc.supplies_code suppliesCode, ssc.sn_no manageNo, sum(ssc.all_number) allNumber
        FROM stock_supplies_config_detail ssc
        where ssc.version = #{version}
        group by ssc.supplies_code, ssc.sn_no
    </select>

    <select id="getSuppliesBySnnoAndWareCode" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO">
        select distinct b.supplies_code suppliesCode,a.name suppliesName,b.sn_no snNo from stock_supplies a
        inner join stock_supplies_config_detail b on a.code=b.supplies_code
        where b.warehouse_code=#{warehouseCode} and b.version=#{version}
        and not exists (select 1 from (select d.real_warehouse_code,d.supplies_code,e.SERIAL_NUMBER,count(1) as num from stock_delivery_detail d inner join stock_delivery_detail_sns e on d.delivery_detail_id=e.delivery_detail_id where d.status=3 group by d.real_warehouse_code,d.supplies_code,e.SERIAL_NUMBER) aa
        where b.warehouse_code=aa.real_warehouse_code and b.supplies_code=aa.supplies_code and b.sn_no=aa.SERIAL_NUMBER and b.all_number &lt;= aa.num)
        <if test="snNoList != null and snNoList.size() > 0">
            AND b.sn_no in
            <foreach collection="snNoList" item="snNo" index="index" open="(" separator="," close=")">
                #{snNo}
            </foreach>
        </if>
    </select>


    <select id="selectExistsSnsByBatch"
            parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="java.lang.String">
        SELECT
        ssc.sn_no
        FROM stock_supplies_config_detail ssc
        WHERE ssc.version = #{version}
        AND ssc.supplies_code = #{suppliesCode}
        <if test="warehouseCode != null and warehouseCode != ''">
            AND ssc.warehouse_code = #{warehouseCode}
        </if>
        <if test="snNoList != null and snNoList.size() > 0">
            AND ssc.sn_no in
            <foreach collection="snNoList" item="snNo" index="index" open="(" separator="," close=")">
                #{snNo}
            </foreach>
        </if>
    </select>

</mapper>