<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.manage.SuppliesConfigMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockSuppliesConfig">
        <id column="supplies_config_id" jdbcType="BIGINT" property="suppliesConfigId"/>
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode"/>
        <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode"/>
        <result column="max_number" jdbcType="BIGINT" property="maxNumber"/>
        <result column="all_number" jdbcType="BIGINT" property="allNumber"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="is_default" jdbcType="TINYINT" property="isDefault"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>
    <sql id="Base_Column_List">
    supplies_config_id, warehouse_code, supplies_code, max_number, all_number, version,
    is_default, created_by, created_at, updated_by, updated_at
  </sql>
    <sql id="gps_select_condition1">
        <if test="manageNo != null and manageNo != ''">
            and a.sn_no = #{manageNo}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND b.supplies_code = #{suppliesCode}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND c.code = #{warehouseCode}
        </if>
        <if test="costCenterCode != null and costCenterCode != ''">
            AND c.cost_center_code = #{costCenterCode}
        </if>
        <if test="installStatus != null and installStatus != ''">
            AND a.install_status = #{installStatus}
        </if>
        <if test="createdStartTime != null and createdStartTime != ''">
            AND DATE_FORMAT(a.created_at,'%Y-%m-%d') >= #{createdStartTime}
        </if>
        <if test="createdEndTime != null and createdEndTime != ''">
            AND DATE_FORMAT(a.created_at,'%Y-%m-%d') &lt;= #{createdEndTime}
        </if>
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and c.code in
            <foreach collection="warehouseCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>
    <sql id="gps_select_condition2">
        <if test="manageNo != null and manageNo != ''">
            and sdds.SERIAL_NUMBER = #{manageNo}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sdd.supplies_code = #{suppliesCode}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sdd.real_warehouse_code = #{warehouseCode}
        </if>
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and sdd.real_warehouse_code in
            <foreach collection="warehouseCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>
    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO stock_supplies_config
        (warehouse_code, supplies_code,max_number, all_number, is_default, version,
        created_by, created_at, updated_by, updated_at)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.warehouseCode,jdbcType=VARCHAR},
            #{item.suppliesCode,jdbcType=VARCHAR},#{item.maxNumber,jdbcType=BIGINT},#{item.allNumber,jdbcType=BIGINT},
            #{item.isDefault,jdbcType=INTEGER}, #{item.version,jdbcType=VARCHAR},#{item.createdBy,jdbcType=VARCHAR},
            #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <update id="updateInventoryByKey" parameterType="com.gz.eim.am.stock.entity.StockSuppliesConfig">
    UPDATE stock_supplies_config
    SET all_number = all_number + #{allNumber,jdbcType=BIGINT}
    WHERE supplies_config_id = #{suppliesConfigId,jdbcType=BIGINT}
  </update>



    <select id="getGpsConfigDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO">
        select aa.sn_no snNo,
        aa.supplies_code suppliesCode,
        ss.name suppliesName,
        ss.remark suppliesRemark,
        aa.warehouse_code warehouseCode,
        aa.name warehouseName,
        aa.cost_center_code costCenterCode,
        dept.dept_name costCenterName,
        ssu.unit_name unit,
        if (aa.all_number>if(dd.number is null,0,dd.number),'在库','已出库') configStatusName,
        (case aa.install_status when 0 then '未安装' when 1 then '已安装' else '已拆除' end) installStatusName
        from
        (select a.sn_no,b.supplies_code,c.code warehouse_code,c.name,c.cost_center_code,b.all_number,a.install_status
        from stock_gps_sns_tail a
        left join stock_supplies_config_detail b on a.sn_no=b.sn_no and b.version = #{version}
        left join stock_warehouse c on b.warehouse_code=c.code
        where 1=1
        <include refid="gps_select_condition1" />) aa
        left join (SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        sdds.SERIAL_NUMBER sn_no,
        count(1) number
        FROM stock_delivery sd
        inner JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        inner join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
        where sd.out_stock_type != 10
        and sd.status=8
        <include refid="gps_select_condition2" />
        group by sdd.real_warehouse_code,sdd.supplies_code,sdds.SERIAL_NUMBER) dd on aa.warehouse_code=dd.warehouse_code and aa.supplies_code=dd.supplies_code
        and aa.sn_no = dd.sn_no
        left join stock_supplies ss on aa.supplies_code=ss.code
        left join stock_supplies_unit ssu ON ss.unit_code = ssu.unit_code
        left join ambase.sys_dept dept ON aa.cost_center_code = dept.dept_id
        where 1=1
        <!--  筛选在库的数据 -->
        <if test="configStatus == 0">
            AND aa.all_number > if(dd.number is null,0,dd.number)
        </if>
        <!--  筛选已出库的数据 -->
        <if test="configStatus == 1">
            AND aa.all_number = if(dd.number is null,0,dd.number)
        </if>
        order by aa.sn_no
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="countGpsConfigDetail" parameterType="com.gz.eim.am.stock.dto.request.supplies.StockSuppliesQuantityReqDTO"
            resultType="java.lang.Long">
        select count(1)
        from
        (select a.sn_no,b.supplies_code,c.code warehouse_code,c.name,c.cost_center_code,b.all_number,a.install_status
        from stock_gps_sns_tail a
        left join stock_supplies_config_detail b on a.sn_no=b.sn_no and b.version = #{version}
        left join stock_warehouse c on b.warehouse_code=c.code
        where 1=1
        <include refid="gps_select_condition1" />) aa
        left join (SELECT
        sdd.real_warehouse_code warehouse_code,
        sdd.supplies_code,
        sdds.SERIAL_NUMBER sn_no,
        count(1) number
        FROM stock_delivery sd
        inner JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        inner join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
        where sd.out_stock_type != 10
        and sd.status=8
        <include refid="gps_select_condition2" />
        group by sdd.real_warehouse_code,sdd.supplies_code,sdds.SERIAL_NUMBER) dd on aa.warehouse_code=dd.warehouse_code and aa.supplies_code=dd.supplies_code
        and aa.sn_no = dd.sn_no
        left join stock_supplies ss on aa.supplies_code=ss.code
        left join stock_supplies_unit ssu ON ss.unit_code = ssu.unit_code
        where 1=1
        <!--  筛选在库的数据 -->
        <if test="configStatus == 0">
            AND aa.all_number > if(dd.number is null,0,dd.number)
        </if>
        <!--  筛选已出库的数据 -->
        <if test="configStatus == 1">
            AND aa.all_number = if(dd.number is null,0,dd.number)
        </if>
    </select>
</mapper>
