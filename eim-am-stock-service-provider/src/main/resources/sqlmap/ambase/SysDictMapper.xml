<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.ambase.SysDictMapper">

    <select id="selectDictListByValue"  resultType="com.gz.eim.am.stock.entity.ambase.SysDict">
        select code,value,name from ambase.sys_dict where code = #{code} and value in
        <foreach collection="values" item="value" index="index" open="(" separator="," close=")">
            #{value}
        </foreach>
    </select>

</mapper>