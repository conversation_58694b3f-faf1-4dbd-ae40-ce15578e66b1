<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.ambase.PsBusLocationTblMapper">

    <!--获取所有省(直辖市)location信息 -->
    <select id="queryProvinceLocationInfo" resultMap="BaseResultMap">
        select location,descr,descrshort,cst_city_level,cst_sup_location
        from  ambase.PS_BUS_LOCATION_TBL
        where cst_city_level='10' or cst_city_level='20'
    </select>


    <!--获取指定省(直辖市)下的城市location信息-->
    <select id="queryCityLocationInfoByProvince"  parameterType="map"  resultMap="BaseResultMap">
        select location,descr,descrshort,cst_city_level,cst_sup_location
        from ambase.PS_BUS_LOCATION_TBL
        where
        <choose>
            <when test="subType == 'all'">
                cst_sup_location like CONCAT(#{provinceLocation,jdbcType=VARCHAR},'%')
            </when>
            <otherwise>
                cst_sup_location=#{provinceLocation,jdbcType=VARCHAR}
            </otherwise>
        </choose>

    </select>

    <!--获取指定城市的location信息-->
    <select id="queryCityInfoByCityName"  resultMap="BaseResultMap">
        select location,descr,descrshort,cst_city_level,cst_sup_location
        from  ambase.PS_BUS_LOCATION_TBL
        where descr=#{cityName,jdbcType=VARCHAR}
    </select>


    <!--获取指定location信息-->
    <select id="queryBusLocation"  resultMap="BaseResultMap">
        select location,descr,descrshort,cst_city_level,cst_sup_location
        from ambase.PS_BUS_LOCATION_TBL
        where 1=1
        <if test="locationList != null and locationList.size() > 0 ">
            and location in
            <foreach collection="locationList" index="index" item="itemLocation" open="(" separator="," close=")">
                #{itemLocation}
            </foreach>
        </if>
    </select>


</mapper>
