<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.ambase.PsBusJobCodeTblMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTbl">
    <id column="JOBCODE" jdbcType="VARCHAR" property="jobcode" />
    <result column="EFF_STATUS" jdbcType="VARCHAR" property="effStatus" />
    <result column="HPS_JOBCD_DESCR" jdbcType="VARCHAR" property="hpsJobcdDescr" />
    <result column="DESCRSHORT" jdbcType="VARCHAR" property="descrshort" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    JOBCODE, EFF_STATUS, HPS_JOBCD_DESCR, DESCRSHORT
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTblExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ambase.ps_bus_jobcode_tbl
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ambase.ps_bus_jobcode_tbl
    where JOBCODE = #{jobcode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from ambase.ps_bus_jobcode_tbl
    where JOBCODE = #{jobcode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTbl">
    insert into ambase.ps_bus_jobcode_tbl (JOBCODE, EFF_STATUS, HPS_JOBCD_DESCR,
      DESCRSHORT)
    values (#{jobcode,jdbcType=VARCHAR}, #{effStatus,jdbcType=VARCHAR}, #{hpsJobcdDescr,jdbcType=VARCHAR}, 
      #{descrshort,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTbl">
    insert into ambase.ps_bus_jobcode_tbl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jobcode != null">
        JOBCODE,
      </if>
      <if test="effStatus != null">
        EFF_STATUS,
      </if>
      <if test="hpsJobcdDescr != null">
        HPS_JOBCD_DESCR,
      </if>
      <if test="descrshort != null">
        DESCRSHORT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jobcode != null">
        #{jobcode,jdbcType=VARCHAR},
      </if>
      <if test="effStatus != null">
        #{effStatus,jdbcType=VARCHAR},
      </if>
      <if test="hpsJobcdDescr != null">
        #{hpsJobcdDescr,jdbcType=VARCHAR},
      </if>
      <if test="descrshort != null">
        #{descrshort,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTblExample" resultType="java.lang.Long">
    select count(*) from ambase.ps_bus_jobcode_tbl
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ambase.ps_bus_jobcode_tbl
    <set>
      <if test="record.jobcode != null">
        JOBCODE = #{record.jobcode,jdbcType=VARCHAR},
      </if>
      <if test="record.effStatus != null">
        EFF_STATUS = #{record.effStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.hpsJobcdDescr != null">
        HPS_JOBCD_DESCR = #{record.hpsJobcdDescr,jdbcType=VARCHAR},
      </if>
      <if test="record.descrshort != null">
        DESCRSHORT = #{record.descrshort,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ambase.ps_bus_jobcode_tbl
    set JOBCODE = #{record.jobcode,jdbcType=VARCHAR},
      EFF_STATUS = #{record.effStatus,jdbcType=VARCHAR},
      HPS_JOBCD_DESCR = #{record.hpsJobcdDescr,jdbcType=VARCHAR},
      DESCRSHORT = #{record.descrshort,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTbl">
    update ambase.ps_bus_jobcode_tbl
    <set>
      <if test="effStatus != null">
        EFF_STATUS = #{effStatus,jdbcType=VARCHAR},
      </if>
      <if test="hpsJobcdDescr != null">
        HPS_JOBCD_DESCR = #{hpsJobcdDescr,jdbcType=VARCHAR},
      </if>
      <if test="descrshort != null">
        DESCRSHORT = #{descrshort,jdbcType=VARCHAR},
      </if>
    </set>
    where JOBCODE = #{jobcode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.ambase.PsBusJobCodeTbl">
    update ambase.ps_bus_jobcode_tbl
    set EFF_STATUS = #{effStatus,jdbcType=VARCHAR},
      HPS_JOBCD_DESCR = #{hpsJobcdDescr,jdbcType=VARCHAR},
      DESCRSHORT = #{descrshort,jdbcType=VARCHAR}
    where JOBCODE = #{jobcode,jdbcType=VARCHAR}
  </update>
</mapper>