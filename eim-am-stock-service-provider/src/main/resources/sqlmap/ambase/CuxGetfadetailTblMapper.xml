<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.ambase.CuxGetfadetailTblMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl">
    <id column="ASSET_NUMBER" jdbcType="VARCHAR" property="assetNumber" />
    <result column="GS_NAME" jdbcType="VARCHAR" property="gsName" />
    <result column="TAG_NUMBER" jdbcType="VARCHAR" property="tagNumber" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="PERIOD_NAME" jdbcType="VARCHAR" property="periodName" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="FIMS_CLASS_B" jdbcType="VARCHAR" property="fimsClassB" />
    <result column="FIMS_CLASS_B_NAME" jdbcType="VARCHAR" property="fimsClassBName" />
    <result column="FIMS_CLASS_L" jdbcType="VARCHAR" property="fimsClassL" />
    <result column="FIMS_CLASS_L_NAME" jdbcType="VARCHAR" property="fimsClassLName" />
    <result column="FA_KEY_WORD" jdbcType="VARCHAR" property="faKeyWord" />
    <result column="MODEL_NUMBER" jdbcType="VARCHAR" property="modelNumber" />
    <result column="MANUFACTURER_NAME" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="RETIRED_TYPE" jdbcType="VARCHAR" property="retiredType" />
    <result column="RETIRED_DATE" jdbcType="VARCHAR" property="retiredDate" />
    <result column="FLSEGMENT1" jdbcType="VARCHAR" property="flsegment1" />
    <result column="USED_COMPANY" jdbcType="VARCHAR" property="usedCompany" />
    <result column="FLSEGMENT2" jdbcType="VARCHAR" property="flsegment2" />
    <result column="USED_DEPARTMENT" jdbcType="VARCHAR" property="usedDepartment" />
    <result column="FLSEGMENT3" jdbcType="VARCHAR" property="flsegment3" />
    <result column="FULL_NAME" jdbcType="VARCHAR" property="fullName" />
    <result column="ADDITION_DATE" jdbcType="VARCHAR" property="additionDate" />
    <result column="DATE_PLACED_IN_SERVICE" jdbcType="VARCHAR" property="datePlacedInService" />
    <result column="LIFE_IN_YEAR" jdbcType="INTEGER" property="lifeInYear" />
    <result column="DEPRN_METHOD_CODE" jdbcType="VARCHAR" property="deprnMethodCode" />
    <result column="COST" jdbcType="DECIMAL" property="cost" />
    <result column="DEPRN_AMOUNT" jdbcType="DECIMAL" property="deprnAmount" />
    <result column="YTD_DEPRN" jdbcType="DECIMAL" property="ytdDeprn" />
    <result column="DEPRN_RESERVE" jdbcType="DECIMAL" property="deprnReserve" />
    <result column="LAST_COST" jdbcType="DECIMAL" property="lastCost" />
    <result column="DEPRN_TYPE" jdbcType="VARCHAR" property="deprnType" />
    <result column="SURPLUS_YEAR" jdbcType="VARCHAR" property="surplusYear" />
    <result column="PZ_NO" jdbcType="BIGINT" property="pzNo" />
    <result column="CONCATENATED_SEGMENTS" jdbcType="VARCHAR" property="concatenatedSegments" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ASSET_NUMBER, GS_NAME, TAG_NUMBER, SERIAL_NUMBER, PERIOD_NAME, DESCRIPTION, FIMS_CLASS_B, 
    FIMS_CLASS_B_NAME, FIMS_CLASS_L, FIMS_CLASS_L_NAME, FA_KEY_WORD, MODEL_NUMBER, MANUFACTURER_NAME, 
    RETIRED_TYPE, RETIRED_DATE, FLSEGMENT1, USED_COMPANY, FLSEGMENT2, USED_DEPARTMENT, 
    FLSEGMENT3, FULL_NAME, ADDITION_DATE, DATE_PLACED_IN_SERVICE, LIFE_IN_YEAR, DEPRN_METHOD_CODE, 
    COST, DEPRN_AMOUNT, YTD_DEPRN, DEPRN_RESERVE, LAST_COST, DEPRN_TYPE, SURPLUS_YEAR, 
    PZ_NO, CONCATENATED_SEGMENTS
  </sql>
  <select id="selectByExample" parameterType="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTblExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ambase.cux_getfadetail_tbl
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ambase.cux_getfadetail_tbl
    where ASSET_NUMBER = #{assetNumber,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from ambase.cux_getfadetail_tbl
    where ASSET_NUMBER = #{assetNumber,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl">
    insert into ambase.cux_getfadetail_tbl (ASSET_NUMBER, GS_NAME, TAG_NUMBER, 
      SERIAL_NUMBER, PERIOD_NAME, DESCRIPTION, 
      FIMS_CLASS_B, FIMS_CLASS_B_NAME, FIMS_CLASS_L, 
      FIMS_CLASS_L_NAME, FA_KEY_WORD, MODEL_NUMBER, 
      MANUFACTURER_NAME, RETIRED_TYPE, RETIRED_DATE, 
      FLSEGMENT1, USED_COMPANY, FLSEGMENT2, 
      USED_DEPARTMENT, FLSEGMENT3, FULL_NAME, 
      ADDITION_DATE, DATE_PLACED_IN_SERVICE, LIFE_IN_YEAR, 
      DEPRN_METHOD_CODE, COST, DEPRN_AMOUNT, 
      YTD_DEPRN, DEPRN_RESERVE, LAST_COST, 
      DEPRN_TYPE, SURPLUS_YEAR, PZ_NO, 
      CONCATENATED_SEGMENTS)
    values (#{assetNumber,jdbcType=VARCHAR}, #{gsName,jdbcType=VARCHAR}, #{tagNumber,jdbcType=VARCHAR}, 
      #{serialNumber,jdbcType=VARCHAR}, #{periodName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{fimsClassB,jdbcType=VARCHAR}, #{fimsClassBName,jdbcType=VARCHAR}, #{fimsClassL,jdbcType=VARCHAR}, 
      #{fimsClassLName,jdbcType=VARCHAR}, #{faKeyWord,jdbcType=VARCHAR}, #{modelNumber,jdbcType=VARCHAR}, 
      #{manufacturerName,jdbcType=VARCHAR}, #{retiredType,jdbcType=VARCHAR}, #{retiredDate,jdbcType=VARCHAR}, 
      #{flsegment1,jdbcType=VARCHAR}, #{usedCompany,jdbcType=VARCHAR}, #{flsegment2,jdbcType=VARCHAR}, 
      #{usedDepartment,jdbcType=VARCHAR}, #{flsegment3,jdbcType=VARCHAR}, #{fullName,jdbcType=VARCHAR}, 
      #{additionDate,jdbcType=VARCHAR}, #{datePlacedInService,jdbcType=VARCHAR}, #{lifeInYear,jdbcType=INTEGER}, 
      #{deprnMethodCode,jdbcType=VARCHAR}, #{cost,jdbcType=DECIMAL}, #{deprnAmount,jdbcType=DECIMAL}, 
      #{ytdDeprn,jdbcType=DECIMAL}, #{deprnReserve,jdbcType=DECIMAL}, #{lastCost,jdbcType=DECIMAL}, 
      #{deprnType,jdbcType=VARCHAR}, #{surplusYear,jdbcType=VARCHAR}, #{pzNo,jdbcType=BIGINT}, 
      #{concatenatedSegments,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl">
    insert into ambase.cux_getfadetail_tbl
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="assetNumber != null">
        ASSET_NUMBER,
      </if>
      <if test="gsName != null">
        GS_NAME,
      </if>
      <if test="tagNumber != null">
        TAG_NUMBER,
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER,
      </if>
      <if test="periodName != null">
        PERIOD_NAME,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="fimsClassB != null">
        FIMS_CLASS_B,
      </if>
      <if test="fimsClassBName != null">
        FIMS_CLASS_B_NAME,
      </if>
      <if test="fimsClassL != null">
        FIMS_CLASS_L,
      </if>
      <if test="fimsClassLName != null">
        FIMS_CLASS_L_NAME,
      </if>
      <if test="faKeyWord != null">
        FA_KEY_WORD,
      </if>
      <if test="modelNumber != null">
        MODEL_NUMBER,
      </if>
      <if test="manufacturerName != null">
        MANUFACTURER_NAME,
      </if>
      <if test="retiredType != null">
        RETIRED_TYPE,
      </if>
      <if test="retiredDate != null">
        RETIRED_DATE,
      </if>
      <if test="flsegment1 != null">
        FLSEGMENT1,
      </if>
      <if test="usedCompany != null">
        USED_COMPANY,
      </if>
      <if test="flsegment2 != null">
        FLSEGMENT2,
      </if>
      <if test="usedDepartment != null">
        USED_DEPARTMENT,
      </if>
      <if test="flsegment3 != null">
        FLSEGMENT3,
      </if>
      <if test="fullName != null">
        FULL_NAME,
      </if>
      <if test="additionDate != null">
        ADDITION_DATE,
      </if>
      <if test="datePlacedInService != null">
        DATE_PLACED_IN_SERVICE,
      </if>
      <if test="lifeInYear != null">
        LIFE_IN_YEAR,
      </if>
      <if test="deprnMethodCode != null">
        DEPRN_METHOD_CODE,
      </if>
      <if test="cost != null">
        COST,
      </if>
      <if test="deprnAmount != null">
        DEPRN_AMOUNT,
      </if>
      <if test="ytdDeprn != null">
        YTD_DEPRN,
      </if>
      <if test="deprnReserve != null">
        DEPRN_RESERVE,
      </if>
      <if test="lastCost != null">
        LAST_COST,
      </if>
      <if test="deprnType != null">
        DEPRN_TYPE,
      </if>
      <if test="surplusYear != null">
        SURPLUS_YEAR,
      </if>
      <if test="pzNo != null">
        PZ_NO,
      </if>
      <if test="concatenatedSegments != null">
        CONCATENATED_SEGMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="assetNumber != null">
        #{assetNumber,jdbcType=VARCHAR},
      </if>
      <if test="gsName != null">
        #{gsName,jdbcType=VARCHAR},
      </if>
      <if test="tagNumber != null">
        #{tagNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="periodName != null">
        #{periodName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassB != null">
        #{fimsClassB,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassBName != null">
        #{fimsClassBName,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassL != null">
        #{fimsClassL,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassLName != null">
        #{fimsClassLName,jdbcType=VARCHAR},
      </if>
      <if test="faKeyWord != null">
        #{faKeyWord,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null">
        #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="retiredType != null">
        #{retiredType,jdbcType=VARCHAR},
      </if>
      <if test="retiredDate != null">
        #{retiredDate,jdbcType=VARCHAR},
      </if>
      <if test="flsegment1 != null">
        #{flsegment1,jdbcType=VARCHAR},
      </if>
      <if test="usedCompany != null">
        #{usedCompany,jdbcType=VARCHAR},
      </if>
      <if test="flsegment2 != null">
        #{flsegment2,jdbcType=VARCHAR},
      </if>
      <if test="usedDepartment != null">
        #{usedDepartment,jdbcType=VARCHAR},
      </if>
      <if test="flsegment3 != null">
        #{flsegment3,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null">
        #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="additionDate != null">
        #{additionDate,jdbcType=VARCHAR},
      </if>
      <if test="datePlacedInService != null">
        #{datePlacedInService,jdbcType=VARCHAR},
      </if>
      <if test="lifeInYear != null">
        #{lifeInYear,jdbcType=INTEGER},
      </if>
      <if test="deprnMethodCode != null">
        #{deprnMethodCode,jdbcType=VARCHAR},
      </if>
      <if test="cost != null">
        #{cost,jdbcType=DECIMAL},
      </if>
      <if test="deprnAmount != null">
        #{deprnAmount,jdbcType=DECIMAL},
      </if>
      <if test="ytdDeprn != null">
        #{ytdDeprn,jdbcType=DECIMAL},
      </if>
      <if test="deprnReserve != null">
        #{deprnReserve,jdbcType=DECIMAL},
      </if>
      <if test="lastCost != null">
        #{lastCost,jdbcType=DECIMAL},
      </if>
      <if test="deprnType != null">
        #{deprnType,jdbcType=VARCHAR},
      </if>
      <if test="surplusYear != null">
        #{surplusYear,jdbcType=VARCHAR},
      </if>
      <if test="pzNo != null">
        #{pzNo,jdbcType=BIGINT},
      </if>
      <if test="concatenatedSegments != null">
        #{concatenatedSegments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTblExample" resultType="java.lang.Long">
    select count(*) from ambase.cux_getfadetail_tbl
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ambase.cux_getfadetail_tbl
    <set>
      <if test="record.assetNumber != null">
        ASSET_NUMBER = #{record.assetNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.gsName != null">
        GS_NAME = #{record.gsName,jdbcType=VARCHAR},
      </if>
      <if test="record.tagNumber != null">
        TAG_NUMBER = #{record.tagNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNumber != null">
        SERIAL_NUMBER = #{record.serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.periodName != null">
        PERIOD_NAME = #{record.periodName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.fimsClassB != null">
        FIMS_CLASS_B = #{record.fimsClassB,jdbcType=VARCHAR},
      </if>
      <if test="record.fimsClassBName != null">
        FIMS_CLASS_B_NAME = #{record.fimsClassBName,jdbcType=VARCHAR},
      </if>
      <if test="record.fimsClassL != null">
        FIMS_CLASS_L = #{record.fimsClassL,jdbcType=VARCHAR},
      </if>
      <if test="record.fimsClassLName != null">
        FIMS_CLASS_L_NAME = #{record.fimsClassLName,jdbcType=VARCHAR},
      </if>
      <if test="record.faKeyWord != null">
        FA_KEY_WORD = #{record.faKeyWord,jdbcType=VARCHAR},
      </if>
      <if test="record.modelNumber != null">
        MODEL_NUMBER = #{record.modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturerName != null">
        MANUFACTURER_NAME = #{record.manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="record.retiredType != null">
        RETIRED_TYPE = #{record.retiredType,jdbcType=VARCHAR},
      </if>
      <if test="record.retiredDate != null">
        RETIRED_DATE = #{record.retiredDate,jdbcType=VARCHAR},
      </if>
      <if test="record.flsegment1 != null">
        FLSEGMENT1 = #{record.flsegment1,jdbcType=VARCHAR},
      </if>
      <if test="record.usedCompany != null">
        USED_COMPANY = #{record.usedCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.flsegment2 != null">
        FLSEGMENT2 = #{record.flsegment2,jdbcType=VARCHAR},
      </if>
      <if test="record.usedDepartment != null">
        USED_DEPARTMENT = #{record.usedDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.flsegment3 != null">
        FLSEGMENT3 = #{record.flsegment3,jdbcType=VARCHAR},
      </if>
      <if test="record.fullName != null">
        FULL_NAME = #{record.fullName,jdbcType=VARCHAR},
      </if>
      <if test="record.additionDate != null">
        ADDITION_DATE = #{record.additionDate,jdbcType=VARCHAR},
      </if>
      <if test="record.datePlacedInService != null">
        DATE_PLACED_IN_SERVICE = #{record.datePlacedInService,jdbcType=VARCHAR},
      </if>
      <if test="record.lifeInYear != null">
        LIFE_IN_YEAR = #{record.lifeInYear,jdbcType=INTEGER},
      </if>
      <if test="record.deprnMethodCode != null">
        DEPRN_METHOD_CODE = #{record.deprnMethodCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cost != null">
        COST = #{record.cost,jdbcType=DECIMAL},
      </if>
      <if test="record.deprnAmount != null">
        DEPRN_AMOUNT = #{record.deprnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.ytdDeprn != null">
        YTD_DEPRN = #{record.ytdDeprn,jdbcType=DECIMAL},
      </if>
      <if test="record.deprnReserve != null">
        DEPRN_RESERVE = #{record.deprnReserve,jdbcType=DECIMAL},
      </if>
      <if test="record.lastCost != null">
        LAST_COST = #{record.lastCost,jdbcType=DECIMAL},
      </if>
      <if test="record.deprnType != null">
        DEPRN_TYPE = #{record.deprnType,jdbcType=VARCHAR},
      </if>
      <if test="record.surplusYear != null">
        SURPLUS_YEAR = #{record.surplusYear,jdbcType=VARCHAR},
      </if>
      <if test="record.pzNo != null">
        PZ_NO = #{record.pzNo,jdbcType=BIGINT},
      </if>
      <if test="record.concatenatedSegments != null">
        CONCATENATED_SEGMENTS = #{record.concatenatedSegments,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ambase.cux_getfadetail_tbl
    set ASSET_NUMBER = #{record.assetNumber,jdbcType=VARCHAR},
      GS_NAME = #{record.gsName,jdbcType=VARCHAR},
      TAG_NUMBER = #{record.tagNumber,jdbcType=VARCHAR},
      SERIAL_NUMBER = #{record.serialNumber,jdbcType=VARCHAR},
      PERIOD_NAME = #{record.periodName,jdbcType=VARCHAR},
      DESCRIPTION = #{record.description,jdbcType=VARCHAR},
      FIMS_CLASS_B = #{record.fimsClassB,jdbcType=VARCHAR},
      FIMS_CLASS_B_NAME = #{record.fimsClassBName,jdbcType=VARCHAR},
      FIMS_CLASS_L = #{record.fimsClassL,jdbcType=VARCHAR},
      FIMS_CLASS_L_NAME = #{record.fimsClassLName,jdbcType=VARCHAR},
      FA_KEY_WORD = #{record.faKeyWord,jdbcType=VARCHAR},
      MODEL_NUMBER = #{record.modelNumber,jdbcType=VARCHAR},
      MANUFACTURER_NAME = #{record.manufacturerName,jdbcType=VARCHAR},
      RETIRED_TYPE = #{record.retiredType,jdbcType=VARCHAR},
      RETIRED_DATE = #{record.retiredDate,jdbcType=VARCHAR},
      FLSEGMENT1 = #{record.flsegment1,jdbcType=VARCHAR},
      USED_COMPANY = #{record.usedCompany,jdbcType=VARCHAR},
      FLSEGMENT2 = #{record.flsegment2,jdbcType=VARCHAR},
      USED_DEPARTMENT = #{record.usedDepartment,jdbcType=VARCHAR},
      FLSEGMENT3 = #{record.flsegment3,jdbcType=VARCHAR},
      FULL_NAME = #{record.fullName,jdbcType=VARCHAR},
      ADDITION_DATE = #{record.additionDate,jdbcType=VARCHAR},
      DATE_PLACED_IN_SERVICE = #{record.datePlacedInService,jdbcType=VARCHAR},
      LIFE_IN_YEAR = #{record.lifeInYear,jdbcType=INTEGER},
      DEPRN_METHOD_CODE = #{record.deprnMethodCode,jdbcType=VARCHAR},
      COST = #{record.cost,jdbcType=DECIMAL},
      DEPRN_AMOUNT = #{record.deprnAmount,jdbcType=DECIMAL},
      YTD_DEPRN = #{record.ytdDeprn,jdbcType=DECIMAL},
      DEPRN_RESERVE = #{record.deprnReserve,jdbcType=DECIMAL},
      LAST_COST = #{record.lastCost,jdbcType=DECIMAL},
      DEPRN_TYPE = #{record.deprnType,jdbcType=VARCHAR},
      SURPLUS_YEAR = #{record.surplusYear,jdbcType=VARCHAR},
      PZ_NO = #{record.pzNo,jdbcType=BIGINT},
      CONCATENATED_SEGMENTS = #{record.concatenatedSegments,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl">
    update ambase.cux_getfadetail_tbl
    <set>
      <if test="gsName != null">
        GS_NAME = #{gsName,jdbcType=VARCHAR},
      </if>
      <if test="tagNumber != null">
        TAG_NUMBER = #{tagNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="periodName != null">
        PERIOD_NAME = #{periodName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassB != null">
        FIMS_CLASS_B = #{fimsClassB,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassBName != null">
        FIMS_CLASS_B_NAME = #{fimsClassBName,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassL != null">
        FIMS_CLASS_L = #{fimsClassL,jdbcType=VARCHAR},
      </if>
      <if test="fimsClassLName != null">
        FIMS_CLASS_L_NAME = #{fimsClassLName,jdbcType=VARCHAR},
      </if>
      <if test="faKeyWord != null">
        FA_KEY_WORD = #{faKeyWord,jdbcType=VARCHAR},
      </if>
      <if test="modelNumber != null">
        MODEL_NUMBER = #{modelNumber,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="retiredType != null">
        RETIRED_TYPE = #{retiredType,jdbcType=VARCHAR},
      </if>
      <if test="retiredDate != null">
        RETIRED_DATE = #{retiredDate,jdbcType=VARCHAR},
      </if>
      <if test="flsegment1 != null">
        FLSEGMENT1 = #{flsegment1,jdbcType=VARCHAR},
      </if>
      <if test="usedCompany != null">
        USED_COMPANY = #{usedCompany,jdbcType=VARCHAR},
      </if>
      <if test="flsegment2 != null">
        FLSEGMENT2 = #{flsegment2,jdbcType=VARCHAR},
      </if>
      <if test="usedDepartment != null">
        USED_DEPARTMENT = #{usedDepartment,jdbcType=VARCHAR},
      </if>
      <if test="flsegment3 != null">
        FLSEGMENT3 = #{flsegment3,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null">
        FULL_NAME = #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="additionDate != null">
        ADDITION_DATE = #{additionDate,jdbcType=VARCHAR},
      </if>
      <if test="datePlacedInService != null">
        DATE_PLACED_IN_SERVICE = #{datePlacedInService,jdbcType=VARCHAR},
      </if>
      <if test="lifeInYear != null">
        LIFE_IN_YEAR = #{lifeInYear,jdbcType=INTEGER},
      </if>
      <if test="deprnMethodCode != null">
        DEPRN_METHOD_CODE = #{deprnMethodCode,jdbcType=VARCHAR},
      </if>
      <if test="cost != null">
        COST = #{cost,jdbcType=DECIMAL},
      </if>
      <if test="deprnAmount != null">
        DEPRN_AMOUNT = #{deprnAmount,jdbcType=DECIMAL},
      </if>
      <if test="ytdDeprn != null">
        YTD_DEPRN = #{ytdDeprn,jdbcType=DECIMAL},
      </if>
      <if test="deprnReserve != null">
        DEPRN_RESERVE = #{deprnReserve,jdbcType=DECIMAL},
      </if>
      <if test="lastCost != null">
        LAST_COST = #{lastCost,jdbcType=DECIMAL},
      </if>
      <if test="deprnType != null">
        DEPRN_TYPE = #{deprnType,jdbcType=VARCHAR},
      </if>
      <if test="surplusYear != null">
        SURPLUS_YEAR = #{surplusYear,jdbcType=VARCHAR},
      </if>
      <if test="pzNo != null">
        PZ_NO = #{pzNo,jdbcType=BIGINT},
      </if>
      <if test="concatenatedSegments != null">
        CONCATENATED_SEGMENTS = #{concatenatedSegments,jdbcType=VARCHAR},
      </if>
    </set>
    where ASSET_NUMBER = #{assetNumber,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.gz.eim.am.stock.entity.ambase.CuxGetfadetailTbl">
    update ambase.cux_getfadetail_tbl
    set GS_NAME = #{gsName,jdbcType=VARCHAR},
      TAG_NUMBER = #{tagNumber,jdbcType=VARCHAR},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      PERIOD_NAME = #{periodName,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      FIMS_CLASS_B = #{fimsClassB,jdbcType=VARCHAR},
      FIMS_CLASS_B_NAME = #{fimsClassBName,jdbcType=VARCHAR},
      FIMS_CLASS_L = #{fimsClassL,jdbcType=VARCHAR},
      FIMS_CLASS_L_NAME = #{fimsClassLName,jdbcType=VARCHAR},
      FA_KEY_WORD = #{faKeyWord,jdbcType=VARCHAR},
      MODEL_NUMBER = #{modelNumber,jdbcType=VARCHAR},
      MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      RETIRED_TYPE = #{retiredType,jdbcType=VARCHAR},
      RETIRED_DATE = #{retiredDate,jdbcType=VARCHAR},
      FLSEGMENT1 = #{flsegment1,jdbcType=VARCHAR},
      USED_COMPANY = #{usedCompany,jdbcType=VARCHAR},
      FLSEGMENT2 = #{flsegment2,jdbcType=VARCHAR},
      USED_DEPARTMENT = #{usedDepartment,jdbcType=VARCHAR},
      FLSEGMENT3 = #{flsegment3,jdbcType=VARCHAR},
      FULL_NAME = #{fullName,jdbcType=VARCHAR},
      ADDITION_DATE = #{additionDate,jdbcType=VARCHAR},
      DATE_PLACED_IN_SERVICE = #{datePlacedInService,jdbcType=VARCHAR},
      LIFE_IN_YEAR = #{lifeInYear,jdbcType=INTEGER},
      DEPRN_METHOD_CODE = #{deprnMethodCode,jdbcType=VARCHAR},
      COST = #{cost,jdbcType=DECIMAL},
      DEPRN_AMOUNT = #{deprnAmount,jdbcType=DECIMAL},
      YTD_DEPRN = #{ytdDeprn,jdbcType=DECIMAL},
      DEPRN_RESERVE = #{deprnReserve,jdbcType=DECIMAL},
      LAST_COST = #{lastCost,jdbcType=DECIMAL},
      DEPRN_TYPE = #{deprnType,jdbcType=VARCHAR},
      SURPLUS_YEAR = #{surplusYear,jdbcType=VARCHAR},
      PZ_NO = #{pzNo,jdbcType=BIGINT},
      CONCATENATED_SEGMENTS = #{concatenatedSegments,jdbcType=VARCHAR}
    where ASSET_NUMBER = #{assetNumber,jdbcType=VARCHAR}
  </update>
</mapper>