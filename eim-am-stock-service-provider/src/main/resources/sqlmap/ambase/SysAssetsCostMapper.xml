<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.ambase.SysAssetsCostMapper">

    <select id="selectCostByAssetsCode" parameterType="java.lang.String" resultType="com.gz.eim.am.stock.dto.response.assets.AssetsCostRespDTO">
        select tag_number as assetsCode,DESCRIPTION as assetsName,SERIAL_NUMBER as snNo,  LIFE_IN_YEAR as lifeYear,cost,
        LAST_COST as lastCost,SURPLUS_YEAR as surplusYear from ambase.cux_getfadetail_tbl where tag_number in
        <foreach collection="assetsCodes" item="item" index="index" open=" (" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>