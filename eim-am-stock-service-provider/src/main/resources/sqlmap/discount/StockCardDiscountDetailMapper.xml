<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.discount.StockCardDiscountDetailMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="discount_no" jdbcType="VARCHAR" property="discountNo" />
        <result column="convert_supplies_code" jdbcType="VARCHAR" property="convertSuppliesCode" />
        <result column="switch_supplies_code" jdbcType="VARCHAR" property="switchSuppliesCode" />
        <result column="convert_number" jdbcType="INTEGER" property="convertNumber" />
        <result column="convert_result" jdbcType="DECIMAL" property="convertResult" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>


    <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockCardSuppliesDiscountDetail"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into stock_card_supplies_discount_detail (id, discount_no, convert_supplies_code,
        switch_supplies_code,
        convert_number, convert_result, created_by,
        created_at, updated_by, updated_at
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.discountNo,jdbcType=VARCHAR},
            #{item.convertSuppliesCode,jdbcType=VARCHAR},
            #{item.switchSuppliesCode,jdbcType=VARCHAR},
            #{item.convertNumber,jdbcType=INTEGER}, #{item.convertResult,jdbcType=DECIMAL},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedAt,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
