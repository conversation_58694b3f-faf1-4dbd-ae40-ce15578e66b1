<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.inventory.InventoryInSuppliesAssetMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInSuppliesAsset">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="in_supplies_id" jdbcType="BIGINT" property="inSuppliesId"/>
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
    </resultMap>

    <select id="selectByPage" parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO">
        select
        siisa.assets_code assetCode,
        sa.sn_code snNo
        from
        stock_inventory_in_supplies_assets siisa
        join stock_inventory_in_supplies siis on siisa.in_supplies_id = siis.in_supplies_id
        left join stock_assets sa on siisa.assets_code = sa.assets_code
        where
        siis.inventory_in_plan_line_id = #{inventoryInPlanLineId}
        <if test="assetsCode != null and assetsCode != ''">
            and siisa.assets_code = #{assetsCode}
        </if>
        order by siisa.id
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <insert id="insertMultiple" parameterType="com.gz.eim.am.stock.entity.StockInventoryInSuppliesAsset">
        insert into stock_inventory_in_supplies_assets (in_supplies_id, assets_code,
        CREATED_BY, UPDATED_BY, use_user_dept)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.inSuppliesId,jdbcType=BIGINT},
            #{item.assetsCode,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.useUserDept,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectCountByParam"
            parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from
        stock_inventory_in_supplies_assets siisa,
        stock_inventory_in_supplies siis
        where siisa.in_supplies_id = siis.in_supplies_id
        and siis.inventory_in_plan_line_id = #{inventoryInPlanLineId}
        <if test="assetsCode != null and assetsCode != ''">
            and siisa.assets_code = #{assetsCode}
        </if>
    </select>
</mapper>