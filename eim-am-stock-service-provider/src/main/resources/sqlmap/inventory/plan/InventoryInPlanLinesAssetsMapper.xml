<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.inventory.plan.InventoryInPlanLinesAssetsMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="inventory_in_plan_head_id" jdbcType="BIGINT" property="inventoryInPlanHeadId" />
        <result column="inventory_in_plan_line_id" jdbcType="BIGINT" property="inventoryInPlanLineId" />
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
        <result column="in_stock_status" jdbcType="TINYINT" property="inStockStatus" />
        <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
        <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
    </resultMap>


    <select id="selectInventoryPlanAssetRespDTO" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets" resultType="com.gz.eim.am.stock.dto.response.inventory.plan.InventoryPlanAssetRespDTO">
        select
        siipl.assets_code assetsCode,
        sa.assets_name assetsName,
        sa.supplies_code suppliesCode,
        sa.status,
        sd.dept_name needDeptName,
        su.name assetsKeeperName,
        sa.sn_code snCode
        from stock_inventory_in_plan_lines_assets siipl
        left join stock_assets sa on siipl.assets_code = sa.assets_code
        left join ambase.sys_dept sd on sd.dept_id = sa.need_dept
        left join ambase.sys_user su on su.emp_id = sa.assets_keeper
        where 1=1
        <!-- 根据需要进行添加 -->
        <if test="inventoryInPlanHeadId != null ">
            and siipl.inventory_in_plan_head_id = #{inventoryInPlanHeadId}
        </if>
        <if test="inStockStatus != null">
            and siipl.in_stock_status = #{inStockStatus}
        </if>
        <if test="assetsCode != null and assetsCode != ''">
            and siipl.assets_code = #{assetsCode}
        </if>
        <if test="id != null ">
            and siipl.id = #{id}
        </if>
        order by siipl.id
    </select>

    <insert id="batchInsertStockInventoryPlanLineAssets" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        insert into stock_inventory_in_plan_lines_assets (inventory_in_plan_head_id, inventory_in_plan_line_id,
        assets_code, in_stock_status, data_status,
        del_flag, CREATED_BY, CREATED_AT,
        UPDATED_BY, UPDATED_AT)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.inventoryInPlanHeadId,jdbcType=BIGINT}, #{item.inventoryInPlanLineId,jdbcType=BIGINT},
            #{item.assetsCode,jdbcType=VARCHAR}, #{item.inStockStatus,jdbcType=TINYINT}, #{item.dataStatus,jdbcType=TINYINT},
            #{item.delFlag,jdbcType=TINYINT}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="selectByPage" parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO">
        select
        siisa.assets_code assetCode,
        sa.sn_code snNo,
        sa.assets_name assetsName
        from
        stock_inventory_in_plan_lines_assets siisa
        left join stock_assets sa on siisa.assets_code = sa.assets_code
        where
        siisa.inventory_in_plan_line_id = #{inventoryInPlanLineId}
        <if test="assetsCode != null and assetsCode != ''">
            and siisa.assets_code = #{assetsCode}
        </if>
        order by siisa.id
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectCountByParam"
            parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from
        stock_inventory_in_plan_lines_assets siisa
        where siisa.inventory_in_plan_line_id = #{inventoryInPlanLineId}
        <if test="assetsCode != null and assetsCode != ''">
            and siisa.assets_code = #{assetsCode}
        </if>
    </select>

    <select id="selectLineAssets" parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanLineAssetRespDTO">
        select DISTINCT(a.id), a.id ,a.assets_code as assetsCode,b.assets_name as assetsName,b.brand,b.model, a.in_stock_status as status,
        c.assets_condition as assetsCondition,c.deal_type as dealType,c.real_in_warehouse as realInWarehouse,d.name as realInWarehouseName,
        c.duty_body as dutyBody,c.person_liable as personLiable,c.amount,c.remark,c.pay_status as payStatus,a.CREATED_AT as outTime,b.explain_remark as explainRemark
        from stock_inventory_in_plan_lines_assets a
        inner join stock_assets b on a.assets_code=b.assets_code
        left join stock_inventory_in_assets_conditions c on a.id=c.inventory_in_plan_line_assets_id
        left join stock_warehouse d on c.real_in_warehouse = d.code
        <where>
            a.inventory_in_plan_head_id=#{inventoryInPlanHeadId}
            <if test="null != assets and assets.size > 0">
                and a.assets_code in
                <foreach collection="assets" item="assetsCode" separator="," open="(" close=")">
                    #{assetsCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        order by a.in_stock_status,a.id
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectLineAssetsCountByParam"
            parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanLineReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from
        stock_inventory_in_plan_lines_assets siisa
        <where>
            siisa.inventory_in_plan_head_id = #{inventoryInPlanHeadId}
            <if test="null != assets and assets.size > 0">
                and siisa.assets_code in
                <foreach collection="assets" item="assetsCode" separator="," open="(" close=")">
                    #{assetsCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <update id="batchUpdateStockInventoryPlanLineAssets" >
        update stock_inventory_in_plan_lines_assets
        set in_stock_status = #{status,jdbcType=TINYINT},
        UPDATED_BY = #{empId,jdbcType=VARCHAR},
        UPDATED_AT = SYSDATE()
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanLinesAssets">
        <foreach collection="list" item="item" separator=";">
            update stock_inventory_in_plan_lines_assets
            <set>
                <if test="item.inventoryInPlanHeadId != null">
                    inventory_in_plan_head_id = #{item.inventoryInPlanHeadId,jdbcType=BIGINT},
                </if>
                <if test="item.inventoryInPlanLineId != null">
                    inventory_in_plan_line_id = #{item.inventoryInPlanLineId,jdbcType=BIGINT},
                </if>
                <if test="item.assetsCode != null">
                    assets_code = #{item.assetsCode,jdbcType=VARCHAR},
                </if>
                <if test="item.inStockStatus != null">
                    in_stock_status = #{item.inStockStatus,jdbcType=TINYINT},
                </if>
                <if test="item.dataStatus != null">
                    data_status = #{item.dataStatus,jdbcType=TINYINT},
                </if>
                <if test="item.delFlag != null">
                    del_flag = #{item.delFlag,jdbcType=TINYINT},
                </if>
                <if test="item.createdBy != null">
                    CREATED_BY = #{item.createdBy,jdbcType=VARCHAR},
                </if>
                <if test="item.createdAt != null">
                    CREATED_AT = #{item.createdAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updatedBy != null">
                    UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.updatedAt != null">
                    UPDATED_AT = #{item.updatedAt,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>
