<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.inventory.plan.InventoryInPlanHeadMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryInPlanHead">
        <id column="inventory_in_plan_head_id" jdbcType="BIGINT" property="inventoryInPlanHeadId"/>
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo"/>
        <result column="inventory_in_plan_no" jdbcType="VARCHAR" property="inventoryInPlanNo"/>
        <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode"/>
        <result column="out_warehouse_code" jdbcType="VARCHAR" property="outWarehouseCode"/>
        <result column="inventory_in_plan_type" jdbcType="INTEGER" property="inventoryInPlanType"/>
        <result column="billing_user" jdbcType="VARCHAR" property="billingUser"/>
        <result column="billing_time" jdbcType="TIMESTAMP" property="billingTime"/>
        <result column="reason_code" jdbcType="INTEGER" property="reasonCode"/>
        <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode"/>
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName"/>
        <result column="receive_user" jdbcType="VARCHAR" property="receiveUser"/>
        <result column="plan_in_time" jdbcType="TIMESTAMP" property="planInTime"/>
        <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="duty_user" jdbcType="VARCHAR" property="dutyUser"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="demand_dept_code" jdbcType="VARCHAR" property="demandDeptCode"/>
    </resultMap>

    <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockInventoryInPlanHead"
            useGeneratedKeys="true"
            keyProperty="inventoryInPlanHeadId">
    insert into stock_inventory_in_plan_heads (inventory_in_plan_head_id, biz_no, inventory_in_plan_no,Warehouse_type,
      in_warehouse_code, out_warehouse_code, inventory_in_plan_type,
      billing_user, billing_time, reason_code,
      vendor_code, vendor_name,Company_code,system_code, receive_user,
      plan_in_time,adjust_date, delivery_no,Purchase_order_no, status,
      remark, duty_user, duty_dept, duty_address, CREATED_BY,  UPDATED_BY,
      demand_dept_code)
    values (#{inventoryInPlanHeadId,jdbcType=BIGINT}, #{bizNo,jdbcType=VARCHAR}, #{inventoryInPlanNo,jdbcType=VARCHAR},#{warehouseType,jdbcType=INTEGER},
      #{inWarehouseCode,jdbcType=VARCHAR}, #{outWarehouseCode,jdbcType=VARCHAR}, #{inventoryInPlanType,jdbcType=INTEGER},
      #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=TIMESTAMP}, #{reasonCode,jdbcType=INTEGER},
      #{vendorCode,jdbcType=VARCHAR}, #{vendorName,jdbcType=VARCHAR},#{companyCode,jdbcType=VARCHAR},
        #{systemCode,jdbcType=VARCHAR},#{receiveUser,jdbcType=VARCHAR},
      #{planInTime,jdbcType=TIMESTAMP},#{adjustDate,jdbcType=TIMESTAMP}, #{deliveryNo,jdbcType=VARCHAR},#{purchaseOrderNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{remark,jdbcType=VARCHAR}, #{dutyUser,jdbcType=VARCHAR}, #{dutyDept,jdbcType=VARCHAR},#{dutyAddress,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
       #{updatedBy,jdbcType=VARCHAR}, #{demandDeptCode,jdbcType=VARCHAR})
  </insert>


    <select id="selectByPage" parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanHeadRespDTO">
        select
        siiph.inventory_in_plan_head_id as inventoryInPlanHeadId,
        siiph.inventory_in_plan_no as inventoryInPlanNo,
        siiph.biz_no as bizNo,
        siiph.system_code as systemCode,
        CONCAT_WS(' ',su1.name, siiph.billing_user) billingUserName,
        CONCAT_WS(' ',su2.name,siiph.receive_user) receiveUserName,
        sw.name inWarehouseName,
        sw.code inWarehouseCode,
        siiph.vendor_name vendorName,
        siiph.status status,
        siiph.reason_code reasonCode,
        siiph.delivery_no deliveryNo,
        sw1.name outWarehouseName,
        sw1.code outWarehouseCode,
        swb.address inWarehouseAddress,
        DATE_FORMAT(siiph.plan_in_time, '%Y-%m-%d') planInTime,
        CONCAT_WS(' ',su3.name, siiph.duty_user) dutyUserName,
        siiph.billing_user billingUser,
        DATE_FORMAT(siiph.billing_time, '%Y-%m-%d') billingTime,
        siiph.receive_user receiveUser,
        siiph.duty_user dutyUser
        from stock_inventory_in_plan_heads siiph
        left join ambase.sys_user su1 on siiph.billing_user = su1.emp_id
        left join ambase.sys_user su2 on siiph.receive_user = su2.emp_id
        left join ambase.sys_user su3 on siiph.duty_user = su3.emp_id
        left join stock_warehouse sw1 on siiph.out_warehouse_code = sw1.code
        join stock_warehouse sw on siiph.in_warehouse_code = sw.code
        left join stock_warehouse_base swb on siiph.in_warehouse_code = swb.warehouse_code
        where 1=1
        <if test="inventoryInPlanNo != null and inventoryInPlanNo != ''">
            and siiph.inventory_in_plan_no like CONCAT('%',#{inventoryInPlanNo}, '%')
        </if>
        <if test="bizNo != null and bizNo!= ''">
            and siiph.biz_no = #{bizNo}
        </if>
        <if test="demandDeptCode != null and demandDeptCode != ''">
            and siiph.demand_dept_code = #{demandDeptCode}
        </if>
        <if test="vendorName != null and vendorName != ''">
            and siiph.vendor_name like CONCAT('%',#{vendorName}, '%')
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode != ''">
            and siiph.in_warehouse_code = #{inWarehouseCode}
        </if>
        <if test="outWarehouseCode != null and outWarehouseCode != ''">
            and siiph.out_warehouse_code = #{outWarehouseCode}
        </if>
        <if test="billingUser != null and billingUser != ''">
            and siiph.billing_user = #{billingUser}
        </if>
        <if test="status != null and status !=34">
            and siiph.status = #{status}
        </if>
        <if test="status != null and status ==34">
            and siiph.status in (3,4)
        </if>
        <if test="inventoryInPlanType != null ">
            and siiph.inventory_in_plan_type = #{inventoryInPlanType}
        </if>
        <if test="inventoryInPlanTypeList != null  and inventoryInPlanTypeList.size()>0 ">
            and siiph.inventory_in_plan_type in
            <foreach collection="inventoryInPlanTypeList" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="dutyUser != null and dutyUser != ''">
            and siiph.duty_user = #{dutyUser}
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and siiph.delivery_no = #{deliveryNo}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(siiph.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(siiph.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanInTime != null and beginPlanInTime != ''">
            and DATE_FORMAT(siiph.plan_in_time, '%Y-%m-%d') &gt;= #{beginPlanInTime}
        </if>
        <if test="endPlanInTime != null and endPlanInTime != ''">
            and DATE_FORMAT(siiph.plan_in_time, '%Y-%m-%d') &lt;= #{endPlanInTime}
        </if>
        <if test="receiveUser != null and receiveUser != ''">
            and siiph.receive_user = #{receiveUser}
        </if>
        <if test="beginInventoryInTime != null and beginInventoryInTime != ''">
            and DATE_FORMAT(sii.inventory_in_time, '%Y-%m-%d') &gt;= #{beginInventoryInTime}
        </if>
        <if test="endInventoryInTime != null and endInventoryInTime != ''">
            and DATE_FORMAT(sii.inventory_in_time, '%Y-%m-%d') &gt;= #{endInventoryInTime}
        </if>
        <if test="inventoryInUser != null and inventoryInUser != ''">
            and sii.inventory_in_user = #{inventoryInUser}
        </if>
        <if test="assetsCode != null and assetsCode != '' and inventoryInPlanType != 12">
            and exists (select 1 from stock_inventory_in_plan_lines_assets s
            where siiph.inventory_in_plan_head_id=s.inventory_in_plan_head_id
            and s.assets_code= #{assetsCode})
        </if>
        <if test="assetsCode != null and assetsCode != '' and inventoryInPlanType == 12">
            and exists (select 1 from stock_inventory_in_plan_lines_sns s
            where siiph.inventory_in_plan_head_id=s.inventory_in_plan_head_id
            and s.sn_no = #{assetsCode})
        </if>
        <if test="codes != null and codes.size() > 0">
            AND siiph.in_warehouse_code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="reasonCode != null">
            and siiph.reason_code = #{reasonCode}
        </if>
        <if test="showCancel == null">
            and siiph.status &lt;&gt; 7
        </if>
        <if test="showClosed == null">
            and siiph.status &lt;&gt; 6
        </if>
        order by siiph.UPDATED_AT desc, siiph.inventory_in_plan_head_id desc
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectCountByParam"
            parameterType="com.gz.eim.am.stock.dto.request.inventory.plan.InventoryInPlanSearchReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from stock_inventory_in_plan_heads siiph
        where 1=1
        <if test="inventoryInPlanNo != null and inventoryInPlanNo != ''">
            and siiph.inventory_in_plan_no like CONCAT('%',#{inventoryInPlanNo}, '%')
        </if>
        <if test="bizNo != null and bizNo!= ''">
            and siiph.biz_no = #{bizNo}
        </if>
        <if test="demandDeptCode != null and demandDeptCode != ''">
            and siiph.demand_dept_code = #{demandDeptCode}
        </if>
        <if test="vendorName != null and vendorName != ''">
            and siiph.vendor_name like CONCAT('%',#{vendorName}, '%')
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode != ''">
            and siiph.in_warehouse_code = #{inWarehouseCode}
        </if>
        <if test="outWarehouseCode != null and outWarehouseCode != ''">
            and siiph.out_warehouse_code = #{outWarehouseCode}
        </if>
        <if test="billingUser != null and billingUser != ''">
            and siiph.billing_user = #{billingUser}
        </if>
        <if test="status != null and status != 34">
            and siiph.status = #{status}
        </if>
        <if test="status != null and status == 34">
            and siiph.status in (3,4)
        </if>
        <if test="inventoryInPlanType != null ">
            and siiph.inventory_in_plan_type = #{inventoryInPlanType}
        </if>
        <if test="inventoryInPlanTypeList != null  and inventoryInPlanTypeList.size()>0 ">
            and siiph.inventory_in_plan_type in
            <foreach collection="inventoryInPlanTypeList" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="dutyUser != null and dutyUser != ''">
            and siiph.duty_user = #{dutyUser}
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and siiph.delivery_no = #{deliveryNo}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(siiph.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(siiph.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanInTime != null and beginPlanInTime != ''">
            and DATE_FORMAT(siiph.plan_in_time, '%Y-%m-%d') &gt;= #{beginPlanInTime}
        </if>
        <if test="endPlanInTime != null and endPlanInTime != ''">
            and DATE_FORMAT(siiph.plan_in_time, '%Y-%m-%d') &lt;= #{endPlanInTime}
        </if>
        <if test="receiveUser != null and receiveUser != ''">
            and siiph.receive_user = #{receiveUser}
        </if>
        <if test="assetsCode != null and assetsCode != '' and inventoryInPlanType != 12">
            and exists (select 1 from stock_inventory_in_plan_lines_assets s
            where siiph.inventory_in_plan_head_id=s.inventory_in_plan_head_id
            and s.assets_code= #{assetsCode})
        </if>
        <if test="assetsCode != null and assetsCode != '' and inventoryInPlanType == 12">
            and exists (select 1 from stock_inventory_in_plan_lines_sns s
            where siiph.inventory_in_plan_head_id=s.inventory_in_plan_head_id
            and s.sn_no = #{assetsCode})
        </if>
        <if test="codes != null and codes.size() > 0">
            AND siiph.in_warehouse_code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="reasonCode != null">
            and siiph.reason_code = #{reasonCode}
        </if>
        <if test="showCancel == null">
            and siiph.status &lt;&gt; 7
        </if>
        <if test="showClosed == null">
            and siiph.status &lt;&gt; 6
        </if>
    </select>

    <select id="selectByInventoryInPlanHeadId" parameterType="java.lang.Long"
            resultType="com.gz.eim.am.stock.dto.response.inventory.plan.InventoryInPlanHeadRespDTO">
            select
            distinct       
            siiph.inventory_in_plan_head_id inventoryInPlanHeadId,
            siiph.biz_no bizNo,
            siiph.inventory_in_plan_no inventoryInPlanNo,
            siiph.system_code systemCode,
            siiph.in_warehouse_code inWarehouseCode,
            siiph.out_warehouse_code outWarehouseCode,
            siiph.inventory_in_plan_type inventoryInPlanType,
            siiph.billing_user billingUser,
            siiph.billing_time billingTime,
            siiph.reason_code reasonCode,
            siiph.vendor_code vendorCode,
            siiph.vendor_name vendorName,
            slc.company_name companyName,
            siiph.receive_user receiveUser,
            DATE_FORMAT(siiph.plan_in_time, '%Y-%m-%d') planInTime,
            siiph.delivery_no deliveryNo,
            siiph.status status,
            siiph.remark remark,
            siiph.duty_user dutyUser,
            siiph.demand_dept_code demandDeptCode,
            CONCAT_WS(' ',su1.name, siiph.receive_user) receiveUserName,
            su1.phone receiveUserPhone,
            CONCAT_WS(' ',su2.name, siiph.billing_user) billingUserName,
            swb1.address inWarehouseAddress,
            sw1.`name` inWarehouseName,
            sw2.name outWarehouseName,
            swb2.address outWarehouseAddress,
            CONCAT_WS(' ',su3.name, siiph.duty_user) dutyUserName,
            sd.dept_name demandDeptName,
            pbjt.HPS_JOBCD_DESCR dutyUserOccasionName,
            sb1.dept_full_name dutyUserDeptName
            from stock_inventory_in_plan_heads siiph
            left join ambase.sys_user su1 on siiph.receive_user = su1.emp_id
            left join ambase.sys_user su2 on siiph.billing_user = su2.emp_id
            left join ambase.sys_user su3 on siiph.duty_user = su3.emp_id
            JOIN stock_warehouse sw1 on siiph.in_warehouse_code = sw1.code
            left JOIN stock_warehouse_base swb1 on siiph.in_warehouse_code = swb1.warehouse_code
            LEFT JOIN stock_warehouse sw2 on siiph.out_warehouse_code = sw2.code
            LEFT JOIN stock_warehouse_base swb2 on siiph.out_warehouse_code = swb2.warehouse_code
            left join ambase.sys_dept sd on sd.dept_id =  siiph.demand_dept_code
            left join ambase.ps_bus_jobcode_tbl pbjt on pbjt.JOBCODE = su3.job_id
            left join ambase.sys_dept sb1 on sb1.dept_id = su3.dept_id
            left join ambase.sys_lgl_company slc on slc.company_code = siiph.company_code
            where siiph.inventory_in_plan_head_id = #{inventoryInPlanHeadId}
    </select>

    <select id="selectAccessibleByPage"
            parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryInSearchReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.inventory.InventoryInRespDTO">
        SELECT
        sii.inventory_in_no inventoryInNo,
        sii.biz_no bizNo,
        CONCAT_WS(' ',su1.name, sii.billing_user) billingUserName,
        CONCAT_WS( ' ', su2.NAME, sii.receive_user) receiveUserName,
        sw.NAME warehouseCodeName,
        sii.vendor_name vendorName,
        sii.STATUS STATUS,
        sii.inventory_in_id inventoryInId,
        sii.reason_code reasonCode,
        sw1.NAME outWarehouseName,
        DATE_FORMAT( sii.plan_in_time, '%Y年%m月%d日' ) planInTime,
        (
        SELECT
        CONCAT( ss.NAME, '等' )
        FROM
        stock_supplies ss,
        stock_inventory_in_supplies siis
        WHERE
        siis.supplies_code = ss.CODE
        AND siis.inventory_in_id = sii.inventory_in_id
        LIMIT 1
        ) supplierName,
        sii.inventory_in_type inventoryInType
        FROM
        stock_inventory_in sii
        LEFT JOIN ambase.sys_user su1 ON sii.billing_user = su1.emp_id
        LEFT JOIN ambase.sys_user su2 ON sii.receive_user = su2.emp_id
        LEFT JOIN stock_warehouse sw1 ON sii.out_warehouse_code = sw1.CODE,
        stock_warehouse sw
        WHERE
        sii.in_warehouse_code = sw.CODE
        AND sii.STATUS IN ( 3, 4 )
        <if test="inventoryInNo != null and inventoryInNo != ''">
            and sii.inventory_in_no like CONCAT('%',#{inventoryInNo}, '%')
        </if>
        <if test="bizNo != null and bizNo != ''">
            and sii.biz_no like CONCAT('%', #{bizNo} '%')
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and sii.delivery_no like CONCAT('%', #{deliveryNo}, '%')
        </if>
        <if test="codes != null and codes.size() > 0">
            AND sw.code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="vendorName != null and vendorName != ''">
            and sii.vendor_name like CONCAT('%', #{vendorName}, '%')
        </if>
        <if test="status != null">
            and sii.status = #{status}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(sii.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(sii.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanInTime != null and beginPlanInTime != ''">
            and DATE_FORMAT(sii.plan_in_time, '%Y-%m-%d') &gt;= #{beginPlanInTime}
        </if>
        <if test="endPlanInTime != null and endPlanInTime != ''">
            and DATE_FORMAT(sii.plan_in_time, '%Y-%m-%d') &lt;= #{endPlanInTime}
        </if>
        <if test="billingUser != null">
            and sii.billing_user = #{billingUser}
        </if>
        <if test="receiveUser != null">
            and sii.receive_user = #{receiveUser}
        </if>
        <if test="inventoryInType != null">
            and sii.inventory_in_type = #{inventoryInType}
        </if>
        <if test="outWarehouseCode != null and outWarehouseCode != ''">
            AND sii.out_warehouse_code = #{outWarehouseCode}
        </if>
        <if test="warehouseCodeIn != null and warehouseCodeIn != ''">
            AND sii.in_warehouse_code = #{warehouseCodeIn}
        </if>
        ORDER BY sii.updated_at desc, sii.inventory_in_no desc
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectAccessibleCountByParam"
            parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryInSearchReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from stock_inventory_in sii
        , stock_warehouse sw
        where sii.in_warehouse_code = sw.code
        AND sii.STATUS IN ( 3, 4 )
        <if test="inventoryInNo != null and inventoryInNo != ''">
            and sii.inventory_in_no like CONCAT('%',#{inventoryInNo}, '%')
        </if>
        <if test="bizNo != null and bizNo != ''">
            and sii.biz_no like CONCAT('%', #{bizNo} '%')
        </if>
        <if test="deliveryNo != null and deliveryNo != ''">
            and sii.delivery_no like CONCAT('%', #{deliveryNo}, '%')
        </if>
        <if test="codes != null and codes.size() > 0">
            AND sw.code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="vendorName != null and vendorName != ''">
            and sii.vendor_name like CONCAT('%', #{vendorName}, '%')
        </if>
        <if test="status != null">
            and sii.status = #{status}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(sii.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(sii.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanInTime != null and beginPlanInTime != ''">
            and DATE_FORMAT(sii.plan_in_time, '%Y-%m-%d') &gt;= #{beginPlanInTime}
        </if>
        <if test="endPlanInTime != null and endPlanInTime != ''">
            and DATE_FORMAT(sii.plan_in_time, '%Y-%m-%d') &lt;= #{endPlanInTime}
        </if>
        <if test="billingUser != null">
            and sii.billing_user = #{billingUser}
        </if>
        <if test="receiveUser != null">
            and sii.receive_user = #{receiveUser}
        </if>
        <if test="inventoryInType != null">
            and sii.inventory_in_type = #{inventoryInType}
        </if>
        <if test="outWarehouseCode != null and outWarehouseCode != ''">
            AND sii.out_warehouse_code = #{outWarehouseCode}
        </if>
        <if test="warehouseCodeIn != null and warehouseCodeIn != ''">
            AND sii.in_warehouse_code = #{warehouseCodeIn}
        </if>
    </select>

    <insert id="batchInsertInfo" parameterType="com.gz.eim.am.stock.entity.vo.StockInventoryInPlanHeadInfo"
            useGeneratedKeys="true"
            keyProperty="inventoryInPlanHeadId" keyColumn="inventory_in_plan_head_id">
        insert into stock_inventory_in_plan_heads (inventory_in_plan_head_id, biz_no, inventory_in_plan_no,
        in_warehouse_code, out_warehouse_code, inventory_in_plan_type,
        billing_user, billing_time, reason_code,
        vendor_code, vendor_name, receive_user,
        plan_in_time, delivery_no, status,
        remark, duty_user, CREATED_BY, UPDATED_BY,
        demand_dept_code)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.inventoryInPlanHeadId,jdbcType=BIGINT}, #{item.bizNo,jdbcType=VARCHAR}, #{item.inventoryInPlanNo,jdbcType=VARCHAR},
            #{item.inWarehouseCode,jdbcType=VARCHAR}, #{item.outWarehouseCode,jdbcType=VARCHAR},
            #{item.inventoryInPlanType,jdbcType=INTEGER},
            #{item.billingUser,jdbcType=VARCHAR}, #{item.billingTime,jdbcType=TIMESTAMP}, #{item.reasonCode,jdbcType=INTEGER},
            #{item.vendorCode,jdbcType=VARCHAR}, #{item.vendorName,jdbcType=VARCHAR}, #{item.receiveUser,jdbcType=VARCHAR},
            #{item.planInTime,jdbcType=TIMESTAMP}, #{item.deliveryNo,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR}, #{item.dutyUser,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.demandDeptCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
