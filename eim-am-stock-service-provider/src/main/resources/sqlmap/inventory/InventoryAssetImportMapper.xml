<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.inventory.InventoryAssetImportMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockInventoryAssetImport">
        <id column="inventory_asset_imp_id" jdbcType="INTEGER" property="inventoryAssetImpId"/>
        <id column="inventory_asset_imp_id" jdbcType="INTEGER" property="inventoryAssetImpId"/>
        <result column="inventory_asset_batch_code" jdbcType="VARCHAR" property="inventoryAssetBatchCode"/>
        <result column="asset_code" jdbcType="VARCHAR" property="assetCode"/>
        <result column="sn_no" jdbcType="VARCHAR" property="snNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="asset_type_name" jdbcType="VARCHAR" property="assetTypeName"/>
        <result column="type" jdbcType="INTEGER" property="type" />
    </resultMap>
    <sql id="Base_Column_List">
    inventory_asset_imp_id, inventory_asset_batch_code, asset_code, sn_no, status, CREATED_BY,
    CREATED_AT, UPDATED_BY, UPDATED_AT
  </sql>

    <insert id="batchInsertStockAssetImport" parameterType="com.gz.eim.am.stock.entity.StockInventoryAssetImport">
        insert into stock_inventory_asset_import (inventory_asset_batch_code, asset_code,used_left,
        sn_no, attr_1, attr_2,
        attr_3, attr_4, attr_5,
        attr_6, attr_7, attr_8,
        attr_9, attr_10, attr_11,
        attr_12, attr_13, attr_14,
        attr_15, attr_16, attr_17,
        attr_18, attr_19, attr_20,
        status, CREATED_BY, CREATED_AT,
        UPDATED_BY, UPDATED_AT, asset_type_name,
        type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.inventoryAssetBatchCode,jdbcType=VARCHAR}, #{item.assetCode,jdbcType=VARCHAR},#{item.usedLeft,jdbcType=INTEGER},
            #{item.snNo,jdbcType=VARCHAR}, #{item.attr1,jdbcType=VARCHAR}, #{item.attr2,jdbcType=VARCHAR},
            #{item.attr3,jdbcType=VARCHAR}, #{item.attr4,jdbcType=VARCHAR}, #{item.attr5,jdbcType=VARCHAR},
            #{item.attr6,jdbcType=VARCHAR}, #{item.attr7,jdbcType=VARCHAR}, #{item.attr8,jdbcType=VARCHAR},
            #{item.attr9,jdbcType=VARCHAR}, #{item.attr10,jdbcType=VARCHAR}, #{item.attr11,jdbcType=VARCHAR},
            #{item.attr12,jdbcType=VARCHAR}, #{item.attr13,jdbcType=VARCHAR}, #{item.attr14,jdbcType=VARCHAR},
            #{item.attr15,jdbcType=VARCHAR}, #{item.attr16,jdbcType=VARCHAR}, #{item.attr17,jdbcType=VARCHAR},
            #{item.attr18,jdbcType=VARCHAR}, #{item.attr19,jdbcType=VARCHAR}, #{item.attr20,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.assetTypeName,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectRepeatAsset" parameterType="java.lang.String" resultType="java.lang.String">
        select t.asset_code from (select asset_code, count(1) num
        from
        stock_inventory_asset_import
        where inventory_asset_batch_code in
        <foreach collection="list" item="batchCode" index="index" open="(" separator="," close=")">
            #{batchCode}
        </foreach>
        group by asset_code) t where t.num > 1
    </select>

    <select id="selectRepeatSn" parameterType="java.lang.String" resultType="java.lang.String">
        select t.sn_no from (select sn_no, count(1) num
        from
        stock_inventory_asset_import
        where inventory_asset_batch_code in
        <foreach collection="list" item="batchCode" index="index" open="(" separator="," close=")">
            #{batchCode}
        </foreach>
        and sn_no is not null
        and sn_no != ''
        group by sn_no) t where t.num > 1
    </select>

    <select id="countInventoryAssetImport" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO" resultType="java.lang.Long">
        select count(1) from stock_inventory_asset_import where inventory_asset_batch_code = #{inventoryAssetBatchCode}
    </select>

    <select id="selectByParam" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO" resultType="com.gz.eim.am.stock.dto.response.inventory.InventoryAssetImportRespDTO">
        select
        aiai.asset_code assetCode,
        aiai.sn_no snNo,
        aiai.used_left usedLeft,
        aiai.asset_type_name assetTypeName
        from
        stock_inventory_asset_import  aiai
        where
        aiai.inventory_asset_batch_code = #{inventoryAssetBatchCode}
        order by aiai.asset_code
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="assetCountInventoryAssetImport" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportSearchReqDTO" resultType="java.lang.Long">
        select count(1)
        from stock_inventory_asset_import
        where inventory_asset_batch_code = #{inventoryAssetBatchCode}
        and asset_code is not null
        and asset_code != ''
    </select>

    <delete id="deleteByParam" parameterType="com.gz.eim.am.stock.entity.StockInventoryAssetImport">
        delete from stock_inventory_asset_import
        where
        inventory_asset_batch_code = #{inventoryAssetBatchCode,jdbcType=VARCHAR}
        <if test="assetCode != null and assetCode != ''">
            and asset_code = #{assetCode,jdbcType=VARCHAR}
        </if>
        <if test="type != null">
            and type = #{type,jdbcType=INTEGER}
        </if>
    </delete>


    <delete id="deleteByParams" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryAssetImportReqDTO">
        delete from stock_inventory_asset_import
        where
        inventory_asset_batch_code = #{inventoryAssetBatchCode,jdbcType=VARCHAR}
        <if test="assetCode != null and assetCode != ''">
            and asset_code = #{assetCode,jdbcType=VARCHAR}
        </if>
        <if test="type != null">
            and type = #{type,jdbcType=INTEGER}
        </if>
        <if test="typeList != null and typeList.size()>0">
            and type in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="selectAssetImportNetValue" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        select ifnull(sum(b.LAST_COST),0) from stock_inventory_asset_import a inner join ambase.cux_getfadetail_tbl b on a.asset_code=b.TAG_NUMBER
        and a.inventory_asset_batch_code=#{bindBatchCode}
    </select>

    <select id="getScrapLinesByBindCode" parameterType="java.lang.String" resultType="com.gz.eim.am.stock.dto.response.assets.StockAssetsScrapLineRespDTO">
        select a.asset_code as assetsCode,
        b.assets_name as assetsName,
        b.sn_code as snCode,
        b.category,
        b.assets_keeper as assetsKeeper,
        b.warehouse_code as warehouseCode,
        b.holder,
        c.address as assetsWarehouseAddress,
        b.status as assetsStatus,
        b.need_dept as needDept,
        d.cost as assetsCost,
        d.LAST_COST as assetsLastCost,
        d.LIFE_IN_YEAR*12 as assetsLife,
        d.SURPLUS_YEAR as assetsSurplusLifeStr,
        0 as scrapFlag,
        a.status as scrapReason
        from stock_inventory_asset_import a
        inner join stock_assets b on a.asset_code=b.assets_code
        left join stock_warehouse_base c on c.warehouse_code=b.warehouse_code
        left join ambase.cux_getfadetail_tbl d on a.asset_code=d.tag_number
        where a.inventory_asset_batch_code= #{bindBatchCode}
    </select>

    <update id="batchUpdateStockAssetImport" parameterType="com.gz.eim.am.stock.entity.StockInventoryAssetImport">
        <foreach collection="list" item="item" separator=";">
        update stock_inventory_asset_import
        <set>
            <if test="item.inventoryAssetImpId != null">
                inventory_asset_imp_id = #{item.inventoryAssetImpId,jdbcType=INTEGER},
            </if>
            <if test="item.inventoryAssetBatchCode != null">
                inventory_asset_batch_code = #{item.inventoryAssetBatchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.assetCode != null">
                asset_code = #{item.assetCode,jdbcType=VARCHAR},
            </if>
            <if test="item.snNo != null">
                sn_no = #{item.snNo,jdbcType=VARCHAR},
            </if>
            <if test="item.usedLeft != null">
                used_left = #{item.usedLeft,jdbcType=INTEGER},
            </if>
            <if test="item.attr1 != null">
                attr_1 = #{item.attr1,jdbcType=VARCHAR},
            </if>
            <if test="item.attr2 != null">
                attr_2 = #{item.attr2,jdbcType=VARCHAR},
            </if>
            <if test="item.attr3 != null">
                attr_3 = #{item.attr3,jdbcType=VARCHAR},
            </if>
            <if test="item.attr4 != null">
                attr_4 = #{item.attr4,jdbcType=VARCHAR},
            </if>
            <if test="item.attr5 != null">
                attr_5 = #{item.attr5,jdbcType=VARCHAR},
            </if>
            <if test="item.attr6 != null">
                attr_6 = #{item.attr6,jdbcType=VARCHAR},
            </if>
            <if test="item.attr7 != null">
                attr_7 = #{item.attr7,jdbcType=VARCHAR},
            </if>
            <if test="item.attr8 != null">
                attr_8 = #{item.attr8,jdbcType=VARCHAR},
            </if>
            <if test="item.attr9 != null">
                attr_9 = #{item.attr9,jdbcType=VARCHAR},
            </if>
            <if test="item.attr10 != null">
                attr_10 = #{item.attr10,jdbcType=VARCHAR},
            </if>
            <if test="item.attr11 != null">
                attr_11 = #{item.attr11,jdbcType=VARCHAR},
            </if>
            <if test="item.attr12 != null">
                attr_12 = #{item.attr12,jdbcType=VARCHAR},
            </if>
            <if test="item.attr13 != null">
                attr_13 = #{item.attr13,jdbcType=VARCHAR},
            </if>
            <if test="item.attr14 != null">
                attr_14 = #{item.attr14,jdbcType=VARCHAR},
            </if>
            <if test="item.attr15 != null">
                attr_15 = #{item.attr15,jdbcType=VARCHAR},
            </if>
            <if test="item.attr16 != null">
                attr_16 = #{item.attr16,jdbcType=VARCHAR},
            </if>
            <if test="item.attr17 != null">
                attr_17 = #{item.attr17,jdbcType=VARCHAR},
            </if>
            <if test="item.attr18 != null">
                attr_18 = #{item.attr18,jdbcType=VARCHAR},
            </if>
            <if test="item.attr19 != null">
                attr_19 = #{item.attr19,jdbcType=VARCHAR},
            </if>
            <if test="item.attr20 != null">
                attr_20 = #{item.attr20,jdbcType=VARCHAR},
            </if>
            <if test="item.status != null">
                status = #{item.status,jdbcType=INTEGER},
            </if>
            <if test="item.createdBy != null">
                CREATED_BY = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="item.createdAt != null">
                CREATED_AT = #{item.createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.updatedBy != null">
                UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="item.updatedAt != null">
                UPDATED_AT = #{item.updatedAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.assetTypeCode != null">
                asset_type_code = #{item.assetTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="item.assetTypeName != null">
                asset_type_name = #{item.assetTypeName,jdbcType=VARCHAR},
            </if>
            <if test="item.type != null">
                type = #{item.type,jdbcType=INTEGER},
            </if>
        </set>
            where inventory_asset_imp_id = #{item.inventoryAssetImpId,jdbcType=INTEGER}
        </foreach>
    </update>

    <delete id="batchDeleteStockAssetImport" parameterType="java.lang.Integer">
        delete from stock_inventory_asset_import
        where inventory_asset_imp_id in
        <foreach collection="list" item="inventoryAssetImpId" open="(" separator="," close=")">
            #{inventoryAssetImpId,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>