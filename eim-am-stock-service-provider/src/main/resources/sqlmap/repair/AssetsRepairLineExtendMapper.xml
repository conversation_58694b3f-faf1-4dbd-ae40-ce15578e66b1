<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.repair.AssetsRepairLineExtendMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="repair_item_no" jdbcType="VARCHAR" property="repairItemNo" />
    <result column="field1" jdbcType="VARCHAR" property="field1" />
    <result column="field2" jdbcType="VARCHAR" property="field2" />
    <result column="field3" jdbcType="VARCHAR" property="field3" />
    <result column="field4" jdbcType="VARCHAR" property="field4" />
    <result column="field5" jdbcType="VARCHAR" property="field5" />
    <result column="field6" jdbcType="VARCHAR" property="field6" />
    <result column="field7" jdbcType="VARCHAR" property="field7" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>

  <sql id="Base_Column_List">
    id, repair_item_no, field1, field2, field3, field4, field5, field6, field7, created_by, 
    created_at, updated_by, updated_at
  </sql>

  <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    insert into stock_assets_repair_line_extend (repair_item_no, field1,
    field2, field3, field4,
    field5, field6, field7,
    created_by, created_at, updated_by,
    updated_at)
    values
    <foreach collection="list" item="item" separator=",">
    (#{item.repairItemNo,jdbcType=VARCHAR}, #{item.field1,jdbcType=VARCHAR},
    #{item.field2,jdbcType=VARCHAR}, #{item.field3,jdbcType=VARCHAR}, #{item.field4,jdbcType=VARCHAR},
    #{item.field5,jdbcType=VARCHAR}, #{item.field6,jdbcType=VARCHAR}, #{item.field7,jdbcType=VARCHAR},
    #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
    #{item.updatedAt,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLineExtend">
    <foreach collection="list" item="item" separator=";">
      update stock_assets_repair_line_extend
      <set>
        <if test="item.repairItemNo != null">
          repair_item_no = #{item.repairItemNo,jdbcType=VARCHAR},
        </if>
        <if test="item.field1 != null">
          field1 = #{item.field1,jdbcType=VARCHAR},
        </if>
        <if test="item.field2 != null">
          field2 = #{item.field2,jdbcType=VARCHAR},
        </if>
        <if test="item.field3 != null">
          field3 = #{item.field3,jdbcType=VARCHAR},
        </if>
        <if test="item.field4 != null">
          field4 = #{item.field4,jdbcType=VARCHAR},
        </if>
        <if test="item.field5 != null">
          field5 = #{item.field5,jdbcType=VARCHAR},
        </if>
        <if test="item.field6 != null">
          field6 = #{item.field6,jdbcType=VARCHAR},
        </if>
        <if test="item.field7 != null">
          field7 = #{item.field7,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdAt != null">
          created_at = #{item.createdAt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updatedBy != null">
          updated_by = #{item.updatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.updatedAt != null">
          updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>