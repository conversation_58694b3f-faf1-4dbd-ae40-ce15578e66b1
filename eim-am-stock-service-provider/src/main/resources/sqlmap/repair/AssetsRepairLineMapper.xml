<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.repair.AssetsRepairLineMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAssetsRepairLine">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="repair_no" jdbcType="VARCHAR" property="repairNo" />
    <result column="repair_item_no" jdbcType="VARCHAR" property="repairItemNo" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="assets_name" jdbcType="VARCHAR" property="assetsName" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="is_valid" jdbcType="INTEGER" property="isValid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, repair_no, repair_item_no, assets_code, assets_name, brand, model, warehouse_code, 
    check_status, created_by, created_at, updated_by, updated_at, is_valid
  </sql>

  <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLine">
    insert into stock_assets_repair_line (repair_no, repair_item_no,
      assets_code, assets_name, brand, 
      model, warehouse_code, check_status, 
      created_by, created_at, updated_by, 
      updated_at, is_valid)
    values
    <foreach collection="list" item="item" separator=",">
    (#{item.repairNo,jdbcType=VARCHAR}, #{item.repairItemNo,jdbcType=VARCHAR},
      #{item.assetsCode,jdbcType=VARCHAR}, #{item.assetsName,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR},
      #{item.model,jdbcType=VARCHAR}, #{item.warehouseCode,jdbcType=VARCHAR}, #{item.checkStatus,jdbcType=INTEGER},
      #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
      #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.isValid,jdbcType=INTEGER})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockAssetsRepairLine">
    <foreach collection="list" item="item" separator=";">
    update stock_assets_repair_line
    <set>
      <if test="item.repairNo != null">
        repair_no = #{item.repairNo,jdbcType=VARCHAR},
      </if>
      <if test="item.repairItemNo != null">
        repair_item_no = #{item.repairItemNo,jdbcType=VARCHAR},
      </if>
      <if test="item.assetsCode != null">
        assets_code = #{item.assetsCode,jdbcType=VARCHAR},
      </if>
      <if test="item.assetsName != null">
        assets_name = #{item.assetsName,jdbcType=VARCHAR},
      </if>
      <if test="item.brand != null">
        brand = #{item.brand,jdbcType=VARCHAR},
      </if>
      <if test="item.model != null">
        model = #{item.model,jdbcType=VARCHAR},
      </if>
      <if test="item.warehouseCode != null">
        warehouse_code = #{item.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="item.checkStatus != null">
        check_status = #{item.checkStatus,jdbcType=INTEGER},
      </if>
      <if test="item.createdBy != null">
        created_by = #{item.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="item.createdAt != null">
        created_at = #{item.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="item.updatedBy != null">
        updated_by = #{item.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="item.updatedAt != null">
        updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="item.isValid != null">
        is_valid = #{item.isValid,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>