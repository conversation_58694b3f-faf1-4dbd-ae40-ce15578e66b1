<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.allocate.AllocateImportLineMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockAllocateImportLine">
        <id column="line_id" jdbcType="BIGINT" property="lineId" />
        <result column="head_id" jdbcType="BIGINT" property="headId" />
        <result column="out_import_warehouse_name" jdbcType="VARCHAR" property="outImportWarehouseName" />
        <result column="out_warehouse_code" jdbcType="VARCHAR" property="outWarehouseCode" />
        <result column="in_import_warehouse_name" jdbcType="VARCHAR" property="inImportWarehouseName" />
        <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode" />
        <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
        <result column="number" jdbcType="INTEGER" property="number" />
        <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
        <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
        <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
        <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    </resultMap>
    <sql id="Base_Column_List">
    line_id, head_id, out_import_warehouse_name, out_warehouse_code, in_import_warehouse_name,
    in_warehouse_code, supplies_code, number, error_message, status, del_flag, CREATED_BY,
    CREATED_AT, UPDATED_BY, UPDATED_AT, assets_code
  </sql>

    <insert id="batchInsertStockAllocateImportLine" parameterType="java.util.List">
        insert into stock_allocate_import_lines (head_id,
        out_import_warehouse_name, out_warehouse_code,
        in_import_warehouse_name, in_warehouse_code,
        supplies_code, number, status, reason_code,
        error_message, CREATED_BY,
        UPDATED_BY, del_flag, assets_code)
        values
        <foreach collection="stockAllocateImportLineList" item="item" index="index" separator=",">
            (#{item.headId,jdbcType=BIGINT},
            #{item.outImportWarehouseName,jdbcType=VARCHAR}, #{item.outWarehouseCode,jdbcType=VARCHAR},
            #{item.inImportWarehouseName,jdbcType=VARCHAR}, #{item.inWarehouseCode,jdbcType=VARCHAR},
            #{item.suppliesCode,jdbcType=VARCHAR}, #{item.number,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},#{item.reasonCode,jdbcType=INTEGER},
            #{item.errorMessage,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=VARCHAR},
            #{item.delFlag,jdbcType=INTEGER}, #{item.assetsCode,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="countStockAllocateImportLineByHeadId" parameterType="java.lang.Long"
            resultType="java.lang.Long">
        select count(1) from stock_allocate_import_lines sail where sail.head_id = #{headId} and del_flag = 0
    </select>

    <select id="selectAllocateImportLineByHeadId"
            parameterType="com.gz.eim.am.stock.dto.request.allocate.AllocateImportSearchReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.allocate.AllocateImportLineRespDTO">
         select
         sail.line_id as lineId,
         sail.head_id as headId,
         saih.active_name as activityName,
         sail.out_import_warehouse_name as outImportWarehouseName,
         sail.out_warehouse_code as outWarehouseCode,
         sail.in_import_warehouse_name as inImportWarehouseName,
         sail.in_warehouse_code as inWarehouseCode,
         sail.supplies_code as suppliesCode,
         sail.number as number,
         sail.status as status,
         sail.error_message as errorMessage,
         sw1.name as outWarehouseName,
         sw2.name as inWarehouseName,
         ss.name as suppliesName,
         sail.assets_code as assetsCode
         from
         stock_allocate_import_lines sail
         left join stock_warehouse sw1 on sw1.code = sail.out_warehouse_code
         left join stock_warehouse sw2 on sw2.code = sail.in_warehouse_code
         left join stock_supplies ss on ss.code = sail.supplies_code
         , stock_allocate_import_heads saih
         where sail.head_id = #{headId}
         and saih.del_flag = 0
         and sail.del_flag = 0
         and sail.head_id = saih.head_id
         LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectAllocateImportLineSumByHeadId"
            parameterType="com.gz.eim.am.stock.dto.response.allocate.AllocateImportHeadRespDTO"
            resultType="com.gz.eim.am.stock.dto.response.allocate.AllocateImportLineSumRespDTO">
        SELECT
        a.suppliesCode,
        ss.name AS suppliesName,
        a.sumNumber,
        CASE
           WHEN ss.purchase_price IS NULL THEN
           0
          ELSE ss.purchase_price
        END AS unitPrice,
        CASE
           WHEN ss.purchase_price IS NULL THEN 0
           ELSE ss.purchase_price * a.sumNumber
           END AS sumPrice
        FROM
           (SELECT
            sail.supplies_code AS suppliesCode,
            SUM(sail.number) AS sumNumber
            FROM
            stock_allocate_import_lines sail
            LEFT JOIN stock_supplies ss ON ss.code = sail.supplies_code
            WHERE
            sail.status = 0 AND sail.number > 0
            and sail.del_flag = 0
            AND sail.head_id = #{headId}
            GROUP BY sail.supplies_code) a
        LEFT JOIN stock_supplies ss ON ss.code = a.suppliesCode
        order by a.suppliesCode
    </select>

    <delete id="deleteStockAllocateImportLineByHeadId" parameterType="java.lang.Long">
        delete from stock_allocate_import_lines  where head_id = #{headId};
    </delete>

    <select id="selectAllocateImportLineBySelective" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLine" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_allocate_import_lines sail
        where 1=1
        <if test="lineId != null">
            and sail.line_id = #{lineId,jdbcType=BIGINT}
        </if>
        <if test="headId != null">
            and sail.head_id = #{headId,jdbcType=BIGINT}
        </if>
        <if test="outImportWarehouseName != null and outImportWarehouseName !='' ">
            and sail.out_import_warehouse_name = #{outImportWarehouseName,jdbcType=VARCHAR}
        </if>
        <if test="outWarehouseCode != null and outWarehouseCode !='' ">
            and sail.out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR}
        </if>
        <if test="inImportWarehouseName != null and inImportWarehouseName !='' ">
            and sail.in_import_warehouse_name = #{inImportWarehouseName,jdbcType=VARCHAR}
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode !='' ">
            and sail.in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR}
        </if>
        <if test="suppliesCode != null and suppliesCode !='' ">
            and sail.supplies_code = #{suppliesCode,jdbcType=VARCHAR}
        </if>
        <if test="number != null">
            and sail.number = #{number,jdbcType=INTEGER}
        </if>
        <if test="status != null">
            and sail.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="errorMessage != null and errorMessage !='' ">
            and sail.error_message = #{errorMessage,jdbcType=VARCHAR}
        </if>
        <if test="createdBy != null and createdBy !='' ">
            and sail.CREATED_BY = #{createdBy,jdbcType=VARCHAR}
        </if>
        <if test="updatedBy != null and updatedBy !='' ">
            and sail.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR}
        </if>
        <if test="delFlag != null">
            and sail.del_flag = #{delFlag,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectWflAllocateImportLineSumRespDTOByHeadCode" parameterType="java.lang.String"
            resultType="com.gz.eim.am.stock.dto.response.wfl.WflAllocateImportLineSumRespDTO">
         SELECT
        a.suppliesCode,
        ss.name AS suppliesName,
        a.sumNumber,
        CASE
           WHEN ss.purchase_price IS NULL THEN
           0
          ELSE ss.purchase_price
        END AS unitPrice,
        CASE
           WHEN ss.purchase_price IS NULL THEN 0
           ELSE ss.purchase_price * a.sumNumber
           END AS sumPrice
        FROM
           (SELECT
            sail.supplies_code AS suppliesCode,
            SUM(sail.number) AS sumNumber
            FROM
            stock_allocate_import_lines sail
            LEFT JOIN stock_supplies ss ON ss.code = sail.supplies_code,
            stock_allocate_import_heads saih
            WHERE
            sail.head_id = saih.head_id
            and sail.status = 0
            AND sail.number > 0
            and sail.del_flag = 0
            and saih.del_flag = 0
            and saih.head_code = #{headCode}
            GROUP BY sail.supplies_code) a
        LEFT JOIN stock_supplies ss ON ss.code = a.suppliesCode
        order by a.suppliesCode
    </select>

    <select id="selectAllocateImportLineSumPriceByHeadId" parameterType="java.lang.Long"
            resultType="java.math.BigDecimal">
         select sum(b.sumPrice) from
        (SELECT
        a.suppliesCode,
        CASE
           WHEN ss.purchase_price IS NULL THEN 0
           ELSE ss.purchase_price * a.sumNumber
           END AS sumPrice
        FROM
           (SELECT
            sail.supplies_code AS suppliesCode,
            SUM(sail.number) AS sumNumber
            FROM
            stock_allocate_import_lines sail
            LEFT JOIN stock_supplies ss ON ss.code = sail.supplies_code
            WHERE
            sail.head_id = #{headId}
            and sail.status = 0
            AND sail.number > 0
            and sail.del_flag = 0
            GROUP BY sail.supplies_code) a
        LEFT JOIN stock_supplies ss ON ss.code = a.suppliesCode) b
    </select>
    <select id="selectStockDeliveryPlanHeadListByHeadId" parameterType="java.lang.Long" resultType="com.gz.eim.am.stock.entity.StockDeliveryPlanHead">
        select
        sail.out_warehouse_code as outWarehouseCode,
        sail.in_warehouse_code as inWarehouseCode,
        sail.reason_code as reasonCode,
        saih.head_code as bizNo,
        saih.billing_user as billingUser,
        saih.active_name as remark
        from
        stock_allocate_import_lines sail,
        stock_allocate_import_heads saih
        where sail.head_id = saih.head_id
        and sail.del_flag = 0
        and sail.del_flag = 0
        and saih.head_id = #{headId}
<!--        and saih.status = 3-->
        and sail.status = 0
        and sail.number is not null
        group by sail.out_warehouse_code, sail.in_warehouse_code
    </select>

    <select id="selectStockDeliveryPlanLineListByParam" resultType="com.gz.eim.am.stock.entity.StockDeliveryPlanLine">
        select
        sail.supplies_code as suppliesCode,
        sum(sail.number) as number
        from
        stock_allocate_import_lines sail,
        stock_allocate_import_heads saih
        where sail.head_id = saih.head_id
        and sail.del_flag = 0
        and sail.del_flag = 0
        and saih.head_id = #{headId}
<!--		and saih.status = 3-->
        and sail.status = 0
        and sail.out_warehouse_code = #{outWarehouseCode}
        and sail.in_warehouse_code = #{inWarehouseCode}
        and sail.number is not null
        and sail.number > 0
        group by sail.supplies_code
    </select>

    <select id="selectLineRespDTOByList" parameterType="java.util.List" resultType="com.gz.eim.am.stock.dto.response.allocate.AllocateImportLineRespDTO">
        select
         sail.line_id as lineId,
         sail.head_id as headId,
         saih.active_name as activityName,
         sail.out_import_warehouse_name as outImportWarehouseName,
         sail.out_warehouse_code as outWarehouseCode,
         sail.in_import_warehouse_name as inImportWarehouseName,
         sail.in_warehouse_code as inWarehouseCode,
         sail.supplies_code as suppliesCode,
         sail.number as number,
         sail.status as status,
         sail.error_message as errorMessage,
         sw1.name as outWarehouseName,
         sw2.name as inWarehouseName,
         ss.name as suppliesName
         from
         stock_allocate_import_lines sail
         left join stock_warehouse sw1 on sw1.code = sail.out_warehouse_code
         left join stock_warehouse sw2 on sw2.code = sail.in_warehouse_code
         left join stock_supplies ss on ss.code = sail.supplies_code
         , stock_allocate_import_heads saih
         where saih.del_flag = 0
         and sail.del_flag = 0
         and sail.head_id = saih.head_id
         and sail.line_id in
        <foreach collection="stockAllocateImportLineList" item="item" index="index" open="(" separator="," close=")">
            #{item.lineId}
        </foreach>
    </select>

    <select id="selectAllocateImportLineByStockAllocateImportLineList" parameterType="com.gz.eim.am.stock.entity.StockAllocateImportLine" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stock_allocate_import_lines
        where
        1=1
        and
        <foreach collection="list" item="item" index="index" open="(" separator="or" close=")">
            (head_id = #{item.headId}
            and out_warehouse_code = #{item.outWarehouseCode}
            and in_warehouse_code = #{item.inWarehouseCode})
        </foreach>
    </select>


</mapper>