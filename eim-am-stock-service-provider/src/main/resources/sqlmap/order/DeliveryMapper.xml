<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.order.DeliveryMapper">
    <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockDelivery">
        <id column="delivery_id" jdbcType="BIGINT" property="deliveryId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo"/>
        <result column="from_system" jdbcType="VARCHAR" property="fromSystem"/>
        <result column="from_model" jdbcType="VARCHAR" property="fromModel"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="linkman" jdbcType="VARCHAR" property="linkman"/>
        <result column="contact_way" jdbcType="VARCHAR" property="contactWay"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="logistics_channel" jdbcType="VARCHAR" property="logisticsChannel"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode"/>
        <result column="in_warehouse_code" jdbcType="VARCHAR" property="inWarehouseCode"/>
        <result column="out_stock_time" jdbcType="TIMESTAMP" property="outStockTime"/>
        <result column="out_stock_type" jdbcType="INTEGER" property="outStockType"/>
        <result column="is_send" jdbcType="INTEGER" property="isSend"/>
        <result column="send_channel" jdbcType="VARCHAR" property="sendChannel"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="weight" jdbcType="INTEGER" property="weight"/>
        <result column="volume" jdbcType="INTEGER" property="volume"/>
        <result column="approval_status" jdbcType="TINYINT" property="approvalStatus"/>
        <result column="duty_user" jdbcType="VARCHAR" property="dutyUser"/>
        <result column="use_user" jdbcType="VARCHAR" property="useUser"/>
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
        <result column="billing_user" jdbcType="VARCHAR" property="billingUser"/>
        <result column="billing_time" jdbcType="TIMESTAMP" property="billingTime"/>
        <result column="plan_out_time" jdbcType="TIMESTAMP" property="planOutTime"/>
        <result column="reason_code" jdbcType="INTEGER" property="reasonCode"/>
        <result column="delivery_plan_head_id" jdbcType="BIGINT" property="deliveryPlanHeadId"/>
    </resultMap>
    <sql id="Base_Column_List">
        delivery_id, order_id, order_no, delivery_no, from_system, from_model, receive_time,
        country, province, city, area, address, linkman, contact_way, zip_code, remark, dept_code,
        activity_code, logistics_channel, status, warehouse_code, in_warehouse_code, out_stock_time,
        out_stock_type, is_send, send_channel, send_time, delivery_time, weight, volume,
        approval_status, duty_user, use_user, biz_no, created_by, created_at, updated_by,
        updated_at, billing_user, billing_time, plan_out_time, reason_code, delivery_plan_head_id
    </sql>
    <insert id="insert" parameterType="com.gz.eim.am.stock.entity.StockDelivery"
            useGeneratedKeys="true"
            keyProperty="deliveryId" keyColumn="delivery_id">
        
        insert into stock_delivery (delivery_id, order_id, order_no,
        delivery_no, from_system, from_model,
        receive_time, country, province,
        city, area, address,
        linkman, contact_way, zip_code,
        remark, dept_code, activity_code,
        logistics_channel, status, warehouse_code,
        in_warehouse_code, out_stock_time, out_stock_type,
        is_send, send_channel, send_time,
        delivery_time, weight, volume,
        approval_status, duty_user, use_user,
        biz_no, created_by,
        created_at, updated_by, updated_at,
        billing_user, billing_time, plan_out_time,
        reason_code, delivery_plan_head_id, use_user_dept
        )
        values (#{deliveryId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR},
        #{deliveryNo,jdbcType=VARCHAR}, #{fromSystem,jdbcType=VARCHAR}, #{fromModel,jdbcType=VARCHAR},
        #{receiveTime,jdbcType=TIMESTAMP}, #{country,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
        #{linkman,jdbcType=VARCHAR}, #{contactWay,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{deptCode,jdbcType=VARCHAR}, #{activityCode,jdbcType=VARCHAR},
        #{logisticsChannel,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{warehouseCode,jdbcType=VARCHAR},
        #{inWarehouseCode,jdbcType=VARCHAR}, #{outStockTime,jdbcType=TIMESTAMP}, #{outStockType,jdbcType=INTEGER},
        #{isSend,jdbcType=INTEGER}, #{sendChannel,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP},
        #{deliveryTime,jdbcType=TIMESTAMP}, #{weight,jdbcType=INTEGER}, #{volume,jdbcType=INTEGER},
        #{approvalStatus,jdbcType=INTEGER}, #{dutyUser,jdbcType=VARCHAR}, #{useUser,jdbcType=VARCHAR},
        #{bizNo,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedAt,jdbcType=TIMESTAMP},
        #{billingUser,jdbcType=VARCHAR}, #{billingTime,jdbcType=TIMESTAMP}, #{planOutTime,jdbcType=TIMESTAMP},
        #{reasonCode,jdbcType=INTEGER}, #{deliveryPlanHeadId, jdbcType=BIGINT}, #{useUserDept,jdbcType=VARCHAR}
        )
    </insert>

    <select id="selectInventoryOutFlow" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.InventoryFlowRespDTO">
        SELECT
        t.flowId,
        t.warehouse_code warehouseCode,
        2 ioType,
        t.out_stock_type bizType,
        t.dept_code costCenterCode,
        dept.dept_name costCenterName,
        t.create_user createUser,
        t.billing_user dutyUser,
        t.remark remark,
        t.out_stock_time flowTime,
        t.supplies_code suppliesCode,
        t.batch_no batchNo,
        t.sn_no snNo,
        t.number number,
        ss.name suppliesName,
        unit.unit_name unit,
        t.warehouseName,
        user1.name createUserName,
        user2.name dutyUserName,
        dept.dept_name deptName,
        ss.remark suppliesRemark
        FROM
        (SELECT
        sdd.delivery_detail_id flowId,
        sd.warehouse_code,
        sd.out_stock_type,
        sd.dept_code,
        sd.created_at,
        sd.use_user,
        sd.duty_user,
        sd.remark,
        sd.out_stock_time,
        sdd.supplies_code,
        sdd.batch_no,
        sdd.sn_no,
        sdd.real_number number,
        sd.billing_user,
        sdph.billing_user create_user,
        sw.name warehouseName
        FROM stock_delivery sd
        LEFT JOIN stock_delivery_plan_heads sdph on sd.delivery_plan_head_id = sdph.delivery_plan_head_id
        LEFT JOIN stock_delivery_detail sdd ON sd.delivery_id =sdd.delivery_id
        ,stock_warehouse sw
        WHERE 1 = 1
        <if test="createUser != null and createUser != ''">
            AND sdph.billing_user = #{createUser}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="bizType != null">
            AND sd.out_stock_type = #{bizType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND sd.dept_code = #{deptCode}
        </if>
        <if test="bizStartTime != null and bizStartTime != ''">
            AND DATE_FORMAT(sd.out_stock_time,'%Y-%m-%d') >= #{bizStartTime}
        </if>
        <if test="bizEndTime != null and bizEndTime != ''">
            AND DATE_FORMAT(sd.out_stock_time,'%Y-%m-%d') &lt;= #{bizEndTime}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sdd.supplies_code = #{suppliesCode}
        </if>
        <if test="manageNo != null and manageNo != ''">
            AND exists (select 1 from stock_delivery_detail_sns siiss where
            sdd.delivery_detail_id=siiss.delivery_detail_id
            and siiss.SERIAL_NUMBER=#{manageNo})
        </if>
        and sw.code = sd.warehouse_code
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and sw.code in
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        UNION ALL
        SELECT
        sddh.delivery_detail_id flowId,
        sdh.warehouse_code,
        sdh.out_stock_type,
        sdh.dept_code,
        sdh.created_at,
        sdh.use_user,
        sdh.duty_user,
        sdh.billing_user,
        sdph.billing_user create_user,
        sdh.remark,
        sdh.out_stock_time,
        sddh.supplies_code,
        sddh.batch_no,
        sddh.sn_no,
        sddh.real_number number,
        sw.name warehouseName
        FROM stock_delivery_history sdh
        LEFT JOIN stock_delivery_plan_heads sdph on sdh.delivery_plan_head_id = sdph.delivery_plan_head_id
        LEFT JOIN stock_delivery_detail_history sddh ON sdh.delivery_id =sddh.delivery_id
        , stock_warehouse sw
        WHERE 1 = 1
        <if test="createUser != null and createUser != ''">
            AND sdph.billing_user = #{createUser}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sdh.warehouse_code = #{warehouseCode}
        </if>
        <if test="bizType != null">
            AND sdh.out_stock_type = #{bizType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND sdh.dept_code = #{deptCode}
        </if>
        <if test="bizStartTime != null and bizStartTime != ''">
            AND DATE_FORMAT(sdh.out_stock_time,'%Y-%m-%d') >= #{bizStartTime}
        </if>
        <if test="bizEndTime != null and bizEndTime != ''">
            AND DATE_FORMAT(sdh.out_stock_time,'%Y-%m-%d') &lt;= #{bizEndTime}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sddh.supplies_code = #{suppliesCode}
        </if>
        <if test="manageNo != null and manageNo != ''">
            AND exists (select 1 from stock_delivery_detail_sns_history siiss where
            sddh.delivery_detail_id=siiss.delivery_detail_id
            and siiss.SERIAL_NUMBER=#{manageNo})
        </if>
        and sw.code = sdh.warehouse_code
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and sw.code in
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        ) t
        LEFT JOIN stock_supplies ss ON t.supplies_code = ss.code
        LEFT JOIN stock_supplies_unit unit ON ss.unit_code = unit.unit_code
        LEFT JOIN stock_warehouse sw on t.warehouse_code = sw.code
        LEFT JOIN ambase.sys_user user1 on t.create_user = user1.emp_id
        LEFT JOIN ambase.sys_user user2 on t.billing_user = user2.emp_id
        LEFT JOIN ambase.sys_dept dept on t.dept_code = dept.dept_id
        ORDER BY t.created_at DESC
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>
    <select id="countInventoryOutFlow" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO"
            resultType="java.lang.Long">
        SELECT SUM(total)
        FROM
        (SELECT
        COUNT(*) total
        FROM stock_delivery sd
        LEFT JOIN stock_delivery_plan_heads sdph on sd.delivery_plan_head_id = sdph.delivery_plan_head_id
        LEFT JOIN stock_delivery_detail sdd ON sd.delivery_id =sdd.delivery_id
        , stock_warehouse sw
        WHERE 1 = 1
        <if test="createUser != null and createUser != ''">
            AND sdph.billing_user = #{createUser}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="bizType != null">
            AND sd.out_stock_type = #{bizType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND sd.dept_code = #{deptCode}
        </if>
        <if test="bizStartTime != null and bizStartTime != ''">
            AND DATE_FORMAT(sd.out_stock_time,'%Y-%m-%d') >= #{bizStartTime}
        </if>
        <if test="bizEndTime != null and bizEndTime != ''">
            AND DATE_FORMAT(sd.out_stock_time,'%Y-%m-%d') &lt;= #{bizEndTime}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sdd.supplies_code = #{suppliesCode}
        </if>
        <if test="manageNo != null and manageNo != ''">
            AND exists (select 1 from stock_delivery_detail_sns siiss where
            sdd.delivery_detail_id=siiss.delivery_detail_id
            and siiss.SERIAL_NUMBER=#{manageNo})
        </if>
        and sw.code = sd.warehouse_code
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and sw.code in
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        UNION ALL
        SELECT
        COUNT(*) total
        FROM stock_delivery_history sdh
        LEFT JOIN stock_delivery_plan_heads sdph on sdh.delivery_plan_head_id = sdph.delivery_plan_head_id
        LEFT JOIN stock_delivery_detail_history sddh ON sdh.delivery_id =sddh.delivery_id
        , stock_warehouse sw
        WHERE 1 = 1
        <if test="createUser != null and createUser != ''">
            AND sdph.billing_user = #{createUser}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sdh.warehouse_code = #{warehouseCode}
        </if>
        <if test="bizType != null">
            AND sdh.out_stock_type = #{bizType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND sdh.dept_code = #{deptCode}
        </if>
        <if test="bizStartTime != null and bizStartTime != ''">
            AND DATE_FORMAT(sdh.out_stock_time,'%Y-%m-%d') >= #{bizStartTime}
        </if>
        <if test="bizEndTime != null and bizEndTime != ''">
            AND DATE_FORMAT(sdh.out_stock_time,'%Y-%m-%d') &lt;= #{bizEndTime}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sddh.supplies_code = #{suppliesCode}
        </if>
        <if test="manageNo != null and manageNo != ''">
            AND exists (select 1 from stock_delivery_detail_sns_history siiss where
            sddh.delivery_detail_id=siiss.delivery_detail_id
            and siiss.SERIAL_NUMBER=#{manageNo})
        </if>
        and sw.code = sdh.warehouse_code
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            and sw.code in
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        ) t
    </select>
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        DELETE FROM stock_delivery
        WHERE delivery_id IN (
        <foreach collection="list" item="deliveryId" index="index" separator=",">
            #{deliveryId}
        </foreach>)
    </delete>
    <update id="batchUpdateStatus">
        UPDATE stock_delivery
        SET status = #{status,jdbcType=INTEGER}
        WHERE delivery_id IN
        <foreach collection="list" item="deliveryId" index="index" open="(" separator="," close=")">
            #{deliveryId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectInventoryOutRespDTOByDeliveryId" parameterType="java.lang.Long"
            resultType="com.gz.eim.am.stock.dto.response.order.InventoryOutRespDTO">
        select
        sd.delivery_id deliveryId,
        sd.out_stock_type outStockType,
        sd.delivery_no deliveryNo,
        sd.billing_user billingUser,
        CONCAT_WS(' ',su1.name, sd.billing_user) billingUserName,
        sd.warehouse_code warehouseCode,
        sw1.name warehouseName,
        swb1.address warehouseAddress,
        sd.in_warehouse_code inWarehouseCode,
        sw2.name inWarehouseName,
        sd.reason_code reasonCode,
        sd.status,
        sd.remark,
        sd.duty_user dutyUser,
        CONCAT_WS(' ',su2.name, sd.duty_user) dutyUserName,
        CONCAT_WS(' ',su3.name, sd.use_user) useUserName,
        sd.use_user useUser
        from
        stock_delivery sd
        LEFT JOIN ambase.sys_user su1 on sd.billing_user = su1.emp_id
        LEFT JOIN stock_warehouse sw1 on sd.warehouse_code = sw1.code
        LEFT JOIN stock_warehouse_base swb1 on sd.warehouse_code = swb1.warehouse_code
        LEFT JOIN stock_warehouse sw2 on sd.in_warehouse_code = sw2.code
        LEFT JOIN ambase.sys_user su2 on sd.duty_user = su2.emp_id
        LEFT JOIN ambase.sys_user su3 on sd.use_user = su3.emp_id
        where sd.delivery_id = #{deliveryId}

    </select>

    <select id="selectCountByParam" parameterType="com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from
        stock_delivery sd
        where
        1=1
        <if test="deliveryNo != null and deliveryNo != ''">
            and sd.delivery_no like CONCAT('%',#{deliveryNo}, '%')
        </if>
        <if test="bizNo != null and bizNo != ''">
            and sd.biz_no like CONCAT('%',#{bizNo}, '%')
        </if>
        <if test="status != null">
            and sd.status = #{status}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode != ''">
            and sd.in_warehouse_code = #{inWarehouseCode}
        </if>
        <if test="dutyUser != null and dutyUser != ''">
            and sd.duty_user = #{dutyUser}
        </if>
        <if test="useUser != null and useUser != ''">
            and sd.use_user = #{useUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanOutTime != null and beginPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &gt;= #{beginPlanOutTime}
        </if>
        <if test="endPlanOutTime != null and endPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &lt;= #{endPlanOutTime}
        </if>
        <if test="billingUser != null and billingUser != ''">
            and sd.billing_user = #{billingUser}
        </if>
        <if test="outStockType != null">
            and sd.out_stock_type = #{outStockType}
        </if>
        <if test="codes != null and codes.size() > 0">
            AND sd.warehouse_code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
            <if test="outStockType != null and outStockType == 3">
                and sd.in_warehouse_code IN
                <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </if>
        <if test="outStockTypeList != null and outStockTypeList.size() > 0">
            AND sd.out_stock_type IN
            <foreach collection="outStockTypeList" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>

    <select id="selectByPage" parameterType="com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.order.InventoryOutRespDTO">
        select
        sd.delivery_id deliveryId,
        sd.delivery_no deliveryNo,
        sd.reason_code reasonCode,
        CONCAT_WS(' ',su1.name, sd.duty_user) dutyUserName,
        CONCAT_WS(' ',su2.name, sd.use_user) useUserName,
        DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') planOutTime,
        sw1.name warehouseName,
        swb1.address warehouseAddress,
        sw2.name inWarehouseName,
        sd.status,
        sd.out_stock_type outStockType
        from
        stock_delivery sd
        left join ambase.sys_user su1 on sd.duty_user = su1.emp_id
        left join ambase.sys_user su2 on sd.use_user = su2.emp_id
        left join stock_warehouse_base swb1 on sd.warehouse_code = swb1.warehouse_code
        LEFT JOIN stock_warehouse sw1 on sd.warehouse_code = sw1.code
        left join stock_warehouse_base swb2 on sd.in_warehouse_code = swb2.warehouse_code
        LEFT JOIN stock_warehouse sw2 on sd.in_warehouse_code = sw2.code
        where
        1=1
        <if test="deliveryNo != null and deliveryNo != ''">
            and sd.delivery_no like CONCAT('%',#{deliveryNo}, '%')
        </if>
        <if test="bizNo != null and bizNo != ''">
            and sd.biz_no like CONCAT('%',#{bizNo}, '%')
        </if>
        <if test="status != null">
            and sd.status = #{status}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode != ''">
            and sd.in_warehouse_code = #{inWarehouseCode}
        </if>
        <if test="dutyUser != null and dutyUser != ''">
            and sd.duty_user = #{dutyUser}
        </if>
        <if test="useUser != null and useUser != ''">
            and sd.use_user = #{useUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanOutTime != null and beginPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &gt;= #{beginPlanOutTime}
        </if>
        <if test="endPlanOutTime != null and endPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &lt;= #{endPlanOutTime}
        </if>
        <if test="billingUser != null and billingUser != ''">
            and sd.billing_user = #{billingUser}
        </if>
        <if test="outStockType != null">
            and sd.out_stock_type = #{outStockType}
        </if>
        <if test="codes != null and codes.size() > 0">
            AND sd.warehouse_code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
            <if test="outStockType != null and outStockType == 3">
                and sd.in_warehouse_code IN
                <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </if>
        <if test="outStockTypeList != null and outStockTypeList.size() > 0">
            AND sd.out_stock_type IN
            <foreach collection="outStockTypeList" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        ORDER BY sd.updated_at desc, sd.delivery_no desc
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectAccessibleCountByParam"
            parameterType="com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO"
            resultType="java.lang.Long">
        select
        count(1)
        from
        stock_delivery sd
        where
        1=1
        and sd.status in (6, 7)
        <if test="deliveryNo != null and deliveryNo != ''">
            and sd.delivery_no like CONCAT('%',#{deliveryNo}, '%')
        </if>
        <if test="bizNo != null and bizNo != ''">
            and sd.biz_no like CONCAT('%',#{bizNo}, '%')
        </if>
        <if test="status != null">
            and sd.status = #{status}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode != ''">
            and sd.in_warehouse_code = #{inWarehouseCode}
        </if>
        <if test="dutyUser != null and dutyUser != ''">
            and sd.duty_user = #{dutyUser}
        </if>
        <if test="useUser != null and useUser != ''">
            and sd.use_user = #{useUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanOutTime != null and beginPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &gt;= #{beginPlanOutTime}
        </if>
        <if test="endPlanOutTime != null and endPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &lt;= #{endPlanOutTime}
        </if>
        <if test="billingUser != null and billingUser != ''">
            and sd.billing_user = #{billingUser}
        </if>
        <if test="outStockType != null">
            and sd.out_stock_type = #{outStockType}
        </if>
        <if test="codes != null and codes.size() > 0">
            AND sd.warehouse_code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
            <if test="outStockType != null and outStockType == 3">
                and sd.in_warehouse_code IN
                <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </if>
    </select>

    <select id="selectAccessibleByPage" parameterType="com.gz.eim.am.stock.dto.request.order.InventoryOutSearchReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.order.InventoryOutRespDTO">
        select
        sd.delivery_id deliveryId,
        sd.delivery_no deliveryNo,
        sd.reason_code reasonCode,
        CONCAT_WS(' ',su1.name, sd.duty_user) dutyUserName,
        DATE_FORMAT(sd.plan_out_time, '%Y年%m月%d日') planOutTime,
        sw1.name warehouseName,
        swb1.address warehouseAddress,
        sw2.name inWarehouseName,
        sd.status,
        (
        SELECT
        CONCAT( ss.NAME, '等' )
        FROM
        stock_supplies ss,
        stock_delivery_detail sdd
        WHERE
        sdd.supplies_code = ss.CODE
        AND sdd.delivery_id = sd.delivery_id
        LIMIT 1
        ) supplierName,
        sd.out_stock_type outStockType
        from
        stock_delivery sd
        left join ambase.sys_user su1 on sd.duty_user = su1.emp_id
        left join stock_warehouse_base swb1 on sd.warehouse_code = swb1.warehouse_code
        LEFT JOIN stock_warehouse sw1 on sd.warehouse_code = sw1.code
        left join stock_warehouse_base swb2 on sd.in_warehouse_code = swb2.warehouse_code
        LEFT JOIN stock_warehouse sw2 on sd.in_warehouse_code = sw2.code
        where
        1=1
        and sd.status in (6, 7)
        <if test="deliveryNo != null and deliveryNo != ''">
            and sd.delivery_no like CONCAT('%',#{deliveryNo}, '%')
        </if>
        <if test="bizNo != null and bizNo != ''">
            and sd.biz_no like CONCAT('%',#{bizNo}, '%')
        </if>
        <if test="status != null">
            and sd.status = #{status}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="inWarehouseCode != null and inWarehouseCode != ''">
            and sd.in_warehouse_code = #{inWarehouseCode}
        </if>
        <if test="dutyUser != null and dutyUser != ''">
            and sd.duty_user = #{dutyUser}
        </if>
        <if test="useUser != null and useUser != ''">
            and sd.use_user = #{useUser}
        </if>
        <if test="beginBillingTime != null and beginBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &gt;= #{beginBillingTime}
        </if>
        <if test="endBillingTime != null and endBillingTime != ''">
            and DATE_FORMAT(sd.billing_time, '%Y-%m-%d') &lt;= #{endBillingTime}
        </if>
        <if test="beginPlanOutTime != null and beginPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &gt;= #{beginPlanOutTime}
        </if>
        <if test="endPlanOutTime != null and endPlanOutTime != ''">
            and DATE_FORMAT(sd.plan_out_time, '%Y-%m-%d') &lt;= #{endPlanOutTime}
        </if>
        <if test="billingUser != null and billingUser != ''">
            and sd.billing_user = #{billingUser}
        </if>
        <if test="outStockType != null">
            and sd.out_stock_type = #{outStockType}
        </if>
        <if test="codes != null and codes.size() > 0">
            AND sd.warehouse_code IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
            <if test="outStockType != null and outStockType == 3">
                and sd.in_warehouse_code IN
                <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </if>
        ORDER BY sd.updated_at desc, sd.delivery_no desc
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="selectDeliverySuppliesRespDTOByParam"
            resultType="com.gz.eim.am.stock.dto.response.external.DeliverySuppliesRespDTO">
        SELECT a.warehouse_code as warehouseCode,
        a.supplies_code suppliesCode,
        a.sn_no snNo,
        CONCAT(a.supplies_code, '_', a.sn_no) as suppliesCodeAndSnNo,
        ss.name suppliesName,
        ss.brand_code as brandCode
        from
        (
        select * from (SELECT
        sd.warehouse_code,
        sdd.supplies_code,
        sdd.sn_no,
        sd.out_stock_type,
        sd.out_stock_time
        FROM stock_delivery sd
        LEFT JOIN stock_delivery_detail sdd ON sd.delivery_id =sdd.delivery_id
        WHERE 1 = 1
        AND sdd.sn_no = #{snNo}
        union
        SELECT
        sdh.warehouse_code,
        sddh.supplies_code,
        sddh.sn_no,
        sdh.out_stock_type,
        sdh.out_stock_time
        FROM stock_delivery_history sdh
        LEFT JOIN stock_delivery_detail_history sddh ON sdh.delivery_id =sddh.delivery_id
        WHERE 1 = 1
        AND sddh.sn_no = #{snNo}) b
        order by b.out_stock_time desc
        limit 1
        ) a left join stock_supplies ss on ss.code = a.supplies_code
        where a.out_stock_type = #{outStockType};
    </select>
    <select id="selectSnCountByParam" parameterType="com.gz.eim.am.stock.dto.request.order.InventoryOutSnSearchReqDTO"
            resultType="java.lang.Long">
        SELECT SUM(t.num) FROM (SELECT COUNT(1) num
        FROM
        stock_delivery sd,
        stock_delivery_detail sdd,
        stock_delivery_detail_sns sdds
        WHERE
        sd.`delivery_id` = sdd.delivery_id
        AND sdd.delivery_detail_id = sdds.delivery_detail_id
        AND sdd.`delivery_detail_id` = #{flowId}
        <if test="codes != null and codes.size() > 0">
            AND sd.`warehouse_code` IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="snNo != null and snNo != ''">
            AND sdds.SERIAL_NUMBER = #{snNo}
        </if>
        UNION ALL
        SELECT COUNT(1) num
        FROM
        stock_delivery_history sdh,
        stock_delivery_detail_history sddh,
        stock_delivery_detail_sns_history sddsh
        WHERE
        sdh.`delivery_id` = sddh.delivery_id
        AND sddh.delivery_detail_id = sddsh.delivery_detail_id
        AND sddh.`delivery_detail_id` = #{flowId}
        <if test="codes != null and codes.size() > 0">
            AND sdh.`warehouse_code` IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="snNo != null and snNo != ''">
            AND sddsh.SERIAL_NUMBER = #{snNo}
        </if>) t
    </select>

    <select id="selectSnByPage" parameterType="com.gz.eim.am.stock.dto.request.order.InventoryOutSnSearchReqDTO"
            resultType="java.lang.String">
        SELECT t.SERIAL_NUMBER FROM (SELECT sdds.SERIAL_NUMBER
        FROM
        stock_delivery sd,
        stock_delivery_detail sdd,
        stock_delivery_detail_sns sdds
        WHERE
        sd.`delivery_id` = sdd.delivery_id
        AND sdd.delivery_detail_id = sdds.delivery_detail_id
        AND sdd.`delivery_detail_id` = #{flowId}
        <if test="codes != null and codes.size() > 0">
            AND sd.`warehouse_code` IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="snNo != null and snNo != ''">
            AND sdds.SERIAL_NUMBER = #{snNo}
        </if>
        UNION ALL
        SELECT sddsh.SERIAL_NUMBER
        FROM
        stock_delivery_history sdh,
        stock_delivery_detail_history sddh,
        stock_delivery_detail_sns_history sddsh
        WHERE
        sdh.`delivery_id` = sddh.delivery_id
        AND sddh.delivery_detail_id = sddsh.delivery_detail_id
        AND sddh.`delivery_detail_id` = #{flowId}
        <if test="codes != null and codes.size() > 0">
            AND sdh.`warehouse_code` IN
            <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="snNo != null and snNo != ''">
            AND sddsh.SERIAL_NUMBER = #{snNo}
        </if>) t
        ORDER BY t.`SERIAL_NUMBER`
        LIMIT #{pageSize} OFFSET #{startNum}
    </select>

    <select id="exportInventoryOutFlow" parameterType="com.gz.eim.am.stock.dto.request.inventory.InventoryFlowReqDTO"
            resultType="com.gz.eim.am.stock.dto.response.InventoryFlowRespDTO">
        SELECT
        t.warehouse_code warehouseCode,
        t.out_stock_type bizType,
        t.remark remark,
        t.out_stock_time flowTime,
        t.supplies_code suppliesCode,
        t.batch_no batchNo,
        t.SERIAL_NUMBER snNo,
        t.number number,
        unit.unit_name unit,
        t.warehouseName,
        user1.name createUserName,
        user2.name dutyUserName,
        dept.dept_full_name deptName,
        ss.remark suppliesRemark
        FROM
        (SELECT
        sd.warehouse_code,
        sd.out_stock_type,
        sd.dept_code,
        sd.created_at,
        sd.use_user,
        sd.duty_user,
        sd.remark,
        sd.out_stock_time,
        sdd.supplies_code,
        sdd.batch_no,
        sdds.`SERIAL_NUMBER`,
        sdd.real_number number,
        sw.name warehouseName
        FROM stock_delivery sd
        LEFT JOIN stock_delivery_detail sdd ON sd.delivery_id =sdd.delivery_id
        LEFT JOIN stock_delivery_detail_sns sdds ON sdds.`delivery_detail_id` = sdd.`delivery_detail_id`
        ,stock_warehouse sw
        WHERE 1 = 1
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sd.warehouse_code = #{warehouseCode}
        </if>
        <if test="bizType != null">
            AND sd.out_stock_type = #{bizType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND sd.dept_code = #{deptCode}
        </if>
        <if test="bizStartTime != null and bizStartTime != ''">
            AND DATE_FORMAT(sd.out_stock_time,'%Y-%m-%d') >= #{bizStartTime}
        </if>
        <if test="bizEndTime != null and bizEndTime != ''">
            AND DATE_FORMAT(sd.out_stock_time,'%Y-%m-%d') &lt;= #{bizEndTime}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sdd.supplies_code = #{suppliesCode}
        </if>
        <if test="manageNo != null and manageNo != ''">
            AND (sdd.batch_no = #{manageNo} OR sdds.`SERIAL_NUMBER` = #{manageNo})
        </if>
        AND sw.code = sd.warehouse_code
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            AND sw.code IN
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        UNION ALL
        SELECT
        sdh.warehouse_code,
        sdh.out_stock_type,
        sdh.dept_code,
        sdh.created_at,
        sdh.use_user,
        sdh.duty_user,
        sdh.remark,
        sdh.out_stock_time,
        sddh.supplies_code,
        sddh.batch_no,
        sddsh.SERIAL_NUMBER,
        sddh.real_number number,
        sw.name warehouseName
        FROM stock_delivery_history sdh
        LEFT JOIN stock_delivery_detail_history sddh ON sdh.delivery_id =sddh.delivery_id
        LEFT JOIN stock_delivery_detail_sns_history sddsh ON sddsh.delivery_detail_id = sddh.delivery_detail_id
        , stock_warehouse sw
        WHERE 1 = 1
        <if test="warehouseCode != null and warehouseCode != ''">
            AND sdh.warehouse_code = #{warehouseCode}
        </if>
        <if test="bizType != null">
            AND sdh.out_stock_type = #{bizType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND sdh.dept_code = #{deptCode}
        </if>
        <if test="bizStartTime != null and bizStartTime != ''">
            AND DATE_FORMAT(sdh.out_stock_time,'%Y-%m-%d') >= #{bizStartTime}
        </if>
        <if test="bizEndTime != null and bizEndTime != ''">
            AND DATE_FORMAT(sdh.out_stock_time,'%Y-%m-%d') &lt;= #{bizEndTime}
        </if>
        <if test="suppliesCode != null and suppliesCode != ''">
            AND sddh.supplies_code = #{suppliesCode}
        </if>
        <if test="manageNo != null and manageNo != ''">
            AND (sddh.batch_no = #{manageNo} OR sddsh.SERIAL_NUMBER = #{manageNo})
        </if>
        AND sw.code = sdh.warehouse_code
        <if test="warehouseCodeList != null and warehouseCodeList.size > 0">
            AND sw.code IN
            <foreach collection="warehouseCodeList" item="warehouseCode" open="(" close=")" separator=",">
                #{warehouseCode}
            </foreach>
        </if>
        ) t
        LEFT JOIN stock_supplies ss ON t.supplies_code = ss.code
        LEFT JOIN stock_supplies_unit unit ON ss.unit_code = unit.unit_code
        LEFT JOIN stock_warehouse sw ON t.warehouse_code = sw.code
        LEFT JOIN ambase.sys_user user1 ON t.use_user = user1.emp_id
        LEFT JOIN ambase.sys_user user2 ON t.duty_user = user2.emp_id
        LEFT JOIN ambase.sys_dept dept ON t.dept_code = dept.dept_id
        ORDER BY t.created_at DESC
    </select>

    <insert id="batchInsert" parameterType="com.gz.eim.am.stock.entity.StockDelivery"
            useGeneratedKeys="true" keyProperty="deliveryId" keyColumn="delivery_id">
        insert into stock_delivery (delivery_id, order_id, order_no,
        delivery_no, from_system, from_model,
        receive_time, country, province,
        city, area, address,
        linkman, contact_way, zip_code,
        remark, dept_code, activity_code,
        logistics_channel, status, warehouse_code,
        in_warehouse_code, out_stock_time, out_stock_type,
        is_send, send_channel, send_time,
        delivery_time, weight, volume,
        approval_status, duty_user, use_user,
        biz_no, created_by, updated_by,
        billing_user, billing_time, plan_out_time,
        reason_code, delivery_plan_head_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deliveryId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=VARCHAR},
            #{item.deliveryNo,jdbcType=VARCHAR}, #{item.fromSystem,jdbcType=VARCHAR},
            #{item.fromModel,jdbcType=VARCHAR},
            #{item.receiveTime,jdbcType=TIMESTAMP}, #{item.country,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR},
            #{item.linkman,jdbcType=VARCHAR}, #{item.contactWay,jdbcType=VARCHAR}, #{item.zipCode,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.deptCode,jdbcType=VARCHAR}, #{item.activityCode,jdbcType=VARCHAR},
            #{item.logisticsChannel,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER},
            #{item.warehouseCode,jdbcType=VARCHAR},
            #{item.inWarehouseCode,jdbcType=VARCHAR}, #{item.outStockTime,jdbcType=TIMESTAMP},
            #{item.outStockType,jdbcType=INTEGER},
            #{item.isSend,jdbcType=INTEGER}, #{item.sendChannel,jdbcType=VARCHAR}, #{item.sendTime,jdbcType=TIMESTAMP},
            #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.weight,jdbcType=INTEGER}, #{item.volume,jdbcType=INTEGER},
            #{item.approvalStatus,jdbcType=TINYINT}, #{item.dutyUser,jdbcType=VARCHAR},
            #{item.useUser,jdbcType=VARCHAR},
            #{item.bizNo,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.billingUser,jdbcType=VARCHAR},
            #{item.billingTime,jdbcType=TIMESTAMP}, #{item.planOutTime,jdbcType=TIMESTAMP},
            #{item.reasonCode,jdbcType=INTEGER}, #{item.deliveryPlanHeadId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <select id="selectSuppliesConfigRespDTOList"
            resultType="com.gz.eim.am.stock.dto.response.supplies.SuppliesConfigRespDTO">
        SELECT
        sdd.real_warehouse_code warehouseCode,
        sdd.supplies_code suppliesCode,
        sum(if(sdd.real_number is null,0,sdd.real_number)) as useNumber
        FROM stock_delivery sd,
        stock_delivery_detail sdd
        WHERE
        sdd.delivery_id = sd.delivery_id
        and sd.status IN (7, 8, 9, 10, 30)
        AND sdd.status IN(2, 3)
        and sd.warehouse_code in
        <foreach collection="wareCodeList" item="wareCode" open="(" close=")" separator=",">
            #{wareCode}
        </foreach>
        and sdd.supplies_code in
        <foreach collection="suppliesCodeList" item="suppliesCode" open="(" close=")" separator=",">
            #{suppliesCode}
        </foreach>
        GROUP BY sdd.supplies_code ,sd.warehouse_code
    </select>

    <select id="selectSuppliesConfigDetailRespDTOList"
            resultType="com.gz.eim.am.stock.dto.response.manage.StockSuppliesConfigDetailRespDTO">
        SELECT m.supplies_code suppliesCode,m.sn_no manageNo,sum( m.number ) allNumber
        FROM (
        SELECT
        sdd.supplies_code,
        sdds.SERIAL_NUMBER sn_no,
        1 number
        FROM stock_delivery sd
        JOIN stock_delivery_detail sdd ON sdd.delivery_id = sd.delivery_id
        join stock_delivery_detail_sns sdds on sdds.delivery_detail_id = sdd.delivery_detail_id
        WHERE sd.status IN (7, 8, 9, 10, 30)
        and sdd.status in (2, 3)
        ) m GROUP BY m.supplies_code,m.sn_no
    </select>

    <insert id="batchInsertDeliveryInfo" parameterType="com.gz.eim.am.stock.entity.vo.StockDeliveryInfo"
            useGeneratedKeys="true" keyProperty="deliveryId" keyColumn="delivery_id">
        insert into stock_delivery (delivery_id, order_id, order_no,
        delivery_no, from_system, from_model,
        receive_time, country, province,
        city, area, address,
        linkman, contact_way, zip_code,
        remark, dept_code, activity_code,
        logistics_channel, status, warehouse_code,
        in_warehouse_code, out_stock_time, out_stock_type,
        is_send, send_channel, send_time,
        delivery_time, weight, volume,
        approval_status, duty_user, use_user,
        biz_no,  created_by, updated_by,
        billing_user, billing_time, plan_out_time,
        reason_code, delivery_plan_head_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deliveryId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=VARCHAR},
            #{item.deliveryNo,jdbcType=VARCHAR}, #{item.fromSystem,jdbcType=VARCHAR}, #{item.fromModel,jdbcType=VARCHAR},
            #{item.receiveTime,jdbcType=TIMESTAMP}, #{item.country,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR},
            #{item.linkman,jdbcType=VARCHAR}, #{item.contactWay,jdbcType=VARCHAR}, #{item.zipCode,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.deptCode,jdbcType=VARCHAR}, #{item.activityCode,jdbcType=VARCHAR},
            #{item.logisticsChannel,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.warehouseCode,jdbcType=VARCHAR},
            #{item.inWarehouseCode,jdbcType=VARCHAR}, #{item.outStockTime,jdbcType=TIMESTAMP}, #{item.outStockType,jdbcType=INTEGER},
            #{item.isSend,jdbcType=INTEGER}, #{item.sendChannel,jdbcType=VARCHAR}, #{item.sendTime,jdbcType=TIMESTAMP},
            #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.weight,jdbcType=INTEGER}, #{item.volume,jdbcType=INTEGER},
            #{item.approvalStatus,jdbcType=TINYINT}, #{item.dutyUser,jdbcType=VARCHAR}, #{item.useUser,jdbcType=VARCHAR},
            #{item.bizNo,jdbcType=VARCHAR},  #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.billingUser,jdbcType=VARCHAR}, #{item.billingTime,jdbcType=TIMESTAMP}, #{item.planOutTime,jdbcType=TIMESTAMP},
            #{item.reasonCode,jdbcType=INTEGER}, #{item.deliveryPlanHeadId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <select id="selectDeliveryInfoList"  resultType="com.gz.eim.am.stock.entity.vo.StockDeliveryInfo">
        select sd.use_user as useUser,sdda.assets_code as assetsCode
        from stock_delivery_detail_assets sdda
        inner join stock_delivery sd on sd.delivery_id = sdda.delivery_id
        where  sd.out_stock_type = 7
        and sd.use_user in
        <foreach collection="empIdList" item="empId" open="(" close=")" separator=",">
            #{empId}
        </foreach>
        and sd.out_stock_type = #{outStockType}
    </select>


</mapper>
