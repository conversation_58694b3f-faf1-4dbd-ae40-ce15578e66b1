<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.order.DeliveryDetailHistoryMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockDeliveryDetailHistory">
    <id column="delivery_detail_id" jdbcType="BIGINT" property="deliveryDetailId" />
    <result column="delivery_id" jdbcType="BIGINT" property="deliveryId" />
    <result column="supplies_code" jdbcType="VARCHAR" property="suppliesCode" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="quality" jdbcType="INTEGER" property="quality" />
    <result column="real_number" jdbcType="INTEGER" property="realNumber" />
    <result column="difference_reason" jdbcType="VARCHAR" property="differenceReason" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="sn_no" jdbcType="VARCHAR" property="snNo" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="inventory_out_time" jdbcType="TIMESTAMP" property="inventoryOutTime" />
    <result column="plan_out_time" jdbcType="TIMESTAMP" property="planOutTime" />
    <result column="real_warehouse_code" jdbcType="VARCHAR" property="realWarehouseCode" />
    <result column="is_send" jdbcType="INTEGER" property="isSend" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    delivery_detail_id, delivery_id, supplies_code, number, quality, real_number, difference_reason, 
    assets_code, batch_no, sn_no, created_by, created_at, updated_by, updated_at, inventory_out_time,
    plan_out_time, real_warehouse_code, is_send, status
  </sql>
  <insert id="insertList" parameterType="java.util.List">
    insert into stock_delivery_detail_history (delivery_detail_id, delivery_id, supplies_code, 
      number, quality, real_number, 
      difference_reason, assets_code, batch_no, 
      sn_no, created_by, created_at, 
      updated_by, updated_at, inventory_out_time,
      plan_out_time, real_warehouse_code, is_send,
      status,delivery_plan_line_id)
    values
      <foreach collection="list" item="item" index="index" separator=",">
        (#{item.deliveryDetailId,jdbcType=BIGINT}, #{item.deliveryId,jdbcType=BIGINT}, #{item.suppliesCode,jdbcType=VARCHAR},
        #{item.number,jdbcType=INTEGER}, #{item.quality,jdbcType=INTEGER}, #{item.realNumber,jdbcType=INTEGER},
        #{item.differenceReason,jdbcType=VARCHAR}, #{item.assetsCode,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR},
        #{item.snNo,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
        #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.inventoryOutTime,jdbcType=TIMESTAMP},
        #{item.planOutTime,jdbcType=TIMESTAMP}, #{item.realWarehouseCode,jdbcType=VARCHAR}, #{item.isSend,jdbcType=INTEGER},
        #{item.status,jdbcType=INTEGER},#{item.deliveryPlanLineId,jdbcType=BIGINT})
      </foreach>
  </insert>

  <select id="selectInventoryOutDetailRespDTOByDeliveryId" parameterType="java.lang.Long"
          resultType="com.gz.eim.am.stock.dto.response.order.InventoryOutDetailRespDTO">
    select
    sdd.delivery_detail_id deliveryDetailId,
    sdd.supplies_code suppliesCode,
    ss.name suppliesName,
    sdd.number,
    sdd.real_number realNumber,
    sdd.difference_reason differenceReason,
    ssu.unit_name unitName,
    DATE_FORMAT(sdd.plan_out_time, '%Y-%m-%d') planOutTime,
    DATE_FORMAT(sdd.inventory_out_time, '%Y-%m-%d') inventoryOutTime,
    sdd.real_warehouse_code realWarehouseCode,
    sw.name realWarehouseCodeName,
    sdd.is_send isSend,
    sdd.status,
    sdd.delivery_plan_line_id deliveryPlanLineId,
    ss.remark suppliesRemark
    from
    stock_delivery_detail_history sdd
    LEFT JOIN stock_supplies ss on sdd.supplies_code = ss.code
    LEFT JOIN stock_supplies_unit ssu on ss.unit_code = ssu.unit_code
    LEFT JOIN stock_warehouse sw on sdd.real_warehouse_code = sw.code
    where
    sdd.delivery_id = #{deliveryId}
  </select>
</mapper>