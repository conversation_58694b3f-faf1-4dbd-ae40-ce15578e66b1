<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gz.eim.am.stock.dao.order.DeliveryDetailAssetMapper">
  <resultMap id="BaseResultMap" type="com.gz.eim.am.stock.entity.StockDeliveryDetailAsset">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="delivery_id" jdbcType="BIGINT" property="deliveryId" />
    <result column="delivery_detail_id" jdbcType="BIGINT" property="deliveryDetailId" />
    <result column="assets_code" jdbcType="VARCHAR" property="assetsCode" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_AT" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_AT" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>

  <insert id="insertMultiple" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetailAsset">
    insert into stock_delivery_detail_assets (delivery_id, delivery_detail_id, assets_code, 
      CREATED_BY, UPDATED_BY)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.deliveryId,jdbcType=BIGINT},
      #{item.deliveryDetailId,jdbcType=BIGINT},
      #{item.assetsCode,jdbcType=VARCHAR},
      #{item.createdBy,jdbcType=VARCHAR},
      #{item.updatedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="com.gz.eim.am.stock.entity.StockDeliveryDetailAsset">
    <foreach collection="list" item="item" separator=";">
      update stock_delivery_detail_assets
      <set>
        <if test="item.deliveryId != null">
          delivery_id = #{item.deliveryId,jdbcType=BIGINT},
        </if>
        <if test="item.deliveryDetailId != null">
          delivery_detail_id = #{item.deliveryDetailId,jdbcType=BIGINT},
        </if>
        <if test="item.assetsCode != null">
          assets_code = #{item.assetsCode,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null">
          CREATED_BY = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createdAt != null">
          CREATED_AT = #{item.createdAt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updatedBy != null">
          UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
        </if>
        <if test="item.updatedAt != null">
          UPDATED_AT = #{item.updatedAt,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

</mapper>