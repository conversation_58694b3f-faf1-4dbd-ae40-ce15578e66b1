<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="mysqlgenerator" targetRuntime="MyBatis3">
        <plugin type="com.fuu.eim.mybatis.plugins.MySQLLimitPlugin"/>
        <plugin type="com.fuu.eim.mybatis.plugins.OverIsMergeablePlugin"/>
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--数据库连接 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*****************************************************"
                        userId="ambase"
                        password="shake.dog">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- model package and location -->
        <javaModelGenerator targetPackage="com.gz.eim.am.stock.entity.ambase" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- mapping package and location -->
        <sqlMapGenerator targetPackage="resources/sqlmap/ambase" targetProject="src/main">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- dao package and location -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.gz.eim.am.stock.dao.ambase"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
<!--        <table tableName="apv_task"-->
<!--          domainObjectName="ApvTask"-->
<!--          mapperName="ApvTaskMapper"-->
<!--          enableCountByExample="true"-->
<!--          enableUpdateByExample="true"-->
<!--          enableDeleteByExample="false"-->
<!--          enableSelectByExample="true"-->
<!--          selectByExampleQueryId="true">-->
<!--         </table>-->
<!--        <table tableName="fin_dem_cost_item"-->
<!--               domainObjectName="FinDemCostItem"-->
<!--               mapperName="FinDemCostItemMapper"-->
<!--               enableCountByExample="true"-->
<!--               enableUpdateByExample="true"-->
<!--               enableDeleteByExample="false"-->
<!--               enableSelectByExample="true"-->
<!--               selectByExampleQueryId="true">-->
<!--        </table>-->

        <!-- table name,  change tableName and domainObjectName  -->

        <!--<table tableName="category"
               domainObjectName="FinCategory"
               mapperName="FinCategoryMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="category_id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="fin_category_cost_item"
               domainObjectName="FinCategoryCostItem"
               mapperName="FinCategoryCostItemMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="fin_dem_cost_item"
               domainObjectName="FinDemCostItem"
               mapperName="FinDemCostItemMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="cost_item_id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="sys_dict"
               domainObjectName="FinSysDict"
               mapperName="FinSysDictMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->


        <!--<table tableName="sys_attach"
               domainObjectName="SysAttach"
               mapperName="SysAttachMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="attach_id" sqlStatement="MySql" identity="true" />
        </table>

        <table tableName="sys_attach_ext"
               domainObjectName="SysAttachExt"
               mapperName="SysAttachExtMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="attach_ext_id" sqlStatement="MySql" identity="true" />
        </table>-->

        <!-- <table tableName="ps_bus_jobcode_tbl"
                domainObjectName="PsBusJobcodeTbl"
                mapperName="PsBusJobcodeTblMapper"
                enableCountByExample="true"
                enableUpdateByExample="true"
                enableDeleteByExample="false"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="jobcode" sqlStatement="MySql" identity="true" />
         </table>-->

        <table tableName="ps_bus_jobdata_inactive_tbl"
               domainObjectName="PsBusJobdataInactiveTbl"
               mapperName="PsBusJobdataInactiveTblMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="emplid" sqlStatement="MySql" identity="true" />
        </table>

        <!-- <table tableName="stock_supplies_category"
                domainObjectName="StockSuppliesCategory"
                mapperName="StockSuppliesCategoryMapper"
                enableCountByExample="true"
                enableUpdateByExample="true"
                enableDeleteByExample="false"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="supplies_category_id" sqlStatement="MySql" identity="true" />
         </table>-->

        <!-- <table tableName="sys_dept"
                domainObjectName="SysDept"
                mapperName="SysDeptMapper"
                enableCountByExample="true"
                enableUpdateByExample="true"
                enableDeleteByExample="false"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="id" sqlStatement="MySql" identity="true" />
         </table>

         <table tableName="sys_user"
                domainObjectName="SysUser"
                mapperName="SysUserMapper"
                enableCountByExample="true"
                enableUpdateByExample="true"
                enableDeleteByExample="false"
                enableSelectByExample="true"
                selectByExampleQueryId="true">
             <generatedKey column="id" sqlStatement="MySql" identity="true" />
         </table>-->


        <!--<table tableName="cux_ce_getcnapsbank"
               domainObjectName="CuxCeGetCnapsbank"
               mapperName="CuxCeGetCnapsbankMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="bank_id" sqlStatement="MySql" identity="true" />
        </table>-->

        <!--table tableName="cux_ce_getcnaps"
               domainObjectName="CuxCeGetCnaps"
               mapperName="CuxCeGetCnapsMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="cnaps_id" sqlStatement="MySql" identity="true" />
        </table>-->

        <!--table tableName="bus_fnd_territories"
               domainObjectName="BusFndTerritories"
               mapperName="BusFndTerritoriesMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="territory_code" sqlStatement="MySql" identity="true" />
        </table>-->

        <!-- <table tableName="ps_bus_location_tbl"
               domainObjectName="PsBusLocationTbl"
               mapperName="PsBusLocationTblMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="location" sqlStatement="MySql" identity="true" />
        </table> -->

        <!--<table tableName="org_bus_testpersonal_tbl"
               domainObjectName="OrgBusTestPersonalTbl"
               mapperName="OrgBusTestPersonalTblMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="org_bus_testjobdata_tbl"
               domainObjectName="OrgBusTestJobDataTbl"
               mapperName="OrgBusTestJobDataTblMapper"
               enableCountByExample="true"
               enableUpdateByExample="true"
               enableDeleteByExample="false"
               enableSelectByExample="true"
               selectByExampleQueryId="true">
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
<!--        <table tableName="cux_getfadetail_tbl"-->
<!--               domainObjectName="CuxGetfadetailTbl"-->
<!--               mapperName="CuxGetfadetailTblMapper"-->
<!--               enableCountByExample="true"-->
<!--               enableUpdateByExample="true"-->
<!--               enableDeleteByExample="false"-->
<!--               enableSelectByExample="true"-->
<!--               selectByExampleQueryId="true">-->
<!--        </table>-->
    </context>
</generatorConfiguration>
