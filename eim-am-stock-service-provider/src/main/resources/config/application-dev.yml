spring:
  profiles:
    include: swagger
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *******************************************************************************************************************************************************************************************************************    #如果使用router，请写router的url即可。
    username: stock
    password: shake-dog-dev
    validationQuery: select 'x'
    driver-class-name: com.mysql.jdbc.Driver
    tomcat:
      max-idle: 4
      minIdle: 1
      max-active: 32   # <建议与Application并发级别保持一致>
      max-wait: 3000
      initial-size: 4
      name: dataSource
      remove-abandoned: true
      remove-abandoned-timeout: 60
      validation-query: SELECT 1
      validation-interval: 30000
      test-while-idle: true
      time-between-eviction-runs-millis: 30000
      min-evictable-idle-time-millis: 300000
      test-on-borrow: true
      test-on-return: true
      max-age: 30000
      jmx-enabled: true
  redis:
    pool:
      max-active: 8
      max-idle: 8
      max-wait: -1
      min-idle: 0
    cluster:
      nodes: 10.16.208.93:5001,10.16.208.93:5002,10.16.208.93:5003,10.16.208.93:5004,10.16.208.93:5005,10.16.208.93:5006
    password: fb9092
eureka:
  client:
    service-url:
      defaultZone: http://dev-admin:<EMAIL>/eureka/

# 所有项目配置都写在porject下. 按模块层级
project:
  activity:
    messageUrl: http://testwfl.guazi-corp.com:8001
    wflUrl: http://testwfl.guazi-corp.com:8004
    fromUrl: http://testwfl.guazi-corp.com:8005
  env: dev  # 每个配置文件不同
  cors:
    enable: true # 本地环境开 别的环境不用添加该配置
  wfl:
    systemId: 2bbd6fb6-365b-4151-819d-07dc90d99c86
    checkCode: 2F610F32-F3A0-4723-8CF1-5DB0BCB7A6E9
    systemModule: stock
  filePlatformUrl:  http://uateimfile-api.guazi-corp.com
  file:
    systemCode: stock20191209
    privateKey: 8cc29239d191404585c11fe1aa56e901
    filePath: /stock/asset_code
    logoPath: http://testfile.guazi-corp.com/api/file/download?fileId=e43de8f23fc24fb28827e6d04efb7547&systemCode=stock20191209&privateKey=8cc29239d191404585c11fe1aa56e901&fileName=log.jpg
    systemModule: eim-am-stock
    attachModule: common
  qr:
    systemCode: GUGZI_STOCK
  mcrypt:
    appKey: key_app49_test
    appSecret: tXfd9FBLNqClBoRi53vm
    apiUrl: http://mcrypt-dev.dns.guazi.com/
  syncebs:
    syncUrl: http://eim-fin-ebs-itf-test.guazi-cloud.com/fa/impAsset
    queryUrl: http://eim-fin-ebs-itf-test.guazi-cloud.com/fa/getCheckasset
    scrapSyncUrl: http://eim-fin-ebs-itf-test.guazi-cloud.com/fa/impFaRetirement
    scrapQueryUrl: http://eim-fin-ebs-itf-test.guazi-cloud.com/fa/getRetirement
    changeSyncUrl: http://eim-fin-ebs-itf-test.guazi-cloud.com/fa/impFaAdj
    changeQueryUrl: http://eim-fin-ebs-itf-test.guazi-cloud.com/fa/getAdjustment
  assignUrl:
    people: http://testam-app.guazi-cloud.com/stock.html#/userLists
    admin: http://testam-app.guazi-cloud.com/stock.html#/adminList
  welfare:
    systemCode: eim-am-stock
    tid: dev-436b81e4ba3049aaaa5bdfba7136

jwt:
  enable: false
#业务系统账号认证
security:
  basic:
    enabled: false

namespace:
  name: amstockdev

request-config:
  platbaseUrl: http://deveimapi.guazi-cloud.com

kafka-producer-config:
  bootstrapServers: g1-bdp-cdhtest-01.dns.guazi.com:9091,g1-bdp-cdhtest-02.dns.guazi.com:9091,g1-bdp-cdhtest-03.dns.guazi.com:9091
  schemaRegistryUrl: http://schema-registry.guazi-corp.com
  lingerMs: 0
  securityProtocol: SASL_PLAINTEXT
  saslMechanism: GSSAPI
  saslKerberoserviceName: kafka
  saslJaasConfig: com.sun.security.auth.module.Krb5LoginModule required useKeyTab=true storeKey=true keyTab="/data/www/imc-am/eim-am-stock/eim-am-stock-service-provider/target/classes/config/test/erp_kafka.keytab" principal="<EMAIL>";
  acks: -1
  retries: 0
  retryBackOff: 600
  maxInFlightRequestsPerConnection: 1
  keySerializer: org.apache.kafka.common.serialization.StringSerializer
  valueSerializer: io.confluent.kafka.serializers.KafkaAvroSerializer
  topics:
    factStockWflCommon: fact_stock_wfl_record

permission:
  #拦截器配置
  urlFilter:
    #开启拦截器
    enable: true
    #排除拦截地址
    excludePath: /open/am/stock/health/check,/open/am/stock/readiness/check,/error,/api/am/stock/external/**,/api/am/stock/planAssetPurchase/**,/api/am/inventory/temporary/**,/api/am/stock/purchase-return/**,/api/am/stock/assets/search,/api/am/stock/warehouse/vague,/api/am/stock/config/supplies,/api/am/stock/delivery/file,/api/am/stock/wfl/**,/api/am/stock/assets/search,/api/am/stock/warehouse/vague,/api/am/stock/config/supplies,/api/am/stock/delivery/file,error,/api/am/stock/assets/remind/send-message,/api/am/stock/checkTask/update-to-do-status,/api/am/stock/checkResult/takingPlanEnd,/api/am/stock/license/remind/send-message,/api/am/stock/assets/sync/extract-sync,/api/am/stock/assets/sync/query,/api/am/stock/supplies/page-by-purchase,/api/am/stock/supplies/search-stock-supplies-purchase,/api/am/stock/supplies/category/**,/api/am/stock/warehouse,/api/am/stock/planLicenseLoan/search/*,/api/am/stock/platform/getTemporaryToken,/api/am/stock/delivery/stockQuantityMonthEnd,/api/am/stock/supplies/unit/find-all,/api/am/sns/plan-sns-purchase/gps-search,/api/am/sns/plan-sns-purchase/install-status,/api/am/stock/assets/sendAssetsEmail,/api/am/stock/checkTask/sendGuaGuaMessage/urgeCheck,/api/am/stock/assets/compensation/dealHrDevolveLeaveUser,/api/am/stock/assets/compensation/leaveNoReturnAssets,/api/am/stock/assets/compensation/queryLeaveApproveUserByWfl,/api/am/stock/assets/compensation/manualPushHrCompensationData,/api/am/stock/licenseRenew/license/wfl/*,/api/am/stock/assets/compensation/queryLastMonthNoReturnAssetsAndSendEmail,/api/am/stock/warehouse/queryWarehouseByReceiveAssets,/api/am/stock/assets/searchAssetsByReceive,/api/am/stock/assets/searchAssetsByRemand,/api/am/stock/assetsRepair/ITApprove,/api/am/stock/assetsRepair/adminApprove,/api/am/stock/assetsRepair/assetsHolderApprove,/api/am/stock/assetsRepair/queryRepairDetail,/api/am/stock/assetsRepair/exportAssetsExcel,/api/am/stock/assetsRepair/importBatchUpdateAssetsExcel,/api/am/stock/assetsRepair/repairCheckApprove,/api/am/stock/demand/queryAdminEnterAssetsApproveDetail,/api/am/stock/demand/submitAdminEnterAssetsApproveDetail,/api/am/stock/demand/queryEmployeeConfirmDetail,/api/am/stock/demand/submitEmployeeConfirmDetail,/api/am/stock/demand/sendMessageReminderToNotReceiveEmployee,/api/am/stock/demand/queryCourierApproveDetail,/api/am/stock/demand/queryCourierApproveEnterAssets,/api/am/stock/demand/createOrderByCourier,/api/am/stock/demand/judgeCourierOrderIsDone,/api/am/stock/demand/automaticConfirmReceipt,/api/am/stock/assets/queryAssetsNumberGroupByCategoryCode,/api/am/stock/check/remind/**,/api/am/stock/check-result-adjust/select,/api/am/file/**
  #数据权限配置，目前不集成数据权限，所以下面两项是false，后期集成数据权限，需要打开相关配置
  dataControl:
    #是否开启自动控制，默认true：全部扫描，false：只扫描增加注解的
    autoControl: false
    #是否开启黑名单控制，默认true：未配置规则无权限，false：未配置规则的放开权限
    blacklist: false
  #租户配置
  tenant:
    #自己系统化是否是租户模式、不是租户模式配置false
    enable: false
    #租户ID 多个用逗号隔空
    tids: 68a9bb2c40e145e9ba51bce2e665c679
  #权限数据同步配置
  sync:
    #自己系统编码，对应权限系统申请是的系统编码
    sysCode: eim-am-stock
    kafka:
      enable: true
      bootstrap:
        servers: g1-bdp-cdhtest-01.dns.guazi.com:9092,g1-bdp-cdhtest-02.dns.guazi.com:9092,g1-bdp-cdhtest-03.dns.guazi.com:9092
      group:
        id: eim-am-stock_dev
