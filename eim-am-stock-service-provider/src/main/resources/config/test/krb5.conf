[libdefaults]
default_realm = CDHTEST.COM
dns_lookup_kdc = false
dns_lookup_realm = false
ticket_lifetime = 86400
renew_lifetime = 604800
forwardable = true
default_tgs_enctypes = rc4-hmac aes128-cts des3-hmac-sha1
default_tkt_enctypes = rc4-hmac aes128-cts des3-hmac-sha1
permitted_enctypes = rc4-hmac aes128-cts des3-hmac-sha1
udp_preference_limit = 1
kdc_timeout = 3000
[realms]
CDHTEST.COM = {
kdc = g1-bdp-cdhtest-01.dns.guazi.com
kdc = g1-bdp-cdhtest-02.dns.guazi.com
kdc = g1-bdp-cdhtest-03.dns.guazi.com
master_kdc = g1-bdp-cdhtest-01.dns.guazi.com

}
[domain_realm]