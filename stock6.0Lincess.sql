-- 资产增加字段
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `plan_return_time` datetime  COMMENT '执照借用预计归还时间' AFTER `plan_handle_time`;
-- 资产日志表增加字段
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `plan_return_time` datetime  COMMENT '执照借用预计归还时间' AFTER `plan_handle_time`;
-- 申请出库单增加字段
ALTER TABLE `stock`.`stock_delivery_plan_heads`
ADD COLUMN `plan_return_time` datetime  COMMENT '执照借用预计归还时间' AFTER `plan_out_time`;

-- 修正资产卡片表与入库单编号关联
update
 stock_assets a inner join
  (select b.inventory_in_plan_no,c.inventory_in_no,e.assets_code from stock_inventory_in_plan_heads b
 inner join stock_inventory_in c on b.inventory_in_plan_head_id=c.inventory_in_plan_head_id
 inner join stock_inventory_in_supplies d on c.inventory_in_id=d.inventory_in_id
 inner join stock_inventory_in_supplies_assets e on d.in_supplies_id=e.in_supplies_id)
 as dd on a.purchase_no=dd.inventory_in_plan_no and a.assets_code=dd.assets_code set a.purchase_no=dd.inventory_in_no

 -- 初始化枚举配置
 INSERT INTO `ambase`.`meta_data`(`project`, `module`, `md_key`, `md_value`, `md_type`, `description`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES
 ('stock', 'document', 'document-warehouse', '[{\"documentName\": \"采购申请入库单\", \"documentType\": 1, \"warehouseType\": [2, 3]}, {\"documentName\": \"资产归还申请入库单\",
 \"documentType\": 2, \"warehouseType\": [2, 3]}, {\"documentName\": \"资产申请调入单\", \"documentType\": 3, \"warehouseType\": [2, 3]},
 {\"documentName\": \"金融GPS入库\", \"documentType\": 4, \"warehouseType\": [1]}, {\"documentName\": \"金融GPS出库\", \"documentType\":
 5, \"warehouseType\": [1]}, {\"documentName\": \"资产申请调拨出库单\", \"documentType\": 6, \"warehouseType\": [2, 3]}, {\"documentName\":
  \"资产申请处置单\", \"documentType\": 7, \"warehouseType\": [2, 3]}, {\"documentName\": \"资产申请领用单\", \"documentType\": 8, \"warehouseType\":
   [2, 3]}, {\"documentName\": \"查看库存量\", \"documentType\": 9, \"warehouseType\": [2, 3]}, {\"documentName\": \"查看GPS库存量\", \"documentType\":
    10, \"warehouseType\": [1]}, {\"documentName\": \"查看资产卡片\", \"documentType\": 11, \"warehouseType\": [2, 3]}, {\"documentName\":
    \"查看执照台账\", \"documentType\": 12, \"warehouseType\": [12]}, {\"documentName\": \"执照领用申请单\", \"documentType\": 13,
    \"warehouseType\": [12]}, {\"documentName\": \"执照归还申请单\", \"documentType\": 14, \"warehouseType\": [12]},
    {\"documentName\": \"办公用品领用申请单\", \"documentType\": 15, \"warehouseType\": [4]}]', '1', '单据类型映射仓库类型',
    '10122730', '2020-08-31 14:01:31', '10122730', '2020-09-06 11:49:14');

 -- 添加执照物料分类
 INSERT INTO `stock`.`stock_supplies_category`(`supplies_category_id`, `parent_id`, `code`, `name`, `level`, `type`, `status`, `all_code`, `all_name`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES (76, 0, 'ZZ10', '执照', 1, 2, 1, '/ZZ10', '/执照', 0, '10122730', '2020-09-14 18:12:06', '10122767', '2020-09-14 18:12:06');
 INSERT INTO `stock`.`stock_supplies_category`(`supplies_category_id`, `parent_id`, `code`, `name`, `level`, `type`, `status`, `all_code`, `all_name`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES (77, 76, 'ZZ1010', '执照', 2, 2, 1, '/ZZ10/ZZ1010', '/执照/执照', 0, '10122730', '2020-09-14 18:12:06', '10122767', '2020-09-14 18:12:06');
 INSERT INTO `stock`.`stock_supplies_category`(`supplies_category_id`, `parent_id`, `code`, `name`, `level`, `type`, `status`, `all_code`, `all_name`, `del_flag`, `created_by`, `created_at`, `updated_by`, `updated_at`) VALUES (78, 77, 'ZZ101010', '执照', 3, 2, 1, '/ZZ10/ZZ1010/ZZ101010', '/执照/执照/执照', 0, '10122730', '2020-09-14 18:12:06', '10122767', '2020-09-14 18:12:06');



----------采购对接模块--------
-- 资产同步数据表
CREATE TABLE `stock_assets_ebs_sync` (
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assets_code` varchar(64) NOT NULL COMMENT '资产编码',
  `batch_no` varchar(64) NOT NULL COMMENT '抽取批次号',
  `query_code` varchar(64) NOT NULL DEFAULT '0' COMMENT '查询批次号',
  `sync_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '数据状态（0:抽取成功 1:审批中 2:审批拒绝 3:审批通过 4:财务处理中 5:财务入库失败 6:同步成功 7:同步失败）',
  `SYS_CODE` varchar(25) DEFAULT '电子表格',
  `ASSET_CATEGORY_SEGMENT1` varchar(25) NOT NULL DEFAULT '' COMMENT '资产财务大类',
  `ASSET_CATEGORY_SEGMENT2` varchar(25) NOT NULL DEFAULT '' COMMENT '资产财务小类',
  `ASSET_KEY_SEGMENT1` varchar(25) NOT NULL DEFAULT '购买' COMMENT '资产来源',
  `ASSET_KEY_SEGMENT2` varchar(25) NOT NULL DEFAULT '' COMMENT '实物大类编码',
  `ASSET_KEY_SEGMENT3` varchar(25) NOT NULL DEFAULT '' COMMENT '实物小类编码',
  `ASSET_TYPE` varchar(11) NOT NULL DEFAULT '' COMMENT '资产类型',
  `BOOK_TYPE_CODE` varchar(15) NOT NULL DEFAULT '' COMMENT '资产帐簿',
  `DATE_PLACED_IN_SERVICE` varchar(64) NOT NULL DEFAULT '' COMMENT '资产启用日期',
  `DEPRECIATE_FLAG` varchar(3) NOT NULL DEFAULT '' COMMENT '是否对资产进行折旧（Y/N）',
  `DESCRIPTION` varchar(2) NOT NULL DEFAULT '' COMMENT '资产名称',
  `DEPRN_METHOD_CODE` varchar(12) NOT NULL DEFAULT 'STL' COMMENT '折旧方法',
  `FIXED_ASSETS_COST` decimal(12,4) NOT NULL DEFAULT 0 COMMENT '资产成本',
  `FIXED_ASSETS_UNITS` int NOT NULL DEFAULT 1 COMMENT '组成资产的数量',
  `INVOICE_NUMBER` varchar(15) NOT NULL DEFAULT '' COMMENT '发票编号',
  `LOC_SEGMENT1` varchar(25) NOT NULL DEFAULT ''  COMMENT '资产使用公司',
  `LOC_SEGMENT2` varchar(25) NOT NULL DEFAULT ''  COMMENT '	资产使用部门',
  `LOC_SEGMENT3` varchar(25) NOT NULL DEFAULT '' COMMENT '资产实物地点',
  `LIFE_IN_MONTHS` int NOT NULL DEFAULT 0 COMMENT '资产寿命（月）',
  `MODEL_NUMBER` varchar(40) NOT NULL DEFAULT '' COMMENT '资产的型号',
  `QUEUE_NAME` varchar(15) NOT NULL DEFAULT '过帐' COMMENT '使用您输入 POSTING_STATUS 栏的相同值（如新建、挂起和过帐）',
  `SERIAL_NUMBER` varchar(35) NOT NULL DEFAULT '' COMMENT '资产标签号，即资产卡片的资产标签号，每一个资产标签号生成一行数据',
  `ACC_SEGMENT1` varchar(25) NOT NULL DEFAULT '' COMMENT '公司段',
  `ACC_SEGMENT2` varchar(25) NOT NULL DEFAULT '' COMMENT 'BU段',
  `ACC_SEGMENT3` varchar(25) NOT NULL DEFAULT '' COMMENT '成本中心段',
  `ACC_SEGMENT4` varchar(25) NOT NULL DEFAULT '' COMMENT '会计科目段',
  `ACC_SEGMENT5` varchar(25) NOT NULL DEFAULT '' COMMENT '明细段',
  `ACC_SEGMENT6` varchar(25) NOT NULL DEFAULT '' COMMENT '产品段',
  `ACC_SEGMENT7` varchar(25) NOT NULL DEFAULT '' COMMENT '往来段',
  `ACC_SEGMENT8` varchar(25) NOT NULL DEFAULT '' COMMENT '项目段',
  `ACC_SEGMENT9` varchar(25) NOT NULL DEFAULT '' COMMENT '备用段1',
  `ACC_SEGMENT10` varchar(25) NOT NULL DEFAULT '' COMMENT '备用段2',
  `ACC_SEGMENT11` varchar(25) NOT NULL DEFAULT '' COMMENT '备用段3',
  `TAG_NUMBER` varchar(15) NOT NULL DEFAULT '' COMMENT '此栏用于填入资产标签号（如果要求）。如果您按条形码跟踪资产，则可以使用此栏来填入条形码值。Oracle 资产管理系统允许填入除“空”外的不重复值',
  `FA_CODE` varchar(35) NOT NULL DEFAULT '' COMMENT '财务资产编码',
  `ASSET_ID` int NOT NULL DEFAULT 0 COMMENT '财务资产id',
  `created_by` varchar(32) NOT NULL COMMENT '创建人',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) NOT NULL COMMENT '最近修改人',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_stock_assets_sync_01` (`assets_code`)
)  COMMENT='资产卡片表';

-- 资产卡片添加字段
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Purchase_order_no` varchar(64)  DEFAULT NULL COMMENT '采购订单号' AFTER `label_url`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Purchase_order_line_id` bigint(18)  DEFAULT NULL COMMENT '采购订单行id' AFTER `Purchase_order_no`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Receive_item_no` varchar(64)  DEFAULT NULL COMMENT '采购验收单行号'  AFTER `Purchase_order_line_id`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `accountant_code` varchar(64)  DEFAULT NULL COMMENT '结算单编码'  AFTER `Receive_item_no`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `accountant_line_code` varchar(64)  DEFAULT NULL COMMENT '结算单行编码'  AFTER `accountant_code`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `pay_date` datetime DEFAULT NULL COMMENT '付款时间'  AFTER `accountant_line_code`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `bill_code` varchar(64)  DEFAULT NULL COMMENT '发票编码'  AFTER `pay_date`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Currency` varchar(64)  DEFAULT NULL COMMENT '货币名称'  AFTER `bill_code`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Default_dispose_cost` DECIMAL(12,4)  DEFAULT NULL COMMENT '默认处置金额'  AFTER `Currency`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Unit_price` DECIMAL(12,4)  DEFAULT NULL COMMENT '单价'  AFTER `Default_dispose_cost`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Price_excluding_tax` DECIMAL(12,4)  DEFAULT NULL COMMENT '不含税价'  AFTER `Unit_price`;
ALTER TABLE `stock`.`stock_assets`
ADD COLUMN `Extract_status` tinyint(4)  NOT NULL DEFAULT 0 COMMENT '数据抽取状态'  AFTER `Price_excluding_tax`;

-- 资产卡片日志表添加字段
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Purchase_order_no` varchar(64)  DEFAULT NULL COMMENT '采购订单号' AFTER `label_url`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Purchase_order_line_id` bigint(18)  DEFAULT NULL COMMENT '采购订单行id' AFTER `Purchase_order_no`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Receive_item_no` varchar(64)  DEFAULT NULL COMMENT '采购验收单行号'  AFTER `Purchase_order_line_id`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `accountant_code` varchar(64)  DEFAULT NULL COMMENT '结算单编码'  AFTER `Receive_item_no`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `accountant_line_code` varchar(64)  DEFAULT NULL COMMENT '结算单行编码'  AFTER `accountant_code`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `pay_date` datetime DEFAULT NULL COMMENT '付款时间'  AFTER `accountant_line_code`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `bill_code` varchar(64)  DEFAULT NULL COMMENT '发票编码'  AFTER `pay_date`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Currency` varchar(64)  DEFAULT NULL COMMENT '货币名称'  AFTER `bill_code`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Default_dispose_cost` DECIMAL(12,4)  DEFAULT NULL COMMENT '默认处置金额'  AFTER `Currency`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Unit_price` DECIMAL(12,4)  DEFAULT NULL COMMENT '单价'  AFTER `Default_dispose_cost`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Price_excluding_tax` DECIMAL(12,4)  DEFAULT NULL COMMENT '不含税价'  AFTER `Unit_price`;
ALTER TABLE `stock`.`stock_assets_operation_log`
ADD COLUMN `Extract_status` tinyint(4)  NOT NULL DEFAULT 0 COMMENT '数据抽取状态'  AFTER `Price_excluding_tax`;

-- 资产卡片日志表添加字段
ALTER TABLE `stock`.`stock_assets_category`
ADD COLUMN `parent_category_code` varchar(255)  NOT NULL DEFAULT '' COMMENT '资产大类编码'  AFTER `id`;
ALTER TABLE `stock`.`stock_assets_category`
ADD COLUMN `Default_used_time` int DEFAULT 0 COMMENT '默认使用年限（月）'  AFTER `category_name`;
ALTER TABLE `stock`.`stock_assets_category`
ADD COLUMN `Default_used_cost` DECIMAL(12,4)  DEFAULT NULL COMMENT '默认使用金额'  AFTER `Default_used_time`;

-- 申请入库单头表增加字段
ALTER TABLE `stock`.`stock_inventory_in_plan_heads`
ADD COLUMN `Company_code` varchar(64)  NOT NULL DEFAULT '' COMMENT '公司编码'  AFTER `vendor_name`;
ALTER TABLE `stock`.`stock_inventory_in_plan_heads`
ADD COLUMN `system_code` varchar(255)  NOT NULL DEFAULT '' COMMENT '系统识别码'  AFTER `Company_code`;
ALTER TABLE `stock`.`stock_inventory_in_plan_heads`
ADD COLUMN `Warehous_type` tinyint(4)  NOT NULL DEFAULT 100 COMMENT '调入仓库类型'  AFTER `inventory_in_plan_no`;
ALTER TABLE `stock`.`stock_inventory_in_plan_heads`
ADD COLUMN `Purchase_order_no` varchar(64)  NOT NULL DEFAULT '' COMMENT '采购订单号'  AFTER `delivery_no`;

-- 申请入库单行表增加字段
ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `Purchase_order_line_id` bigint(20)  NOT NULL DEFAULT 0 COMMENT '采购订单行id'  AFTER `plan_in_time`;
ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `Receive_item_no` varchar(64)  NOT NULL DEFAULT '' COMMENT '采购验收单行号'  AFTER `Purchase_order_line_id`;
ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `Unit_price` DECIMAL(12,4)  DEFAULT NULL  COMMENT '单价'  AFTER `Receive_item_no`;
ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `Price_excluding_tax` DECIMAL(12,4)  DEFAULT NULL COMMENT '不含税价'  AFTER `Unit_price`;
ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `Total_price` DECIMAL(12,4)  DEFAULT NULL COMMENT '总价'  AFTER `Price_excluding_tax`;
ALTER TABLE `stock`.`stock_inventory_in_plan_lines`
ADD COLUMN `Currency` varchar(64)  NOT NULL DEFAULT '' COMMENT '货币名称'  AFTER `Total_price`;



-------------------------执照二期优化2021-01-09----------------------------
-- 执照执照增加字段
alter table stock_delivery_plan_heads add COLUMN  `need_time` datetime DEFAULT NULL COMMENT '执照借用需求日期'  AFTER `plan_return_time`;
alter table stock_delivery_plan_heads add COLUMN  `need_send` int(4) DEFAULT 0 COMMENT '执照借用是否需要邮寄（0:否，1:是）'  AFTER `need_time`;
alter table stock_delivery_plan_heads add COLUMN  `need_address` varchar(250) DEFAULT '' COMMENT '执照借用是否需要邮寄（0:否，1:是）'  AFTER `need_send`;
alter table stock_delivery_plan_heads add COLUMN  `recipients` varchar(64) DEFAULT '' COMMENT '收件人'  AFTER `need_address`;
alter table stock_delivery_plan_heads add COLUMN  `recipients_phone` varchar(64) DEFAULT '' COMMENT '收件人电话'  AFTER `recipients`;
